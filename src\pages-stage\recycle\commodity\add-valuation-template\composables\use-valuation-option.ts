import { ref } from 'vue';

import { ISelectOption } from '../data';
import { queryOptionList } from '../service';

/**
 * Description 评估项下拉数据
 * @param {number} type 1-基础 2-检测 3-功能
 * @returns {any}
 */
export default function (type: number) {
  const options = ref<ISelectOption[]>([]);

  // 格式化下拉选项
  const mapOption = (options: ISelectOption[]): ISelectOption[] => {
    if (!options) return [];
    if (!(options instanceof Array)) return [];
    return options.map(({ name, log_id: origin_log_id, option, title }, index) => {
      return {
        label: name,
        value: origin_log_id,
        real: {
          origin_log_id,
          sort: index + 1,
          is_multi: 0,
          name,
          title,
          option,
        },
      };
    });
  };

  // 初始化操作
  const load = () => {
    queryOptionList(type).then(({ data }) => {
      options.value = mapOption(data);
    });
  };

  load();
  return {
    options,
    reload: load,
  };
}
