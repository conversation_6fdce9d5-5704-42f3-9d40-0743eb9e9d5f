import type { IAccessoryInfo } from '@/components/order-business/typing';

export interface IExtraData {
  spu_name?: string;
  sku_name?: string;
  server_id?: string | number;
  accessory_data?: IAccessoryInfo[];
  time_start?: string;
  send_time_tip?: number;
  special_category?: boolean;
  ms_loan_limit?: boolean;
  offline_contract_show?: boolean;
  mac_book?: boolean;
  is_exist?: boolean;
  phone?: string;
  go_ship_phone?: string;
  is_mark_order?: boolean;
  item_array?: number[];
  store_name?: string;
  is_need_supplement_protocol?: boolean;
  server_address_list?: any[];
  imei_code_data?: { code: string; sort: number }[];
  is_third_party_store_order?: boolean;
  express?: string;
  is_open_notarization_tips?: boolean;
  is_grayscale?: boolean;
}

export interface IDeliverStore {
  order_id?: string;
  brand_id?: string;
  category_id?: string;
  is_new?: boolean;
  pdm_brand_id?: string;
  pdm_category_id?: string;
  is_checked?: number;
  item_num?: string;
  binding_code?: boolean;
  replace_auth?: boolean;
  special_category?: boolean;
  order_status?: string;
  sku_id?: string;
  sku_info?: string;
  device_auth?: boolean;
  /** false代表渠道属于支付宝-生活号、支付宝-企业小程序，不显示‘平台代发’功能界面 */
  is_can_distribute?: boolean;
  device_special_tag?: number; // 设备类型
  scm_auth?: boolean;
  warehouse_list?: any;
  reserve_status?: boolean; // 预定状态
  issued_before?: boolean; // 是否取消过代发
  can_send_self?: boolean; // 是否可自发
  storage_memory_up_auth?: boolean; //是否可补贴升级
  one_device_code?: string; // 是否一机一码
  one_device_code_type?: number; // 一机一码类型
  is_new_online_server: boolean; // 是否新网商家
}

export interface RootObject {
  category_id: string;
  brand_id: string;
  is_new: boolean;
  pdm_category_id: string;
  pdm_brand_id: string;
  replace_auth: boolean;
  order_status: string;
  is_checked: number;
  check_server: string;
  binding_code: boolean;
  item_num: string;
  sku_id: string;
  sku_info: string;
  device_auth: boolean;
  is_can_distribute: boolean;
}

export interface IAcountAmount {
  canShow?: boolean;
  amount?: string;
  account_id?: string;
  account_type?: string;
}

export interface IWarehouseInformation {
  sku_id?: string;
  warehouse_id?: number;
  warehouse_type?: number;
  inventoryStatus?: boolean;
  evaluatePrice?: string;
  showPrice?: boolean;
  deviceMoney?: string | boolean;
  inspectMoney?: string | boolean;
  sendMoney?: string | boolean;
  accessoryMoney?: string | boolean;
  lockMoney?: string | boolean;
  tagType?: string;
  tagText?: string;
  newTagText?: string;
}

export interface IModalState {
  is_support: boolean;
  activate_only: boolean;
  is_checked: boolean;
  sku_id?: string;
  sku_info?: string;
  stockStatusLoading: boolean;
  priceInfoLoading: boolean;
  warehouseInformation: {
    rental: IWarehouseInformation;
    nonRental: IWarehouseInformation;
  };
  accountAmount?: IAcountAmount;
  is_one_pay_account?: number;
}

export interface IAccessoryData {
  type: number;
  list: {
    accessory_name: string;
    accessory_price: string;
    accessory_type: number;
    num: number;
  }[];
}

export type IOldShipForm = {
  shipper_code?: string | -1 | -2;
  logistic_code?: string;
  ms_loan_limit_imei?: number;
  ms_loan_limit_image: Record<string, any>[];
  is_support_offline_contract?: string;
  offline_contract_file: [
    {
      title: string;
      file_url: Record<string, any>[];
    },
    {
      title: string;
      file_url: Record<string, any>[];
    },
  ];
  image: string[];
};
