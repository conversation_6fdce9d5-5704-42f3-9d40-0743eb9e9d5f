import { Rule } from 'ant-design-vue/lib/form';
import { ColumnType } from 'ant-design-vue/lib/table';

import { FormGroupItem } from '@/components/form-create/src/typing';

import { phoneSeparated } from './../login-log/config';
/**
 * 自定义模态框手机号码校验规则
 */
const validatePhone = async (_rule: Rule, value: string) => {
  if (value.length !== 11) {
    return Promise.reject('请输入11位的手机号码！');
  } else return Promise.resolve();
};

/**
 * 检索表单渲染配置
 */

//  <template #customer1>
//  <a-form-item
//    label="创建时间"
//    name="created_at"
//  >
//    <a-range-picker
//      v-model:value="created_at1"
//      allow-clear
//      format="YYYY-MM-DD"
//      value-format="YYYY-MM-DD"
//      @change="handleRangeChange"
//    />
//  </a-form-item>
// </template>
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'phone',
    originProps: { label: '手机', name: 'phone' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'username',
    fragmentKey: 'renderInput',
    originProps: { label: '用户名', name: 'username' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'created_at',
    fragmentKey: 'renderRangePicker',
    originProps: { label: '注册时间', name: 'created_at' },
    elProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
      style: { width: '100%' },
    },
  },
];
/**
 * 表格columns
 */
export const columns: ColumnType[] = [
  {
    title: '序号',
    width: 100,
    dataIndex: 'name',
    key: 'index',
    customRender: ({ index }: { index: number }) => `${index + 1}`,
  },
  {
    title: '手机',
    dataIndex: 'phone',
    key: 'phone',
    customRender: ({ record }) => phoneSeparated(record.phone),

    // sortDirections: ['descend', 'ascend'],
    // sorter: (a: any, b: any) => new Date(a.pay_at).getTime() - new Date(b.pay_at).getTime(),
  },
  { title: '用户名', dataIndex: 'username', key: 'username' },
  {
    title: '微信昵称',
    dataIndex: 'wx_nickname',
    key: 'wx_nickname',
  },
  {
    title: '注册时间',
    dataIndex: 'created_at',
    key: 'created_at',
  },
  {
    title: '操作',
    key: 'operation',
    align: 'left',
    customCell: () => {
      return {
        style: {
          //可以定义样式
          'text-align': 'left',
        },
      };
    },
  },
];

/**
 * 模态框表单渲染配置
 */
export const addOrEditFormGroup: FormGroupItem[] = [
  {
    key: 'username',
    fragmentKey: 'renderInput',
    originProps: {
      label: '用户名（账号名称）',
      name: 'username',
      rules: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { max: 30, trigger: 'change', message: '用户名长度不可大于30' },
      ],
    },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'phone',
    originProps: {
      label: '手机号（11位手机号）',
      name: 'phone',
      rules: [{ required: true, validator: validatePhone, trigger: 'blur' }],
    },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
];
