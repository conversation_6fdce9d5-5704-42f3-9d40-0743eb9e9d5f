import { GET, POST } from '@/services/api';

/** 生成账单判断是否订单处于取消中 */
export function superCheckOrderClosing(params: any) {
  return GET('/super/v2-order/check-order-closing', params);
}

/** 生成账单判断是否订单处于取消中 */
export function checkOrderClosing(params: any) {
  return GET('/order/check-order-closing', params);
}

/** 加急风控 */
export function expediteRiskOrder(params: any) {
  return GET('/super/order-distribution/set-expedite-risk-order', params);
}

/** 获取签约状态 */
export function getWithholdStatus(data: any) {
  return POST('/super/v2-order/withhold-status', data);
}

/**
 * @description: 查询当前订单发起物流变更申请的次数
 * @param {string} order_id
 * @return {*}
 */
export function logisticsChangeCheckCount(order_id) {
  return GET('/warehouse/logisticschanges/checkCount', { order_id }, { hostType: 'Golang' });
}

/**
 * @description: 查询商家是否在MDM平台有关联店铺
 * @param {string} server_id
 * @return {*}
 */

export function findMDMAffiliatedShop(server_id: string) {
  return GET('/order/mdm/queryServerIdBindMdm', { server_id }, { hostType: 'Golang' });
}

/**
 * @description:获取SCM对应参数
 * @param order_id
 * @returns { scm_auth,is_enterprise_sales,warehouse_list } 是否scm权限，是否企销订单，仓库列表-同alert接口
 */
export function getScmAuthInfo(order_id: string) {
  return GET('/v1-order-imei/scm-auth', { order_id });
}

// 是否可以申请修改寄出方式
export function isCanApplySend(order_id: string) {
  return GET('/super/orderLogisticsV2/order-logistics-v2/is-can-apply', { order_id });
}
