<template>
  <layout-shops-page title="回收报价">
    <template
      v-if="isShowDemand"
      #extra
    >
      <add-requirements @refresh="refresh" />
    </template>
    <div class="container">
      <div class="main">
        <a-tabs
          v-model:activeKey="activeKey"
          @change="activeKeyChange"
        >
          <a-tab-pane
            v-if="isShowDemand"
            :key="1"
            tab="需求列表"
          >
            <requirement-list ref="requirementListRef" />
          </a-tab-pane>
          <a-tab-pane
            :key="2"
            tab="代发货商品回收报价"
          />
          <a-tab-pane
            :key="3"
            tab="非代发货商品回收报价"
          />
          <a-tab-pane
            :key="4"
            tab="回收规则"
          >
            <recycle-rule ref="recycleRuleRef" />
          </a-tab-pane>
        </a-tabs>
        <today-price
          v-if="isShowPrice"
          :key="showPriceKey"
          ref="todayPriceRef"
          :active-key="showPriceKey"
        />
      </div>
    </div>
  </layout-shops-page>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import AddRequirements from './components/add-requirements.vue';
import RecycleRule from './components/recycle-rule.vue';
import RequirementList from './components/requirement-list.vue';
import TodayPrice from './components/today-price.vue';

const isShowDemand = false;

const activeKey = ref(2);
const requirementListRef = ref<InstanceType<typeof RequirementList>>();
const todayPriceRef = ref<InstanceType<typeof TodayPrice>>();

const showPriceKey = ref(2);
const isShowPrice = computed(() => {
  return [2, 3].includes(showPriceKey.value);
});

const activeKeyChange = (key: number) => {
  if (key === 1) {
    requirementListRef.value?.getTableList();
  }
  showPriceKey.value = key;
};
const refresh = () => {
  requirementListRef.value?.getTableList();
};
</script>

<style lang="less" scoped>
@import '../../common/base.less';
</style>
