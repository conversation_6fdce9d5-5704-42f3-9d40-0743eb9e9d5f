import { GET, POST } from '@/services/api';

interface ISkuInfoParams {
  category_id: number;
  brand_id: number;
  model_id: number;
}

/** 获取下发仓库基础数据（运营端） */
export function superGetSendWarehouseInfo(order_id: string) {
  return GET('/super/v3-order/send-warehouse-alert', { order_id });
}

/** 获取下发仓库基础数据（商家端） */
export function getSendWarehouseInfo(order_id: string) {
  return GET('/order/send-warehouse-alert', { order_id });
}

/** 获取型号 */
export function getModal(params: any) {
  return GET('/warehouse/SpuRelation', { source: 'skuCreate', ...params }, { hostType: 'Golang' });
}

/** 获取当前型号的sku信息 */
export function getSkuInfo(params: ISkuInfoParams) {
  return GET('/warehouse/AttrList', params, { hostType: 'Golang' });
}

/** 根据sku属性查找skuid */
export function searchSkuId(data: any) {
  return POST('/warehouse/FindSingleSku', data, { hostType: 'Golang' });
}

/** 分仓服务 */
export function subWarehouseService(data: any) {
  return POST(
    '/warehouse/WarehouseSplit',
    {
      trigger_way: 3,
      order_scene: 1, // 订单业务场景值
      created_port: 1, // 运营后台
      ...data,
    },
    { hostType: 'Golang' },
  );
}

/** 换货申请检测（运营端） */
export function superExchangeGoodsCheck(order_id: string) {
  return GET('/super/v3-order/exchange-goods-apply-check', { order_id });
}

/** 换货申请检测（商家端） */
export function exchangeGoodsCheck(order_id: string) {
  return GET('/order/exchange-goods-apply-check', { order_id });
}

/** 提交换货申请（运营端） */
export function superSubmitGoodsBarter(data: any) {
  return POST('/super/v3-order/exchange-goods-apply-submit', data);
}

/** 提交换货申请（商家端） */
export function submitGoodsBarter(data: any) {
  return POST('/order/exchange-goods-apply-submit', data);
}

/**
 * @description: 根据skuid找属性值
 * @param {any} data
 * @return {*}
 */
export function skuSearchBySkuId(data: { sku_id: string }) {
  return GET('/warehouse/SkuSearch', data, { hostType: 'Golang' });
}

/**
 * @Description 获取下发货品信息（商家端）
 * @param {any} params:any
 * @returns {any}
 */
export function getPurchaseInfo(params: any) {
  return GET('/shop-purchased-device/purchased-delivery-info', params);
}

/**
 * @Description 获取下发货品信息（运营端）
 * @param {any} params:any
 * @returns {any}
 */
export function getPurchaseInfoSuper(params: any) {
  return GET('/super/warehouse/shop-purchased-device/purchased-delivery-info', params);
}
