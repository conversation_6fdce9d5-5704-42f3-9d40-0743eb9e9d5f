import type { TableColumnType } from 'ant-design-vue';

import { reactive } from 'vue';

type PayType = {
  columns: TableColumnType[];
};

export default function (): PayType {
  const columns: TableColumnType[] = [
    {
      title: '应支付',
      dataIndex: 'amount',
      key: 'amount',
    },
    {
      title: '支付状态',
      dataIndex: 'state_desc',
      key: 'state_desc',
    },
    {
      title: '支付方式',
      dataIndex: 'pay_way',
      key: 'pay_way',
    },
    {
      title: '支付时间',
      dataIndex: 'pay_time',
      key: 'pay_time',
    },
  ];

  const billing = reactive<PayType>({
    columns,
  });

  return billing;
}
