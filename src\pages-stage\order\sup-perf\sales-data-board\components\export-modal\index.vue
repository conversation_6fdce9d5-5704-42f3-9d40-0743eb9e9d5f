<template>
  <div>
    <a-modal
      v-model:visible="visible"
      v-bind="modalConfig"
      :confirm-loading="loading"
    >
      <a-form
        ref="exportFormRef"
        :model="exportData"
        :rules="rules"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item
              label="数据分类"
              name="type"
            >
              <a-select
                v-model:value="exportData.type"
                allow-clear
                placeholder="请选择销售部门"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in selectList"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="exportData.type === 1"
            :span="12"
          >
            <a-form-item
              label="时间区间"
              name="month_range"
            >
              <a-range-picker
                v-model:value="exportData.month_range"
                picker="month"
                style="width: 100%"
                value-format="YYYY-MM"
              />
            </a-form-item>
          </a-col>
          <a-col
            v-if="exportData.type === 3"
            :span="12"
          >
            <a-form-item
              label="时间"
              name="month"
            >
              <a-date-picker
                v-model:value="exportData.month"
                picker="month"
                placeholder="请选择时间"
                style="width: 100%"
                value-format="YYYY-MM"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider style="margin: 0 0 24px 0" />
        <a-form-item
          label="导出字段"
          style="margin-bottom: 16px"
        >
          <a-checkbox
            v-model:checked="checkOptions.checkedAll"
            class="group-all"
            :indeterminate="checkOptions.indeterminate"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </a-form-item>
        <a-form-item
          class="form-item"
          label=""
          name="fields"
        >
          <a-spin :spinning="selectLoading">
            <a-checkbox-group v-model:value="exportData.fields">
              <a-row>
                <a-col
                  v-for="item in optionsList"
                  :key="item.value"
                  class="col-item"
                  :span="8"
                >
                  <a-checkbox
                    :disabled="exportData.type === 2 && item.value === 'sales_name'"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-spin>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { asyncEduce } from '@/utils/educe';
import { useVModel } from '@/hook';
import { getExportConfig } from './config';
import { GET } from '@/services/api';
import type { ModalProps } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';

interface IProps {
  visible?: boolean;
  deptId?: string | number | null;
}

interface IFormData {
  type: number;
  fields: string[];
  month_range: string[] | string;
  month: string;
}

const props = withDefaults(defineProps<IProps>(), {
  visible: false,
  deptId: '',
});

const emits = defineEmits(['update:visible']);

const visible = useVModel(props, 'visible', emits);
const loading = ref(false);
const route = useRoute();
const exportData = reactive<IFormData>({
  type: 1,
  fields: [],
  month_range: [],
  month: '',
});

const exportFormRef = ref<FormInstance>();
const { selectList, rules } = getExportConfig();
const selectLoading = ref(false);

// 获取多选数据
watch(
  () => props.visible,
  async val => {
    if (val) {
      for (const item of selectList.value) {
        if (!item.options.length) {
          selectLoading.value = true;
          try {
            const { data } = await GET(item.api);
            item.options = data.map((item: any) => ({
              label: item.value,
              value: item.key,
            }));
          } finally {
            selectLoading.value = false;
          }
        }
      }
    }
  },
);

const optionsList = computed(() => {
  const selectItem = selectList.value.find(item => item.value === exportData.type);
  return (selectItem && selectItem.options) ?? [];
});

const setFormData = (key: string, value: string | string[] = '') => {
  exportData[key] = value;
};

const checkOptions = reactive({
  checkedAll: false,
  indeterminate: true,
});

// 全选配置
watch(
  () => exportData.fields,
  val => {
    checkOptions.checkedAll = val.length === optionsList.value.length;
    checkOptions.indeterminate = val.length < optionsList.value.length;
  },
);

// 当类型为退租时默认选中销售姓名
watch(
  () => exportData.type,
  val => {
    setFormData('fields', []);
    switch (val) {
      case 1:
        setFormData('month');
        break;
      case 2:
        setFormData('fields', ['sales_name']);
        break;
      case 3:
        setFormData('month_range', []);
        break;
    }
  },
);

const onCheckAllChange = (e: Record<string, any>) => {
  if (e.target.checked) {
    exportData.fields = optionsList.value.map(item => item.value);
  } else {
    exportData.fields = exportData.type === 2 ? ['sales_name'] : [];
  }
};

const handleExportData = async () => {
  try {
    await exportFormRef.value?.validate();
    const selectItem = selectList.value.find(item => item.value === exportData.type);
    if (!selectItem) return;
    if (exportData.month_range) {
      exportData['month_range'] = exportData.month_range.toString();
    }
    await asyncEduce(
      selectItem.exportApi,
      {
        ...exportData,
        dept_id: props.deptId,
      },
      route.query.origin as string,
      'POST',
    );
    closeModal();
  } catch (_) {
  } finally {
    loading.value = false;
  }
};

const closeModal = () => {
  exportFormRef.value?.resetFields();
  emits('update:visible', false);
};

const modalConfig: ModalProps = {
  title: '导出',
  okText: '确认',
  width: 640,
  wrapClassName: 'export-modal-wrap',
  onOk: handleExportData,
  onCancel: closeModal,
};
</script>

<style lang="less" scoped>
.export-modal-wrap {
  .form-item {
    margin-bottom: -16px;
  }
  .form-item-disabled {
    margin-top: -8px;
    margin-bottom: 16px;
  }
  .col-item {
    margin-bottom: 16px;
  }
}
</style>
