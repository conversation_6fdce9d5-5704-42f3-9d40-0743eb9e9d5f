<template>
  <div class="rent-info">
    <!-- 押金部分 -->
    <extend :default-show="true">
      <template #label>
        <a-tooltip>
          <template #title>
            需付款/冻结押金
          </template>
          押金：￥{{ Number(deposit.deposit_need_pay_frozen).toFixed(2) }}
        </a-tooltip>
      </template>
      <template #content>
        <div
          v-if="deposit.credit_evaluate_quota"
          class="row"
        >
          信用评估额度：￥{{ Number(deposit.credit_evaluate_quota).toFixed(2) }}
        </div>
        <!-- 平台减免押金 -->
        <div
          v-if="deposit.platform_dec_deposit"
          class="row"
        >
          {{ deposit.platform_dec_deposit.title }}：￥{{ deposit.platform_dec_deposit.text }}
        </div>
        <!-- 已支付 -->
        <div
          v-if="deposit.already_pay"
          class="row freeze"
        >
          已支付：￥{{ Number(deposit.already_pay).toFixed(2) }}
        </div>
        <!-- 用户芝麻分数 -->
        <div
          v-if="deposit.zhima_score"
          class="row"
        >
          用户芝麻分：{{ deposit.zhima_score }}
        </div>
        <!-- 商品押金 -->
        <div
          v-if="deposit.depositGoods"
          class="row"
        >
          {{ deposit.depositGoods.title }}：￥{{ deposit.depositGoods.text }}
        </div>
      </template>
    </extend>
    <!-- 已转租金 -->
    <div
      v-if="deposit.partialPayAndThaw"
      class="row freeze"
    >
      {{ deposit.partialPayAndThaw.text }}
    </div>
    <!-- 已转支付 -->
    <div
      v-if="deposit.alreadyFreezeOrPay"
      class="row freeze"
    >
      {{ deposit.alreadyFreezeOrPay.text }}
    </div>
    <!-- 已退款或已解冻 -->
    <div
      v-if="deposit.alreadyThawOrRefund"
      class="row freeze"
    >
      {{ deposit.alreadyThawOrRefund.text }}
    </div>
    <!-- 已解冻 -->
    <div
      v-if="deposit.already_thaw"
      class="row freeze"
    >
      已解冻：￥{{ Number(deposit.already_thaw).toFixed(2) }}
    </div>
    <div class="divider" />
    <!-- 租金部分 -->
    <extend :default-show="true">
      <template #label>
        租金：￥{{ foundationData.rental_need_pay }}
        <a-tooltip
          v-if="rental.discountName"
          placement="topLeft"
        >
          <template
            v-if="rental.discountName.title"
            #title
          >
            <span>{{ rental.discountName.title }}</span>
          </template>
          <span class="discount-rental">￥{{ rental.discountName.text }}</span>
        </a-tooltip>
      </template>
      <template #content>
        <template v-if="overdueRecord.overdue_total_rent">
          <div class="row freeze flex-wrap flex-y-center">
            逾期租金：￥{{ overdueRecord.overdue_total_rent }}
            <i class="overdue-rent-icon" />
          </div>
          <div class="row freeze">
            (待缴纳：{{ overdueRecord.wait_pay }})
          </div>
        </template>
        <!-- 期数部分 -->
        <template v-if="rental && rental.gradData">
          <div
            v-if="rental.gradData.first_money"
            class="row"
          >
            首期：￥{{ rental.gradData.first_money }}
          </div>
          <div
            v-if="rental.gradData.fenqi_first_money"
            class="row"
          >
            首期：￥{{ rental.gradData.fenqi_first_money }}
          </div>
          <template v-if="rental.gradData.gradRentalItem">
            <div
              v-for="item in rental.gradData.gradRentalItem"
              :key="item.qi_shu"
              class="row"
            >
              {{ item.qi_shu }}：￥{{ item.month_rental }}
            </div>
          </template>
          <div
            v-if="rental.gradData.surplus_each_money"
            class="row"
          >
            {{ rental.gradData.surplus_each_mum }}期：￥{{ rental.gradData.surplus_each_money }}
          </div>
          <div
            v-if="rental.gradData.last_money"
            class="row"
          >
            尾期：￥{{ rental.gradData.last_money }}
          </div>
          <div
            v-if="rental.gradData.fenqi_last_money"
            class="row"
          >
            尾期：￥{{ rental.gradData.fenqi_last_money }}
          </div>
        </template>
      </template>
    </extend>

    <!-- 红包使用情况 -->
    <div
      v-if="rental.real_fenqi_num_txt"
      class="row"
    >
      {{ rental.real_fenqi_num_txt }}
    </div>

    <!-- 参与活动 -->
    <template v-if="rental.activityData">
      <div
        v-for="(item, index) in rental.activityData"
        :key="index"
        class="row"
      >
        {{ item.act_name }}（-￥{{ item.rental_reduce.toFixed(2) }}）
      </div>
    </template>
    <div class="divider" />

    <!-- 其他部分 -->
    <extend :default-show="true">
      <template #label>
        <span>数量：{{ other.item_num }}</span>
      </template>
    </extend>
    <div
      class="row"
      style="margin-top: 4px"
    >
      {{ other.freight }}
    </div>
    <div class="row">
      保险：￥{{ other.insurance_money }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

import type { DepositType, FoundationData,OtherType, RentalType } from '../../data';
import Extend from '../extend/index.vue';

defineProps({
  deposit: {
    type: Object as PropType<DepositType>,
    default() {
      return {};
    },
  },
  //租金
  rental: {
    type: Object as PropType<RentalType>,
    default() {
      return {};
    },
  },
  other: {
    type: Object as PropType<OtherType>,
    default() {
      return {};
    },
  },
  overdueRecord: {
    type: Object as PropType<RentalType>,
    default() {
      return {};
    },
  },
  // 基础数据
  foundationData: {
    type: Object as PropType<FoundationData>,
    required: true,
  },
});
</script>

<style lang="less" scoped>
.discount-rental {
  color: rgba(6, 21, 51, 0.45);
  font-size: 12px;
  text-decoration: line-through;
}
</style>
