import { POST } from '@/services/api';

interface ReportData {
  server_id: number;
  order_id: number;
  is_internal_server: 0 | 1;
  is_new: 0 | 1;
  replace_auth: 0 | 1;
  is_checked: 0 | 1;
  is_can_distribute: 0 | 1;
  store_type: 1 | 2;
}

class Reporter {
  private reportData: ReportData;
  constructor() {
    this.reportData = {
      server_id: 123,
      order_id: 123,
      is_internal_server: 0,
      is_new: 0,
      replace_auth: 0,
      is_checked: 0,
      is_can_distribute: 0,
      store_type: 1,
    };
  }

  public setReportData<F extends keyof ReportData, V extends ReportData[F]>(field: F, value: V) {
    this.reportData[field] = value;
  }

  public reportDataAction() {
    if ([this.reportData.order_id, this.reportData.server_id].includes(123)) return;
    const payload = { ...this.reportData };
    POST('/warehouse/sendwarehousealert/record', payload, { hostType: 'Golang' });
  }
}

export default new Reporter();
