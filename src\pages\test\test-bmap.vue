<template>
  <div class="map-container">
    <b-map
      ref="bmapRef"
      :center="center"
      height="800px"
      width="1000px"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';

import BMap from '@/components/bmap/bmap.vue';

const bmapRef = ref();
const center = ref({});

function getTrackData() {
  return new Promise(resolve => {
    resolve({
      data: {
        center: { x: 113.324896, y: 23.150698 },
        points: [
          { x: 113.324896, y: 23.150698 }, // 广州东站起点
          { x: 113.324512, y: 23.149287 },
          { x: 113.323987, y: 23.147865 },
          { x: 113.323153, y: 23.146423 },
          { x: 113.322046, y: 23.145112 },
          { x: 113.320541, y: 23.143876 }, // 林和西路
          { x: 113.318736, y: 23.142654 },
          { x: 113.316932, y: 23.141432 },
          { x: 113.314864, y: 23.140305 }, // 广州大道中
          { x: 113.312521, y: 23.139178 },
          { x: 113.310178, y: 23.138051 },
          { x: 113.307835, y: 23.136924 }, // 天河立交
          { x: 113.305492, y: 23.135797 },
          { x: 113.303149, y: 23.13467 },
          { x: 113.300806, y: 23.133543 }, // 内环路入口
          { x: 113.298463, y: 23.132416 },
          { x: 113.29612, y: 23.131289 },
          { x: 113.293777, y: 23.130162 }, // 环市中路
          { x: 113.291434, y: 23.129035 },
          { x: 113.289091, y: 23.127908 }, // 广州站终点
        ],
      },
    });
  });
}

async function openBMapTrack(points: Array<IBMapPoint>) {
  await bmapRef.value.loadBMapScript();
  bmapRef.value.initBMapContainer();
  bmapRef.value.drawTrackByPoints(points);
}

onMounted(() => {
  getTrackData().then(res => {
    center.value = res.data.center;
    openBMapTrack(res.data.points);
  });
});
</script>

<style scoped lang="less">
.map-container {
  position: relative;
  top: 50%;
  left: 50%;
  width: 1000px;
  height: 800px;
  background-color: #fff;
  border: 1px solid #eee;
  transform: translate(-50%, -50%);
}
</style>
