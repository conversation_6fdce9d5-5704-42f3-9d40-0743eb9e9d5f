<template>
  <div class="table-list">
    <div class="table-title">
      销售{{ month }}数据明细
    </div>
    <div class="table-content">
      <RTable
        ref="tableListRef"
        v-bind="tableListConfig"
        :use-table-options="useTableOptions"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { RTable } from 'rrz-web-design';
import { ref, computed, defineExpose } from 'vue';
import { getTableListConfig } from './config';
import { IFormData } from '../../data';
import { conversionSalesId } from '../../compositions/index';

interface IProps {
  searchForm: IFormData;
}

const props = defineProps<IProps>();

const tableListRef = ref<InstanceType<typeof RTable> | null>(null);
const { tableListConfig } = getTableListConfig();
const month = ref<string>('');

const useTableOptions = computed(() => ({
  formatHandle: (res: any) => {
    month.value = res.data.month;
    return res.data.list;
  },
  totalKey: 'data.pageData.count',
  pageKey: 'page',
  pageSizeKey: 'page_size',
  extraParams() {
    return {
      ...props.searchForm,
      sales_ids: conversionSalesId(props.searchForm.sales_ids),
    };
  },
}));

const getData = () => {
  tableListRef.value?.getTableList();
};
defineExpose({
  getData,
});
</script>

<style scoped lang="less">
.table-list {
  margin-top: 16px;
  padding: 24px 24px 0 24px;
  background: #fff;
  border-radius: 4px;
  .table-title {
    margin-bottom: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
  .table-content {
    :deep(.r-bottom-fix-table .ant-table-pagination) {
      padding: 16px 0 24px 0;
    }
    :deep(.r-bottom-fix-table .ant-table-pagination .ant-pagination-total-text) {
      line-height: 32px;
      text-align: right;
    }
  }
}
</style>
