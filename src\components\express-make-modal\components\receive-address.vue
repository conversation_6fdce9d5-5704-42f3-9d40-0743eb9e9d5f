<template>
  <div>
    <div class="address-box">
      <a-select
        v-model:value="provinceValue"
        :filter-option="filterOption"
        :options="provinceOptions"
        placeholder="请选择省份"
        show-search
        @change="
          () => {
            cityValue = null;
            areaValue = null;
          }
        "
      />
      <a-select
        v-model:value="cityValue"
        :filter-option="filterOption"
        :options="cityOptions"
        placeholder="请选择城市"
        show-search
        @change="
          () => {
            areaValue = null;
          }
        "
      />
      <a-select
        v-model:value="areaValue"
        :filter-option="filterOption"
        :options="areaOptions"
        placeholder="请选择市区"
        show-search
        @change="updateValue"
      />
    </div>
    <a-input
      v-model:value="addressValue"
      placeholder="请输入详细地址"
      style="margin-top: 8px"
      @change="updateValue"
    />
  </div>
</template>

<script lang="ts" setup>
import { type PropType, watch } from 'vue';
import { useInjectFormItemContext } from 'ant-design-vue/es/form';

import { useAddress } from '../composables/use-address';
import type { IReceiveAddressProps } from '../data';

const props = defineProps({
  value: {
    type: Object as PropType<IReceiveAddressProps>,
    default: () => ({}),
    required: true,
  },
});

const emits = defineEmits(['update:value']);

const {
  provinceValue,
  cityValue,
  areaValue,
  addressValue,

  provinceOptions,
  cityOptions,
  areaOptions,

  filterOption,
  requestAddressData,
} = useAddress(props);

const formItemContext = useInjectFormItemContext();

watch(
  () => props.value,
  val => {
    if (Object.prototype.toString.call(val) !== '[object Object]') {
      return console.error('props.value类型错误');
    }
    if (Object.keys(val).length) {
      const { province, city, area, address } = val;
      provinceValue.value = province;
      cityValue.value = city;
      areaValue.value = area;
      addressValue.value = address;
    }
  },
  { immediate: true }
);

watch(
  () => provinceValue.value,
  val => {
    if (val) {
      requestAddressData({ name: val, level: 1 });
    } else {
      cityOptions.value = [];
      areaOptions.value = [];
    }
    updateValue();
  },
  { immediate: true }
);

watch(
  () => cityValue.value,
  val => {
    if (val) {
      requestAddressData({ name: val, level: 2 });
    } else {
      areaOptions.value = [];
    }
    updateValue();
  },
  { immediate: true }
);

function updateValue() {
  emits('update:value', {
    province: provinceValue.value,
    city: cityValue.value,
    area: areaValue.value,
    address: addressValue.value,
  });
  formItemContext.onFieldChange();
}
</script>

<style lang="less" scoped>
.address-box {
  display: flex;
  gap: 8px;
}
</style>
