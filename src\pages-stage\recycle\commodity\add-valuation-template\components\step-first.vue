<template>
  <a-form layout="vertical">
    <form-card title="基础信息">
      <a-form-item
        label="模板名称"
        v-bind="validateInfos.name"
      >
        <a-row :gutter="24">
          <a-col :span="8">
            <a-input
              v-model:value="formState.name"
              :maxlength="12"
              placeholder="请输入模板名称（如：手机-苹果）"
              show-count
            />
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item v-bind="validateInfos.condition_type">
        <template #label>
          <span>成新度类型</span>
        </template>
        <a-row
          :gutter="24"
          style="margin-top: -12px; margin-bottom: -12px"
        >
          <a-col
            v-for="(item, index) in formState.condition_type"
            :key="index"
            :span="8"
            style="padding-top: 12px; padding-bottom: 12px"
          >
            <a-input
              v-model:value="item.value"
              :maxlength="20"
              placeholder="请输入成新度类型（如：99新）"
              show-count
            >
              <template #suffix>
                <a-popconfirm
                  v-if="formState.condition_type.length > 1"
                  title="确定要删除该项吗？"
                  @confirm="() => deleteConditionType(index)"
                >
                  <MinusCircleOutlined class="minus-btn" />
                </a-popconfirm>
              </template>
            </a-input>
          </a-col>
          <a-col
            v-if="formState.condition_type.length < 8"
            :span="8"
            style="padding-top: 12px; padding-bottom: 12px"
          >
            <a-button
              block
              type="dashed"
              @click="addConditionType"
            >
              <template #icon>
                <PlusOutlined />
              </template>
              <span>增加成新度类型</span>
            </a-button>
          </a-col>
        </a-row>
      </a-form-item>
    </form-card>
    <form-card title="检测评估内容">
      <a-form-item v-bind="validateInfos.qua_check_option">
        <a-select
          :max-tag-count="5"
          :max-tag-text-length="8"
          mode="multiple"
          :options="quaOptions"
          placeholder="请选择检测评估内容"
          style="width: 732px"
          :value="formState.qua_check"
          @deselect="(val: any) => onDeselect(val, 'qua')"
          @select="(val: any) => onSelect(val, 'qua')"
        >
          <template #tagRender="{ label, option }">
            <a-tooltip>
              <template #title>
                {{ option.label }}
              </template>
              <a-tag style="margin-right: 3px; cursor: default">
                {{ label }}
              </a-tag>
            </a-tooltip>
          </template>
        </a-select>
      </a-form-item>
      <template v-if="formState.qua_check_option.length">
        <div class="seperator" />
        <a-row
          :gutter="24"
          style="margin-top: -12px; margin-bottom: -12px"
        >
          <a-col
            v-for="(item, index) in formState.qua_check_option"
            :key="index"
            :span="8"
            style="margin-top: 12px; margin-bottom: 12px"
          >
            <a-collapse
              :bordered="false"
              expand-icon-position="right"
              ghost
            >
              <template #expandIcon="{ isActive }">
                <a-space
                  :size="4"
                  style="display: inline-flex"
                >
                  <down-outlined :rotate="isActive ? 180 : 0" />
                  <span>{{ isActive ? '收起' : '展开' }}</span>
                </a-space>
              </template>
              <a-collapse-panel>
                <template #header>
                  <div
                    class="flex-wrap flex-y-center"
                    style="width: 100%"
                  >
                    <a-popconfirm
                      title="确定要删除该项吗？"
                      @click.stop="() => {}"
                      @confirm="() => onDeleteOption('qua', item.origin_log_id, index)"
                    >
                      <MinusCircleOutlined class="delete-btn" />
                    </a-popconfirm>
                    <a-tooltip>
                      <template #title>
                        {{ item.name }}
                      </template>
                      <span
                        style="margin: 0 20px 0 8px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
                      >{{ item.name }}</span>
                    </a-tooltip>
                  </div>
                </template>
                <ul>
                  <li
                    v-for="(option, i) in item.option"
                    :key="i"
                  >
                    {{ option }}
                  </li>
                </ul>
              </a-collapse-panel>
            </a-collapse>
          </a-col>
        </a-row>
      </template>
    </form-card>
    <form-card title="功能评估内容">
      <a-form-item v-bind="validateInfos.function_option">
        <a-select
          :max-tag-count="5"
          :max-tag-text-length="8"
          mode="multiple"
          :options="functionOptions"
          placeholder="请选择功能评估内容"
          style="width: 732px"
          :value="formState.function"
          @deselect="(val: any) => onDeselect(val, 'function')"
          @select="(val: any) => onSelect(val, 'function')"
        >
          <template #tagRender="{ label, option }">
            <a-tooltip>
              <template #title>
                {{ option.label }}
              </template>
              <a-tag style="margin-right: 3px; cursor: default">
                {{ label }}
              </a-tag>
            </a-tooltip>
          </template>
        </a-select>
      </a-form-item>
      <template v-if="formState.function_option.length">
        <div class="seperator" />
        <a-row
          :gutter="24"
          style="margin-top: -12px; margin-bottom: -12px"
        >
          <a-col
            v-for="(item, index) in formState.function_option"
            :key="index"
            :span="8"
            style="margin-top: 12px; margin-bottom: 12px"
          >
            <a-collapse
              :bordered="false"
              expand-icon-position="right"
              ghost
            >
              <template #expandIcon="{ isActive }">
                <a-space
                  :size="4"
                  style="display: inline-flex"
                >
                  <down-outlined :rotate="isActive ? 180 : 0" />
                  <span>{{ isActive ? '收起' : '展开' }}</span>
                </a-space>
              </template>
              <a-collapse-panel>
                <template #header>
                  <div
                    class="flex-wrap flex-y-center"
                    style="width: 100%"
                  >
                    <a-dropdown
                      trigger="click"
                      @click.stop
                    >
                      <MenuOutlined :class="['menu-btn', item.is_multi && 'active']" />
                      <template #overlay>
                        <a-menu>
                          <a-menu-item>
                            <a
                              href="javascript:;"
                              @click="toggleMulti(item)"
                            >{{
                              item.is_multi ? '关闭多选' : '开启多选'
                            }}</a>
                          </a-menu-item>
                          <a-menu-item>
                            <a
                              href="javascript:;"
                              @click="() => onDeleteOption('function', item.origin_log_id, index)"
                            >删除</a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                    <a-tooltip>
                      <template #title>
                        {{ item.name }}
                      </template>
                      <span
                        :style="{
                          margin: '0 20px 0 8px',
                          overflow: 'hidden',
                          'white-space': 'nowrap',
                          'text-overflow': 'ellipsis',
                          color: item.is_multi ? 'var(--ant-primary-color)' : 'inherit',
                        }"
                      >{{ item.name }}</span>
                    </a-tooltip>
                  </div>
                </template>
                <ul>
                  <li
                    v-for="(option, i) in item.option"
                    :key="i"
                  >
                    {{ option }}
                  </li>
                </ul>
              </a-collapse-panel>
            </a-collapse>
          </a-col>
        </a-row>
      </template>
    </form-card>
  </a-form>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue';
import { DownOutlined, MenuOutlined,MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import FormCard from '../../../common/components/form-card.vue';
import useStepFirst from '../composables/use-step-first';
import { ITemplate } from '../data';

const props = defineProps<{
  formData: ITemplate;
}>();

const {
  quaOptions,
  functionOptions,

  formState,
  validate,
  validateInfos,

  init,

  addConditionType,
  deleteConditionType,

  onSelect,
  onDeselect,
  onDeleteOption,
  toggleMulti,
} = useStepFirst(props);

defineExpose({
  validate: () => {
    return new Promise(resolve => {
      const closeLoading = message.loading();
      validate()
        .then(() => {
          const { name, condition_type, qua_check_option, function_option, condition_set } = formState;
          const payload = {
            name,
            condition_type: condition_type.filter(_ => _.value).map(_ => _.value),
            condition_set,
            qua_check_option,
            function_option,
          };
          resolve(payload);
        })
        .catch(err => {
          console.error(err);
        })
        .finally(() => {
          closeLoading();
        });
    });
  },
  init,
});
</script>

<style lang="less" scoped>
:deep(.ant-input-show-count-has-suffix) {
  margin-right: 4px;
}
.ant-form-item:last-child {
  margin-bottom: 0;
}
.delete-btn {
  color: #9298a6;
  transition: color 0.3s;
  &:active,
  &:hover,
  &:focus {
    color: var(--ant-error-color);
  }
}
.menu-btn {
  color: #9298a6;
  transition: color 0.3s;
  &:active,
  &:hover,
  &:focus {
    color: var(--ant-primary-color);
  }
  &.active {
    color: var(--ant-primary-color);
  }
}
.seperator {
  margin: 24px 0;
  border-top: 1px solid #e9e9e9;
}
// 自定义折叠面板
:deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
  background: #f7f9fc;
  border-radius: 4px !important;
}
:deep(.ant-collapse-content > .ant-collapse-content-box) {
  padding-right: 0;
  padding-left: 0;
  & > ul {
    margin: 0;
    padding-left: 34px;
    list-style: auto;
    border-left: 2px solid var(--ant-primary-color);
  }
  & > ul > li {
    padding-left: 2px;
  }
}
// 自定义可删除表单删除按钮
:deep(.ant-input-affix-wrapper:has(.minus-btn:focus)) {
  border-color: var(--ant-error-color);
}
:deep(.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover),
:deep(.ant-input-affix-wrapper-focused:hover),
:deep(.ant-input-affix-wrapper-focused) {
  .ant-input-show-count-has-suffix {
    display: none;
  }
  .minus-btn {
    display: block;
  }
}
:deep(.ant-input-suffix) {
  &:has(.minus-btn:focus) {
    .ant-input-show-count-has-suffix {
      display: none;
    }
  }
}
.minus-btn {
  display: none;
  color: #9298a6;
  transition: color 0.3s;
  &:active,
  &:hover,
  &:focus {
    display: block;
    color: var(--ant-error-color);
  }
}
</style>
