<template>
  <div class="shipment-data-board">
    <header class="page-header">
      <span class="title">发货数据查询</span>
    </header>
    <search-page @get-inspection-dimension="getInspectionDimension" />
  </div>
</template>

<script setup lang="ts">
import type { OptionMapType } from './data';

import { ref } from 'vue';

import SearchPage from './components/search-page.vue';

const optionMap = ref<OptionMapType>({});

function getInspectionDimension(value: OptionMapType['inspection_dimension']) {
  optionMap.value.inspection_dimension = value;
}
</script>

<style scoped lang="less">
@import '@/style/variables.less';

.shipment-data-board {
  height: 100%;
  background-color: #f5f7fa;
}
.shipment {
  padding-bottom: 10px;
}
.page-header {
  position: relative;
  display: flex;
  align-items: center;
  padding: 24px 24px 0;
  background-color: #fff;
  .title {
    color: @font-black-85;
    font-weight: bold;
    font-size: 20px;
  }
}
.banner {
  position: relative;
  .banner-icon {
    width: 100%;
  }

  .close-icon {
    position: absolute;
    top: 8px;
    right: 12px;
    width: 18px;
    height: 18px;
    cursor: pointer;
  }
}

.tab-list {
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
    background-color: #fff;
    padding-inline: 24px;
  }
}
</style>
