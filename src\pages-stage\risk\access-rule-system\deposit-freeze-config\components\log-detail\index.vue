<template>
  <a-drawer
    :destroy-on-close="true"
    placement="right"
    :visible="props.value"
    width="100%"
    @close="emit('update:value', false)"
  >
    <div class="header">
      <div>
        <span class="header-title">修改日志</span>
        <a-radio-group v-model:value="logVersion">
          <a-radio-button value="old">
            修改前
          </a-radio-button>
          <a-radio-button value="new">
            修改后
          </a-radio-button>
        </a-radio-group>
      </div>
    </div>

    <div class="content">
      <div class="color-legend">
        <span class="add-legend">新增</span>
        <span class="update-legend">修改</span>
        <span class="del-legend">删除</span>
      </div>
      <a-spin
        :spinning="tbLoading"
        tip="加载中..."
      >
        <a-table
          v-for="item in taData[logVersion]"
          :key="item"
          bordered
          class="content-item"
          :columns="tbCol"
          :data-source="item"
          :pagination="false"
          :scroll="{ x: 1280 }"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.key === 'type'">
              <template v-if="record.ruleType === '0'">
                兜底规则
              </template>
              <template v-else>
                <div class="category-box">
                  <div class="category-box-item">
                    <a-tag color="#00C8BE">
                      类目
                    </a-tag>:
                    <template
                      v-for="tag in text.category"
                      :key="tag.id"
                    >
                      <a-tag :color="TAG_COLOR[tag.status]">
                        {{ tag.name }}
                      </a-tag>
                    </template>
                  </div>
                  <a-divider />
                  <div class="category-box-item">
                    <a-tag color="#3777FF">
                      型号
                    </a-tag>:
                    <template
                      v-for="tag in text.model"
                      :key="tag.id"
                    >
                      <a-tag :color="TAG_COLOR[tag.status]">
                        {{ tag.name }}
                      </a-tag>
                    </template>
                  </div>
                </div>
              </template>
            </template>
            <template v-else-if="column.key === 'deposit'">
              {{ text.name }}
            </template>
            <template v-else-if="column.key === 'condition'">
              {{ text.name }}
            </template>
            <template v-else-if="column.renderType === 'inputNumber'">
              <a-input-number
                addon-after="%"
                :bordered="false"
                :class="record.user_risk_levels[column.dataIndex].status"
                :controls="false"
                :disabled="true"
                :value="record.user_risk_levels[column.dataIndex].value"
              />
            </template>
          </template>
        </a-table>
      </a-spin>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { deepClone } from '@/utils/base';
import { ref, watch, reactive } from 'vue';
import { tbCol } from '../../config';
import { useOptions } from '../../composables/use-options';
import { ICategoryIdsItem, IConfigItem, ITbDataItem } from './data';
import { apiGetLogDetail } from './services';

const TAG_COLOR = {
  added: 'blue',
  deleted: 'red',
};
const props = defineProps({
  value: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
});
const emit = defineEmits<{
  (event: 'update:value', value: boolean): void;
}>();
const { taDataItem, getFreezeRuleOptions } = useOptions();
const logVersion = ref('old');
const oldTbData = ref<ITbDataItem[]>([]);
const newTbData = ref<ITbDataItem[]>([]);

const taData = reactive({
  old: oldTbData,
  new: newTbData,
});
const tbLoading = ref(true);
function handleRuleList(ruleList: IConfigItem[]) {
  const tbData: ITbDataItem[] = [];

  ruleList.forEach(dataItem => {
    const arr = [];
    const data: ITbDataItem[] = deepClone(taDataItem.value);
    data.map(item => {
      const { id, category_ids, model_ids, tmp_config, type } = dataItem;
      const { condition, deposit } = item;
      const statusKey = `${id}-${deposit.id}-${condition.id}`;
      item.id = id;
      item.type.category = category_ids;
      item.type.model = model_ids;
      item.ruleType = type;
      item.user_risk_levels = tmp_config.user_risk_levels[statusKey];
      arr.push(item);
    });
    tbData.push((data as unknown) as ITbDataItem);
  });
  console.log(tbData);
  return tbData;
}

function handleIds(target: ICategoryIdsItem[], temp: ICategoryIdsItem[], mark: 'added' | 'deleted') {
  target.forEach((item: ICategoryIdsItem) => {
    // 看看当前的id是否存在在oldConfig中
    let index = -1;
    if (temp.length) {
      index = temp.findIndex((tempItem: ICategoryIdsItem) => tempItem.id === item.id);
    }
    item.status = index === -1 ? mark : '';
  });
  if (!temp.length) {
    console.log(target);
  }
}
function handleLevels(
  target: { [key: string]: number[] },
  tmep: { [key: string]: number[] } | undefined,
  mark: 'added' | 'updated' | 'deleted' | '',
) {
  for (const rKey in target) {
    (target[rKey] as unknown) = target[rKey].map((item, index) => {
      return {
        value: item,
        status: tmep ? (tmep[rKey][index] !== item ? mark : '') : mark,
      };
    });
  }
}
function diffConfigToMark(oldConfig: { [key: string]: IConfigItem }, newConfig: { [key: string]: IConfigItem }) {
  // 以新数据为基准,打标识 旧数据中不存在的数据为新增,旧数据中存在的数据为删除, 旧数据中存在的数据,如果值不同则为更新
  for (const key in newConfig) {
    // 旧数据中存在的数据, diff求出差异
    if (oldConfig[key]) {
      handleIds(newConfig[key].category_ids, oldConfig[key].category_ids, 'added');
      handleIds(newConfig[key].model_ids, oldConfig[key].model_ids, 'added');
      handleLevels(newConfig[key].tmp_config.user_risk_levels, oldConfig[key].tmp_config.user_risk_levels, 'updated');
    } else {
      handleIds(newConfig[key].category_ids, [], 'added');
      handleIds(newConfig[key].model_ids, [], 'added');
      handleLevels(newConfig[key].tmp_config.user_risk_levels, undefined, 'added');
    }
  }
  for (const key in oldConfig) {
    // 新数据中存在, diff 求出差异
    if (newConfig[key]) {
      handleIds(oldConfig[key].category_ids, newConfig[key].category_ids, 'deleted');
      handleIds(oldConfig[key].model_ids, newConfig[key].model_ids, 'deleted');
      handleLevels(oldConfig[key].tmp_config.user_risk_levels, newConfig[key].tmp_config.user_risk_levels, '');
    } else {
      handleIds(oldConfig[key].category_ids, [], 'deleted');
      handleIds(oldConfig[key].model_ids, [], 'deleted');
      handleLevels(oldConfig[key].tmp_config.user_risk_levels, undefined, 'deleted');
    }
  }
}

/**
 * 将配置项转换为hash
 * @param config 配置项
 */
function handleConfigToHash(config: IConfigItem[]) {
  const hash = {};
  config.forEach(item => {
    hash[item.id] = item;
  });
  return hash;
}

function getLogDetail() {
  tbLoading.value = true;
  apiGetLogDetail({
    id: props.id,
  })
    .then(res => {
      const { new_config, old_config } = res.data;
      const newConfig = handleConfigToHash(new_config);
      const oldConfig = handleConfigToHash(old_config);

      diffConfigToMark(oldConfig, newConfig);
      console.log(new_config);
      newTbData.value = handleRuleList(new_config);
      oldTbData.value = handleRuleList(old_config);
    })
    .finally(() => {
      tbLoading.value = false;
    });
}
watch(
  () => props.value,
  async (value: boolean) => {
    if (value) {
      await getFreezeRuleOptions();
      logVersion.value = 'old';
      getLogDetail();
    }
  },
);
</script>
<style lang="less" scoped>
.header {
  position: sticky;
  top: -24px;
  z-index: 9;
  display: flex;
  justify-content: space-between;
  padding: 20px 24px;
  background-color: #fff;
  border-bottom: 1px solid #e9e9e9;
  .header-title {
    margin-right: 24px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
  .header-btns {
    .ant-btn {
      margin-left: 8px;
    }
  }
}
.content {
  padding: 24px;
  text-align: center;
  background-color: #fff;
  .color-legend {
    text-align: left;
    .add-legend::before {
      display: inline-block;
      width: 16px;
      height: 6px;
      margin-right: 8px;
      background-color: #1890ff;
      border-radius: 2px;
      content: '';
    }
    .del-legend::before {
      display: inline-block;
      width: 16px;
      height: 6px;
      margin-right: 8px;
      margin-left: 16px;
      background-color: #ff4d4f;
      border-radius: 2px;
      content: '';
    }
    .update-legend::before {
      display: inline-block;
      width: 16px;
      height: 6px;
      margin-right: 8px;
      margin-left: 16px;
      background-color: #13c2c2;
      border-radius: 2px;
      content: '';
    }
  }
  .ant-spin {
    margin-top: 50px;
  }
  .content-item {
    margin-top: 24px;
  }
  .category-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .category-box-item {
      min-height: 124px;
      .box-item-title {
        margin-bottom: 8px;
        padding: 8px 0;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        line-height: 22px;
        text-align: center;
        background: #f0f7ff;
        border-radius: 4px;
      }
      .ant-tag {
        margin: 8px;
      }
    }
  }
  .ant-pagination {
    margin-top: 24px;
    text-align: right;
  }
}
:deep(.ant-table-cell) {
  &:has(.ant-input-number) {
    padding: 0;
  }
  &:has(.added) {
    box-shadow: 0 0 2px 1px #1890ff inset;
  }
  &:has(.updated) {
    box-shadow: 0 0 2px 1px #00c8be inset;
  }
  .ant-input-number-disabled {
    color: rgba(6, 21, 51, 0.65);
    background-color: rgba(0, 0, 0, 0);
  }

  .ant-input-number {
    width: 100%;
    height: 55px;
    margin: 0;
    .ant-input-number-input-wrap,
    .ant-input-number-input {
      height: 100%;
    }
  }
  .ant-input-number-group-addon {
    // 透明背景色
    background: rgba(0, 0, 0, 0);
    border: none;
    border-radius: 0;
  }
}
</style>
