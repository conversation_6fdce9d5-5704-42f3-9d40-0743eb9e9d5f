import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';

/**
 * 检索表单渲染配置
 */
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'phone1',
    originProps: { label: '姓名', name: 'phone' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'username1',
    fragmentKey: 'renderInput',
    originProps: { label: '手机', name: 'username' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'phone2',
    originProps: { label: '省份', name: 'phone' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'username2',
    fragmentKey: 'renderInput',
    originProps: { label: '城市', name: 'username' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'phone3',
    originProps: { label: '区/县', name: 'phone' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'username3',
    fragmentKey: 'renderInput',
    originProps: { label: '地址', name: 'username' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'created_at',
    fragmentKey: 'renderRangePicker',
    originProps: { label: '创建时间', name: 'created_at' },
    elProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
      style: { width: '100%' },
    },
  },
];

/**
 * 验证是否为手机号码
 */
// const isPhone = (phone: string) => {
//   const reg = /^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/;
//   return reg.test(phone);
// };

export const addOrEditFormGroup: FormGroupItem[] = [
  {
    key: 'name',
    originProps: {
      label: '收货人姓名',
      name: 'name',
      rules: [
        {
          message: '请输入收货人姓名',
          required: true,
          trigger: 'blur',
        },
        {
          message: '收货人姓名长度不可大于30',
          max: 30,
          trigger: 'change',
        },
      ],
    },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'phone',
    fragmentKey: 'renderInput',
    originProps: {
      label: '手机号码',
      name: 'phone',
      rules: [
        {
          message: '手机号已超过11位或填写有误，请重新填写',
          max: 11,
          trigger: 'change',
        },
        {
          message: '请输入手机号码',
          required: true,
          trigger: 'blur',
        },
      ],
    },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
];

/**
 * 表格columns
 */

export const columns: ColumnType[] = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'index',
  },
  {
    title: '手机',
    dataIndex: 'phone',
    key: 'phone',
    // sortDirections: ['descend', 'ascend'],
    // sorter: (a: any, b: any) => new Date(a.pay_at).getTime() - new Date(b.pay_at).getTime(),
  },
  { title: '省份', dataIndex: 'province', key: 'province' },
  { title: '城市', dataIndex: 'city', key: 'city' },
  { title: '区/县级', dataIndex: 'region', key: 'region' },
  { title: '地址', dataIndex: 'address', key: 'address' },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    sorter: {
      compare: (a, b) => {
        const aTime = new Date(a.created_at).getTime();
        const bTime = new Date(b.created_at).getTime();
        return aTime - bTime;
      },
    },
  },
  {
    title: '操作',
    key: 'operation',
    align: 'left',
    fixed: 'right',
    width: 200,
  },
];
