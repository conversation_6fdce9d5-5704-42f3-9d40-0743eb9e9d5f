!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports):"function"==typeof define&&define.amd?define(["exports"],o):o((e="undefined"!=typeof globalThis?globalThis:e||self).GLWidget={})}(this,(function(e){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var o=function(){return(o=Object.assign||function(e){for(var o,t=1,r=arguments.length;t<r;t++)for(var i in o=arguments[t])Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i]);return e}).apply(this,arguments)},t=function(){function e(e){this.config=o({showTips:!0,customTips:"",tips:{logo:"",url:"",copyright:"",name:""},support:{IE:"",EDGE:"",Firefox:"",Chrome:"",Opera:"",Safari:"",Unkonwn:"",iphone:"",android:"",ipad:""}},e)}return e.prototype.getExplore=function(){var e,o={},t=navigator.userAgent.toLowerCase();console.log("当前浏览器版本",t);var r=this.config.support||{};return(e=t.match(/(ipad).*os\s([\d_]+)/))?o.ipad=e[1]:(e=t.match(/(iphone\sos)\s([\d_]+)/))?o.iphone=e[1]:(e=t.match(/(android)\s+([\d.]+)/))&&(o.android=e[1]),o.ipad?{type:"ipad",version:o.ipad,support:r.ipad||"all"}:o.iphone?{type:"iphone",version:o.iphone,support:r.iphone||"all"}:o.android?{type:"android",version:o.android,support:r.android||"all"}:((e=t.match(/rv:([\d.]+)\) like gecko/))||(e=t.match(/msie ([\d\.]+)/))?o.ie=e[1]:(e=t.match(/edge\/([\d\.]+)/))?o.edge=e[1]:(e=t.match(/firefox\/([\d\.]+)/))?o.firefox=e[1]:(e=t.match(/(?:opera|opr).([\d\.]+)/))?o.opera=e[1]:(e=t.match(/chrome\/([\d\.]+)/))?o.chrome=e[1]:(e=t.match(/version\/([\d\.]+).*safari/))&&(o.safari=e[1]),o.ie?{type:"IE",version:o.ie,support:r.IE||"all"}:o.edge?{type:"EDGE",version:o.edge,support:r.EDGE||"all"}:o.firefox?{type:"Firefox",version:o.firefox,support:r.Firefox||"all"}:o.chrome?{type:"Chrome",version:o.chrome,support:r.Chrome||"all"}:o.opera?{type:"Opera",version:o.opera,support:r.Opera||"all"}:o.safari?{type:"Safari",version:o.safari,support:r.Safari||"all"}:{type:"Unkonwn",support:r.Unkonwn||"all"})},e.prototype.showNoSupportTips=function(){if(this.config.showTips){var e=this.config.tips||{},o=e.logo,t=e.url,r=e.copyright,i=e.name,n=this.config.customTips;document.body.innerHTML=n||'\n      <div style="color: #666;">\n        '+(o?'<img style="margin: 100px auto 0; display: block; width: 200px;" src="'+o+'" />':"")+'\n        <div style="padding: 0 0 64px; overflow: hidden;">\n          <div style="position: relative; height: 200px; margin: 150px auto; display: flex; justify-content: center; align-items: center; max-width: 80%;">\n            <div style="font-size: 14px; line-height: 2;">\n              <p><span>使用当前浏览器访问，无法享受最佳体验，推荐升级到最新版本或使用<a href="https://www.google.cn/intl/zh-CN/chrome/" target="_blank"> Chrome </a> 浏览器进行访问。</span></p>\n              <p><span>Use the current browser to access, you cant enjoy the best experience. It is recommended to upgrade your browser to latest version, or you can use<a href="https://www.google.com/chrome/" target="_blank"> Chrome </a> browser.</span></p>\n            </div>\n          </div>\n        </div>\n        <div style="padding-bottom: 20px; color: #999; text-align: center;">\n          <div style="font-size: 12px;">\n            '+(i?'<p><a href="'+t+'" target="_blank">© '+i+"</a></p>":"")+"\n            "+(r?'<p><a href="'+t+'" target="_blank">'+r+"</a></p>":"")+"\n          </div>\n        </div>\n      </div>"}else console.error("浏览器不兼容，请下载Chrome浏览器以获得更好体验")},e.prototype.checkSupport=function(){var e=this.getExplore();"all"!==e.support&&("none"!==e.support&&e.version?parseInt(e.support.toString())>parseInt(e.version.toString())&&this.showNoSupportTips():this.showNoSupportTips())},e}();e.BrowserVersionTool=t,Object.defineProperty(e,"__esModule",{value:!0})}));
