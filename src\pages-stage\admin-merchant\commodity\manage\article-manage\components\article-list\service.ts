import { GET, POST } from '@/services/api';
export function updateArticle(data: { ModNews: any; id: string | number }): Promise<any> {
  return POST('/shop/news/update', data);
}

export function createArticle(data: { ModNews: any }): Promise<any> {
  return POST('/shop/news/create', data);
}

export function fetchCate(): Promise<any> {
  return GET('/shop/news/create');
}

export function deleteArticleById(data: { id: string | number }): Promise<any> {
  return POST('/shop/news/delete', data);
}

export function getNewsIndexInf(data: any): Promise<any> {
  return GET('/shop/news/index-inf', data);
}
export function getCategoryList(): Promise<any> {
  return GET('/shop/news/create');
}
