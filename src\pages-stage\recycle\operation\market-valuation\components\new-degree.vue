<template>
  <div class="model flex-wrap flex-vertical flex-con">
    <a-row :gutter="16">
      <a-col :span="16">
        <div class="model-detail">
          <div class="top">
            <div class="top-left">
              <a-button @click="handleAll">
                全部机型
              </a-button>
              <div class="title">
                <a-breadcrumb>
                  <a-breadcrumb-item
                    v-for="(item, index) in breadCrumbList"
                    :key="item.type"
                    class="title-label"
                    @click="handleBread(index)"
                  >
                    {{ item.label }}
                  </a-breadcrumb-item>
                </a-breadcrumb>
              </div>
            </div>
            <week-picker
              v-model:value="dateRange"
              class="block"
              @pick="(timeArr: dayjs.Dayjs[]) => onRangeChange(timeArr)"
            />
          </div>
          <div
            class="content"
            style="margin-top: 12px"
          >
            <div
              id="chart"
              style="margin-top: 12px"
            />
          </div>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="model-config">
          <div class="config-top">
            <div
              v-for="(item, index) in configTabsList"
              :key="item.platform"
              :class="{ 'config-title': true, 'config-title__active': configIndex === index }"
              @click="handleConfig(index)"
            >
              {{ item.platform_name }}
            </div>
            <a-dropdown>
              <SettingOutlined class="config-icon" />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-popconfirm
                      title="你确定删除该项吗？"
                      @confirm="handleDelete"
                    >
                      <template #icon>
                        <ExclamationCircleFilled style="color: red" />
                      </template>
                      <span>删除</span>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <span @click="handleEdit">编辑</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="config-container">
            <div
              v-for="(item, index) in configTabsList"
              :key="item.platform"
            >
              <ShowConfig
                v-show="index === configIndex"
                :key="configKey"
                class="config-detail"
                :options-dict="item.options_dict"
                :options-list="item.option_list"
              />
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
  <AddMarkerDrawer
    :id="currentId"
    v-model:visible="editVisible"
    type="edit"
    @on-load-list="reset"
  />
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { PropType } from 'vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import { message } from 'ant-design-vue';
import { SettingOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue';
import WeekPicker from '../../../common/components/week-picker.vue';
import { useLineChart } from '../../../common/use-line-chart';
import type { IModelOptionConfig, IBreadCrumb, IRequestModelParams } from '../data';
import { getEvaluationConfig, queryChartDetail, deleteTask } from '../service';
import ShowConfig from './show-config.vue';
import AddMarkerDrawer from './add-marker-drawer.vue';

const props = defineProps({
  breadCrumbList: {
    type: Array as PropType<IBreadCrumb[]>,
    default: () => [],
  },
});

const emits = defineEmits(['update:breadCrumbList']);

const editVisible = ref(false);

const configIndex = ref(0);
const configTabsList = ref<IModelOptionConfig[]>([]);
const dateFormat = 'YYYY-MM-DD';
const dateRange = ref<[Dayjs, Dayjs]>([dayjs(), dayjs()]);

// 渲染折线图表
const { render: renderLineChart } = useLineChart();
const generateChart = async (item: any) => {
  const currentContainer = document.querySelector('#chart');
  if (currentContainer) {
    currentContainer.innerHTML = '';
  }
  const source = item.flat();
  const chart = await renderLineChart({
    container: 'chart',
    source,
    height: 552,
  });
  item.chart = chart;
};

const handleConfig = (index: number) => {
  configIndex.value = index;
};

/**
 *
 * @param breadCrumbList 获取型号、内存、成新度请求参数
 */
const generateParams = (breadCrumbList: IBreadCrumb[]): IRequestModelParams => {
  const [category_id, brand_id, model_id] = breadCrumbList[0].value;
  const { value: memory } = breadCrumbList[1];
  const { value: new_ratio } = breadCrumbList[2];
  return {
    category_id,
    brand_id,
    model_id,
    memory,
    new_ratio,
  };
};

const processQueryChart = async (params: IRequestModelParams) => {
  const { data } = await queryChartDetail(params);
  if (!data.platform_list?.length) {
    message.error('该区间暂无数据');
    return;
  }
  const platformDataArray = data.platform_list.flatMap(option => option.platform_data);
  generateChart(platformDataArray);
  dateRange.value = [dayjs(data.start_date, dateFormat), dayjs(data.end_date, dateFormat)];
};

// 图表按日期筛选
const onRangeChange = (timeArr: dayjs.Dayjs[]) => {
  const [start_date, end_date] = timeArr.map(_ => _.format('YYYY-MM-DD'));
  const params = generateParams(props.breadCrumbList);
  params.start_at = start_date;
  params.end_at = end_date;

  processQueryChart(params);
};

//获取配置详情
const getConfig = async () => {
  const params = generateParams(props.breadCrumbList);
  const res = await getEvaluationConfig(params);
  configTabsList.value = res.data;
};

const handleAll = () => {
  emits('update:breadCrumbList', []);
};

const handleBread = (index: number) => {
  if (index === 2) return;
  const newBreadList = props.breadCrumbList.slice(0, index + 1);
  emits('update:breadCrumbList', newBreadList);
};

//获取图表
const fetchCharts = () => {
  const params = generateParams(props.breadCrumbList);
  processQueryChart(params);
};
const configKey = ref(1); //组件进行重载

const reset = async () => {
  fetchCharts();
  await getConfig();
  ++configKey.value;
};

//删除
const handleDelete = async () => {
  const id = configTabsList.value[configIndex.value].market_id;
  await deleteTask(id);
  message.success('删除成功');
  if (configTabsList.value.length > 1) {
    //删除后索引重置
    configIndex.value = 0;
    reset();
  } else {
    //全部删除后跳回列表
    handleAll();
  }
};

const currentId = ref('');
//编辑
const handleEdit = () => {
  currentId.value = configTabsList.value[configIndex.value].market_id;
  editVisible.value = true;
};

onMounted(() => {
  fetchCharts();
  getConfig();
});
</script>
<style scoped lang="less">
.model {
  height: 100vh;
  background: #f0f2f5;
  .model-detail {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 12px 16px;
    background-color: #fff;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .top-left {
        display: flex;
        gap: 12px;
        align-items: center;
        .title {
          position: relative;
          padding-left: 12px;
          color: rgba(6, 21, 51, 0.85);
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
          .title-label:hover:not(:last-child) {
            color: #00c8be;
            cursor: pointer;
          }

          &::before {
            position: absolute;
            top: 50%;
            left: 0;
            width: 4px;
            height: 16px;
            background: #00c8be;
            border-radius: 2px;
            transform: translateY(-50%);
            content: '';
          }
        }
      }
    }
  }
  .model-config {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 12px 16px;
    background-color: #fff;
    .config-top {
      display: flex;
      flex-wrap: wrap;
      gap: 2px;
      align-items: center;
      .config-title {
        padding: 5px 16px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        background: rgba(6, 21, 51, 0.02);
        border: 1px solid rgba(6, 21, 51, 0.15);
        border-radius: 2px 2px 0 0;
        &:hover {
          cursor: pointer;
        }
        &__active {
          color: #00c8be;
          background: #fff;
          border: 1px solid rgba(6, 21, 51, 0.15);
          border-bottom: none;
          border-radius: 2px 2px 0 0;
        }
      }
      .config-icon {
        padding-left: 6px;
        &:hover {
          color: #00c8be;
          cursor: pointer;
        }
      }
    }

    .config-container {
      flex: 1;
      margin-top: 8px;
    }
  }
}
</style>
