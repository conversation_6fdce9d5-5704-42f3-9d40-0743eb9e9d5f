.container {
  min-height: 100%;
  overflow: hidden;
  background-color: #fff;

  .search-bar {
    margin: 0 24px;
    padding-bottom: 24px;
  }

  .main {
    padding: 0 24px;
    .list-table {
      :deep(.ant-table-column-title) {
        font-weight: bold;
      }
      :deep(.ant-table-thead .ant-table-cell) {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 700;
        font-size: 14px;
      }
      :deep(.ant-pagination) {
        position: relative;
      }
      :deep(.ant-pagination-total-text) {
        position: absolute;
        left: 0;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}

.p-b2 {
  padding-bottom: 2px;
}

@media screen and (min-width: 816px) {
  .container .search-form {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}
@media screen and (min-width: 1080px) {
  .container .search-form {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}
@media screen and (min-width: 1440px) {
  .container .search-form {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

.link {
  color: var(--ant-primary-color);
  cursor: pointer;
}

.link-btn {
  color: #3777ff;
  cursor: pointer;
}

// 搜索框删格布局
@media screen and (min-width: 816px) {
  .search-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media screen and (min-width: 1080px) {
  .search-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media screen and (min-width: 1440px) {
  .search-grid {
    grid-template-columns: repeat(5, 1fr) !important;
  }
}

// 给a-from这个class就ok
.search-grid {
  display: grid;
  flex: 1;
  grid-gap: 24px;
  grid-template-columns: repeat(1, 1fr); // 修改这里
  margin-bottom: 12px;
  :deep(.ant-form-item) {
    margin: 0;
  }
}
