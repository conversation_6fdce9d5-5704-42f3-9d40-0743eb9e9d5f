<!-- 投保-弹框 -->
<template>
  <a-modal
    v-model:visible="bindVisible"
    class="be-insured-modal"
    :mask-closable="false"
    title="信息"
    :width="650"
  >
    <a-spin :spinning="loading">
      <div
        class="content"
        v-html="content"
      />
      <a-empty
        v-show="!content"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </a-spin>
    <template #footer>
      <template
        v-if="[EOrderInsure.InsuredSucceed, EOrderInsure.InsureStatusCancelFail].includes(extraData?.insure_status)"
      >
        <a-button @click="close">
          取消
        </a-button>
        <a-button
          :loading="loading"
          type="primary"
          @click="submit"
        >
          申请退保
        </a-button>
      </template>
      <template v-else>
        <a-button
          type="primary"
          @click="close"
        >
          我知道了
        </a-button>
      </template>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { createVNode, ref, watch } from 'vue';
import { Empty, message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import useCancelInsurance from '@/components/order-business/composables/use-cancel-insurance';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import { EOrderInsure } from '@/typing';

import { getBeInsured } from './services';

interface IExtraData {
  /** 投保状态 */
  insure_status?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const content = ref('');
const loading = ref(false);
const bindVisible = useVModel(props, 'visible', emit);
const { cancelInsuranceApi } = useCancelInsurance();

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      showModal();
    }
  },
  { immediate: true },
);

function showModal() {
  content.value = '';
  if (props.extraData.insure_status === EOrderInsure.ToBe) {
    content.value = '该订单暂未投保，请投保后再点击查看';
    return;
  }
  loading.value = true;
  getBeInsured({
    order_id: props.orderId,
  })
    .then(res => {
      content.value = res.data.html;
      if (props.extraData.insure_status === EOrderInsure.InsuredSucceed) {
        content.value = content.value.replace(/>申请退保</, '><');
      }
    })
    .catch(() => {
      bindVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

function confirm() {
  return new Promise(resolve => {
    Modal.confirm({
      title: '温馨提示',
      content: '确定申请退保吗？',
      icon: createVNode(ExclamationCircleOutlined),
      okType: 'danger',
      onOk() {
        resolve(null);
      },
    });
  });
}

function submit() {
  confirm().then(() => {
    loading.value = true;
    cancelInsuranceApi({
      order_id: props.orderId,
    })
      .then(res => {
        res.message && message.success(res.message);
        close();
        emit('refresh', props.orderId, ['data']);
      })
      .catch(err => {
        if (err.status === 20003) emit('refresh', props.orderId, ['data']);
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

function close() {
  bindVisible.value = false;
}
</script>

<style scoped lang="less">
.content {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
