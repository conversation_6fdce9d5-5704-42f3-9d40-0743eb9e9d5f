/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-03-31 10:24:46
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-03-31 11:28:00
 * @FilePath: 统计栏
 * @Description:
 */
import { reactive, computed } from 'vue';
import { accMul } from '@/utils/number';

type DataType = {
  already_rented_order?: number;
  all_overdue_order?: number;
  estimate_loss_amount?: number;
  rental_arrears?: number;
  rental_money?: number;
  estimate_loss_amount_ratio?: number;
  all_overdue_order_ratio?: number;
  rental_arrears_ratio?: number;
  rental_has_pay?: number;
};

type ItemType = {
  description: string;
  title: string;
  unit?: string;
  value?: number;
  remain?: number;
};

type StatisticsType = {
  data: DataType;
  list: ItemType[];
  loading: boolean;
};

export default function (): StatisticsType {
  const list = computed<ItemType[]>(() => {
    return [
      {
        title: '已租订单',
        description: '已产生租赁关系的订单，包含待归还、归还中、已归还、交易完成这几种状态的订单',
        value: statistics.data.already_rented_order,
        remain: 0,
      },
      {
        title: '逾期订单数',
        description:
          '当逾期类型为全部时，包含所有存在逾期行为的订单；当逾期类型为账单逾期时，包含存在未结清租金账单的订单；当逾期类型为归还逾期时，包含租金已结清但设备未归还的订单',
        value: statistics.data.all_overdue_order,
        remain: 0,
      },
      // {
      //   title: '逾期率',
      //   description: '逾期订单在总订单中的占比',
      //   value: statistics.data.all_overdue_order_ratio && accMul(statistics.data.all_overdue_order_ratio, 100),
      //   unit: '%',
      // },
      {
        title: '应收总租金',
        description: '已租订单所有租金之和，理想状态下的最大收入租金',
        value: statistics.data.rental_money,
        unit: '元',
      },
      {
        title: '欠租金额',
        description: '订单逾期时未结清的租金账单金额',
        value: statistics.data.rental_arrears,
        unit: '元',
      },
      {
        title: '总实收租金',
        description: '已租订单中用户已付的租金之和',
        value: statistics.data.rental_has_pay,
        unit: '元',
      },
      {
        title: '租金逾期率',
        description: '欠租金额在总租金中的占比',
        value: statistics.data.rental_arrears_ratio && accMul(statistics.data.rental_arrears_ratio, 100),
        unit: '%',
      },
      {
        title: '预估资损金额',
        description: '预估资损金额为订单逾期时资产损失的最大值；预估资损金额=商品押金-已付租金-已冻结押金',
        value: statistics.data.estimate_loss_amount,
        unit: '元',
      },
      {
        title: '预估资损率',
        description: '预估资损金额在设备总价值中的占比',
        value: statistics.data.estimate_loss_amount_ratio && accMul(statistics.data.estimate_loss_amount_ratio, 100),
        unit: '%',
      },
    ];
  });

  const statistics: StatisticsType = reactive({
    data: {},
    list,
    loading: false,
  });

  return statistics;
}
