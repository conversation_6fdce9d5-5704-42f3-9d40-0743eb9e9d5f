<template>
  <div class="total-price recycle-customer-table-flex-col">
    <div class="price">
      商品总价：￥{{ priceData.goods_price }}
    </div>
    <div class="count">
      数量：{{ priceData.goods_num }}
    </div>
    <div class="other">
      <a-tooltip placement="rightBottom">
        <template #title>
          <div class="deliver-item">
            <span class="deliver-label">运费</span>
            <span class="deliver-value">￥{{ priceData.logistics_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">检测费</span>
            <span class="deliver-value">￥{{ priceData.quality_check_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">锁机费</span>
            <span class="deliver-value">￥{{ priceData.lock_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">配件费</span>
            <span class="deliver-value">￥{{ priceData.accessory_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">发货服务费</span>
            <span class="deliver-value">￥{{ priceData.delivery_service_price }}</span>
          </div>
        </template>
        其它：￥{{ priceData.other_price }}
      </a-tooltip>
    </div>
    合计：￥{{ priceData.order_price }}
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

import { IPriceData } from '../../data';

defineProps({
  priceData: {
    type: Object as PropType<IPriceData>,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.total-price {
  min-width: 152px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Regular, PingFang SC;
  .price {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
  }
  .count,
  .other {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
  }
  .total {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
  }
}
.deliver-item {
  text-align: center;
  .deliver-label {
    &::after {
      content: '：';
    }
  }
}
</style>
