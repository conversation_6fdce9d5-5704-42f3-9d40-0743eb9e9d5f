import { InternalUploadFile } from 'ant-design-vue/lib/upload/interface';

export type Quality = 'o' | 'm' | 's';

export type ParseFile = {
  url: string;
  status: string;
  uid: number;
  name: string;
  path: string;
};

export type StartUploadOptions = {
  onProgress?: (progress: number) => void;
  onSuccess?: (urlList: string[], fileList: (InternalUploadFile | ParseFile)[], pathList: string[]) => void;
};
