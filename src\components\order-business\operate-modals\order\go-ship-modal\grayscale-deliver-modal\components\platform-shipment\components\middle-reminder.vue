<template>
  <div class="middle-reminder">
    <img
      v-if="hasDeviceAuth"
      alt="全新仅激活价格更有优势哦"
      src="https://img1.rrzuji.cn/uploads/scheme/2412/07/m/vjmUldYtsr08NBfAwm2D.png"
    >
    <!--  缺货状态才做显示   -->
    <div
      v-if="stockStatus === 1"
      style="margin-left: auto"
    >
      <span
        class="middle-reminder-tag cursor"
        @click="handleReserve"
      >
        <clock-circle-outlined />
        <template v-if="!reserveStatus"> 预定下发 </template>
        <template v-else>
          <a-popconfirm
            :overlay-style="{ width: '260px' }"
            title="您确定要取消预订下发吗？设备到库不会自动发货。如需自动发货，请重新预订下发。"
            @confirm="onCancellation"
          >
            预定下发中
          </a-popconfirm>
        </template>
      </span>
      <a-tooltip
        :overlay-style="{
          backgroundColor: 'rgba(6,21,51,0.75);',
        }"
      >
        <template #title>
          <div>
            选择<span class="gum-yellow">预定下发</span>后，在<span class="gum-yellow">48h</span>内，
            如果有货将自动发货；超时可查询代发重新预订。
          </div>
        </template>
        <question-circle-outlined class="cursor" />
      </a-tooltip>
    </div>
  </div>

  <a-modal
    v-model:visible="modalState.visible"
    :confirm-loading="modalState.loading"
    destroy-on-close
    title="预定下发"
    width="470px"
    @cancel="onClose"
    @ok="onConfirm"
  >
    <div class="explain-card">
      请注意：您选择了“预订下发”选项，在 <span class="red">48h</span> 内，如果有货将自动发货；超时可查询代发重新预订。
    </div>
    <!-- 新网商家才有的票据选择 -->
    <template v-if="isNewOnlineServer">
      <a-radio-group
        v-model:value="invoice"
        style="margin-top: 16px"
      >
        <a-radio-button :value="2">
          有票
        </a-radio-button>
        <a-radio-button
          :disabled="isNew"
          :value="3"
        >
          无票
        </a-radio-button>
      </a-radio-group>
    </template>
    <div class="title">
      请核对预订下发的货品信息：
    </div>
    <div class="sku-info">
      {{ skuInfo }} {{ state.is_checked ? '|| 送检' : '' }} ||
      {{ state.is_support ? '租赁服务设备' : '非租赁服务设备' }}
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';
import { message } from 'ant-design-vue';
import { ClockCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';

import type { IModalState } from '../../../../data';
import { reserveSend } from '../../../../services';

const props = defineProps({
  hasDeviceAuth: {
    type: Boolean,
    default: false,
  },
  skuInfo: {
    type: String,
    default: '',
  },
  isNewOnlineServer: {
    type: Boolean,
    default: false,
  },
  isNew: {
    type: Boolean,
    default: false,
  },
  skuId: {
    type: String,
    default: '',
  },
  state: {
    type: Object as PropType<IModalState>,
    default: () => ({}),
  },
  reserveStatus: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  stockStatus: {
    type: Number,
    default: 2,
  },
});

const emit = defineEmits(['refresh']);

const modalState = ref({
  visible: false,
  loading: false,
});

const invoice = ref(2);

const onClose = () => {
  modalState.value.visible = false;
};

const haneleParams = () => {
  const {
    orderId,
    skuId,
    state: { is_support, activate_only, is_checked, sku_id },
    isNewOnlineServer,
  } = props;
  const params = {
    order_id: orderId,
    sku_id: skuId || sku_id,
    is_support,
    activate_only: activate_only ? 1 : 0,
    is_checked: is_checked ? 1 : 0,
  };
  if (isNewOnlineServer) {
    params.invoice = invoice.value;
  }
  return params;
};

const onConfirm = async () => {
  await reserveSend({
    is_reserve: 1,
    ...haneleParams(),
  });
  emit('refresh');
  onClose();
  message.success('预定下发成功');
};

const handleReserve = () => {
  if (props.reserveStatus) return;
  modalState.value.visible = true;
};

// 取消预定
const onCancellation = async () => {
  await reserveSend({
    is_reserve: 0,
    ...haneleParams(),
  });
  emit('refresh');
  onClose();
  message.success('您已取消（预订下发），设备到库不会自动发货。如需自动发货，请重新预订下发。');
};
</script>

<style lang="less" scoped>
.middle-reminder {
  display: flex;
  align-items: center;
  justify-content: space-between;

  img {
    width: 402px;
  }
  &-tag {
    margin-right: 14px;
    padding: 6px;
    color: #fff;
    font-weight: 400;
    background-color: #00c8be;
    border-radius: 4px;
  }

  .cursor {
    cursor: pointer;
  }
}

.explain-card {
  padding: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  background-color: rgba(6, 21, 51, 0.04);
  border-radius: 4px;
}

.title {
  margin: 16px 0;
  color: rgba(6, 21, 51, 0.85);
}

.sku-info {
  color: #00c8be;
}

.red {
  color: #ff4d4f;
}

.gum-yellow {
  color: #ca9326;
}
</style>
