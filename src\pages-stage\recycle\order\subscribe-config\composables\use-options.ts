import { ref } from 'vue';
import { getFormOptions } from '../service';

export default function useOptions() {
  const selectManage = ref({
    activity_select: [],
    new: [],
  });

  const init = async () => {
    const res = await getFormOptions();
    const data = res.data || {};
    const keys = Object.keys(selectManage.value);
    keys.forEach(keyItem => {
      if (data.hasOwnProperty(keyItem)) {
        selectManage.value[keyItem] = data[keyItem];
      }
    });
    selectManage.value.new.forEach((item: { name?: string; id?: number; label: string; value: number }) => {
      item.name = item.label;
      item.id = item.value;
    });
  };

  init();

  return {
    selectManage,
  };
}
