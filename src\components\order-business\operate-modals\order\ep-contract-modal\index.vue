<template>
  <a-modal
    v-model:visible="bindVisible"
    class="enterprise-modal height-limit"
    title="合同管理"
    :width="1000"
    @cancel="activeKey = '1'"
  >
    <a-tabs
      v-model:activeKey="activeKey"
      type="card"
      @change="loadData"
    >
      <a-tab-pane
        key="1"
        tab="已签署"
      />
      <a-tab-pane
        key="2"
        tab="未签署"
      />
    </a-tabs>
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <div
        v-if="data.length > 0"
        class="content"
      >
        <template
          v-for="item in data"
          :key="item.date"
        >
          <a-space
            v-if="item.date"
            class="title"
            :size="8"
          >
            <div class="tip" />
            {{ item.date }}
          </a-space>
          <div
            v-for="j in item.list"
            :key="j.id"
            class="item-box"
          >
            <div class="header flex-wrap flex-x-justify flex-y-center">
              <div class="name">
                {{ j.download_name || '未知' }}
              </div>
              <div class="btns">
                <a-space>
                  <a-button
                    type="link"
                    @click="showContractView(j.id, j.download_link, j.contract_id, j.sign_status)"
                  >
                    查看
                  </a-button>
                  <a-button
                    v-if="item.date"
                    type="link"
                    @click="download(j.download_link)"
                  >
                    下载
                  </a-button>
                  <a-button
                    v-else
                    type="link"
                    @click="() => openRemarkModal(j.contract_id)"
                  >
                    备注
                  </a-button>
                </a-space>
              </div>
            </div>
            <div class="info flex-wrap flex-x-justify flex-y-center">
              <div
                v-if="item.date"
                class="time"
              >
                签署时间: {{ j.sign_at }}
              </div>
              <div v-else>
                <a-badge
                  :status="STATUS_CONFIG[j.sign_status]"
                  :text="j.sign_status_name"
                />
              </div>
            </div>
          </div>
        </template>
      </div>
      <a-empty v-else />
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        关闭
      </a-button>
    </template>
    <!--  企业备注  -->
    <RemarkModal
      v-model:visible="remarkVisible"
      :contract-id="remarkContractId"
    />
    <!--  预览合同PDF  -->
    <PreviewPdf
      v-model:visible="previewPdfVisible"
      :link="previewLink"
    />
    <!-- 预览合同模板 -->
    <ContractModal>
      <template
        v-if="activeKey === '2'"
        #btn
      >
        <a-button
          v-if="[3, 4].includes(signStatus)"
          disabled
          type="primary"
        >
          {{ signStatusConfig[signStatus] }}
        </a-button>
        <a-button
          v-else
          :disabled="viewTime !== 0"
          type="primary"
          @click="handleSign"
        >
          {{ viewTime !== 0 ? `签署(${viewTime}s)` : '签署' }}
        </a-button>
      </template>
    </ContractModal>
  </a-modal>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';

import { useVModel } from '@/hook';
import ContractModal from '@/pages-stage/order/contract/content/components/contract-modal/index.vue';
import { useFormData } from '@/pages-stage/order/contract/content/composations/form-data';

import PreviewPdf from './components/preview-pdf/index.vue';
import RemarkModal from './components/remark-modal/index.vue';
import { signStatusConfig, STATUS_CONFIG } from './config';
import { IContractList } from './data';
import { getCheckAuthRemind, getContractDetail, getThreeContractList, postAutoSign } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emit);

const loading = ref(false);
// ==================================  合同信息  ======================================
const data = ref<IContractList[]>([]);
const activeKey = ref('1');

/** 获取合同列表 */
function loadData() {
  loading.value = true;
  getThreeContractList({
    order_id: props.orderId,
    type: activeKey.value,
  })
    .then(res => {
      data.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

watch(
  bindVisible,
  value => {
    if (value) {
      activeKey.value = '1';
      loadData();
    }
  },
  { immediate: true },
);

// ==================================  企业备注  ======================================
const remarkVisible = ref(false);
const remarkContractId = ref('');

/** 查看企业备注 */
function openRemarkModal(contract_id: string) {
  remarkContractId.value = contract_id;
  remarkVisible.value = true;
}

// ==================================  查看合同  ======================================
const previewPdfVisible = ref(false);

const { formData, showContract, downloadLink, contractId } = useFormData();
/** 签署状态 */
const signStatus = ref(1);
/** 下载链接 */
const previewLink = ref('');
const viewTime = ref(15);
let timer: any;

onBeforeUnmount(() => {
  if (timer) clearInterval(timer);
});

/** 预览合同 */
function showContractView(id: string, link: string, cId: string, sign_status: string) {
  if (activeKey.value === '1') {
    previewLink.value = link;
    previewPdfVisible.value = true;
    return;
  }
  contractId.value = cId;
  // 打开预览合同弹窗
  showContract.value = true;
  signStatus.value = Number(sign_status);
  viewTime.value = 15;
  if (timer) clearInterval(timer);
  timer = setInterval(() => {
    viewTime.value--;
    if (viewTime.value === 0) {
      clearInterval(timer);
    }
  }, 1000);
  handleContract(id);
}

/** 获取合同详情 */
async function handleContract(id: string) {
  const { data } = await getContractDetail({ id });
  downloadLink.value = data.download_link;
  for (let key in data.tpl_vars) {
    if (['rent_date_start', 'rent_date_end'].includes(key)) {
      formData['rent_date'] = [data.tpl_vars.rent_date_start, data.tpl_vars.rent_date_end];
      continue;
    }
    if ('period_rent' === key) {
      formData.period_rent = Number(data.tpl_vars[key]).toFixed(2);
      continue;
    }
    if ('leasee_account' === key) {
      formData.leasee_account = data.tpl_vars[key]?.split(',');
      continue;
    }
    if ('period' === key) {
      formData.period = data.tpl_vars[key]?.split(',');
      continue;
    }
    formData[key] = data.tpl_vars[key];
  }
}

/** 签署合同 */
async function handleSign() {
  const { data } = await getCheckAuthRemind({
    contract_id: contractId.value,
  });
  if (data.is_remind === 1) {
    Modal.info({
      title: '授权提醒',
      content:
        '微企租检测到您的企业暂未完成数字证书的使用授权，授权短信已发送至企业法人手机号，请完成授权后再进行合同签署。',
      onOk() {
        console.log('ok');
      },
    });
  } else {
    await postAutoSign({
      contract_id: contractId.value,
    });
    message.success('操作成功！');
    showContract.value = false;
    loadData(activeKey.value);
  }
}

/** 下载合同 */
function download(url: string) {
  window.open(url);
}
</script>

<style scoped lang="less">
.title {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 14px;
  line-height: 14px;

  &:not(&:first-child) {
    margin-top: 12px;
  }

  .tip {
    width: 4px;
    height: 14px;
    background: #00c8be;
    border-radius: 2px;
  }
}

.content {
  max-height: 500px;
  overflow-y: scroll;
}

.item-box {
  padding: 16px;
  background: #f9f9fb;

  &:not(&:first-child) {
    margin-top: 12px;
  }

  .name {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
  }

  .info {
    margin-top: 8px;
    color: rgba(6, 21, 51, 0.25);
    font-weight: 400;
    font-size: 14px;
  }
}
</style>
