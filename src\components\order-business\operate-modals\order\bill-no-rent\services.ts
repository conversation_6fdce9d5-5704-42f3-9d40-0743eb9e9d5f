import { GET, POST } from '@/services/api';

let pre = '';
let hasLoad = false;

export function formatPre(route) {
  if (hasLoad) {
    return;
  }
  hasLoad = true;
  if (route.query?.role === 'super') {
    pre = '/super';
  }
}

// 账单列表筛选数据
export function getFilterData() {
  return GET(pre + '/business-bill/list-filter');
}

// 创建长的那
export function createBill(data) {
  return POST(pre + '/business-bill/add', data);
}
