<template>
  <ExpandBox
    placement="top"
    transition-time="0.09s"
    :value="isExpandMore"
  >
    <template #content>
      <div class="expand-cell">
        <!-- 风控结果 -->
        <a-space v-if="orderData.risk_info?.risk?.risk_result">
          <div
            v-if="orderData.risk_info.risk.risk_qualitative"
            :style="
              orderData.risk_info.risk.risk_qualitative === '通过'
                ? { color: '#52C41A', backgroundColor: '#F0FFE1' }
                : { color: '#FF4D4F', backgroundColor: '#FFF1F0' }
            "
          >
            {{ orderData.risk_info.risk.risk_qualitative }}
          </div>
          <a-divider
            v-if="orderData.risk_info.risk.risk_qualitative"
            style="height: 22px; margin-inline: 0"
            type="vertical"
          />
          <div>风控结果：{{ orderData.risk_info.risk.risk_result || '--' }}</div>
        </a-space>

        <!-- 风控报告按钮组 -->
        <OperateButtonGroup
          v-if="isShowRiskBtn"
          :button-attrs="{ type: 'primary' }"
          :button-data="orderData.button_list || []"
          :button-group="riskButtonGroup"
          :modal-attrs="{ orderId }"
          style="width: 100%"
        />

        <!-- 决策输出 -->
        <div
          v-if="orderData.risk_info?.auto_engine_result?.[0]?.risk_info"
          class="risk-info"
          style="color: #ff4d4f"
        >
          <!--  仅显示一条  -->
          <div
            v-for="(item, index) in orderData.risk_info.auto_engine_result.slice(0, 1)"
            :key="index"
          >
            <span>决策输出：{{ item.risk_info }}{{ orderData.risk_info.risk.advice }}</span>
            <span>{{ item.decision_action_result }}{{ item.create_at }}</span>
          </div>
        </div>

        <!-- 风控信息 -->
        <div
          v-if="orderData.risk_info?.risk?.risk_content && isShowRiskContent"
          class="risk-info"
        >
          <div
            style="cursor: pointer"
            @click="handleClickRisk(orderData.risk_info.risk.need_risk_warning_click)"
          >
            <WarningOutlined
              v-if="orderData.risk_info.risk.need_risk_warning_click"
              class="risk-warning-icon"
            />
            <span class="risk-info-title">风控信息：</span>
            <span class="risk-info-content">
              {{ orderData.risk_info.risk.risk_content }}
              {{ orderData.risk_info.risk.risk_info }}
              {{ orderData.risk_info.risk.decision_action_result }}</span>
          </div>
        </div>

        <!-- 风控复审建议 -->
        <div
          v-if="isSuper && orderData.risk_info?.syn_review_info?.advice_title"
          class="risk-info"
          style="color: red; font-weight: bold"
        >
          <span
            class="risk-info-title"
            style="color: red"
          >风控复审建议：</span>
          【复审建议】{{ orderData.risk_info?.syn_review_info?.advice }}。
        </div>

        <div
          v-else-if="!isSuper && orderData.risk_info?.syn_review_info?.length"
          class="risk-info"
          style="color: red; font-weight: bold"
        >
          <div>
            <span
              class="risk-info-title"
              style="color: red"
            >风控复审建议：</span>
            【复审建议】{{ orderData.risk_info?.syn_review_info[0].advice }}
            <span v-if="orderData.risk_info?.syn_review_info.length > 1">
              （复审结果更新{{ orderData.risk_info?.syn_review_info[0]?.created_at_format }}），请以最新结果为准
            </span>
          </div>
          <div
            v-if="orderData.risk_info?.syn_review_info.length > 1"
            class="total-num"
            style="text-decoration: underline; cursor: pointer"
            @click="onOpenReviewModal(orderData.risk_info?.syn_review_info)"
          >
            {{ orderData.risk_info?.syn_review_info.length }}次记录
          </div>
        </div>

        <!-- 用户留言 -->
        <a-row
          v-if="orderData.base_info?.remark || orderData.base_info?.invoice"
          class="leave-message"
        >
          <a-col
            v-if="orderData.base_info?.remark"
            :span="12"
          >
            <span class="label">用户留言：</span>
            <span class="value">
              {{ orderData.base_info?.remark }}
            </span>
          </a-col>
          <a-col
            v-if="orderData.base_info?.invoice"
            :span="12"
          >
            <span class="label">发票抬头：</span>
            <span class="value">
              {{ orderData.base_info.invoice }}
            </span>
          </a-col>
        </a-row>

        <!-- 分销商 -->
        <div v-if="orderData.distributor?.origin || orderData.distributor?.proprietary_text">
          <span v-if="orderData.distributor.origin">{{ orderData.distributor.origin }}：</span>
          <span
            v-if="orderData.distributor.name"
            style="margin-right: 10px"
          >{{ orderData.distributor.name }}</span>
          <!-- 非首次下单自营/自营 -->
          <a-tag
            v-if="orderData.distributor.proprietary_text"
            color="#d9534f"
          >
            {{ orderData.distributor.proprietary_text }}
          </a-tag>
        </div>

        <!-- 备注 -->
        <div>
          <crm-remark
            :id="`${orderId}_order.all`"
            :block-flex="false"
            :format-logs="formatLog"
            :item="orderData.async.all_remark"
            layout="horizontal"
            log-link="/crm/log"
            push-link="/crm/push-server"
            size="block"
            title="crm备注，v1.0"
            @before-open="openRemark"
          >
            <template #emptyText>
              <span>无备注</span>
            </template>
            <!-- 判断是否代发订单 -->
            <template
              v-if="
                orderData.base_info?.is_order_delivery_mark &&
                  orderData.base_info?.order_status === EOrderStatus.WaitSend
              "
              #addModalText
            >
              <div style="padding-top: 10px; color: red">
                请不要在此备注仓库发货租赁服务设备或非租赁服务设备，如需要修改请拦截发货重新下发。
              </div>
            </template>
          </crm-remark>
        </div>
      </div>
    </template>
    <template #custom="{ isOutRange, hasExtendMore, toggleExtendMore }">
      <div
        v-if="isOutRange"
        class="extend-btn"
        @click="toggleExtendMore"
      >
        {{ hasExtendMore ? '收起更多信息' : '展开更多信息' }}

        <DoubleRightOutlined :class="[hasExtendMore ? 'icon-collapse' : 'icon-expend']" />
      </div>
    </template>
  </ExpandBox>
</template>

<script setup lang="ts">
import { computed, h, ref, watch } from 'vue';
import { PropType, toRef } from 'vue';
import { useRoute } from 'vue-router';
import { Modal } from 'ant-design-vue';
import { DoubleRightOutlined, WarningOutlined } from '@ant-design/icons-vue';

import ExpandBox from '@/components/expand-box';
import { basicModal } from '@/components/order-business/basic-modals';
import { taggingPointByOrder } from '@/components/order-business/composables/use-tagging-point';
import { OperateButtonGroup } from '@/components/order-business/feature-application';
import { operateModal } from '@/components/order-business/operate-modals';
import type { IButtonGroupItem, IOrderItem } from '@/components/order-business/typing/index.d';
import { useUnityModal } from '@/components/unity-modal';
import { EOrderStatus } from '@/typing';

const props = defineProps({
  isExpandMore: {
    type: Boolean,
    default: false,
  },
  orderData: {
    type: Object as PropType<IOrderItem>,
    required: true,
  },
});

const route = useRoute();
const isSuper = computed(() => route.query.role === 'super');

const orderId = toRef(() => props.orderData?.base_info?.order_id || '');

// ============================= 风控操作按钮组 ==========================
const { onToggleComponent } = useUnityModal();

/** 风控按钮组 */
const riskButtonGroup = computed<IButtonGroupItem[]>(() => [
  {
    buttonCom: operateModal.riskCreating,
    buttonAttrs: { style: { backgroundColor: '#f09413', border: '#f09413' } },
  },
  {
    buttonCom: operateModal.riskReport,
    label: '风控报告已生成',
    buttonAttrs: {
      style: { backgroundColor: '#7da4f3', border: '#7da4f3' },
      onClick: () => {
        onToggleComponent(operateModal.riskReport, { orderId: orderId.value });
        taggingPointByOrder(orderId.value, 1033);
      },
    },
  },
  {
    buttonCom: operateModal.riskReportAdvance,
    label: '风控报告进阶版',
    buttonAttrs: {
      onClick: () => {
        onToggleComponent(operateModal.riskReportAdvance, { orderId: orderId.value, version: 0 });
        taggingPointByOrder(orderId.value, 1034);
      },
    },
  },
  {
    buttonCom: operateModal.riskReportAdvanceNew,
    label: '风控报告进阶版',
    buttonAttrs: {
      onClick: () => {
        onToggleComponent(operateModal.riskReportAdvanceNew, { orderId: orderId.value, version: 1 });
        taggingPointByOrder(orderId.value, 1034);
      },
    },
  },
  {
    buttonCom: operateModal.riskApproveInfo,
    modalAttrs: {
      type: '2',
    },
  },
]);

/** 判断按钮组里是否有按钮显示 */
const isShowRiskBtn = computed(() => {
  for (let i = 0; i < riskButtonGroup.value.length; i++) {
    if (props.orderData?.button_list?.find(item => item.button === riskButtonGroup.value[i].buttonCom.key)) {
      return true;
    }
  }
  return false;
});

function openRemark(obj: { stop: boolean }) {
  obj.stop = true;
  onToggleComponent(basicModal.serverRemark, {
    orderId: props.orderData.base_info.order_id,
    remark: props.orderData?.order_mark?.remark,
    displayValue: false,
    isCrmRemark: true,
  });
}

// ============================= 风控信息显示控制 ==========================
const isShowRiskContent = ref(true);

watch(
  () => props.orderData,
  () => (isShowRiskContent.value = true),
);

/** 点击"风控信息"图标时隐藏风控信息 */
function handleClickRisk(need_risk_warning_click) {
  if (need_risk_warning_click) {
    Modal.info({
      title: '注意',
      content: h('div', {}, [h('div', '用户逾期风险较高，建议谨慎发货'), h('div', '若要婉拒客户时，注意避免造成客诉')]),
    });
  }
  isShowRiskContent.value = false;
}

function formatLog(list) {
  return list.map(item => {
    item.remark = item.remark.replace(/;/g, '<br/>');
    return item;
  });
}

function onOpenReviewModal(reviewList) {
  const reviewAdviceList = [...reviewList].reverse() || [];
  onToggleComponent(() => import('./components/review-advice-modal.vue'), {
    reviewAdviceList,
  });
}
</script>

<style scoped lang="less">
.expand-cell {
  border-top: 1px solid #f0f1f3;

  > * {
    width: 100%;
    padding: 12px 16px;
  }

  // 为子级的元素添加分割线
  > *:not(:last-child) {
    border-bottom: 1px solid #f0f1f3;
  }
}

.extend-btn {
  padding: 8px 0;
  color: rgba(6, 21, 51, 0.65);
  color: #06153373;
  text-align: center;
  background-color: #fafafa;

  &:hover {
    color: #061533a6;
    cursor: pointer;
  }
}

.icon-expend {
  transform: rotate(90deg);
}

.icon-collapse {
  transform: rotate(-90deg);
}

.risk-info {
  position: relative;
  color: rgb(6, 21, 51);

  .risk-warning-icon {
    margin-right: 8px;
    color: #ff4d4f;
  }

  &-title {
    color: rgba(6, 21, 51, 0.65);
  }

  &-content {
    color: rgba(6, 21, 51);
  }
}

.leave-message {
  color: rgb(6, 21, 51);

  .label {
    color: rgba(6, 21, 51, 0.65);
  }

  .value {
    color: rgba(6, 21, 51, 1);
  }

  .edit-btn {
    margin-left: 12px;
    color: #3777ff;
    cursor: pointer;

    &:hover {
      color: #6198ff;
    }
  }
}
</style>
