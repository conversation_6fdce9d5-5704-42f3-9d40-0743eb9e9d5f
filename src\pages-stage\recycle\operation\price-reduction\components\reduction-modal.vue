<template>
  <a-modal
    v-model:visible="bindVisiable"
    destroy-on-close
    :title="`${type === 1 ? '创建' : '编辑'}价格趋势`"
    width="480px"
    @cancel="emits('close')"
    @ok="emits('confirm', formRef, emits)"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="bindValue"
      >
        <a-form-item
          label="商品ID"
          name="goods_id"
          required
        >
          <a-input-search
            v-model:value="bindValue.goods_id"
            placeholder="请输入"
            @search="searchByGoodsId"
          />
        </a-form-item>
        <a-form-item
          label="商品名称"
          name="model"
        >
          <a-input
            v-model:value="bindValue.goods_name"
            disabled
            placeholder="请输入商品ID"
          />
        </a-form-item>
        <a-form-item
          label="降价周期"
          name="days"
          required
        >
          <a-select
            v-model:value="bindValue.days"
            :options="reduceOptions"
            placeholder="请选择"
            style="width: 141px"
          />
        </a-form-item>
        <a-form-item
          label="降价比例"
          name="ratio"
          required
        >
          <a-input-number
            v-model:value="bindValue.ratio"
            :max="100"
            :min="0"
            placeholder="请输入降价百分比（最多精确到小数点后两位例：0.02）"
            style="width: 141px"
          >
            <template #addonAfter>
              %
            </template>
          </a-input-number>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { useVModel } from '@/hook';
import { reduceOptions } from '../config';
import { PropType, computed, ref } from 'vue';
import { IReduction } from '../data';
import { FormInstance } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  type: {
    type: Number,
    default: 1,
  },
  formState: {
    type: Object as PropType<Partial<IReduction>>,
    default: () => ({}),
  },
  searchByGoodsId: {
    type: Function,
    default: () => undefined,
  },
});
const emits = defineEmits(['update:visible', 'update:formState', 'reset-table', 'confirm', 'close']);
const bindValue = computed({
  get: () => props.formState,
  set: val => {
    emits('update:formState', val);
  },
});
const bindVisiable = useVModel(props, 'visible', emits);
const formRef = ref<FormInstance | null>(null);
</script>

<style lang="less" scoped>
@import '../../../common/base.less';
:deep(.ant-form-item-label) {
  align-self: flex-start;
}
.ant-form-item {
  flex-direction: column;
  width: 100%;
}
:deep(.ant-col) {
  flex: 1;
  min-height: min-content;
}
</style>
