import { PaginationProps, TableColumnType } from 'ant-design-vue';
import { reactive } from 'vue';

type ExamTableType = {
  loading: boolean;
  list: Record<string, any>[];
  pagination: PaginationProps;
  resetPagination: () => void;
  columnsObj: Record<string, TableColumnType[]>;
  reasonObj: Record<string, string>;
  optionMap: {
    risk_qualitative: Record<string, any>;
  };
};

export default function (): ExamTableType {
  const riskColumns = [
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id',
      width: 200,
    },
    {
      title: '风控定性',
      dataIndex: 'risk_qualitative',
      key: 'risk_qualitative',
      width: 144,
    },
    {
      title: '是否可发货订单',
      width: 168,
      dataIndex: 'server_can_delivery_sign',
      key: 'server_can_delivery_sign',
    },
    {
      title: '是否--前已发货订单',
      width: 208,
      dataIndex: 'is_delivery',
      key: 'is_delivery',
    },
    {
      title: '是否常规考核',
      width: 184,
      dataIndex: 'inspection_dimension',
      key: 'inspection_dimension',
    },
    {
      title: '不计入理由',
      width: 240,
      dataIndex: 'reason',
      key: 'reason',
    },
  ];

  const unLabelColumns = [
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id',
      width: 176,
    },
    {
      title: '是否--前已发货订单',
      dataIndex: 'is_delivery',
      key: 'is_delivery',
      width: 184,
    },
    {
      title: '接单时间',
      width: 184,
      dataIndex: 'server_receive_at',
      key: 'server_receive_at',
    },
    {
      title: '标记发货时间',
      width: 184,
      dataIndex: 'sent_sign_created_at',
      key: 'sent_sign_created_at',
    },
    {
      title: '自发订单发货超48H',
      width: 192,
      dataIndex: 'no_mark_deliver_pass_48h',
      key: 'no_mark_deliver_pass_48h',
    },
  ];

  const averColumns = [
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id',
      width: 232,
    },
    {
      title: '风控定性',
      dataIndex: 'risk_qualitative',
      key: 'risk_qualitative',
      width: 192,
    },
    {
      title: '是否--前已发货订单',
      width: 248,
      dataIndex: 'is_delivery',
      key: 'is_delivery',
    },
    {
      title: '发货时间',
      width: 248,
      dataIndex: 'sent_at',
      key: 'sent_at',
    },
    {
      title: '发货前收取期数',
      width: 224,
      dataIndex: 'sent_pay_num',
      key: 'sent_pay_num',
    },
  ];

  const responColums = [
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id',
      width: 232,
    },
    {
      title: '接单时间',
      width: 184,
      dataIndex: 'server_receive_at',
      key: 'server_receive_at',
    },
    {
      title: '最早响应时间',
      width: 184,
      dataIndex: 'min_respone_time',
      key: 'min_respone_time',
    },
  ];

  const resetPagination = () => {
    examTable.list.length = 0;
    examTable.pagination.current = 1;
    examTable.pagination.pageSize = 10;
  };

  const examTable: ExamTableType = reactive({
    loading: false,
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    optionMap: {
      risk_qualitative: {
        0: '未知',
        1: '通过',
        2: '预收',
        3: '风险高',
        4: '关闭',
        5: '跳过',
      },
    },
    columnsObj: {
      0: riskColumns,
      1: unLabelColumns,
      2: averColumns,
      3: responColums,
    },
    reasonObj: {
      1: '计入常规考核',
      2: '已删除订单',
      3: '码商订单',
      4: '风控关闭订单',
      5: '转单订单',
      6: '派单前取消订单',
      7: '订单创建时间距离订单生成时间超过48H',
      8: '预租',
    },
    resetPagination,
  });

  return examTable;
}
