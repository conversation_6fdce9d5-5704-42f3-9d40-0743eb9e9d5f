<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :width="800"
    @ok="handleOk"
  >
    <form
      ref="formRef"
      layout="horizontal"
    >
      <a-form-item
        label="图片地址"
        :label-col="{ span: 4 }"
        name="cover"
        v-bind="validateInfos.cover"
        :wrapper-col="{
          span: 18,
        }"
      >
        <a-input
          v-model:value="formData.cover"
          placeholder="请输入图片地址，需为https开头"
        />
      </a-form-item>
      <a-form-item
        label="跳转方式"
        :label-col="{ span: 4 }"
        name="type"
        :wrapper-col="{
          span: 18,
        }"
      >
        <a-radio-group v-model:value="formData.type">
          <a-radio :value="4">
            指定页面
          </a-radio>
          <a-radio :value="9">
            小程序链接
          </a-radio>
          <a-radio :value="8">
            域外链接url
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="跳转链接"
        :label-col="{ span: 4 }"
        name="link"
        :wrapper-col="{
          span: 18,
        }"
      >
        <a-input
          v-model:value="formData.link"
          placeholder="小程序页面链接或者产品ID"
        />
      </a-form-item>
      <a-form-item
        label="path"
        :label-col="{ span: 4 }"
        name="path"
        :wrapper-col="{
          span: 18,
        }"
      >
        <a-input
          v-model:value="formData.path"
          placeholder="跳转方式选择【小程序链接】时需填写"
        />
      </a-form-item>
      <a-form-item
        label="appId"
        :label-col="{ span: 4 }"
        name="appid"
        :wrapper-col="{
          span: 18,
        }"
      >
        <a-input
          v-model:value="formData.appid"
          placeholder="跳转方式选择【小程序链接】时需填写"
        />
      </a-form-item>
      <a-form-item
        label="额外参数"
        :label-col="{ span: 4 }"
        name="extraData"
        :wrapper-col="{
          span: 18,
        }"
      >
        <a-input
          v-model:value="formData.extraData"
          placeholder="跳转方式选择【小程序链接】时需填写"
        />
      </a-form-item>
    </form>
  </a-modal>
</template>

<script setup lang="ts">
import { useVModel } from '@/hook/use-vmodel';
import { reactive, watch } from 'vue';
import { orderSuccessPopCreate, orderSuccessPopEdit, orderSuccessPopDetail } from '../service';
import { Form, message } from 'ant-design-vue';

const props = defineProps<{
  visible: boolean;
  title: string;
  adType: string;
  id: string | undefined;
}>();
const useForm = Form.useForm;
const emit = defineEmits(['update:visible', 'getList']);

const visible = useVModel(props, 'visible', emit);
const formData = reactive({
  cover: '',
  type: 4,
  link: '',
  path: '',
  appid: '',
  extraData: '',
  adType: props.adType,
  // status: '',
});
const rulesRef = reactive({
  cover: [{ required: true, message: '请输入图片链接地址', trigger: 'blur' }],
});

const { resetFields, validate, validateInfos } = useForm(formData, rulesRef);

const handleOk = () => {
  if (formData.type === 4 && formData.link === '') {
    message.warning('当前您选择的跳转方式为指定页面,请输入跳转链接');
    return;
  }
  if (formData.type === 8 && formData.link === '') {
    message.warning('当前您选择的跳转方式为域外链接url,请输入跳转链接');
    return;
  }
  if (formData.type === 9 && (formData.path === '' || formData.appid === '')) {
    message.warning('当前您选择的跳转方式为小程序链接,请输入path、appid、额外参数');
    return;
  }

  validate().then(async () => {
    if (props.id) {
      await orderSuccessPopEdit({ ...formData, id: props.id });
      emit('getList');
    } else {
      await orderSuccessPopCreate({ ...formData });
      emit('getList');
      resetFields();
    }
    visible.value = false;
  });
};
const getDetail = async () => {
  const res = await orderSuccessPopDetail({ id: props.id, adType: props.adType });
  Object.keys(formData).forEach(key => {
    formData[key] = res.data[key];
  });
};
watch(
  () => props.adType,
  value => {
    if (value) {
      formData.adType = props.adType;
    }
  },
);
watch(
  () => visible.value,
  value => {
    if (!value) {
      resetFields();
    } else {
      if (props.id) {
        getDetail();
      }
    }
  },
);
</script>
