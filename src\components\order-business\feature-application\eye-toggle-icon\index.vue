<template>
  <span
    class="operate-link"
    @click="handleChange"
  >
    <EyeInvisibleOutlined v-if="value" />
    <EyeOutlined v-else />
  </span>
</template>

<script setup lang="ts">
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  value: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['change']);

function handleChange() {
  emit('change', !props.value);
}
</script>

<style scoped lang="less">
.operate-link {
  color: #9197a4;
  cursor: pointer;

  &:hover {
    color: #3777ff;
  }
}
</style>
