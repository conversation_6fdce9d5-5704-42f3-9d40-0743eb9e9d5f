import type { RTableColumnType } from 'rrz-web-design';
export const detailsConfig = [
  {
    label: '资质类型',
    key: 'config_name',
    type: 'text',
  },
  {
    label: '资质名称',
    key: 'customize_name',
    type: 'text',
  },
  {
    label: '资质证书',
    key: 'server_credential_goods_image',
    type: 'img',
  },
  {
    label: '资质有效期',
    type: 'title',
  },
  {
    label: '开始日期',
    key: 'valid_start_at_label',
    type: 'text',
  },
  {
    label: '截止日期',
    key: 'valid_end_at_label',
    type: 'text',
  },
];

export const goodsInfoColumns: RTableColumnType[] = [
  {
    dataIndex: 'spu_id',
    title: '商品ID',
    width: 200,
    hideInSearch: true,
  },
  {
    dataIndex: 'spu_name',
    title: '商品名称',
    width: 200,
    hideInSearch: true,
  },
  {
    dataIndex: 'spu_status_label',
    title: '商品状态',
    width: 200,
    hideInSearch: true,
  },
  {
    dataIndex: 'updated_at',
    title: '资质更新时间',
    width: 200,
    valueType: 'date',
    hideInSearch: true,
  },
];
