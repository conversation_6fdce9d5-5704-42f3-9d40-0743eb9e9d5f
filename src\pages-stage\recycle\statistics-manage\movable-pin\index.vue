<template>
  <layout-admin-page :navs="['趣回收', '经营管理', '业务动销']">
    <template #title-prefix>
      <div>
        <div class="title">
          业务动销
        </div>
        <a-form
          layout="inline"
          :model="searchInfo"
        >
          <a-form-item
            label="数据来源"
            name="origin"
          >
            <a-select
              v-model:value="searchInfo.origin"
              :options="originOptions"
              placeholder="请选择"
              style="width: 184px"
              @change="reload"
            />
          </a-form-item>
          <a-form-item
            label="时间范围"
            name="rangeDate"
          >
            <a-range-picker
              v-model:value="searchInfo.rangeDate"
              :ranges="rangePresets"
              style="width: 422px"
              @change="onRangeChange"
            />
          </a-form-item>
        </a-form>
      </div>
    </template>
    <div class="container">
      <CardItem title="交易指标">
        <a-spin :spinning="indexLoading">
          <div class="grid-index-wrap">
            <DataStatisticsItem
              v-for="(item, index) in indexList"
              :key="index"
              :info="item"
              @update="(key, cb) => indexLoad(requestParams, key, cb)"
            />
          </div>
        </a-spin>
      </CardItem>
      <CardItem title="数据趋势">
        <a-spin :spinning="gmvDayLoading || gmvContributionLoading">
          <div class="grid-data-wrap">
            <GmvDayItem
              :info="statisticsInfo"
              :list="statisticsList"
            />
            <GmvContributionItem
              :info="gmvContributionInfo"
              :list="tableList"
            />
          </div>
        </a-spin>
      </CardItem>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { reactive, computed } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import { ISearchInfo } from './data.d';
import useIndexHook from './composables/use-index-hook';
import useGmvDayHook from './composables/use-gmv-day-hook';
import useGmvContributionHook from './composables/use-gmv-contribution-hook';

import CardItem from './components/card-item.vue';
import DataStatisticsItem from './components/data-statistics-item.vue';
import GmvDayItem from './components/gmv-day-item.vue';
import GmvContributionItem from './components/gmv-contribution-item.vue';

type RangeValue = [Dayjs, Dayjs];

const searchInfo = reactive<ISearchInfo>({
  origin: '0',
  rangeDate: [],
  rangeDateStrList: [],
});

const originOptions = [
  { label: '全部', value: '0' },
  { label: '个人回收', value: '1' },
  { label: '企业回收', value: '2' },
];

const rangePresets = {
  今天: [dayjs(), dayjs()],
  昨日: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')],
  本周: [dayjs().startOf('week'), dayjs()],
  上周: [dayjs().startOf('week').subtract(1, 'week'), dayjs().endOf('week').subtract(1, 'week')],
  本月: [dayjs().startOf('month'), dayjs()],
  上月: [dayjs().startOf('month').subtract(1, 'month'), dayjs().endOf('month').subtract(1, 'month')],
  本年: [dayjs().startOf('year'), dayjs()],
  去年: [dayjs().startOf('year').subtract(1, 'year'), dayjs().endOf('year').subtract(1, 'year')],
  过去7天: [dayjs().subtract(6, 'day'), dayjs()],
  过去14天: [dayjs().subtract(13, 'day'), dayjs()],
  过去30天: [dayjs().subtract(29, 'day'), dayjs()],
  过去60天: [dayjs().subtract(59, 'day'), dayjs()],
  过去90天: [dayjs().subtract(89, 'day'), dayjs()],
  过去180天: [dayjs().subtract(179, 'day'), dayjs()],
  上线至今: [dayjs('2023/09/01'), dayjs()],
};

const requestParams = computed(() => {
  return {
    start_at: searchInfo.rangeDateStrList[0],
    end_at: searchInfo.rangeDateStrList[1],
    source_type: searchInfo.origin,
  };
});

const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dates) {
    searchInfo.rangeDateStrList[0] = dateStrings[0];
    searchInfo.rangeDateStrList[1] = dateStrings[1];
  } else {
    searchInfo.rangeDateStrList = [];
  }
  reload();
};

const { loading: indexLoading, list: indexList, load: indexLoad } = useIndexHook();

const { loading: gmvDayLoading, statisticsInfo, statisticsList, load: gmvDayLoad } = useGmvDayHook();

const {
  loading: gmvContributionLoading,
  info: gmvContributionInfo,
  tableList,
  load: gmvContribution,
} = useGmvContributionHook();

const reload = () => {
  indexLoad(requestParams.value);
  gmvDayLoad(requestParams.value);
  gmvContribution(requestParams.value);
};
</script>

<style lang="less" scoped>
.title {
  margin-bottom: 24px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
}
.layout-admin-page {
  background: transparent;
}

.container {
  display: grid;
  grid-template-columns: auto;
  gap: 16px;
  .grid-index-wrap {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
  }
  .grid-data-wrap {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }
}
</style>
