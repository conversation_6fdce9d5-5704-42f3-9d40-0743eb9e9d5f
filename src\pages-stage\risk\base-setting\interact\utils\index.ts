export function formatModelSource2List(source: any[], staticOptions: { [key: string]: any }): any[] {
  const result: any[] = [];
  source.forEach((item: any) => {
    const configKeys = Object.keys(item.tmp_config.is_new_product);
    configKeys.forEach((key: string) => {
      const tagArray: any[] = [];
      item.model_ids.forEach((tags: string) => {
        tagArray.push({
          id: Object.entries(tags)[0][1],
          name: Object.entries(tags)[1][1],
        });
      });
      const listItem: { [key: string]: any } = {
        id: item.id,
        rowspan: configKeys.length,
        tags: tagArray,
        condition: staticOptions.condition[`con${key}`],
        // zm_category: item.config[key].zm_category,
      };
      const ratioGroup = item.tmp_config.is_new_product[key].risk_grade;
      Object.keys(ratioGroup).forEach((ratioKey: string) => {
        listItem[`ratio${ratioKey}`] = ratioGroup[ratioKey];
        listItem[`wholeRatio${ratioKey}`] = ratioGroup[ratioKey] ? ratioGroup[ratioKey] : '';
      });
      result.push(listItem);
    });
  });
  return result;
}

export function formatCategorySource2List(source: any[], staticOptions: { [key: string]: any }): any[] {
  const result: any[] = [];
  source.forEach((item: any) => {
      const levelKeys = Object.keys(item.tmp_config.deposit_grade);
      levelKeys.forEach((levelKey: string) => {
        const conditionKeys = Object.keys(item.tmp_config.deposit_grade[levelKey].is_new_product);
        conditionKeys.forEach((condiKey: string) => {
          const tagArray: any[] = [];
        item.category_ids.forEach((tags: string) => {
          tagArray.push({
            id: Object.entries(tags)[0][1],
            name: Object.entries(tags)[1][1],
          });})
          const listItem: { [key: string]: any } = {
            id: item.id,
            tags: tagArray || [],
            level: staticOptions.depositLevel[`dep${levelKey}`],
            condition: staticOptions.condition[`con${condiKey}`],
            // zm_category: item.config[levelKey][condiKey].zm_category,
            rowspan: levelKeys.length * conditionKeys.length,
            levelRowspan: conditionKeys.length,
          };
          const ratioGroup = item.tmp_config.deposit_grade[levelKey].is_new_product[condiKey].risk_grade;
          Object.keys(ratioGroup).forEach((ratioKey: string) => {
            listItem[`ratio${ratioKey}`] = ratioGroup[ratioKey];
            listItem[`wholeRatio${ratioKey}`] = ratioGroup[ratioKey] ? ratioGroup[ratioKey] : '';
          });
          result.push(listItem);
        });
        
      });
    });
    return result;
  }
  
  
