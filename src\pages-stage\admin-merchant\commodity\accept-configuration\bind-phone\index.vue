<template>
  <div class="page-wrap">
    <div class="title">
      手机号绑定
    </div>

    <div>
      1、根据监管部门的虚拟号备案要求，商家需完善平台登记的客服手机号的相关资料;如未提交运营商将对相关号码的隐私号实施限呼拦截或关停，用户将无法通过虚拟号联系您。
    </div>
    <div>2、当商家客服手机号异常时，客户联系商家会通过虚拟号拨打到重要通知手机号，请及时维护</div>
    <a-form
      layout="vertical"
      :model="formModal"
      style="margin-top: 24px"
    >
      <a-form-item>
        <template #label>
          <div class="label">
            重要通知手机
            <span class="gray">(商家接收重要消息的手机号码，建议是老板、总经理等店铺直接负责人)</span>
          </div>
        </template>
        <a-space>
          <a-input
            v-model:value="formModal.customer_phone"
            disabled
            placeholder="请输入"
            style="width: 300px"
          />
          <a-button
            type="primary"
            @click="showEditModal('important_phone')"
          >
            修改
          </a-button>
        </a-space>
      </a-form-item>
      <a-form-item>
        <template #label>
          <div class="label">
            客服手机
            <span class="gray">(产品详情显示的手机号码，建议是处理售前和售后的客服负责人)</span>
          </div>
        </template>
        <a-space>
          <a-input
            v-model:value="formModal.server_phone"
            disabled
            placeholder="请输入"
            style="width: 300px"
          />
          <a-button
            type="primary"
            @click="showEditModal('service_phone')"
          >
            修改
          </a-button>
        </a-space>
      </a-form-item>
      <a-form-item label="状态">
        <a-input
          v-model:value="formModal.status_text"
          disabled
          placeholder="请输入"
          style="width: 300px"
          type="text"
        />
      </a-form-item>
      <a-form-item label="客服手机号备案情况">
        <a-space>
          备案状态：
          <div>
            <a-badge
              :status="registractionStatus ? 'success' : 'error'"
              :text="registractionStatus ? '已备案' : '未备案'"
            />
          </div>
          <a-button
            v-if="!registractionStatus"
            type="primary"
            @click="handleJumpTicket"
          >
            去提交工单
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
    <a-modal
      v-model:visible="formModal.visible"
      title="修改信息"
      @ok="handleOk"
    >
      <a-form
        ref="formModalRef"
        layout="vertical"
        :model="formModal"
      >
        <a-form-item
          label="手机号码"
          name="phoneVal"
          :rules="[
            { required: true, message: '请输入正确的手机号码' },
            { min: 11, max: 11, message: '请输入11位的手机号码', trigger: 'blur' },
          ]"
        >
          <a-input
            v-model:value="formModal.phoneVal"
            placeholder="请输入"
            type="text"
          />
        </a-form-item>
        <a-form-item
          label="验证码"
          name="code"
          :rules="[{ required: true, message: '请输入正确的验证码' }]"
        >
          <a-input-group compact>
            <div style="display: flex; align-items: center">
              <a-input
                v-model:value="formModal.code"
                allow-clear
                placeholder="请输入"
                style="width: 150px; margin-right: 12px"
              />
              <aliyun-captcha :config="initAliyunCaptchaConfig" />
            </div>
          </a-input-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import type { IConfig } from '@/components/aliyun-captcha/data';
import { useTopWindow } from '@/hook/common/use-top-window';

import { editPhone, getPhone, getRegistractionStatus } from './service';

const showEditModal = (str: 'service_phone' | 'important_phone') => {
  formModal.key = str;
  formModal.visible = true;
  formModal.phoneVal = getPhoneVal();
};

const formModalRef = ref();

const formModal = reactive({
  customer_phone: '',
  server_phone: '',
  status_text: '',
  code: '',
  visible: false,
  key: '',
  phoneVal: '',
});

const registractionStatus = ref(false);

// const captchaBox = ref();

const fetchPhone = async () => {
  const { data } = await getPhone();
  Object.keys(formModal).map(k => {
    formModal[k] = data[k] || undefined;
  });
};

const ticketId = ref();

const fetchRegistractionStatus = async () => {
  const { data } = await getRegistractionStatus();
  registractionStatus.value = data.is_registration;
  ticketId.value = data.ticket_id;
};

onMounted(async () => {
  await fetchPhone();
  await fetchRegistractionStatus();
});

const getPhoneVal = () => {
  return formModal.key === 'service_phone' ? formModal.server_phone : formModal.customer_phone;
};

const initAliyunCaptchaConfig: IConfig = {
  url: '/api/ali-verify',
  params: formModal,
  formatHandle: params => {
    return {
      phone: params.phoneVal,
    };
  },
  beforeShow: () => {
    if (!formModal.phoneVal || !/^1[3456789]\d{9}$/.test(formModal.phoneVal)) {
      message.error('请输入正确的手机号');
      return false;
    }
    return true;
  }, // 打开验证前的钩子
  requestCallback: res => res.status === 0, // 请求结果，返回业务是否成功的结果,boolean类型
};

const { navigate } = useTopWindow();
const router = useRouter();

const handleJumpTicket = () => {
  const ticketIdVal = JSON.stringify(ticketId.value);
  if (process.env.NODE_ENV !== 'development') {
    navigate('blank', `/work-order/index?ticket_id=${ticketIdVal}`);

    return;
  }
  router.push(`/customer/tickets/submit?ticket_id=${ticketIdVal}`);
};

const handleOk = async () => {
  await formModalRef.value.validateFields();
  await editPhone({
    value: formModal.phoneVal,
    field: formModal.key,
    code: formModal.code,
  });
  const key = formModal.key;
  await fetchPhone();
  message.success('修改成功');

  if (key === 'service_phone') {
    Modal.confirm({
      title: '提示',
      icon: h(ExclamationCircleOutlined),
      content: '修改完手机号需进行新手机号的备案工单提交，去提交客服手机号备案工单么？',
      okText: '去提交',
      cancelText: '取消',
      onOk() {
        handleJumpTicket();
      },
    });
  }
};
</script>

<style scoped lang="less">
.page-wrap {
  min-height: 100vh;
  padding: 0 24px;
  background: #fff;
  .title {
    padding: 24px 0;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
  .gray {
    color: rgba(6, 21, 51, 0.45);
    font-size: 12px;
  }
}
</style>
