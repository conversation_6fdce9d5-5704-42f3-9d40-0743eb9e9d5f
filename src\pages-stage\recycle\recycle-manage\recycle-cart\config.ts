import moment from 'moment';

import { IFormState } from './data';

export const baseForm: IFormState = {
  userName: null,
  phone: null,
  bookTime: [],
  payeeName: null,
  payeeType: 1,
  return_name: null,
  return_phone: null,
  return_province_name: null,
  return_city_name: null,
  return_area_name: null,
  return_addr_detail: null,
  use_send_info: 1,
};

export const columns = [
  {
    title: '商品信息',
    key: 'goodsName',
    dataIndex: 'goodsName',
    width: 312,
  },
  {
    title: '预估商品信息',
    dataIndex: 'sku_info',
    width: 452,
  },
  {
    title: '预估回收价',
    dataIndex: 'evaluatePrice',
    width: 160,
  },
  {
    title: '操作',
    dataIndex: 'handle',
    width: 160,
    fixed: 'right',
  },
];

export const agreementOptions = [
  { label: '非合格商品平台处理', text: '等待检测不合格商品报价，并同意回收商直接按照商品报价回收您的商品。', value: 1 },
  { label: '非合格商品商家处理', text: '等待检测不合格商品报价，并由您查看定价后自行决定是否进行回收。', value: 2 },
  {
    label: '非合格商品检测退回',
    text: '不等待检测不合格商品报价，商品将按照退货地址（若未填写则为原寄件地址）退回给您。',
    value: 3,
  },
];
export function createTimeOptions(start = 8, end = 19) {
  // 生成配置信息
  const time: any[] = [];
  for (let i = start; i < end; i++) {
    time.push({
      id: i,
      value: `${i < 10 ? '0' + i : i}:00-${i + 1 < 10 ? '0' + (i + 1) : i + 1}:00`,
      label: `${i < 10 ? '0' + i : i}:00-${i + 1 < 10 ? '0' + (i + 1) : i + 1}:00`,
    });
  }
  const options = ['今天', '明天', '后天'].map((item, index) => {
    return {
      value: moment().add(index, 'days').format('YYYY-MM-DD'),
      label: item,
      children: time,
    };
  });
  const todayHour = moment().hour();
  // 当前时间是否大于最大时间 ？ 移除今天 : 过滤今天时间
  todayHour >= end ? options.splice(0, 1) : (options[0].children = time.filter(item => item.id > todayHour));
  return options;
}
