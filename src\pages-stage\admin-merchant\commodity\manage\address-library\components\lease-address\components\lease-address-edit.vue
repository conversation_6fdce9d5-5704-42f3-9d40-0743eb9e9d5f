<template>
  <a-modal
    :confirm-loading="modal.loading"
    :title="modal.title"
    :visible="modal.visible"
    :width="800"
    @cancel="onClose"
    @ok="onOk"
  >
    <template #footer>
      <a-button
        key="back"
        @click="onClose"
      >
        取消
      </a-button>
      <a-button
        key="submit"
        type="primary"
        @click="onOk"
      >
        确定
      </a-button>
    </template>
    <div style="max-height: 60vh; overflow: auto">
      <form-extend
        ref="form"
        :form-group="modal.form"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <template #content>
          <a-form
            ref="formSlot"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 18 }"
          >
            <template v-if="modal.type === 'send' || modal.type === 'store'">
              <a-form-item label="详细地址">
                <div class="flex-wrap flex-y-center">
                  <div
                    class="flex-con"
                    style="padding-right: 20px"
                  >
                    <a-alert
                      v-if="location.name"
                      :message="location.name"
                      show-icon
                      type="success"
                    />
                    <a-alert
                      v-else
                      message="请设置详细地址"
                      show-icon
                      type="warning"
                    />
                  </div>
                  <a
                    v-if="location.name"
                    style="margin-right: 12px"
                    @click="onClearLocation"
                  >
                    清除
                    <CloseCircleOutlined />
                  </a>
                  <a-button
                    type="primary"
                    @click="onOpenLocation"
                  >
                    设置
                  </a-button>
                </div>
              </a-form-item>
              <a-form-item label="门牌号">
                <a-input
                  v-model:value="houseNumber"
                  placeholder="例：5楼301室"
                />
              </a-form-item>
            </template>
          </a-form>
        </template>
      </form-extend>
      <location
        :options="locationModal.options"
        :visible="locationModal.visible"
        @on-select="onLocationSelect"
        @on-visible-change="val => (locationModal.visible = val)"
      />
    </div>
  </a-modal>
</template>
<script>
import { message } from 'ant-design-vue';
import { CloseCircleOutlined } from '@ant-design/icons-vue';

import Location from '@/components/location/location.vue';
import { POST } from '@/services/api';

export default {
  components: {
    Location,
    CloseCircleOutlined,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'send', // send, return, store
    },
    item: {
      type: Object,
      default() {
        return {};
      },
    },
    isLiBang: {
      // 是否为立邦
      type: Boolean,
      default: false,
    },
  },
  emits: ['on-success', 'on-visible-change'],
  data() {
    return {
      modal: {
        visible: false,
        form: [],
        loading: false,
        title: '',
        type: '',
        url: '',
      },
      unionData: [],
      unionSelect: [],
      location: {
        name: '',
        lat: '',
        lng: '',
        city: '',
        province: '',
      },
      locationModal: {
        visible: false,
        options: {},
      },
      returnAddress: '',
      houseNumber: '',
    };
  },
  watch: {
    visible(val) {
      this.modal.visible = val;
      if (val) {
        this.openModal(this.item);
      } else {
        this.location = {
          name: '',
          lat: '',
          lng: '',
          city: '',
          province: '',
        };
        this.unionSelect = [];
        this.houseNumber = '';
      }
    },
  },
  methods: {
    openModal(data = {}) {
      const status = data.type;
      switch (this.type) {
        case 'send':
          this.modal.title = status === 'edit' ? '修改租赁地址' : '添加租赁地址';
          this.modal.url = status === 'edit' ? '/address/update' : '/address/create';
          this.modal.form = [
            {
              title: '姓名',
              type: 'string',
              key: 'name',
              value: data.name || '',
              rules: [
                {
                  required: true,
                  message: '请输入姓名',
                },
              ],
            },
            {
              title: '手机号',
              type: 'string',
              key: 'tell',
              value: data.tell || '',
              rules: [
                {
                  required: true,
                  message: '请输入手机号',
                },
              ],
            },
            {
              title: '服务承诺',
              type: 'textarea',
              key: 'rent_desc',
              placeholder:
                '每行一条服务承诺，例如 \n 1、24小时*7服务，节假日无休；\n 2、30分钟响应，2小时上门，4小时解决，8小时提供备用商品',
              value: data.rent_desc || '',
            },
            {
              title: '公司形象图片',
              type: 'uploadImage',
              key: 'cover',
              max: '1',
              pathName: 'scheme',
              value: data.cover ? [data.imgOssServer + data.cover] : [],
            },
          ];
          break;
        default:
          break;
      }
      if (['edit'].includes(status))
        this.onLocationSelect(
          Object.assign(data, {
            point: {
              addressComponents: {
                city: data.city,
                province: data.province,
              },
            },
            lng: data.lon,
          }),
        );
      this.modal.type = this.type;
      this.modal.visible = true;
      this.modal.loading = false;
    },
    onOk() {
      this.$refs.form.submit().then(res => {
        if (!res.rent_desc) return message.info('服务承诺不能为空');
        if (!res.cover.length) return message.info('公司形象图片不能为空');
        if (!this.location.name) return message.info('详细地址不能为空');
        if (res.rent_desc.length > 300) {
          return message.info('服务承诺字数不可大于300');
        }
        if (!this.houseNumber) {
          return message.info('门牌号不能为空');
        }
        let postData = {};
        let cover = '';
        // 这个比较特殊，路径不要前缀
        if (Array.isArray(res.cover) && res.cover.length) {
          const url = res.cover[0];
          const comIndex = url.indexOf('.com/');
          const comUrl = comIndex !== -1 ? url.slice(comIndex + 4) : '';
          const cnIndex = url.indexOf('.cn/');
          const cnUrl = cnIndex !== -1 ? url.slice(cnIndex + 3) : '';
          cover = comUrl || cnUrl || '';
        }
        if (this.type === 'send') {
          postData = {
            TblServerAddress: {
              ...res,
              address: `${this.location.name.trim()} ${this.houseNumber.trim()}`, // 需要拼接门牌号
              lon: this.location.lng,
              lat: this.location.lat,
              city: this.location.city,
              province: this.location.province,
              cover,
            },
          };
          if (this.item.type === 'edit') postData['id'] = this.item.id;
        }
        this.modal.loading = true;
        POST(this.modal.url, postData)
          .then(() => {
            this.onClose();
            this.$message.success('操作成功');
            this.$emit('on-success');
          })
          .finally(() => {
            this.modal.loading = false;
          });
      });
    },
    onClose() {
      this.modal.visible = false;
      this.$emit('on-visible-change', false);
    },
    onOpenLocation() {
      this.locationModal.visible = true;
      this.locationModal.options = {
        address: this.location.name,
        lat: this.location.lat,
        lng: this.location.lng,
      };
    },
    onLocationSelect(ret) {
      // 地址需要拆分为 地址 和 门牌号
      const addressArr = ret.address.split(' ');
      if (addressArr.length > 1) {
        this.houseNumber = addressArr.splice(addressArr.length - 1, 1)[0];
      }
      this.location.name = addressArr.join(' ');
      this.location.lat = ret.lat;
      this.location.lng = ret.lng;
      this.location.city = ret.point.addressComponents.city || ret.point.addressComponents.district; // 地级市返回city，县级市返回district
      this.location.province = ret.point.addressComponents.province;
    },
    onClearLocation() {
      this.location = {
        name: '',
        lat: '',
        lng: '',
        city: '',
        province: '',
      };
      this.houseNumber = '';
    },
  },
};
</script>
