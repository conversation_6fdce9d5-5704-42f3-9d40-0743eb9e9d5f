import { GET, POST } from '@/services/api';
export function updateCate(data: {
  ModArticleCate: {
    name: string;
  };
}): Promise<any> {
  return POST('/shop/article-cate/update', data);
}

export function createCate(data: {
  ModArticleCate: {
    name: string;
  };
}): Promise<any> {
  return POST('/shop/article-cate/create', data);
}

export function deleteCateById(data: { id: string | number }): Promise<any> {
  return POST('/shop/article-cate/delete', data);
}

export function getArticleCateIndexInf(data: any): Promise<any> {
  return GET('/shop/article-cate/index-inf', data);
}
