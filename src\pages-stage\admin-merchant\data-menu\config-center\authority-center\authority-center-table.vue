<template>
  <a-table
    :columns="columns"
    :data-source="list"
    :loading="loading"
    :pagination="false"
  >
    <template #headerCell="{ title }">
      <span class="emphasis">{{ title }}</span>
    </template>
    <template #emptyText>
      <REmpty />
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'permission'">
        {{ formatPermission(record) }}
      </template>
      <template v-if="column.key === 'operator'">
        <div
          class="operator-btn edit-btn"
          @click="edit(record)"
        >
          编辑
        </div>
        <div
          class="operator-btn delete-btn"
          @click="unbind(record)"
        >
          解绑
        </div>
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { createVNode, PropType } from 'vue';
import { message, Modal, TableColumnType } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

import { AuthorityCenterRow } from './data';
import { unbindAccount } from './service';

const columns: TableColumnType[] = [
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '权限',
    dataIndex: 'permission',
    key: 'permission',
  },
  {
    title: '操作',
    dataIndex: 'operator',
    key: 'operator',
  },
];
defineProps({
  list: {
    type: Array as PropType<AuthorityCenterRow[]>,
    default() {
      return [];
    },
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['resetList', 'edit']);

// 编辑
function edit(row: AuthorityCenterRow) {
  emit('edit', row);
}

// 解绑
function unbind(row: AuthorityCenterRow) {
  Modal.confirm({
    title: '是否确认解除绑定？',
    icon: createVNode(ExclamationCircleFilled),
    content: '解绑后该账号在租赁数智不可见本店铺数据',
    onOk() {
      unbindAccount({ account_id: row.account_id }).then(() => {
        message.success('账号解绑完成');
        emit('resetList');
      });
    },
  });
}

// 格式化权限
function formatPermission(row: any) {
  return [
    {
      label: '查询',
      value: row.auth_search,
    },
    {
      label: '导出',
      value: row.auth_export,
    },
  ]
    .filter(item => item.value)
    .map(item => item.label)
    .join('、');
}
</script>

<style scoped lang="less">
.operator-btn {
  display: inline-block;
  margin-right: 16px;
  color: #3777ff;
  cursor: pointer;
}
</style>
