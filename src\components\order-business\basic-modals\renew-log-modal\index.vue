<template>
  <a-modal
    v-model:visible="bindVisible"
    :mask-closable="false"
    title="续租订单"
    width="fit-content"
  >
    <a-tabs
      v-model:activeKey="activeKey"
      @change="getLogData"
    >
      <a-tab-pane
        key="new"
        tab="续租记录"
      >
        <div
          v-if="isSuper"
          style="margin: 10px 0 20px 0; color: rgb(6, 21, 51)"
        >
          初始购买价：{{ baseBuyoutPrice }} 元
        </div>
        <a-table
          :columns="newLogTableColumns"
          :data-source="newLogList"
          :loading="loading"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'date_start'">
              {{ record.date_start }} 至 {{ record.date_end }}
            </template>
            <template v-if="column.key === 'bill_money'">
              {{ record.bill_money }}元
            </template>
            <template v-if="column.key === 'buyout_price'">
              {{ record.buyout_price }}元
            </template>
            <template v-if="column.key === 'created_by'">
              {{ record.created_by }}({{ record.created_role }})
            </template>
            <template v-if="column.key === 'agree_user'">
              {{ record.agree_user }}({{ record.agree_user_role }})
            </template>
            <template v-if="column.key === 'protocol_url'">
              <div
                v-if="record.is_has_file == 1 && record.protocol_url"
                style="color: #3777ff; cursor: pointer"
                @click="WINDOW.open(record.protocol_url)"
              >
                查看
              </div>
              <div v-else>
                -
              </div>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane
        v-if="isSuper"
        key="old"
        force-render
        tab="续租记录（旧）"
      >
        <a-table
          :columns="oldLogTableColumns"
          :data-source="oldLogList"
          :loading="loading"
          :pagination="false"
        >
          <template #bodyCell="{ column, index, record }">
            <template v-if="column.key === 'count'">
              续{{ index + 1 }}
            </template>
            <template v-if="column.key === 'lease_term'">
              {{ record.time_start }} 至 {{ record.time_end }}
            </template>
            <template v-if="column.key === 'money'">
              {{ record.money }}元
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { useVModel } from '@/hook';

import { newLogTableColumns, oldLogTableColumns } from './config';
import type { INewLogListItem, IOldLogListItem } from './data';
import { getNewRenewHistory, getOldRenewHistory, getRenewHistory } from './services';

const props = defineProps<{
  visible?: boolean;
  orderId: string;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

const WINDOW = window;
/** 初始购买价 */
const baseBuyoutPrice = ref(0);
const newLogList = ref<INewLogListItem[]>([]);
const oldLogList = ref<IOldLogListItem[]>([]);
const activeKey = ref<'new' | 'old'>('new');
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const isSuper = useRoute().query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      getLogData();
    }
  },
  { immediate: true },
);

async function getLogData() {
  loading.value = true;
  try {
    if (isSuper) {
      // 运营人员使用
      if (activeKey.value === 'new') {
        const res = await getNewRenewHistory({ order_id: props.orderId });
        baseBuyoutPrice.value = res.data.base_buyout_price;
        newLogList.value = res.data.list;
      } else {
        const res = await getOldRenewHistory({ id: props.orderId });
        oldLogList.value = res.data;
      }
    } else {
      // 商家人员使用
      const res = await getRenewHistory({ order_id: props.orderId });
      baseBuyoutPrice.value = res.data.base_buyout_price;
      newLogList.value = res.data.list;
    }
  } finally {
    loading.value = false;
  }
}
</script>
