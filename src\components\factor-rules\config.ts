/** 发货时效 */
export const deliveryTimeOptions = [
  { value: '12', label: '12小时内发货' },
  { value: '24', label: '24小时内发货' },
  { value: '48', label: '48小时内发货' },
  { value: '72', label: '72小时内发货' },
];

/** 收发地关系 */
export const cityOptions = [
  { value: '0', label: '无' },
  { value: '1', label: '同城市' },
  { value: '2', label: '同省份' },
  { value: '3', label: '同地区' },
  { value: '4', label: '不同地区（不含偏远地区）' },
  { value: '5', label: '不同地区（含偏远地区）' },
];

// 寄出方式。1-包邮;2-到付;3-到店,4-统一运费;5运费模板;6.下单满x天包邮;7.无需支付;8.自付,9.上门安装
export const expressOptions = [
  { value: '1', label: '包邮' },
  { value: '2', label: '到付' },
  { value: '3', label: '到店自取' },
  { value: '4', label: '统一运费' },
  { value: '5', label: '运费模板' },
  { value: '6', label: '下单满x天包邮' },
  // { value: '7', label: '无需支付' },
  // { value: '8', label: '自付' },
  { value: '9', label: '上门安装' },
];

/** 配送方式 */
export const sendOutOptions = [
  { value: '1', label: '快递配送' },
  { value: '2', label: '上门自提' },
  { value: '3', label: '同城闪送' },
];

/** 同城商品库 */
export const isSameCityOptions = [
  { value: '2', label: '否' },
  { value: '1', label: '是' },
];
