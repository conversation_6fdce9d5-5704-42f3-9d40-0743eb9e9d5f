<template>
  <!-- 风控报告-内容 -->
  <div
    v-if="riskData?.base_info"
    class="risk-report"
  >
    <div class="report-title-wrapper flex-wrap flex-y-center">
      <div class="title-icon title-icon-results" />
      <span class="title">风控结果</span>
      <span class="title-tips">请商家根据风控结果决策，不配合运营策略会受到流量管控</span>
      <div class="header-info">
        <div>版本号：{{ riskData.base_info?.version_id }}</div>
        <div class="header-info-line" />
        <div>报告编号：{{ riskData.base_info?.id }}</div>
        <div class="header-info-line" />
        <div>生成时间：{{ riskData.base_info?.created_time }}</div>
      </div>
    </div>
    <!-- 风控结果 -->
    <div class="report-card risk-control-results">
      <div>
        <div :class="['header-bg-wrap', 'header-bg-wrap-' + getStatus()]">
          <div :class="['header-res-title', 'header-res-title-' + getStatus()]">
            <div
              class="header-icon-wrap"
              :class="['header-icon-wrap-' + getStatus()]"
            />
            风控{{ riskData.result_info?.risk_qualitative.value }}
          </div>
          <div
            v-if="showFace"
            class="live-detection"
            :class="[riskData.isFace ? 'live-detection-success' : 'live-detection-error']"
          >
            <template v-if="riskData.isFace">
              <div class="live-detection-icon live-detection-icon-success" />
              活体检测通过
            </template>
            <template v-else>
              <div class="live-detection-icon live-detection-icon-error" />
              活体检测不通过
            </template>
          </div>
        </div>
        <div class="content-bg-wrap">
          {{ riskData.result_info?.risk_result.value }}
        </div>
      </div>
      <div
        v-if="riskData.result_info?.risk_advise.value"
        class="risk-precautions"
      >
        <a-tag class="tag">
          注意事项
        </a-tag>
        {{ riskData.result_info?.risk_advise.value }}
      </div>
    </div>
    <!-- 客户信息 -->
    <div
      v-if="riskData.user_info?.length > 0"
      class="report-card customer-info"
    >
      <div class="report-title-wrapper">
        <div class="title-icon title-icon-info" />
        <span class="title">客户信息</span>
      </div>
      <div class="user-info-wrapper">
        <div
          v-for="(item, index) in riskData.user_info"
          :key="item"
          :class="['user-info-item', item.label === '身份证' && 'grid-2', item.label === '收货地址' && 'grid-4']"
        >
          <div class="user-info-item-label">
            {{ item.label }}
            <template v-if="item.desc">
              <div
                v-show="showDesc[index]"
                class="desc-tooltip"
              >
                {{ item.desc }}
              </div>
              <i
                class="icon-question"
                @mouseenter="showDesc[index] = true"
                @mouseleave="showDesc[index] = false"
              />
            </template>
          </div>
          <div class="user-info-item-value">
            {{ item.value }}
          </div>
        </div>
      </div>
    </div>
    <!-- 风控日志 -->
    <div
      v-if="riskData.log_info?.length > 0"
      class="report-card risk-control-log"
    >
      <div class="report-title-wrapper">
        <div class="title-icon title-icon-log" />
        <span class="title">风控日志</span>
      </div>
      <div class="risk_log_info_table">
        <div class="row t_header">
          <div class="col index title">
            关键指标
          </div>
          <div class="col detail title">
            详情
          </div>
        </div>
        <template
          v-for="item in riskData?.log_info"
          :key="item.id"
        >
          <div class="row">
            <div class="col index">
              {{ item.label }}
            </div>
            <div
              v-if="item.id > 33 && item.id < 42"
              class="col detail"
            >
              <div
                v-if="item.type === 1"
                class="progress_box"
              >
                <div class="percent_box">
                  <div
                    class="percent"
                    :style="{
                      width: PROGRESS_STATUS[item.value]['percent'] + 'px',
                      backgroundColor: PROGRESS_STATUS[item.value]['color'],
                    }"
                  />
                </div>
                <span :style="{ color: PROGRESS_STATUS[item.value]['textColor'] }">{{
                  PROGRESS_STATUS[item.value]['text']
                }}</span>
              </div>
              <div
                v-if="item.type === 0"
                :class="{ tag: true, hit: item.value >= 1 }"
              >
                {{ item.value >= 1 ? '命中' : '未命中' }}
              </div>
            </div>
            <div
              v-else
              class="col detail"
            >
              {{ item.value }}
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, PropType, reactive } from 'vue';

import { IRiskReportData } from './data';
const props = defineProps({
  data: {
    type: Object as PropType<IRiskReportData>,
    default: () => ({}),
  },
  isDrag: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'update:data', data: IRiskReportData): void;
}>();

const PROGRESS_STATUS = {
  0: { text: '无', textColor: 'rgba(6,21,51,0.45)', color: 'rgb(6,21,51)', percent: 0 },
  1: { text: '少', textColor: '#3777FF', color: '#3777FF', percent: 51 },
  2: { text: '中', textColor: '#FAAD14', color: '#FAAD14', percent: 162 },
  3: { text: '多', textColor: '#FF4D4F', color: '#FF4D4F', percent: 252 },
};

const riskData = computed({
  get: () => props.data,
  set: data => emit('update:data', data),
});

const showFace = computed(() => {
  const faceInfo = riskData.value.user_info?.filter((item: any) => item.label === '活体检测');
  return Boolean(faceInfo?.length);
});

const showDesc = reactive<boolean[]>([]);

function getStatus() {
  let type = '';
  if (riskData.value.result_info?.risk_qualitative.result === '1') {
    type = 'success';
  } else if (riskData.value.result_info?.risk_qualitative.result === '2') {
    type = 'warning';
  } else {
    type = 'error';
  }
  return type;
}
</script>

<style scoped lang="less">
.risk-report {
  width: 100%;
  padding: 24px 24px 0;
  overflow-y: auto;
}

.report-title-wrapper {
  display: flex;
  align-items: center;
  padding: 0 0 8px 0;
  .title {
    margin-right: 8px;
    margin-left: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
  .title-icon {
    width: 16px;
    height: 16px;
  }

  .title-icon-results {
    background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/9Npb8emm9jjUHqJ0VxEU.png') no-repeat;
    background-size: 100%;
  }
  .title-icon-info {
    background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/OIXiYXhi78uwdu4xCtkJ.png') no-repeat;
    background-size: 100%;
  }
  .title-icon-log {
    background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/0K6lsWxgPhulgPO4nGph.png') no-repeat;
    background-size: 100%;
  }
  .title-tips {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 12px;
    line-height: 22px;
  }
}
.header-info {
  display: flex;
  align-items: center;
  margin-left: 32px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  .header-info-line {
    width: 1px;
    height: 10px;
    margin-right: 12px;
    margin-left: 12px;
    background: rgba(6, 21, 51, 0.15);
    border-radius: 0 0 0 0;
  }
}
.report-card {
  padding: 16px;
  background: linear-gradient(180deg, #fff 0%, #fff 100%);
  border-radius: 8px 8px 8px 8px;
  box-shadow: 0 4px 16px 0 rgba(6, 21, 51, 0.08);
}
// 活体检测
.live-detection {
  display: flex;
  align-items: center;
  height: 22px;
  padding: 0 8px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 4px 4px 4px 4px;
  &-success {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }
  &-error {
    color: #ff4d4f;
    background: #fff1f0;
    border: 1px solid #eb8f8f;
  }
  .live-detection-icon {
    width: 14px;
    height: 14px;
    margin-right: 3px;
    &-success {
      background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/w4KQK6KeZnCHSg4rFpXE.png') no-repeat;
      background-size: 100%;
    }
    &-error {
      background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/deVDbU5z9L5OYXrKmipn.png') no-repeat;
      background-size: 100%;
    }
  }
}
// 风控结果
.risk-control-results {
  padding: 24px 16px;
  .header-bg-wrap {
    position: relative;
    display: flex;
    align-items: center;
    width: 820px;
    height: 41px;
    padding: 2px 12px;
    border-radius: 4px 4px 4px 4px;
    &-success {
      background: rgba(142, 225, 134, 0.06);
      background-size: 100%;
    }
    &-warning {
      background: rgba(225, 179, 134, 0.06);
      background-size: 100%;
    }
    &-error {
      background: rgba(251, 133, 133, 0.06);
      background-size: 100%;
    }

    .header-res-title {
      display: flex;
      align-items: center;
      margin-right: 12px;
      font-weight: bold;
      font-size: 24px;
      line-height: 36px;
      &-success {
        color: #52c41a;
      }
      &-warning {
        color: #faad14;
      }
      &-error {
        color: #ff4d4f;
      }
      .header-icon-wrap {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        &-success {
          background: url('https://img1.rrzuji.cn/uploads/scheme/2504/07/m/gOMfW9SASxVqPbpdsHQK.png') no-repeat;
          background-size: 100%;
        }
        &-warning {
          background: url('https://img1.rrzuji.cn/uploads/scheme/2504/07/m/0Bl8D8GnWTcF7rpOFaML.png') no-repeat;
          background-size: 100%;
        }
        &-error {
          background: url('https://img1.rrzuji.cn/uploads/scheme/2504/07/m/4oT9T2QyI9zhLRwtqEnK.png') no-repeat;
          background-size: 100%;
        }
      }
    }
  }
  .content-bg-wrap {
    margin-top: 20px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
  }

  // 注意事项
  .risk-precautions {
    width: 820px;
    margin-top: 20px;
    padding-top: 20px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    border-top: 1px solid rgba(32, 32, 32, 0.04);
    .tag {
      color: #fa8c16;
      background: #fff7e6;
      border: 1px solid #ffd591;
    }
  }
}
// 客户信息
.customer-info {
  margin: 16px 0;
  .user-info-wrapper {
    display: grid;
    flex: 1;
    grid-template-columns: repeat(6, 1fr);
    margin-top: 16px;
    row-gap: 16px;
    .user-info-item {
      &.grid-2 {
        grid-column-start: span 2;
      }

      &.grid-4 {
        grid-column-start: span 4;
      }
      .user-info-item-label {
        position: relative;
        z-index: 9;
        display: flex;
        align-items: center;
        height: 55px;
        margin-bottom: 4px;
        padding: 0 16px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        background: #f9f9fb;
        border-bottom: 1px solid rgba(6, 21, 51, 0.06);
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 1px;
          height: 1.6em;
          background-color: rgba(0, 0, 0, 0.06);
          transform: translateY(-50%);
          transition: background-color 0.3s;
          content: '';
        }
        .desc-tooltip {
          position: absolute;
          top: 0;
          max-width: 100%;
          padding: 5px 8px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          background: rgba(6, 21, 51, 0.75);
          border-radius: 4px;
          box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 0.05), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
            0 3px 6px -4px rgba(0, 0, 0, 0.12);
          transform: translateY(-100%);
        }
        .icon-question {
          display: inline-block;
          width: 14px;
          height: 14px;
          margin-left: 4px;
          background: url(https://img1.rrzuji.cn/uploads/scheme/2202/08/m/aal0s9MT92sKLpuYsHr2.png) no-repeat;
          background-size: 100%;
          cursor: pointer;
        }
      }
      .user-info-item-value {
        width: 100%;
        height: 55px;
        padding: 16px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        background: rgba(255, 255, 255, 0);
        border-bottom: 1px solid rgba(6, 21, 51, 0.06);
      }
    }
  }
}
// 风控日志
.risk-control-log {
  .risk_log_info_table {
    width: 100%;
    margin-top: 8px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    .t_header {
      color: rgba(6, 21, 51, 0.45);
      .title {
        position: relative;
        display: flex;
        align-items: center;
        height: 55px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        background: #f9f9fb;
        border-bottom: 1px solid rgba(6, 21, 51, 0.06);
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 1px;
          height: 1.6em;
          background-color: rgba(0, 0, 0, 0.06);
          transform: translateY(-50%);
          transition: background-color 0.3s;
          content: '';
        }
      }
    }
    .row {
      display: flex;
      align-items: center;
      height: 55px;
      border-bottom: 1px solid rgba(6, 21, 51, 0.06);
    }

    .col {
      padding-left: 16px;
    }

    .index {
      flex: 40%;
      .des {
        margin-left: 8px;
        color: rgba(6, 21, 51, 0.45);
        font-size: 12px;
      }
    }

    .detail {
      flex: 60%;
      .tag {
        width: 52px;
        height: 22px;
        color: #3777ff;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        background: #f0f7ff;
        border: 1px solid #b3d2ff;
        border-radius: 4px;

        &.hit {
          color: #f5222d;
          background: #fff1f0;
          border: 1px solid #ffa39e;
        }
      }

      .progress_box {
        display: flex;
        align-items: center;

        .percent_box {
          position: relative;
          width: 300px;
          height: 6px;
          background: rgba(6, 21, 51, 0.06);
          border-radius: 6px;

          .percent {
            position: absolute;
            top: 0;
            left: 0;
            width: 100px;
            height: 6px;
            background-color: red;
            border-radius: 8px;
          }
        }

        span {
          margin-left: 16px;
          color: #3777ff;
          font-weight: 400;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
