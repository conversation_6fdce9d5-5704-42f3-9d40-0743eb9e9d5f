<template>
  <a-card
    :class="cardItemClass"
    hoverable
    :style="cardStyle"
  >
    <template #actions>
      <div class="flex-wrap flex-x-around">
        <EditOutlined @click="onEdit(item)" />
        <div v-if="!allowDelete" />
        <DeleteOutlined
          v-if="allowDelete"
          @click="onDelete(item)"
        />
        <a-switch
          v-if="useSwitch"
          :checked="switchChecked"
          size="small"
          @change="onSwitchChange"
        />
      </div>
    </template>
    <a-card-meta>
      <template
        v-if="item.name"
        #title
      >
        {{ item.name }}
      </template>
      <template #description>
        <label
          class="list-wrap"
          :style="switchWrapStyle"
        >
          <template v-if="useSwitch">
            <a-tag
              v-if="switchChecked"
              color="success"
            > 启用 </a-tag>
            <a-tag
              v-else
              color="error"
            > 未启用 </a-tag>
          </template>
        </label>

        <label
          v-if="item.path"
          class="line-wrap"
        >
          {{ item.path }}
        </label>

        <template v-if="cardTextType === 'add-gift'">
          <a-row>
            <a-col span="8">
              <label class="line-wrap">福利类型<a-tag color="blue">{{ item.gift_type === 1 ? '优惠券' : '现金' }}</a-tag></label>
              <label class="line-wrap">福利内容<a-tag color="blue">{{ item.gift_content || '未设置' }}</a-tag></label>
            </a-col>
            <a-col span="8">
              <label class="line-wrap">首页开启<a-tag color="blue">{{ item.index_open === 1 ? '开启' : '关闭' }}</a-tag></label>
              <label class="line-wrap">下单成功页<a-tag color="blue">{{ item.order_success_open === 1 ? '开启' : '关闭' }}</a-tag></label>
            </a-col>
            <a-col span="8">
              <label class="line-wrap">已领现金金额<a-tag color="red">{{ item.money_num || 0 }} 元</a-tag></label>
              <label class="line-wrap">已领优惠券张数<a-tag color="red">{{ item.coupon_num || 0 }} 张</a-tag></label>
            </a-col>
          </a-row>
        </template>
        <template v-if="cardTextType === 'member-type'">
          {{ item.member ? '开启' : '关闭' }}会员视角
        </template>
        <template v-if="cardTextType === 'radius-type'">
          {{ item.top ? '开启' : '关闭' }}上圆角<br>
          {{ item.bottom ? '开启' : '关闭' }}下圆角
        </template>
        <!-- 收藏引导 -->
        <template v-if="cardTextType === 'collection-guidance'">
          <div class="collection-guidance-box">
            <div class="collection-guidance flex-wrap flex-x-justify">
              <div class="triangle" />
              <div class="collection-guidance-text">
                {{ item.title }}
              </div>
              <div class="collection-guidance-close">
                <close-outlined />
              </div>
            </div>
            <a-tooltip placement="bottom">
              <template #title>
                {{ item.page_type === '0' ? item.page_link : pageTypeTextMap[item.page_type] }}
              </template>
              <div class="page-type">
                {{ item.page_type === '0' ? item.page_link : pageTypeTextMap[item.page_type] }}
              </div>
            </a-tooltip>
          </div>
        </template>
        <!-- 添加小程序引导 -->
        <template v-else-if="cardTextType === 'add-mini-program-guidance'">
          <div class="collection-guidance-box">
            <div class="add-mini-program-guidance flex-wrap">
              <div class="triangle" />
              <div
                class="collection-guidance-close"
                style="margin-right: 8px"
              >
                <close-outlined />
              </div>
              <div class="add-mini-program-guidance-text">
                {{ item.title }}
                <div
                  v-if="item.btnText"
                  class="collection-guidance-btn"
                >
                  {{ item.btnText }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </template>
    </a-card-meta>
  </a-card>
</template>
<script>
/**
 * cardtext 容器
 */
import { CloseOutlined } from '@ant-design/icons-vue';

import { card } from '@/mixin/card';

export default {
  components: { CloseOutlined },
  mixins: [card],
  data() {
    return {
      pageTypeTextMap: {
        1: '商品详情',
        2: '确认订单',
        3: '订单详情-待付款',
        4: '订单详情-审核中',
        5: '订单详情-待发货',
        6: '订单详情-申请取消中',
        7: '订单详情-待收货',
        8: '订单详情-租用中',
        9: '订单详情-续租中',
        10: '订单详情-待归还',
        11: '订单详情-已归还',
        12: '订单详情-已购买',
        13: '订单详情-已取消',
        14: '订单详情-归还中',
      },
    };
  },
};
</script>
<style lang="less" scoped>
.line-wrap {
  display: -webkit-box;
  height: 44px;
  overflow: hidden;
  line-height: 44px;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  .ant-tag {
    margin-left: 20px;
  }
}
.collect-guide-card {
  :deep(.ant-card-body) {
    padding: 0;
  }
}
.collection-guidance-box {
  min-height: 90px;
}
.collection-guidance,
.add-mini-program-guidance {
  width: 168px;
  margin: 56px 16px 10px;
  padding: 6px;
  color: #fff;
  font-weight: 400;
  font-size: 11px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 16px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 5px;
  .triangle {
    position: absolute;
    top: 40px;
    left: 50%;
    border-top: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid rgba(0, 0, 0, 0.7);
    border-left: 6px solid transparent;
    transform: translateX(-50%);
  }
  .collection-guidance-text {
    width: 144px;
    margin-right: 4px;
    white-space: wrap;
  }
  .add-mini-program-guidance-text {
    display: flex;
    align-items: center;
    white-space: nowrap;
  }
  .collection-guidance-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    padding: 2px 8px;
    color: #fff;
    font-size: 11px;
    white-space: nowrap;
    background: #00c8be;
    border-radius: 40px;
  }
}
.page-type {
  max-width: 168px;
  margin: 0 auto;
  padding-bottom: 14px;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 14px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Medium, PingFang SC;
  line-height: 22px;
  white-space: nowrap;
  text-align: center;
  text-overflow: ellipsis;
}
.add-mini-program-guidance {
  align-items: center;
  width: auto;
  min-width: 168px;
  .triangle {
    right: 16%;
    left: unset;
  }
}
</style>
