<template>
  <div
    v-if="Object.keys(mark).length > 0"
    class="page"
  >
    <div
      v-for="(item, index) in serviceList"
      :key="index"
      class="list"
    >
      <div class="title">
        {{ item.title }}
      </div>
      <template
        v-for="item2 in item.items"
        :key="item2.key"
      >
        <div
          v-if="item2.key !== 'guarantee' || serverType !== '1'"
          class="item"
        >
          <div class="img">
            <img
              alt=""
              :src="item2.src"
            >
          </div>
          <div class="content">
            <div class="sub-title">
              {{ item2.title }}
            </div>
            <div class="info">
              {{ item2.info }}
            </div>
            <div
              v-if="item2.tip"
              class="tip"
            >
              {{ typeof item2.tip === 'function' ? item2.tip() : '' }}
            </div>
          </div>
          <template v-if="isGuarantee(item2.key)">
            <a-button
              class="button"
              type="primary"
              @click="handleOpenService(item2)"
            >
              {{ isOpenFn(item2.key) ? '开通' : '已开通' }}
            </a-button>
          </template>
          <template v-else-if="isDecorate(item2.key)">
            <a-button
              class="button"
              disabled
              type="primary"
            >
              {{ '已开通' }}
            </a-button>
          </template>
          <template v-else>
            <a-button
              class="button"
              :disabled="!item2.tip || !isOpenFn(item2.key)"
              type="primary"
              @click="handleOpenService(item2)"
            >
              {{ item2.tip ? (isOpenFn(item2.key) ? '开通' : '已开通') : isOpenFn(item2.key) ? '未获得' : '已获得' }}
            </a-button>
          </template>
        </div>
      </template>
    </div>
    <a-modal
      v-model:visible="openVisible"
      cancel-text="暂不开通"
      ok-text="免费开通"
      title="服务开通"
      @ok="handleSubmitOpen"
    >
      <div
        v-if="openItem['key']"
        class="item open-item"
      >
        <div class="img">
          <img
            alt=""
            :src="openItem.src"
          >
        </div>
        <div class="content">
          <div class="sub-title">
            {{ openItem.title }}
          </div>
          <div class="info">
            {{ openItem.info }}
          </div>
        </div>
      </div>
      <div class="alter">
        本服务当前可免费开通，点击下方开通按钮即可获取服务头衔
      </div>
      <div class="alter-gray">
        需缴费用：<span class="light">0</span> 元
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { getServiceInfo, updateServiceInfo } from './service';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';

const mark = ref<Record<string, string | number>>({});
const serverType = ref();

const fetchServiceData = async () => {
  const res = await getServiceInfo();
  mark.value = res.data.mark;
  serverType.value = res.data.server_type;
};

fetchServiceData();

const isOpenFn = (key: string) => Number(mark.value[key]) === 1;
interface ServiceItemInterface {
  key: string;
  title: string;
  info: string;
  tip: ((num?: number) => string) | string;
  src: string;
}
interface ServiceListInterface {
  title: string;
  items: ServiceItemInterface[];
}
const serviceList: ServiceListInterface[] = [
  {
    title: '服务列表',
    items: [
      {
        key: 'guarantee',
        title: '质保金认证',
        info:
          '个人东家发布产品须缴纳99元质保金，是对服务质量的保障。质保金最低门槛为99元，未完成质保金认证的个人东家，发布的租赁商品无法通过审核。',
        tip: (num = 99) => `保证金门槛：${num}元`,
        src: 'https://img1.rrzuji.cn/uploads/scheme/2206/14/s/lt65ocz4h8RLqxukDsk3.png',
      },
      {
        key: 'decorate',
        title: '租户直通车',
        info:
          '十大保障措施，共享租赁经济红利，租赁商委托平台专业美工设计产品商品、店铺装修，获得更多自定义权限，产品上架支付宝，提升租户转化率，租户有保障。',
        tip: () => '详情及费用请咨询 020-28187591转3',
        src: 'https://img1.rrzuji.cn/uploads/scheme/2206/14/s/rez7uQ0ffzlLaMsaiWkF.png',
      },
      {
        key: 'seven',
        title: '7天无忧换机',
        info:
          '租赁商为租户提供7天无理由换机器服务保障，让租户更安心租用设备。开通该服务保障的，必须执行服务承诺，否则受到平台处罚。冻结的资金，商家3个月后可以随时申请退还。',
        tip: (num = 0) => `冻结资金：${num}元`,
        src: 'https://img1.rrzuji.cn/uploads/scheme/2206/14/s/EjAilzPemHZjaHFhNbsL.png',
      },
      {
        key: 'speediness',
        title: '快速维修',
        info:
          '快修服务是租赁商能够提供快速响应服务，30分钟受理，2小时上门，24小时完成维修。开通该服务保障的，必须执行服务承诺，否则受到平台处罚。冻结的资金，商家3个月后可以随时申请退还。',
        tip: (num = 0) => `冻结资金：${num}元`,
        src: 'https://img1.rrzuji.cn/uploads/scheme/2206/14/s/f1znkK9ZB9YUl0pIoZEU.png',
      },
    ],
  },
  {
    title: '保障列表',
    items: [
      {
        key: 'examine',
        title: '已审核',
        info: '租赁商提交认证资料后，经平台审核通过后自动获得。',
        tip: '',
        src: 'https://img1.rrzuji.cn/uploads/scheme/2206/14/s/Iplw01ZQEGvC4yC3KL9U.png',
      },
      {
        key: 'enterprise',
        title: '企业认证',
        info: '租赁商提交认证资料后，经平台审核通过后自动获得。',
        tip: '',
        src: 'https://img1.rrzuji.cn/uploads/scheme/2206/14/s/H0qWRLRpZvmTEFECSO3o.png',
      },
    ],
  },
];

const openItem = ref<ServiceItemInterface>({
  key: '',
  title: '',
  info: '',
  tip: '',
  src: '',
});
const openVisible = ref<boolean>(false);
const handleOpenService = (item: ServiceItemInterface) => {
  openItem.value = item;
  openVisible.value = true;
};

const { query } = useRoute();
const { origin } = query;

const isGuarantee = (str: string) => {
  return str === 'guarantee';
};

// 租户直通车
const isDecorate = (str: string) => {
  return str === 'decorate';
};

const toDeposit = (subUrl: string) => {
  const url = `${origin}/deposit/${subUrl}`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};

const handleSubmitOpen = async () => {
  const key = openItem.value.key;
  if (isGuarantee(key)) {
    if (isOpenFn(key)) {
      toDeposit('recharge');
    } else {
      toDeposit('index');
    }
    return;
  }
  const res = await updateServiceInfo({
    type: key,
  });
  message.success(res.message);
  openVisible.value = false;
  fetchServiceData();
};
</script>
<style scoped lang="less">
.page {
  min-height: calc(100vh - 64px);
  padding: 24px 24px 200px 24px;
  background: #fff;
}

.title {
  width: 100%;
  padding: 16px 24px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 14px;
  background: #f0f7ff;
  border-radius: 4px;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid rgba(6, 21, 51, 0.06);
  &:last-child {
    border-bottom: none;
  }
  .img {
    width: 88px;
    height: 88px;
    overflow: hidden;
    border-radius: 8px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .content {
    flex: 1;
    margin-left: 16px;
    .sub-title {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
    }
    .info {
      margin-top: 8px;
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 14px;
    }
    .tip {
      margin-top: 8px;
      color: #3777ff;
      font-weight: 500;
      font-size: 14px;
    }
  }
  .button {
    margin-left: 166px;
  }
}
.open-item {
  align-items: flex-start;
  justify-content: space-between;
  padding: 0;
  border-bottom: none;
  .img {
    width: 98px;
    height: 98px;
    overflow: hidden;
    border-radius: 8px;
  }
}
.alter {
  width: 100%;
  margin-top: 24px;
  padding: 12px 16px;
  color: #ff4d4f;
  font-weight: 400;
  font-size: 14px;
  background: #fff2f0;
  border-radius: 4px;
}

.alter-gray {
  width: 100%;
  margin-top: 24px;
  padding: 12px 16px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  background: #f5f7fa;
  border-radius: 4px;
  .light {
    color: #3777ff;
  }
}
</style>
