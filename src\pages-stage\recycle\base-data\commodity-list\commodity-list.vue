<template>
  <layout-shops-page :navs="['趣回收', '基础数据', '商品列表']">
    <div class="container">
      <div class="search-bar">
        <a-form
          ref="formRef"
          layout="inline"
          :model="formState"
          style="width: 100%"
        >
          <a-row style="display: flex; align-items: center; justify-content: space-between; width: 100%">
            <a-col style="display: flex; align-items: center">
              <a-form-item
                label="商品名称"
                name="category_id"
              >
                <ProdutionCascader
                  ref="produtionCascader"
                  v-model:modelValue="formState.category_id"
                />
              </a-form-item>
              <a-form-item>
                <a-button
                  style="border-radius: 4px"
                  type="primary"
                  @click="onHandleControl().search"
                >
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索
                </a-button>
                <a-button
                  style="margin-left: 10px; border-radius: 4px"
                  @click="onHandleControl().reset"
                >
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-form-item>
            </a-col>
            <a-col style="display: flex; justify-content: end">
              <a-button
                type="primary"
                @click="onHandleControl().edit('create')"
              >
                添加商品
                <template #icon>
                  <plus-outlined />
                </template>
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="main">
        <a-tabs
          v-model:activeKey="formState.status"
          @change="onLoadList"
        >
          <a-tab-pane
            key=""
            tab="全部商品"
          />
          <a-tab-pane
            key="0"
            tab="销售中商品"
          />
          <a-tab-pane
            key="1"
            tab="下架商品"
          />
        </a-tabs>

        <a-table
          class="list-table"
          :columns="columns"
          :data-source="tableList"
          :loading="isLoading"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.count,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total: number) => `共 ${total} 条数据 第${pagination.current}页`
          }"
          :scroll="{ x: 1260 }"
          @change="onChangePageSize"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.name === 'image'">
              <a-image
                :src="record.image"
                :width="80"
              />
              <span style="padding-left: 12px">{{ record.name }}</span>
            </template>

            <template v-if="column.name === 'action'">
              <div style="display: flex; justify-content: center">
                <a-button
                  type="link"
                  @click="onHandleControl(record.id).edit(Number(record.status) === 0 ? 'check' : 'update')"
                >
                  {{ Number(record.status) === 0 ? '查看' : '编辑' }}
                </a-button>
                <a-button
                  v-if="record.level !== '二级'"
                  :danger="Number(record.status) === 0"
                  type="link"
                  @click="onHandleControl(record.id).changeStatus(record.status, record.name)"
                >
                  {{ Number(record.status) !== 0 ? '上架' : '下架' }}
                </a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <affirm-modal
      v-if="affirmModalVisible"
      :id="currentId"
      v-model:visible="affirmModalVisible"
      :current-commodity="currentCommodity"
      @on-load-list="onLoadList"
    />
    <edit-commodity-modal
      v-if="editModalVisible"
      :id="currentId"
      v-model:visible="editModalVisible"
      :action-type="actionType"
      @on-load-list="handleUpdate"
    />
  </layout-shops-page>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { FormInstance } from 'ant-design-vue';
import { getTableList } from './service';
import useTableList from '../../common/use-table-list';
import useBoolean from '../../common/use-boolean';
import AffirmModal from './components/affirm-modal.vue';
import EditCommodityModal from './components/edit-commodity-modal.vue';
import ProdutionCascader from './components/prodution-cascader.vue';

type actionType = 'create' | 'update' | 'check';

const columns: any[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '商品信息',
    dataIndex: 'image',
    name: 'image',
    width: 180,
  },
  {
    title: '商品分类',
    dataIndex: 'categoryName',
    width: 80,
  },
  {
    title: '发布时间',
    dataIndex: 'createdAt',
    width: 60,
  },
  {
    title: '操作',
    name: 'action',
    fixed: 'right',
    align: 'center',
    width: 90,
  },
];

const [affirmModalVisible, { setTrue: showAffirmModal }] = useBoolean();
const [editModalVisible, { setTrue: showEditModal }] = useBoolean();

const currentId = ref('');
const currentCommodity = ref({
  status: false,
  name: '',
});
const produtionCascader = ref<any>(null);

const actionType = ref<actionType>('create');

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  category_id: [], // 商品名称
  status: '',
});

// 分页器
let pagination = reactive({
  current: 1,
  pageSize: 10,
  count: 0,
});

const { onLoadList, tableList, isLoading } = useTableList({
  getTableList,
  payload: formState.value,
  pagination,
  isAutoInit: true,
});

const onChangePageSize = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  // 再次发起网络请求
  onLoadList();
};
const handleUpdate = () => {
  produtionCascader.value?.handleGetOptions();
  onLoadList();
};
const onHandleControl = (id?: string) => ({
  search() {
    pagination.current = 1;
    onLoadList();
  },
  reset() {
    // 1.重置所有字段
    formRef.value?.resetFields();
    // 2. 初始化页码
    pagination.current = 1;
    pagination.pageSize = 10;
    // 3. 重新获取table数据
    onLoadList();
  },
  edit(type?: actionType) {
    currentId.value = id as string;
    actionType.value = type as actionType;
    showEditModal();
  },
  changeStatus(status: string, name: string) {
    currentId.value = id as string;
    currentCommodity.value = {
      status: Number(status) === 0,
      name: name,
    };
    showAffirmModal();
  },
});
</script>
<style scoped lang="less">
@import '../../common/base.less';
.list-table {
  padding-top: 14px;
}
</style>
