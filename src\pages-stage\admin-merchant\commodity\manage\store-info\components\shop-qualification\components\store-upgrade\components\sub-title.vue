<template>
  <div class="sub-title">
    <div
      v-if="name"
      class="name"
    >
      {{ name }}
    </div>
    <div
      v-if="desc"
      class="desc"
    >
      {{ desc }}
    </div>
    <slot name="suffix" />
  </div>
</template>

<script setup lang="ts">
defineProps<{
  name?: string;
  desc?: string;
}>();
</script>

<style lang="less" scoped>
.sub-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.name {
  width: 160px;
  padding-right: 8px;
  color: #061533d9;
  font-weight: bold;
  text-align: right;
}

.desc {
  margin-right: 8px;
}
</style>
