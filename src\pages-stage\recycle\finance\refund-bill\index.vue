<template>
  <layout-admin-page
    :navs="['趣回收', '财务管理', '收款退款单']"
    title="收款退款单"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="handleAddRefundBill"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        新增退款单
      </a-button>
    </template>
    <div class="form-wrap">
      <form-create
        ref="searchFormRef"
        v-model:value="searchParams"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="getTableList('search')"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleReset"
          >
            重置
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleExport"
          >
            <template #icon>
              <DownloadOutlined />
            </template>
            导出
          </a-button>
        </template>
      </form-create>
      <a-table
        class="bottom-fix-table table-list"
        :columns="refundBillColumns"
        :data-source="list"
        :loading="listLoading"
        :pagination="false"
        row-key="refund_order_id"
        :row-selection="rowSelection"
        :scroll="{ x: '100%' }"
        :sticky="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'refund_order_id'">
            <span
              class="order-link"
              @click="toRefundDetail(record.refund_order_id)"
            >
              {{ record.refund_order_id }}
            </span>
          </template>
          <template v-if="column.key === 'status'">
            <div class="status-block">
              <div
                class="point"
                :style="{ background: record.status === '1' ? '#00C8BE' : '#FAAD14' }"
              />
              {{ record.status_label }}
            </div>
          </template>
          <template v-if="column.key === 'pay_account_label'">
            <div>{{ record.pay_account_label.join(',') }}</div>
          </template>
          <template v-if="column.key === 'pay_type_label'">
            <div>{{ record.pay_type_label.join(',') }}</div>
          </template>
        </template>
      </a-table>
      <div class="table-pagination">
        <div class="all-checked">
          <a-checkbox
            v-model:checked="checkAllState.checkAll"
            :indeterminate="checkAllState.indeterminate"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
          <div class="check-num">
            已选 <span class="num">{{ checkAllState.checkedList.length }}</span> 条
          </div>
          <a-button
            :disabled="checkAllState.checkedList.length === 0"
            :loading="applyLoading"
            style="margin-left: 12px"
            type="primary"
            @click="handleApply"
          >
            审核
          </a-button>
        </div>
        <a-pagination
          v-model:current="page.current"
          :default-page-size="10"
          :page-size="page.page_size"
          :page-size-options="[10, 30]"
          show-quick-jumper
          :total="page.total"
          @change="tableChange(page)"
        />
      </div>
    </div>
  </layout-admin-page>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useTable } from '@/hook/component/use-table';
import useExportModalConfirm from '@/pages-stage/recycle/finance/self-order-pay/use-export-modal-confirm';
import type { ISearchParams } from './data';
import { searchFormGroup, refundBillColumns } from './config';
import { getRefundList, checkRefundOrder, exportRefundOrder } from './service';
import useCheckAll from './composables/use-check-all';

const router = useRouter();
const route = useRoute();

// 搜索参数
const searchParams = ref<Partial<ISearchParams>>({
  refund_order_id: undefined,
  server_name: undefined,
  server_id: undefined,
});
const searchFormRef = ref();
const applyLoading = ref(false);

const { list, listLoading, page, getTableList, tableChange } = useTable<any, Partial<ISearchParams>>({
  api: getRefundList,
  searchForm: searchParams.value,
  totalKey: 'meta.pagination.total',
  formatHandle: res => {
    return res.data;
  },
  pagination: {
    showTotal: (): string => {
      let { total, page_size } = page;
      let total_page = Math.ceil((total as number) / (page_size as number));
      return `共${page.total}条记录  第 ${page.current} / ${total_page} 页`;
    },
  },
});

const { checkAllState, onCheckAllChange, rowSelection } = useCheckAll(list);
const domain = route.query.origin || window.location.origin;
//导出
const handleExport = async () => {
  const { down: downOrder } = useExportModalConfirm(
    async () => {
      const res = await exportRefundOrder();
      return res;
    },
    `${domain}/super/async-export/index`,
    {
      title: '退账单导出',
      content: '请确认是否导出退收账单',
    },
  );
  downOrder();
};

const handleReset = () => {
  searchFormRef.value?.getFormRef().resetFields();
  getTableList('search');
};

const handleAddRefundBill = () => {
  router.push({ path: '/recycle/finance/refund-bill-detail', query: { type: 'create' } });
};

const toRefundDetail = (id: string) => {
  router.push({ path: '/recycle/finance/refund-bill-detail', query: { id } });
};

const handleApply = async () => {
  try {
    applyLoading.value = true;
    const params = checkAllState.checkedList.map((item: any) => item.refund_order_id);
    await checkRefundOrder({ refund_order_ids: params });
    message.success('审核成功');
    getTableList('search');
    checkAllState.checkedList = [];
  } finally {
    applyLoading.value = false;
  }
};

onMounted(() => {
  getTableList('search');
});
</script>
<style scoped lang="less">
.form-wrap {
  padding: 0 24px 24px;
  background-color: #ffff;
}

.table-list {
  padding-top: 24px;
}
.order-link {
  color: #3777ff;
  cursor: pointer;
}

.table-pagination {
  position: fixed;
  right: 16px;
  bottom: 0;
  left: 16px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: #fff;
  .all-checked {
    display: flex;
    align-items: center;
  }
  .check-num {
    margin-left: 28px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
  }
  .num {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
  }
}

.status-block {
  display: flex;
  align-items: center;
  .point {
    width: 8px;
    height: 8px;
    margin-right: 10px;
    border-radius: 8px;
  }
}
</style>
