export interface ISearchParams {
  source_type: string | null;
  rc_order_id: string | null;
  device_code: string | null;
}

interface Ilorderprice {
  order_id: string;
  total_money: string;
  quotation_money: string;
  inspect_money: string;
  accessory_money: string;
  lease_money: string;
}

export interface IDataSource {
  id: string | null;
  created_at: string | null;
  sku_info: string | null;
  goods_image: string | null;
  order_id: string | null;
  rc_order_id: string | null;
  device_code?: any;
  purchased_price: string | null;
  trade_no: string | null;
  sell_type: string | null;
  sell_type_text: string | null;
  server_name: string | null;
  zl_order_id: string | null;
  zl_order_price?: Ilorderprice | null;
  dealer_price: string | null;
  status_text: string | null;
  show_dealer_mark: number | null;
  order_replace_buy_protocol: string | null;
  order_replace_buy_protocol_time: string | null;
  order_replace_send_protocol: string | null;
  order_replace_send_protocol_time: string | null;
}
