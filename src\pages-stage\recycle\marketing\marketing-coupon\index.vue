<template>
  <layout-admin-page
    :navs="['趣回收', '营销管理', '回收营销券']"
    title="回收营销券"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="onAdd"
      >
        <template #icon>
          <plus-outlined />
        </template>
        添加营销券
      </a-button>
    </template>
    <div class="main">
      <a-form
        ref="formRef"
        layout="inline"
        :model="searchForm"
        style="width: 100%; margin-bottom: 24px"
      >
        <a-form-item
          class="form-item-block"
          label="商品ID"
          name="goods_id"
        >
          <a-input
            v-model:value="searchForm.goods_id"
            placeholder="请输入商品id"
            style="width: 198px"
            @press-enter="onSearch"
          />
        </a-form-item>
        <a-form-item
          class="form-item-block"
          label="券ID"
          name="coupon_market_id"
        >
          <a-input
            v-model:value="searchForm.coupon_market_id"
            placeholder="请输入"
            style="width: 212px"
            @press-enter="onSearch"
          />
        </a-form-item>
        <a-form-item class="form-item-block">
          <a-button
            style="margin-right: 8px"
            type="primary"
            @click="onSearch"
          >
            查询
          </a-button>
          <a-button @click="onReset">
            重置
          </a-button>
        </a-form-item>
      </a-form>
      <a-table
        class="bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: '1118px' }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'info'">
            <div class="form-item">
              <span class="form-item__label">券名称：</span>
              <span class="form-item__value">{{ record.name }}</span>
            </div>
            <br>
            <div class="form-item">
              <span class="form-item__label">活动名称：</span>
              <span class="form-item__value">{{ record.activity_name }}</span>
            </div>
            <br>
            <div class="form-item time-text">
              <span class="form-item__label">创建时间：</span>
              <span class="form-item__value">{{ record.created_at }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-switch
              v-model:checked="record.statusValue"
              checked-children="启用"
              un-checked-children="停用"
              @change="changeStatus(record, index)"
            />
          </template>
          <template v-if="column.dataIndex === 'goods_info'">
            <a-tag
              v-for="(item, infoIndex) in record.goods_info || []"
              :key="infoIndex"
              style="margin: 0 8px 8px 0"
            >
              {{ item }}
            </a-tag>
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <a-button
              class="handle-btn primary"
              type="link"
              @click="onEdit(record)"
            >
              编辑
            </a-button>
            <a-popconfirm
              cancel-text="取消"
              ok-text="确定"
              title="确定删除吗?"
              @confirm="onDownCoupon(record)"
            >
              <a-button
                class="handle-btn"
                danger
                type="text"
              >
                删除
              </a-button>
            </a-popconfirm>

            <a-button
              class="handle-btn primary"
              type="link"
              @click="onShowRecordModal(record)"
            >
              修改记录
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
  <CouponFormModal
    ref="couponFormModal"
    @init="getTableList"
  />
  <HandleRecordModal ref="handleRecordModal" />
</template>

<script lang="ts" setup>
import { ref, Ref, reactive } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useTable } from '@/hook/component/use-table';
import { columns } from './config';
import { IDataInfo } from './data';
import { changeItemStatus, delItem } from './service';
import CouponFormModal from './components/coupon-form-modal.vue';
import HandleRecordModal from './components/handle-record.modal.vue';

const formRef = ref<any>(null);
const couponFormModal = ref<any>(null);
const handleRecordModal = ref<any>(null);

const searchForm = reactive({
  goods_id: '',
  coupon_market_id: '',
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  url: '/super/recycle/coupon-market/list',
  searchForm,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatHandle: (res: { data: { list: IDataInfo[] } }) => {
    const data = res.data || {};
    const list = data.list || [];
    list.forEach((item: IDataInfo) => {
      item.goods_info = item.goods_id_text.split(',');
      item.statusValue = !!(item.status === 1);
      item.goods_id = (item.goods_id as string).split(',');
    });
    return list;
  },
  pagination: {
    showTotal: (): string => {
      let totalPages = Math.ceil((page.total ?? 0) / (page.pageSize ?? 0));
      return `共 ${page.total} 条记录   第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const onSearch = () => {
  getTableList('search');
};

const onReset = () => {
  formRef.value.resetFields();
  onSearch();
};

const onAdd = () => {
  couponFormModal.value.onShow();
};
const onEdit = (item: IDataInfo) => {
  couponFormModal.value.onShow(item);
};

const changeStatus = async (item: IDataInfo, index: number) => {
  (dataSource as Ref).value[index].statusValue = !(dataSource as Ref).value[index].statusValue;
  const { id } = item;
  const params = {
    coupon_market_id: id,
  };
  await changeItemStatus(params);
  getTableList();
};

const onShowRecordModal = (item: IDataInfo) => {
  const { id } = item;
  handleRecordModal.value?.onShow(id);
};

const onDownCoupon = async (item: IDataInfo) => {
  const { id } = item;
  await delItem({ coupon_market_id: id });
  getTableList();
};

getTableList();
</script>

<style lang="less" scoped>
.main {
  padding: 0 24px;
  .form-item-block {
    margin-right: 24px;
  }
  .form-item {
    display: inline-flex;
    flex-wrap: wrap;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    &.time-text {
      color: rgba(0, 0, 0, 0.65);
    }
  }
  .handle-btn {
    margin-right: 16px;
    padding: 0;
    &:nth-last-child(1) {
      margin-right: 0;
    }
    &.primary {
      color: #3777ff;
    }
  }
}
</style>
