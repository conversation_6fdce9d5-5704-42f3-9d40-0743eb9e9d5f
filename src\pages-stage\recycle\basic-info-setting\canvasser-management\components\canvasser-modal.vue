<template>
  <a-modal
    :destroy-on-close="true"
    :title="title"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-form
      ref="formRef"
      :hide-required-mark="isTypeLook"
      :layout="isTypeLook ? 'horizontal' : 'vertical'"
      :model="formState"
      :rules="rules"
      style="width: 100%"
    >
      <a-form-item
        label="兜底商名称"
        name="name"
      >
        <a-input
          v-model:value="formState.name"
          :bordered="!isTypeLook"
          :disabled="isTypeLook"
          :maxlength="15"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item
        label="运营联系人"
        name="contact_infos_name"
      >
        <template v-if="isTypeLook">
          <sensitive-field
            field="contact_infos_name"
            field-type="2"
            id-key="id"
            :is-super-v2="true"
            :row="formState"
            :type="691"
          />
        </template>

        <a-input
          v-else
          v-model:value="formState.contact_infos_name"
          :bordered="!isTypeLook"
          :disabled="isTypeLook"
          :maxlength="15"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item
        label="联系电话"
        name="phone"
      >
        <template v-if="isTypeLook">
          <sensitive-field
            field="phone"
            field-type="50"
            id-key="id"
            :is-super-v2="true"
            :row="formState"
            :type="691"
          />
        </template>

        <a-input
          v-else
          v-model:value="formState.phone"
          :bordered="!isTypeLook"
          :disabled="isTypeLook"
          :maxlength="40"
          placeholder="请选择"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <div v-if="isTypeLook">
        <a-button
          type="primary"
          @click="onCancel"
        >
          我知道了
        </a-button>
      </div>
      <div v-else>
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button
          :disabled="btnDisable"
          type="primary"
          @click="handleOk"
        >
          确定
        </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';

import { useSensitiveData } from '@/composables/use-sensitive-data';

import { IFormParams } from '../data';
import { createCanvasser, detailCanvasser, editCanvasser } from '../service';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  actionType: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
});

const { queryData } = useSensitiveData({
  type: 691,
  id: props.id,
  isSuperV2: true,
});

const emits = defineEmits(['update:visible', 'getTableList']);

const TITLE_MAP = {
  create: '添加兜底商',
  update: '修改兜底商',
  look: '查看兜底商信息',
};

const title = computed(() => {
  return TITLE_MAP[props.actionType] || '添加兜底商';
});

const isTypeLook = computed(() => {
  return props.actionType === 'look';
});

const isTypeEdit = computed(() => {
  return props.actionType === 'update';
});

const formState = ref<IFormParams>({
  name: null,
  contact_infos_name: null,
  phone: null,
});

//检验
const rules = {
  name: [{ required: true, message: '请输入兜底商名称' }],
  contact_infos_name: [{ required: true, message: '请输入运营联系人' }],
  phone: [{ required: true, message: '请输入联系电话' }],
};

const btnDisable = ref(true);

const onCancel = () => emits('update:visible', false);

const addCanvasser = async () => {
  const res = await createCanvasser(formState.value);
  message.success(res.message || '添加成功');
};

const getDetail = async () => {
  const res = await detailCanvasser({ id: props.id });
  formState.value = { ...res.data };
};

const updateDetail = async () => {
  const res = await editCanvasser({ id: props.id, ...formState.value });
  message.success(res.message || '操作成功');
};

const formRef = ref<FormInstance>();
const handleOk = async () => {
  await formRef.value?.validateFields();
  if (props.actionType === 'create') {
    await addCanvasser();
  }
  if (props.actionType === 'update') {
    await updateDetail();
  }
  onCancel();
  emits('getTableList');
};

onMounted(() => {
  btnDisable.value = false;
  if (props.actionType !== 'create') {
    getDetail();
    if (isTypeEdit.value) {
      for (const key in formState.value) {
        if (['phone', 'contact_infos_name'].includes(key)) {
          queryData(formState.value, key === 'phone' ? 50 : 2, key, false, res => {
            if (typeof res === 'string') {
              formState.value[key] = res;
              btnDisable.value = false;
            } else {
              btnDisable.value = true;
            }
          });
        }
      }
    }
  }
});
</script>
<style scoped lang="less">
:deep(.ant-input-disabled) {
  color: rgba(6, 21, 51, 0.85);
  background-color: #fff;
}
</style>
