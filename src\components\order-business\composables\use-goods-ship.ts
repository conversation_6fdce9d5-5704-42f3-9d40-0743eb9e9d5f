import { Ref } from 'vue';

import { operateModal } from '@/components/order-business/operate-modals';
import type { IButtonList } from '@/components/order-business/typing';
import { useUnityModal } from '@/components/unity-modal';

export function useGoodsShip(orderId?: string, buttonList: Ref<IButtonList[] | undefined>, serverId: number) {
  const { onToggleComponent } = useUnityModal();

  /** 打开换货申请单 */
  function openChangeGoodsModal() {
    const buttonInfo = buttonList.value?.find(item => item.button === operateModal.changeGoods.key);
    onToggleComponent(operateModal.changeGoods, {
      orderId: orderId,
      exchangeApplyBtn: Boolean(buttonList.value?.find(item => item.button === operateModal.exchangeApply.key)),
      extraData: buttonInfo?.extra ? JSON.parse(buttonInfo?.extra) : undefined,
      onShowExchangeApplyModal: openExchangeApplyModal,
    });
  }

  /** 打开去发货 */
  function openGoShipModal() {
    const buttonInfo = buttonList.value?.find(item => item.button === operateModal.goShip.key);
    onToggleComponent(operateModal.goShip, {
      orderId: orderId,
      serverId: serverId,
      extraData: buttonInfo?.extra ? JSON.parse(buttonInfo?.extra) : undefined,
      onShowExchangeApplyModal: openExchangeApplyModal,
    });
  }

  /** 打开重新换货申请 */
  function openExchangeApplyModal() {
    const buttonInfo = buttonList.value?.find(item => item.button === operateModal.exchangeApply.key);
    onToggleComponent(operateModal.exchangeApply, {
      orderId: orderId,
      extraData: buttonInfo?.extra ? JSON.parse(buttonInfo?.extra) : undefined,
      onShowChangeGoodsModal: openChangeGoodsModal,
    });
  }

  /** 打开去发货-品牌门店订单 */
  function openGoShipBrandModal() {
    const buttonInfo = buttonList.value?.find(item => item.button === operateModal.goShip.key);
    onToggleComponent(operateModal.goShipBrand, {
      orderId: orderId,
      extraData: buttonInfo?.extra ? JSON.parse(buttonInfo?.extra) : undefined,
    });
  }

  /** 打开发货拦截 */
  function openDeliveryInterceptModal(extraData = undefined) {
    const modalConfig = {
      orderId: orderId,
      title: '发货拦截',
    };
    if (extraData) {
      modalConfig.extraData = extraData;
    } else {
      const buttonInfo = buttonList.value?.find(item => item.button === operateModal.deliveryIntercept.key);
      modalConfig.extraData = buttonInfo?.extra
        ? JSON.parse(buttonInfo.extra.replaceAll('\n', '\\n').replaceAll('\r', '\\r'))
        : undefined;
    }
    onToggleComponent(operateModal.deliveryIntercept, modalConfig);
  }

  /** 打开发货拦截记录 */
  function openDeliveryIntRecordModal() {
    const buttonInfo = buttonList.value?.find(item => item.button === operateModal.deliveryInterceptRecord.key);
    const extraData = buttonInfo?.extra
      ? JSON.parse(buttonInfo.extra.replaceAll('\n', '\\n').replaceAll('\r', '\\r'))
      : undefined;
    onToggleComponent(operateModal.deliveryInterceptRecord, {
      orderId: orderId,
      title: '发货拦截申请',
      onShowInterceptModal: () => openDeliveryInterceptModal(extraData),
    });
  }

  /** 打开物流拦截 */
  function openLogisticsInterceptModal(extraData = undefined) {
    const modalConfig = {
      orderId: orderId,
      title: '物流拦截',
    };
    if (extraData) {
      modalConfig.extraData = extraData;
    } else {
      const buttonInfo = buttonList.value?.find(item => item.button === operateModal.logisticsIntercept.key);
      modalConfig.extraData = buttonInfo?.extra
        ? JSON.parse(buttonInfo.extra.replaceAll('\n', '\\n').replaceAll('\r', '\\r'))
        : undefined;
    }
    onToggleComponent(operateModal.deliveryIntercept, modalConfig);
  }

  /** 打开物流拦截记录 */
  function openLogisticsIntRecordModal() {
    const buttonInfo = buttonList.value?.find(item => item.button === operateModal.logisticsInterceptRecord.key);
    const extraData = buttonInfo?.extra
      ? JSON.parse(buttonInfo.extra.replaceAll('\n', '\\n').replaceAll('\r', '\\r'))
      : undefined;
    onToggleComponent(operateModal.logisticsInterceptRecord, {
      orderId: orderId,
      title: '物流拦截申请',
      onShowInterceptModal: () => openLogisticsInterceptModal(extraData),
    });
  }

  /** 打开闪送运输服务详情*/
  function openShanSongModal() {
    const buttonInfo = buttonList.value?.find(item => item.button === operateModal.shanSongServiceDetails.key);
    onToggleComponent(operateModal.shanSongServiceDetails, {
      extraData: buttonInfo?.extra ? JSON.parse(buttonInfo?.extra) : undefined,
      orderId: orderId,
      serverId: serverId,
    });
  }

  return {
    openChangeGoodsModal,
    openGoShipModal,
    openExchangeApplyModal,
    openGoShipBrandModal,
    openDeliveryInterceptModal,
    openDeliveryIntRecordModal,
    openLogisticsInterceptModal,
    openLogisticsIntRecordModal,
    openShanSongModal,
  };
}
