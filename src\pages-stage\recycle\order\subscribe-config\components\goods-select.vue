<template>
  <div class="select-row">
    <slot name="icon" />
    <div class="select-row-right">
      <div class="row-left">
        {{ leftAttr.label }}：
        <a-select
          v-model:value="form[leftField]"
          allow-clear
          :field-names="{
            label: 'name',
            value: 'id',
          }"
          :filter-option="filterOption"
          max-tag-count="responsive"
          :mode="mode"
          :options="leftOptions"
          placeholder="请选择"
          show-search
          :style="`width: ${leftAttr.width}; margin-right: 8px`"
          @change="onLeftChange"
        />
      </div>
      <div class="row-right">
        {{ rightAttr.label }}：
        <a-select
          v-model:value="form[rightField]"
          allow-clear
          :field-names="{
            label: 'name',
            value: 'id',
          }"
          :filter-option="filterOption"
          max-tag-count="responsive"
          :mode="mode"
          :options="isRelevance ? rightOptions : getOptions(form[leftField])"
          placeholder="请选择"
          show-search
          :style="`width: ${rightAttr.width}`"
          @change="onRightChange"
        />
      </div>
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    leftOptions: {
      type: Array,
      default: () => [],
    },
    leftField: {
      type: String,
      default: '',
    },
    isRelevance: {
      type: Boolean,
      default: false,
    },
    rightOptions: {
      type: Array,
      default: () => [],
    },
    rightField: {
      type: String,
      default: '',
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    leftAttr: {
      type: Object,
      default: () => {
        return {
          label: '',
          width: '',
        };
      },
    },
    rightAttr: {
      type: Object,
      default: () => {
        return {
          label: '',
          width: '',
        };
      },
    },
    mode: {
      type: String,
      default: '',
    },
    filterKey: {
      type: String,
      default: 'name',
    },
  },
  emits: ['onSelectChange'],
  data() {
    return {
      contentOptions: [],
      contentLoading: false,
      form: {},
    };
  },
  computed: {
    contentOptionsEmptyText({ form, contentOptions, contentLoading }) {
      const { attr } = form;
      if (!attr) return '请先选择属性';
      if (contentLoading) return '正在获取中...';
      if (!contentLoading && !contentOptions.length) return '暂无数据';
      return '暂无数据';
    },
  },
  watch: {
    value: {
      handler(value) {
        this.form = value || {};
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getOptions(value) {
      const filterList = this.leftOptions.find(item => item.id === value);
      return (filterList && filterList.child) || [];
    },
    onLeftChange(value, options) {
      this.form[this.leftField + 'Cn'] = !value
        ? this.mode === 'multiple'
          ? []
          : ''
        : this.mode === 'multiple'
        ? options.length
          ? options.map(item => item.name)
          : []
        : options.name;
      if (!this.isRelevance) {
        this.form[this.rightField] = this.mode === 'multiple' ? [] : null;
        this.form[this.rightField + 'Cn'] = this.mode === 'multiple' ? [] : null;
      }
      this.$emit('onSelectChange', this.leftField);
    },
    onRightChange(value, options) {
      this.form[this.rightField + 'Cn'] = !value
        ? this.mode === 'multiple'
          ? []
          : ''
        : this.mode === 'multiple'
        ? options.length
          ? options.map(item => item.name)
          : []
        : options.name;
      this.$emit('onSelectChange', this.rightField);
    },
    filterOption(value, option) {
      return option[this.filterKey].toLowerCase().indexOf(value.toLowerCase()) >= 0;
    },
  },
};
</script>

<style lang="less" scoped>
.select-row {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.select-row-right {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  padding: 4px 8px;
  border-radius: 2px;
  &:hover {
    background: rgba(6, 21, 51, 0.04);
    cursor: pointer;
  }
  .row-left {
    margin-right: 16px;
  }
}
:deep(.anticon-close) {
  font-size: 12px !important;
}
</style>
