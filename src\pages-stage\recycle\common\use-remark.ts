import dayjs from 'dayjs';
import { Ref, ref } from 'vue';
import { IAxiosResponse, POST, GET } from '@/services/api';

// 获取列表备注数据
export function getCrmList(params: Record<string, any>): Promise<IAxiosResponse<any>> {
  return POST('/crm/data', params);
}

// 获取备注历史
export function getCrmLog(unionId: string | number): Promise<IAxiosResponse<any>> {
  return GET('/crm/log', { unionId });
}

/**
 * 列表加载后调用loadRemarks(order_ids)，配合crm-remark组件，使用示例：
 * ```html
 * <crm-remark v-bind="crmPropsMap[record.order_id]" />
 * ```
 * @param {string} suffix crm后缀
 * @param {any} props <crm-remark ...props />的其他配置属性（size、layout、push-link、log-link...)
 * @returns {any}
 */
export const useRemarks = function (
  suffix: string,
  props?: any,
): {
  loadRemarks: (ids: string[]) => void;
  crmPropsMap: Ref<Record<string, any>>;
  remarksMap: Ref<Record<string, any>>;
} {
  const remarksMap = ref<Record<string, any>>({});
  const crmPropsMap = ref<Record<string, any>>({});
  /**
   * Description 获取crm列表
   * @param {any} ids:string[]|number[]
   * @returns {any}
   */
  const loadRemarks = (ids: string[] | number[]) => {
    if (!ids || !ids.length) return;
    getCrmList({
      unionSuffix: suffix,
      unionIds: ids,
    }).then(({ data }) => {
      data.forEach((item: any) => {
        const key = item.union_id.replace(suffix, '');
        remarksMap.value[item.union_id] = item;
        crmPropsMap.value[key] = {
          ...props,
          item,
          id: item.union_id,
          onAddSuccess: () => onRemarkAddSuccess(key),
        };
      });
    });
  };
  /**
   * Description 局部刷新crm控件
   * @param {any} key:string
   * @returns {any}
   */
  const onRemarkAddSuccess = (key: string) => {
    const unionId = key + suffix;
    getCrmLog(unionId).then(({ data }) => {
      const { remark, remark_by, created_at, union_id } = data[0];
      const crmItem = {
        union_id,
        last_remark: remark,
        remark_by,
        remark_num: data.length,
        created_at: dayjs(created_at).format('MM月DD hh:mm'),
      };
      remarksMap.value[unionId] = crmItem;
      crmPropsMap.value[key].item = crmItem;
    });
  };

  return {
    loadRemarks,
    remarksMap,
    crmPropsMap,
  };
};

export default useRemarks;
