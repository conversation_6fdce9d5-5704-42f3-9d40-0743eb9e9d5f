<template>
  <layout-admin-page
    :navs="['趣回收', '基础信息设置', '兜底商管理']"
    title="兜底商管理"
    :top-style="{ marginBottom: 0 }"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="onEdit('create')"
      >
        <template #icon>
          <plus-outlined />
        </template>
        添加兜底商
      </a-button>
    </template>

    <div class="container">
      <a-table
        class="table-box bottom-fix-table"
        :columns="listColumns"
        :data-source="list"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: '100%' }"
        :sticky="true"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="['phone', 'contact_infos_name'].includes(column.key)">
            <sensitive-field
              :field="column.key"
              :field-type="column.key === 'phone' ? 50 : 2"
              id-key="id"
              :is-super-v2="true"
              :row="record"
              :type="691"
            />
          </template>

          <template v-if="column.key === 'operation'">
            <div style="display: flex">
              <a-button
                type="link"
                @click="onEdit('look', record.id)"
              >
                查看详情
              </a-button>

              <a-button
                type="link"
                @click="onEdit('update', record.id)"
              >
                修改基础信息
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
  <CanvasserModal
    v-if="canvasserModalVisible"
    :id="currentId"
    v-model:visible="canvasserModalVisible"
    :action-type="actionType"
    @get-table-list="getTableList"
  />
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';

import { useTable } from '@/hook/component/use-table';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';

import CanvasserModal from './components/canvasser-modal.vue';
import { listColumns } from './config';
import { IDataSource } from './data';
import { getCanvasserList } from './service';

const { list, page, listLoading: loading, getTableList, tableChange } = useTable<IDataSource, any>({
  api: getCanvasserList,
  totalKey: 'data.pageData.count',
  formatHandle: (res: any) => {
    return res.data.listData;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

type TActionType = 'create' | 'update' | 'look';

const [canvasserModalVisible, { setTrue: showCanvasserModal }] = useBoolean();

const actionType = ref<TActionType>('create');
const currentId = ref<string>('');

const onEdit = (type: string, id?: string) => {
  actionType.value = type as TActionType;
  if ((type === 'update' || type === 'look') && id !== undefined) {
    currentId.value = id;
  }
  showCanvasserModal();
};

onMounted(() => {
  getTableList();
});
</script>
<style scoped lang="less">
.container {
  padding: 2px 24px 26px 24px;
}
.table-box {
  margin-top: 24px;
}
</style>
