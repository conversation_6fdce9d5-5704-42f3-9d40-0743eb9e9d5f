<template>
  <div class="flex-wrap flex-vertical flex-gap-8">
    <div style="color: #061533d9">
      选择更换货品为：{{ currentSelectModel }}
    </div>
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px">
      <!-- 型号 -->
      <a-select
        v-model:value="classifyOptions.modal"
        :options="classifyOptions.modalOptions"
        placeholder="请选择型号"
        @change="getSkuOptions"
        @select="(_, { label }) => setLabel(classifyOptions, label)"
      />
      <!-- sku -->
      <template
        v-for="item in classifyOptions.attrList"
        :key="item.id"
      >
        <a-select
          v-model:value="item.value"
          :options="item.options"
          :placeholder="`请选择${item.name}`"
          @select="(_, { label }) => setLabel(item, label)"
        />
      </template>
    </div>
    <div>
      <a-button
        :disabled="searchBtnDisabled"
        :loading="stockStatusLoading"
        type="primary"
        @click="onSearchStock"
      >
        确认并查询库存
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, toRef } from 'vue';
import { message } from 'ant-design-vue';

import useSkuOptions from '@/components/order-business/operate-modals/order/go-ship-modal/composables/use-sku-options';

import { searchSkuId } from '../../../../services';

const emit = defineEmits(['update:stockStatusLoading', 'getStock']);
const props = defineProps<{
  skuId?: number | string;
  categoryId?: number | string;
  brandId?: number | string;
  stockStatusLoading: boolean;
}>();

const setLoading = () => emit('update:stockStatusLoading', true);
const doneLoading = () => emit('update:stockStatusLoading', false);
const getStock = (sku_id: string, sku_info?: string) => emit('getStock', sku_id, sku_info);

// 手动搜索选择匹配货品（无sku_id时）
const { classifyOptions, getModelOptions, getSkuOptions } = useSkuOptions(
  toRef(() => String(props.categoryId)),
  toRef(() => String(props.brandId)),
);

onMounted(() => {
  if (!props.skuId) {
    getModelOptions();
  }
});

const setLabel = (item, label) => {
  item.label = label;
};

const currentSelectModel = ref('');

function setCurrentModel(clear?: boolean) {
  currentSelectModel.value = clear
    ? ''
    : classifyOptions.attrList
        ?.reduce(
          (acc: any, curr: any) => {
            curr.label && acc.push(curr.label);
            return acc;
          },
          [classifyOptions.label],
        )
        .join(' ');
}

const searchBtnDisabled = computed(() => {
  return !classifyOptions.modal || classifyOptions.attrList.some(item => !item.value);
});

/**
 * @Description 手动选择下发商品搜索库存
 * @returns {any}
 */
function onSearchStock() {
  if (!classifyOptions.modal) {
    message.warning('请先选择型号!');
    return;
  }

  const params: any = {
    model_id: classifyOptions.modal,
    brand_id: Number(props.brandId),
    attribute: [],
  };

  const attribute_list = classifyOptions.attrList?.reduce(
    (acc: any, curr: any) => {
      if (curr.value) {
        acc.attribute_list.push(curr.value);
      }
      return acc;
    },
    { attribute_list: [] },
  );
  params.attribute.push(attribute_list);

  setLoading();
  // 1. 根据所选的sku信息查找出对应的skuid
  searchSkuId({ ...params })
    .then(({ data }: any) => {
      const { sku_id } = data.sku_list[0];
      if (!sku_id) {
        message.error('未找到当前商品的sku_id');
      }
      setCurrentModel(!sku_id);
      // 2.查询分仓服务
      getStock(sku_id, currentSelectModel.value);
    })
    .finally(doneLoading);
}
</script>
