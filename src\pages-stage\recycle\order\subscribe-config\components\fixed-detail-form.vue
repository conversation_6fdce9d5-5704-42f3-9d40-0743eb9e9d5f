<template>
  <div class="page-main">
    <!-- 查看状态 -->
    <FormRead
      v-if="!isEdit"
      :params="detailData"
    />
    <!-- 编辑状态 -->
    <a-form
      v-else
      ref="form"
      layout="vertical"
      :model="formState"
    >
      <div class="form-block">
        <div class="form-block-title required">
          基础信息
        </div>
        <div
          class="form-block-content m-l-16"
          style="padding-top: 5px"
        >
          <a-form-item
            label="规则名称"
            name="name"
            :rules="[{ required: true }]"
          >
            <a-input
              v-model:value="formState.name"
              :maxlength="10"
              placeholder="请输入规则名称"
              show-count
              style="width: 754px"
            />
          </a-form-item>
          <a-form-item
            label="规则描述"
            name="detail"
            :rules="[{ required: true }]"
          >
            <a-textarea
              v-model:value="formState.detail"
              allow-clear
              :auto-size="{ minRows: 5, maxRows: 5 }"
              :maxlength="120"
              placeholder="请输入规则内容"
              show-count
              style="width: 754px"
            />
          </a-form-item>
        </div>
      </div>
      <div
        class="form-block"
        style="margin-bottom: 16px"
      >
        <div class="form-block-title p-t-13">
          订单维度
        </div>
        <div class="form-block-content">
          <OrderBlock ref="orderBlockRef" />
        </div>
      </div>
      <div class="form-block">
        <div class="form-block-title p-t-13">
          商品维度
        </div>
        <div class="form-block-content">
          <GoodsBlock
            ref="goodsBlockRef"
            :select-manage="selectManage"
          />
        </div>
      </div>
      <div class="form-block">
        <div class="form-block-title">
          应用活动
        </div>
        <div
          class="form-block-content"
          style="padding-left: 16px"
        >
          <div class="item-value">
            <a-select
              v-model:value="activityValue"
              allow-clear
              :filter-option="filterOption"
              max-tag-count="responsive"
              mode="multiple"
              :options="activityOptions"
              placeholder="请选择"
              style="width: 681px; margin-right: 8px"
            />
            <a-button
              type="primary"
              @click="useActivityId"
            >
              应用
            </a-button>
          </div>
          <div
            v-if="activitys.length"
            class="item-read"
          >
            <a-tag
              v-for="(item, index) in activitys"
              :key="item.value"
              closable
              style="margin-top: 8px"
              @close="delActivityId(index)"
            >
              {{ item.label || '--' }}
            </a-tag>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch } from 'vue';
import OrderBlock from './order-block.vue';
import GoodsBlock from './goods-block.vue';
import FormRead from './fixed-detail-form-read.vue';
import useOptions from '../composables/use-options';
import useActivity from '../composables/use-activity';
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const formState = reactive<any>({
  name: '',
  detail: '',
});

const form = ref<any>(null);
const detailData = ref<any>({});

const orderBlockRef = ref<any>('');
const goodsBlockRef = ref<any>('');

watch(
  () => props.isEdit,
  val => {
    if (val) {
      nextTick(() => {
        const orderBlockData = detailData.value?.order_form || [];
        const goodsBlockData = detailData.value?.goods_form || [];
        orderBlockRef.value.setFormData(orderBlockData);
        goodsBlockRef.value.setFormData(goodsBlockData);
      });
    }
  },
);

const { selectManage } = useOptions();

const { activityOptions, activityValue, activitys, useActivityId, delActivityId } = useActivity(selectManage);

const validate = async () => {
  await form.value?.validate();
  const order_form = orderBlockRef.value.getParams();
  const goods_form = goodsBlockRef.value.getParams();

  const params: any = {
    name: formState.name,
    detail: formState.detail,
    order_form,
    goods_form,
    activity: [...activitys.value],
  };
  return params;
};

// 设置表单值。编辑缓存的表单
const setFormValue = (data: any) => {
  detailData.value = data;
  const formStateKeys = Object.keys(formState);
  formStateKeys.forEach(item => {
    formState[item] = data[item];
  });
  activitys.value = data.activity || [];
};

const filterOption = (value: string, option: any) => {
  return option.label.includes(value);
};

defineExpose({
  setFormValue,
  validate,
});
</script>

<style lang="less" scoped>
.page-main {
  flex: 1;
  box-sizing: border-box;
  overflow-y: auto;
  background: #fff;
  .form-block {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 24px;
    border-bottom: 1px solid rgba(6, 21, 51, 0.06);
    &:nth-child(1) {
      padding-bottom: 0;
    }
    &:nth-last-child(1) {
      border-bottom: none;
    }
    .form-block-title {
      position: relative;
      display: inline-flex;
      flex-shrink: 0;
      align-items: center;
      height: 32px;
      margin-right: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      &.required {
        &::after {
          position: absolute;
          top: 6px;
          left: -8px;
          color: #ff4d4f;
          font-size: 12px;
          content: '*';
        }
      }
    }
    .form-block-content {
      flex: 1;
      .item-read {
        box-sizing: border-box;
        width: 754px;
        max-height: 168px;
        margin-top: 16px;
        padding: 0 12px 8px 12px;
        overflow-y: auto;
        border: 1px solid rgba(0, 0, 0, 0.15);
      }
    }
  }
}
.m-l-16 {
  margin-left: 16px;
}
.p-t-13 {
  padding-top: 13px;
}
</style>
