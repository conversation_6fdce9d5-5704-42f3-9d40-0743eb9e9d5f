<template>
  <a-drawer
    v-model:visible="visible"
    class="opening-record"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
    :get-container="false"
    placement="right"
    title="账单详情"
    width="850"
  >
    <a-spin :spinning="loading">
      <div class="bill-details">
        <!-- 账单信息 -->
        <bill-info :sale-bill-detail="saleBillDetail" />
        <!-- 支付信息 -->
        <pay-info
          :loading="loading"
          :pay-info-list="payInfoList"
        />
        <!-- 退款信息 -->
        <refund-info
          :loading="loading"
          :refund-info-list="refundInfoList"
        />
      </div>
    </a-spin>
    <template #footer>
      <div class="footer-block">
        <div class="total-amount">
          <div>本次应收账款</div>
          <div>{{ saleBillDetail?.finish_pay_price }}</div>
        </div>
        <div class="total-amount">
          <div>累计收款</div>
          <div>{{ saleBillDetail?.order_price }}</div>
        </div>
        <div class="footer-btn">
          <div class="total-amount">
            <div>本单未收</div>
            <div>{{ saleBillDetail?.wait_pay_price }}</div>
          </div>
          <a-button
            style="margin-left: 74px"
            type="primary"
            @click="close"
          >
            确定
          </a-button>
        </div>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { useModal } from '@/hook/component/use-modal';
import { getBillPayInfo, getSaleBillDetails } from './service';
import { ref } from 'vue';
import { IPayInfoList, IRefundInfoList, ISaleBillDetail } from './data';
import BillInfo from './components/bill-info/index.vue';
import RefundInfo from './components/refund-info/index.vue';
import PayInfo from './components/pay-info/index.vue';

const props = defineProps({
  rspOrderId: {
    type: String,
    default: '',
  },
});

const saleBillDetail = ref<ISaleBillDetail>();

const payInfoList = ref<IPayInfoList[]>([]);

const refundInfoList = ref<IRefundInfoList[]>([]);

const { open, visible, close, loading } = useModal(undefined, {
  afterOpen: () => {
    loading.value = true;
    Promise.all([
      getSaleBillDetails(props.rspOrderId),
      getBillPayInfo({ rsp_order_id: props.rspOrderId, operate_type: '1' }),
      getBillPayInfo({ rsp_order_id: props.rspOrderId, operate_type: '2' }),
    ])
      .then(([saleBillDetails, payInfo, refundInfo]) => {
        const { data: saleBillDetailsData } = saleBillDetails;
        const {
          data: { list: payInfoLists },
        } = payInfo;
        const {
          data: { list: refundInfoLists },
        } = refundInfo;

        saleBillDetail.value = saleBillDetailsData;
        payInfoList.value = payInfoLists;
        refundInfoList.value = refundInfoLists;
      })
      .finally(() => {
        loading.value = false;
      });
  },
});

defineExpose({
  open,
});
</script>
<style lang="less" scoped>
.footer-block {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 12px 0;
  .total-amount {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #ff4d4f;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
  }
  .footer-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
