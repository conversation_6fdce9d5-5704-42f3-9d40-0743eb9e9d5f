### GenericTable 通用表格组件

`GenericTable` 是基于 `RTable` 组件 结合后端 api 封装的通用表格处理组件，内置了`GenericForm`组件，传入 `formId` (表单 id) 可以自动进行表格数据新增、编辑、删除、导出、查询操作, `formId` 会优先使用后端配置的路由参数，如果没有则使用组件属性

#### 代码演示

前端调试在浏览器地址栏上拼接 `?formId=2` 调试，比如 `http://localhost:3000/test-generic-form?form_id=2`

#### 基础用法

```vue
<template>
  <layout-admin-page title="基础用法">
    <GenericTable />
  </layout-admin-page>
</template>
```

##### 显示部分操作按钮

```vue
<template>
  <layout-admin-page title="显示部分操作按钮">
    <GenericTable :displayBtn="['edit', 'add', 'export']" />
  </layout-admin-page>
</template>
```

##### 不显示表格操作列

```vue
<template>
  <layout-admin-page title="显示部分操作按钮">
    <GenericTable :display-btn-columns="false" />
  </layout-admin-page>
</template>
```

##### 额外的操作按钮组

```vue
<template>
  <layout-admin-page title="显示部分操作按钮">
    <GenericTable :extra-btn-group="extraBtnGroup" :extra-group-handler="extraGroupHandler" />
  </layout-admin-page>
</template>
<script setup lang="ts">
import type { IBtnGroup, IGroupHandlerParams } from 'rrz-web-design';
const extraBtnGroup: IBtnGroup = [
  {
    name: '发布',
    key: 'released',
    type: RButtonType.Primary,
  },
];
const extraGroupHandler = {
  released: (params: IGroupHandlerParams) => {
    console.log(params);
  },
};
</script>
```

#### 组件属性

| 属性名 | 类型 | 默认值 | 必填 | 描述 |
| --- | --- | --- | --- | --- |
| formId | number | - | 否 | 表单 ID `会优先使用后端配置的路由参数，如果没有则使用组件属性` |
| displayBtnColumns | boolean | `true` | 否 | 是否显示操作按钮列 |
| displayBtn | btnType[] | `['edit', 'add', 'view', 'del', 'export']` | 否 | 显示的功能按钮 |
| extraBtnGroup | IBtnGroup[] | {} | 否 | 额外的按钮组,插入在删除按钮之前 |
| extraGroupHandler | IGroupHandler | {} | 否 | 额外的按钮组函数 |
| rTableProps | RTableProps | {} | 否 | `RTable` 组件属性，除了 `api` `columns` `groupHandler` 以外 |
| beforeGenerateColumns | (columns: RTableColumnType[]) => RTableColumnType[] | - | 否 | 生成 `columns` 之前执行，可用于自定义 `columns` |

<!-- ## 组件事件 -->

<!-- | 事件名 | 说明 | 回调参数 |
| ------ | ---- | -------- | -->

#### 组件插槽

| 插槽名        | 说明           | 属性           |
| ------------- | -------------- | -------------- |
| toolBar       | 表单搜索工具栏 | { formState }  |
| tableBodyCell | 表格 bodyCell  | a-table 原属性 |

#### 组件示例导出

| 名称         | 说明                                                                     |
| ------------ | ------------------------------------------------------------------------ |
| addForm      | 显示新增表单弹框                                                         |
| exportTable  | 导出方法                                                                 |
| rTableMethod | `RTable` 组件实例的方法（原来的 `searchForm` 更换为 `getSearchForm()` ） |
