<template>
  <a-modal
    v-model:visible="bindVisible"
    title="租金券使用明细"
    :width="900"
  >
    <RTable
      ref="RTableRef"
      :api="apiSearchTable"
      class="list"
      :columns="columns"
      :search="false"
      :table-props="{
        pagination: false,
        sticky: false,
      }"
    >
      <template #tableBodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'claim_type'">
          <ReimburseTips
            :order-id="orderId"
            :type="record.claim_type"
          />
        </template>
        <template v-if="column.dataIndex === 'desc'">
          {{ record.other_type === 1 ? '平台' : '商家' }}租金券({{ record.id }}):￥{{ record.money }}
        </template>
      </template>
    </RTable>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { RTable, useCompRef } from 'rrz-web-design';

import ReimburseTips from '@/components/order-business/order-table/components/reimburse-tips/index.vue';
import { useVModel } from '@/hook';

import { columns } from './config';
import { getOrderRentCouponList } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();
const bindVisible = useVModel(props, 'visible', emit);

const RTableRef = useCompRef(RTable);
const apiSearchTable = async (params: any) => {
  const res = await getOrderRentCouponList({ order_id: props.orderId, ...params });
  return {
    data: res.data,
  };
};
watch(
  () => bindVisible.value,
  value => {
    value && RTableRef.value?.getTableList();
  },
  { immediate: true },
);
</script>

<style scoped lang="less">
.list {
  :deep(.ant-table-cell) {
    word-break: break-all;
  }
}
</style>
