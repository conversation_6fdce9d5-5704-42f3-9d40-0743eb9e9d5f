export interface ISearch {
  goods_name: string;
  third_platform_price_status: string;
}

export interface IDataSource {
  goods_id: string;
  image: string[];
  auto_evaluate_price_status: string;
  batch_price_between: string;
  market_change: number;
  name: string;
  sele_price_between: string;
  single_price_between: string;
  stock: number;
}

export interface IOpenDrawerParams {
  setting_type: string;
  goods_id: string;
  goods_name: string;
}

// 计算规则表格
export interface IComputedTableDataSource {
  stock_text?: string;
  stock?: number;
  base1: string | null;
  base2: string | null;
  price_type: string | null;
  change_price_type: string | null;
  arithmetic_type: string | null;
  sku_base_price?: number;
  min_evaluate_price?: string | number;
  max_evaluate_price?: string | number;
}

// 确认调价提交
export interface ISubmitSettingPrice {
  goods_id: string;
  goods_type: string;
  setting_type: string;
  auto_evaluate_price_type: string;
  sku_list: IComputedTableDataSource[];
}

// options字典下拉
export interface IOptions<T> {
  label: string;
  value: T;
}

// 计算表格-组件下拉数据对象管理key
export interface ITableSelectManageMap {
  base1_list: IOptions<string>[];
  base2_list: IOptions<string>[];
  price_type: IOptions<string>[];
  change_price_type: IOptions<string>[];
  arithmetic_list: IOptions<string>[];
}

// 计算表格详情接口
export interface ITableDetail extends ITableSelectManageMap {
  auto_evaluate_price_type: string;
  goods_type: string;
  sku_list: IComputedTableDataSource[];
}

// 预估日回收量-sku
export interface IGoodsSkuInfo {
  recycle_num: number;
  sku: string;
}

// 版本下拉
export interface IVersionOption {
  created_at: string;
  created_by: string;
  data_version_id: string;
  name: string;
}

// 数据图表sku内存，颜色规格
export interface IChartSkuInfo {
  memory: string;
  colorList: IOptions<string>[];
}

// 一键调价-商品信息
export interface IBatchSettingGoods {
  goods_name: string;
  sku_list: string[];
}
