<template>
  <a-select
    v-bind="attrs"
    :value="bindValue"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { nextTick, ref, useAttrs, watch } from 'vue';
import { message } from 'ant-design-vue';

import { IFactorData } from '../factor-library';

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  value?: string;
  factorData: IFactorData;
}>();

const emit = defineEmits(['update:value', 'change']);

const attrs = useAttrs();

const bindValue = ref<string>();

// 绑定传入的value值
watch(
  () => props.value,
  val => (bindValue.value = val),
  { immediate: true },
);

function handleChange(...args: any[]) {
  const val = args[0];
  const hasSelected = props.factorData.rules?.find(item => item.factor_code === val);
  if (hasSelected) {
    // ASelect组件的bug，选择的内容在点击后就改变了，导致显示的选择和绑定的value并不一致，这里的赋值操作相当于更新同步选择项显示的值
    bindValue.value = val;
    nextTick(() => (bindValue.value = props.value));
    message.warn('该条件已选择');
  } else {
    emit('update:value', val);
    emit('change', ...args);
  }
}
</script>
