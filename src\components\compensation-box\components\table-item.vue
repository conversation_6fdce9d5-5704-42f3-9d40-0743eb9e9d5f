<template>
  <a-table
    :columns="cloumns"
    :data-source="currentData.list"
    :pagination="false"
    row-key="id"
    :row-selection="canRowSelect ? rowSelection : null"
  >
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.dataIndex === 'descAndDegree'">
        {{ record.desc }}<span
          v-if="record.degree"
          class="degree"
        >{{ `（${record.degree}）` }}</span>
      </template>
      <template v-if="column.dataIndex === 'damage_type'">
        {{ getDamageType(record.damage_type) }}
      </template>
      <template v-if="column.dataIndex === 'money'">
        <a-input-number
          v-if="isCanEditUserPay"
          v-model:value="record.money"
          addon-after="元"
          class="input-number"
          :disabled="isFromOrderList"
          :min="Number(record.platform_pay_money)"
          :precision="2"
        />
        <span
          v-else
          class="input-number"
        > {{ record.money || 0 }}元 </span>
      </template>
      <template v-if="column.dataIndex === 'platform_pay_money'">
        <a-input-number
          v-if="isCanEditPlatform"
          v-model:value="record.platform_pay_money"
          addon-after="元"
          class="input-number"
          :max="Number(record.money)"
          :min="0"
          :precision="2"
        />
        <span
          v-else
          class="input-number"
        > {{ record.platform_pay_money || 0 }}元 </span>
      </template>
      <template v-if="column.dataIndex === 'haveObjection'">
        {{ record.haveObjection ? '是' : '否' }}
      </template>
      <template v-if="column.dataIndex === 'user_pay_money'">
        <a-input-number
          v-if="isFromOrderList && isEditStatus"
          v-model:value="record.user_pay_money"
          addon-after="元"
          class="input-number"
          :min="0"
          :precision="2"
          @change="e => onUserPayMoneyChange(e, index)"
        />
        <span
          v-else
          class="input-number"
        > {{ Number(record.user_pay_money || 0).toFixed(2) }}元 </span>
      </template>
    </template>
  </a-table>
  <div
    v-if="!noShowImg"
    class="image-vedeion-box"
  >
    <h4 class="title">
      凭证照片/视频
    </h4>
    <RUpload
      :hide-entry="1"
      template="pictureCard"
      :upload-props="{
        disabled: true,
      }"
      :value="currentData.url"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref } from 'vue';

import floatCalculator from '@/utils/float-calculator';

import { compensationTableCloums, damageTypeMap } from '../config';
import { TItemValue, TItemValueList } from '../data.d';
import {
  isCanEditPlatformSymbol,
  isCanEditUserPaySymbol,
  isEditStatusSymbol,
  isFromOrderListSymbol,
} from '../injection-symbols';
const props = defineProps<{
  detailValue: TItemValue;
  noShowPlatform?: boolean;
  canRowSelect?: boolean;
  keyName: string;
  noShowImg?: boolean;
  haveObjection?: boolean;
}>();
const emits = defineEmits(['update:detailValue']);
const currentData = computed({
  get() {
    return props.detailValue;
  },
  set(val) {
    emits('update:detailValue', val);
  },
});
const isCanEditPlatform = inject(isCanEditPlatformSymbol, ref(false));
const isCanEditUserPay = inject(isCanEditUserPaySymbol, ref(false));
const isFromOrderList = inject(isFromOrderListSymbol, ref(false));
const isEditStatus = inject(isEditStatusSymbol, ref(false));
const cloumns = computed(() => {
  let commonCloumn = compensationTableCloums;
  if (isFromOrderList.value && isEditStatus.value) {
    commonCloumn = [
      ...compensationTableCloums,
      {
        title: '用户赔付金额',
        dataIndex: 'user_pay_money',
        width: 200,
      },
    ];
  }
  if (props.haveObjection) {
    return [
      ...commonCloumn,
      {
        title: '商家是否异议',
        dataIndex: 'haveObjection',
        width: 120,
      },
    ];
  }
  return !props?.noShowPlatform ? commonCloumn : commonCloumn.filter(item => item.dataIndex !== 'platform_pay_money');
});

const selectKeys = ref<string[]>([]);
const selectRows = ref<Record<string, any>[]>([]);
const rowSelection = {
  selectedRowKeys: selectKeys,
  onChange: (selectedRowKeys: string[], selectedRows: TItemValueList[]) => {
    rowSelection.selectedRowKeys.value = selectedRowKeys;
    selectRows.value = selectedRows;
    currentData.value['selectRows'] = selectedRows;
  },
};

function getDamageType(type: string) {
  return damageTypeMap[type];
}

function onUserPayMoneyChange(e: number, index: number) {
  const { platform_pay_money = 0 } = currentData.value.list[index];
  currentData.value.list[index].money = floatCalculator.add(Number(platform_pay_money), e);
}
</script>
<style lang="less" scoped>
.input-number {
  color: #ff4d4f;
}
.image-vedeion-box {
  margin-top: 16px;
  .title {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
