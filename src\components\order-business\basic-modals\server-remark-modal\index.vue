<template>
  <a-modal
    v-model:visible="bindVisible"
    :confirm-loading="loading"
    :mask-closable="false"
    title="备注"
    width="568px"
    @ok="submit"
  >
    <a-form
      ref="refRemark"
      :model="remarkData"
      :rules="rules"
    >
      <a-alert
        closable
        message="订单关闭后，仍然能查看备注在此的文案"
        type="info"
      />
      <a-form-item name="firstType">
        <div class="title-first">
          请选择备注分类
        </div>

        <a-radio-group
          v-model:value="remarkData.firstType"
          style="margin-bottom: -24px"
          @change="firstChange"
        >
          <a-row>
            <a-col
              v-for="item in remarkData.firstList"
              :key="item.value"
            >
              <a-radio
                class="type-radio"
                :value="item.value"
              >
                {{ item.label }}
              </a-radio>
            </a-col>
          </a-row>
        </a-radio-group>
      </a-form-item>

      <div class="gray-box">
        <a-form-item
          v-if="secondTitle"
          name="secondChoice"
          style="margin-bottom: 17px"
        >
          <a-radio-group
            v-model:value="remarkData.secondChoice"
            button-style="solid"
            @change="secondChange"
          >
            <div
              class="flex-wrap"
              style="flex-wrap: wrap"
            >
              <div
                v-for="item in remarkData.secondList"
                :key="item.value"
                style="margin-right: 8px; margin-bottom: 10px"
              >
                <a-radio-button :value="item.value">
                  {{ item.label }}
                </a-radio-button>
              </div>
            </div>
          </a-radio-group>
        </a-form-item>

        <div class="remark-box">
          <span
            v-if="!fullTitle.needInput"
            class="remark-box-title"
          >{{ fullTitle.text[0] }}</span>
          <template v-else>
            <div class="flex-wrap remark-input-box flex-y-center">
              <span>{{ fullTitle.text[0] }}</span>
              <a-input
                v-model:value="remarkData.remarkInput"
                class="remark-input"
                :class="{ 'remark-input-error': remarkData.numberError }"
                :maxlength="10"
                placeholder="请输入"
                style="width: 120px"
                @change="remarkData.numberError = false"
              />
              <span>{{ fullTitle.text[1] }}</span>
            </div>
          </template>
          <a-textarea
            v-model:value="remarkData.remark"
            :bordered="false"
            placeholder="请输入备注"
          />
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import { POST } from '@/services/api';

import { changeRemark, getCreateOrderLog, queryAllData, queryListByGroup, relateWorkOrder } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  remark: {
    type: String,
    default: '',
  },
  orderId: {
    type: String,
    default: '',
  },
  displayValue: {
    type: Boolean,
    default: true,
  },
  isCrmRemark: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();
const rules = {
  firstType: [{ required: true, message: '该项为必填项' }],
  secondChoice: [{ required: true, message: '请选择标准备注' }],
};
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
type TOptionItem = { label: string; value: string };
const remarkData = reactive<{
  firstType?: string;
  remark?: string;
  firstList: TOptionItem[]; // 一级
  secondList: TOptionItem[]; //二级
  secondChoice?: string;
  remarkInput?: string;
  numberError: boolean;
}>({
  numberError: false,
  firstType: '',
  remark: '',
  firstList: [], // 一级
  secondList: [], //二级
  secondChoice: '',
  remarkInput: undefined,
});
watch(
  () => bindVisible.value,
  value => {
    if (!value) {
      refRemark.value?.resetFields();
      remarkData.remark = '';
    } else {
      trySetupRemark();
    }
  },
  {
    immediate: true,
  },
);
const refRemark = ref();

const cacheTags = {};

async function firstChange() {
  const val = remarkData.firstType;
  if (val) {
    if (cacheTags[val]) {
      remarkData.secondList = cacheTags[val];
      return;
    }
    const res = await queryListByGroup({ tag_group_id: val });
    const result = res.data.map(item => {
      return { label: item.content, value: item.id };
    });
    cacheTags[val] = result;
    remarkData.secondList = result;
  }
}

function secondChange() {
  remarkData.remarkInput = undefined;
  remarkData.numberError = false;
}

const secondTitle = computed(() => {
  const item = remarkData.firstList.find(item => item.value === remarkData.firstType);
  return item?.label || '';
});
const fullTitle = computed(() => {
  const first = secondTitle.value ? `【${secondTitle.value}】：` : '';
  const secondeContent = remarkData.secondList.find(item => item.value === remarkData.secondChoice);
  const second = secondeContent?.label ? `${secondeContent?.label}；` : '';
  if (second.includes('X')) {
    const reg = /(.*?)X+(.*)/;
    const [, secondPre, secondAfter] = second.match(reg);
    return {
      needInput: true,
      text: [first + secondPre, secondAfter],
    };
  }
  return {
    text: [first + second],
  };
});

queryAllData().then(res => {
  remarkData.firstList = res.data.map(item => {
    return { label: item.name, value: item.id };
  });
  trySetupRemark();
});

/**
 * 回显备注值
 */
async function trySetupRemark() {
  if (props.displayValue && bindVisible.value) {
    // 根据正则表达式找出匹配的值
    const [firstTypeLabel, secondChoiceLabel, input] = normalizeRemark(props.remark);
    // 备注文本
    remarkData.remark = input || '';
    // 尝试找到第一个选项
    const firstTypeOpt = remarkData.firstList.find(item => item.label === firstTypeLabel);
    if (!firstTypeOpt) {
      return;
    }
    // 赋值第一个选项
    remarkData.firstType = firstTypeOpt?.value;
    await firstChange();
    // 尝试找到第二个选项
    const reg = /(.*?)X+(.*)/;
    let remarkInput: string;
    const secondChoiceOpt = remarkData.secondList.find(item => {
      let label = item.label;
      if (label.indexOf('X') !== -1) {
        // 含有 X 的特殊处理，如果匹配，赋值到 remarkInput
        const [, secondPre, secondAfter] = label.match(reg);
        const temp = secondChoiceLabel.replace(secondPre, '').replace(secondAfter, '');
        const toCompared = secondChoiceLabel.replace(temp, '');
        label = label.replace(/X+/, '');
        return toCompared === label ? ((remarkInput = temp), true) : false;
      } else {
        // 常规匹配
        return item.label === secondChoiceLabel;
      }
    });
    // 赋值第二个选项
    remarkData.secondChoice = secondChoiceOpt?.value;
    // 赋值输入框
    remarkInput && (remarkData.remarkInput = remarkInput);
  }
}

async function submit() {
  await refRemark.value.validate();
  if (fullTitle.value.needInput && !remarkData.remarkInput) {
    remarkData.numberError = true;
    return;
  }
  const { needInput, text } = fullTitle.value;
  const titleText = needInput ? text[0] + remarkData.remarkInput + text[1] : text[0];
  loading.value = true;
  if (props.isCrmRemark) {
    await POST('/crm/push-server', {
      unionId: props.orderId + '_order.all',
      remark: titleText + remarkData.remark,
    });
    getCreateOrderLog({ order_id: props.orderData?.base_info?.order_id });
    relateWorkOrder({ order_id: props.orderId });
  } else {
    await changeRemark({
      order_id: props.orderId,
      remark: titleText + remarkData.remark,
      is_sync_crm: 1,
    });
  }
  bindVisible.value = false;
  message.success('修改成功');

  emit('refresh', props.orderId, ['data', 'all_remark']);
  loading.value = false;
}

function normalizeRemark(remarkText: string | undefined) {
  const matched = remarkText?.match(/^【(.+?)】：(.+?)；(.+?)?$/);
  if (matched) {
    return matched.slice(1);
  } else {
    return [, , remarkText];
  }
}
</script>

<style scoped lang="less">
.input-text {
  margin-bottom: 10px;
}

.title-first {
  padding: 16px 0;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.gray-box {
  width: 520px;
  padding: 12px;
  background: rgba(6, 21, 51, 0.04);
  border-radius: 4px 4px 4px 4px;
}

.qualificate-tag-box {
  padding-bottom: 17px;
}

.qualificate-tag {
  cursor: pointer;
}

.type-radio {
  margin-bottom: 24px;
}

.remark-box {
  padding: 12px;
  background: white;

  .remark-box-title {
    line-height: 2em;
  }

  textarea {
    width: 496px;
    height: 147px;
    padding: 0;
    padding-left: 5px;
    border: none;
    outline: none;
  }
}

.remark-input-box {
  flex-wrap: wrap;
}

.remark-input {
  margin: 0 8px;
}

.remark-input-error {
  border: 1px solid red;
  animation: shake 0.5s;
}

@keyframes shake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-10px);
  }
  50% {
    transform: translateX(10px);
  }
  75% {
    transform: translateX(-10px);
  }
  100% {
    transform: translateX(0);
  }
}
</style>
