export type TAgreeCancelForm = {
  refund_rental: number;
  refund_deposit: number;
  reason_type?: number;
  reason?: number;
  reasonLabel?: string;
  subReasonLabel?: string;
};

export type TAgreeCancelFormConfig = {
  rules: Record<string, any>;
  reasonTypeOptions: Record<
    string,
    {
      id: string;
      title: string;
      child: any[];
    }[]
  >;
  reasonOptions: Record<string, any>;
  validateInfos?: any;
};
