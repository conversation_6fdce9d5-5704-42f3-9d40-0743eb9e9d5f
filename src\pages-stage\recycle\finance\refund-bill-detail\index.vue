<template>
  <layout-admin-page
    :navs="navsList"
    :title="navsList[navsList.length - 1]"
  >
    <template #title-prefix>
      <ArrowLeftOutlined
        style="margin-right: 16px; font-size: 16px"
        @click="router.go(-1)"
      />
    </template>
    <BaseInfo
      ref="baseInfoRef"
      v-model:formValue="baseinfo"
      :coin-type-list="selectOptions.coin_type_list"
    />
    <RefundInfo
      ref="refundInfoRef"
      v-model:value="refundinfo"
      :pay-account-list="selectOptions.pay_account_list"
      :pay-type-list="selectOptions.pay_type_list"
    />
    <SourceInfo
      ref="sourceInfoRef"
      v-model:value="sourceInfo"
      :is-first="isFirst"
      :server-id="baseinfo.server_id"
      @handle-total="handleTotal"
    />
    <Attachments v-model:imageValue="imageArr" />
  </layout-admin-page>
  <div class="footer-block">
    <div class="footer-title">
      退款核销总金额(元)：{{ totalNum || '-' }}
    </div>
    <div
      v-if="hasBtn"
      class="footer-content"
    >
      <a-space>
        <a-button
          :loading="saveLoading"
          @click="handleSave(1)"
        >
          保存
        </a-button>
        <a-button
          :loading="applyLoading"
          type="primary"
          @click="handleSave(2)"
        >
          审核
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick } from 'vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import BaseInfo from './components/base-info.vue';
import RefundInfo from './components/refund-info.vue';
import SourceInfo from './components/source-info.vue';
import Attachments from './components/attachments.vue';
import type { IBaseData, IRefundData, IRefundOptionList } from './data';
import {
  createRefundOrder,
  editRefundOrder,
  getRefundDetail,
  getSelectList,
  createRefundCheck,
  editRefundCheck,
  IVerification,
} from './service';

const router = useRouter();
const route = useRoute();

const navsList = ref(['回收', '财务管理', '退款单', '退款单详情']);
const baseinfo = ref<IBaseData>({
  coin_type: 1,
  refund_order_id: undefined,
  server_id: undefined,
  date: '',
  price: '',
  remark: '',
});
const baseInfoRef = ref();
const isFirst = ref(true);
const hasBtn = ref(true);
const saveLoading = ref(false);
const applyLoading = ref(false);
const refundinfo = ref<IRefundData[]>([
  {
    pay_account: 1,
    pay_type: 1,
    amount: '',
    payment_flow_sn: '',
    remark: '',
  },
  //空行合计
  {},
]);
const refundInfoRef = ref();
const sourceInfo = ref([{}]);
const sourceInfoRef = ref();
const imageArr = ref([]);
const totalNum = ref(0);

const selectOptions = ref<IRefundOptionList>({
  coin_type_list: [],
  pay_type_list: [],
  pay_account_list: [],
});

//获取下拉选项
const handlgetServerList = async () => {
  const { data } = await getSelectList();
  selectOptions.value.coin_type_list = data.coin_type_list ?? [];
  selectOptions.value.pay_type_list = data.pay_type_list ?? [];
  selectOptions.value.pay_account_list = data.pay_account_list ?? [];
};

//编辑时获取详细信息
const handleGetDetail = async () => {
  const { data: detail } = await getRefundDetail({ refund_order_id: route.query.id });
  const {
    coin_type,
    refund_order_id,
    server_id,
    date,
    remark,
    need_refund_price,
    images,
    pay_order_list,
    source_order_list,
    status,
  } = detail;
  //隐藏保存、审核
  if (status === '1') {
    hasBtn.value = false;
  }
  baseinfo.value = {
    coin_type: Number(coin_type),
    refund_order_id,
    server_id,
    date,
    price: need_refund_price,
    remark,
  };
  imageArr.value = images;
  refundinfo.value = [
    ...pay_order_list.map((item: any) => ({
      pay_account: Number(item.pay_account),
      pay_type: Number(item.pay_type),
      amount: item.amount,
      payment_flow_sn: item.payment_flow_sn,
      remark: item.remark,
      id: item.id,
    })),
    //合计空行
    {},
  ];
  sourceInfo.value = [...source_order_list, {}];
  nextTick(() => {
    isFirst.value = false;
    refundInfoRef.value.handleBlur();
    sourceInfoRef.value.handleBlur();
    sourceInfoRef.value.handleTableDom();
    totalNum.value = sourceInfoRef.value.totalAmountObj.pay_price;
  });
};

const init = () => {
  //获取下拉选项
  handlgetServerList();
  if (route.query.type === 'create') {
    isFirst.value = false;
    navsList.value = ['回收', '财务管理', '退款单', '新增退款单'];
  }
  if (route.query.id) {
    handleGetDetail();
  }
};

const refundValidate = () => {
  refundinfo.value.forEach((item: any) => {
    if (!String(item.amount)) {
      refundInfoRef.value.handleError();
      throw message.error('请填写收款金额');
    }
  });
};

const sourceInfoValidate = () => {
  const len = sourceInfo.value.length;
  sourceInfo.value.forEach((item: any, index: number) => {
    if (index !== len - 1 && !item.current_refund_price && item.current_refund_price !== '0.00') {
      sourceInfoRef.value.handleError();
      throw message.error('请填写本次核销金额');
    }
  });
};

/**
 * method  1：创建 2：审核
 */
const handleSave = async (method: number) => {
  method === 1 ? (saveLoading.value = true) : (applyLoading.value = true);
  try {
    try {
      await baseInfoRef.value.baseForm.validate();
    } catch (err) {
      return message.error('请填写基础信息');
    }
    refundValidate();
    if (sourceInfo.value.length === 1) {
      baseInfoRef.value.scrollIntoDom();
      return message.error('请添加源单信息');
    }
    sourceInfoValidate();

    //找到最后一个有id的index并让后面的id自增
    if (route.query.id) {
      const index = refundinfo.value.findLastIndex(item => item.id);
      if (index !== -1) {
        let lastId = refundinfo.value.findLast(item => item.id).id;
        for (let i = index + 1; i < refundinfo.value.length; i++) {
          refundinfo.value[i] = { ...refundinfo.value[i], id: ++lastId };
        }
      }
    }

    const params: IVerification = {
      server_id: baseinfo.value.server_id,
      coin_type: baseinfo.value.coin_type,
      remark: baseinfo.value.remark,
      date: baseinfo.value.date,
      images: imageArr.value,
      pay_order_list: refundinfo.value.filter((_, index) => index !== refundinfo.value.length - 1),
      source_order_list: sourceInfo.value
        .filter((_, index) => index !== sourceInfo.value.length - 1)
        .map((item: any) => ({
          after_sale_no: item.after_sale_no,
          refund_order_id: item.refund_order_id,
          current_refund_price: item.current_refund_price,
          id: item.id,
        })),
    };
    if (baseinfo.value.refund_order_id) {
      params.refund_order_id = baseinfo.value.refund_order_id;
      params.pay_order_list = params.pay_order_list.map(item => ({
        ...item,
        refund_order_id: baseinfo.value.refund_order_id,
      }));
    }
    const createApi = route.query.id ? editRefundOrder : createRefundOrder;
    const checkApi = route.query.id ? editRefundCheck : createRefundCheck;
    const fn = method === 1 ? createApi : checkApi;
    await fn(params);
    message.success(method === 1 ? '保存成功' : '审核成功');
    router.go(-1);
  } finally {
    method === 1 ? (saveLoading.value = false) : (applyLoading.value = false);
  }
};
const handleTotal = (value: number) => {
  totalNum.value = value;
};

onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.search-form {
  margin-bottom: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
}
.list-mian {
  padding: 0 24px 24px;
  background-color: #fff;
}
.operation {
  color: #3777ff;
}
.footer-block {
  position: fixed;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 88px;
  background: #fff;
  box-shadow: 0 -2px 20px 0 rgba(0, 0, 0, 0.05);
  .footer-title {
    position: absolute;
    left: 20px;
    color: #00c8be;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-shadow: 0 -2px 20px rgba(0, 0, 0, 0.05);
  }
}
</style>
