<template>
  <div class="file-upload">
    <a-upload
      v-if="showLockedList"
      v-model:file-list="fileList"
      :accept="accept"
      :capture="null"
      :class="className"
      :disabled="true"
      list-type="picture"
      :multiple="multiple"
      @preview="handlePreview"
    >
      <a-button>
        <UploadOutlined />
        {{ uploadText }}
      </a-button>
    </a-upload>
    <a-upload
      v-if="uploadStyle === 'default'"
      v-model:file-list="extralFileList"
      :accept="accept"
      :before-upload="onBeforeUpload"
      :capture="null"
      :class="[className, showLockedList ? 'extraContainer' : '']"
      :custom-request="customRequest"
      :headers="{ 'X-Requested-With': null }"
      list-type="picture"
      :multiple="multiple"
      @change="handleChange"
      @preview="handlePreviewExtral"
    >
      <a-button>
        <UploadOutlined />
        {{ uploadText }}
      </a-button>
    </a-upload>
  </div>
  <!-- 图片预览 -->
  <image-preview
    v-model:visible="isPreview"
    :imgs="previewImage"
    @update:visible="isPreview = false"
  />
  <!-- 视频预览 -->
  <video-preview
    v-model:visible="isVideoPreview"
    :url="previewVideo"
  />
</template>

<script setup lang="ts">
import { PropType, ref, watch } from 'vue';
import { computed } from 'vue';
import { message, Upload, UploadChangeParam } from 'ant-design-vue';
import type { UploadFile } from 'ant-design-vue/lib/upload/interface';
import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
import { UploadOutlined } from '@ant-design/icons-vue';

import { judgeFileType } from '@/utils/base';
import { TUploadImageType } from '@/utils/oss-helper';
import { uploadToOssCompatibility } from '@/utils/oss-helper';

interface FileItem {
  url?: string;
  status?: string;
  uid?: number;
  name?: string;
}
type UploadStyle = 'default' | 'dragger';
type Quality = 'o' | 'm' | 's';

const props = defineProps({
  // 上传组件的样式，当前可选 default、dragger
  uploadStyle: {
    type: String as PropType<UploadStyle>,
    default: 'default',
  },
  // 传入图片列表，支持双向绑定，列表例子 ['aaa.png', 'bbb.png']
  value: {
    type: Array as PropType<string[]>,
    default: [],
  },
  // 可以上传的文件类型，默认为任意类型
  accept: {
    type: String,
    default: '',
  },
  // 保存的文件夹名
  action: {
    type: String as PropType<TUploadImageType>,
    default: 'site',
  },
  // 可上传的最大文件数
  max: {
    type: Number,
    default: 5,
  },
  // 上传视频的最大时长, 单位为分钟, 默认0表示不做限制
  maxDuration: {
    type: Number,
    default: 0,
  },
  // 类名
  className: {
    type: String,
    default: '',
  },
  // 上传文本
  uploadText: {
    type: String,
    default: '上传图片/视频',
  },
  // 多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 图片质量（三种类型 o、m、s），为空时默认为m
  quality: {
    type: String as PropType<Quality>,
    default: '',
  },
  onError: {
    type: Function,
    default: (res: any) => {
      // 人人组件接口错误是msg
      message.warning(res.msg || '抱歉，上传时发生未知错误，请重试');
    },
  },
});
const emit = defineEmits(['update:value', 'change']);

const fileList = ref<UploadFile[] | FileItem[]>([]);
const fileIsUploading = ref<boolean>(false);
// 获取视频的时长
const getVideoTime = (file: UploadFile): Promise<number> => {
  return new Promise(resolve => {
    const video = document.createElement('video');
    video.src = URL.createObjectURL(file as any);
    video.setAttribute('preload', 'auto');
    video.addEventListener('loadedmetadata', () => {
      const time = video.duration;
      resolve(time);
    });
  });
};
// 文件上传前
const onBeforeUpload = async (file: UploadFile): Promise<string | boolean> => {
  const maxDuration = props.maxDuration;
  // 文件类型
  const fileType = file.type?.split('/')?.[0] || '';
  // 上传的文件格式只能是accept所允许的格式
  if (props.accept && !props.accept.includes(fileType)) {
    message.error('上传的文件格式不正确，请选择其他文件');
    return Upload.LIST_IGNORE;
  }
  // 上传视频的时长不能超过maxDuration限定的时间
  if (fileType === 'video' && maxDuration > 0) {
    const time = await getVideoTime(file);
    if (time / 60 > maxDuration) {
      message.error(`视频时长不能超过${maxDuration}分钟`);
      return Upload.LIST_IGNORE;
    }
  }

  // 上传视频的时长不能超过maxDuration限定的时间
  if (fileType === 'video') {
    const size = file.size! / 1024 / 1024; //最大限制为5M
    if (size > 100) {
      message.error('视频大小不能超过100M');
      return Upload.LIST_IGNORE;
    }
  }
  return true;
};
// 自定义请求
const customRequest = async (options: UploadRequestOption): Promise<void> => {
  const file = options.file as File;
  fileIsUploading.value = true;
  if (file?.type?.includes('image')) {
    uploadImage(options);
  } else {
    uploadOther(options);
  }
};
// 上传其他文件
const uploadOther = async (options: UploadRequestOption): Promise<void> => {
  const file = options.file as File;
  // 文件类型
  const fileType = file.type?.split('/')?.[0] || '';
  const result = await uploadToOssCompatibility({
    files: [file],
    file_type: fileType,
  });
  const { ossServer, pathWithOssServer } = result;
  options.onSuccess &&
    options.onSuccess({
      url: pathWithOssServer[0],
      ossServer: ossServer[0],
      status: 'done',
    });
};

// 上传图片
const uploadImage = async (options: UploadRequestOption): Promise<void> => {
  // 阿里云压缩上传
  const file = options.file as File;
  const result = await uploadToOssCompatibility({
    files: [file],
    file_type: 'image',
  });

  const { ossServer, pathWithOssServer } = result;
  const pathWithOssServerFirst = pathWithOssServer[0];
  options.onSuccess &&
    options.onSuccess({
      domain: '',
      imageUrls: { m: pathWithOssServerFirst, o: pathWithOssServerFirst, s: pathWithOssServerFirst },
      imgOssServer: ossServer[0],
      status: 'done',
      url: pathWithOssServerFirst,
    });
};
const handleChange = ({ file, fileList: fileLists }: UploadChangeParam): void => {
  const list: string[] = [];
  fileLists.forEach(item => {
    if (item.status === 'done') {
      if (props.quality && item.response && item.response.imageUrls) {
        // 有要求图片质量
        list.push(item.response.imageUrls[props.quality]);
      } else if (item.response && item.response.url) {
        list.push(`${item.response.imgOssServer || ''}${item.response.url}`);
      } else if (item.url) {
        list.push(item.url);
      } else {
        // 捕获服务器返回的提示错误
        props.onError(item.response || {});
      }
    }
  });
  if (file.status !== 'uploading') {
    fileIsUploading.value = false;
    emit('update:value', {
      value: list,
      lockedValue: fileList.value.map(item => {
        return item.url;
      }),
    });
    emit(
      'change',
      {
        value: list,
        lockedValue: fileList.value.map(item => {
          return item.url;
        }),
      },
      fileLists,
    );
  }
  if (file.status === 'error') {
    fileIsUploading.value = false;
    message.warning(file.error || '抱歉，上传时发生未知错误，请重试');
  }
};
// 图片预览
const previewImage = ref<(string | undefined)[]>([]);
const isPreview = ref<boolean>(false);
// 视频预览
const previewVideo = ref<string>('');
const isVideoPreview = ref<boolean>(false);
// 获取base64格式图片
const getBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

// 通用的文件预览处理函数
const handleFilePreview = async (
  file: UploadFile,
  fileList: Array<{ url: string }>
): Promise<void> => {
  const url = file.url || '';
  const type = judgeFileType(url);

  if (type === 'img') {
    if (!file.preview) {
      file.preview = file.originFileObj && (await getBase64(file.originFileObj));
    }

    // 过滤掉非图片的文件
    previewImage.value = fileList
      ?.filter(item => judgeFileType(item.url) === 'img')
      ?.map(item => item.url);

    isPreview.value = true;
  } else if (type === 'video') {
    previewVideo.value = url;
    isVideoPreview.value = true;
  }
};

// 处理普通文件列表的预览
const handlePreview = async (file: UploadFile): Promise<void> => {
  await handleFilePreview(file, fileList.value as Array<{ url: string }>);
};

// 处理额外文件列表的预览
const handlePreviewExtral = async (file: UploadFile): Promise<void> => {
  await handleFilePreview(file, extralFileList.value as Array<{ url: string }>);
};

const extralFileList = ref([]);
watch(
  () => props.value,
  (value: string[], oldVal?: string[]) => {
    if (JSON.stringify(value) === JSON.stringify(oldVal)) return;
    //上锁数据
    fileList.value = !value.lockedValue
      ? []
      : value.lockedValue.map((url: string, index: number) => ({
          url,
          status: 'done',
          uid: index,
          name: url,
        }));

    //非上锁数据
    extralFileList.value = !value.value
      ? []
      : value.value.map((url: string, index: number) => ({
          url,
          status: 'done',
          uid: index,
          name: url,
        }));
  },
  { immediate: true },
);
const showLockedList = computed(() => {
  return fileList.value.length;
});
</script>

<style lang="less" scoped>
.file-upload {
  position: relative;
  // max-height: 300px;
  // overflow-y: scroll;
  .extraContainer {
    :deep(.ant-upload-select) {
      position: absolute;
      top: 0;
    }
  }
  :deep(.ant-upload.ant-upload-drag .ant-upload) {
    padding: 0;
  }
  :deep(.ant-upload-drag) {
    width: 384px;
    height: 192px;
    margin-bottom: 8px;
    overflow: hidden;
  }
  :deep(.ant-upload-drag-icon) {
    margin: 43px 0 19px 0 !important;
    svg {
      width: 33px;
      height: 420px;
      color: gray;
    }
  }
  .ant-upload-text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 16px;
  }
  .ant-upload-hint {
    margin-bottom: 12px;
    color: rgba(6, 21, 51, 0.45);
  }
}
</style>
