import { GET, POST } from '@/services/api';

// 核销收款单导出
export function exportList(params: any) {
  return GET('/super/recycle/sale-verification/list-export', params);
}
// 核销收款单审核
export function checkList(params: any) {
  return POST('/super/recycle/sale-verification/check-order-list', params);
}

// 线上收款源单列表
export function getSourceOrderList(params: any) {
  return POST('/super/recycle/sale-verification/list-source-order', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

//创建收款单
export function createReceipt(params: any) {
  return POST('/super/recycle/sale-verification/create-pay', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

//通知商家付款
export function adviseMerchant(params: any) {
  return POST('/super/recycle/sale-verification/send-message-pay', params);
}
