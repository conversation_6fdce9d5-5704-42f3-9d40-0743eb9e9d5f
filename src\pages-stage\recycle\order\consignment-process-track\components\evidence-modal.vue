<template>
  <a-modal
    :destroy-on-close="true"
    title="证据详情"
    :visible="visible"
    :width="700"
    @cancel="cancel"
  >
    <a-list
      class="list"
      item-layout="vertical"
    >
      <a-list-item v-if="evidenceObj.order_replace_buy_protocol">
        <a-list-item-meta :description="'存证时间：' + (evidenceObj.order_replace_buy_protocol_time || '-')">
          <template #avatar>
            <span class="index-num">1</span>
          </template>
          <template #title>
            <div class="title">
              回收代卖协议文件存证
            </div>
            <div class="expand-btn">
              <a
                v-if="evidenceObj.order_replace_buy_protocol"
                class="file-extra"
                :href="evidenceObj.order_replace_buy_protocol"
                target="_blank"
              >
                <span class="type">PDF</span>
              </a>
            </div>
          </template>
        </a-list-item-meta>
      </a-list-item>
      <a-list-item v-if="evidenceObj.order_replace_send_protocol">
        <a-list-item-meta :description="'存证时间：' + (evidenceObj.order_replace_send_protocol_time || '-')">
          <template #avatar>
            <span class="index-num">2</span>
          </template>
          <template #title>
            <div class="title">
              回收代买协议文件存证
            </div>
            <div class="expand-btn">
              <a
                v-if="evidenceObj.order_replace_send_protocol"
                class="file-extra"
                :href="evidenceObj.order_replace_send_protocol"
                target="_blank"
              >
                <span class="type">PDF</span>
              </a>
            </div>
          </template>
        </a-list-item-meta>
      </a-list-item>
    </a-list>
    <template #footer>
      <a-button
        type="primary"
        @click="cancel"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  evidenceObj: {
    type: Object,
    default: () => ({}),
  },
});
const emits = defineEmits(['update:visible']);
const cancel = () => emits('update:visible', false);
</script>
<style lang="less" scoped>
.list {
  max-height: 60vh;
  padding-right: 6px;
  overflow-x: hidden;
  overflow-y: auto;
  :deep(.ant-list-item) {
    min-height: 124px;
    .ant-list-item-meta-title {
      position: relative;
      .expand-btn {
        position: absolute;
        top: 0;
        right: 0;
        .img {
          width: 100px;
          height: 100px;
        }
        video,
        audio {
          height: 100px;
        }
      }
    }

    .ant-list-item-meta-description {
      color: rgba(6, 21, 51, 0.45);
    }
  }
}

.index-num {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background-color: #dbdbdb;
  border-radius: 50%;
}

.file-extra {
  position: relative;
  display: inline-block;
  width: 100px;
  height: 100px;
  background: url(https://img1.rrzuji.cn/assest/201904/SUPER5CB6D3794DA60.png) no-repeat;
  background-position: 20px 20px;
  background-size: 60px 60px;
  border: 1px solid #eee;
  .type {
    position: absolute;
    bottom: 33px;
    left: 45px;
    width: 100%;
  }
}
</style>
