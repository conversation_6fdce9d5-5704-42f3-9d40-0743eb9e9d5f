<template>
  <a-spin :spinning="loading">
    <div class="store-upgrade-container">
      <!-- 升级面板 -->
      <template v-if="isPanel">
        <Panel />
      </template>
      <!-- 升级条件 -->
      <template v-else-if="isCondition">
        <Condition />
      </template>
      <!-- 升级步骤 -->
      <template v-else-if="isStepManagement">
        <StepManagement />
      </template>
      <!-- 申请表单 -->
      <template v-else-if="isApplicationForm">
        <ApplicationForm />
      </template>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
import { computed, onMounted, provide, reactive, ref } from 'vue';

import ApplicationForm from './components/application-form.vue';
import Condition from './components/condition.vue';
import Panel from './components/panel.vue';
import StepManagement from './components/step-management.vue';
import { EEnterStatus, EShowComponent, ISharedData } from './data.d';
import { getEnterStatus } from './services';

const loading = ref(false);
const sharedData = reactive({} as ISharedData);

const dedupe = (val: EShowComponent) => computed(() => sharedData.activeComponent === val);
const isPanel = dedupe(EShowComponent.Panel);
const isCondition = dedupe(EShowComponent.Condition);
const isStepManagement = dedupe(EShowComponent.StepManagement);
const isApplicationForm = dedupe(EShowComponent.ApplicationForm);

function updateSharedData(data: Partial<ISharedData> | null) {
  if (data) {
    Object.assign(sharedData, data);
  } else {
    for (const key in sharedData) {
      if (key === 'activeComponent') {
        sharedData.activeComponent = EShowComponent.Panel;
      } else {
        delete sharedData[key];
      }
    }
  }
}

async function setupSharedData() {
  try {
    loading.value = true;
    const { data } = await getEnterStatus();
    const activeComponent =
      !data || String(data.audit_status) === EEnterStatus.Passed ? EShowComponent.Panel : EShowComponent.StepManagement;
    const audit_status = data?.audit_status ? String(data.audit_status) : null;
    updateSharedData({
      activeComponent,
      audit_status,
    });
  } finally {
    loading.value = false;
  }
}

provide('sharedData', sharedData);
provide('updateSharedData', updateSharedData);

onMounted(() => {
  setupSharedData();
});
</script>

<style lang="less" scoped>
.store-upgrade-container {
  padding: 16px 0;
}
</style>
