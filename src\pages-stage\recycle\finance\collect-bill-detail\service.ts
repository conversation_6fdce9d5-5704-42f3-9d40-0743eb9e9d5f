import { GET, POST } from '@/services/api';
export interface IVerification {
  order_id?: string;
  server_id?: number | string;
  coin_type: number;
  pay_order_list: any[];
  source_order_list: any[];
  images: string[];
  remark: string;
  date: string;
  method: number;
}

// 核销商家列表
export function getServerList() {
  return GET('/super/recycle/sale-verification/server-list');
}
// 核销商家待付金额
export function getServerWaitPayMoney(params: any) {
  return GET('/super/recycle/sale-verification/server-wait-pay-money', params);
}

// 商家源单
export function getServerOrderList(params: any) {
  return GET('/super/recycle/sale-verification/server-order-list', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 核销列表选择参数
export function getSelectList() {
  return GET('/super/recycle/sale-verification/select-list');
}
// 创建核销收款单
export function saveVerification(params: IVerification) {
  return POST('/super/recycle/sale-verification/save', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}
// 编辑核销收款单
export function editVerification(params: IVerification) {
  return POST('/super/recycle/sale-verification/edit', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}
//收款单详情
export function getVerificationDetail(params: any) {
  return GET('/super/recycle/sale-verification/detail', params);
}
