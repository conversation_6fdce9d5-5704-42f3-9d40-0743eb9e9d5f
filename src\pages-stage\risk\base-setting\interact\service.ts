import { GET, POST } from '@/services/api';


export function getDepositConfig(params: { version: '0' | '1' }): Promise<any> {
  return GET('/super/interactive-risk-rule/get-rule-list', params);
}
// 获取 类目（型号）
export function getCategoryList(): Promise<any> {
  return GET('/super/produceDict/category/get-menu');
}
// 新接口 添加型号或者类目项
export function addRuleItem(params: { version: ('0' | '1'); model_ids?: string[];category_ids?:string[] }): Promise<any> {
  return POST('/super/interactive-risk-rule/add-rule-item', params);
}
export function ruleItemFetch(params: { version: ('0' | '1'); model_ids?: string[];category_ids?:string[]}): Promise<any> {
  return POST('/super/interactive-risk-rule/add-config-map', params);
}


// 从指定 规则 中删除 类目 或者 型号
export function delCategoryFromRule(params: {  version: '0' | '1',rule_id: string; category_id?: string;model_id?:string[]}): Promise<any> {
  return POST('/super/interactive-risk-rule/del-config-map', params);
}

// 修改配置
export function saveConfig(params: { rule_id: string; tmp_config: string;type:'0'|'1' }): Promise<any> {
  return POST('/super/interactive-risk-rule/update-rule', params);
}

// 重置操作 => 线上版覆盖预览版
export function online2Preview(params: { version: '0' | '1' }): Promise<any> {
  return GET('/super/interactive-risk-rule/reset-conf',params);
}

// 立即生效操作 => 预览版覆盖线上版
export function preview2Online(params: { version: '0' | '1' }): Promise<any> {
  return GET('/super/interactive-risk-rule/take-effect',params);
}
