<template>
  <layout-admin-page
    :navs="['趣回收', '基础数据', '商品估价模板']"
    title="商品估价模板"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="() => onEditTemplate()"
      >
        <template #icon>
          <plus-outlined />
        </template>
        新增估价模板
      </a-button>
    </template>
    <div class="container">
      <div class="search-form">
        <a-form layout="inline">
          <a-form-item label="模板名称">
            <a-input
              v-model:value="searchForm.name"
              allow-clear
              placeholder="请输入模板名称"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              type="primary"
              @click="() => getTableList('search')"
            >
              查询
            </a-button>
          </a-form-item>
          <a-form-item>
            <a-button @click="reset">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="table-box">
        <a-table
          class="bottom-fix-table"
          :columns="columns"
          :data-source="list"
          :loading="listLoading"
          :pagination="page"
          :scroll="{ x: '100%' }"
          sticky
          @on-table-change="tableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'actions'">
              <a-button
                style="color: #3777ff"
                type="link"
                @click="() => onEditTemplate(record.id)"
              >
                编辑
              </a-button>
              <a-button
                danger
                type="link"
                @click="() => onHandleDelete(record.id)"
              >
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';
import { useRouter } from 'vue-router';
import { Modal, message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useTable } from '@/hook/component/use-table';
import { columns } from './config';
import { queryTemplateList, deleteTemplate } from './service';
import { ITemplateItem } from './data';

// table hook
const searchForm = reactive<Record<string, any>>({});
const { list, listLoading, page, getTableList, tableChange } = useTable<ITemplateItem[], Record<string, any>>({
  api: queryTemplateList,
  searchForm,
  totalKey: 'data.pageInfo.count',
  pagination: {
    showTotal: () => {
      return `共${page.total}条记录 第${page.current}/${Math.ceil(
        (page.total as number) / (page.pageSize as number),
      )}页`;
    },
  },
  formatHandle: res => res.data.list,
});
getTableList();

// 重置搜索
const reset = () => {
  for (const key in searchForm) {
    if (searchForm[key]) {
      searchForm[key] = undefined;
    }
  }
  getTableList('search');
};

// 删除
const onHandleDelete = (id: string | number) => {
  Modal.confirm({
    title: '确定要删除吗？',
    onOk: () => {
      const closeLoading = message.loading();
      deleteTemplate(id).then(() => {
        closeLoading();
        message.success('删除成功');
        getTableList();
      });
    },
  });
};

// 新增 & 编辑
const router = useRouter();
const onEditTemplate = (id?: string | number) => {
  router.push({
    path: '/recycle/commodity/add-valuation-template',
    query: { id },
  });
};
</script>

<style scoped lang="less">
.container {
  padding: 0 24px;
  :deep(.ant-btn-link) {
    padding: 4px 0;
    & + .ant-btn-link {
      margin-left: 15px;
    }
  }
  .table-box {
    margin-top: 24px;
  }
}
</style>
