import type { UploadFile } from 'ant-design-vue';

export enum EOptions {
  /* 面审信息 */
  INTERVIEW_INFO = 1,
  /* 店铺升级 */
  SHOP_UPGRADE = 2,
  /* 行业资质 */
  INDUSTRY_QUALIFICATIONS = 3,
  /* 商品资质 */
  GOODS_QUALIFICATIONS = 4,
}

export enum EInterviewStatus {
  WAINTING = '0',
  SUCCESS = '1',
  ERROR = '2',
}

export interface TFileLink {
  type: string;
  file_urls: UploadFile[];
  name: string;
}

export interface IReviewData {
  /* 是否需要面审 */
  has_review: boolean;
  /* 审核状态：0-待审核，1-已通过，2-未通过 */
  status: EInterviewStatus;
  supplement_link_arr: FileLink[];
  url_prefix: string;
  review_reason?: string;
}

export enum EHandleType {
  ADD = '1',
  EDIT = '2',
  DETAIL = '3',
}

export enum ECurrentDataType {
  RELEASE = '1',
  CURRENTUSE = '2',
}

export type TImgae = {
  uid?: number;
  status: string;
  name?: string;
  url: string;
  response?: TImageResponse;
  watermark_image?: string;
};

export type TImageResponse = {
  path: string[];
};
