<template>
  <div class="config-item">
    <a-checkbox
      v-model:checked="is_conf"
      :disabled="item.is_open_module === 0"
      :indeterminate="indeterminate"
      @change="onCheckAllChange"
    >
      <div
        class="config-item-title"
        :style="{ opacity: item.is_open_module === 0 ? '0.25' : '1' }"
      >
        {{ item.module_cn_name }} <img
          class="config-item-icon"
          :src="item.icon"
        >
      </div>
    </a-checkbox>
    <a-divider style="margin: 16px 0" />
    <a-checkbox-group
      v-model:value="checkList"
      :disabled="item.is_open_module === 0"
      :options="checkOptions"
    />
  </div>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue';
import { ConfigurationItem } from '../../data';
import { difference } from 'lodash-es';

const props = defineProps({
  item: {
    type: Object as PropType<ConfigurationItem>,
    required: true,
  },
});

const emit = defineEmits(['set-check-list']);

const is_conf = computed(() => checkList.value.length === checkOptions.value.length);

const checkList = computed({
  get() {
    const list: number[] = [];
    props.item.function.forEach(i => i.is_conf && list.push(i.func_id));
    return list;
  },
  set(value) {
    let filterCheckList: number[] = [];
    let checked = false;

    if (value.length > checkList.value.length) {
      filterCheckList = difference(value, checkList.value);
      checked = true;
    } else if (value.length < checkList.value.length) {
      filterCheckList = difference(checkList.value, value);
      checked = false;
    }

    emit('set-check-list', filterCheckList, false, props.item.module_id, checked);
    // 满足条件后也要更改一级全选框
    if (checkList.value.length === checkOptions.value.length) {
      emit('set-check-list', filterCheckList, true, props.item.module_id, true, true);
    } else {
      emit('set-check-list', filterCheckList, true, props.item.module_id, false, true);
    }
  },
});

const checkOptions = computed(() => {
  const list = props.item.function.map(i => ({ label: i.func_cn_name, value: i.func_id }));
  return list;
});

const indeterminate = computed(() => {
  return !!checkList.value.length && checkList.value.length < checkOptions.value.length;
});

function onCheckAllChange(e: any) {
  const checked = e.target.checked;
  const list: number[] = [];
  props.item.function.forEach(i => list.push(i.func_id));
  emit('set-check-list', list, true, props.item.module_id, checked);
}
</script>

<style lang="less" scoped>
.config-item {
  margin-bottom: 8px;
  padding: 16px;
  background: #f5f7fa;

  &-title {
    display: flex;
    align-items: center;
    color: rgba(6, 21, 51, 0.85);
  }

  &-icon {
    width: 16px;
    margin-left: 5px;
  }
}
</style>
