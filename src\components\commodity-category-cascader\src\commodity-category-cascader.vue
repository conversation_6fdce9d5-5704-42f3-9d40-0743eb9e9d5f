<template>
  <a-cascader
    v-model:value="selectValue"
    :show-checked-strategy="Cascader.SHOW_CHILD"
    v-bind="targetAttrs"
    @change="handleChange"
    @dropdown-visible-change="handleVisible"
    @search="handleSearch"
  >
    <template
      v-if="loading"
      #notFoundContent
    >
      <a-spin size="small" />
    </template>
    <!-- 继承插槽 -->
    <template
      v-for="(_index, name) in slots"
      #[name]="slotData"
    >
      <slot
        :name="name"
        v-bind="slotData || {}"
      />
    </template>
  </a-cascader>
</template>

<script generic="V extends number[] | number[][]" lang="ts" setup>
import { computed, Ref, useAttrs, useSlots, watch } from 'vue';
import { Cascader } from 'ant-design-vue';

import { useCommodityCategoryTree } from '@/composables/commodity-category';
import type { IOption } from '@/composables/commodity-category/data';
import { useVModel } from '@/utils/hooks';

import { useAsyncSearch } from './composables/use-async-search';
import { useLevelLoad } from './composables/use-level-load';
import type { ICommodityCategoryCascaderProps } from './data';

defineOptions({ inheritAttrs: false });

/** 根据value的多维数组获取对应多维的选择项类型 */
type TGetOption<T> = T extends (infer U)[] ? TGetOption<U>[] : IOption;

const props = defineProps<ICommodityCategoryCascaderProps<V>>();

const emit = defineEmits<{
  (e: 'update:value', value?: V): void;
  (e: 'change', value?: V, selectedOptions?: TGetOption<V>): void;
  (e: 'search', value: string): void;
  (e: 'dropdownVisibleChange', value: boolean): void;
}>();

const selectValue: Ref<V | undefined> = useVModel(props, 'value', emit);

// 远程搜索逻辑
const {
  searchName,
  isNameSearch,
  inputSearch,
  clearSearch,
  pushEmptyOptions,
  filterEmptyOptions,
  customDisplayRender,
} = useAsyncSearch({
  asyncSearch: props.asyncSearch,
  level: props.level,
  changeOnSelect: props.changeOnSelect,
  multiple: props.multiple,
  displayRender: props.displayRender,
});

/** 输入框搜索 */
function handleSearch(value: string) {
  inputSearch?.(value);
  emit('search', value);
}

/** 更改选择项 */
function handleChange(value?: V, selectedOptions?: TGetOption<V>) {
  if (!props.isMustChild) {
    selectValue.value = value;
    emit('change', value, selectedOptions);
    return;
  }
  // ============================= isMustChild 绑定项必须包含最后一级 ===============================
  // 多选时，需要将多选的选项子级单独提取出来绑定
  if (props.multiple) {
    const completeOptions: IOption[][] = [];
    (selectedOptions as IOption[][])?.forEach(options => {
      completeOptions.push(...getCompleteOptions(options));
    });
    const completeValue = completeOptions.map(options => options.map(option => option.value)) as V;
    selectValue.value = completeValue;
    emit('change', completeValue, completeOptions as TGetOption<V>);
  }
  // 单选时，也需取出选中的选项最后一级的子级，只取一个选择
  else {
    const completeOptions = getCompleteOptions(selectedOptions as IOption[])?.[0];
    const completeValue = completeOptions?.map(option => option.value) as V;
    selectValue.value = completeValue;
    emit('change', completeValue, completeOptions as TGetOption<V>);
  }
}

/** 获取包括子级的完整选择项 */
function getCompleteOptions(options: IOption[]) {
  const completeOptions: IOption[][] = [];
  // 当前层级未到最后一级时，进入下一级进行递归获取
  if (options.length < (props.level || 4)) {
    options[options.length - 1].children?.forEach(item => {
      completeOptions.push(...getCompleteOptions([...options, item]));
    });
  }
  if (completeOptions.length) {
    return completeOptions;
  }
  return [options];
}

/** 下拉弹窗显示状态 */
function handleVisible(visible: boolean) {
  clearSearch?.(); // 弹窗展开收起后，清空搜索内容
  emit('dropdownVisibleChange', visible);
}

// 分级搜索逻辑
const { levelIds, setLevelIdsByLoad, setLevelIdsByValue } = useLevelLoad({
  asyncSearch: props.asyncSearch,
  multiple: props.multiple,
});
if (props.asyncSearch) {
  watch(
    selectValue,
    () => {
      clearSearch?.(); // 选择项后，清空搜索内容
      setLevelIdsByValue?.(selectValue.value);
    },
    { immediate: true, deep: true },
  );
}

const { loading, categoryTree } = useCommodityCategoryTree(() => ({
  disabledType: props.disabledType,
  level: props.level,
  scene: props.scene,
  filterPidLevel: props.filterPidLevel,
  pid: props.pid,
  isMustChild: !props.changeOnSelect && props.isMustChild,
  isNameSearch: isNameSearch?.value,
  searchName: searchName?.value || undefined,
  isLevelSearch: props.asyncSearch,
  levelIds: levelIds?.value,
}));

const options = computed(() => {
  let renderOptions = categoryTree.value;
  // 有自定义的选项时，使用自定义的
  if (props.defineOptions) {
    renderOptions = props.defineOptions(renderOptions);
  }
  // 有输入远程搜索请求时，需要插入空选择项
  if (isNameSearch?.value) {
    renderOptions = pushEmptyOptions(renderOptions);
  }
  return renderOptions;
});

/** 隐藏下拉框的选择框样式类名 */
const HIDE_CHECKBOX_CLASS = 'commodity-category-hide-checkbox';
/** 自定义下拉框样式类名 */
const customClassName = computed(() => {
  let name = '';
  // 自定义的样式类名
  if (props.dropdownClassName) {
    name += props.dropdownClassName + ' ';
  }
  // 如果不是每级菜单选项值都可选，隐藏选择框
  if (!props.changeOnSelect) {
    name += HIDE_CHECKBOX_CLASS;
  }
  return name;
});

// 属性继承，可以属性传递继承ant组件
const attrs = useAttrs();
const targetAttrs = computed(() => {
  return {
    allowClear: true,
    placeholder: '请选择',
    ...attrs,
    loadData: setLevelIdsByLoad,
    showSearch: {
      limit: 100,
      filter: filterEmptyOptions,
    },
    loading: loading.value,
    multiple: props.multiple,
    changeOnSelect: props.changeOnSelect,
    options: options.value,
    dropdownClassName: customClassName.value,
    displayRender: customDisplayRender,
    showCheckedStrategy: Cascader[props.showCheckedStrategy],
  };
});

// 继承插槽
const slots = useSlots();

// 向外暴露
defineExpose({
  options,
});
</script>

<!--
  这里需要在body层下修改下拉弹窗内的样式，所以不使用“scoped”，
  其他样式应该额外定义style并使用scoped，避免被覆盖
-->
<style lang="less">
.commodity-category-hide-checkbox {
  .ant-cascader-menu > .ant-cascader-menu-item > .ant-cascader-checkbox {
    display: none;
  }

  .ant-cascader-menu:last-child > .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled) {
    font-weight: normal;
    background-color: transparent;
  }

  .ant-cascader-menu > .ant-cascader-menu-item:has(.ant-cascader-checkbox-checked) {
    font-weight: 600 !important;
    background-color: var(--ant-primary-1) !important;
  }
}
</style>
