<template>
  <a-modal
    v-model:visible="bindVisible"
    class="evidence-modal"
    :title="pageType === '1' ? '上传证据' : '查看证据'"
    :width="pageType === '1' ? 633 : 514"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <template v-if="pageType === '1'">
        <a-form
          class="form"
          :label-col="{ span: 6, style: { maxWidth: '28%', flex: '0 0 28%' } }"
        >
          <a-form-item label="订单号">
            {{ initData.order_id }}
          </a-form-item>
          <a-form-item label="申诉类型">
            {{ evidenceType === 1 ? '数据不准' : '客观原因' }}
          </a-form-item>
          <a-form-item
            label="当前订单数据执行结果"
            required
          >
            <a-checkbox-group
              v-model:value="checkData"
              @change="handleChange"
            >
              <a-row>
                <a-col
                  v-for="item in resultOptions"
                  :key="item.value"
                  class="reason-item"
                  :span="24"
                >
                  <a-checkbox :value="item.value">
                    {{ item.label }}
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>

            <div class="checkbox-tips">
              {{ evidenceType === 1 ? '（请勾选以上错误的执行结果）' : '（请勾选以上问题）' }}
            </div>
          </a-form-item>
          <a-form-item
            label="订单说明"
            :rules="{ required: true, message: `备注内容不能为空`, trigger: 'blur' }"
          >
            <a-textarea
              v-model:value="initData.desc"
              allow-clear
              placeholder="请简要说明"
            />
          </a-form-item>
          <a-form-item
            label="申诉证据"
            :rules="{ required: true, message: `申诉证据不能为空`, trigger: 'blur' }"
          >
            <upload-file
              :default-file="initData.file_evidence"
              @file-list-update="fileListUpdate"
            />
          </a-form-item>
        </a-form>
      </template>
      <template v-else>
        <div class="row-first row">
          <div class="item-content">
            <div class="row-item-title">
              订单号
            </div>
            <div class="row-item-value">
              {{ initData.order_id }}
            </div>
          </div>
          <div class="item-content">
            <div
              class="row-item-title"
              style="margin-right: 64px"
            >
              申诉类型
            </div>
            <div class="row-item-value">
              {{ evidenceType === 1 ? '数据不准' : '客观原因' }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="row-item-title">
            商家勾选的订单数据执行不准的结果
          </div>
          <div
            v-for="(item, index) in initData.appeal_order_reasons"
            :key="item.appeal_order_type"
            class="row-item-value"
          >
            {{ index + 1 }}. {{ item.appeal_desc }}
          </div>
        </div>
        <div class="row">
          <div class="row-item-title">
            订单说明
          </div>
          <div class="row-item-value">
            {{ initData.desc }}
          </div>
        </div>
        <div class="row">
          <div class="row-item-title">
            申诉证据
          </div>
          <upload-file
            :can-upload="false"
            :default-file="initData.file_evidence"
          />
        </div>
      </template>
    </a-spin>
    <template #footer>
      <template v-if="pageType === '1'">
        <a-button @click="handleClear">
          清空并退出
        </a-button>
        <a-button
          type="primary"
          @click="handleSubmit"
        >
          提交
        </a-button>
      </template>

      <template v-else>
        <a-button
          type="primary"
          @click="bindVisible = false"
        >
          知道了
        </a-button>
      </template>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, PropType, watch, ref } from 'vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

import UploadFile from '../upload-file/index.vue';

import { TableCacheItem, RecordItem, FileItemType } from '../../data';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object as PropType<TableCacheItem>,
    required: true,
  },
  evidenceType: {
    type: Number,
    required: true,
  },
  recordData: {
    type: Object as PropType<RecordItem>,
    required: true,
  },
  filterTime: {
    type: String,
    required: true,
  },
  pageType: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:visible', 'change-cache-data', 'change-sub-status', 'change-sub-count']);

const resultOptions = computed(() => {
  if (props.evidenceType === 1) {
    const sendTime = props.recordData.sent_at
      ? dayjs.unix(props.recordData.sent_at).format('YYYY-MM-DD HH:mm:ss')
      : '--';
    const server_receive_at = dayjs.unix(props.recordData.server_receive_at || 0).format('YYYY-MM-DD HH:mm:ss');
    return [
      {
        label: `接单时间：${server_receive_at}`,
        value: 1,
      },
      {
        label: '属于常规考核',
        value: 2,
      },
      {
        label: `属于${props.filterTime}之前发货订单，发货时间为：${sendTime}`,
        value: 3,
      },
      {
        label: '标记发货超过48H',
        value: 4,
      },
      {
        label: `发货前收取期数为${props.recordData.sent_pay_num}`,
        value: 5,
      },
    ];
  } else {
    return [
      {
        label: '商家风控结果与平台风控结果不一致',
        value: 6,
      },
      {
        label: '用户坚决取消订单',
        value: 7,
      },
      {
        label: '其他（在订单说明中描述）',
        value: 8,
      },
    ];
  }
});

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

const initData = ref<TableCacheItem>({});
const checkData = ref<number[]>([]);

const fileListUpdate = (fileList: FileItemType[]) => {
  initData.value.file_evidence = [...fileList];
};

const handleChange = (checkValues: number[]) => {
  const resultArr = checkValues.map(item => {
    const label = resultOptions.value.find(option => option.value === item)?.label || '';
    return {
      appeal_order_type: item,
      appeal_desc: label,
    };
  });
  initData.value.appeal_order_reasons = resultArr;
};

const handleClear = () => {
  emit('change-cache-data', { order_id: initData.value.order_id, appeal_type: initData.value.appeal_type });
  emit('change-sub-status', initData.value.order_id, false);
  emit('change-sub-count', -1);
  bindVisible.value = false;
};

const handleCancel = () => {
  emit('change-cache-data', initData.value);
  bindVisible.value = false;
};

const handleSubmit = () => {
  const { appeal_order_reasons, file_evidence, desc } = initData.value;
  if (!appeal_order_reasons || !appeal_order_reasons.length || !file_evidence || !file_evidence.length || !desc) {
    return message.warning('请将信息填写完整');
  }
  initData.value.isEvidence = true;
  emit('change-sub-status', initData.value.order_id, true);
  emit('change-sub-count', 1);
  handleCancel();
};

watch(
  () => props.data,
  value => {
    const arr: number[] = [];
    value.appeal_order_reasons && value.appeal_order_reasons.forEach(item => arr.push(item.appeal_order_type));
    checkData.value = arr;
    initData.value = value;
  },
);
</script>

<style lang="less" scoped>
.evidence-modal {
  .form {
    :deep(.ant-form-item) {
      margin-bottom: 16px;
    }

    .reason-item {
      margin-top: 8px;

      &:nth-child(1) {
        margin-top: 5px;
      }
    }

    .checkbox-tips {
      margin-top: 8px;
      color: #faad14;
    }
  }

  .row {
    margin-bottom: 24px;
  }
  .row-item-title {
    margin-bottom: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: bold;
  }
  .row-item-value {
    margin-bottom: 4px;
    color: rgba(6, 21, 51, 0.65);
  }
  .row-first {
    display: flex;
    justify-content: space-between;
  }
}
</style>
