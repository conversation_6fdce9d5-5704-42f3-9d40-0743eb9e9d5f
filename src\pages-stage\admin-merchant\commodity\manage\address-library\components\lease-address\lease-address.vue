<template>
  <div class="lease-address">
    <a-alert
      message="请谨慎选定“默认”租赁地址，一旦确认，平台在订单派发时，将优先为您推送该地址周边的同城及同省订单。"
      show-icon
      style="margin-bottom: 24px"
      type="error"
    >
      <template #icon>
        <InfoCircleFilled />
      </template>
    </a-alert>

    <RTable
      ref="refRTable"
      :api="apiSearchTable"
      :columns="columns"
      :group-handler="groupHandler"
    >
      <template #tableBodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'address'">
          <div class="flex-wrap flex-y-center">
            <div
              class="flex-con text-ellipse"
              :title="record.address"
            >
              {{ record.address }}
            </div>
            <span
              v-if="Number(record.is_default) === 1"
              class="default-tag"
            >默认</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-space :size="6">
            <div :class="['dot', toggleStatus(record.status)?.color]" />
            {{ toggleStatus(record.status)?.title }}
          </a-space>
        </template>
      </template>
    </RTable>

    <a-modal
      v-model:visible="deleteModal.deleteModalVisible"
      :confirm-loading="deleteModal.loading"
      title="删除租赁地址"
      @close="handleCloseDeleteModal"
      @ok="handleDelete"
    >
      <a-select
        v-model:value="deleteModal.address"
        :loading="deleteModal.selectLoading"
        :options="deleteModal.otherAddress"
        placeholder="请选择替换的地址信息，若无其他地址，不能进行删除操作"
        style="width: 100%"
        @click="onSelectClick"
      >
        <template #notFoundContent>
          <a-spin
            v-if="deleteModal.selectLoading"
            size="small"
          />
          <REmpty v-else />
        </template>
      </a-select>
    </a-modal>
    <AddressEdit
      :item="addressModal.item"
      :type="addressModal.type"
      :visible="addressModal.visible"
      @on-success="onCreateAddressSuccess"
      @on-visible-change="
        val => {
          addressModal.visible = val;
        }
      "
    />
    <a-drawer
      v-model:visible="viewVisible"
      class="drawer-wrapper"
      placement="right"
      title="查看租赁地址"
      :width="850"
    >
      <div style="height: 100%; overflow-x: hidden; overflow-y: scroll">
        <template
          v-for="item in viewFormGroup"
          :key="item.title"
        >
          <div class="item-wrap">
            <div class="item-title">
              {{ item.title }}
            </div>
            <div class="item-box">
              <template
                v-for="item2 in item.item"
                :key="item2.key"
              >
                <a-form-item
                  v-if="item2.key !== 'cover' && item2.key !== 'status'"
                  :label="item2.label"
                >
                  {{ addressModal.item[item2.key] }}
                </a-form-item>
                <a-form-item
                  v-else-if="item2.key === 'status'"
                  :label="item2.label"
                >
                  <a-space :size="6">
                    <div :class="['dot', toggleStatus(addressModal.item[item2.key])?.color]" />
                    {{ toggleStatus(addressModal.item[item2.key])?.title }}
                  </a-space>
                </a-form-item>
                <!-- <a-form-item
                v-else-if="item2.key === 'cover'"
                :label="item2.label"
              >
                <div
                  style="display: inline-block; margin-top: 8px; padding: 8px; border: 1px solid rgba(6, 21, 51, 0.15)"
                >
                  {{ addressModal.item[item2.key] }}
                </a-form-item> -->
                <!-- <a-form-item
                  v-else-if="item2.key === 'status'"
                  :label="item2.label"
                >
                  {{ toggleStatus(addressModal.item[item2.key]) }}
                </a-form-item> -->
                <a-form-item
                  v-else-if="item2.key === 'cover'"
                  :label="item2.label"
                >
                  <div style="display: inline-block; padding: 8px; border: 1px solid rgba(6, 21, 51, 0.15)">
                    <image-item
                      :height="64"
                      :img="addressModal.item.imgOssServer + addressModal.item[item2.key]"
                      :padding="0"
                      :width="64"
                      :wrap-style="{
                        paddingRight: '0',
                      }"
                    />
                  </div>
                </a-form-item>
              </template>
            </div>
          </div>
        </template>
      </div>
    </a-drawer>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { InfoCircleFilled } from '@ant-design/icons-vue';
import type { IGroupHandlerParams, RTableInstance } from 'rrz-web-design';

import AddressEdit from './components/lease-address-edit.vue';
import { columns, viewFormGroup } from './config';
import { deleteLeaseAddress, findOneLeaseAddress, getAddressIndexInf, setAsDefault } from './service';

const refRTable = ref<RTableInstance | null>(null);

const showAddModal = () => {
  addressModal.visible = true;
  addressModal.type = 'send';
  addressModal.item = {
    type: 'add',
  };
};

const imgOssServer = ref<string>('');

function apiSearchTable(searchParams: any) {
  return new Promise(async resolve => {
    const params = {
      page: searchParams.page,
      pageSize: searchParams.page_size,
      TblServerAddressSearchDao: refRTable.value.searchForm,
    };
    const { data } = await getAddressIndexInf(params);
    imgOssServer.value = data.imgOssServer;
    resolve({
      data: data.list,
      meta: {
        pagination: {
          page: data.page.page,
          page_size: data.page.pageSize,
          total: data.page.count,
        },
      },
    });
  });
}

defineExpose({
  handleAdd: showAddModal,
});

/**
 * 地址
 */
const addressModal = reactive<{
  visible: boolean;
  type: string;
  item: {
    [b: string]: any;
  };
}>({
  visible: false,
  type: '', // send, return, store
  item: {},
});

const onCreateAddressSuccess = async () => {
  refRTable.value?.getTableList();
};

const toggleStatus = (num: number) => {
  return {
    10: {
      title: '正常',
      color: 'success',
    },
    12: {
      title: '待审核',
      color: 'orange',
    },
    11: {
      title: '已屏蔽',
      color: 'gray',
    },
  }[num];
};

const viewVisible = ref<boolean>(false);

const viewLeaseAddress = (record: any, type: string) => {
  if (type === 'edit') {
    addressModal.visible = true;
    addressModal.type = 'send';
  } else {
    viewVisible.value = true;
  }
  addressModal.item = { ...record, type, imgOssServer: imgOssServer.value };
};

const deleteModal = reactive<{
  deleteModalVisible: boolean;
  otherAddress: string[];
  address: string | undefined | number;
  id: string | number;
  loading: boolean;
  selectLoading: boolean;
}>({
  deleteModalVisible: false,
  otherAddress: [],
  address: undefined,
  id: '',
  loading: false,
  selectLoading: true,
});

const onSelectClick = async () => {
  try {
    deleteModal.selectLoading = true;
    const res = await findOneLeaseAddress({
      id: deleteModal.id,
    });
    deleteModal.otherAddress = res.data.other.map((i: any) => ({
      label: i.province + ' ' + i.city + ' ' + i.address,
      value: i.id,
    }));
  } catch (error) {
    message.error('加载失败');
  } finally {
    deleteModal.selectLoading = false;
  }
};

const showDeleteModal = (params: IGroupHandlerParams) => {
  const { record } = params;
  deleteModal.id = record.id;
  deleteModal.address = undefined;
  deleteModal.otherAddress = [];
  deleteModal.deleteModalVisible = true;
};

const handleCloseDeleteModal = () => {
  deleteModal.deleteModalVisible = false;
};

const handleDelete = async () => {
  if (!deleteModal.address) return message.error('所选地址不能为空');
  try {
    deleteModal.loading = true;
    await deleteLeaseAddress({
      id: deleteModal.id,
      method: 'store.address.delete',
      address_id: deleteModal.address,
    });
    message.success('删除成功');
    refRTable.value?.getTableList();
  } catch (error) {
    message.error('删除失败');
  } finally {
    deleteModal.loading = false;
    deleteModal.deleteModalVisible = false;
  }
};

const handleSetDefault = async (params: IGroupHandlerParams) => {
  const { record } = params;
  const id = record.id;
  await setAsDefault({ id });
  message.success('操作成功');
  refRTable.value?.getTableList();
};

function previewAddress(params: IGroupHandlerParams) {
  const { record } = params;
  viewLeaseAddress(record, 'view');
}

function editAddress(params: IGroupHandlerParams) {
  const { record } = params;
  viewLeaseAddress(record, 'edit');
}

const groupHandler = {
  previewAddress,
  editAddress,
  showDeleteModal,
  handleSetDefault,
};
</script>
<style lang="less" scoped>
.lease-address {
  margin-top: 8px;
}

.drawer-wrapper {
  .item-wrap {
    margin-bottom: 16px;

    .item-title {
      margin-bottom: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 600;
      font-size: 16px;
    }

    .item-box {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -8px 16px;

      :deep(.ant-form-item) {
        flex-basis: 50%;
        align-items: flex-start;
        min-width: 268px;
        margin: 0 0 16px 0;
        padding: 0 8px;
        white-space: nowrap;

        label {
          height: 0;
        }

        .ant-form-item-control-input {
          min-height: 0;
        }
      }

      :deep(.ant-form-item-control) {
        width: 0;

        .ant-form-item-control-input-content {
          white-space: initial;
          word-wrap: break-word;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin: 0 0 64px 0;
    }
  }
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.success {
    background: #52c41a;
  }

  &.orange {
    background: #faad14;
  }

  &.gray {
    background: #fafafa;
  }
}

.text-ellipse {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.default-tag {
  margin-left: 4px;
  padding: 2px 4px;
  color: #fff;
  background: var(--ant-primary-color);
}

:deep(.r-search-table-container .r-search-content) {
  margin: 0 0 24px;
}

:deep(.r-search-table-container .r-table-content) {
  padding: 0;
}
</style>
