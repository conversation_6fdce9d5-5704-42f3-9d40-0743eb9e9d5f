<template>
  <!-- 采购余额展示 -->
  <div
    class="flex-wrap flex-y-center flex-gap-8"
    style="margin-top: 40px"
  >
    <template v-if="isSpecialCategory" />
    <template v-else-if="!showSelectControls">
      <span class="bold">项目采购余额：<span style="font-weight: 500">选择设备信息后可见</span></span>
    </template>
    <template v-else-if="!accountAmount.sku_id">
      <span class="bold">项目采购余额：
        <a-spin
          size="small"
          :spinning="true"
        />
      </span>
    </template>
    <template v-else-if="!accountAmount?.plat && !accountAmount?.notPlat">
      <span class="bold">项目采购余额：<span style="font-weight: 500">线下结算</span></span>
    </template>
    <template v-else>
      <div class="flex-wrap flex-vertical">
        <div class="flex-wrap flex-y-center flex-gap-6">
          <div>
            <div
              v-if="accountAmount?.plat?.acc_amount"
              class="bold"
            >
              平台采购账户余额：
              <span style="font-weight: 500">￥{{ accountAmount?.plat?.acc_amount }}</span>
            </div>
            <div
              v-if="accountAmount?.notPlat?.acc_amount"
              class="bold"
            >
              第三方采购账户余额：
              <span style="font-weight: 500">￥{{ accountAmount?.notPlat?.acc_amount }}</span>
            </div>
          </div>
          <div
            class="action-icon bold"
            @click="emits('getPriceInfo')"
          >
            <SyncOutlined :spin="loading" />
          </div>
          <div class="flex-wrap flex-vertical flex-gap-4">
            <a-button
              v-if="!isSuper && accountAmount?.plat?.acc_amount"
              class="line-btn"
              size="small"
              style="margin-left: 8px"
              @click="emits('goRechargeRoute', 'plat', accountAmount?.plat)"
            >
              充值
            </a-button>
            <a-button
              v-if="!isSuper && accountAmount?.notPlat?.acc_amount"
              class="line-btn"
              size="small"
              style="margin-left: 8px"
              @click="emits('goRechargeRoute','notPlat',accountAmount?.notPlat)"
            >
              充值
            </a-button>
          </div>

          <template v-if="accountAmount?.plat?.need_recharge || accountAmount?.notPlat?.need_recharge">
            <div class="flex-wrap flex-vertical flex-x-justify division-price">
              <div>
                <span v-if="accountAmount?.plat?.need_recharge">
                  <span v-if="accountAmount?.is_one_pay_account">（预计充值￥{{ accountAmount?.plat?.need_recharge_amount }}后，可下发仓库）</span>
                  <span v-else>（账户余额不足）</span>
                </span>
              </div>
              <div>
                <span v-if="accountAmount?.notPlat?.need_recharge">
                  <span v-if="accountAmount?.is_one_pay_account">（预计充值￥{{ accountAmount?.notPlat?.need_recharge_amount }}后，可下发仓库）</span>
                  <span v-else>（账户余额不足）</span>
                </span>
              </div>
            </div>
          </template>
        </div>
        <div
          v-if="accountAmount?.credit"
          class="bold"
        >
          商家信用额度可用金额：
          <span style="font-weight: 500">￥{{ accountAmount?.credit?.acc_amount }}</span>
        </div>
        <div
          class="bold"
          style="margin-top: 8px;"
        >
          本订单预计消耗
          <span v-if="accountAmount?.plat?.total">平台采购账户金额：
            <span style="font-weight: 500;">￥{{ accountAmount?.plat?.total }}</span>
          </span>
          <span v-if="accountAmount?.plat?.total && accountAmount?.notPlat?.total">，</span>
          <span v-if="accountAmount?.notPlat?.total">第三方采购账户金额：
            <span style="font-weight: 500;">￥{{ accountAmount?.notPlat?.total }}</span>
          </span>
          <span
            class="detail-text"
            @click="feeDetailModalVisible = true"
          >查看详情</span>
        </div>
      </div>
    </template>
  </div>
  <a-modal
    v-model:visible="feeDetailModalVisible"
    title="费用详情"
    @cancel="handleCancel"
  >
    <a-alert
      message="实际价格按设备下发日期的价格为准"
      show-icon
      style="margin-bottom: 24px;"
      type="warning"
    />
    <div class="fee-card">
      <!-- 平台采购账户 -->
      <div
        v-if="accountAmount?.plat?.total"
        class="fee-section"
      >
        <div class="fee-section-title">
          消耗平台采购账户金额：
          <span style="font-weight: 500;">￥{{ accountAmount?.plat?.total }}</span>
        </div>
        <div
          v-if="!!accountAmount?.plat?.fee_type_trans?.length"
          class="fee-items"
        >
          <div
            v-for="(item,index) in accountAmount?.plat?.fee_type_trans"
            :key="index"
            class="fee-item"
          >
            <span class="fee-label">{{ item.fee_type_text }}：</span>
            <span class="fee-value">￥{{ item.amount }}</span>
          </div>
        </div>
      </div>

      <!-- 第三方采购账户 -->
      <div
        v-if="accountAmount?.notPlat?.total"
        class="fee-section"
      >
        <div class="fee-section-title">
          消耗第三方采购账户金额：
          <span style="font-weight: 500;">￥{{ accountAmount?.notPlat?.total }}</span>
        </div>
        <div
          v-if="!!accountAmount?.notPlat?.fee_type_trans?.length"
          class="fee-items"
        >
          <div
            v-for="(item,index) in accountAmount?.notPlat?.fee_type_trans"
            :key="index"
            class="fee-item"
          >
            <span class="fee-label">{{ item.fee_type_text }}</span>
            <span class="fee-value">￥{{ item.amount }}</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button
        type="primary"
        @click="handleCancel"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { SyncOutlined } from '@ant-design/icons-vue';

defineProps({
  isSpecialCategory: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showSelectControls: {
    type: Object,
    default: () => ({}),
  },
  accountAmount: {
    type: Object,
    default: () => ({}),
  }
});

const emits = defineEmits(['goRechargeRoute', 'getPriceInfo']);

const route = useRoute();
const isSuper = route.query.role === 'super';

const feeDetailModalVisible = ref(false);

const handleCancel = () => {
  feeDetailModalVisible.value = false;
}
</script>
<style lang="less" scoped>
.bold {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
}

.action-icon {
  color: var(--ant-primary-color);
  cursor: pointer;
}

.division-price {
  height: 44px;
  border-left: 1px solid rgba(6, 21, 51, 0.15);
}

.line-btn {
  color: var(--ant-primary-color);
  border-color: var(--ant-primary-color);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:hover,
  &:active,
  &:focus,
  &:visited {
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    background: var(--ant-primary-color);
    border-color: var(--ant-primary-color);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }
}

.detail-text {
  margin-left: 6px;
  color: #3777ff;
  cursor: pointer;
}

.fee-card {
  display: flex;
  flex-direction: column;
  gap: 16px;


  .fee-section {
    border: 1px solid rgba(6,21,51,0.06);
    border-radius: 6px;
  }
}

.fee-section-title {
  padding: 16px;
  color: #3777FF;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  background: #F5F7FA;
}

.fee-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
}

.fee-item {
  position: relative;
  color: rgba(6,21,51,0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  &:not(:first-of-type){
    position: relative;
    padding-left: 16px;
  }
  &:not(:first-of-type)::before {
    position: absolute;
    top: 50%;
    left: 0;
    display: block;
    width: 1px;
    height: 14px;
    background-color: rgba(6,21,51,0.15);;
    transform: translateY(-50%);
    content: '';
  }
}


</style>
