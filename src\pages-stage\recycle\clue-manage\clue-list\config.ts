import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';
import { IExcelModel } from '../../quotation-manage/quotation-registration/data';
import { AuditStatus, TableToolTips } from '../config';
import { IModel } from '../../recycle-manage/requirement-registration/data';
export const clueListColumns: ColumnType<IExcelModel>[] = [
  {
    title: '商铺id',
    key: 'server_id',
    dataIndex: 'server_id',
    width: 100,
  },
  {
    title: '商家名称',
    key: 'server_name',
    dataIndex: 'server_name',
    width: 260,
  },
  {
    title: '联系号码',
    key: 'phone',
    dataIndex: 'phone',
    width: 140,
  },
  {
    title: '回收设备 型号 数量',
    key: 'demand_content',
    dataIndex: 'demand_content',
    width: 400,
    customRender: ({ value }) => {
      const data = JSON.parse(value) as IModel[];
      let str = '';
      data.forEach((item, index, arr) => {
        for (const key in item) {
          if (key === 'id') continue;
          str += `${item[key]} `;
          if (key === 'count') str += '台';
        }
        if (index != arr.length - 1) str += '、';
      });
      return TableToolTips(str);
    },
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
    width: 400,
    customRender: ({ value }) => {
      return TableToolTips(value);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 200,
  },
  {
    title: '处理状态',
    key: 'status',
    dataIndex: 'status',
    width: 100,
    fixed: 'right',
    customRender: ({ text }: { text: number }) => {
      const STATUS = {
        0: '待跟进',
        1: '已跟进',
      };
      return AuditStatus({ value: text, backarray: ['#FAAD14', '#00C8BE'], status: STATUS });
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 100,
    fixed: 'right',
  },
];
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'status',
    originProps: { label: '处理状态', name: 'status' },
    elProps: { allowClear: true, style: { width: '144px' }, placeholder: '请输入' },
    fragmentKey: 'renderSelect',
    options: [
      {
        label: '全部',
        value: 2,
      },
      {
        label: '已跟进',
        value: 1,
      },
      {
        label: '待跟进',
        value: 0,
      },
    ],
  },
];
