import { useTable } from '@/hook/component/use-table';
import { AxiosResponse } from 'axios';
import { reactive } from 'vue';
import { ITableListType, ITableSearchParams } from '../data';
import { Modal, message } from 'ant-design-vue';
import { deleteAction, updataStatus } from '../service';

export const useHandler = () => {
  const searchParams = reactive<ITableSearchParams>({});
  /*---------------- table hooks  --------------------*/
  const { list, listLoading, page, getTableList, tableChange } = useTable<ITableListType, ITableSearchParams>({
    url: '/super/recycle/common-question/list',
    totalKey: 'data.pageInfo.count',
    searchForm: searchParams,
    formatHandle: (res: AxiosResponse<{ list: ITableListType[] }>) => {
      return res.data.list;
    },
    pagination: {
      showTotal: (): string => {
        const { total, pageSize } = page;
        const totalPages = Math.ceil((total as number) / (pageSize as number));
        return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
      },
    },
  });
  const statusChange = async (status: string, record: ITableListType) => {
    try {
      await updataStatus({ id: record.id, status: status });
      message.success(`${status === '1' ? '启用' : '禁用'}成功`);
    } catch (error) {
      // 回滚状态
      record.status = status === '1' ? '2' : '1';
    }
  };
  const deleteHandler = (id: string) => {
    Modal.confirm({
      title: '确认删除该文章吗？',
      content: '是否删除文章，文章删除后将不能恢复。',
      onOk: async () => {
        await deleteAction(id);
        getTableList();
        message.success('操作成功');
      },
    });
  };

  return {
    list,
    listLoading,
    page,
    getTableList,
    tableChange,
    searchParams,
    statusChange,
    deleteHandler,
  };
};

export const findLabel = (options: { label: string; value: string }[], value: string) =>
  options.find(item => item.value === value)?.label;
