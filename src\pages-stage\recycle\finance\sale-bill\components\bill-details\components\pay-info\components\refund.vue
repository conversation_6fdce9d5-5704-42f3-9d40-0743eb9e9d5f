<template>
  <a-modal
    v-model:visible="visible"
    title="退款"
    width="560px"
    @cancel="close"
    @ok="confirm"
  >
    <div class="intrduce">
      <img
        class="intrduce-img"
        src="https://img1.rrzuji.cn/uploads/scheme/2305/29/m/6uRwbmq3kXSCUeLsEKo6.png"
      >
      <div>
        1.退款金额将原路退回，可能会有延迟；<br>
        2.如退款失败，可到账单详情页重试。
      </div>
    </div>
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :label-col="{
          span: 5,
        }"
        :model="formState"
      >
        <a-form-item label="可退款金额">
          ￥99999.99
        </a-form-item>
        <a-form-item
          label="本次退款金额"
          required
        >
          <a-input-number
            v-model:value="formState.mm"
            :max="10"
            :min="1"
            placeholder="￥0.00"
            style="width: 124px"
          />
        </a-form-item>
        <a-form-item
          label="备注"
          name="remark"
          required
        >
          <a-textarea
            v-model:value.trim="formState.remark"
            :auto-size="{ minRows: 4, maxRows: 8 }"
            :maxlength="50"
            placeholder="请输入"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { useModal } from '@/hook/component/use-modal';
import { reactive } from 'vue';

const { open, close, visible, confirm, loading } = useModal();

const formState = reactive({
  remark: '',
  mm: '',
});

defineExpose({
  open,
});
</script>
