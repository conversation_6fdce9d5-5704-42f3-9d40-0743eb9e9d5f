<template>
  <div class="card-data-item">
    <div
      class="item-header"
      :style="{ ...titleStyle }"
    >
      {{ title }}
      <slot name="suffix" />
    </div>

    <div class="item-body">
      <a-statistic
        class="item-body__value"
        :precision="precision"
        :value="value"
        :value-style="{
          color: 'rgba(6, 21, 51, 0.85)',
          ...valueStyle,
        }"
      />
      <span class="item-body__unit">
        {{ unit }}
      </span>
      <span class="item-body__suffix">
        {{ valueSuffix }}
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  titleStyle: {
    type: Object,
    default: () => ({}),
  },
  value: {
    type: [String, Number],
    default: '',
  },
  valueStyle: {
    type: Object,
    default: () => ({}),
  },
  unit: {
    type: String,
    default: '',
  },
  valueSuffix: {
    type: String,
    default: '',
  },
  precision: {
    type: Number,
    default: 2,
  },
});
</script>

<style lang="less" scoped>
.card-data-item {
  padding: 16px;
  background: #f9f9fb;
  border-radius: 8px;
  .item-header {
    margin-bottom: 4px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  .item-body {
    display: flex;
    align-items: flex-end;
    &__value {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }
    &__unit {
      margin: 0 4px 0 2px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }
    &__suffix {
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
</style>
