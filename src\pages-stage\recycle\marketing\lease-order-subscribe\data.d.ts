// 租期
export type TenancyType = {
  tenancy_text?: string; // 租期
  time_start?: string; // 开始租期
  time_end?: string; // 结束租期
  tenancyTextStr?: string; // 天数
  lease_term_type_txt?: string; // 租期类型
  // 提醒发货
  remindDeliveryBtn?: {
    btn_text: string;
  };
  // 修改租期
  modifyLeaseTermBtn?: {
    btn_text: string;
    form_data: {
      item_id: string;
      time_start: string;
      time_end: string;
    };
  };
  // 续租操作
  continueRenewOperateBtn?: {
    btn_text: string;
    form_data: {
      renew_end: string;
      renew_start: string;
    };
  };

  // 查看归还物流
  viewLogisticsHref?: {
    btn_text: string;
    href: string;
  };
  // 是否需要发货弹窗
  sendTimeTip: string | number;
};

export type ActualPayType = {
  isBuyOutOrder: number;
  // 实付款
  payDetailBtn?: {
    btn_text: string;
  };
  // 实付款-详细按钮
  detailBtn?: {
    btn_text: string;
  };

  toggleList: {
    items?: string[]; // 已交租金收起展开列表
    text: string;
    href: string;
  };
  // 分账失败
  separateAccountFailList: {
    separateAccountBtn?: {
      btn_text: string;
      href: string;
    };
  };

  payDiscount: string[]; //红包 租赁券等
  // 支付宝免密代扣
  payType?: {
    text: string;
    title: string;
  };

  pay_date?: string; // 付款时间
  // 已授权代扣
  withHoldBtn?: {
    btn_text: string;
  };
  gradsBuyoutPrice?: string | number; // 买断价（梯度）
  pay_break_price?: string | number; // 买断价
  user_buy_out?: string; // 客户已买断
  buy_money?: number; // 购买金额
  deduction_status?: string; // 尚未扣款
  viewBillBtn: {
    btn_text: string;
    href: string;
    // 已交租金 有展开
    toggleList: string[];
  };
  already_pay_rent: string; // 已交租金 无展开
  already_refund_money?: string; // 已退款

  // 生成账单
  generateBillBtn?: {
    btn_text: string;
    status: number | string;
    form_data: {
      btnText: string;
      href: string;
      text: string;
      twice_deposit: number;
    };
  };
  // 非租金-开始
  total_pay_amount?: string;
  total_bill_amount?: string;
  total_refund_amount?: string;
  // 非租金-结束
};

// 商品基本信息类型
export type ItemDataType = {
  student_type_txt: string;
  id_card_tip: string;
  buttonList: BtnType[];
  deviceImeiListNew?: {
    imei: string;
    work_data: {
      type_text: string;
      type: number;
      work_id: number;
    };
  }[];
  deviceImeiList: string[]; // 设备串码
  quality_item_imei?: string; // IMEI

  item_cover: string; // 商品图片
  item_name: string; // 商品名称
  item_href: string; // // 商品详情路径
  qrCodeBtn: {
    // 二维码
    href: string;
  };
  sku_name: string; // 套餐
  studentAuthText?: string; // 学生认证资料
  // 编辑商品信息
  itemEditBtn?: {
    btn_text: string;
    href: string;
  };
  showTip?: boolean;
  order_id: string; // 订单id
  remind_send_status: number; // 是否已催发货
  urgent_send_status: number; // 加急发货标签显示状态
  view_shipment_log: number; // 是否可查看加急发货日志权限
  logisticsChangesTag: boolean | string; // 物流拦截tag标签
};

export type ReceivingDataType = {
  oneClickBtnVisible: boolean; // 点击一键查看后眼睛按钮状态
  name: string; // 用户姓名
  register_phone?: string; // 注册手机
  trip_type?: number;
  pick_car_address?: string; // 取车点地址
  return_car_address?: string; // 还车点地址
  third_address?: string; // 门店地址或供应商地址
  address_updated_at?: string;
  update_logs: IEditAddressRecord[];
  name_and_phone?: string;
  phone?: string; // 收货手机号
  // 收货手机号右边查看按钮
  lookReceivingPhoneBtn?: {
    btn_text: string;
  };
  // 注册手机号右边查看按钮
  lookRegisterPhone?: {
    btn_text: string;
  };
  address?: string; // 地址
  // 成功下单
  phoneSuccess?: {
    text: string;
    href: string;
  };
  // 累计下单
  phoneLeiJi?: {
    text: string;
    href: string;
  };
  // 身份证成功下单
  idCardSuccess?: {
    text: string;
    href: string;
  };
  // 身份证累计下单
  idCardLeiJi?: {
    text: string;
    href: string;
  };
  // 收货手机成功下单
  receivePhoneSuccess?: {
    text: string;
    href: string;
  };
  // 收货手机累计下单
  receivePhoneLeiJi?: {
    text: string;
    href: string;
  };
  // 区域1公里成功下单
  areaOneKilometerSuccess?: {
    text: string;
    href: string;
  };
  // 区域1公里累计下单
  areaOneKilometerLeiJi?: {
    text: string;
    href: string;
  };

  addOrderCount: {
    // 成功下单
    phoneSuccess?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
    // 累计下单
    phoneLeiJi?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
    // 身份证成功下单
    idCardSuccess?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
    // 身份证累计下单
    idCardLeiJi?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
    // 收货手机成功下单
    receivePhoneSuccess?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
    // 收货手机累计下单
    receivePhoneLeiJi?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
    // 区域1公里成功下单
    areaOneKilometerSuccess?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
    // 区域1公里累计下单
    areaOneKilometerLeiJi?: {
      text: string;
      href: string;
      requestParams: Record<string, string | number>;
    };
  };
};

export interface IEditAddressRecord {
  action_type: string;
  address: string;
  created_at: string;
  created_by: string;
  id: string;
  name: string;
  order_id: string;
  order_status: string;
  phone: string;
  type: string;
  updated_at: string;
  updated_by: string;
}

export type RentalType = {
  rentalNeedPay: string; // 需付租金
  rentalMoney?: {
    text: string;
    title: string;
  }; // 原租金
  gradData: {
    first_money?: string; // 梯度定价-首期租金
    last_money?: string; // 梯度定价-尾期租金
    gradRentalItem?: {
      month_rental: string;
      qi_shu: string;
    }[]; // 梯度定价-分期租金
    fenqi_first_money?: string; // 非梯度定价-分期首期
    fenqi_last_money?: string; // 非梯度定价-分期尾期
    surplus_each_money?: string; // 非梯度定价-剩下期数
    surplus_each_mum?: number;
  };

  wait_pay?: string;
  overdue_total_rent: string; // 逾期租金
  real_fenqi_num_txt: string; // 红包使用情况
  surplus_each_mum: number; // 剩余期数

  realFenqiNum?: number; // 超额分期数
  bargainDay?: string; // 砍价
  bargainMoney?: string; // 砍价
  // 申请退款按钮
  refundBtn?: {
    name: string;
    btn_text: string;
    form_data: Record<string, string>;
    texts: string[];
  };
  // xxx打折
  discountName?: {
    text: string;
    title: string;
  };
  // 参与活动
  activityData: {
    act_name: string;
    rental_reduce: number;
  }[];
  // 修改逾期租金
  modifyOverdueRentBtn: {
    btn_text: string;
    form_data: Record<string, any>;
  };
};

export type DepositType = {
  // 需付款/冻结押金
  deposit_need_pay_frozen?: string;
  credit_evaluate_quota?: number; // 信用评估额度
  depositTotal1?: { text: number; title: string }; // 订单总押金
  depositTotal2?: { text: number; title: string }; // 订单总押金
  evaluateQuotaDecDeposit?: { text: number; title: string }; // 资金预授权减押金
  platform_dec_deposit?: { text: number; title: string }; // 平台减免押金
  already_pay?: string; // 已支付
  partialPayAndThaw?: { text: string }; // 已转租金
  alreadyFreezeOrPay?: { text: string }; // 已转支付
  already_thaw?: string; // 已解冻
  alreadyThawOrRefund?: { text: string; title: string }; // 已退款或已解冻
  zhima_score?: string; // 用户芝麻分数
  depositGoods?: { text: string; title: string }; // 商品押金
  // 自如信用减押金
  creditNameDecDeposit?: {
    text: string;
    title: string;
  };

  // 预授权信息
  preAuthBtn: {
    btn_text: string;
    name?: string;
  };
  sfTransportServiceDetailBtn: {
    btn_text: string;
    name?: string;
  };
  jdTransportServiceDetailBtn: {
    btn_text: string;
    name?: string;
  };
  sfTransportBillWaitPayBtn: {
    btn_text: string;
    name?: string;
  };
  jdTransportBillWaitPayBtn: {
    btn_text: string;
    name?: string;
  };
  baasOrderDepositResumeBtn: {
    btn_text: string;
    name?: string;
  };
  // 从押金扣款
  freezeMoneyPayBtn: FreezeMoneyPayBtnType;
  // 重新冻结押金未完成
  depositUnfinishedBtn?: {
    btn_text: string;
  };
};

export type FreezeMoneyPayBtnType = {
  btn_text: string;
  form_data: {
    max_can_deposit_pay: number;
    rental_need_pay: number;
    texts: string[];
  };
};

export type OtherType = {
  freight: string; // 运费
  insurance_money: string; // 保险
  item_num: number; // 数量
  brokenScreenRecordBtn?: { btn_text: string }; // 碎屏保服务记录
  travelInsuranceBtn?: { btn_text: string }; // 骑行无忧
  qualityItemIsLockBtn?: { btn_text: string }; // 已安装锁机插件
};

// 基础数据
export type FoundationData = {
  is_fast_delivery: boolean;
  fast_delivery_rule: string; //极速发货规则
  deposit_has_pay: number;
  deposit_has_refund: number;
  deposit_money: number;
  order_id: string;
  order_status: number;
  pay_status: number;
  rental_has_pay: number;
  rental_has_refund: number;
  user_id: string;
  rental_need_pay: number;
  created_by: number;
  id_card: number;
  phone: number;
  acc_rental: number | null;
  rental_money: number;
  is_lenovo_order: boolean; // 联想租赁订单
  deposit_need_pay: number;
  mark_send_status: boolean; // 标记代发状态
  rental_service: boolean; // 租赁服务设备
  /** 公正是否通过结果 */
  cai_hua_pass_record: boolean;
  warehouse_delivery_imei_detail?: {
    imei_list: string[];
    type: string;
    work_data?: Record<string, { type: string; type_text: string; work_id: string }>;
  };
  mark_status: string;
  /** 是否是品牌门店订单 */
  is_third_party_store_order: boolean;
  /** 品牌门店订单信息 */
  third_party_store_order: {
    order_id: string;
    protocol_url: string;
    is_sync: string;
    store_name: string;
  };
};
