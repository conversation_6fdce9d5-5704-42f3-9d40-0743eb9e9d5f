<template>
  <CardDataItem
    tips-text="近7天内经过待入库状态的订单估价金额总和，按照端口来源进行区分：1.支付宝小程序2.微信小程序3.PC后台"
    title="各端GMV贡献情况"
  >
    <div class="date-text">
      {{ dateText }}
    </div>
    <a-table
      :columns="columns"
      :data-source="list || []"
      :pagination="false"
      :scroll="{ x: '100%' }"
      style="height: 338px"
    >
      <template #bodyCell="{ column, index }">
        <template v-if="column.dataIndex === 'index'">
          {{ index + 1 }}
        </template>
      </template>
    </a-table>
  </CardDataItem>
</template>

<script lang="ts" setup>
import { computed, PropType } from 'vue';
import { IGmvContributionRes, IGmvContributionInfo } from '../data';
import CardDataItem from './card-data-item.vue';

const props = defineProps({
  info: {
    type: Object as PropType<Partial<Omit<IGmvContributionRes, 'list'>>>,
    default: () => ({}),
  },
  list: {
    type: Array as PropType<IGmvContributionInfo[]>,
    default: () => [],
  },
});

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
  },
  {
    title: '平台类型',
    dataIndex: 'terminal_name',
    key: 'terminal_name',
  },
  {
    title: 'GMV',
    dataIndex: 'gmv',
    key: 'gmv',
    sorter: (a: any, b: any) => {
      const aGmv = a.gmv.replace(/,/g, '');
      const bGmv = b.gmv.replace(/,/g, '');
      return aGmv - bGmv;
    },
  },
  {
    title: '订单量',
    dataIndex: 'order_count',
    key: 'order_count',
    sorter: (a: any, b: any) => {
      const aOrderCount = a.order_count.replace(/,/g, '');
      const bOrderCount = b.order_count.replace(/,/g, '');
      return aOrderCount - bOrderCount;
    },
  },
];

const dateText = computed(() => {
  if (!Object.keys(props.info).length) return;
  const { start_at, end_at, date_between, update_time } = props.info;
  return `${start_at || '-- '}~${end_at || ' --'}${date_between ? ' | ' + date_between : ''} | 更新于${
    update_time || '--'
  }`;
});
</script>

<style lang="less" scoped>
.date-text {
  margin-bottom: 24px;
  color: #808080;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
}
:deep(.ant-table-tbody > tr > td) {
  padding: 25px 20px 24px 24px;
}
</style>
