<template>
  <a-modal
    v-model:visible="bindVisible"
    :body-style="{
      minHeight: '664px',
    }"
    :footer="null"
    title="修改记录"
    width="880px"
  >
    <a-table
      :columns="tableColumns"
      :data-source="tableData"
      :pagination="false"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useVModel } from '@/hook';
import { tableColumns } from './config';
import type { IEditAddressRecord } from './data.d';
import { fetchReceivingAddressLog } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const tableData = ref<IEditAddressRecord[]>([]);
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  fetchReceivingAddressLog({ order_id: props.orderId })
    .then(res => {
      tableData.value = res.data || [];
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
