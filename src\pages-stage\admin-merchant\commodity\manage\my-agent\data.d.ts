import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';

export interface SettingSetupData {
  searchFormGroup: FormGroupItem[];
  searchParams: {
    item_id: string;
    sku_id: string;
    category: string;
    server_id: string;
    merch_id: string;
    agent_ratio: string;
    status: string;
    created_at: string;
    updated_at: string;
  };
  columns: ColumnType[];
}
