/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-05-10 10:24:46
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-05-10 11:28:00
 * @FilePath: 获取搜索栏选择框数据
 * @Description:
 */
import { reactive } from 'vue';
import { CheckboxOptionType, RadioGroupProps, RadioProps } from 'ant-design-vue';
import { CheckboxValueType } from 'ant-design-vue/lib/checkbox/interface';

type TabelMap = Map<string | number, { label: string; text: string }>;

export type CheckOptionsType = {
  order_created_date: {
    time_params: string;
    time_relation: string;
  };
  assessOptions: CheckboxOptionType[];
  riskOptions: CheckboxOptionType[];
  modelOptions: RadioGroupProps['options'];
  inspection_dimension: RadioProps['value'][];
  risk_qualitative: CheckboxValueType[];
  spu_model_type: RadioProps['value'];
  riskMap: TabelMap;
  assessMap: TabelMap;
};

export default function (): CheckOptionsType {
  const checkOptions: CheckOptionsType = reactive({
    order_created_date: {
      time_params: '',
      time_relation: '',
    },
    assessOptions: [
      { label: '所有订单', value: 0 },
      { label: '常规考核', value: 1 },
      { label: '码商考核', value: 2 },
      { label: '转单', value: 4 },
      { label: '订单流转异常', value: 3 },
    ],
    assessMap: new Map([
      [0, { label: '所有订单', text: 'all' }],
      [1, { label: '常规考核', text: 'normal_sign' }],
      [2, { label: '码商考核', text: 'code_sign' }],
      [3, { label: '订单流转异常', text: 'order_uncircul_sign' }],
      [4, { label: '转单', text: 'transfer_sign' }],
    ]),
    inspection_dimension: [],
    riskOptions: [
      { label: '通过', value: '1' },
      { label: '预收', value: '2' },
      { label: '不通过', value: '3' },
      { label: '跳过', value: '5' },
      { label: '关闭', value: '4' },
      { label: '未知', value: '0' },
    ],
    risk_qualitative: [],
    riskMap: new Map([
      ['0', { label: '风控未知', text: 'unknown' }],
      ['1', { label: '风控通过', text: 'pass' }],
      ['2', { label: '风控预收', text: 'collect' }],
      ['3', { label: '风控不通过', text: 'no_pass' }],
      ['4', { label: '风控关闭', text: 'close' }],
      ['5', { label: '风控跳过', text: 'skip' }],
    ]),
    modelOptions: [
      { label: '主要型号', value: 1 },
      { label: '全部型号', value: 2 },
    ],
    spu_model_type: 1,
  });
  return checkOptions;
}
