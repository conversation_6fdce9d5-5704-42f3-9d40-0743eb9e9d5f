<template>
  <a-modal
    v-model:visible="bindVisible"
    class="work-order-modal"
    title="查看历史工单回执"
    :width="1000"
  >
    <a-table
      class="list"
      :columns="columns"
      :data-source="list"
      :loading="loading"
      :pagination="pagination"
      @change="handleChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button
              style="padding: 0"
              type="link"
              @click="openParentPage(domain + '/work-order/index?id=' + record.number)"
            >
              查看回单
            </a-button>
            <a-button
              v-if="['0', '1', '2', '3'].includes(record.status)"
              danger
              :disabled="Number(record.is_urgent) === 1"
              style="margin-left: 4px; padding: 0"
              type="link"
              @click="handleSetUrgentOrder(record.number)"
            >
              {{ Number(record.is_urgent) === 1 ? '已' : '' }}申请加急
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { h, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message, TableColumnType } from 'ant-design-vue';
import modal from 'ant-design-vue/lib/modal';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';

import type { WorkOrderListType } from './data';
import { getWorkOrderList, setUrgentOrder } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const list = ref<WorkOrderListType[]>([]);

const columns: TableColumnType[] = [
  {
    title: '工单编号',
    key: 'number',
    dataIndex: 'number',
  },
  {
    title: '类型',
    key: 'type_name',
    dataIndex: 'type_name',
  },
  {
    title: '发起人',
    key: 'initiator',
    dataIndex: 'initiator',
  },
  {
    title: '处理人',
    key: 'handle',
    dataIndex: 'handle',
  },
  {
    title: '提交时间',
    key: 'created_time',
    dataIndex: 'created_time',
  },
  {
    title: '关闭/完成时间',
    key: 'closed_time',
    dataIndex: 'closed_time',
  },
  {
    title: '状态',
    key: 'status_name',
    dataIndex: 'status_name',
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
  },
];

const emit = defineEmits(['update:visible']);
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const route = useRoute();
const domain = route.query.origin || window.location.origin;

const pagination = reactive({
  page: 1,
  page_size: 10,
  total: 0,
});

watch(
  () => bindVisible.value,
  value => {
    pagination.page = 1;
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function handleChange(params: any) {
  list.value = [];
  pagination.page = params.current;
  loadData();
}

function loadData() {
  loading.value = true;
  getWorkOrderList({
    ...pagination,
    order_id: props.orderId,
  })
    .then(res => {
      list.value = res.data?.list || [];
      pagination.total = res.data?.pageInfo?.totalPage || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

const openParentPage = (url: string) => {
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};

async function handleSetUrgentOrder(number: string) {
  try {
    await setUrgentOrder({ number });
    message.success('操作成功');
    loadData();
  } catch (err) {
    if (err.status === 4011) {
      modal.confirm({
        title: '提示',
        icon: h(ExclamationCircleOutlined),
        content: '您没有加急工单权益，需先申请',
        okText: '去申请',
        cancelText: '取消',
        onOk() {
          openParentPage(`${domain}/equity-application/index?type=introduction&id=work_order_urgent`);
        },
      });
    }
  }
}
</script>
