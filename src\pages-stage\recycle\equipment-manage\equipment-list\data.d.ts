export interface ISearchParams {
  //回收订单ID
  order_id: string | null;
  // 回收订单类别：0个人订单，1企业订单
  order_type?: string;
  // skuID
  sku_id: number | null;
  //sku详情
  sku_detail: string | null;
  //是否锁机：0否1是
  is_lock: number | null;
  // 入库商家ID
  server_id: number | null;
  // 回收来源
  recycle_from: string | null;
  // 销售去向
  sale_to: string | null;
  // 收件物流单号
  receive_logistics_no: string | null;
  // 寄件物流单号
  send_logistics_no: string | null;
  // imei字符串，多个用逗号分隔
  imei: string | null;
  // 设备sn
  sn: string | null;
  // 批次号
  batch_no: string | null;
  // created_at_gt: string | null;
  // created_at_lt: string | null;
  data_type?: string | null;
  timeType?: any[] | string | null;
  return_type: number | null;
  order_time?: any[] | null;
  afterSale_time?: any[] | null;
  recycle_time?: any[] | null;
}

export interface IOrderCountParams {
  normal_order: string;
  batch_order: string;
}

export interface IHideParams {
  order_id: string;
  order_type: string;
  field: string;
}

export interface IDateSelectTypeParams {
  label: string;
  value: string;
}
