<template>
  <a-modal
    v-model:visible="bindVisible"
    title="引导步骤"
    width="1104px"
    @cancel="handleConfirm"
    @ok="handleConfirm"
  >
    <a-carousel
      :after-change="handleAfterChange"
      arrows
      dots-class="custom-slick-dots"
      :infinite="false"
    >
      <template #prevArrow>
        <div
          :class="[
            'custom-slick-arrow',
            {
              disabled: current === 0,
            },
          ]"
          style="left: 10px"
        >
          <LeftCircleFilled />
        </div>
      </template>

      <template #nextArrow>
        <div
          :class="[
            'custom-slick-arrow',
            {
              disabled: current === 6,
            },
          ]"
          style="right: 10px"
        >
          <RightCircleFilled />
        </div>
      </template>

      <template
        v-for="stepItem in stepList"
        :key="stepItem.title"
      >
        <div class="carousel-item flex-wrap">
          <img
            class="guide-img"
            :src="stepItem.image"
          >
          <div class="guide-text flex-wrap flex-vertical flex-gap-16">
            <div class="title">
              {{ stepItem.title }}
            </div>
            <template v-if="Array.isArray(stepItem.description)">
              <div
                v-for="(text, index) in stepItem.description"
                :key="index"
              >
                {{ text }}
              </div>
            </template>
            <div v-else>
              {{ stepItem.description }}
            </div>
          </div>
        </div>
      </template>
    </a-carousel>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { LeftCircleFilled, RightCircleFilled } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:visible', 'close']);
const bindVisible = useVModel(props, 'visible', emits);
const current = ref(0);
const stepList = [
  {
    title: '规则指南',
    description: '规则指南可帮您快速熟悉业务哦！',
    image: 'https://img1.rrzuji.cn/uploads/scheme/2405/10/m/aYott7huZ8yK1yTDoZWf.png',
  },
  {
    title: '发货类型',
    description: ['平台代发：您可向平台采购设备，平台直接帮您发货给租户', '自行发货：您的自有货源可进行快递发货给租户'],
    image: 'https://img1.rrzuji.cn/uploads/scheme/2405/10/m/AtuazlvNpL0MPbrPUa7F.png',
  },
  {
    title: '租赁服务设备',
    description: '租赁服务指针对设备已安装租赁服务管理软件（锁机）',
    image: 'https://img1.rrzuji.cn/uploads/scheme/2405/10/m/TKlJnFTD7wN7qFXx0biW.png',
  },
  {
    title: '预估价',
    description: '采购后会按预估价（参考价）冻结余额，实际价格以设备出库时间为准,整理价格波动不会很大',
    image: 'https://img1.rrzuji.cn/uploads/scheme/2405/10/m/xGYK6uC3dZUsAMHhjW7J.png',
  },
  {
    title: '账户余额',
    description: '您的余额要大于勾选的设备预估价才能进行采购哦！',
    image: 'https://img1.rrzuji.cn/uploads/scheme/2405/10/m/39c58syD3xS7fHBki4jc.png',
  },
  {
    title: '平台服务协议',
    description: '请仔细阅读协议，同意后即可开始采购啦！',
    image: 'https://img1.rrzuji.cn/uploads/scheme/2405/10/m/yr9CRuR54WknV994n55f.png',
  },
  {
    title: '下发仓库',
    description: '确认后可在“设备订单”页查看您的采购订单哦！',
    image: 'https://img1.rrzuji.cn/uploads/scheme/2405/10/m/5dVlpetnF3hdN8jVsRyX.png',
  },
];

function handleAfterChange(index) {
  current.value = index;
}

function handleConfirm() {
  localStorage.setItem('_NEW_DELIVERY_FORM', true);
  bindVisible.value = false;
  emits('close');
}
</script>

<style scoped lang="less">
:deep(.slick-arrow.custom-slick-arrow) {
  z-index: 1;
  width: 35px;
  height: 35px;
  color: #b9bec8;
  font-size: 35px;
  transition: all 0.3s ease;

  &.disabled {
    color: rgb(227, 230, 235);
    cursor: not-allowed;
  }

  &::before {
    display: none;
  }

  &:hover {
    opacity: 0.7;
  }
}

:deep(.custom-slick-dots > li) {
  width: auto !important;

  & > button {
    width: 32px;
    height: 6px;
    background: rgba(6, 21, 51, 0.06);
    border-radius: 4px;
  }

  &.slick-active > button {
    background: var(--ant-primary-color);
  }
}

.carousel-item {
  display: inline-flex !important;
  align-items: center;
  padding-right: 95px;
  background: #f5f7fa;
  border-radius: 4px;

  & > .guide-img {
    width: 755px;
    height: 628px;
  }

  & > .guide-text {
    color: rgba(6, 21, 51, 0.65);
    font-size: 16px;
    line-height: 24px;

    .title {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 600;
      font-size: 24px;
      line-height: 36px;
    }
  }
}
</style>
