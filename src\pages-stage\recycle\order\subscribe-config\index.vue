<template>
  <layout-admin-page
    :navs="['趣回收', '订单管理', '预约回收规则配置']"
    title="预约回收规则配置"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="openDrawer('add')"
      >
        添加规则
        <template #icon>
          <plus-outlined />
        </template>
      </a-button>
    </template>
    <div class="container">
      <div class="search-bar">
        <a-form
          ref="formRef"
          layout="inline"
          :model="searchForm"
          style="width: 100%"
        >
          <a-form-item
            label="状态"
            name="status"
          >
            <a-select
              v-model:value="searchForm.status"
              :options="[
                { label: '启用中', value: '1' },
                { label: '停用中', value: '2' },
              ]"
              placeholder="请选择"
              style="width: 184px"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              style="margin-right: 8px"
              type="primary"
              @click="getTableList('search')"
            >
              查询
            </a-button>
            <a-button @click="onReset">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      <a-table
        class="bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: '1140px' }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'status'">
            <a-switch
              v-model:checked="record.statusValue"
              checked-children="启用"
              un-checked-children="停用"
              @change="(val:boolean) => onChangeStatus(val, index)"
            />
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <a
              style="color: #3777ff"
              @click="openDrawer('detail', record)"
            >查看</a>
            <a-popconfirm
              v-if="!record.statusValue"
              cancel-text="取消"
              ok-text="确定"
              title="你确定要删除吗?"
              @confirm="onDel(record)"
            >
              <a-button
                danger
                type="text"
              >
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
  <FixedDetailDrawer
    ref="fixedDetailDrawer"
    @table-load="getTableList"
  />
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useTable } from '@/hook/component/use-table';
import { IDataSourceItem } from './data.d';
import { columns } from './config';
import { switchStatus, ruleDel } from './service';
import FixedDetailDrawer from './components/fixed-detail-drawer.vue';

const formRef = ref<any>(null);
const fixedDetailDrawer = ref<any>(null);
const searchForm = reactive({
  status: null,
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  url: '/super/recycle/booking-recycle-rule/list',
  searchForm,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatHandle: (res: { data: { list: IDataSourceItem[] } }) => {
    const {
      data: { list },
    } = res;
    (list || []).forEach((item: IDataSourceItem) => {
      item.statusValue = item.status === '1';
    });
    return list;
  },
  pagination: {
    showTotal: (): string => {
      let totalPages = Math.ceil((page.total ?? 0) / (page.pageSize ?? 0));
      return `共 ${page.total} 条记录   第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const onReset = () => {
  formRef.value.resetFields();
  getTableList('search');
};

const openDrawer = (type: 'add' | 'detail', item?: IDataSourceItem) => {
  fixedDetailDrawer.value.onShow(type, item);
};

const onChangeStatus = async (val: boolean, index: number) => {
  dataSource.value[index].statusValue = !val;
  await switchStatus({
    id: dataSource.value[index].id,
    status: val ? '1' : '2',
  });
  getTableList();
};

const onDel = async (item: IDataSourceItem) => {
  const { id } = item;
  await ruleDel({ id });
  message.success('操作成功');
  getTableList();
};

getTableList();
</script>

<style lang="scss" scoped>
.container {
  padding: 0 24px;
  .search-bar {
    margin-bottom: 24px;
  }
}
</style>
