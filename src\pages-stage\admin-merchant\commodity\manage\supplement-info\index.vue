<script setup lang="ts">
import { computed, onMounted, ref, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { RadioChangeEvent } from 'ant-design-vue/lib/radio';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { isEmptyValue } from '@rrzu/utils';
import { cloneDeep, isArray, omit } from 'lodash-es';
import { RForm, RFormInstance, RPageFooter, useBoolean } from 'rrz-web-design';

import PageBreadcrumb from '@/components/breadcrumb/src/breadcrumb';
import { asyncConfirm } from '@/utils';

import { useCityData } from './composables/use-city-data';
import { formGroup, notRequiredKeys, TFormGroupItem } from './config';
import { getSupplementFormData, getSupplementInfo, postSubmitSupplementInfo } from './services';

const route = useRoute();
const router = useRouter();
const [loading, setLoading] = useBoolean();
const [submitLoading, setSubmitLoading] = useBoolean();

const { loadChineseDistricts, getCityData } = useCityData();

const formRef = ref<RFormInstance>();
const formState = ref({});
/** 已保存的数据源 */
const originSavedData = ref({});
/** 已提交的数据源 */
const originSubmittedData = ref({});

const dynamicFormGroup = shallowRef<TFormGroupItem[]>([]);

const formGroupWithExtra = computed(() => dynamicFormGroup.value.filter(t => t.extraInputKey));

/** 进度条 */
const filledPercent = ref(0);

/** 计算进度条 */
watch(
  () => [formState.value, dynamicFormGroup.value],
  async () => {
    const arr = await Promise.allSettled(
      dynamicFormGroup.value
        .filter(item => item.display(formState.value, []) && item.originProps?.rules)
        .map(item => item.originProps.rules?.[0].validator(undefined, formState.value[item.key])),
    );
    const fulfilled = arr.filter(r => r.status === 'fulfilled').length;
    const rejected = arr.filter(r => r.status === 'rejected').length;
    filledPercent.value = Math.ceil((fulfilled / (fulfilled + rejected)) * 100);
  },
  { immediate: true, deep: true },
);

/** 获取表单的规则 */
function getRules(formGroupItem: TFormGroupItem) {
  const { key, fragmentKey, label, extraInputKey, extraInputMatch } = formGroupItem;
  // 非必填项不需要rules
  if (notRequiredKeys.includes(key)) return;
  /** 选项式表单 */
  const selectFragment = ['renderRadio', 'renderCheckbox', 'renderCascader', 'renderSelect'];
  return [
    {
      required: true,
      validator: (_, value) => {
        if (!value || (isArray(value) && !value.length)) {
          return Promise.reject(`请${selectFragment.includes(fragmentKey) ? '选择' : '输入'}${label}`);
        }
        if (key === 'model_accept_intention' && value.length < 4) {
          return Promise.reject('最少选择4项');
        }
        const valueMatch = isArray(value) ? value.includes(extraInputMatch) : value === extraInputMatch;
        if (extraInputKey && valueMatch && !formState.value[extraInputKey]) {
          return Promise.reject('请先完善输入');
        }
        return Promise.resolve();
      },
    },
  ];
}

/**
 * 动态生成formGroup
 * @param formGroup formGroup配置
 * @param formData 字段权限配置
 * @param formMap 字段选项配置
 * @param displayFn 表单显示条件
 */
function formGroupMapper(formGroup: TFormGroupItem[], formData, formMap, displayFn?) {
  const targetFormGroup = [];
  formGroup.forEach(formGroupItem => {
    const {
      key,
      fragmentKey,
      originProps = {},
      elProps = {},
      extraInputKey,
      extraInputMatch,
      childrenDisplay,
      children,
    } = formGroupItem;
    const { is_read, is_edit } = formData[key] || {};

    function display(state) {
      return (is_read === 1 || is_edit === 1) && (!displayFn || displayFn?.(state));
    }

    function onChange(checkedValue) {
      if (!extraInputKey) return;
      if (fragmentKey === 'renderRadio') {
        formState.value[extraInputKey] = undefined;
      }
      if (fragmentKey === 'renderCheckbox') {
        if (formState.value[key]?.includes(extraInputMatch) !== checkedValue?.includes(extraInputMatch)) {
          formState.value[extraInputKey] = undefined;
        }
        formState.value[key] = checkedValue;
      }
    }

    function onClick(e: RadioChangeEvent) {
      if (fragmentKey !== 'renderRadio') return;
      if (e.target.value === formState.value[key]) {
        formState.value[key] = undefined;
      }
    }

    targetFormGroup.push({
      ...formGroupItem,
      options: formMap[key],
      display,
      originProps: {
        ...originProps,
        rules: is_edit === 0 ? undefined : getRules(formGroupItem),
        style: displayFn ? { marginLeft: '24px' } : undefined,
      },
      elProps: {
        ...elProps,
        options: formMap[key],
        disabled: is_edit === 0,
        onChange,
        onClick,
      },
    });
    if (children?.length) {
      targetFormGroup.push(...formGroupMapper(children, formData, formMap, childrenDisplay));
    }
  });
  return targetFormGroup;
}

function getFormattedForm() {
  const result: Record<string, any> = {};
  for (const formStateKey in formState.value) {
    if (['work_city', 'work_county'].includes(formStateKey)) continue;
    const value = formState.value[formStateKey];
    if (isArray(value)) {
      if (formStateKey === 'work_province') {
        [result.work_province, result.work_city, result.work_county] = value;
      } else result[formStateKey] = value.join(',');
    } else result[formStateKey] = value;
  }
  return result;
}

function isSameValue(a, b) {
  return a === b || (isEmptyValue(a) && isEmptyValue(b));
}

function getDifferentParams(origin, target) {
  const result: Record<string, any> = {};
  for (const reqInfoKey in target) {
    if (!isSameValue(target[reqInfoKey], origin[reqInfoKey])) {
      result[reqInfoKey] = target[reqInfoKey];
    }
  }
  return result;
}

/**
 * 保存数据
 * @param port 提交端口，2-提交到运营后台 3-商家自行保存
 * @param disableMessage 是否禁用message提示，用于静默保存
 */
async function executeSubmit(port: '2' | '3', disableMessage = false) {
  try {
    const originState = port === '3' ? originSavedData.value : originSubmittedData.value;
    const reqParams = getDifferentParams(originState, getFormattedForm());
    const hasChange = Object.keys(reqParams).length > 0;
    if (!hasChange) {
      !disableMessage && message.warn('没有改动');
      return Promise.reject('没有改动');
    }
    setSubmitLoading(true);
    await postSubmitSupplementInfo({ port, edit_channel: '21', ...reqParams });
    !disableMessage && message.success('操作成功');
  } finally {
    setSubmitLoading(false);
  }
}

/** 返回上一页 */
async function handleCancel() {
  const reqParams = getDifferentParams(originSavedData.value, getFormattedForm());
  let needAction = Object.keys(reqParams).length > 0;
  if (needAction) {
    needAction = await asyncConfirm({
      title: '提示',
      content: '关闭后是否对填写的资料进行保存？',
    });
  }
  if (needAction) executeSubmit('3', true);
  router.push({
    path: '/merchant/commodity/manage/store-info',
    query: { ...route.query, tabKey: 'supplementInfo' },
  });
}

async function handleSave() {
  formRef.value?.getFormRef().clearValidate();
  await executeSubmit('3');
  getPageDetail();
}

/** 提交动作 */
async function handleConfirm() {
  try {
    setSubmitLoading(true);
    await formRef.value?.getFormRef().validate();
    if (!(await asyncConfirm({ title: '提示', content: '确定提交商家经营档案信息？' }))) return;
    await executeSubmit('2');
    await executeSubmit('3', true);
    getPageDetail();
  } catch (err) {
    const elementId = err.errorFields?.[0]?.name?.[0] as string;
    const element = document.getElementById(`form_item_${elementId}`) as HTMLElement;
    if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  } finally {
    setSubmitLoading(false);
  }
}

function formatOriginData(data) {
  const result: Record<string, any> = {};
  for (const key in data) {
    result[key] = data[key]?.value;
  }
  return result;
}

/** 获取数据回显 */
function getPageDetail() {
  setLoading(true);
  Promise.all([
    getSupplementFormData(),
    getSupplementInfo({ edit_channel: '21', port: '3' }),
    getSupplementInfo({ edit_channel: '21', port: '2' }),
    loadChineseDistricts(),
  ])
    .then(([{ data: formGroupConfig }, { data: savedData }, { data: submittedData }]) => {
      formGroupConfig.work_province = getCityData();
      dynamicFormGroup.value = formGroupMapper(cloneDeep(formGroup), savedData, formGroupConfig);
      originSavedData.value = formatOriginData(savedData);
      originSubmittedData.value = formatOriginData(submittedData);
      // 表单回填
      const formInfo = cloneDeep(originSavedData.value);
      const { work_province, work_county, work_city } = formInfo;
      if (work_province && work_county && work_city) {
        formInfo.work_province = [work_province, work_city, work_county];
      }
      dynamicFormGroup.value.forEach(item => {
        if (item.fragmentKey === 'renderCheckbox' && formInfo[item.key]) {
          formInfo[item.key] = formInfo[item.key].split(',');
        }
      });
      formState.value = formInfo;
    })
    .finally(() => setLoading(false));
}

onMounted(() => {
  getPageDetail();
});
</script>

<template>
  <div class="page-top">
    <page-breadcrumb :items="['店铺', '信息库', '商家经营档案']" />
    <div class="page-title flex-wrap flex-y-center gap-12">
      <ArrowLeftOutlined @click="handleCancel" />
      <span>你正在修改商家经营档案信息</span>
    </div>
  </div>

  <a-spin :spinning="loading">
    <div class="page-content">
      <div class="sticky-progress">
        <div class="tips">
          当前信息完整度：
        </div>
        <a-progress :percent="filledPercent" />
      </div>

      <div class="form-container">
        <RForm
          ref="formRef"
          v-model:value="formState"
          :form-group="dynamicFormGroup"
        >
          <template
            v-for="item in formGroupWithExtra"
            :key="item.key"
            #[item.key]
          >
            <a-form-item
              v-bind="item.originProps"
              :label="item.label"
              :name="item.key"
            >
              <a-radio-group
                v-if="item.fragmentKey === 'renderRadio'"
                v-model:value="formState[item.key]"
                v-bind="omit(item.elProps, ['options'])"
              >
                <template
                  v-for="opt in item.elProps?.options"
                  :key="opt.value"
                >
                  <a-radio :value="opt.value">
                    <span>{{ opt.label }}</span>
                    <a-form-item-rest>
                      <a-input
                        v-if="formState[item.key] === item.extraInputMatch && opt.value === item.extraInputMatch"
                        v-model:value="formState[item.extraInputKey]"
                        :disabled="item.elProps?.disabled"
                        maxlength="20"
                        :placeholder="item.extraInputLabel || '请输入'"
                        style="width: 180px; margin-left: 8px"
                        @change="formRef?.getFormRef().clearValidate(item.key)"
                      />
                    </a-form-item-rest>
                  </a-radio>
                </template>
              </a-radio-group>
              <a-checkbox-group
                v-if="item.fragmentKey === 'renderCheckbox'"
                v-bind="omit(item.elProps, ['options'])"
                :value="formState[item.key]"
              >
                <template
                  v-for="opt in item.elProps?.options"
                  :key="opt.value"
                >
                  <a-checkbox :value="opt.value">
                    <span>{{ opt.label }}</span>
                    <a-form-item-rest>
                      <a-input
                        v-if="formState[item.key]?.includes(item.extraInputMatch) && opt.value === item.extraInputMatch"
                        v-model:value="formState[item.extraInputKey]"
                        :disabled="item.elProps?.disabled"
                        maxlength="20"
                        :placeholder="item.extraInputLabel || '请输入'"
                        style="width: 180px; margin-left: 8px"
                        @change="formRef?.getFormRef().clearValidate(item.key)"
                      />
                    </a-form-item-rest>
                  </a-checkbox>
                </template>
              </a-checkbox-group>
            </a-form-item>
          </template>
        </RForm>
      </div>
    </div>
  </a-spin>

  <RPageFooter
    :cancel-button-props="{ loading: submitLoading }"
    cancel-button-text="保存"
    :confirm-button-props="{ loading: submitLoading }"
    position="fixed"
    show-cancel-button
    show-confirm-button
    style="z-index: 99"
    @cancel="handleSave"
    @confirm="handleConfirm"
  >
    <template #leftButton>
      <a-button
        class="footer-button"
        size="large"
        style="margin-right: 16px"
        @click="handleCancel"
      >
        取消
      </a-button>
    </template>
  </RPageFooter>
</template>

<style scoped lang="less">
.flex-inline-wrap {
  display: inline-flex;
}

.page-top {
  margin: 16px;
  background: #fff;

  .page-title {
    padding: 12px 24px 16px;
    color: rgba(6, 21, 51, 0.85);
    font-size: 20px;

    .anticon {
      font-size: 16px;
      cursor: pointer;
    }
  }
}

.page-content {
  min-height: 360px;
  margin: 16px;
  padding: 0 24px;
  background: #fff;

  .sticky-progress {
    position: sticky;
    top: 0;
    z-index: 9;
    margin: 0 auto;
    padding: 24px 0;
    background: #fff;
    border-bottom: 1px solid #f1f2f3;
  }

  .form-container {
    width: 800px;
    margin: 0 auto;
    padding-top: 24px;
  }
}

.footer-button {
  min-width: 104px;
  height: 40px;
  padding: 7px 32px;
  font-size: 16px;
  border-radius: 4px;
}
</style>
