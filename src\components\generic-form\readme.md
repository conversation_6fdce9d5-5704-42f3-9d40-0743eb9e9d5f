### GenericForm 通用表单组件

`GenericForm` 是基于 `RModalForm` 组件结合后端 api 封装的通用表单处理组件，传入 `formId` (表单 id) 、 `id` (表单数据 id) 和 `modalType` (表单类型) 可以进行添加、编辑和查看表单数据。

#### 代码演示

```vue
<template>
  <div>
    <GenericForm :id="id" v-model:open="open" :form-id="formId" :modal-type="modalType" :title-prefix="titlePrefix" />
    <a-button @click="add"> 新增 </a-button>
    <a-button @click="edit"> 编辑 </a-button>
    <a-button @click="preview"> 预览 </a-button>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

import GenericForm from '@/components/generic-form/generic-form.vue';

// formId 和 id 用于演示用，一般是后端返回，如果有特殊情况需要前端定义，请在 '@/composables/generic-form/generic-form-id.ts' 中统一定义
const formId = ref(2);
const id = ref('67a70fd2395418192027e7c1');
const open = ref(false);
const titlePrefix = ref('新增');
const modalType = ref('add');

function add() {
  open.value = true;
  titlePrefix.value = '新增';
  modalType.value = 'add';
}

function edit() {
  open.value = true;
  titlePrefix.value = '编辑';
  modalType.value = 'edit';
}

function preview() {
  open.value = true;
  titlePrefix.value = '预览';
  modalType.value = 'view';
}
</script>
```

#### 组件属性

| 属性名      | 类型    | 默认值 | 必填 | 描述                |
| ----------- | ------- | ------ | ---- | ------------------- |
| open        | boolean | false  | 是   | 弹窗显隐（v-model） |
| formId      | number  | -      | 是   | 表单 ID             |
| id          | string  | -      | 否   | 表单数据 ID         |
| modalType   | string  | 'add'  | 否   | 弹窗类型            |
| titlePrefix | string  | -      | 否   | 弹窗标题前缀        |

<!-- ## 组件事件 -->

<!-- ## 组件插槽 -->

<!-- ## 组件示例导出 -->
