import { ITreeItem } from '@/pages-stage/recycle/base-data/valuation-model/data';
import { loadChineseDistricts } from '@/utils/static-load';

type TNumberKeyObject = {
  [key: string]: string;
};

interface ITreeItem {
  label: string;
  value: string;
  union_id?: string | number;
  children?: ITreeItem[];
}

export function useCityData() {
  function formatCityData(cityData: TNumberKeyObject, prefix: string, level: number): ITreeItem[] {
    const result: ITreeItem[] = [];
    const keys: string[] = Object.keys(cityData);
    keys.forEach((key: string) => {
      const item: ITreeItem = {
        label: cityData[key],
        value: cityData[key],
        union_id: key,
      };
      if (window['ChineseDistricts'][key] && level < 3) {
        item.children = formatCityData(window['ChineseDistricts'][key], item.value, level + 1);
      }
      result.push(item);
    });
    return result;
  }

  /**
   * 使用之前请确保loadChineseDistricts已经正确请求
   */
  function getCityData() {
    if (!window['ChineseDistricts']) return [];
    return formatCityData(window['ChineseDistricts'][86], '', 1);
  }

  return { loadChineseDistricts, getCityData };
}
