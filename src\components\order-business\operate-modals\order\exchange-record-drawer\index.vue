<template>
  <a-drawer
    :closable="false"
    destroy-on-close
    title="订单货品更换记录"
    :visible="bindVisible"
    width="800"
    @close="bindVisible = false"
  >
    <template #extra>
      <CloseOutlined @click="bindVisible = false" />
    </template>
    <a-timeline>
      <a-timeline-item
        v-for="item in goodsInfoList"
        :key="item.id"
      >
        <div class="header">
          <div class="title">
            {{ item.change_type_txt }}
          </div>
          <a-tag
            v-if="item.send_up"
            color="blue"
            style="margin: 0 0 0 8px"
          >
            下发升级
          </a-tag>
          <span class="time">{{ item.created_at }}</span>
          <span class="name">{{ item.created_by }}</span>
          <span
            v-if="['换货申请', '库内换货'].includes(item.change_type_txt)"
            class="link-text"
            @click="openChangeGoodsModal(item.created_time)"
          >
            换货单<RightOutlined style="color: #3777ff" />
          </span>
        </div>
        <div class="content-card">
          <div class="item">
            <span class="new-tag">新</span>
            <div class="text">
              {{ item.new_sku_id }} | {{ item.new_sku_info }}
            </div>
          </div>

          <div
            class="item"
            style="padding: 12px 0 0; border: none"
          >
            <span class="old-tag">旧</span>
            <div
              class="text"
              style="color: rgba(6, 21, 51, 0.65)"
            >
              {{ item.sku_id }} | {{ item.sku_info }}
            </div>
          </div>
        </div>
      </a-timeline-item>
    </a-timeline>

    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <a-button
          style="margin-left: 8px"
          type="primary"
          @click="bindVisible = false"
        >
          确认
        </a-button>
      </div>
    </template>
  </a-drawer>

  <ChangeGoodsModal
    v-model:visible="changeGoodsModalVisible"
    :extra-data="{ created_time: recordCreatedTime }"
    :order-id="orderId"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { CloseOutlined, RightOutlined } from '@ant-design/icons-vue';

import ChangeGoodsModal from '@/components/order-business/operate-modals/order/change-goods-modal/index.vue';
import { useVModel } from '@/hook';

import { IGoodsInfo } from './data';
import { getOrderSkuChange, superGetOrderSkuChange } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emits);

const route = useRoute();
const isSuper = route.query.role === 'super';

const goodsInfoList = ref<IGoodsInfo[]>([]);

watch(
  bindVisible,
  (val: boolean) => {
    if (val) {
      const api = isSuper ? superGetOrderSkuChange : getOrderSkuChange;
      api(props.orderId).then(res => {
        goodsInfoList.value = res?.data || [];
      });
    }
  },
  { immediate: true },
);

// ================== 换货单弹窗 ====================
const changeGoodsModalVisible = ref(false);
const recordCreatedTime = ref('');

/** 打开换货单弹窗 */
function openChangeGoodsModal(created_time: string) {
  changeGoodsModalVisible.value = true;
  recordCreatedTime.value = created_time;
}
</script>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  padding-bottom: 8px;

  .title {
    color: #061533;

    font-weight: 500;
  }

  .time,
  .name {
    padding-left: 8px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;

    font-size: 12px;
  }

  .link-text {
    margin-left: auto;
    color: #3777ff;
    font-weight: 400;
    cursor: pointer;
  }
}

.content-card {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .item {
    display: flex;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(6, 21, 51, 0.06);

    .new-tag,
    .old-tag {
      padding: 2px 4px;
      color: #00c8be;
      font-weight: 400;

      font-size: 12px;
      text-align: center;
      background-color: #ebfafa;
      border: 1px solid #7fe0da;
      border-radius: 4px;
    }

    .old-tag {
      color: rgba(6, 21, 51, 0.65);
      background-color: rgba(6, 21, 51, 0.04);
      border: 1px solid rgba(6, 21, 51, 0.15);
    }

    .text {
      padding-left: 10px;
      color: #061533;
      font-weight: 400;
    }
  }
}
</style>
