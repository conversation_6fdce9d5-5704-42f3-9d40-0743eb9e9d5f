<template>
  <layout-admin-page
    :navs="['趣回收', '模型管理', '模型校验', '模型详情']"
    :title="`模型${isEdit ? '编辑' : '详情'}`"
  >
    <template #title-prefix>
      <ArrowLeftOutlined
        style="margin-right: 16px; font-size: 16px"
        @click="router.go(-1)"
      />
    </template>
    <template
      v-if="isEdit"
      #extra
    >
      <a-button
        style="margin-right: 8px; border-radius: 4px"
        @click="router.go(-1)"
      >
        取消
      </a-button>
      <a-button
        style="border-radius: 4px"
        type="primary"
        @click="onSave"
      >
        <template #icon>
          <save-outlined />
        </template>

        保存
      </a-button>
    </template>
    <a-spin :spinning="spinning">
      <div class="form-wrap">
        <div class="title">
          基本信息
        </div>
        <div class="base-info">
          <div>
            <span class="info-name">模型名称：</span><span class="info-model">{{ formState.model_name }}</span>
          </div>
          <div class="info-block">
            <div class="info-aside">
              <span class="info-name">&nbsp;&nbsp;&nbsp;&nbsp;模型ID：</span><span class="info-content">{{ formState.model_key }}</span>
            </div>
            <div class="info-aside">
              <span class="info-name">最新版本：</span> <span class="info-content">{{ formState.model_version }}</span>
            </div>
          </div>
          <div class="info-block">
            <div class="info-aside">
              <span class="info-name">创建时间：</span> <span class="info-content">{{ formState.created_at }}</span>
            </div>
            <div class="info-aside">
              <span class="info-name">更新时间：</span><span class="info-content">{{ formState.updated_at }}</span>
            </div>
          </div>
          <div class="info-description">
            <span class="info-name"> 模型描述： </span>
            <span
              v-if="!isEdit"
              class="info-content"
            >{{ formState.model_description }}</span>
            <a-textarea
              v-else
              v-model:value="formState.model_description"
              class="model-description"
              :maxlength="500"
              placeholder="请输入"
              show-count
            />
          </div>
        </div>
        <div class="title">
          历史版本
        </div>
        <a-table
          class="bottom-fix-table"
          :columns="modelDetailColumns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="page"
          :scroll="{ x: '100%', y: '100%' }"
          @change="tableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'model_version'">
              <div>
                <a-tag>{{ record.model_version }}</a-tag>
              </div>
            </template>
            <template v-if="column.key === 'runtime_num'">
              <div>
                <span
                  :class="{ active: record.runtime_num !== 0 }"
                  @click="onHandleLog(record.id, record.runtime_num)"
                >{{ record.runtime_num }}次</span>
              </div>
            </template>
            <template v-if="column.key === 'operator'">
              <div class="operator-box">
                <a
                  style="color: #3777ff"
                  @click="onHandleCheck(record.id)"
                > 补货校验数据下载 </a>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </a-spin>
    <ExecLogModal
      v-if="logModalVisible"
      v-model:visible="logModalVisible"
      :version-id="versionId"
    />
    <DownloadModal
      v-if="downloadModalVisible"
      v-model:visible="downloadModalVisible"
      :version-id="versionId"
    />
  </layout-admin-page>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons-vue';
import { useTable } from '@/hook/component/use-table';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import { modelDetailColumns } from '../config';
import { getModelDetail, updateModelDetail, getVersionList } from '../service';
import ExecLogModal from './exec-log-modal.vue';
import DownloadModal from './download-modal.vue';

const router = useRouter();
const route = useRoute();

const [logModalVisible, { setTrue: execLogModal }] = useBoolean();
const [downloadModalVisible, { setTrue: downloadModal }] = useBoolean();

const isEdit = ref<boolean>(false);

const spinning = ref<boolean>(false);

const versionId = ref<string>(); //版本id

const formState = ref({
  model_name: '',
  model_key: '',
  model_version: '',
  created_at: '',
  updated_at: '',
  model_description: '',
});

const getBaseInfo = async () => {
  if (route.query.id) {
    const { data } = await getModelDetail({ id: route.query.id });
    formState.value = data ?? {};
  }
};

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  api: getVersionList,
  totalKey: 'data.pageInfo.count',
  formatSearchValue: () => {
    const queryParams = {
      model_id: route.query.id,
    };
    return queryParams;
  },
  pagination: {
    pageSize: 3,
    pageSizeOptions: ['3'],
  },
});

const onSave = async () => {
  await updateModelDetail({ id: route.query.id, description: formState.value.model_description });
  message.success('保存成功');
};
const onHandleLog = (id: string, num: number) => {
  if (num === 0) {
    return;
  }
  versionId.value = id;
  execLogModal();
};
const onHandleCheck = (id: string) => {
  versionId.value = id;
  downloadModal();
};

onMounted(() => {
  isEdit.value = route.query.type === 'edit' ? true : false;
  getBaseInfo();
  getTableList('search');
});
</script>
<style scoped lang="less">
.title {
  position: relative;
  display: flex;
  align-items: center;
  margin: 24px 0 16px 0;
  margin-right: 8px;
  padding-left: 12px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 4px;
    height: 16px;
    background: #00c8be;
    border-radius: 2px;
    transform: translateY(-50%);
    content: '';
  }
}
.form-wrap {
  padding: 0 24px 24px;
  background-color: #ffff;
  .active {
    color: #3777ff;
    cursor: pointer;
  }
}

.base-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 250px;
  padding: 24px;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  background: #f5f7fa;
  border-radius: 4px;
  .info-name {
    color: rgba(6, 21, 51, 0.45);
  }
  .info-content {
    color: rgba(6, 21, 51, 0.65);
  }
  .info-model {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
  }
  .info-block {
    display: flex;
    align-items: center;
    .info-aside {
      flex: 1;
      align-items: center;
    }
  }
  .info-description {
    display: flex;
    align-items: flex-start;
    .model-description {
      flex: 1;
      box-sizing: border-box;
      height: 82px;
    }
  }
}
</style>
