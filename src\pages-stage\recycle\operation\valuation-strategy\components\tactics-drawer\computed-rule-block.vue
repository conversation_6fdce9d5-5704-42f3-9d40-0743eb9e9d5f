<template>
  <div class="computed-rule-block">
    <HeaderBlock>
      <div class="handle-slot">
        <div class="handle-item">
          <div>
            计算规则：按所选
            <a-select
              v-model:value="goodsType"
              :disabled="!isEdit"
              :options="ruleOptions"
              placeholder="请选择规则"
              style="width: 114px; margin: 0 4px"
            />
          </div>
          <div>价格，直接加/减/乘/除已输入数值。</div>
        </div>
        <div class="handle-item">
          <div>调价方案：</div>
          <a-select
            v-model:value="schemeValue"
            :disabled="!isEdit"
            :options="schemeOptions"
            placeholder="请选择"
            style="width: 114px; margin-left: 4px"
            @change="changeScheme"
          />
        </div>
      </div>
    </HeaderBlock>
    <!-- 仅仅一个占位间距。减少对HeaderBlock多一层容器包裹去设置下间距 -->
    <div class="placeholder-block" />
    <div v-show="goodsType === '1'">
      <TableElementSpu
        ref="tableElementSpu"
        :goods-id="goodsId"
        :is-use-tactics="schemeValue !== 0"
        :options-manage="optionsManage"
        :table-data="spuTableData"
        @validator-table="validatorSkunSettingResult"
      />
    </div>
    <div v-show="goodsType === '2'">
      <TableElementSku
        ref="tableElementSku"
        :goods-id="goodsId"
        :is-use-tactics="schemeValue !== 0"
        :options-manage="optionsManage"
        :table-data="skuTableData"
        @add-item="addSkuTableItem"
        @del-item="delSkuTableItem"
        @validator-table="validatorSkunSettingResult"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, inject, h, PropType, Ref, onMounted } from 'vue';
import { notification } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';
import { checkSkuSetting, getDetailData, getPriceSchemeList } from '../../service';
import { IComputedTableDataSource, IOpenDrawerParams, ITableDetail, ITableSelectManageMap } from '../../data.d';
import useSkuTable from '../../composables/use-sku-table';
import HeaderBlock from './header-block.vue';
import TableElementSpu from './table-element-spu.vue';
import TableElementSku from './table-element-sku.vue';

const isEdit = inject('isEdit');
const settingType = inject('settingType') as Ref<string>;

const props = defineProps({
  goodsId: {
    type: String,
    default: '',
  },
  detailRes: {
    type: Object as PropType<Partial<ITableDetail>>,
    default: () => ({}),
  },
  // 弹窗内容固定需要的参数
  initParams: {
    type: Object as PropType<Omit<IOpenDrawerParams, 'goods_name'>>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:schemeType', 'onLoadList']);

const tableElementSpu = ref<any>(null);
const tableElementSku = ref<any>(null);

const goodsType = ref<string>('1');
const ruleOptions = [
  { label: '商品', value: '1' },
  { label: '商品sku', value: '2' },
];

const schemeValue = ref(0);
const schemeOptions = ref([]);

const tableRef = computed(() => {
  return goodsType.value === '1' ? tableElementSpu.value : tableElementSku.value;
});

const optionsManage = ref<ITableSelectManageMap>({
  base1_list: [],
  base2_list: [],
  price_type: [],
  change_price_type: [],
  arithmetic_list: [],
});
function setOptionsManage(data: Partial<ITableDetail> = {}) {
  const keysArr = Object.keys(optionsManage.value);
  keysArr.forEach(key => {
    optionsManage.value[key] = data[key] || [];
  });
}

async function validatorSkunSettingResult(skuList: IComputedTableDataSource[]) {
  // 没有符合的sku则不进行请求
  if (!skuList.length) {
    notification.destroy();
    return;
  }
  const params = {
    goods_id: props.goodsId,
    goods_type: goodsType.value,
    setting_type: settingType.value,
    auto_evaluate_price_type: schemeValue.value,
    sku_list: skuList || [],
  };
  const res = await checkSkuSetting(params);
  const { less_ai_recycle_price, more_sale_price } = res.data || {};
  const lessAiRecycleText = createSettingResultText(less_ai_recycle_price);
  const moreSaleText = createSettingResultText(more_sale_price);
  notification.destroy();
  [lessAiRecycleText, moreSaleText]
    .filter(item => item)
    .forEach(item => {
      notification.open({
        message: '调价风险提醒',
        description: item,
        placement: 'topLeft',
        duration: 0,
        icon: () => h(InfoCircleOutlined),
        style: {
          width: '480px',
        },
      });
    });
}

function createSettingResultText(data: { list: string[]; msg: string }) {
  const { list, msg } = data;
  const text = list
    .map(item => {
      return `【${item}】`;
    })
    .join('');
  return text ? text + msg : '';
}

// 商品维度表格
const spuTableData = ref<IComputedTableDataSource[]>([]);

const { skuTableData, delSkuTableItem, addSkuTableItem } = useSkuTable();

const changeScheme = (value: any) => {
  emits('update:schemeType', value);
  emits('onLoadList');
};

watch(
  () => props.detailRes,
  val => {
    const { goods_type, auto_evaluate_price_type, sku_list = [] } = val;
    goodsType.value = goods_type || '1';
    schemeValue.value = Number(auto_evaluate_price_type) || 0;
    emits('update:schemeType', schemeValue.value);
    // 详情表格需克隆给【商品、商品sku】表格单独使用
    spuTableData.value = cloneDeep(sku_list);
    skuTableData.value = cloneDeep(sku_list);
    setOptionsManage(val);
    // 有可能当前版本表格数据sku不全，所以当前编辑的版本选择的是商品sku时，需对商品规则表格进行获取完整的sku表格信息
    if (goodsType.value === '2') {
      getDefaultSkuList();
    }
  },
);

async function getDefaultSkuList() {
  const res = await getDetailData({ ...props.initParams });
  const sku_list = res.data.sku_list || [];
  spuTableData.value = cloneDeep(sku_list);
}

function validator() {
  return tableRef.value?.validator().then(() => {
    const skuList = cloneDeep(goodsType.value === '1' ? spuTableData.value : skuTableData.value);
    // 二分法模型需要把数值字段变为0
    if (schemeValue.value === 1) {
      skuList.forEach((item: IComputedTableDataSource) => {
        item.sku_base_price = 0;
      });
    }
    const submitParams = {
      goods_type: goodsType.value,
      auto_evaluate_price_type: schemeValue.value,
      sku_list: skuList,
    };
    return submitParams;
  });
}

const getPriceSchemeOption = async () => {
  const { data } = await getPriceSchemeList();
  schemeOptions.value = data;
};

onMounted(() => {
  getPriceSchemeOption();
});

defineExpose({
  validator,
});
</script>

<style lang="less" setup>
.computed-rule-block {
  padding: 20px 24px 0 24px;
}
.handle-slot {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  .handle-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    &:nth-last-child(1) {
      justify-content: flex-end;
    }
  }
}
.placeholder-block {
  margin-bottom: 20px;
}
</style>
