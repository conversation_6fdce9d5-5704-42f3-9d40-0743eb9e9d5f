<template>
  <div class="upload_item">
    <ImageUploadWithImageLibrary
      ref="uploadLibraryRef"
      v-model:value="image"
      class="image_upload_wrap"
      :height="178"
      :max="1"
      :padding="0"
      :show-delete="false"
      :show-preview="true"
      :width="282"
      @cancel="onCancel"
    />
    <slot name="picTitle" />
    <div class="footer">
      <div
        class="edit"
        @click="onEdit"
      >
        <edit-outlined />
      </div>
      <div
        class="del"
        @click="onDel"
      >
        <delete-outlined />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, createVNode } from 'vue';
import { EditOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import ImageUploadWithImageLibrary from '@/components/image-upload-with-image-library/image-upload-with-image-library.vue';
import { Modal } from 'ant-design-vue';
interface IProps {
  add?: boolean;
  imgSrc: string;
  isActiveGoodsEdit: boolean;
}
interface IImage {
  src: string;
}
const props = withDefaults(defineProps<IProps>(), {
  isShowDel: true,
  add: false,
  imgSrc: '',
  isActiveGoodsEdit: false,
});
const uploadLibraryRef = ref();
const emit = defineEmits(['onAdd', 'onEdit', 'onDel', 'update:imgSrc']);

// 编辑
let tempImg = '';
const onEdit = () => {
  if (props.isActiveGoodsEdit) {
    emit('onEdit');
  } else {
    if (!image.value[0]) return;
    tempImg = image.value[0].src;
    image.value = [];
    uploadLibraryRef.value.openImageLibrary();
  }
};
const onCancel = () => {
  if (tempImg) {
    image.value[0] = { src: tempImg };
  }
  tempImg = '';
};
// 删除
const onDel = () => {
  Modal.confirm({
    icon: createVNode(ExclamationCircleOutlined),
    content: '确定要删除吗?',
    onOk() {
      emit('update:imgSrc', '');
      image.value = [];
      emit('onDel');
    },
  });
};

const image = ref<IImage[]>([]);

watch(
  image,
  val => {
    if (val[0]) {
      emit('update:imgSrc', val[0].src);
    }
  },
  {
    deep: true,
  },
);

watch(
  () => props.imgSrc,
  val => {
    if (val) {
      image.value[0] = { src: val };
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
.upload_item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: fit-content;
}
.footer {
  position: relative;
  display: flex;
  width: 282px;
  height: 46px;
  background: #f0f7ff;
  border-radius: 0 0 8px 8px;
  cursor: pointer;
  div {
    flex: 50%;
    line-height: 46px;
    text-align: center;
    &:hover {
      :deep(svg) {
        color: #3777ff;
      }
    }
  }

  &::after {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1px;
    height: 15px;
    background: rgba(6, 21, 51, 0.65);
    border-radius: 16px;
    transform: translate(-50%, -50%);
    content: '';
  }
}

.image_upload_wrap {
  :deep(.add-btn-wrap) {
    box-sizing: border-box;
    &:hover {
      border-color: #3777ff;
    }
  }
  :deep(.add-btn) {
    background-color: #fff;
    border: 1px solid #f0f7ff;
    border-radius: 8px 8px 0 0;
    svg {
      width: 17px;
      height: 17px;
      color: rgba(6, 21, 51, 0.65);
    }
    .text {
      margin-top: 15px;
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 14px;
    }
  }

  :deep(.drag-image-wrap) {
    box-sizing: border-box;
    padding: 16px;
    border: 1px solid #f0f7ff;
    border-bottom-color: transparent;
    border-radius: 8px 8px 0 0;

    .img-wrap {
      width: 250px !important;
      height: 146px !important;
      overflow: hidden;
      border-radius: 8px;
      img {
        width: 250px !important;
      }
    }
  }

  :deep(.list-item-wrap) {
    margin: 0 0 -4px 0 !important;
  }
}
</style>
