<template>
  <a-modal
    :destroy-on-close="true"
    title="换货申请单"
    :visible="bindVisible"
    width="640px"
    @cancel="closeModal"
  >
    <a-spin
      class="barter-content"
      :spinning="loading"
    >
      <div class="delivery-card">
        <div class="delivery-card-header">
          <span class="title"> 申请更换货品为 : </span>
          <span class="delivery-message">
            {{ barterData.new_sku_info }}
          </span>
          <div style="margin-top: 8px">
            <a-checkbox
              :checked="barterData.is_support === '1'"
              disabled
            >
              发货设备默认支持监管服务，如不需要请取消勾选
            </a-checkbox>
          </div>
        </div>
      </div>
      <div class="barter-detail split-lines">
        <div
          class="barter-detail-item"
          style="padding-top: 15px"
        >
          <div class="barter-detail-label">
            换货原因:
          </div>
          <div class="barter-detail-value">
            {{ barterData.reason }}, {{ barterData.remark }}
          </div>
        </div>
        <div
          class="barter-detail-item split-lines"
          style="padding-top: 15px"
        >
          <div class="barter-detail-label">
            换货数量:
          </div>
          <div class="barter-detail-value">
            {{ barterData.item_nums }}
          </div>
        </div>
        <div class="barter-detail-item">
          <div class="barter-detail-label">
            原应发货品:
          </div>
          <div class="barter-detail-value">
            {{ barterData.old_sku_info }}
          </div>
        </div>

        <div
          class="barter-detail-item split-lines"
          style="padding-top: 15px"
        >
          <div class="barter-detail-label">
            自有仓库库存情况:
          </div>
          <div class="barter-detail-value">
            暂无数据
          </div>
        </div>
        <div class="barter-detail-item">
          <div class="barter-detail-label">
            平台仓库库存情况:
          </div>
          <div
            class="barter-detail-value"
            style="color: #5ec9c2"
          >
            {{ stockText }}
          </div>
          <div class="barter-detail-explain">
            <img
              alt=""
              src="https://img1.rrzuji.cn/uploads/scheme/2306/25/m/ckdL1GuhSzjcPQp1q7PS.png"
            >
            <span>库存情况为申请时的查询情况</span>
          </div>
        </div>

        <div
          class="barter-detail-item split-lines"
          style="padding: 15px 0 0"
        >
          <div class="barter-detail-label">
            申请端口:
          </div>
          <div class="barter-detail-value">
            {{ barterData?.apply_port_txt }}
          </div>
        </div>
        <div
          class="barter-detail-item"
          style="padding: 15px 0 0"
        >
          <div class="barter-detail-label">
            变更时间:
          </div>
          <div class="barter-detail-value">
            {{ barterData?.created_at }}
          </div>
        </div>
        <div
          class="barter-detail-item"
          style="padding: 15px 0 0"
        >
          <div class="barter-detail-label">
            申请人:
          </div>
          <div class="barter-detail-value">
            {{ barterData?.created_by }}
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>

      <a-button
        v-if="exchangeApplyBtn"
        @click="showReplaceGoodsModal"
      >
        重新申请
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, type PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { useVModel } from '@/hook';

import { seeBarterApply, superSeeBarterApply } from './services';

interface IBarterData {
  order_id?: string;
  id?: string;
  goToShipBtn?: string;
  item_nums?: number;
  new_sku_info?: string;
  old_sku_info?: string;
  reason?: string;
  remark?: string;
  is_support?: string;
  stock_status?: '1' | '2' | '3';
  apply_port_txt?: string;
  created_at?: string;
  created_by?: string;
}

interface IExtraData {
  id?: number | string;
  created_time?: number | string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
  exchangeApplyBtn: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'showExchangeApplyModal'): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);
const barterData = ref<IBarterData>({});
const loading = ref(false);
const isSuper = useRoute().query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loading.value = true;
      const api = isSuper ? superSeeBarterApply : seeBarterApply;
      api({ order_id: props.orderId, id: props.extraData.id, created_time: props.extraData.created_time })
        .then(res => {
          barterData.value = res.data || {};
        })
        .catch(() => {
          bindVisible.value = false;
        })
        .finally(() => {
          loading.value = false;
        });
    }
  },
  { immediate: true },
);

const stockTextMap = {
  1: '充足',
  2: '缺货',
  3: '无数据',
};
const stockText = computed(() => {
  const stockStatus = Number(barterData.value.stock_status);
  return stockTextMap[stockStatus] || '';
});

function closeModal() {
  bindVisible.value = false;
}

function showReplaceGoodsModal() {
  emit('showExchangeApplyModal');
  closeModal();
}
</script>

<style scoped lang="less">
.delivery-card {
  position: relative;
  margin-bottom: 16px;
  padding: 24px;
  color: rgba(6, 21, 51, 1);
  background-color: #f5f7fa;
  border-radius: 4px;
}

.delivery-card-header {
  font-size: 16px;

  .title {
    padding-right: 4px;
    color: rgb(6, 21, 51);
    font-weight: 500;
  }
}

.delivery-message {
  color: #5ec9c2;
  font-weight: 500;
}

.barter-content {
  position: relative;
  color: rgba(6, 21, 51, 1);
}

.barter-content .barter-detail {
  padding-top: 15px;
}

.barter-detail-item {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
}

.barter-detail-item .barter-detail-value {
  color: rgb(6, 21, 51);
}

.barter-detail-item .barter-detail-label {
  padding-right: 8px;
  color: rgba(6, 21, 51, 1);
  font-weight: 400;
}

.barter-detail-item .barter-detail-explain {
  padding-left: 8px;
  color: rgba(6, 21, 51, 0.65);
  font-size: 12px;
}

.barter-detail-item .barter-detail-explain img {
  transform: translateY(-1px);
}

.split-lines {
  border-top: 1px solid rgba(6, 21, 51, 0.06);
}

:deep(.ant-checkbox-disabled + span) {
  color: rgba(6, 21, 51, 1);
}
</style>
