<template>
  <a-modal
    v-model:visible="visible"
    destroy-on-close
    :footer="getFooter"
    :title="getTitle"
    width="480px"
    @cancel="close"
    @ok="confirm"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="formState"
      >
        <a-form-item
          label="订单号"
          name="recycle_order_id"
          required
        >
          <a-input-search
            v-model:value="formState.recycle_order_id"
            :disabled="modalStatus === EModalStatus.VIEW"
            placeholder="请输入订单号"
            @search="onSearch"
          >
            <template #enterButton>
              <a-button :disabled="modalStatus === EModalStatus.VIEW">
                搜索
              </a-button>
            </template>
          </a-input-search>
        </a-form-item>
        <a-form-item
          label="型号"
          required
        >
          <a-input
            disabled
            placeholder="输入订单号后自动显示"
            :value="recordInfo.goods_name"
          />
        </a-form-item>
        <a-form-item
          label="估价金额（元）"
          required
        >
          <a-input
            disabled
            placeholder="输入订单号后自动显示"
            :value="recordInfo.evaluate_price"
          />
        </a-form-item>
        <a-form-item
          label="定价金额（元）"
          required
        >
          <a-input
            disabled
            placeholder="输入订单号后自动显示"
            :value="recordInfo.purchased_price"
          />
        </a-form-item>
        <a-form-item
          :extra="recordInfo.quality_report_images ? undefined : '输入订单号后自动显示'"
          label="检测图片"
          required
        >
          <div
            v-if="!recordInfo.quality_report_images"
            class="image-none-mian"
          >
            <picture-outlined :style="{ fontSize: '24px', color: '#B9BDC6' }" />
            <div class="image-text">
              自动显示
            </div>
          </div>
          <a-image-preview-group v-else>
            <a-image
              v-for="(item, index) in recordInfo.quality_report_images"
              :key="index"
              :src="item"
            />
          </a-image-preview-group>
        </a-form-item>
        <a-form-item label="订单评价">
          <a-switch
            v-model:checked="formState.show_comment"
            checked-value="1"
            un-checked-value="2"
            @change="statusChange"
          />
        </a-form-item>
        <a-form-item label="评价等级">
          <a-input
            v-model:value="recordInfo.comment_level_text"
            disabled
            placeholder="输入订单号后自动显示"
          />
        </a-form-item>
        <a-form-item label="评价标签">
          <a-input
            v-model:value="recordInfo.comment_labels_text"
            disabled
            placeholder="输入订单号后自动显示"
          />
        </a-form-item>
        <a-form-item label="评价内容 ">
          <a-input
            disabled
            placeholder="输入订单号后自动显示"
            :value="recordInfo.comment_content"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';
import { FormInstance, message } from 'ant-design-vue';
import { Form } from 'ant-design-vue';
import { PictureOutlined } from '@ant-design/icons-vue';

import { useModal } from '@/hook/component/use-modal';

import { IDispatchModal, IModalState, ITransactionRecordDetail } from '../data';
import { createModalContent, getModalContent, getOrderInfo, toggleRecordModalOrderStatus } from '../service';

const enum EModalStatus {
  'CREATE' = 1, // 创建
  'VIEW' = 3, // 查看
}
const emits = defineEmits(['reload-context']);

const modalStatus = ref<EModalStatus>(1);
const formRef = ref<FormInstance>();
const currentId = ref('');

const formState = reactive<IModalState>({
  recycle_order_id: '',
  show_comment: '1',
});

const recordInfo = ref<Partial<ITransactionRecordDetail>>({});

const { resetFields } = Form.useForm(formState);

const { open, loading, visible, close, confirm } = useModal(
  async () => {
    if (modalStatus.value === EModalStatus.CREATE) {
      await formRef.value?.validate();
      await createModalContent(formState);
      message.success('操作成功');
      emits('reload-context');
      close();
    }
  },
  {
    afterClose: resetFields,
  },
);

const setModal = (status: EModalStatus, id = '') => {
  if (typeof status === 'undefined') {
    throw new Error(`${status} -> '异常'`);
  }
  currentId.value = id;
  modalStatus.value = status;
  open();
};

const statusChange = async (val: string) => {
  if (modalStatus.value === EModalStatus.VIEW) {
    await toggleRecordModalOrderStatus({ id: currentId.value, show_comment: val });
    message.success('操作成功');
  }
};

const onSearch = async (e: string) => {
  const { data } = await getOrderInfo({ recycle_order_id: e });
  recordInfo.value = data;
};

const disPatch = async ({ action, params }: IDispatchModal<keyof typeof EModalStatus>) => {
  switch (action) {
    case 'CREATE':
      setModal(EModalStatus.CREATE);
      recordInfo.value = {};
      break;
    case 'VIEW':
      if (typeof params?.id === 'undefined') {
        throw Error(`action -> ${action} 致命错误:'ID参数丢失'`);
      }
      setModal(EModalStatus.VIEW, params.id);
      getViewDetail(params.id);
      break;
    default:
      throw Error('未知 action：' + action);
  }
};

const getViewDetail = async (id: string) => {
  try {
    loading.value = true;
    const { data } = await getModalContent({ id });
    recordInfo.value = data;
    formState.recycle_order_id = data.recycle_order_id;
    formState.show_comment = data.show_comment;
  } finally {
    loading.value = false;
  }
};

const getTitle = computed(() => {
  switch (modalStatus.value) {
    case EModalStatus.CREATE:
      return '添加记录展示';
    case EModalStatus.VIEW:
      return '查看记录展示';
    default:
      throw Error('未知 modalStatus：' + modalStatus.value);
  }
});

const getFooter = computed(() => {
  if (modalStatus.value === EModalStatus.VIEW) {
    return null;
  } else {
    return undefined;
  }
});

defineExpose({
  disPatch,
});
</script>

<style lang="less" scoped>
.ant-form-item {
  flex-direction: column;
  width: 100%;
  padding: 0 24px;
}
:deep(.ant-col) {
  flex: 1;
  min-height: min-content;
}
:deep(.ant-form-item-label) {
  align-self: flex-start;
}
:deep(.ant-image) {
  width: 104px;
  height: 104px;
  margin-right: 8px;
  overflow: hidden;
}

.image-none-mian {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 104px;
  height: 104px;
  background: rgba(6, 21, 51, 0.04);
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 2px;
}
.image-text {
  margin-top: 10px;
  color: rgba(6, 21, 51, 0.25);
  font-weight: 400;
  font-size: 14px;
}
</style>
