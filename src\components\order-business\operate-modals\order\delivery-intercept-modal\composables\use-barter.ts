import { onMounted, ref } from 'vue';

import { IInitData } from '@/components/order-business/operate-modals/order/exchange-apply-modal/data';
import { getModal } from '@/components/order-business/operate-modals/order/exchange-apply-modal/services';
import {
  getStoreSendWarehouseInfo,
  superGetSendWarehouseInfo,
} from '@/components/order-business/operate-modals/order/go-ship-modal/services';
export default function (orderId: string, isSuper) {
  const initData = ref<IInitData>();
  const modalOptions = ref<{ label: string; value: number }[]>([]);

  function getModelOptions() {
    const { pdm_category_id, pdm_brand_id } = initData.value;
    getModal({
      category_id: pdm_category_id,
      brand_id: pdm_brand_id,
    }).then(({ data }) => {
      const { spuRelation } = data;
      modalOptions.value =
        spuRelation.children
          .find((item: any) => item.id === Number(pdm_category_id))
          ?.children.find((item: any) => item.id === Number(pdm_brand_id))
          ?.children.map((item: any) => ({
            label: item.title,
            value: item.id,
          })) || [];
    });
  }

  onMounted(() => {
    const api = isSuper ? superGetSendWarehouseInfo : getStoreSendWarehouseInfo;
    api(orderId).then(({ data }) => {
      initData.value = {
        orderId,
        ...data,
      };
      getModelOptions();
    });
  });

  return {
    initData,
    modalOptions,

    getModelOptions,
  };
}
