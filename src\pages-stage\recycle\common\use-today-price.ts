import { ColumnType } from 'ant-design-vue/lib/table';
import { ref, computed, onMounted, ComputedRef, Ref } from 'vue';
import { IExcelModel } from '../quotation-manage/quotation-registration/data';
import { IAxiosResponse } from '@/services/api';
import dayjs from 'dayjs';
export interface IUseTodayPriceOptions {
  fetchDataHook: () => Promise<
    IAxiosResponse<{
      content: IExcelModel[];
      id: string;
    }>
  >;
  onMounted: boolean;
}
export interface IUseTodayPriceResult {
  clueListColumns: ColumnType<IExcelModel>[];
  mergedDataSource: ComputedRef<IExcelModel[]>;
  fetchTableData: () => Promise<void>;
  listLoading: Ref<boolean>;
  dataSource: Ref<IExcelModel[]>;
  getRowKey: (record: IExcelModel) => string;
  today: string;
}
export interface ISelectOptions {
  label: string;
  value: string;
  children?: ISelectOptions[];
}
export const useTodayPrice = (options: IUseTodayPriceOptions): IUseTodayPriceResult => {
  const clueListColumns: ColumnType<IExcelModel>[] = [
    {
      title: '关联基础商品ID',
      key: 'goods_id',
      dataIndex: 'goods_id',
      width: 120,
    },
    {
      title: '设备型号',
      key: 'model',
      dataIndex: 'model',
      width: 300,
      customCell: (record: IExcelModel, index: number | undefined) => {
        if (index && record.model === dataSource.value[index - 1].model) {
          return {
            rowSpan: 0,
          };
        } else {
          return {
            rowSpan: record.modelRowSpan || 1,
          };
        }
      },
    },
    {
      title: '版本',
      key: 'version',
      dataIndex: 'version',
      width: 120,
    },
    {
      title: '内存',
      key: 'memory',
      dataIndex: 'memory',
      width: 120,
    },
    {
      title: '颜色',
      key: 'color',
      dataIndex: 'color',
      width: 120,
    },
    {
      title: '准新',
      key: 'quasi_new',
      dataIndex: 'quasi_new',
      width: 120,
    },
    {
      title: '99新',
      key: 'new99',
      dataIndex: 'new99',
      width: 120,
    },
    {
      title: '95新',
      key: 'new95',
      dataIndex: 'new95',
      width: 120,
    },
    {
      title: '90新',
      key: 'new90',
      dataIndex: 'new90',
      width: 120,
    },
  ];
  const dataSource = ref<IExcelModel[]>([]);
  const listLoading = ref(false);
  const getRowKey = (record: IExcelModel) => record.model;

  const mergedDataSource = computed(() => {
    const mergedData: IExcelModel[] = [];
    let prevModel = null;
    let rowSpan = 0;

    for (const record of dataSource.value) {
      if (record.model !== prevModel) {
        if (prevModel !== null) {
          mergedData[mergedData.length - rowSpan].modelRowSpan = rowSpan;
        }
        prevModel = record.model;
        rowSpan = 0;
      }
      mergedData.push({ ...record });
      rowSpan++;
    }

    if (prevModel !== null) {
      mergedData[mergedData.length - rowSpan].modelRowSpan = rowSpan;
    }
    return mergedData;
  });
  const fetchTableData = async () => {
    try {
      listLoading.value = true;
      const { data } = await options?.fetchDataHook();
      dataSource.value = data?.content ?? [];
    } catch (error) {
      listLoading.value = false;
      throw error;
    } finally {
      listLoading.value = false;
    }
  };
  const today = dayjs().format('YYYY 年 MM 月 DD 日');
  onMounted(() => {
    if (options?.onMounted && typeof options.fetchDataHook === 'function') {
      fetchTableData();
    }
  });
  return {
    clueListColumns,
    mergedDataSource,
    fetchTableData,
    listLoading,
    dataSource,
    getRowKey,
    today,
  };
};
export const formatExcelData = (data: IExcelModel[]) => {
  const formatdData: ISelectOptions[] = [];
  let dataSource: (Pick<IExcelModel, 'memory' | 'model'> & { status: string[] })[] = [];
  // 创建一个辅助对象，用于根据 model 和 memory 值查找对应的节点
  const nodesMap = new Map();
  dataSource = data.map(item => {
    const status: string[] = [];
    if (item.new95) {
      status.push('95新');
    }
    if (item.new99) {
      status.push('99新');
    }
    if (item.new90) {
      status.push('90新');
    }
    return {
      memory: item.memory,
      model: item.model,
      status,
    };
  });
  // 遍历数据数组
  for (const item of dataSource) {
    const { model, memory, status } = item;
    // 处理机型状态
    const modelStatus = status.map(item => {
      return {
        label: item,
        value: item,
      };
    });
    if (nodesMap.get(model)) {
      const currentNode = nodesMap.get(model);
      currentNode.children.push({ label: memory, value: memory, children: modelStatus });
    } else {
      const currentNode: { label: string; value: string; children: ISelectOptions[] } = {
        label: model,
        value: model,
        children: [],
      };
      currentNode.children.push({ label: memory, value: memory, children: modelStatus });
      nodesMap.set(model, currentNode);
    }
  }
  nodesMap.forEach(item => {
    formatdData.push(item);
  });
  return formatdData;
};
