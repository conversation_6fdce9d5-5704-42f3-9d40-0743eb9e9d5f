import { nextTick, reactive, ref, watch } from 'vue';
import { TableColumnType } from 'ant-design-vue';

import uploadToOss from '@/utils/oss-upload';

import { apiGetCommodityList } from './services';

export const useCommodityList = (getOrderId: () => string) => {
  const columns: TableColumnType[] = [
    {
      key: 'no',
      dataIndex: 'no',
      title: '序号',
      width: 70,
    },
    {
      key: 'name',
      dataIndex: 'name',
      title: '商品名称',
      showTitle: true,
    },
    {
      key: 'num',
      dataIndex: 'num',
      title: '数量',
    },
    {
      key: 'codes',
      dataIndex: 'codes',
      title: '设备序列号',
    },
    {
      key: 'actions',
      dataIndex: 'actions',
      title: '操作',
    },
  ].map(item => ({
    ...item,
    align: 'center',
  }));

  const getDefaultRow = (length: number) => ({
    no: length + 1,
    name: '',
    num: 1,
    codes: [],
    isNew: true,
  });
  const pageConfig = reactive({
    title: '序列号商品清单录入',
    subTitle: '（请先上传商品清单，再录入序列号）',
    errorText: '',
  });

  const accept = '.jpg, .png, .jpeg, .gif, .pdf, .doc, .docx, .xls, .xlsx';
  const getUppercaseExtensions = (acceptStr: string): string[] => {
    return acceptStr
      .split(',')
      .map(ext => {
        const cleaned = ext.replace(/^\s*/, '').replace(/\s*$/, '');
        return [cleaned.toLowerCase(), cleaned.toUpperCase()];
      })
      .flat()
      .filter(Boolean);
  };
  const uploadConfig: Record<string, any> = {
    showText: [
      '图片上传：支持JPG，PNG等图片格式（手动修改文件后缀无效）；',
      '图片大小不可超过10M，分辨率不可高于10000*10000',
    ],
    maxSize: '10MB',
    removeConfirm: true,
    template: 'dragger',
    uploadProps: {
      accept: getUppercaseExtensions(accept).join(','),
      maxCount: 1,
      beforeUpload: file => {
        console.log(file, '-------------');
      },
    },
    uploadApi: uploadToOss,
    valueType: 'file',
  };
  const commodifyList = ref<Record<string, any>[]>([]);
  const fileList = ref<Record<string, any>[]>([]);
  const commodifyListLoading = ref(false);
  const updateList = (newList: Record<string, any>[]) => {
    commodifyList.value = newList;
  };

  const getCommodityTableData = async (data: Record<string, any>) => {
    try {
      commodifyListLoading.value = true;
      const res = await apiGetCommodityList({
        order_id: getOrderId(),
        picture_name: data.name,
        picture: data.response.path[0],
      });
      pageConfig.errorText = res.data.reason;
      updateList(res.data.list);
      await nextTick();
      res.data.reason && handleCommodityAddRow();
    } finally {
      commodifyListLoading.value = false;
    }
  };
  const handleChangeCommodityValue = (val: any, record: Record<string, any>, key: string) => {
    updateList(
      commodifyList.value.map(item =>
        item === record
          ? {
              ...item,
              [key]: key === 'num' ? val : val.target.value,
            }
          : item,
      ),
    );
  };
  const handleCommodityDelete = (record: Record<string, any>) => {
    updateList(commodifyList.value.filter(item => item !== record));
  };
  const handleCommodityAddRow = () => {
    updateList([...commodifyList.value, getDefaultRow(commodifyList.value.length)]);
  };

  // 监听文件列表变化
  watch(
    () => fileList.value,
    async val => {
      if (val?.[0]?.status === 'done') {
        await getCommodityTableData(val[0]);
      }
      if (!val || val.length === 0) {
        pageConfig.errorText = '';
      }
    },
  );
  const extractCodesAndItemNos = () => {
    const codes: string[] = [];
    const item_nos: number[] = [];
    commodifyList.value.forEach(item => {
      if (item.no !== undefined && Array.isArray(item.codes)) {
        item.codes.forEach(code => {
          if (code) {
            codes.push(code);
            item_nos.push(item.no);
          }
        });
      }
    });
    return { codes, item_nos };
  };

  const handleChangeUpload = (fileList: any[]) => {
    console.log(fileList);
  };
  return {
    fileList,
    columns,
    commodifyList,
    pageConfig,
    uploadConfig,
    getCommodityTableData,
    handleChangeCommodityValue,
    handleCommodityDelete,
    handleCommodityAddRow,
    commodifyListLoading,
    extractCodesAndItemNos,
    handleChangeUpload,
  };
};
