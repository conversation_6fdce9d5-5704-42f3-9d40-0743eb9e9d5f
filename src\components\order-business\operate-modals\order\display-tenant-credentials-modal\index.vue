<template>
  <a-modal
    v-model:visible="bindVisible"
    class="has-user-credentials-modal"
    title="用户凭证"
    :width="650"
  >
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <div class="id-card-box">
        <div class="card">
          <div class="title">
            身份证正面
          </div>
          <div class="img-box">
            <img
              class="watermark"
              src="https://img1.rrzuji.cn/uploads/scheme/2206/17/m/aUMknu6r2Cl3luxnI3Co.png"
            >
            <a-image
              class="img"
              :preview="false"
              :src="data.new_is_upload_watermark_face_img"
              @click="previewChange(0)"
            />
            <img
              v-if="data.is_upload_watermark_face_img"
              class="download"
              src="https://img1.rrzuji.cn/uploads/scheme/2206/09/m/4hVadoyf5JLKRELcoCVi.png"
              @click="upliadImg(data.is_upload_watermark_face_img, '身份证正面照')"
            >
          </div>
        </div>
        <div class="card">
          <div class="title">
            身份证反面
          </div>
          <div class="img-box">
            <img
              class="watermark"
              src="https://img1.rrzuji.cn/uploads/scheme/2206/17/m/aUMknu6r2Cl3luxnI3Co.png"
            >
            <a-image
              class="img"
              :preview="false"
              :src="data.new_is_upload_watermark_back_img"
              @click="previewChange(1)"
            />
            <img
              v-if="data.is_upload_watermark_back_img"
              class="download"
              src="https://img1.rrzuji.cn/uploads/scheme/2206/09/m/4hVadoyf5JLKRELcoCVi.png"
              @click="upliadImg(data.is_upload_watermark_back_img, '身份证背面照')"
            >
          </div>
        </div>
      </div>
      <div class="prompt">
        租户身份证照片仅可用于在人人租平台产生的租赁业务，若需身份证照片原件可向平台申请
      </div>
    </a-spin>
    <!-- 图片预览 -->
    <image-preview
      v-model:visible="previewVisible"
      :imgs="[data.old_watermark_face, data.old_watermark_back]"
      :index="1"
    />
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useVModel } from '@/hook';
import type { HasUserCredentialsData, IFile } from './data';
import { getOldIdCard } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);

const loading = ref(false);
const data = ref<HasUserCredentialsData>({});

const bindVisible = useVModel(props, 'visible', emit);
watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function setLoading(flag = false) {
  loading.value = flag;
}

function loadData() {
  setLoading(true);
  getOldIdCard({
    order_id: props.orderId,
  })
    .then(res => {
      data.value.new_is_upload_watermark_face_img = res.data.face;
      data.value.new_is_upload_watermark_back_img = res.data.back;
      parseData(res);
    })
    .finally(() => {
      setLoading();
    });
}

/**
 * @description: 格式化图片url
 * @param {string} url
 * @return {*}
 */
function parseData(res: any) {
  const cvs = document.createElement('canvas');
  const ctx = cvs.getContext('2d') as CanvasRenderingContext2D;
  const tstimg = new Image();
  tstimg.setAttribute('crossOrigin', 'Anonymous');
  tstimg.src = res.data.face;
  cvs.setAttribute('width', '1');
  cvs.setAttribute('height', '1');
  tstimg.onload = function () {
    cvs.setAttribute('width', String(tstimg.width));
    cvs.setAttribute('height', String(tstimg.height));
    ctx.drawImage(tstimg, 0, 0);
    drawWaterMark(ctx, tstimg.width, tstimg.height);
    const watermark_face = cvs.toDataURL('image/png', 1);
    yuanimg(watermark_face, tstimg.width, tstimg.height, 'face');
  };

  const bagcvs = document.createElement('canvas');
  const bagctx = bagcvs.getContext('2d') as CanvasRenderingContext2D;
  const bagimg = new Image();
  bagimg.src = res.data.back;
  bagimg.setAttribute('crossOrigin', 'Anonymous');
  bagcvs.setAttribute('width', '1');
  bagcvs.setAttribute('height', '1');
  bagimg.onload = function () {
    bagcvs.setAttribute('width', String(bagimg.width));
    bagcvs.setAttribute('height', String(bagimg.height));
    bagctx.drawImage(bagimg, 0, 0);
    drawWaterMark(bagctx, bagimg.width, bagimg.height);
    const watermark_back = bagcvs.toDataURL('image/png', 1);
    yuanimg(watermark_back, bagimg.width, bagimg.height, 'back');
  };
}

function drawWaterMark(ctx: CanvasRenderingContext2D, imgWidth: number, imgHeight: number) {
  const maskText = '仅限人人租平台租赁使用'; // 水印文字
  const fontColor = 'rgba(0, 0, 0, 0.1)'; // 水印颜色
  const lineHeight = 250; // 水印文字行高
  const textWidth = 700; // 水印文字宽度
  const diagonalLength = imgHeight > imgWidth ? imgHeight * 2 : imgWidth; // 选取最长边
  ctx.translate(-imgWidth / 2, imgHeight / 2 - 200); // 画布旋转原点 移到 图片中心
  ctx.rotate(-Math.PI / 5);
  const crossTime = Math.ceil((diagonalLength * 2) / textWidth);
  // 竖向循环次数
  const verticalTime = Math.ceil((diagonalLength * 2) / lineHeight);
  for (let j = 0; j < verticalTime; j++) {
    // 纵向循环
    ctx.font = '50px Arial';
    ctx.fillStyle = fontColor;
    ctx.fillText(maskText, 0, lineHeight * j);
    for (let i = 1; i < crossTime; i++) {
      // 横向循环
      ctx.font = '50px Arial';
      ctx.fillStyle = fontColor;
      ctx.fillText(maskText, textWidth * i, lineHeight * j);
    }
  }
}

function yuanimg(url: string, width: number, height: number, type: 'face' | 'back') {
  const yuanimg = new Image();
  yuanimg.src = url;
  yuanimg.setAttribute('crossOrigin', 'Anonymous');
  yuanimg.onload = function () {
    const newWidth = width;
    const newHeight = height;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    canvas.width = newWidth;
    canvas.height = newHeight;
    ctx.drawImage(yuanimg, 0, 0, newWidth, newHeight);
    if (type == 'face') {
      data.value.old_watermark_face = canvas.toDataURL('image/png', 1);
      data.value.watermark_face = canvas.toDataURL('image/jpeg', 0.7);
      data.value.is_upload_watermark_face_img = canvas.toDataURL('image/png', 1);
      const arr = data.value.watermark_face.split(',') as string[];
      const mime = (arr[0].match(/:(.*?);/) as string[])[1] as string;
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const file: IFile = new File([u8arr], '身份证正面照', { type: mime });
      data.value.faceFile = file;
    }
    if (type == 'back') {
      data.value.old_watermark_back = canvas.toDataURL('image/png', 1);
      data.value.watermark_back = canvas.toDataURL('image/jpeg', 0.7);
      data.value.is_upload_watermark_back_img = canvas.toDataURL('image/png', 1);
      const newarr = data.value.watermark_back.split(',') as string[];
      const mime = (newarr[0].match(/:(.*?);/) as string[])[1] as string;
      const bstr = atob(newarr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const backfile: IFile = new File([u8arr], '身份证反面照', { type: mime });
      data.value.backFile = backfile;
    }
  };
}

const previewVisible = ref(false);
const previewIndex = ref(0);

function previewChange(index: number) {
  previewIndex.value = index;
  previewVisible.value = true;
}

function upliadImg(url: string, name: string) {
  if (url.indexOf('http') === 0) {
    // 传进来的是图片链接，先转base64，再用a链接下载
    const urlItems = url.split('.');
    const suffix = urlItems[urlItems.length - 1];
    const image = new Image();
    image.crossOrigin = 'anonymous';
    image.onload = function () {
      const width = image.width;
      const height = image.height;
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      (canvas.getContext('2d') as CanvasRenderingContext2D).drawImage(image, 0, 0, width, height);
      const dlink = document.createElement('a');
      dlink.setAttribute('download', name);
      dlink.setAttribute('href', canvas.toDataURL(suffix === 'image' || suffix === 'jpg' ? 'image/png' : 'image/png'));
      dlink.click();
    };
    image.src = url + `?t=${new Date().getTime()}${Math.random() * 32}`;
  } else {
    const dlink = document.createElement('a');
    dlink.setAttribute('download', name);
    dlink.setAttribute('href', url);
    dlink.click();
  }
}
</script>

<style scoped lang="less">
.id-card-box {
  display: flex;
  justify-content: space-around;

  .card {
    .title {
      margin-bottom: 12px;
      font-weight: bold;
      font-size: 16px;
      text-align: center;
    }
  }

  .img-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 256px;
    height: 194px;
    background: #f3f3f3;
    border-radius: 6px;

    .watermark {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }

    .download {
      position: absolute;
      right: 8px;
      bottom: 8px;
      cursor: pointer;
    }

    :deep(.ant-image) {
      width: 100%;
      height: 100%;

      .img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}

.prompt {
  margin-top: 24px;
  text-align: center;
}
</style>
