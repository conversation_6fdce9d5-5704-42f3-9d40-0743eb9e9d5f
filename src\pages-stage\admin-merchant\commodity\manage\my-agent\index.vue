<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px' }"
    title="我代理的精选"
  >
    <div class="p-wrap search-wrap">
      <form-create
        ref="searchFormRef"
        v-model:value="searchParams"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="getTableList"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleSet"
          >
            重置
          </a-button>
        </template>
      </form-create>
    </div>
    <div class="table-wrap">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :pagination="page"
        :scroll="{ x: '100%' }"
        :sticky="true"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <a-button
              type="link"
              @click="deleteById(record)"
            >
              删除
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </layout-shops-page>
</template>

<script lang="ts" setup>
import { FormCreate } from '@/components/form-create';
import { useTable } from '@/hook/component/use-table';
import { Modal, message } from 'ant-design-vue';
import { reactive, ref } from 'vue';
import { deleteSchemeById } from './service';
import { searchFormGroup, columns } from './config';

const searchFormRef = ref<InstanceType<typeof FormCreate>>();

const searchParams = reactive({
  item_id: '', // 产品id
  category: '', // 类目
  scheme_title: '', // 产品标题
  sku_name: '', // 套餐名称
  server_id: '', // 店铺ID
  rental: '', // 租金
  deposit: '',
  agent_ratio: '', // 分成
  created_at: '',
});

const { list, listLoading, page, getTableList, tableChange } = useTable({
  url: '/scheme/choice-index-inf',
  formatHandle: res => {
    return res.data.list;
  },
  searchForm: {
    ChoiceItemAgentSearchDao: searchParams,
  },
});

getTableList();

const handleSet = () => {
  (searchFormRef.value?.getFormRef() as any).resetFields();
};

const deleteById = (record: any) => {
  Modal.confirm({
    title: '删除该数据将无法恢复，是否继续？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      const result = await deleteSchemeById({
        id: record.id,
      });
      message.success(result.error);
      await getTableList();
    },
  });
};
</script>

<style scoped lang="less">
.search-wrap {
  :deep(.ant-form) {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 0 -8px;
  }
  :deep(.ant-form-item) {
    flex-basis: 25%;
    margin-right: 0;
    margin-bottom: 24px;
    padding: 0 8px;
  }
}
</style>
