import { GET, POST } from '@/services/api';
const hostType = 'Golang';
//行业资质列表
export function apiGetIndustryInfoList(params: any) {
  return GET('/server/ServerCredentialIndustry/queryIndustryList', params, { hostType });
}
//删除行业资质
export function delIndustryInfo(params: { id: string }) {
  return POST('/server/ServerCredentialIndustry/delete', params, { hostType });
}

//获取营业类目
export function getServerTargetApi() {
  return GET('/server/ServerCredentialIndustry/serverTarget', undefined, { hostType });
}
