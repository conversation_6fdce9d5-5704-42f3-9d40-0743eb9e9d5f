<template>
  <a-modal
    :destroy-on-close="true"
    :footer="null"
    title="申请记录"
    :visible="bindVisible"
    width="480px"
    @cancel="onCancel"
  >
    <a-table
      :columns="columns"
      :data-source="recordList"
      :loading="loading"
      :pagination="false"
      row-key="id"
      size="small"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'change_type'">
          {{ record.change_type === 1 ? '物流拦截' : '地址变更' }}
        </template>

        <template v-if="column.dataIndex === 'status'">
          <a-badge
            :status="getStatus(record.status).status"
            :text="getStatus(record.status).text"
          />
        </template>

        <template v-if="column.dataIndex === 'action'">
          <span
            class="link-text"
            @click="onShowDetail(record)"
          >查看</span>
        </template>
      </template>
    </a-table>
  </a-modal>

  <a-drawer
    :closable="false"
    destroy-on-close
    style="z-index: 9999"
    title="物流变更"
    :visible="drawerVisible"
    width="500"
    @close="onCloseDrawer"
  >
    <template #extra>
      <CloseOutlined @click="onCloseDrawer" />
    </template>

    <div
      class="details-item"
      style="padding-bottom: 24px; border-bottom: 1px solid #f0f0f0"
    >
      <div
        class="title"
        style="padding-bottom: 12px"
      >
        申请详情
      </div>

      <div class="info">
        <div class="label">
          变更类型 :
        </div>
        <div class="value">
          {{ changeDetail.change_type === 1 ? '物流拦截' : '地址变更' }}
        </div>
      </div>

      <template v-if="changeDetail.change_type === 1">
        <div class="info">
          <div class="label">
            拦截原因 :
          </div>
          <div class="value">
            {{ REASON_MAP[changeDetail.intercept_reason] || '' }}
          </div>
        </div>
      </template>

      <template v-if="changeDetail.change_type === 2">
        <div class="info">
          <div class="label">
            新收货人 :
          </div>
          <div class="value">
            {{ changeDetail?.name }} {{ baseInfo.name !== changeDetail?.name ? '(变更)' : '(不变)' }}
          </div>
        </div>

        <div class="info">
          <div class="label">
            新手机号 :
          </div>
          <div class="value">
            {{ changeDetail.phone }} {{ baseInfo.phone !== changeDetail.phone ? '(变更)' : '(不变)' }}
          </div>
        </div>

        <div class="info">
          <div class="label">
            新地址 :
          </div>
          <div class="value">
            {{ changeDetail.address }} {{ baseInfo.address !== changeDetail.address ? '(变更)' : '(不变)' }}
          </div>
        </div>
      </template>

      <div class="info">
        <div class="label">
          备注 :
        </div>
        <div class="value">
          {{ changeDetail.apply_remark }}
        </div>
      </div>

      <div class="info">
        <div class="label">
          申请时间 :
        </div>
        <div class="value">
          {{ changeDetail.created_at }}
        </div>
      </div>
    </div>
    <div
      class="details-item"
      style="padding-top: 24px"
    >
      <div
        class="title"
        style="padding-bottom: 12px"
      >
        平台处理
      </div>

      <div class="info">
        <div class="label">
          处理状态 :
        </div>
        <div
          class="value"
          :style="{ color: RESULT_MAP.color[changeDetail.result] || '' }"
        >
          {{ RESULT_MAP.text[changeDetail.result] || '-' }}
        </div>
      </div>

      <div class="info">
        <div class="label">
          处理结果 :
        </div>
        <div
          class="value"
          :style="{ color: RESULT_MAP.color[changeDetail.result] || '' }"
        >
          {{ RESULT_MAP.text[changeDetail.result] || '-' }}
        </div>
      </div>

      <div
        class="info"
        :style="{ paddingBottom: changeDetail.images?.length ? '8px' : '16px' }"
      >
        <div class="label">
          图片凭证 :
        </div>
        <div class="images-wrap">
          <a-image-preview-group>
            <div
              v-for="src in changeDetail.images"
              :key="src"
              class="cover-image"
            >
              <a-image
                :src="src"
                :width="86"
              />
            </div>
          </a-image-preview-group>
        </div>
      </div>

      <div class="info">
        <div class="label">
          平台备注 :
        </div>
        <div class="value">
          {{ changeDetail.remark || '-' }}
        </div>
      </div>
    </div>

    <template #footer>
      <div style="display: flex; justify-content: end">
        <a-button
          type="primary"
          @click="onCloseDrawer"
        >
          确定
        </a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';

import { REASON_MAP, RESULT_MAP } from '../config';
import type { IBaseInfo, IDetailInfo } from '../data';
import { searchChangeLog } from '../services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  baseInfo: {
    type: Object as PropType<IBaseInfo>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emits);

function onCancel() {
  bindVisible.value = false;
}

const columns = [
  {
    title: '变更类型',
    dataIndex: 'change_type',
  },
  {
    title: '时间',
    dataIndex: 'created_at',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'action',
  },
];

// ====================  修改记录列表  ======================
const recordList = ref<IDetailInfo[]>([]);
const loading = ref(false);

watch(
  () => props.visible,
  value => {
    if (value) {
      searchChangeLog(props.orderId).then(({ data }) => {
        recordList.value = data?.list || [];
      });
    }
  },
  { immediate: true },
);

function getStatus(status: number) {
  switch (status) {
    case 0:
      return { status: 'warning', text: '待处理' };
    case 1:
      return { status: 'processing', text: '处理中' };
    case 2:
      return { status: 'success', text: '已完成' };
    case 3:
      return { status: 'default', text: '已取消' };
    case 4:
      return { status: 'default', text: '已撤销' };
    default:
      return { status: 'default', text: '未知状态' };
  }
}

// ====================  修改记录详情  ======================
const changeDetail = ref<IDetailInfo>({});
const drawerVisible = ref(false);

function onShowDetail(record: IDetailInfo) {
  changeDetail.value = record;
  drawerVisible.value = true;
}

function onCloseDrawer() {
  drawerVisible.value = false;
}
</script>

<style lang="less" scoped>
@import '../common';

.link-text {
  color: #3777ff;
  cursor: pointer;
}
</style>
