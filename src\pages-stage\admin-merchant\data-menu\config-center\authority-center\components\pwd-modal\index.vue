<template>
  <div class="pwd">
    <a-modal
      v-model:visible="isShow"
      :body-style="{ height: '434px' }"
      centered
      class="pwd-modal"
      :footer="false"
      :mask-closable="false"
      :width="536"
      @cancel="handleCancel"
    >
      <div class="pwd-modal-title">
        账号验证
      </div>

      <div
        v-if="!isVerify"
        class="form"
      >
        <a-spin :spinning="loading">
          <div class="form-phone">
            验证码将发送至：{{ phone }}
          </div>
          <a-form
            ref="Form"
            label-align="left"
            layout="vertical"
            :model="modelData"
          >
            <a-form-item
              label="验证码"
              name="code"
              :rules="[{ required: true, message: '请输入正确的验证码' }]"
            >
              <a-input
                v-model:value="modelData.code"
                class="pwd-input"
                placeholder="请输入验证码"
                size="large"
                @press-enter="submit"
              >
                <template #suffix>
                  <span
                    class="suffix"
                    :class="codeState.disable ? 'suffix-disable' : ''"
                    @click="sendCode"
                  >{{
                    codeState.text
                  }}</span>
                </template>
              </a-input>
            </a-form-item>
          </a-form>
        </a-spin>
      </div>
      <div
        v-if="!isVerify"
        class="pwd-footer"
      >
        <div class="pwd-footer-pwd">
          <a-button
            class="pwd-btn"
            size="large"
            @click="prev"
          >
            上一步
          </a-button>
          <a-button
            class="pwd-btn"
            :class="btnDisable ? 'btn-disable' : ''"
            size="large"
            style="margin-left: 8px"
            type="primary"
            @click="submit"
          >
            提交
          </a-button>
        </div>
      </div>
      <a-result
        v-if="isVerify"
        class="result"
        title="绑定成功"
      >
        <template #icon>
          <img
            alt=""
            src="https://img1.rrzuji.cn/uploads/scheme/2304/25/m/vnxQOaSddBAHnwWYZ05m.png"
            width="60"
          >
        </template>
        <template #extra>
          <a-button
            class="result-btn"
            size="large"
            type="primary"
            @click="handleCancel"
          >
            确定
          </a-button>
        </template>
      </a-result>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { computed } from '@vue/reactivity';
import { reactive, ref } from 'vue';

import { getCode, checkCode } from '../../service';
import { message } from 'ant-design-vue';

const Form = ref<any>(null);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  phone: {
    type: String,
    required: true,
  },
  isVerify: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'update:isVerify', 'account-submit']);

const loading = ref<boolean>(false);
const timer = ref();

const codeState = reactive({
  token: '',
  text: '发送验证码',
  disable: false, // 是否禁用
  time: 60,
  isSend: false, // 是否正在倒计时
});
const modelData = reactive({
  phone: '',
  code: '',
});

const btnDisable = computed<boolean>(() => modelData.code === '');

const isShow = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

const prev = () => {
  isShow.value = false;
};

const submit = () => {
  if (btnDisable.value) return;
  Form.value.validate().then(async () => {
    checkCode({
      code: modelData.code,
      phone: props.phone,
    }).then(() => {
      Form.value.resetFields();
      emit('update:isVerify', true);
      emit('account-submit');
    });
  });
};

const handleCancel = () => {
  Form.value && Form.value.resetFields();
  isShow.value = false;
};

const sendCode = () => {
  if (codeState.disable) return;
  codeState.disable = true;
  codeState.text = `重新发送（${codeState.time}s）`;
  getCode({ phone: props.phone }).then(res => {
    message.success(res.data);
  });
  timer.value = setInterval(() => {
    codeState.time--;
    codeState.text = `重新发送（${codeState.time}s）`;
    if (codeState.time <= 0) {
      codeState.time = 60;
      codeState.text = '重新发送';
      codeState.disable = false;
      clearInterval(timer.value);
    }
  }, 1000);
};
</script>

<style scoped lang="less">
.pwd {
  &-modal {
    &-title {
      margin: 32px 0 24px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: bold;
      font-size: 20px;
      text-align: center;
    }
    .steps {
      padding: 0 46px;
    }
    .form {
      padding: 0 8px;

      :deep(.ant-form-item-label > label) {
        font-size: 16px;
      }

      :deep(.ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input) {
        background-color: #fff;
      }

      &-phone {
        margin-bottom: 56px;
        color: rgba(6, 21, 51, 0.45);
        text-align: center;
      }
    }

    .result-btn {
      height: 48px;
      padding-inline: 81px;
    }

    .pwd-input {
      background-color: #f5f7fa;
      border-color: transparent;
      padding-block: 10.5px;

      :deep(.ant-input) {
        background-color: #f5f7fa;
      }
    }
    .suffix {
      margin-right: 5px;
      color: #3777ff;
      font-weight: 400;
      cursor: pointer;

      &-disable {
        color: rgba(6, 21, 51, 0.25);
        cursor: not-allowed;
      }
    }

    .pwd-icon {
      color: rgba(6, 21, 51, 0.45);
    }

    .result {
      padding: 29px 32px 0;
      :deep(.ant-result-title) {
        margin-bottom: 4px;
        font-weight: 500;
        font-size: 16px;
      }

      :deep(.ant-result-icon) {
        margin-bottom: 18px;
      }

      :deep(.ant-result-extra) {
        margin-top: 78px;
      }
    }

    .pwd-footer {
      position: absolute;
      bottom: 56px;
      width: calc(100% - 48px);
      padding: 0 8px;
      .pwd-btn {
        height: 48px;
      }

      .btn-disable {
        cursor: not-allowed;
        opacity: 0.35;
      }

      .pwd-footer-pwd {
        display: flex;

        .pwd-btn {
          flex: 1;
        }
      }
    }
  }
}
</style>

<style>
.pwd-modal .ant-modal-content {
  border-radius: 16px;
}
</style>
