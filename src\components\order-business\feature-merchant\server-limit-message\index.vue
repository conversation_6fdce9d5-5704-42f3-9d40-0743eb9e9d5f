<template>
  <a-alert
    v-if="isLimit !== null"
    show-icon
    style="margin: 16px 0 -8px"
    type="error"
  >
    <template #message>
      <div
        v-if="isLimit"
        class="limit-msg"
      >
        店铺已被限制结算功能 ， 限制原因：{{
          isGuaranteeLimit ? '质保金账户余额＜3000元' : '租金账户余额＜0'
        }}，可点击<span
          class="link-text"
          @click="onHandleJump"
        >前往充值</span>，充值后将自动解除限制
      </div>
      <div v-else>
        店铺已被限制结算功能 ， 可联系运营人员进行处理。
      </div>
    </template>
  </a-alert>
</template>
<script lang="ts" setup>
import { ref } from 'vue';

import { GET } from '@/services/api';

/** true为系统管控，false是人工管控，null是没被管控 */
const isLimit = ref<boolean | null>(null);
/** 管控限制类型，1是租金账号，2是质保金账户 */
const limitType = ref();
/** 是否因质保金账户管控限制 */
const isGuaranteeLimit = ref(false);
const getLimitMsg = async () => {
  const { data } = await GET('/server-settlement-limitation/view');
  if (!Array.isArray(data)) {
    isLimit.value = data.is_system as boolean;
    limitType.value = data.type;
    isGuaranteeLimit.value = isLimit.value && limitType.value === '2';
  }
};
getLimitMsg();

const onHandleJump = () => {
  window.parent.postMessage(
    {
      action: 'blank',
      href: `/account/asset-index?isLimit=${limitType.value}`,
    },
    '*',
  );
};
</script>
<style lang="less" scoped>
.limit-msg {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;

  .link-text {
    color: #3777ff;
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
