export const groupTypeNum = ['', 'and', 'or', 'nor'];

export const handleInit = (item: any) => {
  let between = [undefined, undefined];
  let include = undefined;
  if (item.content) {
    const value = JSON.parse(item.content);
    if (value) {
      // 9（类目）组件回显的值在content_category，回显不需要拿content的值
      switch (item.factor_type) {
        case 1:
          between = [
            value[0] && value[0].length === 6 ? value[0].replace(/(\d{2})(\d{2})(\d{2})/, '$1:$2:$3') : undefined,
            value[1] && value[1].length === 6 ? value[1].replace(/(\d{2})(\d{2})(\d{2})/, '$1:$2:$3') : undefined,
          ];
          break;
        case 2:
        case 4:
        case 5:
        case 6:
        case 10:
          include = value;
          break;
        case 3:
        case 12:
        case 13:
          between = [Number(value[0]), Number(value[1])];
          break;
        case 7:
        case 8:
          include = value.join(',');
          break;
        case 9:
        case 14:
          include = item.content_category ? JSON.parse(item.content_category) : undefined;
          break;
        case 11:
          include = item.content;
          break;
      }
    }
  }
  return {
    id: Date.now() + Math.random(),
    value: item.factor_type,
    type: item.operator === 1 ? 'between' : 'include',
    between,
    include,
  };
};

export const handleResult = (item: any) => {
  let result = '';
  // 除了11（同城商品库）是string，剩余的都为string[]
  switch (item.value) {
    case 1:
      const startTime = item.between[0].replace(/:/g, '');
      const endTime = item.between[1].replace(/:/g, '');
      result = JSON.stringify([startTime.toString(), endTime.toString()]);
      break;
    case 2:
    case 4:
    case 5:
    case 6:
    case 10:
      result = JSON.stringify(item.include);
      break;
    case 3:
    case 12:
    case 13:
      result = JSON.stringify([item.between[0].toString(), item.between[1].toString()]);
      break;
    case 7:
    case 8:
      result = JSON.stringify(item.include.split(','));
      break;
    case 9:
    case 14:
      const filteredArray =
        item.value === 9
          ? item.include.filter(item => item.length === 2)
          : item.include.filter(item => item.length === 4);
      const lastArr = filteredArray.map(subArr => subArr[subArr.length - 1].toString());
      result = JSON.stringify(lastArr);
      break;
    case 11:
      result = item.include;
      break;
  }
  return result;
};
