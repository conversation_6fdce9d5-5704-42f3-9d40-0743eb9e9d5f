<template>
  <!-- 关联订单 -->
  <a-modal
    v-model:visible="bindVisible"
    :mask-closable="false"
    title="关联订单"
    :width="850"
  >
    <a-table
      :columns="columns"
      :data-source="orderList"
      :loading="loading"
      :pagination="false"
      :scroll="{ y: 500 }"
    >
      <template #bodyCell="{ column, index, text }">
        <template v-if="column.key === 'trade_no' && index === 0">
          <a-button
            type="link"
            @click="linkToTrade()"
          >
            查看流水
          </a-button>
        </template>
        <template v-if="column.key === 'order_id'">
          <a-button
            type="link"
            @click="linkToList(text)"
          >
            {{ text }}
          </a-button>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import type { TableColumnType } from 'ant-design-vue';

import { useVModel } from '@/hook';

import { checkCloseOrder, getSuppleOrder, getSuppleOrder2 } from './services';

interface ISuppleOrderType {
  order_source: string;
  order_id: string;
  trade_no: string;
  money: string;
  created_at: string;
}

const props = defineProps<{
  visible?: boolean;
  orderId: string;
  orderPageUrl?: string;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);
const orderList = ref<ISuppleOrderType[]>([]);
const loading = ref(false);

const route = useRoute();
const isSuper = route.query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  const api = isSuper ? getSuppleOrder : getSuppleOrder2;
  api({ orderId: props.orderId })
    .then(res => {
      orderList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 列表配置 */
const columns: TableColumnType[] = [
  {
    title: '订单用途',
    dataIndex: 'order_source',
    key: 'order_source',
    align: 'center',
  },
  {
    title: '订单号',
    dataIndex: 'order_id',
    key: 'order_id',
    align: 'center',
  },
  {
    title: '流水号',
    dataIndex: 'trade_no',
    key: 'trade_no',
    align: 'center',
  },
  {
    title: '金额',
    dataIndex: 'money',
    key: 'money',
    align: 'center',
  },
  {
    title: '时间',
    dataIndex: 'created_at',
    key: 'created_at',
    align: 'center',
  },
];

const domain = route.query.origin || window.location.origin;

/** 查看流水 */
function linkToTrade() {
  if (isSuper) {
    // 运营用户跳转链接
    window.open(`${domain}/super/account-v2/index?type=all&order_id=${props.orderId}`);
  } else {
    // 商家用户跳转链接
    window.open(`${domain}/deposit/log?order_id=${props.orderId}`);
  }
}

/** 链接到关联订单号的订单列表 */
async function linkToList(orderId: string) {
  // 自定义跳转链接
  if (props.orderPageUrl) {
    window.open(`${domain}${props.orderPageUrl}?order_id=${orderId}`);
    return;
  }
  const { data } = await checkCloseOrder({ order_id: orderId });
  if (isSuper) {
    // 运营用户跳转链接
    window.open(`${domain}/super/v4-order/index?order_id=${orderId}&order_category=${data.is_close ? 2 : 1}`);
  } else {
    // 商家用户跳转链接
    window.open(`${domain}/order/v4-order-index?order_id=${orderId}&order_category=${data.is_close ? 2 : 1}`);
  }
}
</script>
