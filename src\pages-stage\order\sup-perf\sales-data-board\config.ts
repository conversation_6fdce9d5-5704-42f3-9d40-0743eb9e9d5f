import { reactive, ref } from 'vue';
import type { IPageLayoutConfig } from '../data-board-common-components/data';

export const getPageBaseConfig = () => {
  const title = ref('数据看板');
  const pageLayoutConfig = reactive<IPageLayoutConfig>({
    navs: ['订单', '主管销售业绩', title.value],
    title: title.value,
    layoutStyle: {
      minHeight: 'max-content',
      margin: 0,
    },
  });

  return {
    pageLayoutConfig,
    title,
  };
};
