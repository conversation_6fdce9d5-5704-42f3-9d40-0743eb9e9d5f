import { ref } from 'vue';
import { handleOptionsParams, IOptions, ITbDataItem } from '../data';
import { apiGetFreezeRuleOptions } from '../services';
import { useConfig } from '../config';

export function useOptions() {
  const taDataItem = ref<ITbDataItem[]>([]);
  function createTemplate(condition: IOptions[], deposit: IOptions[]) {
    const arr: ITbDataItem[] = [];
    deposit.forEach(depositItem => {
      condition.forEach(conditionItem => {
        arr.push({
          id: '',
          type: {
            category: [],
            model: [],
          },
          condition: conditionItem,
          deposit: depositItem,
          user_risk_levels: undefined,
        });
      });
    });
    return arr;
  }
  function handleOptions(options: handleOptionsParams) {
    const { condition_type_items, deposit_level_items } = options;
    // 生成标准模版
    taDataItem.value = createTemplate(condition_type_items, deposit_level_items);
  }
  async function getFreezeRuleOptions() {
    await apiGetFreezeRuleOptions().then(res => {
      const { condition_type_items, deposit_level_items, user_level_items } = res.data;
      const { handleTbCol } = useConfig();
      handleOptions({ condition_type_items, deposit_level_items });
      handleTbCol(user_level_items);
    });
  }
  return {
    taDataItem,
    createTemplate,
    handleOptions,
    getFreezeRuleOptions,
  };
}
