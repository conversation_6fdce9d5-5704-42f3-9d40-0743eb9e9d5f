import { TImgae } from '../store-info/components/shop-qualification/data.d';

export type TCateInfo = {
  target_id?: number;
  one_name: string;
  two_name: string;
};

export type TIndustryFormState = {
  dict_id: number;
  config_id?: number;
  config_name?: string;
  server_credential_industry_image: TImgae[];
  valid_start_at_label: string;
  valid_end_at_label: string;
  config_prompt: string;
  longTerm: boolean;
};

export type TResultImage = { image: string; watermark_image: string | undefined };
