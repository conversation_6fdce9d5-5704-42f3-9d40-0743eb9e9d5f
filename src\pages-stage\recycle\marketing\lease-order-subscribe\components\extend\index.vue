<template>
  <div
    class="extend"
    :class="{
      show: isShow,
      'show-btn': isShowBtn,
    }"
  >
    <div
      class="keynote row btn"
      @click="showChange"
    >
      <slot name="label" />
      <down-outlined
        v-show="isShowBtn"
        class="extend-btn"
        :class="{ active: isShow }"
      />
    </div>
    <drawer
      class="drawer"
      :value="isShow"
      @change="drawerChange"
    >
      <slot name="content" />
    </drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { DownOutlined } from '@ant-design/icons-vue';
import Drawer from '../drawer/index.vue';

const props = defineProps({
  defaultShow: {
    type: Boolean,
    default: false,
  },
});

const isShow = ref(props.defaultShow);
const isShowBtn = ref(false);

const emit = defineEmits(['change', 'btnVisible']);

/**
 * @description: 监听元素变化
 * @return {*}
 */
function drawerChange(value: boolean) {
  isShowBtn.value = value;
  emit('btnVisible', value);
}

/**
 * @description: 显示变化
 * @param {*} value
 * @return {*}
 */
function showChange() {
  isShow.value = !isShow.value;
  emit('change', isShow.value);
}

defineExpose({
  showChange,
});
</script>

<style scoped lang="less">
.extend.show-btn {
  font-size: 14px;
}
.extend.show-btn.show {
  .drawer {
    margin-top: 4px;
  }
}
.extend-btn {
  margin-left: 8px;
  color: rgba(6, 21, 51, 0.65);
  font-size: 12px;
  cursor: pointer;
  user-select: none;
}
.extend-btn.active {
  transform: rotateZ(180deg);
}

.drawer {
  color: rgba(6, 21, 51, 0.65);
}

.keynote {
  width: fit-content;
  color: rgb(6, 21, 51);
  font-size: 14px;
  white-space: nowrap;
}
</style>
