<template>
  <RModalForm
    ref="refRModalForm"
    v-model:open="bindVisible"
    :form-group="formGroup"
    :form-props="{ originProps: layout }"
    :submit="onSubmit"
    title="订单导出"
    :width="583"
  >
    <template #exportTime="{ formState }">
      <a-form-item
        :label="formState.exportDateType === 1 ? '创建订单日期' : '更新订单日期'"
        name="exportTime"
        :rules="[{ required: true, message: '请选择订单日期' }]"
      >
        <a-range-picker
          v-model:value="formState.exportTime"
          :disabled-date="disabledDate"
          style="width: 100%"
          value-format="YYYY-MM-DD"
          @calendar-change="onCalendarChange"
        />
      </a-form-item>
    </template>
    <template #exportType="{ formState }">
      <a-form-item
        label="订单范围"
        name="exportType"
        :rules="[{ required: true, message: '请选择订单范围' }]"
      >
        <a-radio-group
          v-model:value="formState.exportType"
          :options="orderTypeOptions"
          style="width: 100%"
        />
      </a-form-item>
    </template>
    <template #exportStatus="{ formState }">
      <a-form-item
        label="订单状态"
        name="exportStatus"
      >
        <a-select
          v-model:value="formState.exportStatus"
          mode="multiple"
          :options="orderStatusOptions"
          style="width: 100%"
        />
      </a-form-item>
    </template>
  </RModalForm>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { Dayjs } from 'dayjs';
import type { RModalFormInstance } from 'rrz-web-design';

import { useVModel } from '@/hook';
import { isRentHouseSymbol } from '@/pages-stage/admin-merchant/order/rent-house/order-list/injection-symbols';

import {
  exportOrderOptions,
  exportOrderStatus,
  exportRentOrderOptions,
  exportRentOrderStatus,
  formGroup,
} from './confing';
import { addExportList } from './service';

const refRModalForm = ref<RModalFormInstance | null>(null);
const isRentHouse = inject(isRentHouseSymbol, false);
const orderStatusOptions = computed(() => (isRentHouse ? exportRentOrderStatus : exportOrderStatus));
const orderTypeOptions = computed(() => (isRentHouse ? exportRentOrderOptions : exportOrderOptions));
async function onUpdate() {
  await nextTick();
  const useForm = refRModalForm.value!.useForm;
  useForm.setFieldsValue({
    exportDateType: 1,
    exportType: 1,
  });
}

const layout = {
  labelCol: { flex: '108px' },
};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();
const bindVisible = useVModel(props, 'visible', emit);

watch(
  () => props.visible,
  val => {
    if (val) {
      onUpdate();
    }
  },
  { immediate: true },
);

const loading = ref(false);

const onSubmit = (formState: any) => {
  return new Promise(async (resolve, reject) => {
    const { exportName, exportTime, exportType, exportDateType, exportStatus } = formState;
    const params = {
      start_time: exportTime[0],
      end_time: exportTime[1],
      custom_filename: exportName,
      order_range: exportType,
      time_type: exportDateType,
      order_status: exportStatus.join(','),
    };
    try {
      loading.value = true;
      await addExportList(params);
      Modal.confirm({
        title: '提示',
        content: '导出任务请求成功，需要跳转到我的导出中查看吗？',

        onOk() {
          goHandle();
        },
      });
      bindVisible.value = false;
      dates.value = [];

      resolve();
    } catch (error) {
      reject(error);
    }
  });
};

const route = useRoute();
const { origin } = route.query;
const goHandle = () => {
  // 开发环境调试用
  if (process.env.NODE_ENV === 'development') {
    window.open('/order/manage/export-list');
    return;
  }
  window.open(`${origin}/async-export/new-index`);
};

const disabledDate = (current: Dayjs) => {
  if (!dates.value || dates.value.length === 0) {
    return false;
  }
  // 现【创建订单日期】期望能够支持半年年的范围；
  const first = dayjs(dates.value[0]);
  const limitDays = Math.abs(first.diff(first.add(12, 'month'), 'days'));
  const diff = dayjs(current).diff(first, 'days');

  return Math.abs(diff) > limitDays;
};
const dates = ref<Dayjs[]>([]);
const onCalendarChange = (val: Dayjs[]) => {
  dates.value = val;
};
</script>
