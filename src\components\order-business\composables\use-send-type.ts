import { computed, ref } from 'vue';

const sendTypeMap = {
  1: '包邮',
  2: '到付',
  3: '上门安装',
  4: '到店自取',
  5: '统一运费',
  6: '运费模板',
  7: '租满XX天寄出包邮',
};

/**
 * 获取配送方式
 */
export const useSendType = (options: { api: (params: any) => Promise<IAxiosResponse<any>> }) => {
  const deliveryMethod = ref<{
    /** 代扣权限 */
    withhold: number;
    /** 寄出方式 */
    send_type: string;
    /** 租满xx天寄出包邮 */
    send_full_days: string;
  }>({
    withhold: 0,
    send_type: '',
    send_full_days: '',
  });

  /** 寄出方式的文本 */
  const sendTypeText = computed(() => {
    if (!deliveryMethod.value?.send_type) return;
    if (Number(deliveryMethod.value.send_type) === 7) {
      return `租满${deliveryMethod.value.send_full_days}天寄出包邮`;
    }
    return sendTypeMap[deliveryMethod.value.send_type];
  });

  /** 仅订单为寄出到付，且不满足代扣授权条件时展示 */
  const isWithhold = computed(() => {
    return Number(deliveryMethod.value?.send_type) === 2 && deliveryMethod.value?.withhold === 0;
  });

  /** 获取配送方式 */
  const getSuLogisticsData = async order_id => {
    const res = await options.api({
      order_id,
    });
    deliveryMethod.value = res.data;
  };

  return {
    deliveryMethod,
    sendTypeText,
    isWithhold,
    getSuLogisticsData,
  };
};
