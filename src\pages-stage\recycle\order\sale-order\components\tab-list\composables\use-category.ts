import { CascaderOptionType } from 'ant-design-vue/lib/cascader';
import { DefaultOptionType } from 'ant-design-vue/lib/select';
import { GET } from '@/services/api';
import { Ref } from 'vue';

export default () => {
  const spuDataListloadData = (
    selectedOptions: DefaultOptionType[] = [],
    currentValue: Ref<Array<number>>,
    options: Ref<CascaderOptionType[]>,
  ) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    if (!targetOption) return;
    targetOption.loading = true;
    const { hierarchy, value } = targetOption;
    const MAP = ['category_id', 'brand_id', 'model_id'];
    const params = {
      [MAP[hierarchy - 1]]: value,
    };
    if (currentValue.value?.[0] && hierarchy == 2) {
      params.category_id = currentValue.value?.[0];
    }

    GET('/warehouse/SpuDataList', params, {
      hostType: 'Golang',
    })
      .then(({ data }) => {
        const key = ['brand', 'model'][hierarchy - 1];
        if (key) {
          targetOption.children = handleSpuDataList(data[key], hierarchy + 1);
          options.value = [...options.value];
        }
      })
      .finally(() => {
        targetOption.loading = false;
      });
  };

  const handleSpuDataList = (data: any[], hierarchy: number) => {
    return data.reduce((acc, curr) => {
      const { id, name } = curr;
      acc.push({
        label: name,
        value: id,
        isLeaf: hierarchy === 3,
        hierarchy,
      });
      return acc;
    }, []);
  };

  return {
    spuDataListloadData,
    handleSpuDataList,
  };
};
