/** 模块的因子数据 */
export interface IContentOptions {
  /** 因子名称 */
  name: string;
  /** 因子编码 */
  code: string;
  /** 因子条件类型 */
  factor_type: number;
  /** 因子组件code */
  option_code?: string;
  /** 组件下拉选择项 */
  option_value?: {
    name: string;
    value: number;
  }[];
}

/** 选择的因子值 */
export interface IFactorContentRule {
  /** 条件 */
  condition?: any;
  /** 判断 */
  judge?: string;
  /** 结果 */
  result?: (string | number)[];
}

export interface IFactorContentData {
  _id?: string;
  relationship?: 'logic' | 'condition';
  level?: number;
  relation?: 'and' | 'or';
  rules?: IFactorContentData[] | IFactorContentRule;
}

export interface IComponentConfig {
  target: any;
  props?: Record<string, any>;
}

export interface IConditionConfig extends Record<string, IComponentConfig> {
  judges?: string[];
  default?: IComponentConfig;
}

export type TConditionMap = Record<string, IConditionConfig>;

/** updated触发时显示conditionMap所映射的因子选择 */
export interface IFactorCondition extends IConditionConfig {
  /** 选择的key */
  key?: string;
  /** result部分组件额外的属性 */
  props?: Record<string, any>;
}

export type TUpdatedFn = (value: any, old: any) => Promise<IFactorCondition | false | undefined>;

export interface IConditionRender {
  /** condition显示渲染的组件 */
  target?: any;
  /** 渲染的组件属性 */
  props?: Record<string, any>;
  /** 返回false或者reject时不会触发更新 */
  updated?: TUpdatedFn;
}
