<template>
  <a-modal
    v-model:visible="bindVisible"
    :title="title"
    :width="width"
  >
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <div
        class="content"
        :style="style"
        v-html="content"
      />
      <a-empty
        v-show="!content"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { CSSProperties, PropType, ref, watch } from 'vue';
import { Empty } from 'ant-design-vue';

import { useVModel } from '@/hook';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  width: {
    type: Number,
    default: 700,
  },
  style: {
    type: Object as PropType<CSSProperties>,
    default: () => ({}),
  },
  getContent: {
    type: Function as PropType<() => Promise<string | undefined> | string | undefined>,
    default: () => '',
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref<boolean>(false);
const content = ref<string>('');

watch(
  bindVisible,
  val => {
    if (val) {
      loadContent();
    }
  },
  { immediate: true },
);

async function loadContent() {
  try {
    content.value = '';
    loading.value = true;
    const data = await props.getContent();
    content.value = data || '';
  } catch (e) {
    bindVisible.value = false;
    console.error(e);
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped lang="less">
.content {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
