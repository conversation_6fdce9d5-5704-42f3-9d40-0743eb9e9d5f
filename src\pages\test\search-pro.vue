<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import moment from 'moment';
import type { FormGroupItem, RFormInstance, RTableColumnType } from 'rrz-web-design';
import { ERTableColumnValueType } from 'rrz-web-design';

import { SensitiveField } from '@/components/sensitive-field';
import uploadToOss from '@/utils/oss-upload';

const columns: RTableColumnType[] = [
  {
    title: '文章ID',
    dataIndex: 'id',
    width: 74,
  },
  {
    title: '时间筛选',
    dataIndex: 'rangePickerTab',
    valueType: 'rangePicker',
    formFieldProps: {
      elProps: {
        showSelectTab: true,
        tabSelectValue: 0,
        setTabs: defaultTabs => {
          const before = {
            label: '全部',
            value: -1,
            rangePickerValue: ['1999-01-08', '2021-02-15'],
          };
          // const after = [
          //   {
          //     label: '近365天',
          //     value: 365,
          //   },
          //   {
          //     label: '自定义',
          //     value: -2,
          //     rangePickerValue: ['2021-01-08', '2021-02-15'],
          //   },
          // ];
          return [before, ...defaultTabs]; //, ...after
        },
      },
      // colProps: {
      //   span: 24,
      // },
    },
    hideInTable: true,
  },
  {
    title: '文章标题',
    dataIndex: 'title',
    width: 479,
    hideInTable: true,
    formFieldProps: {
      initialValue: '测试标题',
    },
  },
  // 测试下拉
  {
    title: '下拉',
    dataIndex: 'select',
    valueType: 'select',
    width: 220,
    formFieldProps: {
      options: [
        {
          label: '1',
          value: '1',
        },
      ],
    },
    hideInTable: true,
  },
  // 测试提示语,hideInSearch
  {
    title: '文章标题tooltips',
    dataIndex: 'title',
    hideInSearch: true,
    tooltips: true,
  },
  {
    title: '流程类目',
    dataIndex: 'process_question_type',
    width: 88,
    hideInSearch: true,
  },
  {
    title: '更多类目',
    dataIndex: 'question_type',
    width: 88,
    hideInSearch: true,
  },
  // 测试时间类型
  {
    title: '测试创建时间',
    dataIndex: 'created_at',
    width: 173,
    hideInSearch: true,
    valueType: 'date',
  },
  // 测试敏感字段
  {
    title: '身份证',
    dataIndex: 'id_card',
    hideInSearch: true,
    valueType: 'sensitive',
    component: SensitiveField,
    sensitive: ({ record }) => {
      return {
        field: 'id_card',
        fieldType: '5',
        idKey: 'id',
        row: record,
        type: '90',
      };
    },
  },
  // 测试时间搜索转换
  {
    title: '时间',
    dataIndex: 'create_at',
    valueType: 'rangePicker',
    hideInTable: true,
    search: {
      transform: value => {
        return {
          start_time: value[0],
          end_time: value[1],
        };
      },
    },
  },
  // 测试状态
  {
    title: '使用状态',
    dataIndex: 'status',
    width: 98,
    hideInSearch: true,
    valueType: ERTableColumnValueType.STATUS,
    valueEnum: [{ label: '关闭', value: '1', color: 'red' }],
  },
  // 测试自定义插槽
  {
    title: '测试自定义插槽',
    dataIndex: 'customer',
    valueType: 'select',
    search: {
      transform: value => {
        return {
          test1: value,
          test2: value,
        };
      },
    },
  },
  // 测试操作
  {
    title: '操作',
    dataIndex: 'operation',
    width: 124,
    fixed: 'right',
  },
];

const formGroupTable = [
  {
    key: 'static_title',
    originProps: {
      label: '静态配置文章标题',
      name: 'title',
    },
    elProps: {
      allowClear: true,
      placeholder: '请输入',
      style: {
        width: '100%',
      },
    },
    fragmentKey: 'renderInput',
  },
  {
    key: 'select',
    originProps: {
      label: '我是静态配置',
      name: 'select',
    },
    elProps: {
      allowClear: true,
      placeholder: '请输入',
      style: {
        width: '100%',
      },
    },
    fragmentKey: 'renderSelect',
  },
];

const formRenderRef = ref<FormInstance>();

/**
 * @description: 测试自定义表单下 重置表单
 */
const resetFields = (cb: (...agr: any[]) => void) => {
  console.log('重置');
  formRenderRef.value?.resetFields();
  cb('search');
};

const apiSearchTable = () => {
  return {
    data: [
      {
        id: 1,
        title: '测试标题',
        process_question_type: '流程类目',
        question_type: '更多类目',
        created_at: '1888277721',
        id_card: '123456789',
        status: '1',
        customer: '1',
      },
    ],
    meta: {
      pagination: {
        page: 1,
        page_size: 10,
        total: 1,
      },
    },
  };
};

/** RForm */
const formRef = ref<RFormInstance | null>(null);
const formParams = reactive({
  renderInput: '',
  renderSelect: undefined,
  renderRadio: '',
  renderCheckbox: [],
  renderSwitch: false,
  renderInputNumber: 0,
  selectFetch: undefined,
  renderCascader: [],
  renderRangePicker: [],
  key_work_flag: 1,
  key_work: '',
  renderDatePicker: '',
});
const formGroup: FormGroupItem[] = [
  {
    key: 'otherfiles',
    fragmentKey: 'renderUpload',
    label: 'oss上传其他文件',
    elProps: {
      template: 'pictureCard',
      removeConfirm: true,
      uploadApi: uploadToOss,
      accept: '*',
    },
    changeHandler: () => {
      setTimeout(() => {
        console.log('formParams -->', formParams);
      }, 0);
    },
  },
  {
    key: 'img_urls',
    fragmentKey: 'renderUpload',
    originProps: {
      label: 'oss上传图片',
      name: 'img_urls',
      required: true,
    },
    elProps: {
      template: 'pictureCard',
      removeConfirm: true,
      uploadApi: uploadToOss,
    },
  },
  {
    key: 'renderInput',
    fragmentKey: 'renderInput',
    originProps: { label: 'renderInput测试', name: 'renderInput' },
    elProps: {
      placeholder: 'placeholder覆盖默认值测试',
    },
  },
  {
    key: 'renderSelect',
    originProps: {
      label: 'renderSelect测试',
      name: 'renderSelect',
    },
    options: [
      {
        label: '1',
        value: '1',
      },
      {
        label: '2',
        value: '2',
      },
    ],
    fragmentKey: 'renderSelect',
  },
  {
    key: 'selectFetch',
    fragmentKey: 'renderSelect',
    originProps: {
      label: 'renderSelect远程加载下拉测试',
      name: 'selectFetch',
    },
    elProps: {
      mode: 'multiple',
    },
    // everyTimeLoad: true,
    // mountedLoad: true,
    setOption: async () => {
      const { data } = await new Promise<{ data: any[] }>(resolve => {
        setTimeout(() => {
          resolve({
            data: [
              {
                value: 'jack',
                label: 'Jack',
              },
              {
                value: 'lucy',
                label: 'Lucy',
              },
            ],
          });
        }, 1000);
      });
      return data;
    },
    // elProps: {
    //   mode: 'multiple',
    //   placeholder: '请输入',
    // },
  },
  {
    key: 'renderRadio',
    fragmentKey: 'renderRadio',
    originProps: {
      label: 'renderRadio测试',
      name: 'renderRadio',
    },
    options: [
      { label: 'Apple', value: 'Apple' },
      { label: 'Pear', value: 'Pear' },
      { label: 'Orange', value: 'Orange' },
    ],
  },
  {
    key: 'renderCheckbox',
    fragmentKey: 'renderCheckbox',
    originProps: {
      label: 'renderCheckbox测试',
      name: 'renderCheckbox',
    },
    elProps: {
      options: ['Apple', 'Pear', 'Orange'],
    },
  },
  {
    key: 'renderSwitch',
    fragmentKey: 'renderSwitch',
    originProps: {
      label: 'renderSwitch测试',
      name: 'renderSwitch',
    },
    elProps: {
      style: {
        width: 'unset',
      },
    },
  },
  {
    key: 'renderInputNumber',
    fragmentKey: 'renderInputNumber',
    originProps: {
      label: 'renderInputNumber测试',
      name: 'renderInputNumber',
    },
  },
  // 测试时间搜索转换
  {
    key: 'renderRangePicker',
    fragmentKey: 'renderRangePicker',
    originProps: {
      label: 'renderRangePicker测试',
      name: 'renderRangePicker',
    },
  },
  {
    key: 'renderDatePicker',
    fragmentKey: 'renderDatePicker',
    originProps: {
      label: 'renderDatePicker测试',
      name: 'renderDatePicker',
    },
  },
  {
    key: 'renderMonthPicker',
    fragmentKey: 'renderMonthPicker',
    originProps: {
      label: 'renderMonthPicker测试',
      name: 'renderMonthPicker',
    },
  },
  {
    key: 'renderCascader',
    fragmentKey: 'renderCascader',
    originProps: {
      label: 'renderCascader远程加载下拉测试',
      name: 'renderCascader',
    },
    everyTimeLoad: true,
    // mountedLoad: true,
    setOption: async () => {
      const { data } = await new Promise<{ data: any[] }>(resolve => {
        setTimeout(() => {
          resolve({
            data: [
              {
                value: 'zhejiang',
                label: 'Zhejiang',
                children: [
                  {
                    value: 'hangzhou',
                    label: 'Hangzhou',
                    children: [
                      {
                        value: 'xihu',
                        label: 'West Lake',
                      },
                      {
                        value: 'test',
                        label: 'Test',
                      },
                    ],
                  },
                ],
              },
            ],
          });
        }, 1000);
      });
      return data;
    },
    elProps: {
      multiple: true,
      maxTagCount: 'responsive',
    },
  },
];

function handleSearch() {
  console.log('formParams -->', formParams);
}

function handleReset() {
  formRef.value?.getFormRef().resetFields();
}
</script>

<template>
  <!-- RForm -->
  <layout-admin-page title="超级表单">
    <div style="display: flex; padding: 0 50px">
      <RForm
        ref="formRef"
        v-model:value="formParams"
        :form-group="formGroup"
      >
        <template #buttons>
          <a-button @click="handleSearch">
            查询
          </a-button>
          <a-button @click="handleReset">
            重置
          </a-button>
        </template>
      </RForm>
    </div>
  </layout-admin-page>
  <!-- RTable -->
  <layout-admin-page title="超级表格">
    <RTable
      :api="apiSearchTable"
      :columns="columns"
      :form-group="formGroupTable"
      :table-props="{
        scroll: { x: 1500 },
      }"
      :use-route-params="['order_id']"
    >
      <template #tableTitle>
        <div>我是表头啊</div>
        {{ moment().format('YYYY-MM-DD HH:mm:ss') }}
      </template>
      <template #tableFooter>
        <div>我是表尾啊</div>
      </template>
      <template #toolBar>
        <a-button>工具栏</a-button>
      </template>
      <!-- 自定义表单项-->
      <template #customer="{ searchForm }">
        <a-form-item
          label="测试自定义"
          name="customer"
        >
          <a-input
            v-model:value="searchForm.customer"
            placeholder="请输入"
          />
        </a-form-item>
      </template>
    </RTable>
    <RTable
      :api="apiSearchTable"
      :columns="columns"
      :form-group="formGroup"
      :search="false"
      :table-props="{
        scroll: { x: 1500 },
      }"
    >
      自定义表单
      <template #formRender="{ searchForm, getTableList }">
        <a-form
          ref="formRenderRef"
          class="search-form-grid"
          :model="searchForm"
        >
          <a-form-item
            label="自定义"
            name="name"
          >
            <a-input
              v-model:value="searchForm.name"
              placeholder="请输入"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              type="primary"
              @click="getTableList('search')"
            >
              搜索
            </a-button>
            <a-button @click="resetFields(getTableList)">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </template>
    </RTable>
  </layout-admin-page>
</template>
