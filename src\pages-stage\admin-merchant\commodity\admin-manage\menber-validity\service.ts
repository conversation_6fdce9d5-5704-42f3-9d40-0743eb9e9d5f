import { GET, POST } from '@/services/api';

export const fetchAll = (params: any): Promise<any> => {
  return GET('/admin/index-inf', params);
};

export const addUserInfo = (data: {
  User: {
    username: string;
    phone: string;
    id: any;
  };
}): Promise<any> => {
  return POST('/admin/create', data);
};

export const fetchOne = (id: string): Promise<any> => {
  return GET(`/admin/update/${id}`);
};

export const updateUserInfo = (data: {
  User: {
    username: string;
    phone: string;
    id: any;
  };
  id: any;
}): Promise<any> => {
  return POST('/admin/update', data);
};

export const deleteUserInfo = (data: any): Promise<any> => {
  return POST('/admin/delete', data);
};

export const fetchTreeData = (params: any): Promise<any> => {
  return GET('/admin/auth-inf', params);
};

export const updateTreeData = (data: any): Promise<any> => {
  return POST('/admin/auth-inf', data);
};
// 通过商家可分配的权限id(对有效期页面来说是全部)
export const getRouteForAuth = (data?: any): Promise<any> => {
  return GET('/server-user-auth-time/get-route-for-auth', data);
};
// 通过用户id获取其已分配权限的交集
export const getUserCommonalityAuth = (data: any): Promise<any> => {
  return GET('/server-user-auth-time/get-user-commonality-auth', data);
};
// 获取名字下拉框
export const getUserNameOption = (data?: any): Promise<any> => {
  return GET('/server-user-auth-time/get-user-name-option', data);
};
// 获取列表
export const getValidityList = (data: any): Promise<any> => {
  return POST('/server-user-auth-time/list', data);
};
// 切换状态
export const switchStatus = (data: any): Promise<any> => {
  return POST('/server-user-auth-time/switch-status', data);
};
// 添加权限有效期
export const addAuthTime = (data: any): Promise<any> => {
  return POST('/server-user-auth-time/add', data);
};
// 修改权限有效期
export const editAuthTime = (data: any): Promise<any> => {
  return POST('/server-user-auth-time/modify-data', data);
};
