export namespace Api {
  export interface IApiGetInteractRiskDetail {
    alipay_user_ids: string;
    aliUserIdConfig: Data.IZfbPhoneItem[];
    id: string;
  }
}

export namespace Data {
  export interface IZfbPhoneItem {
    max_ali_id: string;
    min_ali_id: string;
    id?: string;
  }
  export interface ISettingForm {
    id: string;
    alipay_user_ids: string[];
    aliUserIdConfig: IZfbPhoneItem[];
  }
}
