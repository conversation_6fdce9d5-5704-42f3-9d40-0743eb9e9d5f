<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px 24px' }"
    top-fixed
    :top-style="{ padding: '24px 24px 0 24px' }"
  >
    <template #title>
      <ArrowLeftOutlined
        style="margin-right: 18px"
        @click="goBack"
      />
      <span style="font-size: 22px">账号信息</span>
    </template>
    <div class="account-title">
      账号信息设置
      <img
        class="account-title-icon"
        src="https://img1.rrzuji.cn/uploads/scheme/2402/04/m/cfSF1CbmJ1yOYoVeFN1a.png"
      >
    </div>
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formModal"
      :rules="rules"
    >
      <a-form-item
        label="用户姓名"
        name="user_name"
      >
        <a-space>
          <a-input
            v-model:value="formModal.user_name"
            :disabled="formItemStatus.nameDisabled"
            placeholder="请输入"
            style="width: 300px"
          />
          <a-button
            v-if="formItemStatus.nameDisabled"
            @click="formItemStatus.nameDisabled = false"
          >
            修改
          </a-button>
          <span
            v-else
            style="color: #9fa3a6"
          >请填写您的真实姓名</span>
        </a-space>
      </a-form-item>
      <a-form-item
        label="手机号码"
        name="current_phone"
      >
        <a-space>
          <a-input
            v-model:value="formModal.current_phone"
            :disabled="formItemStatus.phoneDisabled"
            placeholder="请输入原手机号码"
            style="width: 300px"
          />
          <a-button
            v-if="!formItemStatus.editNumber"
            @click="changeBindPhone"
          >
            修改
          </a-button>
        </a-space>
      </a-form-item>
      <a-form-item
        v-show="formItemStatus.editNumber"
        label="手机验证码"
        name="code"
      >
        <a-space>
          <a-input
            v-model:value="formModal.code"
            placeholder="请输入验证码"
          />
          <aliyun-captcha
            v-if="isShowCaptcha"
            button-id="phoneCode"
            :config="phoneCaptchaConfig"
            @change-show="changeCaptchaShow"
          />
          <!-- 消除验证码按钮闪烁 -->
          <a-button
            v-if="!isShowCaptcha"
            type="primary"
          >
            获取验证码
          </a-button>
        </a-space>
      </a-form-item>
      <a-form-item
        v-if="formItemStatus.editNumber"
        label="新手机号码"
        name="new_phone"
      >
        <a-space>
          <a-input
            v-model:value="formModal.new_phone"
            placeholder="请输入新的手机号码"
            style="width: 300px"
          />
        </a-space>
      </a-form-item>
      <a-form-item
        v-if="formItemStatus.editNumber"
        label="新手机验证码"
        name="new_code"
      >
        <a-space>
          <a-input
            v-model:value="formModal.new_code"
            placeholder="请输入验证码"
          />
          <aliyun-captcha
            v-if="isShowCaptcha"
            button-id="newPhoneCode"
            :config="newPhoneCaptchaConfig"
            @change-show="changeCaptchaShow"
          />
          <!-- 消除验证码按钮闪烁 -->
          <a-button
            v-if="!isShowCaptcha"
            type="primary"
          >
            获取验证码
          </a-button>
        </a-space>
      </a-form-item>
      <a-form-item label="登录密码">
        <a-space>
          <a-input
            v-model:value="formModal.password"
            :disabled="formItemStatus.passwordDisabled"
            placeholder="******"
            style="width: 300px"
          />
          <a-button @click="openChangePasswordModal">
            修改
          </a-button>
        </a-space>
      </a-form-item>
      <a-form-item label="绑定微信">
        <a-space>
          <a-input
            v-model:value="formModal.weixin"
            :disabled="formItemStatus.weixinDisabled"
            placeholder="请绑定微信"
            style="width: 300px"
          />
          <a-button
            v-if="!WxBindStatus"
            @click="openBindWeixinModal('add')"
          >
            绑定
          </a-button>
          <template v-else>
            <a-button @click="unbindWxHandle">
              解绑
            </a-button>
            <a-button @click="openBindWeixinModal('replace')">
              换绑
            </a-button>
          </template>
        </a-space>
      </a-form-item>
      <a-form-item>
        <a-button
          type="primary"
          @click="saveUserInfo"
        >
          保存资料
        </a-button>
      </a-form-item>
    </a-form>
    <ChangePassword v-model:visible="changePasswordVisible" />
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import type { FormInstance } from 'ant-design-vue';
import { message, Modal } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';

import ChangePassword from './components/change-password.vue';
import { initAliyunCaptchaConfig } from './composables/aliyun-captcha-config';
import { apiSaveUserInfo, getIsBind, getUserInfo, getWxBindConfig, unbindWx } from './service';

const formRef = ref<FormInstance>();
const formModal = ref({
  /** 用户名 */
  user_name: '',
  /** 当前手机 */
  current_phone: '',
  /** 当前手机验证码 */
  code: '',
  /** 新手机 */
  new_phone: '',
  /** 新手机验证码 */
  new_code: '',
  /** 密码 */
  password: '',
  /** 绑定微信 */
  weixin: '',
});
/** 表单初始禁用状态 */
const formItemStatus = ref({
  nameDisabled: true,
  phoneDisabled: true,
  passwordDisabled: true,
  weixinDisabled: true,
  editNumber: false,
});
/** 微信绑定状态 */
const WxBindStatus = ref(false);
// 修改密码弹窗显示/隐藏
const changePasswordVisible = ref(false);

const isShowCaptcha = ref(true);

const route = useRoute();

const openEditPassModal = computed(() => {
  return route.query.openEditPass;
});

// 验证流程完毕后需要将验证码组件卸载（从dom中移除）
const changeCaptchaShow = () => {
  isShowCaptcha.value = !isShowCaptcha.value;
  setTimeout(() => {
    isShowCaptcha.value = !isShowCaptcha.value;
  }, 0);
};

/** ======================表单校验规则====================== */
const validatorNewPhone = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject('请输入新的手机号码');
  } else {
    if (!/^1[3456789]\d{9}$/.test(value)) {
      return Promise.reject('请输入正确的手机号码');
    }
    return Promise.resolve();
  }
};

const rules: Record<string, Rule[]> = {
  user_name: [{ required: true, trigger: 'blur', message: '用户姓名不能为空' }],
  code: [{ required: true, trigger: 'blur', message: '请输入验证码' }],
  new_phone: [{ required: true, validator: validatorNewPhone, trigger: 'blur' }],
  new_code: [{ required: true, trigger: 'blur', message: '请输入验证码' }],
};

// 验证码配置
const phoneCaptchaConfig = initAliyunCaptchaConfig(formModal, 'current_phone', 'old');
const newPhoneCaptchaConfig = initAliyunCaptchaConfig(formModal, 'new_phone');

// 展开修改绑定手机
function changeBindPhone() {
  formItemStatus.value.editNumber = true;
}

// 打开修改密码弹窗
function openChangePasswordModal() {
  changePasswordVisible.value = true;
}
// 打开绑定微信弹窗

async function openBindWeixinModal(type: 'add' | 'replace') {
  const { status, data } = await getWxBindConfig({ type });
  if (status === 0) {
    window.open(
      `https://open.weixin.qq.com/connect/qrconnect?appid=${data.appid}&redirect_uri=${data.redirect_url}&response_type=code&scope=snsapi_login&state=${data.state}#wechat_redirect`,
      '_blank',
    );
  }
}

// 删除query参数
function deleteParams() {
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search);
  params.delete('msg');
  params.delete('error');
  url.search = params.toString();
  history.replaceState(null, '', url.toString());
}

/** 解绑微信 */
function unbindWxHandle() {
  Modal.confirm({
    title: '解绑微信',
    content: '确定要解绑微信吗？',
    onOk() {
      unbindWx().then(() => {
        message.success('已解绑');
        setTimeout(() => {
          deleteParams();
          window.location.reload();
        }, 500);
      });
    },
  });
}

type TData = {
  'UserForm[username]': string;
  'UserForm[msgCode]'?: string;
  'UserForm[newPhone]'?: string;
  'UserForm[newMsgCode]'?: string;
};
const data: TData = {
  'UserForm[username]': '',
};
/** 保存用户信息 */
async function saveUserInfo() {
  if (formItemStatus.value.editNumber) {
    await formRef.value?.validateFields();
  } else {
    await formRef.value?.validateFields(['user_name']);
  }
  if (!formModal.value.code || !formModal.value.new_code || !formModal.value.new_phone) {
    data['UserForm[username]'] = formModal.value.user_name;
  } else {
    data['UserForm[username]'] = formModal.value.user_name;
    data['UserForm[msgCode]'] = formModal.value.code;
    data['UserForm[newPhone]'] = formModal.value.new_phone;
    data['UserForm[newMsgCode]'] = formModal.value.new_code;
  }
  try {
    await apiSaveUserInfo(data);
    message.success('保存成功');
    setTimeout(() => {
      deleteParams();
      window.location.reload();
    }, 500);
  } catch {
    message.error('保存失败，请稍后再试');
  }
}

// 返回
function goBack() {
  window.history.go(-1);
}

/** 获取用户信息 */
async function initialData() {
  try {
    const { data } = await getUserInfo();
    formModal.value.user_name = data.userNameShow;
    formModal.value.current_phone = data.phone;
    formModal.value.weixin = data.wxUser ? data.wxUser.nickname : '';
  } catch {
    message.error('获取用户信息失败');
  }
}

/** 获取绑定状态 */
async function getIsBindInfo() {
  try {
    const { data } = await getIsBind();
    WxBindStatus.value = data.is_bind_wx === 0 ? false : true;
  } catch (error) {
    message.error('获取微信绑定状态失败');
  }
}

/** 获取微信授权登录后状态 */
function getWxLoginStatus() {
  const url = new URL(window.location.href);
  const error = url.searchParams.get('error');
  const msg = url.searchParams.get('msg');
  if (error) {
    message.error(decodeURIComponent(error));
  } else if (msg) {
    message.success(decodeURIComponent(msg));
  }
}

/** 判断是否打开修改密码弹窗 */
function checkOpenEditPassword() {
  if (openEditPassModal.value) {
    openChangePasswordModal();
  }
}

onMounted(() => {
  getIsBindInfo();
  initialData();
  getWxLoginStatus();
  checkOpenEditPassword();
});

// 更改页面顶级iframe属性，不然safari浏览器中，扫码成功重定向会被拦截
const changePageIframeAttr = function () {
  window.parent.postMessage(
    {
      action: 'changeFrameAttr',
    },
    '*',
  );
};
changePageIframeAttr();
</script>
<style lang="less" scoped>
.account-title {
  position: relative;
  padding: 24px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 16px;

  &-icon {
    position: absolute;
    top: 50%;
    left: 0;
    width: 6px;
    transform: translate(0, -50%);
  }
}
</style>
