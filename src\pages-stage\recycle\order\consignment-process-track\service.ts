import { GET, POST } from '@/services/api';

//寄卖订单列表
export function getSaleList(params: any) {
  return GET('/super/recycle/consign-sale-order/list', params);
}

/**
 * crm备注最新一条数据获取
 * @param params 商家ID
 * @returns
 */
export function ApiGetRemarks(data: {
  unionIds: string[];
  unionSuffix: string;
}): Promise<IAxiosResponse<RemarkType[]>> {
  return POST('/crm/data', data);
}

//所有兜底商列表
export function getAllCanvasserList(params: any) {
  return GET('/super/recycle/dealer-info/all-list', params);
}

// 兜底商交易登记
export function markDealer(params: any) {
  return POST('/super/recycle/consign-sale-order/dealer-mark', params);
}
