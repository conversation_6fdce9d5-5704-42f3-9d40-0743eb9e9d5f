<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    title="确认定价金额"
    :visible="visible"
    width="400px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item
        label="定价金额"
        name="purchased_price"
        :rules="[{ required: true, message: '请输入定价金额!' }]"
      >
        <a-input-number
          v-model:value="formState.purchased_price"
          placeholder="请输入定价金额"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message, FormInstance } from 'ant-design-vue';
import { searchCategory } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const submitLoading = ref(false);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref<any>({
  purchased_price: null,
});

const onCancel = () => emits('update:visible', false);

const onOk = () => {
  formRef.value?.validate().then(() => {
    submitLoading.value = true;
    searchCategory(props.orderId, formState.value.purchased_price)
      .then(() => {
        onCancel();
        message.success('操作成功');
        emits('onLoadList');
      })
      .finally(() => (submitLoading.value = false));
  });
};
</script>
