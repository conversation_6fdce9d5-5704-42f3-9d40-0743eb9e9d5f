<template>
  <layout-admin-page
    :navs="['趣回收', '库存管理', '库存查询表']"
    title="库存查询表"
  >
    <div class="container">
      <SearchGroup
        v-model:value="searchInfo"
        @confirm="getTableList"
      />
      <a-table
        class="table-box bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: 1112 }"
        @change="tableChange"
      >
        <template #headerCell="{ column }">
          <template v-if="column.tipsText">
            {{ column.title }}
            <a-tooltip placement="top">
              <template #title>
                <span>{{ column.tipsText }}</span>
              </template>
              <ExclamationCircleOutlined style="color: grey; font-size: 14px" />
            </a-tooltip>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { compact } from 'lodash';
import { useTable } from '@/hook/component/use-table';
import { ISearchInfo } from './data.d';
import { columns } from './config';
import SearchGroup from './components/search-group.vue';

const route = useRoute();

const searchInfo = ref<ISearchInfo>({
  source_type: '0',
  category_brand_model: [],
  sku_id: null,
  condition: [],
  no_sale_stock: 2,
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable<IDataSource, any>({
  url: '/super/recycle/data-count/warehouse-data',
  searchForm: searchInfo.value,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatSearchValue: (value: ISearchInfo) => {
    const params = {
      ...value,
    };
    delete params.category_brand_model;
    const [category_id, brand_id, model_id] = formatCategoryPrams(value.category_brand_model);
    delete params.condition;
    const condition = JSON.stringify(value.condition);
    return {
      ...params,
      category_id,
      brand_id,
      model_id,
      condition,
    };
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const formatCategoryPrams = (data: number[][]) => {
  const category_id: number[] = [],
    brand_id: number[] = [],
    model_id: number[] = [];
  if (data.length > 0) {
    data.forEach(item => {
      const [c, b, m] = item;
      if (item.length === 1) {
        category_id.push(c);
      } else if (item.length === 2) {
        category_id.push(c);
        brand_id.push(b);
      } else if (item.length === 3) {
        category_id.push(c);
        brand_id.push(b);
        model_id.push(m);
      }
    });
  }
  return [[...new Set(category_id)], [...new Set(brand_id)], [...new Set(model_id)]].map(i => i.join(','));
};

// 获取url参数进行默认查询
const getUrlParams = () => {
  const query = route.query || {};
  const brandKeys = ['category_id', 'brand_id', 'model_id'];
  const brandValues = compact(brandKeys.map(key => Number(query[key])));
  if (brandValues.length) {
    searchInfo.value.category_brand_model.push(brandValues);
  }
  if (query.sku_id) {
    searchInfo.value.sku_id = query.sku_id as string;
  }
};

onMounted(() => {
  getUrlParams();
  getTableList();
});
</script>

<style lang="less" scoped>
.container {
  padding: 2px 24px 26px 24px;
}
.table-box {
  margin-top: 24px;
}
</style>
