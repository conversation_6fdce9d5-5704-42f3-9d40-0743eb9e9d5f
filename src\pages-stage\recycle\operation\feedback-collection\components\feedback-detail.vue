<template>
  <a-modal
    v-model:visible="visible"
    destroy-on-close
    :footer="null"
    :title="getTitle"
    width="480px"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="formState"
      >
        <a-form-item label="用户手机号">
          <sensitive-field
            field="phone"
            field-type="1"
            id-key="id"
            :is-super-v2="true"
            :row="formState"
            :type="850"
          />
        </a-form-item>
        <a-form-item label="用户姓名">
          <sensitive-field
            field="username"
            field-type="2"
            id-key="user_id"
            :is-super-v2="true"
            :row="formState"
            :type="850"
          />
        </a-form-item>
        <a-form-item label="提交时间">
          {{ formState.created_at }}
        </a-form-item>
        <a-form-item label="反馈内容">
          {{ formState.content }}
        </a-form-item>
        <a-form-item label="反馈图片">
          <a-image-preview-group v-if="formState.images?.length">
            <a-image
              v-for="(item, index) in formState.images"
              :key="index"
              :src="item"
            />
          </a-image-preview-group>
          <span v-else>无</span>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { FormInstance } from 'ant-design-vue';

import { useModal } from '@/hook/component/use-modal';

import { IDispatchModal, IModalState } from '../data';
import { getModalContent } from '../service';

const enum EModalStatus {
  'VIEW' = 3, // 查看
}
const modalStatus = ref<EModalStatus>(3);
const formRef = ref<FormInstance>();
const currentId = ref('');

const formState = ref<Partial<IModalState>>({});

const { open, loading, visible } = useModal();

const setModal = (status: EModalStatus, id = '') => {
  if (typeof status === 'undefined') {
    throw new Error(`${status} -> '异常'`);
  }
  currentId.value = id;
  modalStatus.value = status;
  open();
};

const disPatch = async ({ action, params }: IDispatchModal<keyof typeof EModalStatus>) => {
  switch (action) {
    case 'VIEW':
      if (typeof params?.id === 'undefined') {
        throw Error(`action -> ${action} 致命错误:'ID参数丢失'`);
      }
      setModal(EModalStatus.VIEW, params.id);
      loading.value = true;
      getModalContent({ id: params.id })
        .then(res => {
          Object.assign(formState.value, res.data);
        })
        .finally(() => {
          loading.value = false;
        });
      break;
    default:
      throw Error('未知 action：' + action);
  }
};

const getTitle = computed(() => {
  switch (modalStatus.value) {
    case EModalStatus.VIEW:
      return '查看';
    default:
      throw Error('未知 modalStatus：' + modalStatus.value);
  }
});

defineExpose({
  disPatch,
});
</script>

<style lang="less" scoped>
:deep(.ant-form-item-control-input-content) {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
}
:deep(.ant-image) {
  width: 104px;
  height: 104px;
  margin-right: 8px;
  overflow: hidden;
}
</style>
