import { throttle } from 'lodash-es';
export default function ({
  targetClass,
  type,
  setWidth,
  clearWidth,
}: {
  targetClass: string;
  type: 'top' | 'bottom';
  setWidth(target: HTMLElement): void;
  clearWidth(target: HTMLElement): void;
}) {
  // target 顶部距离
  let targetTop = window.Infinity;
  // 是否吸顶状态
  let inCeiling = false;
  // 原有target
  let targetEle: HTMLElement | null = null;
  // 占位元素
  let seatEle: HTMLElement | null = null;

  /**
   * @description: 获取元素在页面距离顶部的偏移量
   * @param {*} element
   * @return {*}
   */
  function getElementTop(element: HTMLElement) {
    let actualTop = element.offsetTop;
    let current: any = element.offsetParent;

    while (current != null) {
      actualTop += current.offsetTop;
      current = current.offsetParent;
    }

    return actualTop;
  }

  /**
   * @description: 更新 targetTop
   * @return {*}
   */
  function updateTargetTop() {
    if (!targetEle) addTargetEle();
    if (!seatEle) addSeat(targetEle as HTMLElement);
    targetTop = getElementTop(inCeiling ? (seatEle as HTMLElement) : (targetEle as HTMLElement));
  }

  /**
   * @description: 计算是否越界
   * @return {*}
   */
  function isCrossBorder(html: HTMLElement, targetHeight: number): boolean {
    if (type === 'top') return html.scrollTop >= targetTop;
    if (type === 'bottom') return html.scrollTop + html.clientHeight < targetTop + targetHeight;
    return false;
  }

  /**
   * @description: 添加占位元素
   * @return {*}
   */
  function addSeat(targetEle: HTMLElement) {
    if (!targetEle) {
      console.warn('targetEle元素为空,不能进行克隆');
      return;
    }
    seatEle = targetEle.cloneNode(true) as HTMLElement;
    seatEle.style.opacity = '0';
    seatEle.style.display = 'none';
    targetEle.classList.add('suspension');
    const parent = targetEle.parentNode as HTMLElement;
    parent.insertBefore(seatEle, targetEle);
  }

  /**
   * @description: 添加吸顶效果
   * @return {*}
   */
  function addCeiling() {
    if (!targetEle || !seatEle) return;
    setWidth(targetEle);
    targetEle.classList.add('ceiling');
    seatEle.style.display = '';
    inCeiling = true;
  }

  /**
   * @description: 取消吸顶
   * @return {*}
   */
  function cancelCeiling() {
    if (!targetEle || !seatEle) return;
    clearWidth(targetEle);
    targetEle.classList.remove('ceiling');
    seatEle.style.display = 'none';
    inCeiling = false;
  }

  /**
   * @description: 缓存目标元素
   * @return {*}
   */
  function addTargetEle() {
    const target = targetEle || (document.querySelector('.' + targetClass) as HTMLElement);
    targetEle = target;
  }

  /**
   * @description: 吸顶效果
   * @return {*}
   */
  function targetEleCeiling() {
    const html = document.querySelector('html') as HTMLElement;
    if (!targetEle) addTargetEle();
    if (!seatEle) addSeat(targetEle as HTMLElement);
    const target = targetEle as HTMLElement;

    if (!inCeiling && isCrossBorder(html, target.clientHeight)) {
      throttle(addCeiling, 400, { leading: true })();
    } else if (inCeiling && !isCrossBorder(html, target.clientHeight)) {
      throttle(cancelCeiling, 400, { leading: true })();
    }
  }

  return {
    targetEleCeiling,
    updateTargetTop,
  };
}
