<template>
  <div class="page-container">
    <layout-admin-page
      class="custom-layout"
      :navs="nav"
    >
      <template #title-prefix>
        <a-space class="page-title">
          <arrow-left-outlined
            class="cursor"
            @click="goBack"
          />
          <span>{{ title }}</span>
        </a-space>
      </template>
    </layout-admin-page>
    <div class="step-container">
      <a-steps
        :current="step"
        size="small"
      >
        <a-step title="信息填选" />
        <a-step title="成新度设定" />
        <a-step title="完成创建" />
      </a-steps>
    </div>
    <div class="step-content">
      <step-first
        v-show="step === 0"
        ref="stepFirstRef"
        :form-data="formState"
      />
      <step-second
        v-show="step === 1"
        ref="stepSecondRef"
        v-model:ready="ready"
        :form-data="formState"
      />
      <a-result
        v-show="step === 2"
        status="success"
        title="估价模板创建成功"
      >
        <template #icon>
          <check-circle-filled />
        </template>
        <template #extra>
          <a-button @click="goBack">
            返回列表
          </a-button>
        </template>
      </a-result>
    </div>
    <div
      v-if="step < 2"
      class="action-panel flex-wrap flex-x-center flex-y-center"
    >
      <a-button
        size="large"
        @click="previousStep"
      >
        {{ step > 0 ? '上一步' : '取消' }}
      </a-button>
      <a-button
        :disabled="step === 1 && !ready"
        :loading="submitLoading"
        size="large"
        style="margin-left: 16px"
        type="primary"
        @click="nextStep"
      >
        {{ step > 0 ? (isEdit ? '保存修改' : '完成创建') : '下一步' }}
      </a-button>
    </div>
    <div
      v-if="step < 2"
      class="panel-placeholder"
      style="height: 88px"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { ArrowLeftOutlined, CheckCircleFilled } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import StepFirst from './components/step-first.vue';
import StepSecond from './components/step-second.vue';
import { queryTemplateDetail, saveTemplate } from './service';
import { ITemplate } from './data';

const router = useRouter();
const route = useRoute();
const isEdit = !!route.query.id;

// 顶部导航
const title = `${isEdit ? '编辑' : '新增'}估价模板`;
const nav = ['趣回收', '商品管理', '商品估价模板', title];
const goBack = () => router.go(-1);

// 步骤切换
const step = ref(0);
const ready = ref(false);
const submitLoading = ref(false);
const previousStep = () => {
  if (step.value === 0) {
    return goBack();
  }
  step.value -= 1;
};
const nextStep = async () => {
  try {
    if (step.value === 0) {
      const res = await stepFirstRef.value.validate();
      Object.assign(formState, res);
      step.value += 1;
      return;
    }
    if (step.value === 1) {
      submitLoading.value = true;
      const { condition_set, condition_type, qua_check_option, function_option, name, id } = formState;
      const payload = {
        condition_set,
        condition_type,
        qua_check_option: qua_check_option.map(({ origin_log_id }, idx) => ({ origin_log_id, sort: idx + 1 })),
        function_option: function_option.map(({ origin_log_id, is_multi }, idx) => ({
          origin_log_id,
          sort: idx + 1,
          is_multi,
        })),
        name,
        id,
      };
      saveTemplate(payload)
        .then(() => {
          if (isEdit) {
            submitLoading.value = false;
            message.success('保存成功');
            nextTick(() => {
              goBack();
            });
          } else {
            step.value += 1;
          }
        })
        .finally(() => {
          submitLoading.value = false;
        });
      return;
    }
  } catch (err) {}
};

const formState = reactive<ITemplate>({
  name: '',
  condition_type: [],
  qua_check_option: [],
  function_option: [],
  condition_set: [],
});

const stepFirstRef = ref();
const stepSecondRef = ref();

// 初始化
const init = () => {
  if (isEdit) {
    queryTemplateDetail(route.query.id as string).then(({ data }) => {
      Object.assign(formState, data);
    });
  }
};
init();
</script>

<style scoped lang="less">
.custom-layout {
  position: sticky;
  top: 0;
  z-index: 1;
  min-height: 0;
  margin: 0;
  padding: 16px 16px 0;
  background: #f7f7f7;
  .cursor {
    cursor: pointer;
  }
  .page-title {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
  :deep(.page-top) {
    margin-bottom: 0;
  }
}
.step-container {
  margin: 0 16px;
  padding: 32px 160px;
  background-color: #fff;
}
.action-panel {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  height: 88px;
  background: #fff;
  box-shadow: 0 -2px 20px 0 rgba(0, 0, 0, 0.05);
}
.step-content {
  padding: 16px;
}
:deep(.ant-result-success .ant-result-icon > .anticon) {
  color: var(--ant-primary-color);
}
:deep(.ant-result) {
  background: #fff;
}
</style>
