<template>
  <a-modal
    v-model:visible="bindVisible"
    title="订单业务时间表"
  >
    <a-spin :spinning="loading">
      <a-list
        :data-source="dataSource"
        item-layout="horizontal"
      >
        <template #renderItem="{ item }">
          <a-list-item class="list-item">
            <a-list-item-meta>
              <template #title>
                {{ item.title }}
              </template>
              <template #description>
                <span style="color: rgba(6, 21, 51, 0.65)">{{ item.desc }}</span>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { getTimetable } from './services';
import { useVModel } from '@/hook';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  createAt: {
    type: String,
    default: '',
  },
});

type ItemType = {
  title: string;
  desc: string;
};

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const placeDate = ref('');

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

/** 初始化加载数据 */
function loadData() {
  loading.value = true;
  getTimetable({ order_id: props.orderId })
    .then(res => {
      if (res.data) {
        placeDate.value = res.data.data?.created_at || props.createAt || '无';
      }
    })
    .catch(() => {
      bindVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

const dataSource = computed<ItemType[]>(() => {
  return [
    {
      title: '创建订单时间：' + props?.createAt,
      desc: '受转单&派单业务影响，更改创建订单时间为转单或派单成功后的时间',
    },
    {
      title: '用户下单时间：' + placeDate.value,
      desc: '不受转单&派单业务影响，当前字段只记录用户从客户端下单的时间',
    },
  ];
});
</script>

<style lang="less" scoped>
:deep(.ant-list-item-meta-description) {
  margin-bottom: 24px;
}

:deep(.ant-list-item):last-child {
  .ant-list-item-meta-description {
    margin-bottom: 0;
  }
}
</style>
