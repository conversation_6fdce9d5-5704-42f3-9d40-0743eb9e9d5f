<template>
  <div
    class="head-trigger"
    :style="triggerStyle"
    @click="toggleVisible"
  >
    <slot
      :expand="visible"
      name="trigger"
      :toggle="toggleVisible"
    />
  </div>

  <div
    class="drawer"
    :style="{
      height: (visible ? height : 0) + 'px',
    }"
  >
    <div ref="InnerBox">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, CSSProperties, onMounted, onUnmounted, PropType, ref } from 'vue';
import { throttle } from 'lodash-es';

const emit = defineEmits(['change', 'update:expand']);

const props = defineProps({
  /** 展开状态，可控 */
  expand: {
    type: Boolean,
    default: undefined,
  },
  /** 默认展开状态 */
  defaultExpand: {
    type: Boolean,
    default: false,
  },
  /** trigger样式 */
  triggerStyle: {
    type: Object as PropType<CSSProperties>,
    default: () => {},
  },
});

const show = ref(props.defaultExpand);

const visible = computed({
  get: () => props.expand ?? show.value,
  set: (val: boolean) => {
    show.value = val;
    emit('update:expand', val);
  },
});

let observer: ResizeObserver | null = null;

const InnerBox = ref<HTMLElement>();

const height = ref(0);

function updateHeight() {
  height.value = InnerBox.value?.clientHeight as number;
  emit('change', !!height.value);
}

const throttleUpdate = throttle(updateHeight, 300);

function toggleVisible() {
  visible.value = !visible.value;
}

onMounted(() => {
  updateHeight();
  observer = new ResizeObserver(throttleUpdate);
  observer.observe(InnerBox.value as HTMLElement);
});

onUnmounted(() => {
  observer && observer.disconnect();
});
</script>

<style lang="less" scoped>
.head-trigger {
  cursor: pointer;
}

.drawer {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
</style>
