import { EBasicIndicator } from './config';

export interface IGetWarningRecordsParams {
  warning_type?: number;
  basic_indicator?: string;
  search_key_word?: string;
}

export interface IWarningRecord {
  others: {
    refresh_str: string;
    period_num_rule_path: string;
  };
  warning_num_info: {
    all_warning_num: number;
    warning_limit_warning_num: number;
    daily_operation_warning_num: number;
    delivery_rate_warning_num: number;
    avg_prepayment_period_num_warning_num: number;
    avg_valid_response_second_warning_num: number;
    avg_sent_second_warning_num: number;
    pre_deposit_warning_num: number;
  };
  warning_limit: {
    indicator_warning_items: IWarningItem[];
  };
  warning_daily_operation: {
    indicator_warning_items: IWarningItem[];
  };
}

export interface IDataObj {
  exception_result_type: Record<string, any>;
  item: Record<string, any>;
  notify_type: Record<string, any>;
  penalty_content_type: Record<string, any>;
  punishment_type: Record<string, any>;
  risk_level: Record<string, any>;
  subject: Record<string, any>;
  indicator: Record<string, any>;
  coupon_type: Record<string, any>;
  violation_status: Record<string, any>;
}

export interface IWarningItem {
  hit_id: number;
  rule_id: number;
  warning_type: number;
  warning_suggest: string;
  title: string;
  order_ids: number[];
  last_value: number;
  value: number;
  basic_indicator: EBasicIndicator;
  special_tag?: number;
  conf_symbol: string;
  conf_value: number;
  date_window_str: string;
  date_window: {
    last_days?: number;
    first_days?: number;
  };
  warning_time: number;
  warning_time_str: string;
  course_id: number;
}

export type TItem = IWarningItem;

interface IShopLevelItem {
  class_limit: number;
  level_id: number;
}
interface IValueItem {
  is_open_dispatch: boolean;
  level_id: number;
  shop_level: IShopLevelItem[];
  today_order_num: number;
  today_order_num_limit: number;
  today_schedule_plan: string;

  basic_indicator: string;
  last_value: number;
  show_val: string;
  title: string;
  unit: string;

  indicator_name: string;
  is_penalty_of_restrict_flow: boolean;
  raw_conf_val: string;
  raw_real_val: string;
  rule_symbol_name: string;
  show_conf_val: string;
  show_real_val: string;
  symbol: string;

  activity_num: number;
  can_activity_num: number;
  server_id: number;

  content: string;
  status: number;
  status_txt: string;
  title_content: string;
  violation_id: number;

  accept_new_rate: number;
  activity_id: number;
  last_day_activity_flow_num: number;

  order_ids?: number[];

  coupon_name: string;
  reward_order_num: number;
  coupon_type: number;
  receive_at: number;
  estimate_expire_at: number;
}

export interface IPenaltyItem {
  days: number;
  end_date: number;
  instance: string;
  new_old_type: string;
  shop_diag_penalty_content_type: number;
  start_date: number;
  touch_type: number;
}
interface IPenaltyContentsItem {
  id: number;
  penalty: string | IPenaltyItem;
  source: string;
  penalty_type: number;
}

interface IAffectItem {
  penalty_type: number;
  penalty_contents: IPenaltyContentsItem[];
}

interface IModuleLinesItem {
  abnormal_affect_analysis: IAffectItem[];
  affect_of_value: IAffectItem[][];
  indicator: number;
  jumperLink: string;
  jumper_name: string;
  notifyType: number;
  value: IValueItem[];
}

interface IShopItem {
  module: number;
  moduleLines: IModuleLinesItem[];
}
export interface IShopData {
  business: IShopItem;
  operation: IShopItem;
  overview: {
    risk_level: number;
    updated_at_txt: string;
  };
  perform: boolean;
}

export interface ITableItemType {
  label: string;
  reachValue: string | number;
  realityValue: string | number;
  is_pass: boolean;
}

export interface IPenaltyResult {
  is_invalid: boolean;
  children?: IPenaltyResultChild[];
  resultChild?: IPenaltyResultChild;
}

export interface IPenaltyResultChild {
  date_range_str: string;
  invalid_time_str: string;
  is_invalid: boolean;
  name: string;
  penalty_content: string;
}

export interface IResultItem {
  name: string;
  tableList: ITableItemType[];
}
