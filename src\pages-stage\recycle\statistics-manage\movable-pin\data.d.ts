import { Dayjs } from 'dayjs';

export interface ISearchInfo {
  origin: null | string;
  rangeDate: Dayjs[];
  rangeDateStrList: string[];
}

export interface IIndexRes {
  data_count: Record<string, IIndexDataCountValue>;
  date: string;
  week: string;
}

export interface IIndexDataCountValue {
  number: string;
  update_time: string;
  name: string;
  unit: string;
  tips: string;
  date_between: string;
  start_at: string;
  end_at: string;
  key?: string;
}

export interface IGmvDayRes {
  list: IGmvDayInfo[];
  days: number;
  start_at: string;
  end_at: string;
  tips: string;
  date_text: string;
  gmv_today: string;
  date_between: string;
  gmv_count: string;
  gmv_average: string;
  unit: string;
  update_time: string;
}

export interface IGmvDayInfo {
  date: string;
  gmv: number;
  year: string;
  tipsText?: string;
  label?: string;
}

export interface IGmvContributionRes {
  list: IGmvContributionInfo[];
  start_at: string;
  end_at: string;
  date_between: string;
  update_time: string;
}

export interface IGmvContributionInfo {
  terminal_name: string;
  gmv: string;
  order_count: string;
}
