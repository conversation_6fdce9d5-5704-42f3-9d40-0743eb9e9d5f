import type { RTableColumnType } from 'rrz-web-design';

import { getServerCredentialConfigList } from '@/pages-stage/commodity/dict/qualification/components/qualification-conifg/service';
import { EQualificationFileType } from '@/pages-stage/commodity/dict/qualification/data.d';
import { ECredentialStatus } from '@/pages-stage/server/audit/qualification/data.d';

import { getHasQualificationCategory } from './service';
export const goodsInfoColumns: RTableColumnType[] = [
  {
    title: '资质类型',
    dataIndex: 'config_id',
    valueType: 'select',
    formFieldProps: {
      setOption: async () => {
        const { data } = await getServerCredentialConfigList({ page_size: -1 });
        return data.reduce((acc: Array<{ label: string; value: number }>, item: any) => {
          if (item.config_type === EQualificationFileType.GOODS) {
            acc.push({
              label: item.name,
              value: item.id,
            });
          }
          return acc;
        }, []);
      },
    },
    hideInTable: true,
  },
  {
    title: '类目',
    dataIndex: 'secondary_category',
    valueType: 'select',
    formFieldProps: {
      setOption: async () => {
        const { data } = await getHasQualificationCategory();
        return (
          data.list?.map((item: any) => ({
            label: item.name,
            value: item.id,
          })) || []
        );
      },
    },
    hideInTable: true,
  },
  {
    title: '资质状态',
    dataIndex: 'status',
    valueType: 'select',
    formFieldProps: {
      options: [
        {
          label: '不可使用',
          value: ECredentialStatus.Disabled,
        },
        {
          label: '可使用',
          value: ECredentialStatus.Usable,
        },
        {
          label: '已过期',
          value: ECredentialStatus.Expired,
        },
      ],
    },
    hideInTable: true,
  },
  {
    dataIndex: 'config_name',
    title: '资质类型',
    width: 200,
    hideInSearch: true,
  },
  {
    dataIndex: 'customize_name',
    title: '资质名称',
    width: 200,
  },
  {
    dataIndex: 'updated_at_label',
    title: '更新时间',
    width: 200,
    hideInSearch: true,
  },
  {
    dataIndex: 'status_label',
    title: '使用状态',
    width: 150,
    hideInSearch: true,
  },
  {
    dataIndex: 'release_status_label',
    title: '审核状态',
    width: 150,
    hideInSearch: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 150,
    fixed: 'right',
  },
];
