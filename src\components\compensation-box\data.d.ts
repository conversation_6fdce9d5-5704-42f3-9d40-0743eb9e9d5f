export type TItemValueList = {
  id?: string;
  item: string;
  desc: string;
  degree?: string;
  damage_type: string;
  money: string;
  platform_pay_money?: number;
  user_pay_money?: number;
  temp_money?: string;
  haveObjection?: boolean;
};

export type TItemValueListKeys = keyof TItemValueList;
export type TItemValue = {
  list: TItemValueList[];
  url: TUrl[];
  selectRows?: TItemValueList[];
};

export type TComTemplateData = {
  [key: string]: TItemValue;
};

export type TUrl = {
  url: string;
  [key: string]: any;
};

export type TPeportData = {
  report_before_check_report: any;
  report_after_check_report: any;
};

export type TCompensationFormData = {
  /** 总额 */
  money?: number | string;
  /** 异常项数量 */
  template_num?: number;
  /** 归还前检测报告 */
  report_before?: number;
  /** 归还后检测报告 */
  report_after?: number;
  /** 异常项数据 */
  template_data: TComTemplateData;
  /** 平台支付金额*/
  platform_pay_money?: number | string;
  /** 用户支付金额*/
  user_pay_money?: number | string;
  /** 1未支付，2已支付 */
  /** 平台支付状态*/
  platform_pay_status?: string;
  /** 用户支付状态*/
  user_pay_status?: string;
  /** 检测报告*/
  report_data?: TPeportData;
  /** 图片*/
  damage_pic?: any;
  detection_remark?: string;
  delivery?: any[];
  sign_device_detail?: any[];
  repair_cost?: any[];
  compensate?: string;
  needPlatform?: string;
  is_platform_pay?: string;
  report_template_id?: string;
};

export enum EPayStatus {
  unpay = '1',
  pay = '2',
}

export type TListLevels = {
  label: string;
  value: string;
};

export type TListQuestions = {
  label: string;
  levels: TListLevels[];
};

export type TListOptions = {
  name: string;
  questions: TListQuestions[];
};

export type TList = {
  [key: string]: TListOptions[];
};

export type TDamageExpansionInfo = {
  total_deposit: string;
  rejection_reason: string;
  number: number;
  examine_info: any;
};

export type TProps = {
  /** 详情信息 */
  detail?: TCompensationFormData;
  /** 订单id */
  orderId?: string;
  /** 是否为详情 */
  isDetail?: boolean;
  /** 表单信息 */
  data?: TCompensationFormData;
  /** 是否展示下方详细信息 */
  showDetailBottom?: boolean;
  /** 是否展示检测报告 */
  showTestReport?: boolean;
  /** 不展示平台支付金额 */
  noShowPlatform?: boolean;
  /** 不展示凭证图片 */
  noShowImg?: boolean;
  /** 详情时 开启行选择*/
  canRowSelect?: boolean;
  /** 详情时 可编辑范围 */
  canEdit?: ECanEdit;
  /** 详情时 是否有商家异议 */
  haveObjection?: boolean;
  /** 是否展开报告并横向布局 */
  isFlex?: boolean;
  /** 是否从订单列表中打开 */
  isFromOrderList?: boolean;
  damageExpansionInfo?: TDamageExpansionInfo;
};

export enum ECanEdit {
  none = '0',
  all = '1',
  platformPay = '2',
  userPay = '3',
}

export type TDiffReport = {
  /** key */
  title: string;
  /** 异常项 */
  qualityTitle: string;
  /** 描述 */
  parentChecked: string;
  /** 值 */
  childrenChecked: string;
};
