export enum selectType {
  CATEGORY = '2',
  MODEL = '3',
}

export const modelCols = [
  {
    title: '指定型号',
    key: 'tags',
    slot: 'modelTag',
    rowspanKey: 'rowspan',
    width: 344,
  },
  {
    title: '成新',
    key: 'condition',
    slot: 'condition',
  },
];
export const categoryCols = [
  {
    title: '二级类目',
    key: 'tags',
    slot: 'modelTag',
    rowspanKey: 'rowspan',
    colspan: 2,
    width: 172,
  },
  {
    title: '',
    key: 'level',
    slot: 'level',
    rowspanKey: 'levelRowspan',
    colspan: 0,
    width: 172,
  },
  {
    title: '成新',
    key: 'condition',
    slot: 'condition',
  },
];
