<template>
  <div class="page-wrap">
    <div class="page-header p-wrap">
      <span class="text">微信绑定</span>
      <a-button
        type="primary"
        @click="addWxVisible = true"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        添加微信账号
      </a-button>
    </div>
    <div class="alert-wrap">
      <a-alert
        message="通过微信扫码绑定，最多可同时绑定三个微信号"
        show-icon
        type="info"
      />
    </div>
    <div class="search-wrap">
      <form-create
        ref="searchFormRef"
        v-model:value="searchParams"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="getTableList"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleSet"
          >
            重置
          </a-button>
        </template>
      </form-create>
    </div>
    <div class="table-wrap">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <a-space>
              <a-button
                type="link"
                @click="showUpdateModel(record.id)"
              >
                修改备注名
              </a-button>
              <a-button
                type="link"
                @click="updateWxStatusById(record.id)"
              >
                {{ record.status === '1' ? '开启推送' : '关闭推送' }}
              </a-button>
              <a-button
                type="link"
                @click="deleteById(record.id)"
              >
                删除
              </a-button>
            </a-space>
          </template>
          <template v-if="column.key === 'status_text'">
            <div style="display: flex; align-items: center">
              <div
                :class="['dot', record.status === '10' ? 'green' : 'default']"
                style="margin-right: 6px"
              />
              {{ record.status_text }}
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:visible="addWxVisible"
      title="添加微信账号"
      :width="800"
    >
      <div class="add-wx-box">
        <div class="add-wx-img flex-column">
          <p>1、请打开微信扫码关注人人租公众号，已关注可跳过</p>
          <img
            alt=""
            src="https://img1.rrzuji.cn/assest/202002/SUPER5E59BEDE2DED0.jpg"
          >
        </div>
        <div class="add-wx-img flex-column">
          <p>2、微信扫描下方二维码，同意授权即可完成</p>
          <img
            alt=""
            :src="otherImg"
          >
        </div>
      </div>
      <div style="color: red">
        注：绑定成功后系统将通过“人人租”公众号为您推送新消息通知
      </div>
      <template #footer>
        <a-space :size="16">
          <a-button @click="addWxVisible = false">
            取消
          </a-button>
          <a-button
            type="primary"
            @click="
              getTableList();
              addWxVisible = false;
            "
          >
            我已完成扫码绑定，刷新页面
          </a-button>
        </a-space>
      </template>
    </a-modal>
    <a-modal
      v-model:visible="updateRemarkModal.visible"
      title="修改备注名"
      @ok="updateRemarkSubmit"
    >
      <a-input v-model:value="updateRemarkModal.remark" />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useTable } from '@/hook/component/use-table';
import { PlusOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { searchFormGroup, columns } from './config';
import { updateWxStatus, deleteWx, updateWx } from './service';

const searchFormRef = ref();

const searchParams = reactive({
  nickname: '',
  remark_name: '',
});

const updateRemarkModal = reactive({
  visible: false,
  remark: '',
  id: '',
});

const updateWxStatusById = async (id: any) => {
  await updateWxStatus({ id });
  getTableList();
  message.success('操作成功');
};

const updateRemarkSubmit = async () => {
  await updateWx({
    id: updateRemarkModal.id,
    field: 'remark_name',
    value: updateRemarkModal.remark,
  });
  getTableList();
  message.success('修改成功');
  updateRemarkModal.visible = false;
};

const showUpdateModel = (id: any) => {
  updateRemarkModal.id = id;
  updateRemarkModal.visible = true;
};

const deleteById = async (id: any) => {
  Modal.confirm({
    title: '删除该数据将无法恢复，是否继续？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteWx({
        id,
      });
      message.success('删除成功');
      getTableList();
    },
  });
};

const otherImg = ref('');

const { list, listLoading, getTableList, tableChange } = useTable({
  url: '/v2-tool-setting/wx-index',
  searchForm: searchParams,
  formatHandle: res => {
    otherImg.value = res.data.qrcode;
    return res.data.list;
  },
});

getTableList();

const handleSet = () => {
  searchFormRef.value.getFormRef().resetFields();
  getTableList();
};

const addWxVisible = ref<boolean>(false);
</script>

<style scoped lang="less">
.page-wrap {
  min-height: calc(100vh - 64px);
  background-color: #fff;
}
.p-wrap {
  padding: 24px;
  background: #fff;
}
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}

.alert-wrap {
  padding: 0 24px 24px;
}
.search-wrap {
  padding: 0 24px;
  :deep(.ant-form) {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 0 -8px;
  }
  :deep(.ant-form-item) {
    flex-basis: 25%;
    margin-right: 0;
    margin-bottom: 24px;
    padding: 0 8px;
  }
}
.table-wrap {
  padding: 0 24px 24px;
}
.add-wx-box {
  display: flex;
  justify-content: space-between;
  .add-wx-img {
    flex-basis: 50%;
    img {
      width: 60%;
    }
  }
  .flex-column {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  &.green {
    background: #52c41a;
  }
  &.default {
    background: #f5f7fa;
  }
}
</style>
