import { useRoute } from 'vue-router';
import type { FormGroupItem } from 'rrz-web-design';

import { getCateListApi, getSuperCateListApi } from './service';

interface ICateItem {
  id: number;
  name: string;
  template_id: number;
  second_sort?: ICateItem[];
}

export const cateFormGroup = (route: ReturnType<typeof useRoute>): FormGroupItem[] => [
  {
    key: 'cateSelect',
    fragmentKey: 'renderCascader',
    label: '请选择工单类型',
    mountedLoad: true,
    setOption: async () => {
      const isSuper = route.query?.role === 'super';
      const api = isSuper ? getSuperCateListApi : getCateListApi;
      const { data } = await api();
      return data.map((item: ICateItem) => ({
        label: item.name,
        value: `${item.id}-${item.template_id}-${item.name}`,
        children: item?.second_sort?.map(childrenItem => {
          return {
            label: childrenItem.name,
            value: `${childrenItem.id}-${childrenItem.template_id}-${childrenItem.name}`,
          };
        }),
      }));
    },
    originProps: {
      rules: { required: true, message: '请选择工单类型', trigger: 'change' },
    },
  },
];
