<template>
  <a-modal
    v-model:visible="bindVisible"
    title="已选购增值服务记录"
    @ok="bindVisible = false"
  >
    <div class="list-box">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="item"
        @click="linkBrokenScreenRecord(item.jump_link)"
      >
        <div>
          {{ item.title }}
          <span
            v-if="item.price"
            style="margin-left: 8px; color: rgba(6, 21, 51, 0.65)"
          > ￥{{ item.price }} </span>
        </div>
        <div>></div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { useRoute } from 'vue-router';
import { useVModel } from '@/hook';

interface IAccessoriesInfo {
  title: string;
  price: string;
  jump_link: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array as PropType<IAccessoriesInfo[]>,
    default: () => [],
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();
const bindVisible = useVModel(props, 'visible', emit);

const route = useRoute();
const domain = route.query.origin || window.location.origin;

function linkBrokenScreenRecord(jump_link: string) {
  window.open(`${domain}${jump_link}`);
}
</script>

<style scoped lang="less">
.list-box {
  display: flex;
  flex-direction: column;

  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    cursor: pointer;
  }
}
</style>
