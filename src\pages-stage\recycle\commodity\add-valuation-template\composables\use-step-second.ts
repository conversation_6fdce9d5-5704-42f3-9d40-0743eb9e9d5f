import { watch, reactive, computed, nextTick, ref } from 'vue';
import { Form, message } from 'ant-design-vue';
import { IConditionSet, IOption, IStepProps } from '../data';

export default function (props: IStepProps, emit: any) {
  const typeOptions = [
    { label: '同时满足选中项时', value: '&' },
    { label: '含有任意选中项时', value: '||' },
  ];

  // 初始化操作
  const init = () => {
    state.currentTab = 0;
    const { condition_type, condition_set, qua_check_option } = props.formData;
    state.condition_type = condition_type;
    state.condition_set = condition_set;
    state.allOption = [...JSON.parse(JSON.stringify(qua_check_option))].map(item => {
      item.option = item.option.map((option: string) => {
        return {
          value: option,
          hasChecked: false,
        };
      });
      return item;
    });
    reRender();
    updateIfReadyToSubmit();
  };
  watch(() => props.formData.condition_type, init);

  // 表单
  const state = reactive<{
    currentTab: number;
    type?: string;
    condition_type: string[];
    condition_set: IConditionSet[];
    allOption: IOption[];
  }>({
    currentTab: 0,
    type: undefined,
    condition_type: [],
    condition_set: [],
    allOption: [],
  });
  // 表单校验规则
  const rules = {
    type: [
      {
        required: true,
        message: '请选择条件定义',
      },
    ],
  };
  const { validate, validateInfos, clearValidate } = Form.useForm(state, rules);

  // 当前/其他-成新度配置项
  const currentConditionSet = computed<IConditionSet | undefined>(() => {
    const condition = state.condition_type[state.currentTab];
    return state.condition_set.find(set => set.condition == condition);
  });
  const otherConditionSet = computed<IConditionSet[]>(() => {
    const condition = state.condition_type[state.currentTab];
    return state.condition_set.filter(set => set.condition != condition);
  });

  // 成新度配置校验
  const customValidate = () => {
    return new Promise<void>((resolve, reject) => {
      let hasSelected = false;
      state.allOption.forEach(item => {
        !!item.selected && (hasSelected = true);
      });
      !hasSelected && reject('请先选择后再操作');
      hasSelected && resolve();
    });
  };

  // 重复定义校验
  const validateIfRepeat = (_setting: any) => {
    let hasError = false;
    if (state.type === '&') {
      otherConditionSet.value.forEach(({ setting }) => {
        if (setting.type === '&') {
          if (
            JSON.stringify(setting.option.map((_: IOption) => _.option![0])) ==
            JSON.stringify(_setting.option.map((_: IOption) => _.option![0]))
          ) {
            hasError = true;
          }
        }
      });
    }
    if (hasError) {
      state.allOption.forEach(item => {
        item.selected = undefined;
      });
      message.error('存在相同的成新度定义，请重新配置');
    }
    return hasError;
  };

  // 生成提交配置项
  const generateData = () => {
    const { condition_type, currentTab, type, allOption, condition_set } = state;
    const payload: IConditionSet = {
      condition: condition_type[currentTab],
      setting: {
        type,
        option: [],
      },
    };
    payload.setting.option = allOption
      .filter(({ selected }) => (selected instanceof Array && selected.length) || typeof selected === 'string')
      .map(({ origin_log_id, selected }) => {
        const names = selected instanceof Array ? selected : [selected];
        return { origin_log_id, option: names };
      });
    const ifRepeat = validateIfRepeat(payload.setting);
    if (!ifRepeat) {
      if (currentConditionSet.value) {
        currentConditionSet.value.setting = payload.setting;
      } else {
        condition_set.push(payload);
        updateIfReadyToSubmit();
      }
      return true;
    }
    return false;
  };

  // 逆向生成回显表单
  const reRender = () => {
    if (currentConditionSet.value) {
      state.type = currentConditionSet.value.setting.type;
    } else {
      state.type = undefined;
      nextTick(() => {
        clearValidate('type');
      });
    }
    applyCurrentConditionSet();
    excludeSelected();
  };

  // 应用当前成新度选项
  const applyCurrentConditionSet = () => {
    currentConditionSet.value?.setting.option.forEach(({ origin_log_id, option }) => {
      const target = state.allOption.find(_ => _.origin_log_id == origin_log_id);
      if (target) {
        target.selected = state.type === '&' ? option![0] : option;
      }
    });
  };

  // 禁用其他成新度已选选项
  const excludeSelected = () => {
    const optionNeedBan = otherConditionSet.value.map(set => set.setting);
    optionNeedBan.forEach(setting => {
      // 当前为 含有 时，排除其他选项
      if (setting.type == state.type && state.type == '||') {
        setting.option.forEach(({ origin_log_id, option }) => {
          const target = state.allOption.find(_ => _.origin_log_id == origin_log_id);
          target?.option!.forEach(opt => {
            opt.hasChecked = option!.includes(opt.value) ? true : opt.hasChecked;
          });
        });
      }
    });
  };

  // 切换成新度
  const switchTab = (tab: number) => {
    validate()
      .then(async () => {
        await customValidate();
        if (generateData()) {
          state.currentTab = tab;
          clearSelected();
          reRender();
        }
      })
      .catch(err => {
        if (typeof err === 'string') message.error(err);
        console.log('catch', err);
      });
  };

  // 切换条件定义
  const onTypeChange = () => {
    clearSelected();
    excludeSelected();
  };

  const clearSelected = () => {
    state.allOption.forEach(item => {
      item.selected = undefined;
      item.error = undefined;
      item.option!.forEach(item => {
        item.hasChecked = false;
      });
    });
  };

  const updateIfReadyToSubmit = () => emit('update:ready', state.condition_type.length === state.condition_set.length);

  // 保存当前成新度配置
  const savingConfig = ref(false);
  const saveCurrentConfig = async () => {
    savingConfig.value = true;
    validate()
      .then(async () => {
        await customValidate();
        generateData();
      })
      .catch(err => {
        if (typeof err === 'string') message.error(err);
      })
      .finally(() => {
        setTimeout(() => {
          savingConfig.value = false;
        }, 500);
      });
  };

  return {
    typeOptions,

    state,
    validateInfos,

    switchTab,
    onTypeChange,

    savingConfig,
    saveCurrentConfig,
  };
}
