<template>
  <div class="page-wrap">
    <div class="page-header p-wrap">
      <span class="text">消息订阅</span>
    </div>
    <div class="search-wrap">
      <a-form
        ref="formRef"
        layout="inline"
        :model="formState"
      >
        <a-form-item
          label="消息类型"
          name="business_type"
        >
          <a-select
            v-model:value="formState.business_type"
            allow-clear
            :options="businessTypeList"
            placeholder="请选择"
            show-search
            style="width: 220px"
          />
        </a-form-item>
      </a-form>
      <div class="button-group">
        <a-button
          type="primary"
          @click="onSearch"
        >
          搜索
        </a-button>
        <a-button
          style="margin-left: 10px"
          @click="onReset"
        >
          重置
        </a-button>
      </div>
    </div>
    <div class="table-wrap">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :pagination="false"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'business_type'">
            <div>{{ record.business_type_trans }}</div>
          </template>
          <template v-if="column.key === 'message_type'">
            <div>
              <template
                v-for="(tag, index) in record.message_type_trans"
                :key="index"
              >
                <span>{{ tag.label }}</span><span class="msg-play">；</span>
              </template>
            </div>
          </template>
          <template v-if="column.key === 'sms_status'">
            <div
              v-if="record.message_type.includes(1)"
              style="display: flex; align-items: center"
            >
              <a-switch
                v-model:checked="record.sms_status"
                checked-value="1"
                un-checked-value="2"
                @change="onChangeStatus(record.config_id, record.sms_status, record.weixin_status)"
              />
              <a-button
                type="link"
                @click="onHandleBind(record.config_id, record.message_type, 1, record.server_id, record.wexin_qrcode)"
              >
                设置
              </a-button>
            </div>
            <div v-else>
              -
            </div>
          </template>
          <template v-if="column.key === 'weixin_status'">
            <div
              v-if="record.message_type.includes(2)"
              style="display: flex; align-items: center"
            >
              <a-switch
                v-model:checked="record.weixin_status"
                checked-value="1"
                un-checked-value="2"
                @change="onChangeStatus(record.config_id, record.sms_status, record.weixin_status)"
              />
              <a-button
                type="link"
                @click="onHandleBind(record.config_id, record.message_type, 2, record.server_id, record.wexin_qrcode)"
              >
                设置
              </a-button>
            </div>
            <div v-else>
              -
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <BindModal
    v-if="bindModalVisible"
    v-model:visible="bindModalVisible"
    :message-form="messageForm"
    :server-id="server_id"
    :wexin-qrcode="wexinQrcode"
  />
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';

import { useTable } from '@/hook/component/use-table';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';

import BindModal from './components/bind-modal.vue';
import { columns, formState } from './config';
import { ISelectState } from './data';
import { messageBase, messageTepList, messageTepUp } from './service';
const { list, listLoading, getTableList, tableChange } = useTable({
  api: messageTepList,
  searchForm: formState,
  formatSearchValue: () => {
    for (let key in formState.value) {
      if (formState.value[key] === '') {
        delete formState.value[key];
      }
    }
    return formState.value;
  },
});
getTableList();

//消息类型
const businessTypeList = ref<ISelectState[]>([]);
//消息类别
const messageTypeList = ref<ISelectState[]>([]);
//获取基本数据
const getBase = () => {
  messageBase().then(({ data }) => {
    businessTypeList.value = data?.business_type;
    messageTypeList.value = data?.message_type;
  });
};
getBase();

//1开启 2关闭
const onChangeStatus = async (config_id: string, sms_status: string, weixin_status: string) => {
  const res = await messageTepUp({ config_id: config_id, sms_status: sms_status, weixin_status: weixin_status });
  message.success(res.message || '操作成功');
};

const [bindModalVisible, { setTrue: showBindModal }] = useBoolean();
//弹窗
const messageForm = ref({
  config_id: '',
  message_type: 0,
});
const server_id = ref<number>(0);
const wexinQrcode = ref<string>('');
const onHandleBind = (
  config_id: string,
  message_type: number[],
  number: number,
  serverId: number,
  weixin_qrcode: string,
) => {
  const len = message_type.length;
  messageForm.value.config_id = config_id;
  wexinQrcode.value = weixin_qrcode;
  if (len === 1 && message_type[0] === number) {
    messageForm.value.message_type = message_type[0];
  } else if (len === 2) {
    messageForm.value.message_type = message_type[number - 1];
  } else {
    return;
  }
  server_id.value = serverId;
  showBindModal();
  console.log('111');
};
//表单实例
const formRef = ref();

const onSearch = () => {
  getTableList('search');
};
const onReset = () => {
  for (let key in formState.value) {
    formState.value[key] = undefined;
  }
  getTableList('search');
};
</script>
<style scoped lang="less">
.page-wrap {
  min-height: calc(100vh - 64px);
  background-color: #fff;
}
.p-wrap {
  padding: 24px;
  background: #fff;
}
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}
.search-wrap {
  display: flex;
  padding: 0 24px;
  :deep(.ant-form) {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
  }
  :deep(.ant-form-item) {
    flex-basis: 25%;
    margin-right: 0;
    margin-bottom: 24px;
    padding: 0 8px;
  }
  .button-group {
    display: flex;
    margin-left: 24px;
  }
}
.table-wrap {
  padding: 0 24px 24px;
}
.msg-play:last-child {
  display: none;
}
</style>
