import { GET, POST, type RequestConfig } from '@/services/api';

const config: RequestConfig = {
  hostType: 'Golang',
};

/**
 * @description: 获取下发仓库基础数据
 * @param {any} order_id
 * @return {*}
 */
export function getSendWarehouseInfo(order_id: string) {
  return GET('/super/v3-order/send-warehouse-alert', { order_id });
}

/**
 * @description: 获取型号
 * @param {any} params
 * @return {*}
 */
export function getModal(params: any) {
  return GET('/warehouse/SpuRelation', { source: 'skuCreate', ...params }, config);
}

/**
 * @description: 获取当前型号的sku信息
 * @param {any}
 * @return {*}
 */
export function getSkuInfo({ category_id, brand_id, model_id }: any) {
  return GET('/warehouse/AttrList', { category_id, brand_id, model_id }, config);
}

/**
 * @description: 提交换货申请
 * @param {any} data
 * @return {*}
 */
export function submitGoodsBarter(data: any) {
  return POST('/super/v2-order/exchange-goods-apply', { ...data });
}

/**
 * @description: 根据sku属性查找skuid
 * @param {any} data
 * @return {*}
 */
export function searchSkuId(data: any) {
  return POST('/warehouse/FindSingleSku', { ...data }, config);
}

/**
 * @description: 分仓服务
 * @param {any} data
 * @return {*}
 */
export function subwarehouseService(data: any) {
  return POST(
    '/warehouse/WarehouseExchange',
    {
      trigger_way: 3,
      order_scene: 1, // 订单业务场景值
      created_port: 1, // 运营后台
      ...data,
    },
    config,
  );
}

/**
 * @description: 根据skuid找属性值
 * @param {any} data
 * @return {*}
 */
export function skuSearchBySkuId(data: { sku_id: string }) {
  return GET('/warehouse/SkuSearch', data, config);
}
