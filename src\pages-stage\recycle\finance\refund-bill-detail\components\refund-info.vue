<template>
  <div class="collection-info-mian">
    <div class="title-block">
      <div class="line" />
      <div class="title">
        退款信息
      </div>
      <div
        class="add-btn"
        @click="handleAdd"
      >
        <PlusOutlined style="color: #5f6778" />
        <span class="add-text">新增退款</span>
      </div>
    </div>

    <a-table
      id="collectionTable"
      :columns="refundList.length > 2 ? columns : columns.slice(0, columns.length - 1)"
      :data-source="refundList"
      :pagination="false"
      :scroll="{ x: '100%' }"
      :sticky="true"
      style="margin-top: 16px"
    >
      <template #headerCell="{ column }">
        <template v-if="column.key === 'pay_account'">
          <span style="color: red">*</span>
          退款账户
        </template>
        <template v-if="column.key === 'pay_type'">
          <span style="color: red">*</span>
          退款方式
        </template>
        <template v-if="column.key === 'amount'">
          <span style="color: red">*</span>
          退款金额（元）
        </template>
      </template>
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'pay_account'">
          <a-select
            v-if="index !== refundList.length - 1"
            v-model:value="record.pay_account"
            disabled
            :field-names="{ label: 'name', value: 'value' }"
            :options="payAccountList"
            placeholder="请选择收款账号"
          />
          <div v-else>
            <div class="table-total-title">
              合计
            </div>
          </div>
        </template>
        <template v-if="column.key === 'pay_type'">
          <a-select
            v-if="index !== refundList.length - 1"
            v-model:value="record.pay_type"
            :field-names="{ label: 'name', value: 'value' }"
            :options="payTypeList"
            placeholder="请选择收款方式"
          />
          <!-- 占位 -->
          <div v-else />
        </template>
        <template v-if="column.key === 'amount'">
          <div
            v-if="index !== refundList.length - 1"
            :class="{ error: !String(record.amount) && isError }"
          >
            <a-input-number
              v-model:value="record.amount"
              allow-clear
              :parser="value => value.match(/^[0-9]*\.([0-9]{0,2})|^[0-9]*/)[0]"
              placeholder="请输入"
              :step="0.01"
              style="width: 100%"
              @blur="handleBlur"
            />
          </div>
          <div v-else>
            <div class="table-total-number">
              {{ totalAmount }}
            </div>
          </div>
        </template>
        <template v-if="column.key === 'payment_flow_sn'">
          <a-input
            v-if="index !== refundList.length - 1"
            v-model:value="record.payment_flow_sn"
            allow-clear
            placeholder="请输入"
          />
          <!-- 占位 -->
          <div v-else />
        </template>
        <template v-if="column.key === 'remark'">
          <a-textarea
            v-if="index !== refundList.length - 1"
            v-model:value="record.remark"
            allow-clear
            :auto-size="{ minRows: 1, maxRows: 3 }"
            placeholder="请输入"
          />
          <!-- 占位 -->
          <div v-else />
        </template>
        <template v-if="column.key === 'action'">
          <div
            v-if="index !== refundList.length - 1"
            style="color: red; cursor: pointer"
            @click="handleRemove(index)"
          >
            移除
          </div>
        </template>
      </template>
    </a-table>
    <a-divider />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useVModel } from '@/hook';
import { columns } from '../config';

const props = defineProps<{
  payAccountList: {
    value: number;
    name: string;
  }[];
  payTypeList: {
    value: number;
    name: string;
  }[];
  value: any[];
}>();

const emit = defineEmits(['update:value']);
let tabledom: any = null;
const refundList = useVModel(props, 'value', emit);
const isError = ref(false);

const totalAmount = ref(0);

const handleAdd = () => {
  refundList.value.splice(refundList.value.length - 1, 0, {
    pay_account: 1,
    pay_type: 1,
    amount: '',
    payment_flow_sn: '',
    remark: '',
  });
  nextTick(() => {
    handleTableDom();
  });
};

const handleRemove = (index: number) => {
  refundList.value.splice(index, 1);
  handleBlur();
  nextTick(() => {
    handleTableDom();
  });
};

const handleBlur = () => {
  totalAmount.value = 0;
  refundList.value.forEach((item: any, index: number) => {
    if (refundList.value.length - 1 !== index) totalAmount.value = Number(item.amount) + totalAmount.value;
  });
};

const handleError = () => {
  isError.value = true;
};

const handleTableDom = () => {
  tabledom = document.querySelector('#collectionTable')?.querySelectorAll('.ant-table-row');
  const len = tabledom.length;
  tabledom.forEach((item: any, index: number) => {
    const bgColor = len - 1 === index ? '#ebfef9' : '#fff';
    item.childNodes.forEach((td: any) => {
      td.style && (td.style.background = bgColor); // 设置背景颜色
    });
  });
};

onMounted(() => {
  handleTableDom();
});
defineExpose({
  handleBlur,
  handleError,
});
</script>

<style scoped lang="less">
.collection-info-mian {
  padding: 24px 24px 0 24px;
  .title-block {
    display: flex;
    align-items: center;
    .line {
      width: 4px;
      height: 16px;
      background: #00c8be;
      border-radius: 2px;
    }
    .title {
      margin-left: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
    }
    .add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 94px;
      height: 24px;
      margin-left: 24px;
      background: #fff;
      border: 1px solid rgba(6, 21, 51, 0.15);
      cursor: pointer;
      .add-text {
        margin-left: 8px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }
  }
  .tabel-total {
    display: flex;
    align-items: center;
    height: 54px;
    background: #f9f9fb;
  }
}
.error {
  :deep(.ant-input-number) {
    border: 1px solid red;
  }
}
.table-total-title {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 14px;
}
.table-total-number {
  color: #00c8be;
  font-weight: 500;
  font-size: 14px;
}
</style>
