<template>
  <a-modal
    title="修改密码"
    :visible="visible"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formModal"
      :rules="rules"
    >
      <a-form-item
        label="手机号码"
        name="phone"
      >
        <a-input
          v-model:value="formModal.phone"
          placeholder="请输入手机号码"
        />
      </a-form-item>
      <a-form-item
        label="新密码"
        name="newPassword"
      >
        <a-input
          v-model:value="formModal.newPassword"
          placeholder="请输入新密码"
          type="password"
        />
      </a-form-item>
      <a-form-item
        label="确认密码"
        name="rePassword"
      >
        <a-input
          v-model:value="formModal.rePassword"
          placeholder="请再次输入新密码"
          type="password"
        />
      </a-form-item>
      <a-form-item
        label="验证码"
        name="msgCode"
      >
        <a-space>
          <a-input
            v-model:value="formModal.msgCode"
            placeholder="请输入验证码"
          />
          <aliyun-captcha
            v-if="isShowCaptcha"
            button-id="passwordCode"
            :config="initAliyunCaptchaConfig"
            @change-show="changeCaptchaShow"
          />
          <!-- 消除验证码按钮闪烁 -->
          <a-button
            v-if="!isShowCaptcha"
            type="primary"
          >
            获取验证码
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import type { IConfig } from '@/components/aliyun-captcha/data';
import { redirectLogin } from '@/services/login';

import { changePassword } from '../service';

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:visible']);

const formRef = ref<FormInstance>();
const formModal = ref({
  /** 手机号码 */
  phone: '',
  /** 验证码 */
  msgCode: '',
  /** 新密码 */
  newPassword: '',
  /** 确认密码 */
  rePassword: '',
});

const isShowCaptcha = ref(true);
// 验证流程完毕后需要将验证码组件卸载（从dom中移除）
const changeCaptchaShow = () => {
  isShowCaptcha.value = !isShowCaptcha.value;
  setTimeout(() => {
    isShowCaptcha.value = !isShowCaptcha.value;
  }, 0);
};

/** ======================表单校验规则====================== */
const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@!%*#_~?&+-])[A-Za-z\d$@!%*#_~?&+-]{8,16}$/;
const validatorPass = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject('请输入新密码');
  } else {
    if (!regex.test(value)) {
      return Promise.reject('要求8~16位，有大写、小写、数字、特殊字符（$@!%*#_~?&+-）');
    }
    if (formModal.value.rePassword !== '') {
      formRef.value?.validateFields('rePassword');
    }
    return Promise.resolve();
  }
};
const validatorRePass = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject('请再次输入新密码');
  } else {
    if (value !== formModal.value.newPassword) {
      return Promise.reject('两次输入密码不一致');
    }
    return Promise.resolve();
  }
};

const rules: Record<string, Rule[]> = {
  phone: [{ required: true, trigger: 'blur', message: '请输入手机号码' }],
  newPassword: [{ required: true, validator: validatorPass, trigger: 'blur' }],
  rePassword: [{ required: true, validator: validatorRePass, trigger: 'blur' }],
  msgCode: [{ required: true, trigger: 'blur', message: '请输入验证码' }],
};

const initAliyunCaptchaConfig: IConfig = {
  url: '/user-info/ali-verify',
  params: formModal,
  formatHandle: params => {
    return {
      phone: params.value.phone,
      type: 'reset',
    };
  },
  beforeShow: () => {
    if (!formModal.value.phone || !/^1[3456789]\d{9}$/.test(formModal.value.phone)) {
      message.error('请输入正确的手机号');
      return false;
    }
    return true;
  }, // 打开验证前的钩子
  requestCallback: res => res.status === 0, // 请求结果，返回业务是否成功的结果,boolean类型
};

function handleCancel() {
  emit('update:visible', false);
}
/** 修改密码 */
async function handleOk() {
  await formRef.value?.validateFields();
  try {
    await changePassword({
      'ChangePasswordForm[phone]': formModal.value.phone,
      'ChangePasswordForm[password]': formModal.value.newPassword,
      'ChangePasswordForm[repassword]': formModal.value.rePassword,
      'ChangePasswordForm[msgCode]': formModal.value.msgCode,
    });
    emit('update:visible', false);
    message.success('修改成功，请重新登录');
    redirectLogin();
  } catch {
    message.error('修改失败,请稍后再试');
  }
}
</script>
