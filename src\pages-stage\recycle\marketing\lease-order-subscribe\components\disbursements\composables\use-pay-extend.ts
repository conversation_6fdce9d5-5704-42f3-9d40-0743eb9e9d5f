import type { Ref } from 'vue';

import { reactive } from 'vue';

import Extend from '../extend/index.vue';

export default function ({ PayExtendComponent }: { PayExtendComponent: Ref<InstanceType<typeof Extend> | null> }) {
  const payExtend = reactive({
    visible: false,
    btnText: '展开',
    change,
    switchChange,
    btnVisible,
  });

  /**
   * @description: 扩展切换
   * @param {boolean} value
   * @return {*}
   */
  function change(value: boolean) {
    payExtend.btnText = value ? '收起' : '展开';
  }

  /**
   * @description: 切换展开状态
   * @return {*}
   */
  function switchChange() {
    PayExtendComponent.value && PayExtendComponent.value.showChange();
  }

  /**
   * @description: 按钮显示切换
   * @return {*}
   */
  function btnVisible(value: boolean) {
    payExtend.visible = value;
  }

  return payExtend;
}
