import { GET, POST } from '@/services/api';

type status = 1 | 0;
export type sfParams = {
  order_id: string;
  is_insure: status;
  is_packed: status;
  sf_address: string;
  send_start_time: `${string} ${string}`;
  is_change_receive?: number;
  receive_address?: string;
};
export type ExportParams = {
  qualityResult: string;
  orderStatus: string;
  serverId: string;
  orderId: string;
  startAt: string;
  endAt: string;
  imei: string;
  tracking_num: string;
};
// 获取table数据
export function getTableList(params: any) {
  return POST('/super/recycle/batch-order/list', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 状态筛选器
export function getStatusOptions() {
  return GET('/common/form-filter?page_from=recycleOrder_list');
}
//企业订单筛选器
export function getBatchStatusOptions() {
  return GET('/common/form-filter?page_from=batchOrder_list');
}
// 查看分类详情
export function searchCategory(params: any) {
  return POST('/super/recycle/batch-order/set-purchased-price', params);
}

// 地址筛选器
export function getAddressOptions(params: any) {
  return GET('/super/recycle/order-sf-express/get-address-list', params);
}

// 顺丰取件
export function sfExpress(params: sfParams) {
  return POST('/super/recycle/batch-order/sf-send-back', params);
}
//批量顺丰取件
export function batchSfExpress(params: sfParams) {
  return POST('/super/recycle/batch-order/batch-sf-send-back', params);
}
// 查看检测报告
export function getQuality(orderId: string) {
  return GET(`/super/recycle/batch-order/view-quality-report?orderId=${orderId}`);
}

// 关闭订单
export function closeOrder(orderId: string) {
  return GET(`/super/recycle/batch-order/set-order-close?orderId=${orderId}`);
}
//导出
export function exportList(params: ExportParams) {
  return POST('/super/recycle/batch-order/export', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

/**
 * Description 顺丰取消预约
 * @param {any} data:any
 * @returns {any}
 */
export function cancelDelivery(data: any) {
  return POST('/super/recycle/order-sf-express/cancel-return', data);
}
//暖冬补贴定价详情

export function purchasedDetail(order_id: string) {
  return GET('/super/recycle/batch-order/purchased-detail', { order_id });
}
