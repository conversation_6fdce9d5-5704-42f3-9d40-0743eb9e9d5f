import { loadScript } from '@/utils/base';

interface IChartWindow extends Window {
  G2?: any;
}

interface ILineChartConfig {
  container: string;
  source: any;
  height?: number;
}

export const useLineChart = () => {
  const render = async (config: ILineChartConfig) => {
    try {
      await loadScript('https://gw.alipayobjects.com/os/antv/pkg/_antv.g2-3.5.1/dist/g2.min.js', () => {
        if ((window as IChartWindow).G2) {
          return true;
        }
        return false;
      });
      const chart = new (window as IChartWindow).G2.Chart({
        container: config.container,
        forceFit: true,
        autoFit: true,
        height: config.height || 178,
        padding: [25, 0, 20, 40],
      });
      chart.source(config.source);
      chart.scale('x', {
        alias: '时间',
        range: [0.1, 0.9],
        formatter: function formatter(value: string) {
          return new Date(+value * 1000).toLocaleDateString();
        },
      });
      chart.legend({
        position: 'top-left', // 设置图例的显示位置
        itemGap: 20, // 图例项之间的间距，
      });
      chart.scale('y', {
        alias: '价格',
      });
      chart
        .line()
        .tooltip('capacity*y*price_ratio', (capacity: string, y: number, price_ratio: string) => {
          return {
            name: capacity,
            value: `${y}(${price_ratio})`,
          };
        })
        .position('x*y')
        .color('capacity');
      chart.point().position('x*y').color('capacity').size(2).shape('circle').style({
        stroke: '#fff',
        lineWidth: 1,
      });
      chart.axis('x', {
        label: {
          textStyle: {
            fill: 'rgba(44,53,66,0.45)',
          },
          formatter: function formatter(value: string) {
            const m = new Date(value).getMonth() + 1;
            const d = new Date(value).getDate();
            return m + '/' + d;
          },
        },
      });
      chart.axis('y', {
        label: {
          textStyle: {
            fill: 'rgba(44,53,66,0.45)',
          },
        },
      });
      chart.tooltip;
      chart.render();
      return chart;
    } catch (err) {}
  };
  return {
    render,
  };
};
