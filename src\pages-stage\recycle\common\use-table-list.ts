import { onMounted } from 'vue';
import { clearObject } from '@/utils';
import useAsync from '@/hook/common/use-async';
interface ITableList {
  getTableList: (...args: any[]) => Promise<any>; // 请求方法
  payload?: { [key: string]: unknown }; // 荷载
  pagination: any; // 分页信息
  isAutoInit?: boolean; // 是否自动初始化
  beforeInit?: () => void;
  formatPayLoad?: (val: any) => any;
  cb?: (data?: any) => void;
}

export default function useTableList({
  getTableList,
  payload,
  pagination,
  isAutoInit = false,
  beforeInit,
  formatPayLoad,
  cb,
}: ITableList) {
  const { run, dataList: tableList, isLoading } = useAsync();

  // 获取table数据
  const onLoadList = async () => {
    const page = pagination.current;
    const page_size = pagination.pageSize;
    await run(
      getTableList(
        formatPayLoad
          ? clearObject(formatPayLoad({ page, page_size, ...payload }))
          : clearObject({ page, page_size, ...payload }),
      ),
      {
        callback(data: any) {
          pagination.current = data.pageInfo.page;
          pagination.pageSize = data.pageInfo.pageSize;
          pagination.count = data.pageInfo.count;
          cb && cb(data);
          return data.list;
        },
      },
    );
  };
  onMounted(() => {
    if (isAutoInit) {
      beforeInit && beforeInit();
      onLoadList();
    }
  });
  return {
    onLoadList,
    tableList,
    isLoading,
  };
}
