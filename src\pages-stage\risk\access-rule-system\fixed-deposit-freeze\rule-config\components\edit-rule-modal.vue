<template>
  <a-modal
    v-model:visible="visible"
    :title="`${id ? '编辑' : '新建'}规则`"
    @cancel="onCloseEditRule"
  >
    <template #footer>
      <a-button
        key="back"
        @click="onCloseEditRule"
      >
        取消
      </a-button>
      <a-button
        key="submit"
        :disabled="checkProductIdLoading"
        :loading="editRuleLoading"
        type="primary"
        @click="onEditRule"
      >
        确定
      </a-button>
    </template>
    <a-spin :spinning="getEditRuleConfigLoading">
      <a-form
        ref="ruleFormRef"
        layout="vertical"
        :model="ruleForm"
      >
        <a-form-item
          label="排序"
          name="sort"
          required
        >
          <a-input-number
            v-model:value="ruleForm.sort"
            :max="maxSort"
            :min="1"
            :placeholder="`请输入, 最大值为${maxSort}`"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item
          label="规则名称"
          name="title"
          required
        >
          <a-input
            v-model:value="ruleForm.title"
            :maxlength="10"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          label="绑定类型"
          name="bindType"
          :rules="[{ required: true, message: '请选择绑定类型' }]"
        >
          <a-checkbox-group
            v-model:value="ruleForm.bindType"
            @change="changeBindType"
          >
            <TransitionGroup name="fade">
              <a-checkbox
                v-for="item in ruleConfig.configBindTypeData"
                :key="item.id"
                :disabled="item.disabled"
                :value="item.id"
              >
                {{ item.title }}
              </a-checkbox>
            </TransitionGroup>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
      <a-form
        ref="bindTypeFormRef"
        :model="bindTypeForm"
        :rules="bindTypeFormRules"
      >
        <a-form-item
          v-if="ruleForm.bindType.includes(EBindType.SHOP)"
          label="店铺"
          :name="EBindType.SHOP"
        >
          <a-select
            v-model:value="bindTypeForm[EBindType.SHOP]"
            :filter-option="filterShopOption"
            :loading="searchOptionsLoading"
            mode="multiple"
            :options="searchShopOptions"
            placeholder="请选择店铺"
            show-search
            @blur="() => (searchShopOptions = [])"
            @search="debounceSearch"
          />
        </a-form-item>
        <a-form-item
          v-if="ruleForm.bindType.includes(EBindType.CATEGORY)"
          label="分类"
          :name="EBindType.CATEGORY"
        >
          <CommodityCategoryCascader
            v-model:value="bindTypeForm[EBindType.CATEGORY]"
            change-on-select
            class="category-select"
            :level="2"
            :max-tag-count="20"
            multiple
            placeholder="请选择"
            :scene="EScene.PlatformGoods"
            show-checked-strategy="SHOW_CHILD"
          />
        </a-form-item>
        <a-form-item
          v-if="ruleForm.bindType.includes(EBindType.CHANNEL)"
          label="渠道"
          :name="EBindType.CHANNEL"
        >
          <a-select
            v-model:value="bindTypeForm[EBindType.CHANNEL]"
            mode="multiple"
            option-filter-prop="label"
            :options="ruleConfig.channelData"
            placeholder="请选择渠道"
            show-search
          />
        </a-form-item>
        <a-form-item
          v-if="ruleForm.bindType.includes(EBindType.PRODUCT)"
          label="商品ID/子ID"
          :name="EBindType.PRODUCT"
        >
          <template
            v-if="checkProductIdResult[EBindType.PRODUCT].err_ids.length"
            #help
          >
            <span>存在异常商品ID，<span
              style="cursor: pointer"
              @click="openErrorProductId(EBindType.PRODUCT)"
            >查看详情</span></span>
          </template>
          <a-select
            v-model:value="bindTypeForm[EBindType.PRODUCT]"
            allow-clear
            :loading="checkProductIdLoading"
            mode="tags"
            :open="false"
            placeholder="请输入商品ID"
            :token-separators="[',']"
            @change="checkProductId(EBindType.PRODUCT)"
          >
            <template #tagRender="{ value: val, label, closable, onClose }">
              <a-tag
                :closable="closable"
                :color="checkProductIdResult[EBindType.PRODUCT].err_ids.includes(val) ? 'red' : ''"
                style="margin-right: 3px"
                @close="onClose"
              >
                {{ label }}
              </a-tag>
            </template>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="ruleForm.bindType.includes(EBindType.BLOCK_PRODUCT)"
          label="屏蔽商品ID/子ID"
          :name="EBindType.BLOCK_PRODUCT"
        >
          <template
            v-if="checkProductIdResult[EBindType.BLOCK_PRODUCT].err_ids.length"
            #help
          >
            <span>存在异常商品ID，<span
              style="cursor: pointer"
              @click="openErrorProductId(EBindType.BLOCK_PRODUCT)"
            >查看详情</span></span>
          </template>
          <a-select
            v-model:value="bindTypeForm[EBindType.BLOCK_PRODUCT]"
            allow-clear
            mode="tags"
            :open="false"
            placeholder="请输入屏蔽商品ID"
            :token-separators="[',']"
            @change="checkProductId(EBindType.BLOCK_PRODUCT)"
          >
            <template #tagRender="{ value: val, label, closable, onClose }">
              <a-tag
                :closable="closable"
                :color="checkProductIdResult[EBindType.BLOCK_PRODUCT].err_ids.includes(val) ? 'red' : ''"
                style="margin-right: 3px"
                @close="onClose"
              >
                {{ label }}
              </a-tag>
            </template>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { h, nextTick, reactive, ref, watch } from 'vue';
import { debounce } from 'lodash-es';
import { apiCheckProduct, apiCheckShop, apiGetEditRuleConfig, apiSaveFixedDepositConfig } from '../services';
import { EBindType } from '../data.d';
import { EScene } from '@/utils/enums/commodity';
import { useVModel } from '@/hook';
import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import { Form, message, notification } from 'ant-design-vue';
import { Rule } from 'ant-design-vue/es/form';

const props = defineProps({
  visible: Boolean,
  id: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['update:visible', 'ok']);
const visible = useVModel(props, 'visible', emits);
const maxSort = ref(1);
const ruleConfig = reactive({
  channelData: [],
  configBindTypeData: [],
  configConditionData: [],
  configData: [],
  shopData: [],
});
const ruleFormRef = ref();
const ruleForm = reactive({
  sort: '',
  title: '',
  bindType: [],
});
const bindTypeFormRef = ref();
const bindTypeFormRules = {
  [EBindType.SHOP]: [{ required: true, message: '请选择店铺' }],
  [EBindType.CATEGORY]: [{ required: true, message: '请选择分类' }],
  [EBindType.CHANNEL]: [{ required: true, message: '请选择渠道' }],
  [EBindType.PRODUCT]: [
    {
      required: true,
      validator: (_rule: Rule, value: string[]) => checkProductIdRule(_rule, value, EBindType.PRODUCT),
      trigger: 'change',
    },
  ],
  [EBindType.BLOCK_PRODUCT]: [
    {
      required: true,
      validator: (_rule: Rule, value: string[]) => checkProductIdRule(_rule, value, EBindType.BLOCK_PRODUCT),
      trigger: 'change',
    },
  ],
};

const bindTypeForm = reactive({
  [EBindType.SHOP]: [],
  [EBindType.CATEGORY]: [],
  [EBindType.CHANNEL]: [],
  [EBindType.PRODUCT]: [],
  [EBindType.BLOCK_PRODUCT]: [],
});
const { resetFields: bindTypeFormReset } = Form.useForm(bindTypeForm);

function changeBindType(value: string[]) {
  // 有且仅有屏蔽商品ID
  if (value.length === 1 && value[0] === EBindType.BLOCK_PRODUCT) {
    message.error('屏蔽商品ID不能单独选择');
    ruleForm.bindType = [];
    return;
  }
  const hasProduct = value.includes(EBindType.PRODUCT);
  ruleForm.bindType = hasProduct ? [EBindType.PRODUCT] : value;
  ruleConfig.configBindTypeData = ruleConfig.configBindTypeData.map(item => ({
    ...item,
    // 如果有商品ID，其他类型不可选, 如果没有商品ID，最多选3个
    disabled: hasProduct
      ? item.id !== EBindType.PRODUCT
      : ruleForm.bindType.length >= 3 && !ruleForm.bindType.includes(item.id),
  }));
}

const getEditRuleConfigLoading = ref(false);

function getEditRuleConfig() {
  getEditRuleConfigLoading.value = true;
  apiGetEditRuleConfig({
    id: props.id || undefined,
  })
    .then(res => {
      Object.assign(ruleConfig, res.data);
      bindTypeFormReset();
      ruleForm.sort = res.data.configData?.['sort'] || undefined;
      ruleForm.title = res.data.configData?.title || '';
      maxSort.value = res.data.maxSort || 1;
      ruleForm.bindType =
        res.data.configConditionData?.map(item => {
          const typeId = String(item.type_id);
          bindTypeForm[typeId] = Array.isArray(item.value) ? item.value : item.value.split(',');
          return typeId;
        }) || [];
      searchShopOptions.value =
        res.data.shopData?.map(item => {
          return {
            value: item.server_id,
            label: `${item.company}(${item.server_id})`,
          };
        }) || [];
      // 重新校验
      [EBindType.PRODUCT, EBindType.BLOCK_PRODUCT].forEach(typeId => {
        checkProductId(typeId);
      });
      props.id && Promise.all([ruleFormRef.value.validate(), bindTypeFormRef.value.validate()]);
    })
    .finally(() => {
      changeBindType(ruleForm.bindType);
      getEditRuleConfigLoading.value = false;
    });
}

// 店铺
const searchShopOptions = ref([]);
const searchOptionsLoading = ref(false);

function filterShopOption(input: string, option: { value: string; label: string }) {
  return (
    option.label.toLowerCase().includes(input.toLowerCase()) || option.value.toLowerCase().includes(input.toLowerCase())
  );
}

function handleSearch(value: string) {
  if (!value) {
    searchShopOptions.value = [];
    return;
  }
  searchOptionsLoading.value = true;
  apiCheckShop({
    server_id: value,
  })
    .then(res => {
      searchShopOptions.value = res.data.map(item => {
        return {
          value: item.server_id,
          label: `${item.company}(${item.server_id})`,
        };
      });
    })
    .finally(() => {
      searchOptionsLoading.value = false;
    });
}

// 商品ID
const checkProductIdResult = reactive({
  [EBindType.PRODUCT]: {
    err_ids: [],
    err_msg: [],
  },
  [EBindType.BLOCK_PRODUCT]: {
    err_ids: [],
    err_msg: [],
  },
});
const checkProductIdLoading = ref(false);

async function checkProductIdRule(_rule: Rule, value: string[], typeId: EBindType.PRODUCT | EBindType.BLOCK_PRODUCT) {
  if (!value.length) {
    return Promise.reject('请输入商品id');
  } else if (checkProductIdResult[typeId].err_ids.length) {
    return Promise.reject('存在异常商品ID');
  } else {
    return Promise.resolve();
  }
}

function openErrorProductId(typeId: EBindType.PRODUCT | EBindType.BLOCK_PRODUCT) {
  notification.open({
    key: 'errMsg',
    message: '异常商品ID详情',
    description: h(
      'div',
      checkProductIdResult[typeId].err_msg.map(item => h('div', item)),
    ),
    duration: 0,
    placement: 'bottomRight',
  });
}

function checkProductId(typeId: EBindType.PRODUCT | EBindType.BLOCK_PRODUCT) {
  if (!bindTypeForm[typeId].length) {
    checkProductIdResult[typeId] = {
      err_ids: [],
      err_msg: [],
    };
    return;
  }
  checkProductIdLoading.value = true;
  apiCheckProduct({
    item_ids: bindTypeForm[typeId].join(),
    type_id: typeId,
    id: props.id || undefined,
  })
    .then(({ data }) => {
      checkProductIdResult[typeId] = data;
      bindTypeFormRef.value.validate(typeId);
    })
    .finally(() => {
      checkProductIdLoading.value = false;
    });
}

// 防抖
const debounceSearch = debounce(handleSearch, 300);

const editRuleLoading = ref(false);

function getCondition() {
  return ruleForm.bindType.map(id => {
    return {
      type_id: id,
      value: id === EBindType.CATEGORY ? bindTypeForm[id] : bindTypeForm[id].join(),
    };
  });
}

function onCloseEditRule() {
  visible.value = false;
  notification.close('errMsg');
}

async function onEditRule() {
  await Promise.all([ruleFormRef.value.validate(), bindTypeFormRef.value.validate()]);
  editRuleLoading.value = true;
  apiSaveFixedDepositConfig({
    id: props.id || undefined,
    sort: ruleForm.sort,
    title: ruleForm.title,
    json_condition: JSON.stringify(getCondition()),
  })
    .then(() => {
      visible.value = false;
      message.success('操作成功');
      notification.close('errMsg');
      emits('ok');
    })
    .finally(() => {
      editRuleLoading.value = false;
    });
}

watch(
  () => visible.value,
  val => {
    if (val) {
      nextTick(() => {
        getEditRuleConfig();
      });
    }
  },
);
</script>
