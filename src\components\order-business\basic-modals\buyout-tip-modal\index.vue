<template>
  <RModal
    v-model:open="bindVisible"
    :mask-closable="false"
    title="说明"
    :width="800"
  >
    <div class="tip-text">
      <p class="tip-text-title">
        尊敬的商家伙伴，您好！
      </p>
      <p>
        为提升用户体验，人人租平台已对用户端页面进行优化，将租赁方案中涉及的<span>「买断价」</span>统一调整为更易理解的<span>「到期购买价/当期购买价」</span>。
      </p>
      <p>请注意：用户端显示的<span>「当期购买价/到期购买价」</span>与商家端的<span>「买断尾款」</span>暂为同一概念但不同计价方式，均是租赁物在当期/到期的购买价格。</p>
      <p>请您在与用户沟通时留意术语差异，避免因表述不一致产生误解。</p>
      <p style="text-decoration-line: underline;">
        例如： 用户申请「购买价结算」类似于触发您后台的「买断申请」流程。
      </p>
    </div>
    <div class="tip-light">
      <p>如有用户对费用逻辑存在疑问，建议直接引导其参考订单详情页的「到期购买价/当期购买价」说明，或联系客服协助解释。感谢您的理解与配合！人人租将持续优化服务体验，与您共同助力用户租赁旅程更顺畅！</p>
      <p>
        <InfoCircleOutlined />
        其他说明： 人人租将在未来几个月内对租赁价格体系进行持续迭代，请您留意平台通知
      </p>
    </div>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </RModal>
</template>

<script setup lang="ts">
import { InfoCircleOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';



const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();
const bindVisible = useVModel(props, 'visible', emit);

</script>

<style lang="less" scoped>
.tip-text {
  padding: 16px;
  background: #F4F8FF;
  border-radius: 8px;
  .tip-text-title {
    margin-bottom: 8px;
    color: rgba(6,21,51,0.85);
    font-weight: 500;
    line-height: 22px;
  }
  p {
    color: rgba(6,21,51,0.65);
    span {
      color: var(--ant-primary-color);
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.tip-light {
  margin-top: 12px;
  padding: 16px;
  color: rgba(6,21,51,0.45);
  background: rgba(67,111,199,0.02);
  border: 1px solid rgba(6,21,51,0.04);
  border-radius: 8px;
  p:last-child {
    margin-bottom: 0;
  }
}
</style>
