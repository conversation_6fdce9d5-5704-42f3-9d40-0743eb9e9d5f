import { ref } from 'vue';
import { getGmvDayData } from '../service';
import { IGmvDayRes, IGmvDayInfo } from '../data.d';

export default function () {
  const loading = ref<boolean>(false);
  const statisticsInfo = ref<Partial<Omit<IGmvDayRes, 'list'>>>({});
  const statisticsList = ref<IGmvDayInfo[]>([]);

  const load = async (params?: any) => {
    loading.value = true;

    try {
      const res = await getGmvDayData(params);
      const data: IGmvDayRes = res.data || {};

      const { list } = data;
      const dataSource = list.map((item: IGmvDayInfo) => {
        return {
          ...item,
          tipsText: `${item.year}-${item.date}`,
          label: data.tips,
        };
      });
      statisticsInfo.value = data;
      statisticsList.value = dataSource;
    } finally {
      loading.value = false;
    }
  };

  load();

  return {
    loading,
    statisticsInfo,
    statisticsList,
    load,
  };
}
