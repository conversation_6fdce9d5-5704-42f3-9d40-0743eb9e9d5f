import { GET, POST } from '@/services/api';

//商品型号
export function getSpuDataList(params: any) {
  return GET('/warehouse/SpuDataList', params, {
    hostType: 'Golang',
  });
}

//品类
export function getBreedList(params: any) {
  return GET('/warehouse/SpuRelation?source=skuCreate', params, {
    hostType: 'Golang',
  });
}

//获取平台&成新度
export const getPlatNew = () => {
  return GET('/super/recycle/evaluate-market-v2/form-options');
};

//第三方平台型号列表
export const platformModelList = (platform: number) => {
  return GET('/super/recycle/evaluate-market-v2/third-platform-model-list', { platform });
};

//第三方平台平台型号详情
export const platformModelDetail = (number_id: number) => {
  return GET('/super/recycle/evaluate-market-v2/third-platform-model-detail', { number_id });
};

//新增爬虫任务
export const createNumberTask = (params: any) => {
  return POST('/super/recycle/evaluate-market-v2/create-number-task', params, {
    headers: {
      'content-type': 'application/json;charset=utf-8',
    },
  });
};

//编辑爬虫任务
export const editNumberTask = (params: any) => {
  return POST('/super/recycle/evaluate-market-v2/edit-number-task', params, {
    headers: {
      'content-type': 'application/json;charset=utf-8',
    },
  });
};

//详情
export const numberTaskDetail = (id: string) => {
  return GET('/super/recycle/evaluate-market-v2/number-task-detail', { id });
};

//删除
export const deleteTask = (id: string) => {
  return GET('/super/recycle/evaluate-market-v2/delete-number-task', { id });
};

/**
 * Description 获取市场估价列表
 * @param {any} params
 * @returns {any}
 */
export const queryChartList = (params?: Record<string, any>) => {
  return GET('/super/recycle/evaluate-market-v2/list', params);
};

//获取内存详情
export const queryMemoryList = (params: any) => {
  return GET('/super/recycle/evaluate-market-v2/search-list', params);
};

//获取估价配置详情
export const getEvaluationConfig = (params: any) => {
  return GET('/super/recycle/evaluate-market-v2/options-detail', params);
};

//成新度-图表
export const queryChartDetail = (params: any) => {
  return GET('/super/recycle/evaluate-market-v2/chart-detail', params);
};
