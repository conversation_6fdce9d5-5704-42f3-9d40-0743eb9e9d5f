<template>
  <div class="data-wrap">
    <div class="data-wrap__title">
      {{ info.name }}
      <a-tooltip
        v-if="info.tips"
        placement="top"
      >
        <template #title>
          <span>{{ info.tips }}</span>
        </template>
        <info-circle-outlined class="data-wrap__title__icon" />
      </a-tooltip>
    </div>
    <div class="data-wrap__date">
      <a-tooltip placement="top">
        <template #title>
          <span>{{ dateText }}</span>
        </template>
        <span class="data-wrap__date__value">{{ dateText }}</span>
      </a-tooltip>
      <redo-outlined
        :class="['data-wrap__date__icon', { 'data-wrap__date__icon--loading': loading }]"
        @click="onUpdate"
      />
    </div>
    <div class="data-wrap__bottom">
      <span class="data-wrap__bottom__value">{{ info.number }}</span>
      <span class="data-wrap__bottom__unit">{{ info.unit }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType } from 'vue';
import { InfoCircleOutlined, RedoOutlined } from '@ant-design/icons-vue';
import { IIndexDataCountValue } from '../data.d';
const props = defineProps({
  info: {
    type: Object as PropType<IIndexDataCountValue>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update']);

const loading = ref<boolean>(false);

const dateText = computed(() => {
  const { start_at, end_at, date_between, update_time } = props.info;
  return `${start_at} ~ ${end_at} | ${date_between} | 更新于${update_time}`;
});

const onUpdate = () => {
  loading.value = true;
  emits('update', props.info.key, () => {
    loading.value = false;
  });
};
</script>

<style lang="less" scoped>
.data-wrap {
  padding: 16px 16px 12px 16px;
  background: #f9f9fb;
  border-radius: 8px;
  &:hover {
    .data-wrap__date__icon {
      display: inline-block;
    }
  }
  &__title {
    display: flex;
    align-items: center;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    &__icon {
      margin-left: 10px;
      color: rgba(6, 21, 51, 0.45);
      font-size: 12px;
    }
  }
  &__date {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    color: rgba(6, 21, 51, 0.25);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    &__value {
      max-width: calc(100% - 32px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &__icon {
      display: inline-block;
      display: none;
      margin: 0 10px 0 6px;
      color: rgba(6, 21, 51, 0.25);
      font-size: 16px;
      transform: rotateZ(-90deg);
      cursor: pointer;
      &--loading {
        animation: rotate 1s linear infinite;
      }
      &:hover {
        color: #00c8be;
      }
    }
  }
  &__bottom {
    vertical-align: bottom;
    &__value {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }
    &__unit {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotateZ(-90deg);
  }
  to {
    transform: rotateZ(270deg);
  }
}
</style>
