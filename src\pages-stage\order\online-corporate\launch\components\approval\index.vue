<template>
  <a-drawer
    :body-style="{ padding: '0', background: '#edf0f3' }"
    :destroy-on-close="true"
    :visible="visible"
    width="80%"
    @close="onCancel"
  >
    <div
      v-if="showSpin"
      class="spin"
    >
      <a-spin size="large" />
    </div>
    <a-tabs
      v-model:activeKey="activeKey"
      size="small"
      :style="{ background: '#fff', padding: '0 24px' }"
    >
      <a-tab-pane
        v-for="item in tabs"
        :key="item.key"
        :style="{ background: '#fff' }"
        :tab="!isEdit && item.key !== activeKey ? '' : item.tab"
      />
    </a-tabs>
    <div class="process">
      <div class="product">
        <a-form
          ref="formRef"
          layout="vertical"
          :model="formData"
        >
          <div class="info-box">
            <div class="title">
              产品信息
            </div>
            <div
              v-for="(sku, index) in formData.dataList"
              :key="index"
              :class="{ 'add-sku': isEdit && index > 0 }"
            >
              <a-form
                ref="skuRef"
                class="add-sku-item"
                layout="vertical"
                :model="formData.dataList[index]"
              >
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-item
                      v-if="activeKey === '1'"
                      label="订单号"
                      name="order_id"
                      :rules="[{ required: true, message: '请输入订单号' }]"
                    >
                      <a-input
                        v-model:value.trim.lazy="sku.order_id"
                        :disabled="!isEdit"
                        placeholder="请输入"
                        :style="{ width: '100%' }"
                        @change="getOrder(index)"
                      />
                    </a-form-item>
                    <a-form-item
                      v-else
                      label="合同编号"
                      name="contract_no"
                      :rules="[{ required: true, message: '请输入合同编号' }]"
                    >
                      <a-input
                        v-model:value.trim.lazy="sku.contract_no"
                        :disabled="!isEdit"
                        placeholder="请输入"
                        :style="{ width: '100%' }"
                        @change="getOrder(index)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="租期"
                      name="rangeDate"
                    >
                      <a-range-picker
                        v-model:value="sku.rangeDate"
                        disabled
                        format="YYYY-MM-DD"
                        :style="{ width: '100%' }"
                        value-format="YYYY-MM-DD"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="套餐ID-1"
                      name="sku_id"
                    >
                      <a-input
                        v-model:value.trim="sku.sku_id"
                        :disabled="activeKey === '1' || !isEdit"
                        :placeholder="activeKey === '1' ? '输入订单号后自动填充' : '请输入商品套餐ID，如454212-121645'"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item
                      :label="isEdit ? '第几期租金' : '已缴纳期数'"
                      name="periods"
                      :rules="sku.bills?.length === 0 ? [] : [{ required: true, message: '请选择租期' }]"
                    >
                      <!-- 可选择 -->
                      <template v-if="isEdit && sku.bills?.length > 0">
                        <div class="rent">
                          <div
                            v-for="(items, billIndex) in sku.bills"
                            :key="billIndex"
                            :class="{
                              'active':sku.periods?.some((e: any) => e === items.period_num),
                              'disabled':!items.is_select
                            }"
                            @click="items.is_select ? choseRents(items.period_num, index) : ''"
                          >
                            {{ items.period_num }}
                          </div>
                        </div>
                      </template>
                      <!-- 没选项 -->
                      <template v-else-if="isEdit && sku.bills?.length === 0">
                        <div :style="{ color: '#f00' }">
                          您还没有选择租期！！
                        </div>
                      </template>
                      <!-- 显示已选租期 -->
                      <template v-else>
                        <div class="rented">
                          <div class="rent">
                            <div
                              v-for="(items, periodIndex) in sku.periods"
                              :key="periodIndex"
                              class="actived"
                            >
                              {{ items.period_num || items }}
                            </div>
                          </div>
                        </div>
                      </template>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="商品名称"
                      name="item_name"
                    >
                      <a-input
                        v-model:value.trim="sku.item_name"
                        disabled
                        placeholder="输入商品套餐ID后自动填充"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="月租金"
                      name="fenqi_money"
                    >
                      <a-input-number
                        v-model:value="sku.fenqi_money"
                        :disabled="activeKey === '1' || !isEdit"
                        :min="0"
                        :placeholder="activeKey === '1' ? '输入订单号后自动填充' : '请输入'"
                        :precision="2"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="总租金"
                      name="rental_money"
                    >
                      <a-input-number
                        v-model:value="sku.rental_money"
                        :disabled="activeKey === '1' || !isEdit"
                        :min="0"
                        :placeholder="activeKey === '1' ? '输入订单号后自动填充' : '请输入'"
                        :precision="2"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="数量"
                      name="item_num"
                    >
                      <a-input-number
                        v-model:value="sku.item_num"
                        :disabled="activeKey === '1' || !isEdit"
                        :min="0"
                        :placeholder="activeKey === '1' ? '输入订单号后自动填充' : '请输入'"
                        :precision="0"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="结算比例"
                      name="server_ratio"
                    >
                      <a-input-number
                        v-model:value="sku.server_ratio"
                        disabled
                        :min="0"
                        placeholder="输入订单号后自动填充"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="结算/供应商家"
                      name="server_name"
                    >
                      <a-input
                        v-model:value.trim="sku.server_name"
                        disabled
                        placeholder="不能手动填写，发起审批成功后自动填充"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="账单总金额"
                      name="bill_money"
                    >
                      <a-input
                        disabled
                        :placeholder="'选择期数自动填充'"
                        :value="sku.bill_money"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="结算金额"
                      name="settle_amount"
                    >
                      <a-input-number
                        disabled
                        :min="0"
                        placeholder="输入订单号后自动填充"
                        :precision="2"
                        :style="{ width: '100%' }"
                        :value="sku.settle_amount"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="当月转入金额（元）"
                      name="transfer_money"
                    >
                      <a-input-number
                        v-model:value="sku.transfer_money"
                        disabled
                        :min="0"
                        placeholder="根据账单总金额自动填充"
                        :precision="2"
                        :style="{ width: '100%' }"
                      />
                      <div class="tip-box">
                        根据账单总金额自动填充,若需修改需到账单列表进行修改
                      </div>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="当月转入含税金额（元）"
                      name="transfer_including_tax_money"
                      :rules="getRules(sku)"
                    >
                      <a-input-number
                        v-model:value.tirm="sku.transfer_including_tax_money"
                        :disabled="!isEdit"
                        :min="0"
                        placeholder="请输入"
                        :precision="2"
                        :style="{ width: '100%' }"
                      />
                      <div class="tip-box">
                        建议填写不高于当月转入金额
                      </div>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="运费"
                      name="freight_money"
                    >
                      <a-input-number
                        v-model:value.trim="sku.freight_money"
                        :disabled="!isEdit"
                        :min="0"
                        placeholder="请输入"
                        :precision="2"
                        :style="{ width: '100%' }"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                <div
                  v-if="isEdit && index > 0"
                  class="del-sku-btn"
                  @click="delGoodsItem(index)"
                >
                  <CloseCircleOutlined :style="{ fontSize: '18px', color: '#BFBFBF' }" />
                </div>
              </a-form>
            </div>
            <div
              v-if="isEdit"
              class="add-sku-btn"
              @click="addGoodsData"
            >
              <PlusOutlined /> 添加订单信息
            </div>
          </div>
          <div class="info-box">
            <div class="title">
              付款信息
            </div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item
                  label="所有账单总金额"
                  name="bill_money"
                >
                  <a-input
                    disabled
                    :value="formData.bill_money"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="当月转入总金额（元）"
                  name="transfer_money"
                >
                  <a-input
                    disabled
                    :style="{ width: '100%' }"
                    :value="formData.transfer_money"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="当月转入含税总金额（元）"
                  name="transfer_including_tax_money"
                >
                  <a-input
                    disabled
                    :style="{ width: '100%' }"
                    :value="formData.transfer_including_tax_money"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="结算总金额"
                  name="settle_amount"
                >
                  <a-input
                    disabled
                    :style="{ width: '100%' }"
                    :value="allSettleAmount"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="总运费"
                  name="freight_money"
                >
                  <a-input
                    disabled
                    :style="{ width: '100%' }"
                    :value="formData.freight_money"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="info-box">
            <div class="title">
              基本信息
            </div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item
                  label="部门"
                  name="department"
                >
                  <a-select
                    v-if="isEdit"
                    v-model:value.trim="formData.department"
                    :disabled="!formData.is_edit_dept_white"
                    :options="formData.departmentOptions"
                    @change="handleGetSalesList"
                  />
                  <a-input
                    v-else
                    v-model:value="formData.department_name"
                    :disabled="true"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="销售人员"
                  name="sales_id"
                >
                  <a-select
                    v-if="isEdit"
                    v-model:value="formData.sales_id"
                    :disabled="!formData.is_edit_dept_white"
                    :options="
                      formData?.userAdminData?.map(item => ({
                        key: item.user_id,
                        label: item.username,
                        value: item.user_id,
                      }))
                    "
                  />
                  <a-input
                    v-else
                    v-model:value="formData.sales_name"
                    :disabled="true"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="info-box">
            <div class="title">
              备注信息
            </div>
            <a-textarea
              v-model:value="formData.remark"
              :auto-size="{ minRows: 4, maxRow: 4 }"
              :disabled="!isEdit"
              :maxlength="200"
              show-count
            />
          </div>
        </a-form>
      </div>
    </div>
    <template #footer>
      <div class="submit flex-wrap flex-x-end">
        <a-button
          class="submit-btn"
          @click="onCancel"
        >
          取消
        </a-button>
        <a-button
          v-if="isEdit"
          class="submit-btn"
          style="margin-left: 6px"
          type="primary"
          @click="handleOkBtn"
        >
          提交
        </a-button>
      </div>
    </template>
    <ConfirmModal
      :id="id"
      v-model:visible="isShowConfirm"
    />
  </a-drawer>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { CloseCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import Form from '../../composables/form';
import useModalHandle from '../../composables/use-modal-handle';
import { EOpenType, IFormList } from '../../data.d';
import { apiGetSalesListByDeptId } from '../../service';
import ConfirmModal from './components/confirm-modal/index.vue';
import Tabs from './composables/tabs';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
    required: true,
  },
  formType: {
    type: Number,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
// 加载
const showSpin = ref(false);
const id = ref('');
const emits = defineEmits(['update:visible', 'get-list']);
const { onCancel, handleOk } = useModalHandle({ emits, isGetList: true });
const { activeKey, tabs } = Tabs();
const {
  formData,
  formRef,
  skuRef,
  getOrder,
  delGoodsItem,
  addGoodsData,
  changeFormData,
  getOnlineDetailApi,
  initFormData,
} = Form({
  showSpin,
});
const isEdit = ref(true);
const isShowConfirm = ref(false);
async function handleGetSalesList(value: any) {
  const res = await apiGetSalesListByDeptId(value);
  formData.userAdminData = res.data || [];
  formData.sales_id = ''
}
async function handleOkBtn() {
  try {
    showSpin.value = true;
    const res = await handleOk({
      formDataRef: formRef.value,
      skuRef: skuRef.value,
      formData,
      formType: props.formType,
    });
    if (res.account_online_id) {
      setTimeout(() => {
        showSpin.value = false;
        id.value = res.account_online_id + '';
        isEdit.value = false;
        isShowConfirm.value = true;
      }, 1000);
    } else {
      showSpin.value = false;
      message.error('申请编号为空');
    }
  } finally {
    showSpin.value = false;
  }
}

// 选择租金期数
function choseRents(value: string, index: number) {
  //引用类型
  const tempFormData = formData.dataList[index];
  if (
    tempFormData.periods.some((e: string) => {
      return e === value;
    })
  ) {
    tempFormData.periods.forEach((item: string, index: any) => {
      if (item === value) tempFormData.periods.splice(index, 1);
    });
  } else tempFormData.periods.push(value);
  // 排序
  tempFormData.periods
    .sort((a: string | number, b: string | number) => {
      return +a - +b;
    })
    .join(',');
}

function getResult(result: number) {
  return Number.isFinite(result) ? Math.round(result * 100) / 100 : 0;
}

watch(
  () => formData.dataList,
  () => {
    const key = ['freight_money', 'transfer_including_tax_money', 'bill_money'];
    const secondKey = ['bill_money', 'transfer_including_tax_money', 'transfer_money'];
    let result = {};
    key.forEach((item: string) => {
      result[item] = 0;
    });
    if (formData.dataList?.length > 0) {
      formData.dataList.map((item: IFormList, index: number) => {
        let sum = 0;
        const tempRentList = item.bills || [];
        tempRentList
          ?.filter((rentItem: { period_num: any }) => item?.periods?.includes(rentItem.period_num))
          .forEach((rentItem: { bill_money: any }) => {
            sum += Number(rentItem.bill_money);
          });
        sum = getResult(sum);
        secondKey.forEach((item: string) => {
          changeFormData(item, sum, index);
        });
        const settleAmount = item.server_ratio ? getResult(item.server_ratio * sum) : 0;
        changeFormData('settle_amount', settleAmount, index);
        key.forEach((keyItem: string) => {
          result[keyItem] += Number(item[keyItem]) || 0;
        });
      });
      for (let key in result) {
        if (key === 'bill_money') {
          changeFormData('transfer_money', getResult(result[key]));
        }
        changeFormData(key, getResult(result[key]));
      }
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

//结算总金额
const allSettleAmount = computed(() => {
  let result = 0;
  if (formData.dataList?.length > 0) {
    formData.dataList.map((item: IFormList) => {
      result += Number(item.settle_amount);
    });
  }
  result = getResult(result);
  return result;
});

function getRules(sku: IFormList) {
  const rules = [
    {
      required: true,
      trigger: 'blur',
      validator: (_: any, value: string | number) => {
        if (value === '' || value === null) {
          return Promise.reject(new Error('请输入当月转入含税金额'));
        }
        if (Number(value) > Number(sku.transfer_money)) {
          return Promise.reject(new Error('当前值应小于或等于当月转入金额'));
        }
        return Promise.resolve();
      },
    },
  ];
  return rules;
}

watch(
  () => props.visible,
  val => {
    if (val) {
      switch (props.formType) {
        case EOpenType.Launch:
          isEdit.value = true;
          break;
        case EOpenType.LaunchView:
          isEdit.value = false;
          getOnlineDetailApi(props.id, true);
          break;
        default:
          break;
      }
    } else {
      initFormData();
    }
  },
);
</script>
<style lang="less" scoped>
@import './index.less';
</style>
