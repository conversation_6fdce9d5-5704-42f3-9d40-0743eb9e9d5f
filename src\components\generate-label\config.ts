import { FormGroupItem } from 'rrz-web-design';

import { APIGetBrandLabelList } from './services';
const transformDataToArray = (data: Record<string, any>): Array<{ label: string; value: number }> => {
  return Object.values(data)
    .map(item => ({
      label: item.name,
      value: item.id,
    }))
    .filter(item => item.value);
};

export const formGroup: FormGroupItem[] = [
  {
    key: 'operate_type',
    label: '操作',
    originProps: {
      required: true,
    },
    elProps: {
      placeholder: '请选择操作类型',
      optionType: 'default',
    },
    options: [
      {
        label: '新增',
        value: 'add',
      },
      {
        label: '删除',
        value: 'del',
      },
    ],
    fragmentKey: 'renderRadio',
  },
  {
    key: 'operate_object_type',
    label: '请选择标签对象',
    originProps: {
      rules: {
        required: true,
      },
    },
    elProps: {
      placeholder: '请选择标签对象',
      allowClear: true,
    },
    options: [
      { label: '商家', value: 1, disabled: false },
      { label: '商品', value: 2, disabled: false },
      { label: '套餐', value: 3, disabled: true },
    ],
    fragmentKey: 'renderSelect',
    changeHandler({ setForm }) {
      setForm('pid', null);
    },
  },
  {
    key: 'pid',
    label: '请选择一级标签',
    originProps: {
      required: true,
    },
    elProps: {
      placeholder: '请选择一级标签',
      showSearch: true,
    },
    setOption: async (model: Record<string, unknown>) => {
      if (!model.operate_object_type) return;
      const { data } = await APIGetBrandLabelList({
        tag_type: model.operate_object_type,
      });
      return transformDataToArray(data.list);
    },
    fragmentKey: 'renderSelect',
    everyTimeLoad: true,
    changeHandler({ setForm }) {
      setForm('tagIds', []);
    },
  },
  {
    key: 'tagIds',
    label: '请选择二级标签',
    originProps: {
      required: true,
    },
    elProps: {
      placeholder: '请选择二级标签',
      mode: 'multiple',
      showSearch: true,
    },
    fragmentKey: 'renderSelect',
    everyTimeLoad: true,
    display: (formData: Record<string, unknown>) => formData.operate_object_type !== 1,
    setOption: async (model: Record<string, any>) => {
      if (!model.pid) return;
      const { data } = await APIGetBrandLabelList({
        tag_type: model.operate_object_type,
      });
      const list = Object.values(data.list) as any;
      const selectedItem = list.find(item => item.id === model.pid);
      return selectedItem ? transformDataToArray(selectedItem.child) : [];
    },
  },
  {
    key: 'object_ids_str',
    label: '请输入商家/商品/套餐ID',
    originProps: {
      required: true,
    },
    elProps: {
      placeholder: '多个ID请用英文; 隔开',
      showCount: true,
      allowClear: true,
    },
    fragmentKey: 'renderTextarea',
  },
];
