<template>
  <a-modal
    v-model:visible="bindVisible"
    :footer="releaseInfo.status === '1' ? null : undefined"
    :ok-text="showForm ? '确定' : '重新申请'"
    style="width: fit-content; min-width: 400px"
    title="解除平台配置申请"
    @cancel="handleClose"
    @ok="handleConfirm"
  >
    <a-form
      ref="formRef"
      :layout="showForm ? 'vertical' : 'undefined'"
      :model="formState"
    >
      <a-form-item
        v-if="showForm"
        label="解除原因"
        name="relieve_reason"
        required
        style="margin-bottom: 0"
      >
        <a-textarea
          v-model:value="formState.relieve_reason"
          :maxlength="100"
          placeholder="请输入驳回原因，限制输入1-100个字"
          :rows="4"
          style="width: 400px"
        />
      </a-form-item>
      <template v-else>
        <a-form-item
          class="custom-form-item"
          label="进度"
        >
          <a-badge v-bind="statusMap[releaseInfo.status]" />
        </a-form-item>
        <a-form-item
          v-if="releaseInfo.status === '1'"
          class="custom-form-item"
          label="解除原因"
        >
          <span class="value">{{ releaseInfo.relieve_reason }}</span>
        </a-form-item>
        <a-form-item
          v-if="releaseInfo.status === '2'"
          class="custom-form-item"
          label="驳回原因"
        >
          <span class="value">{{ releaseInfo.reject_reason }}</span>
          <span class="tips">（具体问题可联系运营人员处理）</span>
        </a-form-item>
      </template>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { FormInstance, message } from 'ant-design-vue';

import { useVModel } from '@/hook';

import { getRelieveInfo, postApplyRelieveInfo } from './services';

const statusMap = {
  1: { text: '处理中', status: 'warning' },
  2: { text: '已驳回', status: 'error' },
  3: { text: '已解除', color: 'rgba(6, 21, 51, 0.15)' },
  4: { text: '已结束', color: 'rgba(6, 21, 51, 0.15)' },
};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  businessId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'resetOptions'): void;
}>();

const bindVisible = useVModel(props, 'visible', emits);

const formRef = ref<FormInstance>();
const formState = reactive({ relieve_reason: '' });

const releaseInfo = reactive({ relieve_reason: '', reject_reason: '', status: '0' });

const hackShowForm = ref(false);
const showForm = computed(() => hackShowForm.value || releaseInfo.status === '0');

function handleConfirm() {
  if (!showForm.value) {
    hackShowForm.value = true;
  }
  formRef.value?.validate().then(({ relieve_reason }) => {
    postApplyRelieveInfo({ business_id: props.businessId, relieve_reason }).then(() => {
      message.success('操作成功');
      emits('resetOptions');
      bindVisible.value = false;
    });
  });
}

function handleClose() {
  bindVisible.value = false;
  formRef.value?.resetFields();
}

watch(
  () => props.visible,
  val => {
    if (val) {
      hackShowForm.value = false;
      getRelieveInfo({ business_id: props.businessId }).then(({ data }) => {
        Object.assign(releaseInfo, data);
        // 没有申请记录默认可以申请
        if (!data) {
          releaseInfo.status = '0';
        }
      });
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="less">
.custom-form-item {
  flex-flow: row;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  :deep(.ant-form-item-label) {
    flex: none;
  }

  :deep(.ant-form-item-label > label) {
    height: auto;
    color: rgba(6, 21, 51, 0.65);
  }

  :deep(.ant-form-item-control-input) {
    max-width: 400px;
    min-height: unset;
  }

  .dots {
    width: 6px;
    height: 6px;
    background-color: #faad14;
    border-radius: 50%;

    &.error {
      background: #ff4d4f;
    }
  }

  .value {
    color: rgba(6, 21, 51, 0.85);
  }

  .tips {
    color: rgba(6, 21, 51, 0.45);
  }
}
</style>
