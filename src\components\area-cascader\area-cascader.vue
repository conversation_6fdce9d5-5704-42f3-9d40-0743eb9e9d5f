<template>
  <a-cascader
    v-model:value="selectValue"
    v-bind="mergedAttrs"
    change-on-select
    :field-names="mergedFieldNames"
    :load-data="loadData"
    :max-tag-count="mergedConfig.maxTagCount"
    :multiple="mergedConfig.multiple"
    :options="processedOptions"
    :placeholder="mergedConfig.placeholder"
    :style="mergedConfig.style"
    @change="changeSelect"
  />
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, useAttrs, watch } from 'vue';
import { cloneDeep, merge } from 'lodash-es';

import type { CascaderExtra, TreeNode } from './data';
import { ApiGetChildByPid, ApiGetCityLibraryData } from './service';

const props = withDefaults(
  defineProps<{
    options?: TreeNode[];
    value?: (string | number)[];
    extra?: CascaderExtra;
  }>(),
  {
    options: () => [],
    value: () => [],
    extra: () => ({}),
  },
);

const emit = defineEmits<{
  'update:value': [value: (string | number)[]];
}>();

const attrs = useAttrs();

const mergedConfig = computed(() =>
  merge(
    {
      placeholder: '请选择',
      style: 'width: 100%',
      maxTagCount: 'responsive',
      fieldNames: {
        label: 'n',
        value: 'i',
        children: 'c',
      },
      multiple: false, // 是否多选状态
      level: 3, // 1-省 2-市 3-区 4-街道，默认可选择到区
      mode: 'cascader', // 级联器 / 选择器
    },
    props.extra,
  ),
);

const mergedFieldNames = computed(() => ({
  label: mergedConfig.value.fieldNames?.label || 'name',
  value: mergedConfig.value.fieldNames?.value || 'id',
  children: mergedConfig.value.fieldNames?.children || 'children',
}));

const mergedAttrs = computed(() => ({
  ...attrs,
  'field-names': undefined,
  'max-tag-count': undefined,
  'placeholder': undefined,
  'style': undefined,
}));

const processedOptions = computed(() => {
  const data = props.options.length ? props.options : treeData.value;
  return data;
});

const selectValue = ref<(string | number)[]>(props.value);

const treeData = ref([]);
const cloneData = ref([]);

async function queryAddressTreeData() {
  const res = await ApiGetCityLibraryData();
  const data = await formatCity(res.data);
  treeData.value = data;
  cloneData.value = cloneDeep(res.data);
}

async function setDynamicData() {
  treeData.value = await formatCity(cloneData.value);
}

watch(
  () => mergedConfig.value.level,
  () => {
    setDynamicData();
  },
);
const childList = ref([]);
async function formatCity(data: any[]) {
  const provinceMap = new Map();
  const cityMap = new Map();
  const regionMap = new Map();
  const flatten = (items: any) => {
    return items.flatMap((item: any) => [item, ...(item.c ? flatten(item.c) : [])]);
  };
  const list = flatten(data);
  list.forEach(({ n, i, pi, l }: any) => {
    const map = l === 1 ? provinceMap : l === 2 ? cityMap : regionMap;
    map.set(i, {
      n,
      i,
      c: [],
      pi,
    });
  });

  childList.value = [];

  const isInitLoadFour =
    props.value.every(item => item.length === 4) && props.value && mergedConfig.value.mode === 'cascader';

  const ids = [...new Set(props.value.map(item => item[item.length - 2]))];

  if (isInitLoadFour) {
    await Promise.all(
      ids.map(async pid => {
        const res = await ApiGetChildByPid({ pid });
        const data = {
          key: pid,
          data: res.data.map(item => ({
            n: item.name,
            i: item.id,
            c: undefined,
            pi: item.pid,
            keyId: pid,
          })),
        };
        childList.value.push(data);
      }),
    );
  }

  regionMap.forEach(region => {
    const city = cityMap.get(region.pi);
    region.pi = undefined;
    if (region.c?.length === 0) {
      // 四级默认填充下级，否则动态加载街道数据时无法查询下级数据
      let child = [
        {
          c: undefined,
          i: undefined,
          n: undefined,
          pi: undefined,
        },
      ];

      let target;
      if (isInitLoadFour) {
        target = childList.value?.find(item => {
          if (item.key === region.i) {
            return item;
          }
        });
      }
      if (Number(mergedConfig.value.level) >= 4) {
        region.c = target ? target.data : child;
      } else {
        region.c = undefined;
      }
    }
    if (city && Number(mergedConfig.value.level) >= 3) {
      city.c?.push(region);
    }
  });

  cityMap.forEach(city => {
    const province = provinceMap.get(city.pi);
    city.pi = undefined;
    if (city.c?.length === 0) {
      city.c = undefined;
    }
    if (province && Number(mergedConfig.value.level) >= 2) {
      province.c?.push(city);
    }
  });

  provinceMap.forEach(province => {
    province.pi = undefined;
    if (province.c?.length === 0) {
      province.c = undefined;
    }
  });

  // 返回级联器组件
  if (mergedConfig.value.mode === 'cascader') {
    return Array.from(provinceMap.values());
  } else if (mergedConfig.value.mode === 'select') {
    // 返回选择组件
    const map = mergedConfig.value.level === 1 ? provinceMap : mergedConfig.value.level === 2 ? cityMap : regionMap;
    return Array.from(map.values());
  }
}

async function loadData(selectedOptions) {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  if (selectedOptions.length === 3) {
    targetOption.loading = true;
    try {
      const res = await ApiGetChildByPid({
        pid: targetOption.i,
      });
      targetOption.c = res.data.map(item => ({
        n: item.name,
        i: item.id,
        c: undefined,
      }));
      treeData.value = [...treeData.value];
    } finally {
      targetOption.loading = false;
    }
  }
}

function changeSelect(value) {
  emit('update:value', value);
}

watch(
  () => props.value,
  newVal => {
    selectValue.value = newVal?.length ? [...newVal] : [];
  },
  {
    immediate: true,
  },
);

onBeforeMount(() => {
  queryAddressTreeData();
});

defineExpose({
  setDynamicData,
});
</script>
