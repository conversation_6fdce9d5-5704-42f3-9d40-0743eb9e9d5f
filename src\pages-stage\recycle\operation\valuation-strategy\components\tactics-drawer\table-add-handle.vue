<template>
  <div class="table-add-handle">
    <plus-circle-outlined class="handle-icon" />
    新增数据条目
    <div />
  </div>
</template>

<script lang="ts" setup>
import { PlusCircleOutlined } from '@ant-design/icons-vue';
</script>

<style lang="less" scoped>
.table-add-handle {
  display: inline-flex;
  align-items: center;
  padding: 16px 0 24px 0;
  color: #00c8be;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  border-bottom: 1px solid #e9e9e9;
  cursor: pointer;
  .handle-icon {
    margin-right: 8px;
    color: #00c8be;
    font-size: 16px;
  }
}
</style>
