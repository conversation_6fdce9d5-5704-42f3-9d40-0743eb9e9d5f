export interface ISearchInfo {
  source_type: string | null;
  category_brand_model: string[] | number[] | undefined;
  sku_id: string | null;
  condition: string[] | null;
  no_sale_stock: number;
}

export interface IDataSource {
  category_name: string;
  brand_name: string;
  model_name: string;
  sku_info: string;
  sku_id: string;
  stock: string;
  can_sale_stock: string;
  average_purchased_price: string;
  purchased_price: string;
}
