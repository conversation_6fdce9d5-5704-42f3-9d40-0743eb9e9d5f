export enum EField {
  sales_increase = '销售新增业绩',
  sales_outstanding = '销售回款',
  return_of_order = '订单退租',
  order_overdue = '订单逾期',
}

export type TField = keyof typeof EField;

export enum ECardField {
  performance_target = '业绩目标进度',
  charge = '回款进度',
  terminate_lease = '退租',
  overdue = '逾期',
}

export type TCardField = keyof typeof ECardField;

export interface ISelectItem {
  label: string;
  value: string | number;
  disabled?: boolean;
}

export interface IFormData {
  dept_id: string | null | number;
  dept_type: 1 | 2;
  sales_ids: string[] | string;
  month: string;
  time_type: 1 | 2 | 3;
}
