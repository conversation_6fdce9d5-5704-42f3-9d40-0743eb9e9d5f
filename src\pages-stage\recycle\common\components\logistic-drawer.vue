<template>
  <a-drawer
    :closable="false"
    :destroy-on-close="true"
    title="物流信息"
    :visible="drawerVisible"
    width="800px"
  >
    <!-- 关闭按钮 -->
    <template #extra>
      <div
        class="close-btn"
        @click="closeDrawer"
      >
        <CloseOutlined />
      </div>
    </template>
    <!-- 主体区 -->
    <div class="drawer-inner">
      <div class="drawer-inner-info">
        <div class="title">
          <span>物流公司：</span>
          <span>{{ shipper_name }}</span>
        </div>
        <div class="title">
          <span>物流单号：</span>
          <span>{{ info.number }}</span>
        </div>
      </div>
      <!-- 物流信息 -->
      <a-spin :spinning="loading">
        <div class="tracking-list clearfix">
          <a-timeline v-if="hasList">
            <template
              v-for="(item, index) in logisticList"
              :key="index"
            >
              <a-timeline-item>
                <div class="timeline-item flex-wrap flex-vertical">
                  <div>{{ item[1] }}</div>
                  <div>{{ item[0] }}</div>
                </div>
              </a-timeline-item>
            </template>
          </a-timeline>
          <a-empty
            v-else
            description="暂未查询到物流信息"
            :image="simpleImage"
          />
        </div>
      </a-spin>
    </div>
    <template #footer>
      <div class="flex-wrap flex-x-end">
        <a-button
          type="primary"
          @click="() => closeDrawer()"
        >
          确认
        </a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { Empty } from 'ant-design-vue';
import { POST } from '@/services/api';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';

function getLogisticsTrajectory(data: any) {
  data.method = 'logistic.code.get';
  return POST('/api/gateway', data, {
    successStatusCheckValue: -3,
  });
}

type InitConfig = {
  type?: string;
  code?: string;
  phone?: string;
  shipper_name?: string;
};

interface iProp extends InitConfig {
  visible?: boolean;
}

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const props = defineProps<iProp>();
const emit = defineEmits(['update:visible']);

const _visible = ref(props.visible || false);
const drawerVisible = computed({
  get: () => _visible.value,
  set: newVal => {
    _visible.value = newVal;
    emit('update:visible', newVal);
  },
});

const shipper_name = ref<string>('顺丰快递');

const info = ref({
  type: 'SF',
  code: '',
  phone: '',
});

const closeDrawer = () => (drawerVisible.value = false);

const logisticList = ref<any[]>([]);
const [hasList, { setTrue: setHasList, setFalse: setNoList }] = useBoolean(false);
const [loading, { setFalse: setLoadingDone, setTrue: setLoading }] = useBoolean(true);

const init = (config?: InitConfig) => {
  console.log('config--', config);
  if (config) {
    drawerVisible.value = true;
  }
  if (config.shipper_name) {
    shipper_name.value = config.shipper_name;
  }
  Object.assign(info.value, {
    number: config.code || props.code,
    phone: config.phone || props.phone,
  });
  console.log('info.', info.value);
  setNoList();
  setLoading();
  getLogisticsTrajectory({
    biz_content: JSON.stringify({
      type: config.type || props.type || 'SF',
      number: config.code || props.code,
      phone: config.phone || props.phone,
    }),
    ordinary_delivery: 1,
  }).then(res => {
    let { html } = res;
    html && setHasList();
    logisticList.value = html.split('<br/>').map((item: string) => item.split('&nbsp;'));
    setLoadingDone();
  });
};

watch(
  () => props.visible,
  visible => {
    visible && init();
  },
);

defineExpose({
  open: init,
});
</script>

<style lang="less" scoped>
.close-btn {
  color: rgba(0, 0, 0, 0.45);
  font-weight: 600;
  font-size: 16px;
  font-style: normal;
  line-height: 1;
  cursor: pointer;
  transition: color 0.2s;
  &:hover {
    color: rgba(0, 0, 0, 0.88);
  }
}
.drawer-inner {
  padding-bottom: 53px;
  .drawer-inner-info {
    margin-bottom: 24px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    .title {
      span:last-child {
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
      }
    }
    .title + .title {
      margin-top: 12px;
    }
  }
  .tracking-list {
    padding: 16px 16px 0;
    background: #f6f7f8;
    border-radius: 2px;
    :deep(.ant-timeline-item-last > .ant-timeline-item-content) {
      min-height: 0;
    }
    .timeline-item div:last-child {
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }
  .drawer-action {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 10px 24px;
    background: #fff;
    border-top: 1px solid #e9e9e9;
  }
}
</style>
