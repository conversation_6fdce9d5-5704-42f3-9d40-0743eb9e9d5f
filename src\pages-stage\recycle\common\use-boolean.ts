import { ref } from 'vue';

export default function useBoolean(defaultValue = false) {
  const state = ref(defaultValue);
  const setState = (value: boolean) => (state.value = value);
  const setTrue = () => (state.value = true);
  const setFalse = () => (state.value = false);
  const toggle = () => (state.value = !state.value);
  return [
    state,
    {
      setState,
      setTrue,
      setFalse,
      toggle,
    },
  ] as const;
}
