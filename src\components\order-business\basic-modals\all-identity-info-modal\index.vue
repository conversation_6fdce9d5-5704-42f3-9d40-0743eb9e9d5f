<template>
  <a-modal
    v-model:visible="bindVisible"
    title="全部身份信息"
    :width="1000"
  >
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <a-list
        class="list"
        :data-source="dataList"
        item-layout="horizontal"
      >
        <template #renderItem="{ item, index }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                <span
                  v-if="item.is_order_user == 1"
                  class="col"
                ><a-tag color="orange">下单人</a-tag>：{{ item.name || '-' }}</span>
                <span
                  v-else
                  class="col"
                ><a-tag color="orange">关联人{{ index + 1 }}</a-tag>：{{ item.name }}</span>
                <span class="col">用户id：{{ item.user_id || '-' }}</span>
                <span class="col">身份证：
                  <sensitive-field
                    field="id_card"
                    field-type="5"
                    id-key="alipay_auto_id"
                    :is-super-v2="true"
                    :row="item"
                    :type="883"
                  />
                </span>
                <span class="col">年龄：{{ item.age || '-' }}</span>
                <span class="col">性别：{{ item.sex || '-' }}</span>
                <span class="col">预授权支付宝号：
                  <sensitive-field
                    field="alipay_user_id"
                    field-type="13"
                    id-key="alipay_auto_id"
                    :is-super-v2="true"
                    :row="item"
                    :type="883"
                  />
                </span>
                <span class="col">支付宝地址：{{ item.address || '-' }}</span>
                <span class="col">垫付订单数：{{ item.help_pay_num || '-' }}
                  <a-button
                    v-if="item.help_pay_num > 0"
                    type="link"
                    @click="goRiskListV2(item.pay_order)"
                  >
                    详情
                  </a-button></span>
                <span class="col">身份逾期订单：{{ item.overdue_order_num || '-' }}
                  <a-button
                    v-if="item.overdue_order_num > 0"
                    type="link"
                    @click="goRiskListV2(item.overdue_order)"
                  >
                    详情
                  </a-button></span>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { useVModel } from '@/hook';

import type { IDentityInfo } from './data';
import { getAllIdentity } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  userId: {
    type: Number,
    default: undefined,
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emit);
const dataList = ref<IDentityInfo[]>([]);
const loading = ref(false);

watch(
  () => props.visible,
  val => {
    if (val) {
      loadData();
    }
  },
  { immediate: true },
);

/** 初始化加载数据 */
function loadData() {
  loading.value = true;
  getAllIdentity({
    order_id: props.orderId,
    user_id: props.userId,
  })
    .then(res => {
      if (res.data?.list) {
        const orderList = res.data.list.filter((item: IDentityInfo) => item.is_order_user == 1);
        const otherList = res.data.list.filter((item: IDentityInfo) => item.is_order_user != 1);
        dataList.value = orderList.concat(otherList);
      }
    })
    .catch(() => {
      bindVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

const route = useRoute();
const domain = route.query.origin || window.location.origin;

function goRiskListV2(order_ids: string[]) {
  window.open(`${domain}/super/v2-order/wait-risk-orders-v3?order_id=${order_ids?.join?.(',')}`);
}
</script>

<style scoped lang="less">
.list {
  :deep(.ant-list-item-meta-title) {
    line-height: 1.8;
  }
}

// 列样式
.col + .col {
  margin-left: 12px;
}

.col + .col::before {
  display: inline-block;
  width: 1px;
  height: 14px;
  margin-right: 12px;
  background: rgba(6, 21, 51, 0.15);
  transform: translateY(2px);
  content: '';
}
</style>
