<template>
  <a-modal
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    :title="title"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="onConfirm"
  >
    <a-spin :spinning="loading">
      <a-form layout="vertical">
        <a-form-item
          label="属性名称"
          v-bind="validateInfos.name"
        >
          <a-input
            v-model:value.trim="formState.name"
            :maxlength="12"
            placeholder="请输入属性名称"
            show-count
            @blur="onValidate('name')"
            @change="onValidate('name')"
          />
        </a-form-item>
        <!-- 评估内容 -->
        <div>
          <a-form-item
            class="fake-form-item"
            label="评估内容"
            required
          >
            <div class="form-item-card">
              <a-input
                v-model:value.trim="formState.title_data.title"
                :maxlength="25"
                placeholder="请输入评估内容"
                show-count
                @blur="onValidate('title_data')"
                @change="onValidate('title_data')"
              />
              <div class="flex-wrap inline">
                <image-upload
                  v-model:value="formState.title_data.images"
                  :max="1"
                  :remove-confirm="true"
                  upload-text="646*320px"
                />
                <a-textarea
                  v-model:value="formState.title_data.desc"
                  :maxlength="80"
                  placeholder="请输入详细说明（80字符）"
                  show-count
                />
              </div>
            </div>
          </a-form-item>
          <a-form-item
            class="fake-form-item"
            v-bind="validateInfos.title_data"
          />
        </div>

        <!-- 评估项 -->
        <div>
          <a-form-item
            class="fake-form-item"
            required
          >
            <template #label>
              <span>评估项（{{ optionFormList.length }}）</span>
            </template>
            <template
              v-for="({ state, validateInfos: _validateInfos, validate: _validate }, index) in optionFormList"
              :key="index"
            >
              <div class="form-item-card">
                <CloseCircleOutlined
                  v-if="optionFormList.length > 1"
                  class="action-label"
                  @click="() => onDelOption(state, index)"
                />
                <a-form layout="vertical">
                  <a-form-item
                    label="选项标题"
                    required
                    v-bind="_validateInfos['title']"
                    style="margin-bottom: 0"
                  >
                    <a-input
                      v-model:value.trim="state.title"
                      :maxlength="25"
                      placeholder="请输入评估项标题"
                      show-count
                    />
                  </a-form-item>

                  <a-collapse
                    v-show="[2, 3].includes(formState.type)"
                    collapsible="header"
                    ghost
                  >
                    <a-collapse-panel :show-arrow="false">
                      <template #header>
                        <a-space @click="state.expand = !state.expand">
                          <span
                            :style="{
                              transition: 'color .2s ease',
                              color:
                                _validateInfos['images'].validateStatus === 'error'
                                  ? 'var(--ant-error-color)'
                                  : 'inherit',
                            }"
                          >编辑详情</span>
                          <RightOutlined
                            :rotate="state.expand ? 90 : 0"
                            style="color: #5d677a; font-size: 12px"
                          />
                        </a-space>
                      </template>
                      <image-upload
                        v-model:value="state.images"
                        :class="_validateInfos['images'].validateStatus"
                        :max="4"
                        :remove-confirm="true"
                        :upload-text="`686*520px\n${state.images.length || 0}/4张`"
                      />
                      <div
                        v-for="(help, i) in _validateInfos['images'].help"
                        :key="i"
                        class="vali-error-text"
                      >
                        {{ help[0] }}
                      </div>
                      <a-textarea
                        v-model:value="state.desc"
                        :maxlength="80"
                        placeholder="请输入详细说明（80字符）"
                        :rows="4"
                        show-count
                        @blur="_validate('images')"
                      />
                    </a-collapse-panel>
                  </a-collapse>
                </a-form>
              </div>
            </template>
          </a-form-item>
          <a-form-item
            class="fake-form-item"
            v-bind="validateInfos.option"
          >
            <a-button
              class="add-option-btn"
              :disabled="isLimit"
              type="link"
              @click="onAddOption"
            >
              <PlusCircleFilled />
              <span>添加评估项</span>
            </a-button>
          </a-form-item>
        </div>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import { PlusCircleFilled, CloseCircleOutlined, RightOutlined } from '@ant-design/icons-vue';
import { message, Form, Modal } from 'ant-design-vue';
import { ref, reactive, computed } from 'vue';
import { saveEvaluateOption, queryEvaluateList } from '../service';
import imageUpload from '@/components/image-upload/image-upload.vue';
import useOptionForm, { IOptionForm, IOptionItem } from '../composables/use-option-form';

const emit = defineEmits(['ok']);

const title = ref<string>('');
const visible = ref<boolean>(false);
const isEdit = ref<boolean>(false);
const loading = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);
const formState = reactive({
  type: 0,
  id: 0,
  name: '',
  title_data: {
    title: '',
    desc: '',
    images: [],
  },
});

const titleValidator = async (_rule: Rule, value: any): Promise<void> => {
  if (value.title.trim()) {
    return Promise.resolve();
  } else {
    return Promise.reject('评估内容标题必填');
  }
};

const formRules: Record<string, Rule[]> = {
  name: [
    {
      required: true,
      message: '属性名称不能为空',
    },
  ],
  title_data: [
    {
      required: true,
      validator: titleValidator,
    },
  ],
};

const { resetFields, validate, validateInfos } = Form.useForm(formState, formRules);

const onValidate = (key: string) => {
  try {
    validate([key]);
  } catch (error) {}
};

// 评估项
const optionFormList = ref<IOptionForm[]>([]);

const isLimit = computed(() => {
  const limit = formState.type == 3 ? 20 : 8;
  return limit < optionFormList.value.length + 1;
});

const validateOptions = () =>
  new Promise((resolve, reject) => {
    const promiseList = optionFormList.value.map(({ validate }) => validate());
    Promise.all(promiseList).then(resolve).catch(reject);
  });

const onAddOption = async () => {
  validateOptions()
    .then(() => {
      const { state, resetFields, validate, validateInfos } = useOptionForm();
      optionFormList.value.push({ state, resetFields, validate, validateInfos });
    })
    .catch(console.error);
};

// 删除评估项
const onDelOption = (state: IOptionItem, index: number) => {
  const { title, desc, images } = state;
  if (title || desc || images.length) {
    Modal.confirm({
      title: '将丢失该选项已填内容，确定吗？',
      onOk: () => {
        optionFormList.value.splice(index, 1);
      },
    });
  } else optionFormList.value.splice(index, 1);
};

const init = async (payload?: any) => {
  optionFormList.value.length = 0;
  resetFields();
  const { title: _title, type, id } = payload;
  isEdit.value = !!id;
  title.value = _title;
  formState.type = type;
  if (!id) {
    onAddOption();
    return;
  }
  loading.value = true;
  const { data } = await queryEvaluateList({ id });
  const [rowData] = data;
  Object.assign(formState, rowData);
  rowData.option_data.forEach((data: IOptionItem) => {
    const { state, resetFields, validate, validateInfos } = useOptionForm(data);
    optionFormList.value.push({ state, resetFields, validate, validateInfos });
  });
  loading.value = false;
};

// 打开弹窗
const open = (title: string, type: string, id?: string) => {
  init({ title, type, id });
  visible.value = true;
};

// 确认编辑/添加
const onConfirm = async () => {
  confirmLoading.value = true;
  try {
    await Promise.all([validate(), validateOptions()]);
    let [payload, { type, id, name, title_data }] = [{}, formState];
    const option_data = optionFormList.value.map(({ state }) => {
      delete state['expand'];
      return state;
    });
    if (isEdit.value) {
      payload = { type, id, name, title_data, option_data };
    } else {
      payload = { type, name, title_data, option_data };
    }
    await saveEvaluateOption(payload);
    message.success(isEdit.value ? '修改成功' : '添加成功');
    emit('ok', isEdit.value ? null : payload);
    visible.value = false;
  } catch (error) {
    message.error('请先完善表单再提交');
  } finally {
    confirmLoading.value = false;
  }
};

const onCancel = () => {
  visible.value = false;
};

defineExpose({
  open,
});
</script>
bnm./

<style lang="less" scoped>
.action-label {
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s;
  &:hover {
    color: var(--ant-error-color);
  }
  &[disabled] {
    cursor: not-allowed;
  }
}
.add-option-btn {
  padding: 0;
  &:not([disabled]) {
    color: inherit;
    & span:first-child {
      color: var(--ant-primary-color);
    }
  }
}

.fake-form-item {
  margin-bottom: 0;
  & + .fake-form-item {
    margin-top: 2px;
    margin-bottom: 24px;
  }
  :deep(.ant-form-item-control > .ant-form-item-control-input) {
    min-height: 0;
  }
}
.ant-form-item-has-error.ant-form-item-with-help.fake-form-item {
  margin-bottom: 0;
}

.form-item-card {
  position: relative;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  & + .form-item-card {
    margin-top: 12px;
  }
  .vali-error-text {
    margin-bottom: 2px;
    color: var(--ant-error-color);
    font-size: 14px;
  }
  /** 自定义textarea样式 */
  .ant-input-textarea.ant-input-textarea-show-count {
    position: relative;
    flex: 1;
    width: 100%;
    &::after {
      position: absolute;
      right: 10px;
      bottom: 24px;
    }
  }
  /** 折叠编辑面板样式 */
  :deep(.ant-collapse > .ant-collapse-item.ant-collapse-no-arrow) {
    .ant-collapse-header,
    .ant-collapse-content-box {
      padding: 8px 0 0;
    }
  }
}
/** 自定义imageUpload样式 */
:deep(.ant-upload.ant-upload-select-picture-card),
:deep(.ant-upload-list-picture-card-container) {
  width: 88px;
  height: 88px;
  background: rgba(6, 21, 51, 0.04);
}
.inline {
  margin-top: 8px;
  :deep(.ant-upload.ant-upload-select-picture-card),
  :deep(.ant-upload-list-picture-card-container) {
    width: 104px;
    height: 104px;
    margin-bottom: 0;
  }
}
.error {
  :deep(.ant-upload.ant-upload-select-picture-card) {
    border-color: var(--ant-error-color);
  }
}
</style>
