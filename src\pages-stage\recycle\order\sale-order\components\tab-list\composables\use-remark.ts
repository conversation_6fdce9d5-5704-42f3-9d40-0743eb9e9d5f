import { ref } from 'vue';

import { IOrderList, RemarkType } from '../data';
import { ApiGetRemarks } from '../service';

export default () => {
  // order.recycel备注映射
  const orderRemarks = ref<Record<string, RemarkType>>({});

  // 加载备注列表
  const getCrmList = async (unionIds: string[], type: string) => {
    return ApiGetRemarks({
      unionIds,
      unionSuffix: '_' + type,
    }).then(res => Object.fromEntries(res.data.map(item => [item.union_id, item])));
  };

  // 加载备注
  const loadRemark = async (list: IOrderList[]) => {
    const orderIds = list.map(item => item.commonHead.rs_order_id);
    orderRemarks.value = await getCrmList(orderIds, 'order.recycle');
  };

  return {
    loadRemark,
    orderRemarks,
  };
};
