<template>
  <a-modal
    v-model:visible="bindVisible"
    title="修改订单状态"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-space
        class="alert-box"
        direction="vertical"
        :size="8"
      >
        <a-alert
          v-for="item in reasonList"
          :key="item.text"
          :color="item.color"
          :message="item.text"
          type="warning"
        />
      </a-space>
      <a-form
        ref="formRef"
        :label-col="{ span: 4 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item
          label="订单状态"
          name="order_status"
        >
          <a-select
            v-model:value="formData.order_status"
            :allow-clear="true"
            :options="STATUS_OPTIONS"
            placeholder="请选择"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import { computed, ref, watch } from 'vue';
import { FormInstance, message } from 'ant-design-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import { EOrderStatus } from '@/typing';

import { editOrderStatus } from './services';

interface IFormData {
  order_status?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  orderStatus: {
    type: Number as PropType<EOrderStatus>,
    default: undefined,
  },
  payStatus: {
    type: Number,
    default: undefined,
  },
  hadPayDeposit: {
    type: [Number, String],
    default: 0,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const STATUS_OPTIONS = [
  { label: '待付款', value: EOrderStatus.NoPay },
  { label: '待风控', value: EOrderStatus.WaitRisk },
  { label: '待发货', value: EOrderStatus.WaitSend },
  { label: '待收货', value: EOrderStatus.WaitReceive },
  { label: '待归还', value: EOrderStatus.WaitReturn },
  { label: '归还中', value: EOrderStatus.Returning },
  { label: '交易完成', value: EOrderStatus.Complete },
  { label: '取消（用户）', value: EOrderStatus.CloseUser },
  { label: '取消（租赁商）', value: EOrderStatus.CloseServer },
  { label: '取消（系统）', value: EOrderStatus.CloseSystem },
  { label: '删除', value: EOrderStatus.Deleted },
];

const reasonList = computed(() => {
  const list = [
    {
      text: '修改订单状态，不涉及逻辑处理，例如：从待归还中修改为取消，不会退款。',
      color: '',
    },
  ];
  if (props.payStatus === 11) {
    list.push({
      text: '订单为个人小程序订单，改为取消时请确保信用租订单取消。',
      color: 'red',
    });
  }
  if (props.hadPayDeposit > 0) {
    list.push({
      text: '订单有尚未解冻或退回的押金，改为完结时请注意解冻需退押金',
      color: 'red',
    });
  }
  return list;
});

const formData = ref<IFormData>({});
const bindVisible = useVModel(props, 'visible', emit);
const rules = {
  order_status: [{ required: true, message: '订单状态不能为空', trigger: 'blur' }],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      // 初始化表单
      formData.value = {
        order_status: props.orderStatus,
      };
    }
  },
  { immediate: true },
);

/** 确认 */
function submit() {
  formRef.value?.validate().then(async () => {
    const params = {
      orderId: props.orderId,
      order_status: String(formData.value.order_status),
    };

    loading.value = true;
    editOrderStatus(params)
      .then(() => {
        message.success('操作成功');
        bindVisible.value = false;
        emit('refresh', props.orderId);
      })
      .finally(() => (loading.value = false));
  });
}
</script>

<style lang="less" scoped>
.alert-box {
  margin-bottom: 24px;
}
</style>
