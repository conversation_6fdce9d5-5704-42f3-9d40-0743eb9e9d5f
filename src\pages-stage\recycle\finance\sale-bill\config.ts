import { FormGroupItem, Option } from '@/components/form-create/src/typing';
import { TableColumnType } from 'ant-design-vue';
import TableStatus from '@/components/table-status/index';
import useCategory from '../../order/sale-order/components/tab-list/composables/use-category';
import { ISaleBillList } from './data';

type S = Option[];

interface ISearOptions {
  orderStatusOptions: Array<Option & { color?: string }>;
  customerTypeOtions: S;
  orderTypeOptions: S;
  payWaysOptions: S;
}

export const searchOptions: ISearOptions = {
  orderStatusOptions: [
    {
      label: '全部',
      value: '',
    },
    {
      label: '预出账',
      value: '0',
      color: '#3777FF',
    },
    {
      label: '待支付',
      value: '1',
      color: '#FAAD14',
    },
    {
      label: '已支付',
      value: '10',
      color: '#00C8BE',
    },
    {
      label: '已关闭',
      value: '11',
      color: '#FF4A57',
    },
  ],
  orderTypeOptions: [
    {
      label: '设备销售',
      value: '1',
    },
  ],
  // 客户类型
  customerTypeOtions: [
    {
      label: '个人',
      value: '3',
    },
    {
      label: '企业',
      value: '2',
    },
    {
      label: '企业【租赁商】',
      value: '1',
    },
  ],
  // 支付方式
  payWaysOptions: [
    {
      label: '预付款',
      value: '1',
    },
  ],
};

const { spuDataListloadData, handleSpuDataList } = useCategory();

// 搜索表单组件配置
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'rsp_order_id',
    originProps: { label: '账单编号', name: 'rsp_order_id' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'rs_order_id',
    originProps: { label: '订单编号', name: 'rs_order_id' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请输入',
    },
    fragmentKey: 'renderInput',
  },
  {
    key: 'order_pay_status',
    originProps: { label: '账单状态', name: 'order_pay_status' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入', showArrow: true },
    fragmentKey: 'renderSelect',
    options: searchOptions.orderStatusOptions,
  },
  {
    key: 'source_type',
    originProps: { label: '账单类型', name: 'source_type' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请输入',
      showArrow: true,
      mode: 'multiple',
      showSearch: false,
    },
    fragmentKey: 'renderSelect',
    options: searchOptions.orderTypeOptions,
  },
  {
    key: 'payment_flow_sn',
    originProps: { label: '支付流水号', name: 'payment_flow_sn' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'category',
    originProps: { label: '品类', name: 'category' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请选择',
      changeOnSelect: true,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      loadData: () => {},
    },
    url: '/warehouse/SpuDataList',
    hostType: 'Golang',
    fragmentKey: 'renderCascader',
    optionsFormat: res => {
      return handleSpuDataList(res.data.category, 1);
    },
    changeHandler: ({ selectedOptions, value, options }) => {
      spuDataListloadData(selectedOptions, value, options);
    },
  },
  {
    key: 'pay_type',
    originProps: { label: '支付方式', name: 'pay_type' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderSelect',
    options: searchOptions.payWaysOptions,
  },
  {
    key: 'custom_type',
    originProps: { label: '客户类型', name: 'custom_type' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请输入',
      showArrow: true,
      mode: 'multiple',
      showSearch: false,
    },
    fragmentKey: 'renderSelect',
    options: searchOptions.customerTypeOtions,
  },
  {
    key: 'server_id',
    originProps: { label: '客户ID/名称', name: 'server_id' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'create_time',
    originProps: { label: '创建时间', name: 'create_time' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      format: 'YYYY-MM-DD HH:mm',
      showCount: true,
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm',
    },
    fragmentKey: 'renderRangePicker',
  },
];

// 表格列
export const saleBillColumns: TableColumnType<ISaleBillList>[] = [
  {
    title: '账单编号',
    key: 'rsp_order_id',
    dataIndex: 'rsp_order_id',
    width: 200,
  },
  {
    title: '订单编号',
    key: 'rs_order_id',
    dataIndex: 'rs_order_id',
    width: 180,
  },
  {
    title: '账单类型',
    key: 'source_type',
    dataIndex: 'source_type',
    width: 100,
    customRender: ({ value }: { value: string }) => {
      return findLabel('orderTypeOptions', value);
    },
  },
  {
    title: '品类',
    key: 'category_name',
    dataIndex: 'category_name',
    width: 150,
    customRender: ({ record }) => {
      return `${record.brand_name}-${record.model_name}`;
    },
  },
  {
    title: '客户类型',
    key: 'custom_type',
    dataIndex: 'custom_type',
    width: 130,
    customRender: ({ value }: { value: string }) => {
      return findLabel('customerTypeOtions', value);
    },
  },
  {
    title: '客户ID/名称',
    key: 'server_id',
    dataIndex: 'server_id',
    width: 180,
  },
  {
    title: '账单金额',
    key: 'order_price',
    dataIndex: 'order_price',
    width: 100,
  },
  {
    title: '已核销金额',
    key: 'finish_pay_price',
    dataIndex: 'finish_pay_price',
    width: 120,
  },
  {
    title: '未核销金额',
    key: 'wait_pay_price',
    dataIndex: 'wait_pay_price',
    width: 120,
  },

  {
    title: '支付方式',
    key: 'pay_type_text',
    dataIndex: 'pay_type_text',
    width: 90,
  },
  {
    title: '支付流水号',
    key: 'payment_flow_sn',
    dataIndex: 'payment_flow_sn',
    width: 200,
  },
  {
    title: '账单状态',
    key: 'order_pay_status',
    dataIndex: 'order_pay_status',
    width: 100,
    customRender: ({ value }: { value: number }) => {
      return TableStatus({ status: value, options: searchOptions.orderStatusOptions });
    },
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
    width: 340,
  },
];

/**
 *
 * @param type keyof ISearOptions
 * @param value  value
 * @returns {string}
 */
export function findLabel(type: keyof ISearOptions, value: string) {
  return searchOptions[type].find(item => item.value === value)?.label ?? '';
}
