<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    title="账单备注"
    :visible="visible"
    width="500px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item
        label="备注"
        name="remark"
        :rules="[{ required: true, message: '请输入备注!' }]"
      >
        <a-textarea
          v-model:value="formState.remark"
          placeholder="请输入备注"
          :rows="5"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message, FormInstance } from 'ant-design-vue';
import { createRemark } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const submitLoading = ref(false);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  remark: '',
});

const onCancel = () => emits('update:visible', false);

const onOk = () => {
  formRef.value?.validate().then(() => {
    submitLoading.value = true;
    createRemark(props.id, formState.value.remark)
      .then(() => {
        onCancel();
        message.success('备注成功');
        emits('onLoadList');
      })
      .finally(() => (submitLoading.value = false));
  });
};
</script>
