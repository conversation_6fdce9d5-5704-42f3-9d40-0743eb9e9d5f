import type { RTableColumnType } from 'rrz-web-design';
import { RButtonType } from 'rrz-web-design';

import { FormGroupItem } from '@/components/form-create/src/typing';

import { getCategoryList } from './service';

export const columns: RTableColumnType[] = [
  {
    dataIndex: 'index',
    title: '序号',
    customRender: ({ index }) => {
      return `${index + 1} `;
    },
    width: 100,
    hideInSearch: true,
  },
  {
    dataIndex: 'title',
    title: '文章标题',
    ellipsis: true,
    width: 200,
  },
  {
    dataIndex: 'digest',
    title: '文章摘要',
    ellipsis: true,
    width: 200,
  },
  {
    dataIndex: 'cate_name',
    title: '类目',
    valueType: 'select',
    formFieldProps: {
      setOption: async () => {
        const { data } = await getCategoryList();
        return data || [];
      },
      elProps: {
        'field-names': {
          label: 'name',
          value: 'id',
        },
      },
    },
  },
  {
    dataIndex: 'author',
    title: '作者',
    hiedInTable: true,
  },
  {
    dataIndex: 'operation',
    title: '操作',
    fixed: 'right',
    width: 144,
    btnGroup: [
      {
        name: '预览',
        key: 'handleView',
        type: RButtonType.Primary,
      },
      {
        name: '编辑',
        key: 'handleUpdate',
        type: RButtonType.Primary,
      },
      {
        name: '查看页面',
        key: 'handleLink',
        type: RButtonType.Primary,
      },
      {
        name: '删除',
        key: 'handleDelete',
        type: RButtonType.Primary,
      },
    ],
  },
];

export const articleFormGroup: FormGroupItem[] = [
  {
    key: 'title',
    fragmentKey: 'renderInput',
    originProps: {
      label: '标题',
      name: 'title',
      rules: [{ required: true, message: '标题不能为空' }],
    },
    elProps: { placeholder: '请输入', showCount: true, maxLength: 30 },
  },
  {
    key: 'cate',
    originProps: { label: '类目', name: 'cate', rules: [{ required: true, message: '类目不能为空' }] },
    url: '/shop/news/create',
    optionsFormat: res => res.data.map((item: any) => ({ label: item.name, value: item.id })),
    elProps: { placeholder: '请选择文章类目', showCount: true, maxLength: 30 },
    fragmentKey: 'renderSelect',
  },
  {
    key: 'digest',
    fragmentKey: 'renderInput',
    originProps: {
      label: '摘要',
      name: 'digest',
      rules: [{ required: true, message: '摘要不能为空' }],
    },
    elProps: { placeholder: '请输入', showCount: true, maxLength: 30 },
  },
  {
    key: 'author',
    fragmentKey: 'renderInput',
    originProps: {
      label: '作者',
      name: 'author',
      rules: [{ required: true, message: '作者不能为空' }],
    },
    elProps: { placeholder: '请输入', showCount: true, maxLength: 30 },
  },
  {
    key: 'cover',
    fragmentKey: 'renderInput',
  },
  {
    key: 'content',
    fragmentKey: 'renderInput',
  },
];
