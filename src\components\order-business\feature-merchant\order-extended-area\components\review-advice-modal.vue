<template>
  <a-modal
    v-model:visible="bindVisible"
    :footer="null"
    title="风控复审建议"
  >
    <div
      v-for="(item, index) in reviewAdviceList"
      :key="item.id"
      class="advice-item"
    >
      <div class="top">
        <span>【{{ indexMap[index] || index + 1 }}次复审】</span>
        <span>{{ item.advice }}</span>
      </div>
      <div class="separator" />
      <div>
        <span>常规风控巡检：</span>
        <span>{{ item.created_at_format }}</span>
      </div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { useVModel } from '@/hook/index';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  reviewAdviceList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

/** 抽屉控制 */
const bindVisible = useVModel(props, 'visible', emit);

const indexMap = {
  0: '一',
  1: '二',
  2: '三',
  3: '四',
  4: '五',
  5: '六',
  6: '七',
  7: '八',
  8: '九',
  9: '十',
  10: '十一',
  11: '十二',
  12: '十三',
  13: '十四',
  14: '十五',
  15: '十六',
  16: '十七',
};
</script>
<style lang="less" scoped>
.advice-item {
  padding: 16px;
  background-color: #f6f7fa;
  border-radius: 4px;
  .separator {
    width: 100%;
    height: 1px;
    margin: 8px 0;
    background-color: #d7d7d7;
  }
  &:not(:last-child) {
    margin-bottom: 16px;
  }
}
</style>
