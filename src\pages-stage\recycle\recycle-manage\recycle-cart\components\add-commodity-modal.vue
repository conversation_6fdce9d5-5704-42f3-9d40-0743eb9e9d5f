<template>
  <a-modal
    v-model:visible="visible"
    :body-style="{ 'padding-top': '16px' }"
    destroy-on-close
    :mask-closable="false"
    :title="enterType === 'add' ? '添加回收商品' : '编辑回收商品'"
    :width="480"
    @cancel="close"
    @ok="confirm"
  >
    <a-alert
      v-if="!no_replace_device_max"
      class="alert-panel"
      message="信息填写完整后，系统将自动匹配预估回收价格。"
      show-icon
      type="info"
    />
    <a-alert
      v-else
      :message="dateEndMessage"
      show-icon
      style="margin-bottom: 24px"
      type="error"
    >
      <template #icon>
        <exclamation-circle-outlined />
      </template>
    </a-alert>
    <AddCommodityFormItem label="基础信息">
      <a-form
        ref="baseFormRef"
        :label-col="{ style: { width: '64px' } }"
        :model="baseFormState"
      >
        <a-form-item
          label="IMEI码"
          name="imei"
          :rules="IMEIRULE"
        >
          <a-input-number
            v-model:value="baseFormState.imei"
            :controls="false"
            :maxlength="15"
            placeholder="请输入"
            :precision="0"
            style="width: 370px"
            @change="imeiChange"
          />
        </a-form-item>
        <!-- 自动填选代发货设备或非代发货设备 -->
        <a-form-item
          v-if="!!argumentFormState.is_replace_device"
          label="设备类型"
          name="is_replace_device"
        >
          <a-select
            v-model:value="argumentFormState.is_replace_device"
            disabled
            :options="[
              { label: '代发设备', value: 1 },
              { label: '非代发设备', value: 2 },
            ]"
            :show-arrow="false"
          />
        </a-form-item>
      </a-form>
    </AddCommodityFormItem>
    <AddCommodityFormItem label="关联信息">
      <a-form
        ref="argumentFormRef"
        :label-col="{ style: { width: '64px' } }"
        :model="argumentFormState"
      >
        <a-form-item
          label="型号"
          name="model"
          :rules="[{ required: true, message: '未选择型号，请选择' }]"
        >
          <a-select
            v-model:value="argumentFormState.model"
            :disabled="enterType === 'add' && isFirstEnter"
            :filter-option="filterOption"
            :options="modelOptions"
            placeholder="请选择"
            show-search
            @change="
              () => {
                clearKeyValue(['memory', 'color', 'version', 'condition']);
                price = 0;
                getPrice();
              }
            "
          />
        </a-form-item>
        <a-form-item
          label="内存"
          name="memory"
          :rules="[{ required: true, message: '未选择内存，请选择' }]"
        >
          <a-select
            v-model:value="argumentFormState.memory"
            :disabled="enterType === 'add' && isFirstEnter"
            :options="memoryOptions"
            placeholder="请选择"
            @change="
              () => {
                clearKeyValue(['color', 'version', 'condition']);
                price = 0;
                getPrice();
              }
            "
          />
        </a-form-item>
        <a-form-item
          label="颜色"
          name="color"
          :rules="[{ required: true, message: '未选择颜色，请选择' }]"
        >
          <a-select
            v-model:value="argumentFormState.color"
            :disabled="enterType === 'add' && isFirstEnter"
            :options="colorOptions"
            placeholder="请选择"
            @change="getPrice"
          />
        </a-form-item>
        <a-form-item
          label="版本"
          name="version"
          :rules="[{ required: true, message: '未选择版本，请选择' }]"
        >
          <a-select
            v-model:value="argumentFormState.version"
            :disabled="enterType === 'add' && isFirstEnter"
            :options="versionOptions"
            placeholder="请选择"
            @change="getPrice"
          />
        </a-form-item>
        <a-form-item
          label="成新度"
          name="condition"
          :rules="[{ required: true, message: '未选择成新度，请选择' }]"
        >
          <a-select
            v-model:value="argumentFormState.condition"
            :disabled="enterType === 'add' && isFirstEnter"
            :options="conditionOptions"
            placeholder="请选择"
            @change="getPrice"
          />
        </a-form-item>
      </a-form>
    </AddCommodityFormItem>
    <div class="price-block">
      <span class="price-block__label">预估回收价：</span>
      <div class="price-block-num-wrap">
        <span class="price-block__num">{{ price ? `￥${price}` : '' }}</span>
        <span
          v-if="subsidyPrice"
          class="price-text"
        >补贴价：{{ subsidyPrice ? `￥${subsidyPrice}` : '' }}</span>
        <span
          v-if="subsidyPrice"
          class="price-text"
        >估价金额：{{ basePrice ? `￥${basePrice}` : '' }}</span>
      </div>
    </div>
    <template #footer>
      <a-button
        v-if="enterType === 'add'"
        :disabled="!price || isNaN(price)"
        :loading="loading"
        @click="onSubmit()"
      >
        完成添加
      </a-button>
      <a-button
        v-else
        :disabled="!price || isNaN(price)"
        :loading="loading"
        @click="onSave"
      >
        完成编辑
      </a-button>
      <a-button
        :disabled="loading || !price || isNaN(price)"
        type="primary"
        @click="enterType === 'add' ? onSubmit('next') : onSave('next')"
      >
        添加下一台
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useModal } from '@/hook/component/use-modal';
import { IDataInfo } from '../data';
import AddCommodityFormItem from './add-commodity-form-item.vue';
import { useAddCommondityHooks } from '../composables/use-add-commodity-hooks';
import { addShoppingCar, modifyItem } from '../service';

const emits = defineEmits(['init']);

const baseFormRef = ref<any>(null);
const argumentFormRef = ref<any>(null);

const baseRequestFields = ['carId'];
// 编辑时，部分参数需要提交时作为固定参数；
const baseRequestParams = {};

const {
  enterType,
  baseFormState,
  argumentFormState,
  price,
  subsidyPrice,
  basePrice,
  isFirstEnter,

  modelOptions,
  memoryOptions,
  colorOptions,
  versionOptions,
  conditionOptions,

  getFilterItemOptions,
  clearKeyValue,
  imeiChange,
  getPrice,
  filterOption,

  no_replace_device_max,
  dateEndMessage,
} = useAddCommondityHooks();

const IMEIRULE = [
  {
    required: true,
    validator: (_rule: any, val: any) => {
      if (!val) return Promise.reject('未输入IMEI码，请输入');
      if (String(val).length < 15) return Promise.reject('IMEI码需输入15位');
      return Promise.resolve();
    },
  },
];

const validator = (cb: any) => {
  Promise.all([baseFormRef.value.validate(), argumentFormRef.value.validate()]).then(() => {
    cb();
  });
};

const next = (params: any) => {
  return () => {
    const { argument, requestFn, type } = params;
    requestFn(argument).then(() => {
      close();
      emits('init');
      if (type === 'next') {
        onShow('add');
      }
    });
  };
};

const onSave = (type = 'submit') => {
  if (!price.value || isNaN(price.value)) return;
  const params = {
    ...baseFormState.value,
    ...argumentFormState.value,
    ...baseRequestParams,
  };
  const fn = next({
    argument: params,
    requestFn: modifyItem,
    type,
  });
  validator(fn);
};

const onSubmit = (type = 'submit') => {
  if (!price.value || isNaN(price.value)) return;
  const params = {
    ...baseFormState.value,
    ...argumentFormState.value,
  };
  const fn = next({
    argument: params,
    requestFn: addShoppingCar,
    type,
  });
  validator(fn);
};

const { visible, loading, open, close, confirm } = useModal(
  () => {
    return;
  },
  {
    afterClose: () => {
      baseFormState.value.imei = null;
      const keys = Object.keys(argumentFormState.value);
      keys.forEach((key: string) => {
        argumentFormState.value[key] = null;
      });
      price.value = 0;
      basePrice.value = 0;
      subsidyPrice.value = 0;
      isFirstEnter.value = true;
      no_replace_device_max.value = false;
    },
  },
);
const onShow = (type: 'add' | 'detail', item?: IDataInfo) => {
  enterType.value = type;
  if (item) {
    baseRequestFields.forEach((field: string) => {
      baseRequestParams[field] = item[field];
    });
    getFilterItemOptions({ imei: item.imei }, () => {
      baseFormState.value.imei = item.imei;
      const keysArr = Object.keys(argumentFormState.value);
      keysArr.forEach((key: string) => {
        let value = item[key];
        argumentFormState.value[key] = value || null;
      });
      getPrice();
    });
  }
  open();
};

defineExpose({
  onShow,
});
</script>

<style lang="less" scoped>
.alert-panel {
  margin-bottom: 24px;
  background: #f0f7ff !important;
  border: none !important;
  :deep(.ant-alert-message) {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
.price-block {
  display: flex;
  align-items: flex-start;

  justify-content: space-between;
  padding-top: 23px;
  color: #3777ff;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  border-top: 1px solid rgba(6, 21, 51, 0.06);
  &__num {
    font-size: 20px;
    line-height: 28px;
  }
}
.price-block-num-wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
}
.price-text {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
</style>
