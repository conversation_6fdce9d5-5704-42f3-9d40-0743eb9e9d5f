<template>
  <div class="order">
    <div class="proCode">
      {{ data.pro_code }}
    </div>
    <div class="barcode-area">
      <div class="print-info">
        <span>ZJ</span>
        <span class="print-date">{{ printDate.date }}</span>
        <span class="print-time">{{ printDate.time }}</span>
      </div>
      <div class="barcode">
        <img :src="data.barcode_base64">
      </div>
      <!-- 非子母单 -->
      <div class="waybillno-info flex-center">
        <div class="no">
          <span>运单号：</span>
          <span>{{ data.waybill_no }}</span>
        </div>
      </div>
    </div>
    <div class="order-body">
      <!-- 第一联 -->
      <div class="lian-1">
        <div class="received-area">
          <div class="route">
            {{ data.dest_route_label }}
          </div>
          <div class="info">
            <img
              class="received-icon"
              src="https://img1.rrzuji.cn/uploads/scheme/2202/16/o/gqPP4aqXimOvYif4V1Tl.png"
            >
            <div class="receiver-address">
              <div>
                <span>{{ data.receiveInfo.name }}</span>
                <span>{{ data.receiveInfo.tel }}</span>
                <span>{{ data.receiveInfo.company }}</span>
              </div>
              <div>
                {{
                  data.receiveInfo.county + data.receiveInfo.province + data.receiveInfo.city + data.receiveInfo.detail
                }}
              </div>
              <div class="destTeam-code">
                {{ data.dest_team_code }}
              </div>
            </div>
          </div>
        </div>
        <div class="qrcode-area">
          <div class="left">
            <div class="paymethod">
              <span>{{ data.payment_method }}</span>
            </div>
            <div
              class="codingMapping"
              :style="{
                fontSize: data.coding_mapping && data.coding_mapping.length > 4 ? '22pt' : '',
              }"
            >
              {{ data.coding_mapping }}
            </div>
          </div>
          <div class="qrcode">
            <img :src="data.qrcode_base64">
          </div>
          <div class="yanshi">
            已验视
          </div>
          <div class="right">
            <div class="proName">
              <div>{{ data.pro_name }}</div>
            </div>
            <div class="codingMappingOut">
              {{ data.coding_mapping_out }}
            </div>
          </div>
        </div>
      </div>
      <!-- 第二联 -->
      <div class="lian-2">
        <div class="send-area">
          <img
            class="send-icon"
            src="https://img1.rrzuji.cn/uploads/scheme/2202/16/o/jv0IOz5e4u5oeg6gQzJi.png"
          >
          <div class="info">
            <span>{{ data.senderInfo.name }}</span>
            <span>{{ data.senderInfo.tel }}</span>
            <span>{{ data.senderInfo.company }}</span>
            <span>&nbsp;{{
              data.senderInfo.county + data.senderInfo.province + data.senderInfo.city + data.senderInfo.detail
            }}</span>
          </div>
        </div>
        <div class="cargo-addserviec-area">
          <div class="left">
            <div
              v-if="data.cargo_name"
              class="cargo"
            >
              托寄物：{{ data.cargo_name }}
            </div>
          </div>
          <div class="right">
            <div class="addserviec">
              增值服务：{{ data.value_added_str }}
            </div>
            <div class="other">
              <div class="weight">
                重量：{{ data.weight_qty }}kg
              </div>
              <div class="price">
                费用合计：{{ data.total }} 元
              </div>
              <div class="payMethod2">
                付款方式：{{ data.payment_method }}
              </div>
            </div>
          </div>
        </div>
        <div class="custom-abflag-area">
          <div class="custom">
            <div class="remark">
              备注：
            </div>
          </div>
          <div class="abflag">
            <div class="imageContent">
              <img
                v-for="(url, index) in iconList"
                :key="index"
                class="PIimg"
                :src="url"
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 存根联 -->
    <div class="stub-body">
      <div class="logo-barcode-area">
        <div class="left">
          <img
            class="logo-icon"
            src="https://img1.rrzuji.cn/uploads/scheme/2202/16/o/9qxt0MNzQ8MRRNeoOOCv.jpg"
            style="height: 22px"
          >
          <img
            src="https://img1.rrzuji.cn/uploads/scheme/2202/16/o/vTY4NwLyOuiJEWd9U0oH.png"
            style="height: 23px"
          >
        </div>
        <div class="center">
          <img
            class="barcode"
            :src="data.barcode_base64"
          >
          <span>{{ data.waybill_no }}</span>
        </div>
        <div class="right">
          {{ data.pro_code }}
        </div>
      </div>
      <div class="sender-area">
        <img
          class="sender-icon"
          src="https://img1.rrzuji.cn/uploads/scheme/2202/16/m/jv0IOz5e4u5oeg6gQzJi.png"
        >
        <div class="sender-address">
          <span>{{ data.senderInfo.name }}</span>
          <span>{{ data.senderInfo.tel }}</span>
          <span>{{ data.senderInfo.company }}</span>
          <span>&nbsp;&nbsp;{{
            data.senderInfo.county + data.senderInfo.province + data.senderInfo.city + data.senderInfo.detail
          }}</span>
        </div>
      </div>
      <div class="recevier-area">
        <img
          class="receiver-icon"
          src="https://img1.rrzuji.cn/uploads/scheme/2202/16/m/gqPP4aqXimOvYif4V1Tl.png"
        >
        <div class="receiver-address">
          <span>{{ data.receiveInfo.name }}</span>
          <span>{{ data.receiveInfo.tel }}</span>
          <span>{{ data.receiveInfo.company }}</span>
          <span>&nbsp;&nbsp;{{
            data.receiveInfo.county + data.receiveInfo.province + data.receiveInfo.city + data.receiveInfo.detail
          }}</span>
        </div>
      </div>
      <div class="cargo-addservice-area">
        <div class="left">
          <div
            v-if="data.cargo_name"
            class="cargo-name"
          >
            托寄物：{{ data.cargo_name }}
          </div>
        </div>
        <div class="right">
          <div class="weight">
            重量：{{ data.weight_qty }}kg
          </div>
          <div class="price">
            费用合计：{{ data.total }} 元
          </div>
        </div>
      </div>
      <div class="paymethod-remark-area">
        <div class="left">
          <div class="label">
            <template v-if="data.payment_method?.length > 4">
              {{ data.payment_method }}
            </template>
            <template v-else>
              付款方式：{{ data.payment_method }}
            </template>
          </div>
        </div>
        <div class="right">
          <div>备注：</div>
        </div>
      </div>
      <div class="other-area">
        <div class="other-area-item">
          <div>件数：{{ data.item_num }}件</div>
        </div>
        <div class="other-area-item">
          <div>重量：{{ data.weight_qty }}kg</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { computed,defineComponent } from 'vue';

export default defineComponent({
  props: {
    data: {
      type: Object,
      required: true,
    },
    printDate: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const iconConfig = {
      1: 'https://img1.rrzuji.cn/uploads/scheme/2202/17/m/u6f6PQZ6tanCdl0ka0Td.jpg', //重货
      2: 'https://img1.rrzuji.cn/uploads/scheme/2202/17/m/bGldUZuhfh7LngCTCbmT.jpg', //蟹类
      3: 'https://img1.rrzuji.cn/uploads/scheme/2202/17/m/gro43kVcfSQRTdhneFxb.jpg', //海鲜
      4: 'https://img1.rrzuji.cn/uploads/scheme/2202/17/m/6i0YlcowLWw9hZMmydSx.jpg', //易碎品
      5: 'https://img1.rrzuji.cn/uploads/scheme/2202/17/m/tg4Fito2ae88nibzlLLk.jpg', //医药
      7: 'https://img1.rrzuji.cn/uploads/scheme/2202/17/m/6i0YlcowLWw9hZMmydSx.jpg', //酒类（易碎品）
    };
    /**图标 */
    const iconList = computed(() => {
      let ret: string[] = [];
      //printIcon
      const iconStr = props.data.print_icon;
      if (iconStr && iconStr.length) {
        const data: Array<string> = iconStr.split('');
        data.forEach((value, index) => {
          const url = iconConfig[index + 1];
          //打印规则：值为1显示
          if (value == '1' && url && !ret.includes(url)) {
            ret.push(url);
          }
        });
      }
      //abFlag值
      switch (props.data.ab_flag) {
        case 'A':
        case 'A1':
          ret.unshift('https://img1.rrzuji.cn/uploads/scheme/2203/25/m/OuoeWFdsucT7thZV4GHO.jpg');
          break;
        case 'B':
          ret.unshift('https://img1.rrzuji.cn/uploads/scheme/2203/25/m/1doAV5HcBXIluhvwyVxe.jpg');
          break;
      }
      //最多显示2个图标
      return ret.slice(0, 2);
    });
    return { iconList };
  },
});
</script>
