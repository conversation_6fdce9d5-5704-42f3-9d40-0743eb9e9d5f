<template>
  <div
    :id="bmapInfo.callbackKey"
    :style="containerStyle"
  />
</template>

<script setup lang="ts">
import { computed, CSSProperties, reactive } from 'vue';
import { customAlphabet } from 'nanoid';

import eventBus from '@/utils/evnet-bus';

interface IBMapPoint {
  x: number;
  y: number;
}

const props = defineProps<{
  width: number | string;
  height: number | string;
  center?: IBMapPoint;
  zoom?: number;
}>();

const containerStyle = computed<CSSProperties>(() => {
  const width = typeof props.width === 'string' ? props.width : `${width}px`;
  const height = typeof props.height === 'string' ? props.height : `${height}px`;

  return { width, height };
});

const ALPHANUMERIC = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
const customNanoid = customAlphabet(ALPHANUMERIC, 21);
const bmapInfo = reactive({
  callbackKey: `key${customNanoid()}`,
  mapInstance: null,
});

/**
 * 百度地图初始化流程
 * 判断是否已经初始化 Window.BMapGL 有值/空值
 * 空值的情况下进入初始化流程
 * 1、动态插入百度地图SDK（script）
 * 2、同时注册回调函数，通过回调函数来监测是否完成初始化
 * 3、返回初始化完成状态
 */
function loadBMapScript(key?: string) {
  return new Promise(resolve => {
    if (window.BMapGL) {
      return resolve(true);
    }
    const ak = key || 'oeJiJHkOjkRnQXVMSxWPUvS8JIW7vSG3';
    const mapSDK = `https://api.map.baidu.com/api?v=1.0&type=webgl&ak=${ak}&callback=${bmapInfo.callbackKey}`;
    loadScript(mapSDK);
    eventBus.on(`${bmapInfo.callbackKey}event`, () => {
      resolve(true);
    });
  });
}

function loadBMapCallback() {
  eventBus.emit(`${bmapInfo.callbackKey}event`);
}

window[bmapInfo.callbackKey] = loadBMapCallback;

function initBMapContainer() {
  const map = new BMapGL.Map(bmapInfo.callbackKey);
  if (props.center) {
    map.centerAndZoom(new BMapGL.Point(props.center.x, props.center.y), props.zoom || 13); // 初始中心点
  }
  map.enableScrollWheelZoom(true);
  bmapInfo.mapInstance = map;
  return map;
}

// 内置动作。根据坐标数组绘制轨迹
function drawTrackByPoints(points: Array<IBMapPoint>, options: { xKey?: string; yKey?: string } = {}) {
  // 轨迹坐标点（示例数据）
  const pointsInstance = points.map(p => new BMapGL.Point(p[options.xKey || 'x'], p[options.yKey || 'y']));
  // 创建折线（轨迹）
  const polyline = new BMapGL.Polyline(pointsInstance, {
    strokeColor: options.strokeColor || '#00a1ff', // 轨迹颜色
    strokeWeight: options.strokeWeight || 5, // 轨迹宽度
    strokeOpacity: options.strokeOpacity || 0.8, // 轨迹透明度
  });

  bmapInfo.mapInstance.addOverlay(polyline);

  // 自动调整视野显示完整轨迹
  bmapInfo.mapInstance.setViewport(pointsInstance);
}

defineExpose({
  loadBMapScript,
  initBMapContainer,
  drawTrackByPoints,
});

// 工具方法，动态加载外部JavaScript脚本
function loadScript(src: string) {
  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = src;
  const head = document.querySelector('head');
  if (head) {
    head.appendChild(script);
  }
}
</script>
