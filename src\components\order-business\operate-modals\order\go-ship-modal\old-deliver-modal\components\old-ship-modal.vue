<template>
  <!-- 商家后台整合取件发货服务，拆分快递方式 -->
  <NewModal
    v-if="!isSuper"
    v-model:visible="bindVisible"
    :data="deliveryStoreData"
    :extra-data="extraData"
    :order-id="orderId"
    @refresh="(...args) => emits('refresh', ...args)"
    @show-exchange-apply-modal="(...args) => emits('showExchangeApplyModal', ...args)"
  />
  <!-- 运营后台沿用旧版 -->
  <OldModal
    v-else
    v-model:visible="bindVisible"
    :extra-data="extraData"
    :order-id="orderId"
    @refresh="(...args) => emits('refresh', ...args)"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

import { IDeliverStore } from '../../data';
import NewModal from './new-modal.vue';
import OldModal from './old-modal.vue';

const route = useRoute();
const isSuper = route.query.role === 'super';

const emits = defineEmits(['update:visible', 'refresh', 'showExchangeApplyModal']);
const props = defineProps<{
  visible: boolean;
  extraData: any;
  orderId: string;
  deliveryStoreData: IDeliverStore;
}>();

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(val) {
    emits('update:visible', val);
  },
});
</script>
