<template>
  <layout-admin-page
    title="规则配置"
    top-fixed
  >
    <template #title-prefix>
      <ArrowLeftOutlined
        style="margin-right: 8px"
        @click="$router.go(-1)"
      />
    </template>
    <template #title-suffix>
      <a-radio-group
        v-model:value="ruleType"
        style="margin-left: 16px"
      >
        <a-radio-button value="rulePreview">
          预览版
        </a-radio-button>
        <a-radio-button value="ruleValue">
          线上版
        </a-radio-button>
      </a-radio-group>
    </template>
    <template
      v-if="ruleType === 'rulePreview'"
      #extra
    >
      <a-space>
        <a-button @click="resetRuleConfig">
          重置
        </a-button>
        <a-button
          type="primary"
          @click="saveData('formal')"
        >
          立即生效
        </a-button>
      </a-space>
    </template>
    <div class="rule-detail">
      <div class="rule-item">
        <div class="rule-item-title">
          绑定渠道
        </div>
        <div>
          <a-table
            :columns="conditionCol"
            :data-source="conditionData"
            :pagination="false"
          />
        </div>
      </div>
      <div class="rule-item">
        <div class="rule-item-title">
          型号规则
        </div>
        <div>
          <a-table
            :columns="modelCol"
            :data-source="modelList[ruleType]"
            :pagination="false"
            :scroll="{ y: 300 }"
          >
            <template #bodyCell="{ record, column, index }">
              <template v-if="column.dataIndex === 'title'">
                <a-space wrap>
                  <a-tag
                    v-for="(item, tagIdx) in record.list"
                    :key="item.id"
                    :closable="item.id !== 99999 && ruleType === 'rulePreview'"
                    style="margin-right: 0"
                    @close.prevent="deleteModel(record.list, tagIdx, index, modelList)"
                  >
                    {{ item.title }}
                  </a-tag>
                  <a-tag
                    v-if="ruleType === 'rulePreview'"
                    :key="`model_add_tag_${index}`"
                    class="add-tag"
                    @click="openAddModelModal(record.list, modelLevel, modelType)"
                  >
                    <PlusOutlined />
                    添加型号
                  </a-tag>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'deposit_ratio'">
                <template v-if="ruleType === 'ruleValue'">
                  {{ record.deposit_ratio }}%
                </template>
                <a-input-number
                  v-else
                  v-model:value="record.deposit_ratio"
                  class="ratio-input"
                  :controls="false"
                  :formatter="(value:number) => `${value}%`"
                  :max="100"
                  :min="1"
                  :parser="(value: string) => value.replace('%', '')"
                  :precision="2"
                  @blur="saveData()"
                />
              </template>
            </template>

            <template
              v-if="ruleType === 'rulePreview'"
              #summary
            >
              <a-table-summary fixed="bottom">
                <a-table-summary-row>
                  <a-table-summary-cell :col-span="2">
                    <a-button
                      block
                      type="dashed"
                      @click="addModelItem"
                    >
                      <PlusOutlined />
                      添加型号押金比例
                    </a-button>
                  </a-table-summary-cell>
                </a-table-summary-row>
              </a-table-summary>
            </template>
          </a-table>
        </div>
      </div>
      <div class="rule-item">
        <div class="rule-item-title">
          类目规则
        </div>
        <div>
          <a-table
            :columns="categoryCol"
            :data-source="categoryList[ruleType]"
            :pagination="false"
          >
            <template #bodyCell="{ record, column, index }">
              <template v-if="column.dataIndex === 'title'">
                <template
                  v-for="(item, tagIdx) in record.list"
                  :key="item.id"
                >
                  <a-tag
                    v-if="item.id !== 99999"
                    :closable="ruleType === 'rulePreview'"
                    @close.prevent="deleteModel(record.list, tagIdx, index, categoryList)"
                  >
                    {{ item.title }}
                  </a-tag>
                  <template v-else>
                    兜底规则
                  </template>
                </template>

                <a-tag
                  v-if="ruleType === 'rulePreview' && record.list[0]?.id !== 99999"
                  :key="`categoryType_add_tag_${index}`"
                  class="add-tag"
                  @click="openAddModelModal(record.list, categoryLevel, categoryType)"
                >
                  <PlusOutlined />
                  添加类目
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'deposit_ratio'">
                <template v-if="ruleType === 'ruleValue'">
                  {{ record.deposit_ratio }}%
                </template>
                <a-input-number
                  v-else
                  v-model:value="record.deposit_ratio"
                  class="ratio-input"
                  :controls="false"
                  :formatter="(value:number) => `${value}%`"
                  :max="100"
                  :min="1"
                  :parser="(value: string) => value.replace('%', '')"
                  :precision="2"
                  @blur="saveData()"
                />
              </template>
            </template>

            <template
              v-if="ruleType === 'rulePreview'"
              #summary
            >
              <a-table-summary fixed="bottom">
                <a-table-summary-row>
                  <a-table-summary-cell :col-span="2">
                    <a-button
                      block
                      type="dashed"
                      @click="addCategoryItem"
                    >
                      <PlusOutlined />
                      添加类目押金比例
                    </a-button>
                  </a-table-summary-cell>
                </a-table-summary-row>
              </a-table-summary>
            </template>
          </a-table>
        </div>
      </div>

      <div class="rule-item">
        <div class="rule-item-title">
          租赁方案规则
        </div>
        <div>
          <a-table
            :columns="rentalRuleCol"
            :data-source="rentalRuleList[ruleType]"
            :pagination="false"
          >
            <template #bodyCell="{ record, column, index }">
              <template v-if="column.dataIndex === 'title'">
                <template
                  v-for="(item, tagIdx) in record.list"
                  :key="item.id"
                >
                  <a-tag
                    v-if="item.id !== 99999"
                    :key="item.id"
                    :closable="ruleType === 'rulePreview'"
                    @close.prevent="deleteModel(record.list, tagIdx, index, rentalRuleList)"
                  >
                    {{ item.title }}
                  </a-tag>
                  <template v-else>
                    租赁方案
                  </template>
                </template>

                <a-tag
                  v-if="ruleType === 'rulePreview' && record.list[0]?.id !== 99999"
                  :key="`rentalRule_add_tag_${index}`"
                  class="add-tag"
                  @click="handleOpenRentalRuleModal(record.list)"
                >
                  <PlusOutlined />
                  添加租赁方案
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'deposit_ratio'">
                <template v-if="ruleType === 'ruleValue'">
                  {{ record.deposit_ratio }}%
                </template>
                <a-input-number
                  v-else
                  v-model:value="record.deposit_ratio"
                  class="ratio-input"
                  :controls="false"
                  :formatter="(value:number) => `${value}%`"
                  :max="100"
                  :min="1"
                  :parser="(value: string) => value.replace('%', '')"
                  :precision="2"
                  @blur="saveData()"
                />
              </template>
            </template>

            <template
              v-if="ruleType === 'rulePreview'"
              #summary
            >
              <a-table-summary fixed="bottom">
                <a-table-summary-row>
                  <a-table-summary-cell :col-span="2">
                    <a-button
                      block
                      type="dashed"
                      @click="handleAddRentalRuleItem"
                    >
                      <PlusOutlined />
                      添加租赁方案
                    </a-button>
                  </a-table-summary-cell>
                </a-table-summary-row>
              </a-table-summary>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </layout-admin-page>
  <a-modal
    v-model:visible="addModelVisible"
    :confirm-loading="addModelLoading"
    :title="cascaderModalInfo.title"
    @ok="onAddModelConfirm"
  >
    <CommodityCategoryCascader
      v-model:value="addModelList"
      is-must-child
      :level="addModelLevel"
      multiple
      :placeholder="cascaderModalInfo.placeholder"
      :scene="1"
      show-checked-strategy="SHOW_CHILD"
      style="width: 100%"
    />
  </a-modal>

  <RModalForm
    ref="rentalRuleRModalFormRef"
    :form-group="rentalRuleFormGroup"
    :open="rentalRuleVisible"
    :submit="handleRentalRuleSubmit"
    title="添加租赁方案"
    :width="583"
  >
    <template #rental_rule>
      <a-form-item :rules="{ required: true, message: '请选择租赁方案' }">
        <a-select
          v-model:value="addModelList"
          allow-clear
          :default-active-first-option="false"
          :filter-option="false"
          mode="multiple"
          placeholder="请选择租赁方案"
          :show-arrow="false"
          show-search
          style="width: 100%"
          @blur="handleBlur"
          @focus="handleFocus"
          @popup-scroll="handleServerScroll"
          @search="handleServerSearch"
        >
          <a-select-option
            v-for="itm in serversOptions"
            :key="itm.value"
            :disabled="itm.disabled"
            :value="itm.value"
          >
            {{ itm.label }}
          </a-select-option>
          <a-select-option v-if="isLoadingServers">
            <div style="display: flex; justify-content: center">
              <a-spin :spinning="isLoadingServers" />
            </div>
          </a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </RModalForm>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { ArrowLeftOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { debounce } from 'lodash-es';
import { nanoid } from 'nanoid';
import { FormGroupItem } from 'rrz-web-design';

import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import { useModal } from '@/hook/component/use-modal';
import { getSchemeList } from '@/pages-stage/commodity/dict/plan/service';
import {
  apiGetRuleDetailData,
  apiResetRuleDetailData,
  apiSaveRuleDetailData,
} from '@/pages-stage/risk/access-rule-system/fixed-deposit-freeze/rule-detail/services';

import { rentalRuleCol, rentalRuleFormGroup } from './config';

const route = useRoute();

const modelLevel = 4;
const modelType = 'modelType';
const categoryLevel = 2;
const categoryType = 'categoryType';

const cascaderModalInfo = computed(() => {
  const defaultInfo = {
    title: '添加',
    placeholder: '请选择',
  };
  if (!addModelLevel.value) {
    return defaultInfo;
  }

  const modalInfoMap = {
    [modelType]: {
      title: '添加型号',
      placeholder: '请选择商品型号',
    },
    [categoryType]: {
      title: '添加类目',
      placeholder: '请选择商品类目',
    },
  };

  return modalInfoMap[addModelType.value] || defaultInfo;
});

function saveData(type: 'preview' | 'formal' = 'preview') {
  return apiSaveRuleDetailData({
    type,
    rule_id: route.params.id,
    rule_preview:
      type === 'preview'
        ? JSON.stringify({
            model: modelList.rulePreview,
            category: categoryList.rulePreview,
            rental_plan: rentalRuleList.value.rulePreview,
          })
        : undefined,
  }).then(res => {
    type === 'formal' &&
      message.success({
        content: '操作成功',
        key: 'saveData',
      });
    if (type === 'formal') {
      getRuleDetailData();
    }

    return res;
  });
}

function deleteModel(data, index, recordIdx, parent) {
  Modal.confirm({
    title: '删除',
    content: '确认删除吗？',
    onOk() {
      data.splice(index, 1);
      if (!data.length) {
        parent[ruleType.value].splice(recordIdx, 1);
      }
      return saveData().then(res => {
        console.log(res);
      });
    },
  });
}

const rentalRuleRModalFormRef = ref<RModalFormInstance | null>(null);
const rentalRuleVisible = ref(false);
const rentalRuleList = ref({
  rulePreview: [],
  ruleValue: [],
});
async function handleRentalRuleSubmit() {
  if (!addModelList.value.length) {
    message.warn('请选择租赁方案后再提交！');
    return false;
  }

  const modelIds = addModelList.value.map(item => ({ id: item, title: '加载中...' }));
  const originalLength = addModelRecord.value.length;
  addModelRecord.value.push(...modelIds);
  const restoreModel = () => {
    addModelRecord.value.length = originalLength;
  };

  const res = await saveData().catch(() => false);

  if (!res) {
    restoreModel();
    return false;
  }

  if (res.status !== 0) {
    restoreModel();
    return false;
  }

  getRuleDetailData();
}
function handleAddRentalRuleItem() {
  rentalRuleList.value[ruleType.value].push({
    id: nanoid(),
    list: [],
    // 95~100的随机数
    deposit_ratio: Math.floor(Math.random() * 6) + 95,
  });
}
function handleOpenRentalRuleModal(list) {
  rentalRuleRModalFormRef.value?.open();
  handleBeforeOpen(list);
}
const rentalRuleFormGroup: FormGroupItem[] = [
  {
    key: 'rental_rule',
    label: '',
    fragmentKey: 'renderSelect',
  },
];

const originServersOptions = ref<any[]>();
const serversOptions = ref<any[]>([]);
const serverPageNo = ref(1);
const totalServerNum = ref(0);
const isLoadingServers = ref(false);

async function getRentalRuleDictOptions(options: { type: 'new' | 'add' }) {
  const { type = 'new' } = options || {};

  isLoadingServers.value = true;
  try {
    const res = await getSchemeList({
      page_no: serverPageNo.value,
      page_size: 20,
      scheme_name: searchKeyword.value,
    });

    if (!res.data) {
      serversOptions.value = [];
      serverPageNo.value = 1;
      return;
    }

    let list: any[] = res.data || [];
    totalServerNum.value = Number(res.meta.pagination.total || 0);
    const previewSourceList = rentalRuleList.value.rulePreview;
    const totalSelectedList = previewSourceList.reduce((prev, curr) => {
      return prev.concat(...curr.list);
    }, []);

    list = list.map(x => {
      return {
        label: x.scheme_name,
        value: x.id,
        is_show: x.is_show,
        disabled: !!totalSelectedList.find(y => y.id === x.id),
      };
    });

    if (type === 'new') {
      serversOptions.value = list.filter(x => x.is_show === 1);
      originServersOptions.value = list;
    } else {
      serversOptions.value.push(...list.filter(x => x.is_show === 1));
      originServersOptions.value.push(...list);
    }
  } finally {
    isLoadingServers.value = false;
  }
}

function handleBlur() {
  searchKeyword.value = '';
  serverPageNo.value = 1;
}

function handleServerScroll(e) {
  if (
    e.target.scrollTop + e.target.clientHeight > e.target.scrollHeight - 10 &&
    originServersOptions.value.length < totalServerNum.value &&
    !isLoadingServers.value
  ) {
    serverPageNo.value += 1;
    getRentalRuleDictOptions({ type: 'add' });
  }
}

function handleFocus() {
  getRentalRuleDictOptions({ type: 'new' });
}
const searchKeyword = ref('');
const handleServerSearch = debounce(val => {
  searchKeyword.value = val;
  getRentalRuleDictOptions({ type: 'new' });
}, 300);

const addModelList = ref([]);
const addModelRecord = ref();
const addModelLevel = ref(4);
const addModelType = ref();

/** 对于级联添加弹窗，判断是否可添加 */
function judgeCascaderCanAdd(curType, curList) {
  // 不同的level对应
  const levelSourceMap = {
    [modelType]: modelList,
    [categoryType]: categoryList,
  };

  // 拿到这一分类先前所有的数据
  const sourceInfoMap = levelSourceMap[curType];
  if (!sourceInfoMap) {
    console.warn('添加了未集中管理的数据');
    return false;
  }

  const previewSourceList = sourceInfoMap.rulePreview;
  const selectedList = previewSourceList.reduce((prev, curr) => {
    return prev.concat(...curr.list);
  }, []);

  let canNext = true;
  for (let i = 0; i < curList.length; i++) {
    const curId = curList[i];
    if (selectedList.find(x => x.id === curId)) {
      canNext = false;
      break;
    }
  }

  return canNext;
}

function addModel() {
  const modelIds = addModelList.value.map(item => ({ id: item.at(-1), title: '加载中...' }));
  const canNext = judgeCascaderCanAdd(
    addModelType.value,
    addModelList.value.map(item => item.at(-1)),
  );

  if (!canNext) {
    message.warn('禁止添加重复因子');
    return;
  }

  addModelRecord.value.push(...modelIds);
  saveData().then(() => {
    getRuleDetailData();
    addModelVisible.value = false;
  });
}

function handleBeforeOpen(list, level, type) {
  addModelRecord.value = list;
  addModelLevel.value = level;
  addModelList.value = [];
  addModelType.value = type;
}
const {
  visible: addModelVisible,
  loading: addModelLoading,
  open: openAddModelModal,
  confirm: onAddModelConfirm,
} = useModal(addModel, {
  beforeOpen: handleBeforeOpen,
});

const ruleType = ref<'rulePreview' | 'ruleValue'>('rulePreview');
const modelCol = [
  {
    title: '型号',
    dataIndex: 'title',
    align: 'center',
  },
  {
    title: '押金比例',
    width: 200,
    dataIndex: 'deposit_ratio',
    align: 'center',
  },
];
const modelList = reactive({
  rulePreview: [],
  ruleValue: [],
});

function addModelItem() {
  modelList[ruleType.value].push({
    id: nanoid(),
    list: [],
    deposit_ratio: 100,
  });
}

const categoryCol = [
  {
    title: '二级类目',
    dataIndex: 'title',
    align: 'center',
  },
  {
    title: '押金比例',
    width: 200,
    align: 'center',
    dataIndex: 'deposit_ratio',
  },
];
const categoryList = reactive({
  rulePreview: [],
  ruleValue: [],
});

function addCategoryItem() {
  categoryList[ruleType.value].push({
    id: nanoid(),
    list: [],
    deposit_ratio: 100,
  });
}

const conditionData = ref([]);
const conditionCol = ref([]);

function handleCondition(config) {
  conditionCol.value = config.map(item => ({
    title: item.title,
    dataIndex: item.type_id,
  }));
  conditionData.value[0] = Object.fromEntries(config.map(item => [item.type_id, item.value]));
}

function getRuleDetailData() {
  apiGetRuleDetailData({
    rule_id: route.params.id,
  }).then(res => {
    const { configConditionData, rulePreview, ruleValue } = res.data;
    handleCondition(configConditionData);
    modelList.rulePreview = rulePreview.model;
    categoryList.rulePreview = rulePreview.category;
    rentalRuleList.value.rulePreview = rulePreview.rental_plan;
    modelList.ruleValue = ruleValue.model;
    categoryList.ruleValue = ruleValue.category;
    rentalRuleList.value.ruleValue = ruleValue.rental_plan;
  });
}

function resetRuleConfig() {
  Modal.confirm({
    title: '重置',
    content: '确认重置吗？',
    onOk() {
      return apiResetRuleDetailData({
        rule_id: route.params.id,
      }).then(() => {
        getRuleDetailData();
      });
    },
  });
}

onMounted(() => {
  getRuleDetailData();
});
</script>

<style scoped lang="less">
.rule-detail {
  padding: 0 24px;

  .rule-item {
    margin-bottom: 24px;

    .rule-item-title {
      margin-bottom: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }
  }
}

:deep(.ant-table-wrapper) {
  .ant-table-cell:has(.ratio-input) {
    padding: 0;

    .ratio-input {
      width: 100%;
      padding: 16px;
      border: none;
    }
  }
}

.add-tag {
  background: #fff;
  border-style: dashed;
  cursor: pointer;
}
</style>
