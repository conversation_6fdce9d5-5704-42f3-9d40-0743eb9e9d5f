import legacy from '@vitejs/plugin-legacy';
import moment from 'moment';
import { visualizer } from 'rollup-plugin-visualizer';
import { loadEnv, Plugin } from 'vite';

import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import { ComponentResolver } from 'unplugin-vue-components/types';
import { createSentryPlugin } from './sentry';

const buildEnv = process.env.BUILD_ENV;
const isReport = process.env.REPORT;

export function createLegacyPlugin(): Plugin {
  return legacy({ targets: ['defaults', 'not IE 11'] });
}

export function createReportPlugin(): Plugin {
  return visualizer();
}

export function getBuildTimeVite(): Plugin {
  let startTime: number;
  let endTime: number;
  return {
    name: 'get-build-time',
    buildStart() {
      startTime = Date.now();
      console.log('Build Start: ', moment(startTime).format('YYYY-MM-DD HH:mm:ss'));
    },
    closeBundle() {
      endTime = Date.now();
      console.log('Build End: ', moment(endTime).format('YYYY-MM-DD HH:mm:ss'));
      console.log('Build Time: ', ((endTime - startTime) / 1000).toFixed(2) + 's');
    },
  };
}

export function createCustomPlugins(): Plugin[] {
  const plugins: Plugin[] = [];

  // 浏览器兼容性插件
  if (buildEnv !== 'test') {
    // plugins.push(createLegacyPlugin());
  }

  // 打包体积分析插件
  if (isReport) {
    plugins.push(createReportPlugin());
  }

  const env = loadEnv('production', process.cwd(), 'VITE_');
  // sentry
  if (buildEnv === 'prod' && env.VITE_SENTRY_UPLOAD === 'true') {
    plugins.push(createSentryPlugin());
  }

  plugins.push(getBuildTimeVite());

  return plugins;
}

// 需要定义且使用 default 的组件
const defaultComponent: string[] = ['ADatePicker', 'ATimePicker'];

// 需要自定义的组件
const excludeComponent: string[] = [
  'AMonthPicker',
  'AWeekPicker',
  'ARangePicker',
  'AQuarterPicker',
  'ATimeRangePicker',
  ...defaultComponent,
];

/**
 * @description 自定义解释器
 * @param componentName 组件名称
 */
export function customResolver(): ComponentResolver {
  return {
    type: 'component',
    resolve: (componentName: string) => {
      if (excludeComponent.includes(componentName)) {
        const isTime = componentName.indexOf('ATime') !== -1;
        const resultComponentName = defaultComponent.includes(componentName) ? undefined : componentName.slice(1);
        const fileName = isTime ? 'time-picker' : 'date-picker';
        return { name: resultComponentName, from: `ant-design-vue/es/${fileName}/moment` };
      }
    },
  };
}

/**
 * dirs 对哪个目录下的文件生效
 * include 对哪些文件生效，此处要设置tsx、jsx 不然不生效
 * extensions 包含哪些文件类型
 * resolvers 第三方库的处理
 */
export function createComponents(): Plugin {
  return Components({
    dirs: ['src/global-components'],
    include: [
      /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
      /\.vue$/,
      /\.vue\?vue/, // .vue
    ],
    extensions: ['vue', 'tsx'],
    directoryAsNamespace: true,
    collapseSamePrefixes: true,
    // globs: ['src/components/*.{vue}'],
    resolvers: [
      customResolver(),
      AntDesignVueResolver({
        importStyle: false,
      }),
    ],
  });
}
