<!--
   fieldItem: {
      type: Object,
      default: {
        box: 'text',     // text为查看 文字，eye为眼睛图标
        method: 'alert', // alert为弹出窗口，click为将获取数据替换原来的加密数据显示
        vapi: '',         // nov2 为使用没有v2的接口，其他或空为使用带v2接口
        id: '',          // 订单id或用户id或列表id，需与后端明确是哪个
        field: '',       // 原加密的key字段
        field_type: '',  // 加密所属类别
        type: '',        // 页面对应的值
      },
    }

加密所属类别：1:手机号 | 2:用户名 | 3:地址 | 4:支付宝ID | 5:身份证 | 6:营业执照 | 7:邮箱 | 8:企业名称 | 9:统一社会信用代码 | 10:微信号 | 11:租赁合同存证 | 12:上链协议 | 13:支付宝账号|19 一键查看订单信息|20 一键查看|21 查看快递单号
页面对应值：看后端yapi接口文档 https://yapi.rrzuji.net/project/53/interface/api/41775
-->
<template>
  <span v-if="!!retrunField" />
  {{ !!retrunField ? record[retrunField] : '' }}
  <span
    v-if="fieldItem.box === 'eye'"
    @click="getDecrypt(fieldItem.id, fieldItem.field, fieldItem.field_type, fieldItem.type)"
  >
    <EyeOutlined
      v-if="!viewed"
      class="eye-icon"
    /></span>
  <span v-else>
    <a
      v-if="!viewed"
      @click="getDecrypt(fieldItem.id, fieldItem.field, fieldItem.field_type, fieldItem.type)"
    >查看</a>
  </span>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { getDecryptData, getDecryptDataNov2 } from '@/utils/decrypt-method';
import { decryptType } from '@/utils/constant/decrypt-config';
import { store } from '@/store/store';
import { EyeOutlined } from '@ant-design/icons-vue';
import useSensitiveLookModel from '@/composables/use-sensitive-look-model';
const viewed = ref(false);
const record = ref({});
const props = defineProps({
  fieldItem: {
    type: Object,
    default: {
      box: 'text', // text为查看 文字，eye为眼睛图标
      method: 'alert', // alert为弹出窗口，click为将获取数据替换原来的加密数据显示
      vapi: '', // nov2 为使用没有v2的接口，其他或空为使用带v2接口
      id: '', // 订单id或用户id或列表id，需与后端明确是哪个
      field: '', // 原加密的key字段
      field_type: '', // 加密所属类别 decryptType
      type: '', // 页面对应的值
    },
  },
  row: {
    type: Object,
    default: {},
  },
  retrunField: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['getViewData']);
const getDecrypt = (id: any, field: any, field_type: any, type: any, oneClick = false) => {
  const url = store.getters.iframeUrl;
  const params = {
    url: url ? url : window.location.origin + window.location.pathname,
    id: id,
    field: field,
    field_type: decryptType[field_type],
    type: type,
  };
  const getMethod = props.fieldItem.vapi === 'nov2' ? getDecryptDataNov2 : getDecryptData;
  return getMethod(params).then(res => {
    if (oneClick) {
      return res.data.data;
    } else if (props.fieldItem.method === 'click') {
      emits('getViewData', res.data.data);
    } else if (props.fieldItem.method === 'clickTextShow') {
      record.value[props.retrunField] = res.data.data;
      viewed.value = true;
    } else {
      useSensitiveLookModel(res.data.data);
    }
  });
};

watch(
  () => props.fieldItem,
  () => {
    record.value = props.row;
  },
);

defineExpose({
  getDecrypt,
});
</script>

<style lang="less" scoped>
.eye-icon {
  cursor: pointer;

  &:hover {
    color: #3777ff;
  }
}
</style>
