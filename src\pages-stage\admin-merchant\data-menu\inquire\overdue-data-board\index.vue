<template>
  <div class="overdue-data-board">
    <header class="page-header">
      <span class="title">逾期数据查询</span>
    </header>

    <a-alert
      message="系统维护升级中，数据异常情况在升级完成后恢复正常"
      show-icon
      style="margin: 24px"
      type="info"
    />

    <fold-collapse
      v-model:value="search.isExpandSearch"
      :is-switch="false"
    >
      <section class="search-box">
        <a-form
          class="list-query"
          :model="search.listQuery"
        >
          <a-row :gutter="[24, 16]">
            <a-col :sm="8">
              <a-form-item label="订单生成时间">
                <a-range-picker
                  v-model:value="search.orderCreatedAt"
                  :allow-clear="false"
                  :placeholder="['填入', '填入']"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :sm="8">
              <a-form-item label="逾期类型">
                <a-select
                  v-model:value="search.listQuery.data_type"
                  :allow-clear="true"
                  :options="search.optionMap.overdue_type"
                  placeholder="请选择"
                />
              </a-form-item>
            </a-col>

            <a-col
              :lg="8"
              :md="12"
              style="margin-bottom: 16px"
            >
              <a-button
                class="operator-button"
                type="primary"
                @click="loadList"
              >
                搜 索
              </a-button>
              <a-button
                class="operator-button"
                @click="resetList"
              >
                重 置
              </a-button>
              <span class="more-link">更多筛选条件</span>
              <down-outlined class="icon" />
            </a-col>
          </a-row>
          <div class="more-screen">
            <a-row
              class="screen-one"
              :gutter="[24, 16]"
              @mouseenter="maskOneVisible = true"
              @mouseleave="maskOneVisible = false"
            >
              <div
                v-show="maskOneVisible"
                class="screen-mask"
              >
                <span class="normal-text">
                  该模块为
                  <span class="zulin-text"> 租赁数智进阶版 </span>
                  数据体验，当前内测中，是否参与体验？</span>

                <a-button
                  style="margin-left: 8px"
                  type="primary"
                  @click="toExperience(1)"
                >
                  去体验
                </a-button>
              </div>
              <a-col :sm="8">
                <a-form-item label="风控定性">
                  <a-select
                    v-model:value="search.listQuery.risk_qualitative"
                    :allow-clear="true"
                    :options="search.optionMap.risk_qualitative"
                    placeholder="请选择"
                  />
                </a-form-item>
              </a-col>
              <a-col :sm="8">
                <a-form-item label="类目/品牌/型号">
                  <multiple-cascader
                    v-model="search.categoryBrandModel"
                    :loading="search.optionMapLoading"
                    :options="search.optionMap.goods_types"
                  />
                </a-form-item>
              </a-col>
              <a-col :sm="8">
                <a-form-item label="预收期数">
                  <a-select
                    v-model:value="search.listQuery.advance_amount"
                    :allow-clear="true"
                    :options="search.optionMap.advance_amount"
                    placeholder="请选择"
                  />
                </a-form-item>
              </a-col>
              <a-col :sm="8">
                <a-form-item label="逾期节点">
                  <a-select
                    v-model:value="search.listQuery.overdue_node"
                    :allow-clear="true"
                    :options="search.optionMap.overdue_node"
                    placeholder="请选择"
                  />
                </a-form-item>
              </a-col>
              <a-col :sm="8">
                <a-form-item
                  class="form-item-two-input-number"
                  label="年龄"
                >
                  <a-input-number
                    id="inputNumber"
                    v-model:value="search.buyAgeStart"
                    :max="200"
                    :min="1"
                    placeholder="填入"
                    :precision="0"
                  />
                  <swap-right-outlined />
                  <a-input-number
                    id="inputNumber"
                    v-model:value="search.buyAgeEnd"
                    :max="200"
                    :min="1"
                    placeholder="填入"
                    :precision="0"
                  />
                </a-form-item>
              </a-col>
              <a-col :sm="8">
                <a-form-item
                  class="form-item-two-input-number"
                  label="押金区间"
                >
                  <a-input-number
                    id="inputNumber"
                    v-model:value="search.goodsPriceStart"
                    :min="0"
                    placeholder="填入"
                  />
                  <swap-right-outlined />
                  <a-input-number
                    id="inputNumber"
                    v-model:value="search.goodsPriceEnd"
                    :min="0"
                    placeholder="填入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row
              align="center"
              class="screen-two"
              @mouseenter="maskTwoVisible = true"
              @mouseleave="maskTwoVisible = false"
            >
              <div
                v-show="maskTwoVisible"
                class="screen-mask"
              >
                <span class="normal-text">
                  该模块为
                  <span class="zulin-text"> 租赁数智进阶版 </span>
                  数据体验，当前内测中，是否参与体验？</span>

                <a-button
                  style="margin-left: 8px"
                  type="primary"
                  @click="toExperience(2)"
                >
                  去体验
                </a-button>
              </div>
              <a-col class="export-btn">
                <a-button type="primary">
                  <template #icon>
                    <export-outlined class="export-icon" />
                  </template>
                  导出订单明细
                </a-button>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </section>
    </fold-collapse>
    <section class="statistics-data">
      <a-spin :spinning="statistics.loading">
        <a-row :gutter="24">
          <a-col
            v-for="item in statistics.list"
            :key="item.title"
            :lg="12"
            :md="24"
            :xl="8"
            :xxl="6"
          >
            <card
              :description="item.description"
              :remain="item.remain"
              :time="timeScoped"
              :title="item.title"
              :unit="item.unit"
              :value="item.value"
            >
              <template
                v-if="item.title === '应收总租金'"
                #desc
              >
                <span class="card-desc">未出账单：{{ statistics.data.unprocess_bills || 0 }}元</span>
              </template>
            </card>
          </a-col>
        </a-row>
      </a-spin>
    </section>
    <open-remain-modal
      v-model:visible="openRemainVisible"
      :server-type="serverType"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { DownOutlined, SwapRightOutlined } from '@ant-design/icons-vue';

import Card from '@/components/card-shuzhi/index.vue';
import FoldCollapse from '@/components/fold-collapse/index.vue';
import MultipleCascader from '@/components/multiple-cascader/index.vue';
import { deepClone } from '@/utils/base';
import { formatTimeScoped } from '@/utils/time';

import OpenRemainModal from '../components/open-remain-modal/index.vue';
import { loginShuzhi } from '../shipment-data-board/service';
import useSearch from './composables/use-search';
import useStatistics from './composables/use-statistics';
import { getStatisticsData } from './service';

const search = useSearch();
const statistics = useStatistics();

const timeScoped = ref('');

function loadList() {
  statistics.loading = true;
  getStatisticsData(search.listQuery)
    .then(res => {
      statistics.data = res.data;
      timeScoped.value = formatTimeScoped(search.listQuery.order_created_at.split(','));
    })
    .finally(() => (statistics.loading = false));
}

function resetList() {
  search.listQuery = deepClone(search.listQueryDefault);
  search.categoryBrandModel = [];
  loadList();
}

const maskOneVisible = ref<boolean>(false);
const maskTwoVisible = ref<boolean>(false);
const openRemainVisible = ref<boolean>(false);
const serverType = ref<string>('serverStaff');

const shuzhiLink = ((referrer: string) => {
  if (import.meta.env.DEV || /dev\d*\-/.test(referrer)) {
    return 'dev1-shuzhi.rrzuji.com';
  } else if (/test\d*\-/.test(referrer)) {
    return 'test1-shuzhi.rrzuji.com';
  } else {
    return 'shuzhi.rrzu.com';
  }
})(document.referrer);

const toExperience = (type: number) => {
  if (type === 1) {
    window.sensors.track({ key: '$ScreenExperienceClick' });
  } else {
    window.sensors.track({ key: '$ExportExperienceClick' });
  }
  loginShuzhi().then(res => {
    if (res.data.account_id > 0) {
      window.open('https://' + shuzhiLink + '/statistics/overdue-data-board?token=' + res.data.token);
      window.sensors.track({ key: '$MerchantLoginShuzhi' });
    } else {
      serverType.value = res.data.server_role;
      openRemainVisible.value = true;
    }
  });
};

onMounted(() => {
  search.loadOptionMap();
  timeScoped.value = formatTimeScoped(search.listQuery.order_created_at.split(','));
  loadList();
});
</script>

<style scoped lang="less">
@import '@/style/variables.less';

.overdue-data-board {
  height: 100%;
  background-color: #f5f7fa;
}

.page-header {
  position: relative;
  display: flex;
  align-items: center;
  padding: 24px 24px 0;
  background-color: #fff;
  .title {
    color: @font-black-85;
    font-weight: bold;
    font-size: 20px;
  }
}

.search-box {
  padding: 24px 24px 8px;
  background-color: #fff;
  .operator-button + .operator-button {
    margin-left: 8px;
  }

  .form-item-two-input-number {
    :deep(.ant-form-item-control-input-content) {
      .ant-input-number {
        width: calc(50% - 10px);
      }
      .anticon-swap-right {
        display: inline-block;
        width: 20px;
      }
    }
  }

  :deep(.ant-col > .ant-form-item) {
    width: 100%;
    margin-bottom: 0;
  }

  .more-link {
    margin-left: 16px;
    color: #3777ff;
    cursor: pointer;
  }
}

.icon {
  margin-left: 8px;
  color: #3777ff;
}
.statistics-data {
  padding: 12px 24px;

  :deep(.ant-col) {
    margin-top: 12px;
    margin-bottom: 12px;
  }
  .card-desc {
    margin-left: 8px;
    color: rgb(177, 181, 191);
    font-weight: 500;
  }
}

.more-screen {
  position: relative;
  display: flex;
  margin-bottom: 24px;
  padding: 0 24px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .screen-one {
    flex: 3;
    padding: 24px 0;
  }

  .screen-two {
    flex: 1;
    margin-left: 12px;
    padding: 24px 0;
    text-align: center;
  }

  .screen-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: rgba(6, 21, 51, 0.65);

    .normal-text {
      color: #fff;
    }
    .zulin-text {
      color: #52c41a;
    }
  }

  .export-btn {
    display: flex;
    align-items: center;
  }
}
</style>
