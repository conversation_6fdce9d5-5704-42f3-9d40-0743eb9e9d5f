<template>
  <a-modal
    :footer="null"
    title="补货校验数据下载"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
  >
    <div class="time-box">
      <a-table
        class="table-box"
        :columns="dateListColumns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ y: '100%' }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operator'">
            <div>
              <span
                v-if="record.excel_file"
                style="color: #3777ff; cursor: pointer"
                @click="onDownload(record.excel_file)"
              >下载</span>
              <span v-else>-</span>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { onMounted } from 'vue';
import { useTable } from '@/hook/component/use-table';
import { getLogDateList } from '../service';
import { dateListColumns } from '../config';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  versionId: {
    type: String,
    default: '0',
  },
});

const emits = defineEmits(['update:visible']);

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  api: getLogDateList,
  totalKey: 'data.pageInfo.count',
  formatSearchValue: () => {
    const queryParams = {
      version_id: props.versionId,
    };
    return queryParams;
  },
  pagination: {
    pageSize: 5,
    showSizeChanger: false,
  },
});

const onDownload = (excel_file: string) => {
  window.open(excel_file);
};

const onCancel = () => emits('update:visible', false);

onMounted(() => {
  getTableList('search');
});
</script>
