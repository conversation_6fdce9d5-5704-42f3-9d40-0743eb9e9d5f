<template>
  <a-collapse v-model:activeKey="activeKey">
    <a-collapse-panel
      key="1"
      collapsible="header"
    >
      <template #header>
        {{ currentTitle }}
        <span class="header-num">{{ `共${currentNum}项` }}</span>
      </template>
      <template #extra>
        <a-popconfirm
          v-if="!isDetail || isCanEditAll"
          title="是否确认清除当前标题下所填写的全部信息？"
          @confirm="clearHandle"
        >
          <a-button
            class="del-btn"
            danger
            type="link"
          >
            <template #icon>
              <DeleteOutlined />
            </template>
            删除
          </a-button>
        </a-popconfirm>
      </template>
      <template v-if="isDetail && !isCanEditAll">
        <TableItem
          v-model:detailValue="detailValue"
          :can-row-select="canRowSelect"
          :have-objection="haveObjection"
          :key-name="keyName"
          :no-show-img="noShowImg"
          :no-show-platform="noShowPlatform"
        />
      </template>
      <template v-else-if="currentValue.list && currentTitle">
        <div
          v-for="(current, index) in currentValue.list"
          :key="keyName + index"
          class="item-box"
        >
          <div class="item-value">
            <ValueItem
              v-model:currentValue="currentValue.list[index]"
              :all-list="currentValue.list"
              :is-custom="isCustom"
              :key-name="keyName"
              :options="options"
            />
          </div>
          <a-popconfirm
            v-if="index > 0"
            title="确定删除该项？"
            @confirm="delHandle(index)"
          >
            <MinusCircleOutlined class="del-icon" />
          </a-popconfirm>
        </div>
        <div
          class="add-btn"
          @click="addHandle"
        >
          <PlusCircleFilled /><span class="add-text">添加异常项</span>
        </div>
        <a-dropdown
          placement="topRight"
          :trigger="canSelectReportFile"
          :visible="dropdownOpen"
        >
          <div class="mask-box">
            <FragmentUpload
              ref="fragmentUploadRef"
              v-model:data="currentValue.url"
              :all-show-port="true"
              :show-text="['上传凭证照片/视频', '（数量最多6）']"
              template="dragger"
            >
              <template
                v-if="isFromOrderList"
                #extend
              >
                <a-alert
                  style="margin-bottom: 16px;width: 813px;"
                  type="warning"
                >
                  <template #message>
                    <div class="fragment-upload-extend">
                      <div>
                        <InfoCircleFilled style="color: #faad14" />
                      </div>
                      <div
                        class="extend-tips"
                        @click.stop
                      >
                        注意: 上传的细节图需附尺子<a
                          class="underline-text"
                          @click.stop="openImg"
                        >(查看示例图) </a>且内容需清晰、与异常项匹配;设备故障情形需上传视频演示证明。
                      </div>
                    </div>
                  </template>
                </a-alert>
              </template>
            </FragmentUpload>
            <div
              v-if="canSelectReportFile.length > 0"
              class="mask"
              :style="{ height: `${maskHeight || 146}px` }"
              @click="maskClick(true)"
            />
          </div>
          <template #overlay>
            <a-menu
              @click="selectHandle"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
            >
              <a-menu-item key="1">
                <PlusCircleOutlined /><span class="select-text">选取本地文件</span>
              </a-menu-item>
              <a-menu-item key="2">
                <InboxOutlined /><span class="select-text">选取报告文件</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </a-collapse-panel>
  </a-collapse>
  <ReportImgSelect
    ref="reportImgSelectRef"
    :size="canSelectMaxSize"
    @set-images="setImages"
  />
  <RPreview
    v-model:preview="previewImg"
    :index="0"
    :value="imgsValue"
  />
</template>

<script setup lang="ts">
import { computed, inject, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  DeleteOutlined,
  InboxOutlined,
  InfoCircleFilled,
  MinusCircleOutlined,
  PlusCircleFilled,
  PlusCircleOutlined,
} from '@ant-design/icons-vue';

import FragmentUpload from '@/components/fragment-upload/index.vue';
import { deepClone } from '@/utils/base';

import { initData, initDataV2 } from '../config';
import type { TItemValue, TListOptions, TUrl } from '../data.d';
import { isCanEditAllSymbol, isFromOrderListSymbol } from '../injection-symbols';
import ReportImgSelect from './report-img-select.vue';
import TableItem from './table-item.vue';
import ValueItem from './value-item.vue';
const props = defineProps<{
  itemValue?: TItemValue;
  detailItemValue?: TItemValue;
  isDetail?: boolean;
  keyName?: string;
  options?: TListOptions[];
  noShowPlatform?: boolean;
  canRowSelect?: boolean;
  noShowImg?: boolean;
  haveObjection?: boolean;
  currentIndex: number;
  reportImags?: TUrl[];
}>();
const isCanEditAll = inject(isCanEditAllSymbol, ref(false));
const isFromOrderList = inject(isFromOrderListSymbol, ref(false));
const activeKey = ref(props.currentIndex !== 0 && !props.isDetail ? '' : '1');
const emits = defineEmits(['update:itemValue', 'update:detailItemValue']);
const currentValue = computed({
  get() {
    return props?.itemValue || { list: [], url: [] };
  },
  set(val) {
    emits('update:itemValue', val);
  },
});
const detailValue = computed({
  get() {
    return props.detailItemValue || { list: [], url: [] };
  },
  set(val) {
    emits('update:detailItemValue', val);
  },
});

const canSelectReportFile = computed(() => {
  return props.reportImags && props.reportImags?.length > 0 ? ['click'] : [];
});

const isCustom = computed(() => props.keyName === 'custom_item');

const currentTitle = computed(() => (isCustom.value ? '自定义项' : props.keyName));

const currentNum = computed(() => {
  let num = 0;
  const temp = props.isDetail && !isCanEditAll.value ? detailValue.value?.list : currentValue.value?.list;
  if (!temp || temp.length === 0) return num;
  temp.forEach(item => {
    if (item.money !== '' && Number.isFinite(Number(item.money))) {
      num++;
    }
  });
  return num;
});

function clearHandle() {
  const newInitData = isFromOrderList.value ? initDataV2 : initData;
  currentValue.value.list = deepClone(newInitData.list);
}

function addHandle() {
  const newInitData = isFromOrderList.value ? initDataV2 : initData;
  const item = deepClone(newInitData.list[0]);
  currentValue.value.list.push(item);
}

function delHandle(index: number) {
  currentValue.value.list.splice(index, 1);
}

function openCollapse() {
  activeKey.value = '1';
}

const reportImgSelectRef = ref();
const fragmentUploadRef = ref();
function selectHandle({ key }: any) {
  maskClick(false);
  if (key !== '1') {
    const imags = props.reportImags?.filter(
      (item: any) => !currentValue.value.url.some(img => (img?.url ? item.url === img.url : img === item.url)),
    );
    if (imags?.length === 0) return message.warn('图片已全部选择');
    reportImgSelectRef.value?.open(imags);
  } else {
    fragmentUploadRef.value?.exposeClick();
  }
}

const canSelectMaxSize = computed(() => {
  return 6 - currentValue.value.url.length;
});

function setImages(images: TUrl[]) {
  const temp = deepClone(images.map(item => item.url));
  currentValue.value.url.push(...temp);
}

const dropdownOpen = ref(false);
const closeCutdown = ref();
function maskClick(bool: boolean) {
  dropdownOpen.value = bool;
  handleMouseLeave();
}

function handleMouseEnter() {
  clearTimeout(closeCutdown.value);
}

function handleMouseLeave() {
  closeCutdown.value = setTimeout(() => {
    dropdownOpen.value = false;
  }, 2000);
}

const imgsValue = ['https://img1.rrzuji.cn/uploads/scheme/2508/05/m/O3FaslCWEjUAaxSuqgCZ.png'];

const previewImg = ref(false);

function openImg() {
  previewImg.value = true;
}

const maskHeight = ref();
watch(
  () => currentValue.value.list,
  value => {
    if (value.length === 0) return;
    const element = document.querySelector('.ant-upload-drag');
    if (element) {
      const height = element.getBoundingClientRect().height;
      maskHeight.value = height.toFixed(2);
    }
  },
);

defineExpose({
  openCollapse,
});
</script>
<style lang="less" scoped>
.del-icon {
  color: var(--ant-error-color);
}
.item-box:not(:last-child) {
  margin-bottom: 16px;
}
.del-btn {
  height: auto;
  padding: 0;
}
.header-num {
  margin-left: 4px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
}
.item-box {
  display: flex;
  gap: 16px;
  align-items: center;
  width: 100%;
  padding-left: 6px;
  border-left: 2px solid var(--ant-primary-color);
  .item-value {
    width: 100%;
  }
}
.add-btn {
  width: 100px;
  margin: 16px 0;
  color: var(--ant-primary-color);
  cursor: pointer;
  .add-text {
    margin-left: 4px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
.select-text {
  margin-left: 4px;
}
.mask-box {
  position: relative;
  .mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    width: 100%;
    height: 100%;
  }
}

.fragment-upload-extend {
  display: flex;
  justify-content: center;
  gap: 4px;
}

.underline-text {
  text-decoration: underline;
  text-underline-offset: 3px;
}

.extend-tips {
  font-size: 14px;
  color: rgba(6, 21, 51, 0.65);
  z-index: 100;
}
</style>
