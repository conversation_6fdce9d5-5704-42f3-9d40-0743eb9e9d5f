export const statusMap = {
  1: {
    text: '异 常',
    color: '#F5222D',
    backgroundColor: '#FFF1F0',
    tableColor: 'linear-gradient(90deg, #FFF1F0 0%, #FFFAFA 100%)',
    tooltip_text: '异常：请关注店铺受影响结果，尽快进入诊断详情解决异常',
  },
  2: {
    text: '待优化',
    color: '#FA8C16',
    backgroundColor: '#FFF7E6',
    tableColor: 'linear-gradient(90deg, #FFEED4 0%, #FFFDFA 100%)',
    tooltip_text: '待优化：当前店铺存在可优化项，请进入诊断详情查看平台建议',
  },
  3: {
    text: '进行中',
    color: '#52C41A',
    backgroundColor: '#F6FFED',
    tableColor: 'linear-gradient(90deg, #E0FAD3 0%, #FBFFFA 100%)',
    tooltip_text: '正常：店铺状态正常，请继续保持当前状态并持续提高店铺权重（如数据转化、用户服务等）',
  },
};

export const violationStatusMap = {
  1: {
    color: '#FA8C16',
    backgroundColor: '#FFF7E6',
  },
  2: {
    color: '#FA8C16',
    backgroundColor: '#FFF7E6',
  },
  3: {
    color: '#FA8C16',
    backgroundColor: '#FFF7E6',
  },
  4: {
    color: '#52C41A',
    backgroundColor: '#F6FFED',
  },
  5: {
    color: '#FA8C16',
    backgroundColor: '#FFF7E6',
  },
  6: {
    color: '#F5222D',
    backgroundColor: '#FFF1F0',
  },
  7: {
    color: '#52C41A',
    backgroundColor: '#F6FFED',
  },
};

export enum EBasicIndicator {
  /**
   * 发货率
   */
  DeliveryRate = 'delivery_rate',
  /**
   * 平均预收期数
   */
  AvgPrepaymentPeriodNum = 'avg_prepayment_period_num',
  /**
   * 响应平均时效
   */
  AvgValidResponseSecond = 'avg_valid_response_second',
  /**
   * 发货平均时效
   */
  AvgSentSecond = 'avg_sent_second',
  /**
   * 预存款
   */
  PreDeposit = 'pre_deposit',
  /**
   * 全部
   */
  All = 'all',

  /**
   * 标记发货比例
   */
  DeliveryWarehouseRate = 'delivery_warehouse_rate',
  /**
   * 扣款成功比例
   */
  PaymentAttemptSuccessRate = 'payment_attempt_success_rate',
  /**
   * 待发货比例
   */
  WaitingDeliveryRate = 'waiting_delivery_rate',
}

export const tabItems = [
  { label: '发货率', value: EBasicIndicator.DeliveryRate },
  { label: '平均预收期数', value: EBasicIndicator.AvgPrepaymentPeriodNum },
  { label: '响应平均时效', value: EBasicIndicator.AvgValidResponseSecond },
  { label: '发货平均时效', value: EBasicIndicator.AvgSentSecond },
  { label: '预存款', value: EBasicIndicator.PreDeposit },
  { label: '待发货比例', value: EBasicIndicator.WaitingDeliveryRate },
  { label: '扣款成功比例', value: EBasicIndicator.PaymentAttemptSuccessRate },
  { label: '标记发货比例', value: EBasicIndicator.DeliveryWarehouseRate },
  { label: '全部', value: EBasicIndicator.All },
];

export const specialTagMap = {
  1: '平台采购总账户',
  2: '第三方采购总账户',
  3: '供应链货款',
};

export const adviceMap = {
  7: '目前店铺活动参与率过低，目前报名活动＜4个，建议参加活动4个及以上，可通过报名链接参与更多活动获取流量',
  8: '当前已报名的商品流量较低，建议商家报名更多机型获取流量。',
};

export const indicatorLabelObj = {
  avg_risk_pass_sent_pay_num: '风控通过发货前平均收取期数',
  avg_valid_response_second: '响应时效',
  no_mark_deliver_pass_48h_num: '自发订单发货超48H订单数量',
  risk_pass_delivery_rate: '风控通过发货率',
  avg_delivery_rent: '平均发货租金',
  delivery_rate: '发货率',
};

export const hKeys = ['avg_valid_response_second'];
export const rateKeys = ['risk_pass_delivery_rate', 'delivery_rate', 'delivery_rate_of_paid_order'];
