import { TField } from '../../../data';

export default class Plot {
  public plot: any = null;
  constructor(public key: TField) {
    this.key = key;
  }
  drawChart(key: TField, data: any[]) {
    console.log(key, data);
    const { G2Plot } = window as any;
    this.plot?.destroy();
    this.plot = new G2Plot.Mix(key, {
      appendPadding: 8,
      tooltip: { shared: true },
      syncViewPadding: true,
      height: 300,
      plots: [
        {
          type: 'column',
          options: {
            data,
            xField: 'label',
            yField: 'value',
            isGroup: true,
            isStack: true,
            seriesField: 'name',
            groupField: 'type',
          },
        },
      ],
    });
    this.plot.render();
  }
  updateData(data: any[]) {
    this.plot.changeData(data);
  }
}
