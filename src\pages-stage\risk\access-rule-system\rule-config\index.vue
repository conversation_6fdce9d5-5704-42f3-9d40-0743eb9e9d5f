<template>
  <layout-admin-page
    :content-style="{
      padding: '0 24px',
    }"
    :navs="['风控', '风控准入设置', '自建准入配置']"
    title="自建准入配置"
    top-fixed
  >
    <template #extra>
      <div class="extra-btns">
        <a-button @click="openRuleModal()">
          新建准入规则
        </a-button>
        <a-button @click="goUserQuota">
          用户额度配置
        </a-button>
        <a-button @click="openRuleLogModal">
          操作日志
        </a-button>
        <a-button @click="openDepositWay">
          押金冻结方式
        </a-button>
      </div>
    </template>
    <search-table
      ref="searchTableRef"
      :search-api="apiGetRuleConfigList"
      :search-config="searchFormConfig"
      :tb-col="tbCol"
      :tb-props="{
        scroll: {
          x: 1200,
        },
      }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-switch
            :checked="Boolean(record['status'])"
            :loading="switchLoading[record.id]"
            @change="changeStatus($event, record)"
          />
        </template>
        <template v-else-if="column.key === 'action'">
          <span
            class="action-btn"
            @click="openRuleModal(record)"
          >编辑</span>
          <span
            class="action-btn"
            @click="goDetail(record.id)"
          >详情设置</span>
          <span
            class="action-btn"
            @click="openDetailLogModal(record.id)"
          >日志</span>
          <span
            class="action-btn"
            @click="delRuleItem(record.id)"
          >删除</span>
        </template>
      </template>
    </search-table>
  </layout-admin-page>

  <RuleModal
    v-model:visible="ruleModalData.visible"
    :record="ruleModalData.record"
    @success="getRuleList"
  />
  <LogModal v-model:visible="ruleLogVisible" />
  <DetailLogModal
    v-model:visible="detailLogVisible"
    :rule-id="detailLogRuleId"
  />
  <DepositWay ref="depositWayRef" />
</template>
<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'risk-access-rule-system-rule-config',
});
</script>

<script lang="ts" setup>
import { onActivated, ref } from 'vue';
import { useConfig } from './config';
import { apiGetRuleConfigList } from './services';

import RuleModal from './components/rule-modal/index.vue';
import { useRuleModal } from './components/rule-modal/composables/use-rule-model';

import LogModal from './components/log-modal/index.vue';
import { useRuleLogModal } from './components/log-modal/composables/use-log-modal';

import DetailLogModal from './components/detail-log-modal/index.vue';
import { useDetailLogModal } from './components/detail-log-modal/composables/use-detail-log-modal';

import { useRuleSwitch } from './composables/use-rule-switch';
import { useRuleDel } from './composables/use-rule-del';
import { useRouter } from 'vue-router';

import DepositWay from './components/deposit-way.vue';
const router = useRouter();

const searchTableRef = ref();
const { searchFormConfig, tbCol } = useConfig();
function getRuleList() {
  searchTableRef.value?.searchFormData();
}
const { ruleModalData, openRuleModal } = useRuleModal();
const { ruleLogVisible, openRuleLogModal } = useRuleLogModal();
const { detailLogVisible, detailLogRuleId, openDetailLogModal } = useDetailLogModal();
const { switchLoading, changeStatus } = useRuleSwitch();
const { delRuleItem } = useRuleDel(getRuleList);
const isFefresh = ref(false);
function goDetail(id: string) {
  isFefresh.value = true;
  router.push({
    path: `/risk/access-rule-system/rule-detail-setting/${id}`,
  });
}
function goUserQuota() {
  router.push({
    path: '/risk/access-rule-system/user-quota',
  });
}

const depositWayRef = ref();
const openDepositWay = () => {
  depositWayRef.value?.open();
};

onActivated(() => {
  if (isFefresh.value) {
    isFefresh.value = false;
    getRuleList();
  }
});
</script>
<style lang="less" scoped>
:deep(.search-form) {
  grid-auto-flow: row dense;
  .ant-form-item:nth-child(2) {
    // 2列
    grid-column: span 2;
  }
}
.extra-btns {
  .ant-btn {
    margin-right: 8px;
  }
}
.action-btn {
  color: var(--ant-primary-color);
  cursor: pointer;
}
.action-btn + .action-btn {
  margin-left: 10px;
}
</style>
