import { GET, POST } from '@/services/api';
import { IExcelModel } from '../../quotation-manage/quotation-registration/data';

// 报价登记
export const addRequirements = (params: { content: IExcelModel[] }) => {
  return POST<{ content: IExcelModel[] }, []>('/super/recycle/demand-regis-quotation/save', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
};

// 报价登记
export const getQuitationList = () => {
  return GET<void, { content: IExcelModel[]; id: string }>('/super/recycle/demand-regis-quotation/quotation');
};

// 查询商品id是否有效
export const filterInvalidGoodsId = (params: { goods_id: string }) => {
  return GET('/demand-regis-quotation/filter-invalid-goods-id', params);
};
//校验报价单
export const checkByWarehouse = (params: any) => {
  return POST('/super/recycle/demand-regis-quotation/check-by-warehouse', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
};
