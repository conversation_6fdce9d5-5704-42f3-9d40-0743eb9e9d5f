<template>
  <div class="card-data-item">
    <div class="card-data-item__title">
      {{ title }}
      <a-tooltip
        v-if="tipsText"
        placement="top"
      >
        <template #title>
          <span>{{ tipsText }}</span>
        </template>
        <info-circle-outlined class="card-data-item__title__icon" />
      </a-tooltip>
    </div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
import { InfoCircleOutlined } from '@ant-design/icons-vue';
defineProps({
  title: {
    type: String,
    default: '',
  },
  tipsText: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.card-data-item {
  padding: 24px 24px 16px 24px;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  &__title {
    margin-bottom: 5px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    &__icon {
      color: rgba(6, 21, 51, 0.45);
      font-size: 12px;
    }
  }
}
</style>
