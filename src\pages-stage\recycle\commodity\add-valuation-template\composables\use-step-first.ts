import { nextTick, reactive, watch } from 'vue';
import { Form, Modal } from 'ant-design-vue';
import { Rule } from 'ant-design-vue/es/form';

import useValuationOption from '../composables/use-valuation-option';
import { IConditionSet, IOption, IStepProps } from '../data';

export default function (props: IStepProps) {
  // 下拉选项
  const { options: quaOptions } = useValuationOption(2);
  const { options: functionOptions } = useValuationOption(3);

  // 表单数据
  const formState = reactive<{
    name: string;
    condition_type: { value: string }[];
    condition_set: IConditionSet[];
    qua_check_option: IOption[];
    function_option: IOption[];
    qua_check: number[];
    function: number[];
  }>({
    name: '',
    condition_type: [{ value: '' }],
    condition_set: [],
    qua_check_option: [],
    function_option: [],
    qua_check: [],
    function: [],
  });
  // 表单校验规则
  const formRules = {
    name: [
      {
        required: true,
        message: '请输入模板名称（如：手机-苹果）',
      },
    ],
    condition_type: [
      {
        required: true,
        validator: async (_rule: Rule, condition_type: any[]): Promise<void> => {
          if (condition_type.some(condition => condition.value)) {
            return Promise.resolve();
          } else {
            return Promise.reject('至少需要填写一项成新度');
          }
        },
      },
    ],
    qua_check_option: [
      {
        required: true,
        message: '请选择检测评估内容',
      },
    ],
    function_option: [
      {
        required: true,
        message: '请选择功能评估内容',
      },
    ],
  };
  // 校验表单
  const { validate, clearValidate, validateInfos } = Form.useForm(formState, formRules);

  // 初始化操作
  const init = () => {
    const { name, condition_type, qua_check_option, function_option, condition_set } = props.formData;
    formState.name = name;
    formState.condition_type = condition_type.map((value: string) => ({ value }));
    formState.qua_check_option = qua_check_option;
    formState.function_option = function_option;
    formState.condition_set = condition_set;
  };
  watch(() => props.formData.id, init);

  // 添加成新度类型
  const addConditionType = () => {
    if (!formState.condition_type || !(formState.condition_type instanceof Array)) {
      formState.condition_type = [];
    }
    formState.condition_type.push({
      value: '',
    });
    nextTick(() => {
      clearValidate('condition_type');
    });
  };

  // 删除成新度类型
  const deleteConditionType = (index: number) => {
    const { value: _condition } = formState.condition_type.splice(index, 1)[0];
    formState.condition_set = formState.condition_set.filter(({ condition }) => condition != _condition);
  };

  // 选中 & 取消选中评估内容
  const onSelect = (value: number, type: string) => {
    if (type === 'qua') {
      const realValue = quaOptions.value.find(option => option.value == value);
      const idx = formState.qua_check_option.findIndex(({ origin_log_id }) => value == origin_log_id);
      if (idx != -1) {
        Modal.confirm({
          title: '评估项已经添加，是否更新评估项内容？',
          onOk: () => {
            realValue && formState.qua_check_option.splice(idx, 1, realValue.real);
            formState.qua_check.push(value);
          },
        });
      } else {
        confirmModified().then(() => {
          realValue && formState.qua_check_option.push(realValue.real);
          formState.qua_check.push(value);
        });
      }
    }
    if (type === 'function') {
      const realValue = functionOptions.value.find(option => option.value == value);
      const idx = formState.function_option.findIndex(({ origin_log_id }) => value == origin_log_id);
      if (idx != -1) {
        Modal.confirm({
          title: '评估项已经添加，是否更新评估项内容？',
          onOk: () => {
            realValue && formState.function_option.splice(idx, 1, realValue.real);
            formState.function.push(value);
          },
        });
      } else {
        realValue && formState.function_option.push(realValue.real);
        formState.function.push(value);
      }
    }
  };
  const onDeselect = (value: number, type: string) => {
    if (type === 'qua') {
      // if(formState.condition_set.length) {}
      confirmModified().then(() => {
        const index = formState.qua_check_option.findIndex(target => target.origin_log_id == value);
        formState.qua_check_option.splice(index, 1);
        const idx = formState.qua_check.indexOf(value);
        formState.qua_check.splice(idx, 1);
      });
    }
    if (type === 'function') {
      const index = formState.function_option.findIndex(target => target.origin_log_id == value);
      formState.function_option.splice(index, 1);
      const idx = formState.function.indexOf(value);
      formState.function.splice(idx, 1);
    }
  };
  // 删除评估项
  const onDeleteOption = (type: string, origin_log_id: number, index: number) => {
    if (type === 'qua') {
      confirmModified().then(() => {
        const raw_index = formState.qua_check.indexOf(origin_log_id);
        index != -1 && formState.qua_check_option.splice(index, 1);
        raw_index != -1 && formState.qua_check.splice(raw_index, 1);
      });
    }
    if (type === 'function') {
      Modal.confirm({
        title: '确定要删除该项吗？',
        onOk: () => {
          const raw_index = formState.function.indexOf(origin_log_id);
          index != -1 && formState.function_option.splice(index, 1);
          raw_index != -1 && formState.function.splice(raw_index, 1);
        },
      });
    }
  };

  // 确认修改检测评估项
  const confirmModified = () => {
    return new Promise<void>((resolve, reject) => {
      if (formState.condition_set.length) {
        Modal.confirm({
          title: '修改检测评估项需要重新配置成新度设定，确定修改吗？',
          onOk: () => {
            formState.condition_set.length = 0;
            resolve();
          },
          onCancel: () => reject(),
        });
      } else resolve();
    });
  };

  // 切换功能评估单选多选
  const toggleMulti = (row: IOption) => {
    row.is_multi = !!row.is_multi ? 0 : 1;
  };
  return {
    quaOptions,
    functionOptions,

    formState,
    validateInfos,
    validate,

    init,

    addConditionType,
    deleteConditionType,

    onSelect,
    onDeselect,
    onDeleteOption,
    toggleMulti,
  };
}
