import type { Ref } from 'vue';
import { onBeforeUnmount, toRef } from 'vue';
import { debounce } from 'lodash-es';

import { useRouteSessionData } from '@/composables/use-route-session-data';
import { useSearchHistory } from '@/composables/use-search-history';

import SearchBox from '../components/search-box.vue';
import GatherSearch from '../gather-search.vue';
import { ESearchAction } from '../typing';
import { useControlData } from './use-control-data';

type TGatherSearch = InstanceType<typeof GatherSearch>;
type TSearchBox = InstanceType<typeof SearchBox>;

export function useSearchAction(params: {
  props: TGatherSearch['$props'];
  emit: TGatherSearch['$emit'];
  searchBoxRef: Ref<TSearchBox | undefined>;
}) {
  const { props, emit, searchBoxRef } = params;
  const { pushHistoryData, gotoSearchHistory, destroyHistoryData } = useSearchHistory();

  // 在离开页面时销毁历史记录控制
  onBeforeUnmount(() => {
    destroyHistoryData();
  });

  // ====================== 创建数据 ======================
  // 表单绑定数据
  const { bindData: bindFormData, resetData: resetFormData } = useControlData({
    data: toRef(() => props.formData),
    onChange: val => emit('update:formData', val),
    historyKey: 'formData',
  });

  // 控制显示分页页码
  const { bindData: bindShowPageCount } = useControlData({
    data: toRef(() => props.showPageCount),
    onChange: val => emit('update:showPageCount', val),
    historyKey: 'showPageCount',
  });

  // tab标签选择
  const { bindData: bindTabValue } = useControlData({
    data: toRef(() => props.tabValue),
    onChange: val => emit('update:tabValue', val),
    historyKey: 'tabValue',
  });

  // 分页页码
  const { bindData: bindCurrent, resetData: resetCurrent } = useControlData({
    data: toRef<number>(props.pagination?.current || 1),
  });

  // 分页大小
  const { bindData: bindPageSize, resetData: resetPageSize } = useControlData({
    data: toRef<number>(props.pagination?.pageSize || 10),
  });

  // 路由携带的搜索条件
  const { bindData: bindRouteData, resetData: resetRouteData } = useControlData({
    data: toRef({}),
  });

  // ====================== 数据初始化，使用路由携带的数据或者缓存的数据初始化 ======================
  const { initData, setSession } = useRouteSessionData('TABLE_SEARCH_DATA');
  // 初始化赋值
  bindTabValue.value = initData.tabValue ?? props.tabsGroup[0]?.value ?? props.tabValue;
  if (initData.showPageCount !== undefined) {
    bindShowPageCount.value = initData.showPageCount;
  }
  const groupKeys = props.formGroup
    .map(item => ('items' in item ? item.items.map(temp => temp.key) : [item.key]))
    .flat();
  groupKeys.forEach(key => {
    if (initData[key] !== undefined) {
      bindFormData.value[key] = initData[key];
    }
  });
  const ignoreQueryKey = groupKeys.concat(['role', 'origin', 'orderCache']);
  // 除ignoreQueryKey的值，其他的值都赋值给bindRouteData
  for (const key in initData) {
    if (!ignoreQueryKey.includes(key)) {
      bindRouteData.value[key] = initData[key];
    }
  }

  // ====================== 操作功能 ======================
  let searchData: Record<string, any> = {};

  /** 开始进行搜索 */
  const beginSearchData = debounce((action: ESearchAction) => {
    searchData = { ...bindRouteData.value, ...bindFormData.value };
    // tab标签选择
    searchData.tabValue = bindTabValue.value;
    // pagination传入false时，不给分页相关数据
    if (props.pagination !== false) {
      searchData.current = bindCurrent.value;
      searchData.pageSize = bindPageSize.value;
      searchData.showPageCount = bindShowPageCount.value;
    }
    // 对搜索数据进行缓存，用于刷新页面时保留搜索条件
    setSession(searchData);
    emit('search', searchData, action);
  }, 200);

  /** 点击搜索进行查询，外部直接调用时action为非按钮触发 */
  async function onSearchForm(action = ESearchAction.Manual) {
    await searchBoxRef.value?.validate(); // 校验通过才可搜索
    const success = pushHistoryData();
    // 搜索有新的查询条件时，重置页码
    if (success) {
      resetCurrent();
    }
    beginSearchData(action);
  }

  /** 点击重置进行刷新查询，重置表单，重置页码，重置路由携带的搜索条件 */
  async function onResetForm() {
    await searchBoxRef.value?.resetFields();
    resetFormData();
    resetCurrent();
    resetRouteData();
    pushHistoryData();
    beginSearchData(ESearchAction.Reset);
  }

  /** 重置页码 */
  function onResetCurrent() {
    resetCurrent();
    beginSearchData(ESearchAction.ResetCurrent);
  }

  /** 选择修改tab标签 */
  function onChangeTab(value: string | number) {
    bindTabValue.value = value;
    resetCurrent();
    pushHistoryData();
    beginSearchData(ESearchAction.ChangeTab);
  }

  /** 分页查询 */
  function onChangePage(current: number, pageSize: number) {
    bindCurrent.value = current;
    bindPageSize.value = pageSize;
    pushHistoryData();
    beginSearchData(ESearchAction.ChangePage);
  }

  /** 修改分页数量统计显示 */
  function onChangeShowPageCount(value: boolean) {
    bindShowPageCount.value = value;
    // 如果分页大小改变，则重置分页页码
    const originPageSize = props.pagination?.pageSize || 10;
    if (bindPageSize.value !== originPageSize) {
      resetCurrent();
      resetPageSize();
    }
    pushHistoryData();
    beginSearchData(ESearchAction.ChangeShowPageCount);
    localStorage.setItem('super.risk_list_v3_show_page_count', value);
  }

  /** 历史查询 */
  function onSearchHistory(index: number) {
    resetCurrent();
    gotoSearchHistory(index);
    beginSearchData(ESearchAction.SearchHistory);
  }

  return {
    bindFormData,
    bindPageSize,
    bindCurrent,
    bindShowPageCount,
    bindTabValue,
    bindRouteData,
    onResetCurrent,
    onSearchForm,
    onResetForm,
    onChangeTab,
    onChangePage,
    onChangeShowPageCount,
    onSearchHistory,
  };
}
