<template>
  <a-modal
    :body-style="{ paddingBottom: 0 }"
    :footer="null"
    title="售后消息"
    :visible="visible"
    width="600px"
    @cancel="onCancel"
  >
    <a-spin :spinning="spinning">
      <div class="msg-box-content">
        <ul class="msg-box-menu">
          <li>未处理（{{ pageInfo.count }}）</li>
        </ul>
        <div class="msg-box-list">
          <div
            v-if="!pageInfo.count"
            class="no-data"
          >
            <img src="https://img1.rrzuji.cn/uploads/scheme/2109/11/o/2dE76wKqJiBJqB6pbGzO.png">
            <p>暂无消息</p>
          </div>
          <div
            v-else
            class="the-msg-box"
          >
            <div
              v-for="item in msgList"
              :key="item.id"
              class="msg-box-list-item msg-item"
            >
              <div class="text">
                <!-- 该订单{订单：{{ item.order }}，IMEI码：{{ item.code }}}已完成{{ item.type }}，请及时处理 -->
                {{ item.content }}
              </div>
              <div class="time">
                {{ item.created_at }} <span @click="go(item.work_id, item.id)">去处理</span>
              </div>
            </div>
          </div>
        </div>
        <a-pagination
          v-model:current="pageInfo.page"
          v-model:pageSize="pageInfo.pageSize"
          class="pagination"
          show-size-changer
          :total="pageInfo.count"
          @change="onChange"
        >
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </a-spin>
  </a-modal>
  <div
    v-if="isShowIcon"
    :class="[
      {
        'tips-shake': isHasMsg,
      },
      'msg-icon',
    ]"
    @click="show"
  >
    <div class="red-badge">
      {{ unreadMsgNum }}
    </div>
    <AliwangwangFilled />
  </div>
</template>

<script lang="ts" setup>
import { ref, h, onMounted, onUnmounted } from 'vue';
import { AliwangwangFilled } from '@ant-design/icons-vue';
import { notification, Button } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { debounce } from 'lodash-es';
import { unDealMsgList, dealMsg, noticeMsgInfo } from './services';

interface NewUnReadMsgType {
  id: string;
  work_id: string;
  content: string;
}

interface UnReadMsgListType extends NewUnReadMsgType {
  created_at: string;
}

const router = useRouter();
const visible = ref(false);
const msgList = ref<UnReadMsgListType[]>([]);
const isHasMsg = ref(false);
const unreadMsgNum = ref(0);
const isShowIcon = ref(false); //控制售后消息icon以及触发消息通知
const clearInter = ref<any>();
const spinning = ref(false);
const pageInfo = ref({
  count: 0,
  page: 1,
  pageSize: 10,
  totalPage: 0,
});

//消息通知
const openNotification = (item: NewUnReadMsgType) => {
  notification.info({
    message: '售后消息通知',
    description: item.content,
    duration: 5,
    btn: () =>
      h(
        Button,
        {
          type: 'primary',
          size: 'small',
          onClick: () => go(item.work_id, item.id),
        },
        { default: () => '去处理' },
      ),
    placement: 'bottomRight',
  });
};

notification.config({
  maxCount: 6, //最多显示六个通知
});

const getLatestMsg = debounce(() => {
  noticeMsgInfo().then(({ data }) => {
    const { inWhiteList, hasUnDealMsg, unreadMsgList, unDealMsgTotal } = data;
    if (data.inWhiteList) {
      isShowIcon.value = inWhiteList;
      isHasMsg.value = hasUnDealMsg;
      unreadMsgNum.value = unDealMsgTotal;
      if (unreadMsgList && unreadMsgList.length) {
        unreadMsgList.forEach((v: NewUnReadMsgType) => {
          openNotification(v);
        });
      }
    }
  });
}, 2000);

//定时获取最新售后信息
let firstLoad = true;
const setIntervalInfo = () => {
  const hasFocus = document.hasFocus();
  if (firstLoad) {
    getLatestMsg();
    firstLoad = false;
  }
  if (hasFocus) {
    removeIntervalInfo();
    getLatestMsg();
    clearInter.value = setInterval(getLatestMsg, 3 * 60 * 1000);
  }
};
const removeIntervalInfo = () => {
  if (clearInter.value) {
    clearInterval(clearInter.value);
  }
};

//获取未处理消息
const getUnDealMsgList = () => {
  spinning.value = true;
  unDealMsgList({
    page: pageInfo.value.page,
    page_size: pageInfo.value.pageSize,
  })
    .then(v => {
      msgList.value = v.data.list;
      pageInfo.value = v.data.pageInfo;
      spinning.value = false;
    })
    .catch(() => {
      spinning.value = false;
      msgList.value = [];
    });
};

const onChange = (page: number, pageSize: number) => {
  pageInfo.value.page = page;
  pageInfo.value.pageSize = pageSize;
  getUnDealMsgList();
};

const onCancel = () => {
  visible.value = false;
};

//路由跳转
const go = (work_id: string, id: string) => {
  router.push({
    path: '/order/delivery-sales/list',
    query: {
      work_id: work_id ?? '',
      isReq: 1,
    },
  });
  dealMsg({
    id,
  }).then(() => {
    onCancel();
    getLatestMsg();
  });
};

const show = () => {
  visible.value = true;
  getUnDealMsgList();
};

onMounted(() => {
  setIntervalInfo();
  window.addEventListener('focus', setIntervalInfo);
  window.addEventListener('blur', removeIntervalInfo);
});
onUnmounted(() => {
  removeIntervalInfo();
  window.removeEventListener('focus', setIntervalInfo);
  window.removeEventListener('blur', removeIntervalInfo);
});
</script>

<style lang="less" scoped>
.msg-box-content {
  position: relative;
  display: flex;
  width: 100%;
  height: 540px;
  padding-bottom: 64px;
  overflow: hidden;
}

.msg-box-list {
  flex: 1;
  overflow: scroll;
}
.pagination {
  position: absolute;
  right: 20px;
  bottom: 24px;
  height: 30px;
}
.msg-box-menu {
  width: 100px;
  margin: 0;
  padding: 0;
  list-style-type: none;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  cursor: pointer;
}
.msg-box-menu li {
  height: 44px;
  color: #00c8be;
  line-height: 44px;
  text-align: center;
  background: rgba(0, 200, 190, 0.08);
  border-right: 2px solid #00c8be;
}
.msg-box-list-item {
  margin: 0 20px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(6, 21, 51, 0.06);
  .time {
    display: flex;
    justify-content: right;
    span {
      margin-left: 20px;
      color: #008fe0;
      cursor: pointer;
    }
  }
}
.msg-box-list .no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}
.msg-icon {
  position: fixed;
  right: 6px;
  bottom: 171px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  color: #5d677a;
  font-size: 25px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s;
  &:hover {
    background: #11d2c2;
  }
}

.tips-shake {
  color: orange;
  animation: shake 800ms linear infinite;
}
.red-badge {
  position: absolute;
  top: 15%;
  left: 70%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 1px 2px;
  color: #fff;
  font-size: 12px;
  background: red;
  border-radius: 50%;
}

@keyframes shake {
  /* 水平抖动，核心代码 */
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%,
  80% {
    transform: translate3d(+2px, 0, 0);
  }
  30%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%,
  60% {
    transform: translate3d(+4px, 0, 0);
  }
  50% {
    transform: translate3d(-4px, 0, 0);
  }
}
</style>
