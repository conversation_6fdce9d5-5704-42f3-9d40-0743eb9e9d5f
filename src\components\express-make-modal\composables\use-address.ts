import { ref } from 'vue';

import { GET } from '@/services/api';

import type { IOptionItem, IReceiveAddressProps } from '../data';

export const useAddress = (props: { value: IReceiveAddressProps }) => {
  const provinceValue = ref(props.value.province);
  const cityValue = ref(props.value.city);
  const areaValue = ref(props.value.area);
  const addressValue = ref(props.value.address);

  const provinceOptions = ref<IOptionItem<string>[]>([]);
  const cityOptions = ref<IOptionItem<string>[]>([]);
  const areaOptions = ref<IOptionItem<string>[]>([]);

  const optionsMap = {
    0: provinceOptions,
    1: cityOptions,
    2: areaOptions,
  };

  const requestAddressData = (params: { name?: string; level: 0 | 1 | 2 }) => {
    const { name: parent_name, level: parent_level } = params;
    return new Promise(async resolve => {
      const requestParams = {
        parent_name,
        parent_level,
      };
      try {
        const res = await GET('/super/recycle/order-sf-express/sf-province', requestParams);
        const data = res.data || [];
        if (optionsMap[parent_level]) {
          optionsMap[parent_level].value = data;
        }
        resolve(true);
      } catch (e) {
        resolve(false);
        console.error(e);
      }
    });
  };
  requestAddressData({ level: 0 });

  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  return {
    provinceValue,
    cityValue,
    areaValue,
    addressValue,

    provinceOptions,
    cityOptions,
    areaOptions,

    filterOption,
    requestAddressData,
  };
};
