<!--  -->
<template>
  <a-modal
    :confirm-loading="confirmLoading"
    :visible="visible"
    @cancel="cancel"
    @ok="submit"
  >
    <template #title>
      <div class="title">
        <span class="title-left">服务费设置</span><span class="title-right">此设置仅对未付款的商品生效</span>
      </div>
    </template>
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formState"
      :rules="rules"
    >
      <a-form-item
        label="修改批次"
        name="batch_arr"
      >
        <a-select
          ref="select"
          v-model:value="formState.batch_arr"
          mode="multiple"
          :options="batchSelect"
          placeholder="请选择"
          @change="selectChange"
        />
      </a-form-item>
      <a-form-item
        label="锁机服务费/台"
        name="price"
      >
        <a-input-number
          v-model:value="formState.price"
          :min="0"
          placeholder="请输入"
          :precision="2"
          style="width: 240px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, UnwrapRef, ref, reactive, watch } from 'vue';
import { postSetPrice } from '../services';
interface IFormState {
  batch_arr: Array<any>;
  price: number | null;
}
export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    batchSelect: {
      type: Array,
      default: () => {
        return [];
      },
    },
    currentBatch: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const formState: UnwrapRef<IFormState> = reactive({
      batch_arr: [],
      price: null,
    });
    const confirmLoading = ref(false);
    const singlePriceValidator = async (rule: any, value: any) => {
      if (typeof value === 'string') {
        return Promise.reject('请输入合法的价格！');
      } else if (value == null) {
        return Promise.reject('该项不能为空！');
      } else {
        return Promise.resolve();
      }
    };
    const batchValidator = async (rule: any, value: any) => {
      if (Array.isArray(value) && value.length > 0) {
        return Promise.resolve();
      } else {
        return Promise.reject('该项不能为空！');
      }
    };
    const rules = {
      batch_arr: { required: true, validator: batchValidator, trigger: ['blur', 'change'] },
      price: { required: true, validator: singlePriceValidator, trigger: ['blur', 'change'] },
    };
    const formRef = ref();
    watch(
      () => props.currentBatch,
      batch => {
        console.log('batch', batch);
        formState.batch_arr = batch;
      },
    );
    //选择全部时，取消勾选其他
    function selectChange(value: string[]) {
      const length = value.length;
      if (length > 0) {
        if (value[length - 1] === 'all') {
          formState.batch_arr = ['all'];
        } else {
          formState.batch_arr = value.filter(val => val !== 'all');
        }
      }
    }
    function cancel() {
      emit('update:visible', false);
      formRef.value.resetFields();
    }
    async function submit() {
      console.log('formState', formState);
      try {
        await formRef.value.validateFields();
        confirmLoading.value = true;
        const batch_arr = formState.batch_arr;
        let params = { price: formState.price, batch_arr: batch_arr.includes('all') ? 'all' : batch_arr };
        await postSetPrice(params);
        emit('success');
        cancel();
      } finally {
        confirmLoading.value = false;
      }
    }
    return { formState, formRef, rules, confirmLoading, submit, cancel, selectChange };
  },
});
</script>
<style scoped lang="less">
.title {
  .title-left {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
  }
  .title-right {
    margin-left: 9px;
    color: #ff4a57;
    font-size: 14px;
  }
}
</style>
