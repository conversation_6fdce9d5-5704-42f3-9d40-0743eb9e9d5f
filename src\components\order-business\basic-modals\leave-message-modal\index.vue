<template>
  <a-modal
    v-model:visible="bindVisible"
    :mask-closable="false"
    title="修改用户留言"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 3 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item
          label="留言"
          name="remark"
        >
          <a-textarea
            v-model:value="formData.remark"
            :auto-size="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { ref } from 'vue';
import { useVModel } from '@/hook';
import { FormInstance, message } from 'ant-design-vue';
import { fetchGateway } from '@/components/order-business/basic-modals/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

export interface IFormData {
  remark?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  remake: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const formData = ref<IFormData>({});
const bindVisible = useVModel(props, 'visible', emit);
const rules = {
  remark: [{ type: 'string', required: true, message: '留言不能为空', trigger: 'blur' }],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      formData.value = {
        remark: props.remake,
      };
    }
  },
  { immediate: true },
);

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      orderId: props.orderId,
      ordinary_delivery: 1,
      remark: formData.value.remark,
      method: 'v2.order.edit.remark',
    };
    fetchGateway(params)
      .then(() => {
        message.success('操作成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data']);
      })
      .finally(() => (loading.value = false));
  });
}
</script>
