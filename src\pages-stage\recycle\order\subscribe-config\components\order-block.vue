<template>
  <div class="order-block">
    <a-spin
      v-if="loading"
      class="page-loading"
      tip="正在加载中..."
    />
    <template v-else>
      <div
        v-for="(item, index) in order_dimension_form"
        :key="index"
        class="select-row-wrap"
      >
        <SelectRow
          :content-list-map="contentListMap"
          :disabled="index === 0"
          :list="order_dimension_form.filter((...arg:any) => arg[1] != index)"
          :options="parentOptions"
          :value="item"
        >
          <div
            v-if="index != 0"
            class="handle-block"
          >
            <minus-circle-outlined
              class="item-icon del-icon"
              @click="delParent(index)"
            />
          </div>
        </SelectRow>
      </div>

      <div
        class="add-box"
        :style="!order_dimension_form.length ? 'margin-top: 12px' : ''"
        @click="onAddOrderConfig"
      >
        <plus-circle-filled style="margin-right: 4px; color: #00c8be; font-size: 16px" />
        添加属性
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { MinusCircleOutlined, PlusCircleFilled } from '@ant-design/icons-vue';
import SelectRow from './select-row.vue';
import { formSelect } from '../service';

const loading = ref<boolean>(false);

const order_dimension_form_template: any = {
  attr: null,
  attr_label: null,
  content: [],
  content_label: [],
};
const order_dimension_form: any = ref([
  { attr: 'order_status', attr_label: '订单状态', content: [4], content_label: ['待归还'] },
]);

const parentOptions = ref<any>([]);
const contentListMap = ref<any>({});

const getOptions = async () => {
  loading.value = true;
  const res = await formSelect();
  const data = res.data || [];
  parentOptions.value = data;
  parentOptions.value.forEach((item: any) => {
    contentListMap.value[item.value] = item.next;
  });
  loading.value = false;
};

const onAddOrderConfig = () => {
  const obj = JSON.parse(JSON.stringify(order_dimension_form_template));
  order_dimension_form.value.push(obj);
};

const delParent = (index: number) => {
  order_dimension_form.value.splice(index, 1);
};

const getParams = () => {
  const orderForm = JSON.parse(JSON.stringify(order_dimension_form.value));
  const formState = orderForm.filter((item: any) => {
    const contentIsArray = Array.isArray(item.content);
    // 多选则需要判断数据长度，单选仅需判断值不为null;
    return item.attr && (contentIsArray ? item.content.length : item.content != null);
  });
  formState.forEach((item: any) => {
    // 处理单选的情况，单选则把数据重新包装成数组形式储存；
    item.content = Array.isArray(item.content) ? item.content : [item.content];
  });
  return JSON.parse(JSON.stringify(formState));
};
const setFormData = (params: any) => {
  order_dimension_form.value = params;
};

defineExpose({ getParams, setFormData });

onMounted(() => {
  getOptions();
});
</script>

<style lang="less" scoped>
.order-block {
  .select-row-wrap {
    display: flex;
    flex-direction: column;
    margin-bottom: 4px;
    padding: 4px 8px;
    border-radius: 2px;
    &:hover {
      background: #f5f7fa;
      cursor: pointer;
    }
    .child-row {
      position: relative;
      padding-left: 24px;
      .select-row {
        &::after {
          position: absolute;
          top: 0;
          left: -16px;
          width: 1px;
          height: 90%;
          margin-top: 4px;
          background: rgba(6, 21, 51, 0.15);
          content: '';
        }
        &:nth-child(n + 2) {
          &::before {
            position: absolute;
            top: -8px;
            left: -16px;
            width: 1px;
            height: 12px;
            background: rgba(6, 21, 51, 0.15);
            content: '';
          }
        }
        &:nth-last-child(1) {
          &::after {
            margin-top: 0;
            margin-bottom: 4px;
          }
          &::before {
            height: 8px;
          }
        }
      }
    }
    .handle-block {
      display: inline-flex;
      align-items: center;
      margin-left: 16px;
      .item-icon {
        margin-right: 16px;
        font-size: 16px;
        &.del-icon {
          &:hover {
            color: #f5222d;
          }
        }
      }
      .add-item {
        display: inline-flex;
        align-items: center;
        .addd-item-text {
          margin-top: 1px;
          color: rgba(6, 21, 51, 0.65);
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
        }
        &:hover {
          color: #3777ff;
          cursor: pointer;
          .addd-item-text {
            color: #3777ff;
          }
        }
      }
    }
  }
}
.add-box {
  display: inline-flex;
  align-items: center;
  padding-left: 16px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;
}
.add-icon {
  margin-left: 8px;
  font-size: 20px;
  cursor: pointer;
}
</style>
