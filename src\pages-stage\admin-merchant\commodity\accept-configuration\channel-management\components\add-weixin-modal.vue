<template>
  <a-modal
    :destroy-on-close="true"
    ok-text="我已完成扫码，刷新页面"
    title="添加微信账号"
    :visible="visible"
    :width="800"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <div class="add-wx-box">
      <div class="add-wx-img flex-column">
        <p>1、请打开微信扫码关注人人租公众号，已关注可跳过</p>
        <img
          alt=""
          src="https://img1.rrzuji.cn/assest/202002/SUPER5E59BEDE2DED0.jpg"
        >
      </div>
      <div class="add-wx-img flex-column">
        <p>2、微信扫描下方二维码，同意授权即可完成</p>
        <img
          alt=""
          :src="imageBase64"
        >
      </div>
    </div>
    <div style="color: red">
      注：绑定成功后系统将通过“人人租”公众号为您推送新消息通知
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, nextTick } from 'vue'

import QRCode from 'qrcode';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  serverId: {
    type: Number,
    default: 0,
  },
  wexinQrcode: {
    type: String,
    default:'',
  }
});
const emits = defineEmits(['update:visible','getClose']);
const onCancel = () => {
  emits('update:visible', false);
  emits('getClose')
}

const imageBase64 = ref('')
nextTick(async () => {
  imageBase64.value = await QRCode.toDataURL(props.wexinQrcode, {});
})

const handleOk = () => {
  onCancel();
}
</script>
<style scoped lang="less">
.add-wx-box {
  display: flex;
  justify-content: space-between;
  .add-wx-img {
    flex-basis: 50%;
    img {
      width: 60%;
    }
  }
  .flex-column {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
</style>
