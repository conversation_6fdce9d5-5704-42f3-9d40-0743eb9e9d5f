<template>
  <div class="goods-info">
    <img
      class="sku-img"
      :src="goodsData.goods_main_image"
    >
    <div class="suk-info">
      <div class="goods-name">
        <tooltips :content="[goodsData.goods_name]" />
      </div>
      <div class="sku-model">
        {{ goodsData.sku_info }}
      </div>
      <div class="sku-imei">
        <tooltips
          :clamp="2"
          :content="goodsData.imei_list.map(item => `IMEI码：${item.imei}`)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { IGoodsData } from '../../data';
import Tooltips from '../tooltips/index.vue';

defineProps({
  goodsData: {
    type: Object as PropType<IGoodsData>,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.goods-info {
  display: flex;
  padding: 16px;
  .sku-img {
    width: 72px;
    height: 72px;
    margin-right: 16px;
  }
  .suk-info {
    width: 280px;
    .goods-name {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
    }
    .sku-model {
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
    }
    .sku-imei {
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
    }
  }
}
</style>
