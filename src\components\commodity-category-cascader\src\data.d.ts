import type { IOption, TDisabledType } from '@/composables/commodity-category/data';
import type { EScene } from '@/utils/enums/commodity';

type TValue = number[] | number[][];

export interface ICommodityCategoryCascaderProps<V extends TValue = TValue> {
  /** 绑定值 */
  value?: V;
  /** 禁用项的特殊处理，'show':显示；'enable':启用 */
  disabledType?: TDisabledType;
  /** 可获取到的分类级别：1 一级| 2 二级| 3 品牌| 4 模型, 不填默认取到第4级模型层 */
  level?: 1 | 2 | 3 | 4;
  /** 接口获取的场景，默认1或2(根据用户类型role判断)，1 平台商品 2 商家商品 3 平台订单 4 商家订单 */
  scene?: EScene;
  /** 根据第几层的父id进行筛选（需要传入父级pid获取分类） */
  filterPidLevel?: 1 | 2 | 3;
  /** 使用完整的父级层级id数组（推荐，完整各个父级id数组查询性能更好） */
  pid?: number | (number | undefined)[];
  /** 是否使用异步搜索，无法与filterPidLevel同时使用 */
  asyncSearch?: boolean;
  /** 数据是否必须包含子级（如 level 是 3，而返回数据层级少于 3 且不设置时changeOnSelect时无法被选择） */
  isMustChild?: boolean;
  /** 当此项为 true 时，每级菜单选项值都可选 */
  changeOnSelect?: boolean;
  /** 自定义浮层类名 */
  dropdownClassName?: string;
  /** 是否多选 */
  multiple?: boolean;
  /** 选择后的渲染函数 */
  displayRender?: (params: IDisplayRenderParams) => string;
  /** 定义选项，通过返回值设置下拉的选择项内容 */
  defineOptions?: (options: IOption[]) => IOption[];
  showCheckedStrategy?: 'SHOW_PARENT' | 'SHOW_CHILD';
}

/** 选择后的渲染函数参数类型 */
export interface IDisplayRenderParams {
  labels: string[];
  selectedOptions: (IOption | null)[];
}
