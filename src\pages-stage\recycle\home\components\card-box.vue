<template>
  <div class="card-box">
    <div class="card-header">
      <div class="card-header__title">
        {{ title }}
        <span class="card-header__title--suffix">
          <slot name="suffix" />
        </span>
      </div>
      <div>
        <slot name="datePicker" />
      </div>
      <span
        v-if="!hiddenHandle"
        class="card-header__handle"
        @click="onHandle"
      > 查看详情 </span>
    </div>
    <div class="card-body">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  hiddenHandle: {
    type: Boolean,
    default: false,
  },
  pageUrl: {
    type: String,
    default: '',
  },
});

const onHandle = () => {
  if (!props.pageUrl) return;
  window.parent.postMessage(
    {
      action: 'blank',
      href: props.pageUrl,
    },
    '*',
  );
};
</script>

<style lang="less" scoped>
.card-box {
  padding: 24px;
  background: #fff;
  border-radius: 2px;
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 22px;
    &__title {
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      &--suffix {
        font-weight: 400;
      }
    }
    &__handle {
      color: #3777ff;
      font-weight: 400;
      cursor: pointer;
    }
  }
}
</style>
