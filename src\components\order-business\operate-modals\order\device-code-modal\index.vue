<template>
  <a-drawer
    v-model:visible="isShow"
    class="notarization-modal"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
    title="设备序列号"
    :width="900"
  >
    <a-alert
      style="margin-bottom: 24px"
      type="warning"
    >
      <template #message>
        <div
          v-for="(item, index) in tipList"
          :key="index"
        >
          {{ index + 1 }}. {{ item }}
        </div>
      </template>
    </a-alert>

    <a-form
      ref="formRef"
      :label-col="{ style: { width: '130px' } }"
      layout="vertical"
      :model="formData"
    >
      <a-form-item
        label="选择打印方式"
        name="serials"
      >
        <a-radio-group
          v-model:value="formData.is_prefix"
          @change="onChangePrintType"
        >
          <a-radio :value="1">
            自动生成打印
          </a-radio>
          <a-radio :value="0">
            自定义打印
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <!-- 商品清单 -->
      <div
        v-if="deviceCodeDetail.type === 3"
        class="commodity-list"
      >
        <div class="header">
          <div class="title">
            {{ pageConfig.title }}
          </div>
          <div class="sub-title">
            {{ pageConfig.subTitle }}
          </div>
        </div>
        <a-spin :spinning="commodifyListLoading">
          <RUpload
            v-model:value="fileList"
            v-bind="uploadConfig"
          />
        </a-spin>
        <div class="table-wrap">
          <a-alert
            v-if="pageConfig.errorText"
            :message="pageConfig.errorText"
            show-icon
            style="margin-bottom: 12px"
            type="error"
          />
          <a-table
            bordered
            :columns="columns"
            :data-source="commodifyList"
            :pagination="false"
            :scroll="{ y: 400 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="['name'].includes(column.key) && record.isNew">
                <a-input
                  v-model:value="record[column.key]"
                  allow-clear
                  :placeholder="'请输入' + column.title"
                  @change="handleChangeCommodityValue($event, record, column.key)"
                />
              </template>
              <template v-else-if="['codes'].includes(column.key)">
                <a-tooltip title="仅支持批量录入已系统打印生成的序列号">
                  <a-select
                    v-model:value.trim="record[column.key]"
                    mode="tags"
                    :open="false"
                    placeholder="仅支持批量录入已系统打印生成的序列号"
                    :token-separators="[',', ' ']"
                  />
                </a-tooltip>
              </template>
              <template v-else-if="['num'].includes(column.key) && record.isNew">
                <a-input-number
                  v-model:value="record[column.key]"
                  allow-clear
                  min="1"
                  :placeholder="'请输入' + column.title"
                  @change="handleChangeCommodityValue($event, record, column.key)"
                />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button
                  danger
                  :disabled="!record.isNew"
                  type="link"
                  @click="handleCommodityDelete(record)"
                >
                  移除
                </a-button>
              </template>
            </template>
            <template
              v-if="pageConfig.errorText"
              #footer
            >
              <plus-square-outlined
                style="font-size: 20px; cursor: pointer"
                @click="handleCommodityAddRow"
              />
            </template>
          </a-table>
        </div>
      </div>
      <a-form-item
        v-else
        :label="`序列号录入（${formData.serials?.length}）`"
        name="serials"
      >
        <a-select
          ref="serialInputRef"
          v-model:value="formData.serials"
          mode="tags"
          :open="false"
          :options="options"
          :placeholder="
            formData.is_prefix ? '仅支持批量录入已系统打印生成的序列号' : '请先点击打印按钮，输入需要打印的序列号'
          "
          style="width: 200px"
          :token-separators="[',', ' ']"
        />
      </a-form-item>
      <a-form-item
        label="序列号模板"
        name="serials"
      >
        <a-select
          v-model:value="formData.id"
          :options="formData.is_prefix ? serialModalOptions : is_prefixModelOptions"
          style="flex: 1"
          @change="onChangeSerialModal"
        />
      </a-form-item>
      <div class="modal-container">
        <div class="modal-box">
          <div
            v-if="modalVisible"
            class="modal-canvas"
          >
            <div
              class="modal-main"
              :style="{
                width: modelDetail?.width + 'mm',
                height: modelDetail?.height + 'mm',
              }"
            >
              <div class="main-left">
                <img
                  v-draggableResizable="{ disabled: true, transform: modelDetail?.position?.one }"
                  class="company-logo"
                  src="https://img1.rrzuji.cn/uploads/scheme/2312/22/m/C02YnUTzdXOgfkifhRFA.png"
                >
                <div
                  id="qrcode"
                  v-draggableResizable="{
                    disabled: true,
                    transform: modelDetail?.position?.two,
                  }"
                  class="qrcode"
                />
              </div>
              <div class="main-right">
                <div class="text-group">
                  <div
                    v-for="(textItem, textIndex) in modelDetail?.text_row"
                    :key="textIndex"
                    v-draggableResizable="{ disabled: true, transform: modelDetail?.position?.three?.[textIndex] }"
                    v-overflowEllipsis
                    class="label-text"
                  >
                    {{ textItem }}
                  </div>
                </div>
                <div style="display: flex; flex-direction: column; align-items: center">
                  <img
                    id="barcode"
                    v-draggableResizable="{ disabled: true, transform: modelDetail?.position?.four }"
                    class="barcode"
                  >
                  <div
                    v-draggableResizable="{ disabled: true, transform: modelDetail?.position?.five }"
                    class="serial-text"
                  >
                    {{ modelDetail?.code }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <template v-if="showEditModal">
          <a-form-item
            label="标签文本设置"
            name="text_row"
            :rules="[{ required: true, message: '请输入标签文本', trigger: 'blur' }]"
          >
            <div class="label_item">
              <template
                v-for="(item, index) in modelDetail?.text_row"
                :key="index"
              >
                <div class="text-line">
                  <a-input
                    v-model:value="formData.text_row[index]"
                    :maxlength="36"
                    placeholder="请输入"
                    show-count
                    type="text"
                  />
                </div>
              </template>
            </div>
          </a-form-item>
          <div v-if="formData.is_prefix">
            <div class="item-label">
              <div class="label-title">
                序列号设置
              </div>
            </div>
            <div class="size-item">
              <span class="size-label">前缀：</span>
              <a-input
                v-model:value="formData.prefix"
                :maxlength="6"
                placeholder="请输入前缀"
                show-count
                style="flex-grow: 1"
                type="text"
                @change="needPreview = true"
              />
            </div>
            <div class="size-item">
              <span class="size-label">起始值：</span>
              <a-input
                v-model:value="formData.start_value"
                :maxlength="15"
                placeholder="请输入起始值"
                show-count
                style="flex-grow: 1"
                type="text"
                @blur="onChangeStartValue"
              />
            </div>
            <a-checkbox
              v-model:checked="formData.remember_last"
              style="margin-bottom: 8px"
            >
              记忆最后使用值（不归位）
              <a-tooltip placement="bottom">
                <template #title>
                  <span>该功能可以记录上一次打印时最后一位流水号，在下一次打印时，可以接上一次打印的数字</span>
                </template>
                <QuestionCircleOutlined class="question-icon" />
              </a-tooltip>
            </a-checkbox>
          </div>
          <div class="btn-group">
            <a-button
              style="margin-right: 32px"
              @click="onCancelChange"
            >
              取消
            </a-button>
            <a-button
              type="primary"
              @click="onConfirmEdit"
            >
              确定修改
            </a-button>
          </div>
        </template>
        <template v-else>
          <div class="btn-group">
            <a-button
              style="margin-right: 32px"
              @click="showEditModal = true"
            >
              编辑
            </a-button>
            <a-button
              type="primary"
              @click="onOpenPrint"
            >
              打印
            </a-button>
          </div>
        </template>
      </div>
    </a-form>
    <template #footer>
      <a-button
        :loading="loading"
        style="margin-right: 8px"
        @click="isShow = false"
      >
        取消
      </a-button>
      <a-button
        :loading="loading"
        type="primary"
        @click="onConfirm"
      >
        确定
      </a-button>
    </template>
  </a-drawer>

  <PrintSettingModal
    v-model:visible="printSettingModdalVisible"
    :address-prefix="addressPrefix"
    :extra-data="extraData"
    :modal-detail="modelDetail"
    :order-id="Number(orderId)"
    :wo-id="woId"
    @refresh="onRefreshModal"
    @updata-serial-list="fetchGetOrderCode"
  />
</template>

<script setup lang="ts">
import { computed, nextTick, onUnmounted, reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { PlusSquareOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import JsBarcode from 'jsbarcode';
import qrcode from 'qrcode-generator';

import PrintSettingModal from '@/components/print-setting-modal/index.vue';

import { useCommodityList } from './config';
import {
  getDeviceCodeDetail,
  getModalDetail,
  getModalList,
  getOrderCode,
  saveItemList,
  saveModal,
  serialBind,
} from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
  orderId: {
    type: String,
    default: '',
  },
  addressPrefix: {
    type: String,
    default: '',
  },
  woId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible', 'refresh']);

const isShow = computed({
  get: () => props.visible,
  set: value => {
    emit('update:visible', value);
  },
});

const tipList = [
  '支持选择序列号模板进行打印，打印后会自动关联订单，支持解绑；',
  '【批量录入】支持手动输入或扫码枪录入，手动录入多个序列号可用逗号或回车分隔；',
  '自动生成打印：指按照序列号前缀以及起始值升序自动生成序列号打印；',
  '自定义打印：支持批量录入无序的序列号打印；',
  '图片上传：支持JPG，PNG等图片格式（手动修改文件后缀无效）；图片大小不可超过10M，分辨率不可高于10000*10000。',
];
const serialModalOptions = ref<Array<any>>([]);
const is_prefixModelOptions = ref<Array<any>>([]);
const options = ref([{ value: '1', label: '1' }]);
const formData = reactive({
  id: '',
  text_row: [],
  start_value: '',
  prefix: '',
  remember_last: false,
  serials: '',
  name: '',
  position: {},
  width: 0,
  height: 0,
  is_prefix: 1,
});
const modelDetail = ref({ qrcode_url: '', code: '', name: '', position: {}, width: 0, height: 0 });
const modalVisible = ref(true);
const showEditModal = ref(false);
const printSettingModdalVisible = ref(false);
const formRef = ref();
const serialInputRef = ref();
const loading = ref(false);
const deviceCodeDetail = ref<Record<string, any>>({});
const extraData = ref<Record<string, any>>({});
const {
  columns,
  commodifyList,
  pageConfig,
  uploadConfig,
  handleChangeCommodityValue,
  handleCommodityDelete,
  handleCommodityAddRow,
  commodifyListLoading,
  fileList,
  extractCodesAndItemNos,
} = useCommodityList(() => props.orderId);

onUnmounted(() => {
  //抽屉组件以及Modal组件一起使用会导致body的overflow样式被设为hidden
  document.body.style.overflow = 'auto';
});

watch(
  () => props.visible,
  val => {
    if (val) {
      fetchGetModalList();
      fetchGetOrderCode();
    } else {
      commodifyList.value = [];
      commodifyListLoading.value = false;
      fileList.value = [];
      pageConfig.errorText = '';
    }
  },
  { immediate: true },
);

watch(
  () => modelDetail.value?.qrcode_url,
  val => {
    //生成二维码
    if (val) {
      createQRcode(val);
    }
  },
);

watch(
  () => modelDetail.value?.code,
  val => {
    if (val) {
      //生成条形码
      nextTick(() => {
        createBarCode();
      });
    }
  },
);

//修改打印类型时自动变更模板
function onChangePrintType() {
  if (formData.is_prefix) {
    formData.id = serialModalOptions.value[0].value;
  } else {
    formData.id = is_prefixModelOptions.value[0].value;
  }
  showEditModal.value = false;
  onChangeSerialModal();
}

function createBarCode() {
  nextTick(() => {
    if (modalVisible.value === true) {
      JsBarcode('#barcode', modelDetail.value?.code, {
        format: 'CODE128A',
        displayValue: false,
      });
    }
  });
}

function createQRcode(url: string) {
  const qr = qrcode(4, 'L');
  qr.addData(url);
  qr.make();
  nextTick(() => {
    document.getElementById('qrcode') && (document.getElementById('qrcode')!.innerHTML = qr.createImgTag());
  });
}

function onChangeSerialModal() {
  modalVisible.value = false;
  getModalDetail({ id: formData.id }, props.addressPrefix).then(({ data }) => {
    formData.text_row = data.text_row;
    formData.start_value = data.start_value;
    formData.prefix = data.prefix;
    formData.remember_last = data.remember_last !== '0';
    modelDetail.value = data;
    modalVisible.value = true;
    createQRcode(modelDetail.value.qrcode_url);
    createBarCode();
  });
}

function fetchGetOrderCode() {
  getOrderCode({ order_id: props.orderId }, props.addressPrefix).then(({ data }) => {
    formData.serials = data;
  });
  getDetailInfo();
}

function onRefreshModal() {
  onChangeSerialModal();
  fetchGetOrderCode();
}

/**
 * @description 获取模板列表
 * @returns {*}
 */
function fetchGetModalList() {
  getModalList({ order_id: props.orderId }, props.addressPrefix).then(({ data }) => {
    //筛选序列号模板
    serialModalOptions.value = [];

    data.forEach((item: any) => {
      if (item.is_prefix === '1' || !item.is_prefix) {
        serialModalOptions.value.push({
          value: item.id,
          label: item.name,
        });
      } else if (item.is_prefix === '0') {
        is_prefixModelOptions.value.push({
          value: item.id,
          label: item.name,
        });
      }
    });
    formData.id = serialModalOptions.value.length && serialModalOptions.value[0].value;
    if (formData.is_prefix) {
      formData.id = serialModalOptions.value.length && serialModalOptions.value[0].value;
    } else {
      formData.id = is_prefixModelOptions.value.length && is_prefixModelOptions.value[0].value;
    }
    onChangeSerialModal();
  });
}

/**
 * @description 取消修改模板
 */
function onCancelChange() {
  onChangeSerialModal();
  showEditModal.value = false;
}

/**
 * @description 打印
 */
async function onOpenPrint() {
  if (deviceCodeDetail.value.type === 3) {
    const { codes, item_nos } = await handleSaveItem();
    extraData.value['codes'] = codes;
    extraData.value['item_nos'] = item_nos;
  }
  await nextTick();
  printSettingModdalVisible.value = true;
}

/**
 * @description 确定修改模板
 */
async function onConfirmEdit() {
  formData.name = modelDetail.value.name;
  formData.position = modelDetail.value.position ? JSON.stringify(modelDetail.value.position) : (undefined as any);

  formData.width = modelDetail.value.width;
  formData.height = modelDetail.value.height;
  //校验表单
  await formRef.value.validate();
  if (
    formData.text_row.every((item: string) => {
      return !item;
    })
  ) {
    message.error('请设置文本行');
    return;
  }

  if (!formData.prefix && formData.is_prefix) {
    message.error('请设置前缀');
    return;
  }
  if (!formData.start_value && formData.is_prefix) {
    message.error('请输入起始值');
    return;
  }
  formData.remember_last = formData.remember_last ? 1 : 0;
  saveModal(formData, props.addressPrefix).then(res => {
    message.success(res.message);
    showEditModal.value = false;
    onChangeSerialModal();
  });
}

async function handleSaveItem() {
  const data: Record<string, any> = {};
  const { codes: newCodes, item_nos: newItemNos } = extractCodesAndItemNos();
  data['codes'] = newCodes;
  data['item_nos'] = newItemNos;
  if (pageConfig.errorText) {
    await saveItemList({
      order_id: props.orderId,
      items: commodifyList.value,
    });
  }
  return data;
}

async function onConfirm() {
  let data: Record<string, any> = {
    codes: formData.serials,
    order_id: props.orderId,
    wo_id: props.woId,
  };
  if (deviceCodeDetail.value.type === 3) {
    const itemData = await handleSaveItem();
    data = { ...data, ...itemData };
  }
  loading.value = true;
  serialBind(data, props.addressPrefix)
    .then(() => {
      isShow.value = false;
      message.success('保存成功');
      emit('refresh', props.orderId, ['data', 'commodity_info']);
    })
    .finally(() => {
      loading.value = false;
    });
}

function onChangeStartValue(e: any) {
  // 获取输入的值
  const inputValue = e.target.value;

  // 使用正则表达式检查是否为数字且支持以零开头的格式
  const isValidInput = /^\d+$/.test(inputValue) || /^0\d+$/.test(inputValue);

  // 如果输入不符合要求，清空输入框
  !isValidInput && (formData.start_value = '');
}

async function getDetailInfo() {
  const { data } = await getDeviceCodeDetail({ order_id: props.orderId });
  deviceCodeDetail.value = data;
  const itemList = data?.item_upload?.item_list ?? [];
  if (itemList.length > 0) {
    fileList.value = data.item_upload.picture ? [data.item_upload.picture] : [];
    commodifyList.value = itemList;
  }
}
</script>

<style lang="less" scoped>
.notarization-info {
  margin-top: 24px;
  margin-bottom: 81px;
}

.notarization-info-table {
  width: 100%;
  border-color: #f0f0f0;
}

.notarization-info-table th,
td {
  padding: 16px;
}

.notarization-info-table th {
  width: 160px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  background-color: #fafafa;
}

.notarization-info-table td {
  color: rgba(6, 21, 51, 0.65);
}

.modal-container {
  padding: 16px;
  background-color: #f0f7ff;
  border-radius: 8px;

  .modal-box {
    width: 367px;
    height: 364px;
    margin-bottom: 10px;
    margin-left: 53.5px;
    overflow: hidden;

    .modal-canvas {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 200mm;
      height: 200mm;
      margin-top: 16px;
      background: rgba(6, 21, 51, 0.06);
      border-radius: 8px;
      transform: translateX(-194px) translateY(-213px) scale(0.485);

      .modal-main {
        position: relative;
        display: flex;
        justify-content: space-between;
        width: var(--defaultWidth);
        height: var(--defaultHeight);
        overflow: hidden;
        background: #fff;
        border-radius: 4px;
        transition: all 0.5s linear;

        .main-left {
          display: flex;
          flex-direction: column;
          flex-shrink: 0;
          justify-content: space-between;
          margin: 0 3px 3px 3px;

          .company-logo {
            flex-shrink: 0;
            width: 45px;
            height: 12px;
          }

          .qrcode {
            flex-shrink: 0;

            :deep(img) {
              width: 35px;
              height: 35px;
            }
          }
        }

        .main-right {
          display: flex;
          flex-direction: column;
          flex-shrink: 0;
          margin: 3px 3px 3px 0;

          .text-group {
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            width: fit-content;
            height: 23px;

            .label-text {
              flex-shrink: 0;
              width: fit-content;
              margin-bottom: 1px;
              color: rgba(0, 0, 0, 0.85);
              font-weight: 500;
              font-size: 6px;
              line-height: 6px;
            }
          }

          .barcode {
            flex-shrink: 0;
            width: 75px;
            height: 20px;
          }

          .serial-text {
            flex-shrink: 0;
            width: fit-content;
            font-size: 7px;
            line-height: 7px;
            text-align: center;
          }
        }
      }
    }
  }

  .label_item {
    .text-line {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    .close-icon {
      margin-left: 8px;
      color: gray;
    }

    .icon:hover {
      color: red;
      cursor: pointer;
    }
  }
}

.item-label {
  display: flex;
  align-items: center;
  width: 130px;
  padding-bottom: 8px;
}

.label-title::before {
  display: inline-block;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.size-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: rgba(6, 21, 51, 0.45);

  .size-label {
    min-width: 56px;
    margin-right: 34px;
  }
}

.btn-group {
  display: flex;
  justify-content: center;
}

.question-icon {
  width: 16px;
  height: 16px;
  color: #cbced5;
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-alert-warning) {
  align-items: flex-start;
}

:deep(.ant-alert-icon) {
  margin-top: 5px;
}

:deep(.ant-select) {
  width: 100% !important;
}

:deep(.ant-drawer-body) {
  padding: 24px !important;
}

.commodity-list {
  margin-bottom: 24px;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .title {
      color: rgba(6, 21, 51, 0.85);
      font-size: 14px;
    }

    .sub-title {
      color: #666;
      font-size: 12px;
    }
  }
  .table-wrap {
    margin-top: 16px;
  }
}
</style>
