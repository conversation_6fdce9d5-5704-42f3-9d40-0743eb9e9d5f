<template>
  <a-modal
    :destroy-on-close="true"
    title="兜底商交易登记"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formState"
      :rules="rules"
      style="width: 100%"
    >
      <a-form-item name="dealer_id">
        <template #label>
          <span> 兜底寄卖价格 </span>
          <a-button
            type="link"
            @click="handleAdd"
          >
            新增兜底商
          </a-button>
        </template>
        <a-select
          v-model:value="formState.dealer_id"
          :options="canvasserOptions"
          placeholder="请选择"
          @popup-scroll="cavasserScroll"
        />
      </a-form-item>
      <a-form-item
        label="兜底寄卖价格"
        name="dealer_price"
      >
        <a-input
          v-model:value="formState.dealer_price"
          :maxlength="7"
          placeholder="请选择"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { debounce } from 'lodash-es';
import { getAllCanvasserList, markDealer } from '../service';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'getTableList']);

const formState = ref({
  dealer_id: null,
  dealer_price: null,
});

const canvasserParams = reactive({
  page: 1,
  page_size: 20,
});
type TCanvasserData = Array<{ label: string; value: string }>;
const canvasserOptions = ref<TCanvasserData>([]);

//是否滚动加载数据
const isScroll = ref<boolean>(false);

const getCanvasserList = async () => {
  const res = await getAllCanvasserList(canvasserParams);
  isScroll.value = res.data.length === 20 ? true : false; //一页二十条数据进行请求
  if (res.data.length) res.data.forEach(item => canvasserOptions.value.push({ label: item.name, value: item.id }));
  canvasserParams.page++;
};

const cavasserScroll = debounce(() => {
  if (isScroll.value) {
    getCanvasserList();
  }
}, 200);

const onCancel = () => emits('update:visible', false);

//新增
const handleAdd = () => {
  window.parent.postMessage(
    {
      action: 'blank',
      href: '/super/recycle/dealer-info/index',
    },
    '*',
  );
};

//检验
const rules = {
  dealer_id: [{ required: true, message: '请选择兜底商名称' }],
  dealer_price: [{ required: true, message: '请输入兜底寄卖价格' }],
};

const formRef = ref<FormInstance>();
const handleOk = () => {
  formRef.value?.validateFields().then(async () => {
    const res = await markDealer({ order_id: props.orderId, ...formState.value });
    message.success(res.message || '登记成功');
    onCancel();
    emits('getTableList');
  });
};

onMounted(() => {
  getCanvasserList();
});
</script>
