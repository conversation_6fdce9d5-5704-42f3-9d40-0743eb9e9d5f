import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { apiGetAddRuleItem, apiGetDictionaryMenu } from '../services';

const modalTitle = ref('');
const modalId = ref('');
const modalLoading = ref(false);
function addRuleItem(params: any, callback: () => void) {
  modalLoading.value = true;
  apiGetAddRuleItem({
    ...params,
  })
    .then(() => {
      callback && callback();
    })
    .finally(() => {
      modalLoading.value = false;
    });
}
const mapIds = ref<string[]>([]);
function handleMapIds(ids: string[]) {
  const map_ids = ids.filter(id => !mapIds.value.includes(id));
  return map_ids.join();
}

// useOptions
const menuTreeOptions = ref<any[]>([]);
function isDisabled(isEnd: boolean, item: any, type: 'category' | 'spuModel') {
  if (item.requestType === type) {
    const childIds = item.child.map((child: any) => child.id);
    // 如果childIds的数据全在mapIds中，那么就是disabled
    return childIds.every((id: string) => mapIds.value.includes(id));
  } else if (isEnd) {
    return mapIds.value.includes(item.id);
  }
  return true;
}
function handleOptions(data: any[], type: 'category' | 'spuModel', isEnd = false): any[] {
  if (!data?.length) return [];
  return data.map((item: any) => {
    return {
      title: item.name,
      value: isEnd ? item.id : item.unique_mark,
      children: isEnd ? [] : handleOptions(item.child, type, item.requestType === type),
      disabled: isDisabled(isEnd, item, type),
    };
  });
}
/**获取类目字典 */
function getDictionaryMenu(type: 'category' | 'spuModel') {
  apiGetDictionaryMenu().then(res => {
    menuTreeOptions.value = handleOptions(res.data, type);
  });
}

function useCategoryModal() {
  const categoryVisible = ref(false);
  const categoryIds = ref<string[]>([]);
  function openCategoryModal(id: string, category: { name: string; id: string }[], selectedKey: string[]) {
    modalTitle.value = '添加类目';
    modalId.value = id;
    mapIds.value = [...selectedKey];
    categoryIds.value = category.map(item => item.id);
    getDictionaryMenu('category');
    categoryVisible.value = true;
  }
  function addCategoryRule(callback: () => void) {
    const params = {
      rule_id: modalId.value,
      type: '1',
      map_ids: handleMapIds(categoryIds.value),
    };
    addRuleItem({ ...params }, () => {
      message.success('操作成功');
      categoryVisible.value = false;
      callback();
    });
  }
  function changeCategoryIds(value: string[]) {
    if (!value.length) {
      categoryIds.value = [...mapIds.value];
    }
  }
  return {
    categoryVisible,
    categoryIds,
    openCategoryModal,
    addCategoryRule,
    changeCategoryIds,
  };
}
function useModelModal() {
  const modelVisible = ref(false);
  const modelIds = ref<string[]>([]);
  function openModelModal(id: string, model: { name: string; id: string }[], selectedKey: string[]) {
    getDictionaryMenu('spuModel');
    modalTitle.value = '添加型号';
    modalId.value = id;
    modelIds.value = model.map(item => item.id);
    mapIds.value = [...selectedKey];
    modelVisible.value = true;
  }
  function addModelRule(callback: () => void) {
    const params = {
      rule_id: modalId.value,
      type: '2',
      map_ids: handleMapIds(modelIds.value),
    };
    addRuleItem({ ...params }, () => {
      message.success('操作成功');
      modelVisible.value = false;
      callback();
    });
  }
  function changeModelIds(value: string[]) {
    if (!value.length) {
      modelIds.value = [...mapIds.value];
    }
  }
  return {
    modelVisible,
    modelIds,
    openModelModal,
    addModelRule,
    changeModelIds,
  };
}
export function useModal() {
  return {
    modalTitle,
    modalId,
    modalLoading,
    handleMapIds,

    menuTreeOptions,
    mapIds,
    useCategoryModal,
    useModelModal,
  };
}
