<template>
  <a-form
    ref="formRef"
    layout="vertical"
    :model="formState"
  >
    <a-form-item
      label="原因说明"
      name="apply_remark"
      :rules="[{ required: true }]"
    >
      <a-textarea
        v-model:value="formState.apply_remark"
        :auto-size="{ minRows: 2, maxRows: 5 }"
        placeholder="请输入"
      />
    </a-form-item>

    <a-form-item
      label="上传凭证"
      name="work_img"
      :rules="[
        {
          validator: validateVerify,
          trigger: 'change',
        }
      ]"
    >
      <RUpload
        v-model:value="formState.work_img"
        :remove-confirm="true"
        show-text="上传凭证"
        :upload-api="uploadToOssCompatibility"
        :upload-props="{
          multiple: true,
          maxCount: 6,
          accept: '.jpg,.jpeg,.png,.wav,.mp3,.aac,.m4a,.wma,.midi,.mp4,.3gp,.3gpp,.mp2v,.mov,.flv,.wmv,.avi,.rmvb,.rm'
        }"
      />
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Form } from 'ant-design-vue';
import { isAudio,isImage, isVideo } from '@rrzu/utils';
import { RUpload, useCompRef } from 'rrz-web-design';

import { uploadToOssCompatibility } from '@/utils/oss-helper';


const formRef = useCompRef(Form);
const formState = ref({
  apply_remark: null,
  work_img: [],
});

const validateVerify = async (_rule: any, value: any) => {
  console.log('value',value)
  if (value && value.length) {
    const isUploading = value.some((item) => {
      if (typeof item === 'string') {
        return false;
      }
      return item?.status === 'uploading';
    })
    return isUploading ? Promise.reject('正在上传凭证') : Promise.resolve();;
  } else return Promise.reject('请上传凭证');
};

const commit = () => {
  return new Promise(resolve => {
    formRef.value.validate().then(() => {
      const fileMap = formState.value.work_img.reduce((acc, item) => {
        if (isImage(item)) {
          acc.work_img.push(item);
        } else if (isVideo(item)) {
          acc.video.push(item);
        } else if(isAudio(item)){
          acc.audio.push(item);
        }
        return acc;
      }, {
        work_img: [],
        audio: [],
        video: [],
      });
      resolve({
        ...formState.value,
        work_img: fileMap.work_img.join(','),
        video: fileMap.video.join(','),
        audio: fileMap.audio.join(','),
      });
    });
  });
};

defineExpose({
  commit,
});
</script>
