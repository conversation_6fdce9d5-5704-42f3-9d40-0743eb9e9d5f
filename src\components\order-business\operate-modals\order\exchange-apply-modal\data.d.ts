import { EStockStatus } from './composables/use-check-stock';

export interface IAttrItem {
  id: number;
  name: string;
  value?: number;
  options: { label: string; value: number }[];
}

export interface IWarehouseInformation {
  sku_id?: string;
  warehouse_id?: number;
  warehouse_type?: number;
  inventoryStatus?: boolean;
  evaluatePrice?: string;
  showPrice?: boolean;
  deviceMoney?: string | boolean;
  inspectMoney?: string | boolean;
  sendMoney?: string | boolean;
  accessoryMoney?: string | boolean;
  lockMoney?: string | boolean;
  tagType?: string;
  tagText?: string;
  newTagText?: string;
}

export interface IFormData {
  type: number;
  cause: string;
  modalId?: number;
  skuId?: string;
  activate_only: boolean;
  isChecked: boolean;
  regulation: boolean;
  stockStatus?: EStockStatus;
}

export interface IInitData {
  orderId: string;
  serverId: string;
  category_id: string;
  brand_id: string;
  is_new: boolean;
  pdm_category_id: string;
  pdm_brand_id: string;
  pdm_model_id: string;
  replace_auth: boolean;
  order_status: string;
  is_checked: number;
  check_server: string;
  binding_code: boolean;
  item_num: string;
  sku_id: string;
  sku_info: string;
  device_auth: boolean;
  storage_memory_up_auth?: boolean;
  device_special_tag: number;
  is_new_online_server: boolean;
  one_device_code: string;
}

export interface IAccountState {
  loading: boolean;
  is_one_pay_account?: boolean;
  amount: { [key: string]: any };
}
