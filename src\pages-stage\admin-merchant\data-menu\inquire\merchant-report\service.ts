import { GET, POST } from '@/services/api';

export function apiGetOptions() {
  return GET(
    '/merchant/rDI/serverAssessment/myV2DistributeDate',
    {},
    {
      hostType: '<PERSON><PERSON><PERSON>',
    },
  );
}

export function apiGetReportDetail(data: any) {
  return POST('/merchant/rDI/serverAssessment/myV2TranscriptDetail', data, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
    hostType: '<PERSON><PERSON><PERSON>',
  });
}
