# CategoryMultipleCascader 商品分类级联多选

已全局引入，除了固定为多选其他配置都可再调整，针对传递给后端接口的特殊数据结构进行封装修改 CommodityCategoryCascader 组件的配置，常用于页面内列表头部的商品分类多级多选，选择的值转化为"层级\_分类 id"结构数组存储,如: [1_1,1_2,2_1,2_2,2_3]，选择的值只能根据绑定的数据置空，但不支持绑定值进行回显（回显数据需要包含所有父级 id，但是绑定值只包含最后一级 id，因此无法通过 value 进行回显），同样其余属性用法继承自 CommodityCategoryCascader，可查看对应组件文档使用。



### 默认配置

```ts
{
    maxTagCount: 'responsive',
    scene: EScene.PlatformOrder,
    changeOnSelect: true,
    asyncSearch: true,
    ...attrs,
    multiple: true,
}
```



### 在form-create中使用

项目公共组件form-create中配置时，fragmentKey配置'renderCustom'之后，component配置使用该组件即可，组件的相关属性同样在elProps中配置生效。

```ts
import { FormGroupItem } from '@/components/form-create/src/typing';
import CategoryMultipleCascader from '@/components/category-multiple-cascader';

export const operationRecordSearchFormGroup: FormGroupItem[] = [
  {
    key: 'spu_id',
    fragmentKey: 'renderInput',
    originProps: { label: '商品ID', name: 'spu_id' },
    elProps: { placeholder: '请输入', allowClear: true },
  },
  {
    key: 'category_id',
    fragmentKey: 'renderCustom',
    component: CategoryMultipleCascader,
    originProps: { label: '商品分类', name: 'category_id' },
    elProps: { level: 3 },
  },
}
```