import { ColumnProps } from 'ant-design-vue/es/table';

import { FormGroupItem } from '@/components/form-create/src/typing';

/**
 * 检索表单渲染配置
 */
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'title',
    originProps: { label: '页面名称', name: 'title' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'meta_keywords',
    fragmentKey: 'renderInput',
    originProps: { label: '搜索关键词', name: 'meta_keywords' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'meta_desription',
    originProps: { label: '搜索描述', name: 'meta_desription' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'status',
    fragmentKey: 'renderSelect',
    originProps: { label: '状态', name: 'status' },
    options: [
      { label: '发布中', value: 1 },
      { label: '审核失败', value: 2 },
      { label: '待审核', value: 3 },
    ],
    elProps: { placeholder: '请选择', allowClear: true, style: 'width: 100%;' },
  },
  {
    key: 'reason',
    originProps: { label: '未通过原因', name: 'reason' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'created_at',
    fragmentKey: 'renderRangePicker',
    originProps: { label: '创建时间', name: 'created_at' },
    elProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
      style: { width: '100%' },
    },
  },
];

export const columns: ColumnProps[] = [
  {
    dataIndex: 'index',
    title: '序号',
    key: 'title',
    customRender: ({ index }) => `${index + 1}`,
    width: '9%',
  },
  {
    dataIndex: 'title',
    title: '网页名称',
    key: 'title',
    width: '10%',
  },
  {
    dataIndex: 'meta_keywords',
    title: '搜索关键词',
    key: 'meta_keywords',
    ellipsis: true,
    width: '13%',
  },
  {
    dataIndex: 'meta_desription',
    key: 'meta_desription',
    title: '搜索描述',
    ellipsis: true,
    width: '13%',
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: '状态',
    customRender: ({ record }: any) =>
      `${record.status === 1 ? '发布中' : record.status === 2 ? '审核失败' : '待审核'}`,
    width: '9%',
  },
  {
    dataIndex: 'reason',
    key: 'reason',
    title: '未通过原因',
    width: '18%',
  },
  {
    dataIndex: 'created_at',
    key: 'created_at',
    title: '创建时间',
    width: '10%',
  },
  {
    dataIndex: 'action',
    title: '操作',
    fixed: 'right',
    key: 'operation',
    width: 200,
  },
];
