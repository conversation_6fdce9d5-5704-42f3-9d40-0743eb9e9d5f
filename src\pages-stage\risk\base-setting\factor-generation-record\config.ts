import { ColumnType } from 'ant-design-vue/lib/table';
export const columns: ColumnType[] = [
  {
    title: '订单号',
    width: 200,
    dataIndex: 'order_id',
    key: 'order_id',
  },
  {
    title: '请求节点',
    width: 200,
    dataIndex: 'req_node',
    key: 'req_node',
  },
  { title: '命中规则', dataIndex: 'rules', key: 'rules', width: 200 },
  { title: '风控因子', dataIndex: 'factors', key: 'factors', width: 200 },
  {
    title: '命中条件',
    dataIndex: 'rule_count',
    key: 'rule_count',
    width: 88,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
  },
  {
    title: '操作',
    key: 'operation',
    width: 120,
  },
];
