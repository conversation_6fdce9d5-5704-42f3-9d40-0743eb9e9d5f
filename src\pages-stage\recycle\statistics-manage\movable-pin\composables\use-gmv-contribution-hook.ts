import { ref } from 'vue';
import { IGmvContributionRes, IGmvContributionInfo } from '../data';
import { getGmvContributionData } from '../service';

export default function () {
  const loading = ref<boolean>(false);
  const info = ref<Partial<Omit<IGmvContributionRes, 'list'>>>({});
  const tableList = ref<IGmvContributionInfo[]>([]);

  const load = async (params?: any) => {
    loading.value = true;
    try {
      const res = await getGmvContributionData(params);
      const data: IGmvContributionRes = res.data;
      info.value = data;
      tableList.value = data.list;
    } finally {
      loading.value = false;
    }
  };

  load();

  return {
    loading,
    info,
    tableList,
    load,
  };
}
