import { POST, GET } from '@/services/api';

/** 查询用户数据 */
export function fetchOrderUserInfo(params: { order_id: string[]; check_auth?: boolean }) {
  return GET('/order/orderUserInfo', params, { hostType: 'Golang' });
}

/**
 * @description: 查看运营后台订单列表展开信息弹窗埋点
 * @param {any} data
 * @return {*}
 */
export function fetchLogList(data: any) {
  return POST('/super/query-user-info-log/log-list', data);
}
