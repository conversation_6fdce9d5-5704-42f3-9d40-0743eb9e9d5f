export interface ISearchParams {
  rs_order_id: string;
  rsp_order_id: string;
  order_pay_status: string;
  payment_flow_sn: string;
  category_brand_model: string;
  category: number[];
  pay_type: string | number[];
  custom_type: number[];
  start_at: string;
  end_at: string;
  source_type: number[];
  create_time: string[];
}

export interface ISaleBillList {
  rsp_order_id: string;
  source_type: string;
  pay_price: string;
  pay_type: string;
  flow_sn_type: string;
  payment_flow_sn: string;
  created_at: string;
  back_price: string;
  order_pay_status: string;
  order_price: string;
  rs_order_id: string;
  server_id: string;
  custom_type: string;
  model_id: string;
  brand_id: string;
  model_name: string;
  brand_name: string;
}

// 备注
export type RemarkType = {
  created_at: string;
  last_remark: string;
  remark_by: string;
  remark_num: number;
  remark_tag: string;
  union_id: string;
};

//收款单列表
export interface IReceiptList {
  order_id: string;
  server_id: string;
  coin_type: string;
  status: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  check_by: string;
  source_type: string;
  date: string;
  server_name: string;
  status_text: string;
  coin_type_text: string;
  pay_account_text: string;
  pay_type_text: string;
  payment_flow_sn: string;
  total_amount: string;
  total_pay_price: string;
  source_type_text: string;
}

export interface IReceiptConfirmList {
  date: string;
  source_order_id: string;
  server_name: string;
  order_pay_status_text: string;
  coin_type_text: string;
  pay_price: string;
  finish_pay_price: string;
  wait_pay_price: string;
}

export interface IReceiptConfirmObject {
  server_name: string;
  total_pay_price: string;
}
