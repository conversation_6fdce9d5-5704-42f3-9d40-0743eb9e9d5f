<template>
  <layout-shops-page
    :content-style="{ padding: '24px 24px 72px' }"
    :top-style="{ padding: '24px', borderBottom: '1px solid #DADCE0' }"
  >
    <template #title>
      <div
        class="title"
        @click="onCancel"
      >
        <ArrowLeftOutlined style="margin-right: 16px" />
        {{ handleTypeMap[handleType] }}
      </div>
    </template>
    <div
      v-if="(isEdit || isDetail) && rejectInfo"
      class="alert-wrapper"
    >
      <h4>审核驳回原因</h4>
      <p>{{ rejectInfo }}</p>
    </div>
    <div class="category-box">
      <div class="category-info">
        <p>以下类目需要按照平台要求上传行业资质材料</p>
        <p>{{ cateInfo?.one_name + ' > ' + cateInfo?.two_name }}</p>
      </div>
    </div>
    <a-spin :spinning="pageLoading">
      <template
        v-for="(item, index) in formState"
        :key="item.id"
      >
        <header class="header-title">
          {{ item.config_name }} <span class="tips">{{ item.config_prompt }}</span>
        </header>

        <div class="form-box">
          <RForm
            ref="rFromRef"
            v-model:value="formState[index]"
            :form-dependencies="formDependencies"
            :form-group="formGroup"
            :origin-props="{
              labelCol: { span: 3 },
            }"
          >
            <template
              v-if="isDetail"
              #server_credential_industry_image
            >
              <a-form-item
                label="资质图片"
                name="server_credential_industry_image"
                :required="true"
              >
                <ImgShow
                  img-key="url"
                  :imgs="formState[index].server_credential_industry_image"
                />
              </a-form-item>
            </template>
            <template #time>
              <div class="sceond-title">
                许可证有效期
              </div>
              <a-form-item
                label="开始日期"
                name="valid_start_at_label"
                :required="true"
              >
                <a-date-picker
                  v-model:value="formState[index].valid_start_at_label"
                  :disabled="isDetail"
                  :disabled-date="current => startDisabledDate(current, formState[index].valid_end_at_label)"
                  style="width: 60%"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
              <a-form-item
                label="截止日期"
                name="valid_end_at_label"
                :rules="[
                  {
                    required: !formState[index].longTerm,
                    message: '请选择截止日期',
                  },
                ]"
              >
                <a-date-picker
                  v-model:value="formState[index].valid_end_at_label"
                  :disabled="formState[index]?.longTerm || isDetail"
                  :disabled-date="current => endDisabledDate(current, formState[index].valid_start_at_label)"
                  style="width: 60%"
                  value-format="YYYY-MM-DD"
                />
                <a-form-item-rest>
                  <a-checkbox
                    v-model:checked="formState[index].longTerm"
                    class="checkbox-btn"
                    :disabled="isDetail"
                    @change="formState[index].valid_end_at_label = null"
                  >
                    长期
                  </a-checkbox>
                </a-form-item-rest>
              </a-form-item>
            </template>
          </RForm>
        </div>
      </template>
    </a-spin>

    <div
      v-if="!isDetail"
      class="bottom-box"
    >
      <a-space>
        <a-button
          class="bottom-btn"
          @click="onCancel"
        >
          取消
        </a-button>
        <a-button
          class="bottom-btn"
          :loading="sumbitLoading"
          type="primary"
          @click="onConfirm"
        >
          提交审核
        </a-button>
      </a-space>
    </div>
  </layout-shops-page>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { safeJsonStringify } from '@rrzu/utils';
import { RFormInstance } from 'rrz-web-design';

import { getCredentialDictFiledList } from '@/pages-stage/commodity/dict/qualification/components/qualification-conifg/service';
import { EQualificationFileType } from '@/pages-stage/commodity/dict/qualification/data.d';
import { TQualificationFiled } from '@/pages-stage/commodity/dict/qualification/data.d';
import { deepClone } from '@/utils/base';

import ImgShow from '../goods-qualification-detail/components/img-show.vue';
import { ECurrentDataType, EHandleType } from '../store-info/components/shop-qualification/data.d';
import UseCommonFun from './composables/use-common-fun';
import { formGroup, handleTypeMap } from './config';
import { TCateInfo, TIndustryFormState } from './data.d';
import { createCredentialIndustryApi, getIndustryInfoDetailApi, updateCredentialIndustryApi } from './service';
const rFromRef = ref<RFormInstance[]>();
const { endDisabledDate, startDisabledDate, getFormatImgs, handleResponeImgs, goBack } = UseCommonFun();
const pageLoading = ref(false);
const route = useRoute();
const handleType = computed(() => (route.query?.type as string) || EHandleType.DETAIL);
const isDetail = computed(() => handleType.value === EHandleType.DETAIL);
const isEdit = computed(() => handleType.value === EHandleType.EDIT);
const isRelease = computed(() => route.query?.dataType === ECurrentDataType.RELEASE);
const detailId = computed(() => Number(route.query?.id));
const cateInfo: Partial<TCateInfo> = route.query?.cateInfo ? JSON.parse(route.query?.cateInfo as string) : {};
const formDependencies = {
  server_credential_industry_image: [isDetail],
};

const formState = ref<TIndustryFormState[]>([]);

const originalData = ref<TIndustryFormState[]>();
function onCancel() {
  goBack(EQualificationFileType.INDUSTRY, -1);
}
const sumbitLoading = ref(false);
async function onConfirm() {
  if (safeJsonStringify(formState.value) === safeJsonStringify(originalData.value)) {
    message.info('数据未修改，请勿重复提交');
    return;
  }
  if (rFromRef.value) {
    const validationPromises = rFromRef.value.map(formInstance => {
      if (formInstance) {
        return formInstance.validate();
      }
    });
    await Promise.all(validationPromises);
  }
  try {
    sumbitLoading.value = true;
    const server_credential_industry_data: any[] = [];
    const length = formState.value.length;
    for (let index = 0; index < length; index++) {
      const element = formState.value[index];
      const server_credential_industry_image = await getFormatImgs(element.server_credential_industry_image);
      server_credential_industry_data.push({
        ...element,
        valid_end_at_label: element.longTerm ? '-1' : element.valid_end_at_label,
        server_credential_industry_image,
      });
    }
    const queryData = {
      id: detailId.value || undefined,
      secondary_category: cateInfo.target_id,
      server_credential_industry_data,
    };
    const api = isEdit.value ? updateCredentialIndustryApi : createCredentialIndustryApi;
    await api(queryData);
    message.success('提交成功', 1).then(() => {
      onCancel();
    });
  } finally {
    sumbitLoading.value = false;
  }
}

const rejectInfo = ref('');
async function getDetailInfo() {
  const { data } = await getIndustryInfoDetailApi({ id: detailId.value });
  //修改与详情获取当前正在使用的，审核记录获取当前正在审核的
  const currentKey = isRelease.value ? 'release_industry_version' : 'use_industry_version';
  const info = data[currentKey];
  rejectInfo.value = info.reject_reason;
  const temp = info.server_credential_industry_data?.map((item: any) => {
    return {
      dict_id: item?.dict_id,
      config_id: item?.config_id,
      config_name: item.config_name,
      valid_start_at_label: item?.valid_start_at_label,
      valid_end_at_label: Number(item.valid_end_at) === -1 ? '' : item?.valid_end_at_label,
      longTerm: Number(item.valid_end_at) === -1,
      server_credential_industry_image: handleResponeImgs(item.server_credential_industry_image),
    };
  });
  formState.value = temp;
  originalData.value = deepClone(temp);
}

async function getFieldConfig() {
  const { data } = await getCredentialDictFiledList({ secondary_category: cateInfo.target_id, page_size: -1 });
  if (data && data?.length > 0) {
    const temp: TIndustryFormState[] = [];
    data.forEach((item: TQualificationFiled) => {
      if (item.config_type === EQualificationFileType.INDUSTRY) {
        temp.push({
          dict_id: item.id,
          config_id: item.config_id,
          config_name: item.config_name,
          server_credential_industry_image: [],
          valid_end_at_label: '',
          valid_start_at_label: '',
          config_prompt: item.config_prompt,
          longTerm: false,
        });
      }
    });
    checkFiledHaveDelete(temp);
  }
}
//修改时需过滤掉已删除的
function checkFiledHaveDelete(filedList: TIndustryFormState[]) {
  const temp = deepClone(formState.value);
  const result = temp.filter((item: TIndustryFormState) =>
    filedList.some(filedItem => filedItem.config_id === item.config_id),
  );
  filedList = filedList.filter(
    (item: TIndustryFormState) =>
      !temp.length || !temp?.some((tempItem: TIndustryFormState) => tempItem.config_id === item.config_id),
  );
  result.push(...filedList);
  formState.value = result;
}

async function getPageInfo() {
  try {
    pageLoading.value = true;
    if (isEdit.value || isDetail.value) {
      await getDetailInfo();
    }
    if (cateInfo?.target_id && !isDetail.value) {
      await getFieldConfig();
    }
  } finally {
    pageLoading.value = false;
  }
}

onMounted(() => {
  getPageInfo();
});
</script>

<style scoped lang="less">
.title {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  text-align: left;
}
.alert-wrapper {
  width: 100%;
  margin-bottom: 24px;
  padding: 16px;
  word-wrap: break-word;
  background: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  h4 {
    margin-bottom: 2px;
    color: #ff4d4f;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
  p {
    margin-bottom: 2px;
  }
}
.category-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  .category-info {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    p {
      margin-bottom: 0;
    }
  }
}
.header-title {
  margin-bottom: 20px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 16px;
}
.header-title::before {
  position: relative;
  top: 2px;
  right: 5px;
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #3777ff;
  border-radius: 20px;
  content: '';
}
.tips {
  color: #faad14;
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
}
.form-box {
  width: 900px;
  margin-bottom: 24px;

  .sceond-title {
    margin-bottom: 16px;
    padding-left: 2px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  .sceond-title::before {
    display: inline-block;
    margin-right: 2px;
    color: #ff4d4f;
    font-size: 14px;
    line-height: 1;
    content: '*';
  }
  .checkbox-btn {
    margin-left: 20px;
  }
}
.bottom-box {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  height: 72px;
  background-color: #fff;
  box-shadow: 0 -2px 20px 0 rgba(0, 0, 0, 0.05);
  .bottom-btn {
    height: 40px;
    padding: 8px 32px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
