import { GET, POST } from '@/services/api';

// 取消订单
export function cancelOrder(orderId: string, type: number) {
  return GET(`/recycle-batch-order/cancel-order?orderId=${orderId}&reason_type=${type}`);
}

// 放弃回收
export function refuseOrder(orderId: string, type: number) {
  return GET(`/recycle-batch-order/refuse-purchased?order_id=${orderId}&reason_type=${type}`);
}

// 同意收款
export function confirmOrder(orderId: string) {
  return GET(`/recycle-batch-order/confirm-purchased?order_id=${orderId}`);
}
//取消原因选项
export function reasonFilter() {
  return GET('/super/recycle/batch-order/reason-filter');
}
//商家后台-退回地址详情
export function getReturnAddr(orderId: string) {
  return GET(`/recycle-batch-order/return-address?order_id=${orderId}`);
}
//商家后台-退回地址修改
export function updateReAddr(params: any) {
  return POST('/recycle-batch-order/update-return-address', params);
}
