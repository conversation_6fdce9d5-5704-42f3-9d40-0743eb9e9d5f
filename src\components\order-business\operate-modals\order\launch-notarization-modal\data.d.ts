export type NotarizationInfoType = {
  order_id?: string;
  name?: string;
  phone?: string;
  user_email?: string;
  address?: string;
  item_name?: string;
  sku_name?: string;
  item_num?: string;
  lease_time?: string;
  fenqi_money?: string;
  rental_money?: string;
  deposit_money?: string;
  deposit_has_pay?: string;
  buyout?: string;
  role?: string;
  sex?: number;
  birth?: string;
  identity_type?: string;
  id_card?: string;
  user_phone?: string;
  face_verify_time?: string;
  user_address?: string;
  certificate?: string;
  return_mark?: string; // 退回备注信息
  is_return?: boolean; // 是否退回
};

export type TAnnouncementReadResponse = {
  is_read: boolean;
  id: string;
  message_id: string;
  msg_type: number;
};
