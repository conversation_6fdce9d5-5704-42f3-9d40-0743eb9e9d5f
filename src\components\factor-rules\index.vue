<template>
  <div
    class="factor-rules"
    :style="{ marginBottom: data.length ? '16px' : '32px' }"
  >
    <a-button
      class="create-button"
      :disabled="disabled"
      type="dashed"
      @click="create"
    >
      <PlusOutlined />
      新增因子
    </a-button>
    <div :class="['flex-wrap', data.length ? 'mt-16' : 'mt-32']">
      <RelationLine
        v-model:value="relation"
        :disabled="disabled"
        :show="showRelationLine"
      />
      <div class="flex-con">
        <div
          v-for="item in data"
          :key="item.id"
          :class="item.error ? 'list-error' : ''"
        >
          <a-space class="flex-wrap flex-y-center factor-rules-item">
            <a-select
              v-model:value="item.value"
              :disabled="disabled"
              :options="finalOptions"
              placeholder="请选择"
              style="width: 200px"
              @change="(e, option) => handleChange(e, option, item)"
            />
            <a-space
              v-if="item.type"
              class="flex-wrap flex-y-center"
            >
              <span>{{ item.type === 'between' ? '介于' : '包含' }}</span>
              <a-time-range-picker
                v-if="item.value === 1"
                v-model:value="item.between"
                :disabled="disabled"
                :show-time="true"
                style="width: 300px"
                value-format="HH:mm:ss"
              />
              <template v-if="[2, 4, 5, 6, 10, 11].includes(item.value)">
                <a-select
                  v-model:value="item.include"
                  :disabled="disabled"
                  :filter-option="filterOption"
                  :mode="item.value !== 11 ? 'multiple' : ''"
                  :options="computedOptions(item.value)"
                  placeholder="请选择"
                  show-search
                  style="width: 200px"
                />
              </template>
              <div>
                <div
                  v-if="item.value === 3"
                  class="flex-wrap flex-y-center"
                >
                  <a-input-number
                    v-model:value="item.between[0]"
                    addon-after="小时"
                    :disabled="disabled"
                    :min="0"
                    :precision="0"
                    :step="1"
                    @blur="handleBlurValue(item)"
                  />
                  ～
                  <a-input-number
                    v-model:value="item.between[1]"
                    addon-after="小时"
                    :disabled="disabled"
                    :min="0"
                    :precision="0"
                    :step="1"
                    @blur="handleBlurValue(item)"
                  />
                </div>
              </div>
              <div v-if="[7, 8].includes(item.value)">
                <RTagInput
                  v-model:value="item.include"
                  :disabled="disabled"
                  :outer-bordered="true"
                />
              </div>
              <div v-if="item.value === 9 || item.value === 14">
                <CommodityCategoryCascader
                  v-model:value="item.include"
                  change-on-select
                  :disabled="disabled"
                  :is-must-child="true"
                  :level="item.value === 9 ? 2 : 4"
                  multiple
                  :placeholder="`请选择${item.value === 9 ? '二级类目' : '型号'}`"
                  :scene="EScene.PlatformGoods"
                  style="width: 300px"
                  @change="e => handleCascaderChange(e, item)"
                />
              </div>
              <div
                v-if="item.value === 12 || item.value === 13"
                class="flex-wrap flex-y-center"
              >
                <a-input-number
                  v-model:value="item.between[0]"
                  addon-before="T+"
                  :disabled="disabled"
                  :min="0"
                  :precision="0"
                  :step="1"
                />
                ～
                <a-input-number
                  v-model:value="item.between[1]"
                  addon-before="T+"
                  :disabled="disabled"
                  :min="0"
                  :precision="0"
                  :step="1"
                />
              </div>
            </a-space>
            <DeleteOutlined
              :class="[disabled ? 'is-disabled' : '']"
              @click="deleteItem(item.id)"
            />
          </a-space>
          <div
            v-if="item.error"
            class="item-error"
          >
            {{ item.error }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, PropType, ref, watch } from 'vue';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { debounce } from 'lodash-es';

import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import { EScene } from '@/utils/enums/commodity';

import RelationLine from './components/relation-line.vue';
import { cityOptions, deliveryTimeOptions, expressOptions, isSameCityOptions, sendOutOptions } from './config';
import { spuBindList } from './services';

const props = defineProps({
  optionsKey: {
    type: Array as PropType<number[]>,
    default: [],
  },
  value: {
    type: Array,
    default: () => [],
  },
  groupType: {
    type: String,
    default: 'and',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:value', 'update:groupType']);

const showRelationLine = ref(true);

const relation = ref('and');

// 1-当前发货时间  2-发货时效 3-物流时效 4-收发地关系 5-配送方式 6-寄出方式 7-商家ID
// 8-商品ID 9-类目 10-渠道商品库 11-同城商品库,12-最早起租日,13-默认起租日
const options = ref([
  { type: 'between', label: '当前时间', value: 1 },
  { type: 'include', label: '发货时效', value: 2 },
  { type: 'between', label: '物流时效', value: 3 },
  { type: 'include', label: '收发地关系', value: 4 },
  { type: 'include', label: '配送方式', value: 5 },
  { type: 'include', label: '快递寄出方式', value: 6 },
  { type: 'include', label: '商家ID', value: 7 },
  { type: 'include', label: '商品ID', value: 8 },
  { type: 'include', label: '二级类目', value: 9 },
  { type: 'include', label: '渠道商品库', value: 10 },
  { type: 'include', label: '同城商品库', value: 11 },
  { type: 'between', label: '最早起租日', value: 12 },
  { type: 'between', label: '默认起租日', value: 13 },
  { type: 'include', label: '型号', value: 14 },
]);

const conditionsItem = { value: undefined, type: undefined, between: [undefined, undefined], include: undefined };
const data = ref<any>(props.value);
const create = () => {
  data.value.push({
    id: Date.now() + Math.random(),
    ...conditionsItem,
  });
};

const handleChange = (e: any, option: any, item: any) => {
  const { value, type } = option;
  item.value = value;
  item.type = type;
  item.between = [undefined, undefined];
  item.include = undefined;
};

const channelGoodsOptions = ref([]);
const computedOptions = (value: number) => {
  switch (value) {
    case 2:
      return deliveryTimeOptions;
    case 4:
      return cityOptions;
    case 5:
      return sendOutOptions;
    case 6:
      return expressOptions;
    case 10:
      return channelGoodsOptions.value;
    case 11:
      return isSameCityOptions;
    default:
      return [];
  }
};

const finalOptions = computed(() => {
  return options.value.filter(item => props.optionsKey.includes(item.value));
});

const getSpuBindList = debounce(() => {
  spuBindList().then(res => {
    const { data } = res;
    channelGoodsOptions.value =
      data.map((item: any) => {
        return {
          value: item.id.toString(),
          label: item.title,
        };
      }) || [];
  });
}, 500);

const deleteItem = (id: any) => {
  data.value = data.value.filter((item: any) => item.id !== id);
};

const handleBlurValue = (item: any) => {
  if (item.between.every(val => val != null) && item.between[0] > item.between[1]) {
    item.error = '左边数值不能大于右边';
  } else {
    item.error = undefined;
  }
};

const handleCascaderChange = (e: any, item: any) => {
  if (e.length > 500) {
    item.error = '最多选择500个';
  } else {
    item.error = undefined;
  }
};

function filterOption(input: string, option: any) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) > -1;
}

onMounted(() => {
  getSpuBindList();
});

// 当数据变化时，通过 emit 通知父组件
watch(
  () => data.value,
  newVal => {
    emit('update:value', newVal);
  },
  { deep: true },
);
watch(
  relation,
  newVal => {
    emit('update:groupType', newVal);
  },
  { deep: true, immediate: true },
);
watch(
  () => props.value,
  newVal => {
    data.value = newVal || [];
  },
  { immediate: true }, // 立即执行一次，确保初始化时也赋值
);
</script>

<style lang="less" scoped>
.factor-rules {
  .create-button {
    margin-bottom: 20px;
  }
  .factor-rules-item {
    margin-bottom: 8px;
  }

  .list-error {
    position: relative;
    padding-bottom: 32px;

    .item-error {
      position: absolute;
      left: 242px;
      color: var(--ant-error-color);
    }
  }

  .is-disabled {
    opacity: 0.6;
    user-select: none;
    pointer-events: none;
  }

  .mt-16 {
    margin-top: 16px;
  }
  .mt-32 {
    margin-top: 32px;
  }
}
</style>
