import { ColumnType } from 'ant-design-vue/lib/table';
import { IModel } from './data';
import { AuditStatus, TableToolTips } from '../../clue-manage/config';

export const requierListColumns: ColumnType[] = [
  {
    title: '商铺id',
    key: 'server_id',
    dataIndex: 'server_id',
    width: 100,
  },
  {
    title: '商家名称',
    key: 'server_name',
    dataIndex: 'server_name',
    width: 260,
  },
  {
    title: '联系号码',
    key: 'phone',
    dataIndex: 'phone',
    width: 140,
  },
  {
    title: '设备型号及数量',
    key: 'demand_content',
    dataIndex: 'demand_content',
    width: 400,
    customRender: ({ value }) => {
      const data = JSON.parse(value);
      let str = '';
      data.forEach((data: IModel, index: number, arr: IModel[]) => {
        str += `${data.model} ${data.memory} ${data.new} ${data.count}台`;
        if (index != arr.length - 1) str += '、';
      });
      return TableToolTips(str);
    },
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
    width: 150,
    customRender: ({ value }) => {
      return TableToolTips(value);
    },
  },
  {
    title: '处理状态',
    key: 'status',
    dataIndex: 'status',
    width: 120,
    fixed: 'right',
    customRender: ({ text }: { text: number }) => {
      const STATUS = {
        0: '待跟进',
        1: '已跟进',
      };
      return AuditStatus({ value: text, backarray: ['#FAAD14', '#00C8BE'], status: STATUS });
    },
  },
];
