import { TableProps } from 'ant-design-vue';

import { EEnterCategory } from './data.d';

export const enterCategoryMap = {
  [EEnterCategory.MobilePhone]: '手机',
  [EEnterCategory.Computer]: '电脑',
  [EEnterCategory.NonMobileComputer]: '非手机电脑',
};

export const imageProps = [
  'offline_license_image',
  'offline_rent_contract',
  'offline_store_video',
  'offline_purchase_certificate',
  'commitment',
  'online_promotion_image',
  'promote_account_information',
];

export const conditionTableColumns: TableProps['columns'] = [
  {
    title: '需要满足的条件',
    dataIndex: 'condition_name',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];

export const storeTypes = [
  {
    value: '11',
    label: '线下门店',
  },
  {
    value: '10',
    label: '线上门店',
  },
];

export const companyTypes = [
  {
    value: '1',
    label: '公司',
  },
  {
    value: '2',
    label: '个体工商户',
  },
];

export const onlineCompanyTypes = [
  {
    value: '10',
    label: '自媒体',
  },
  {
    value: '11',
    label: '网店',
  },
];

export const offlineStoreTypes = [
  {
    value: '10',
    label: '沿街门店',
  },
  {
    value: '11',
    label: '楼内门店',
  },
];
