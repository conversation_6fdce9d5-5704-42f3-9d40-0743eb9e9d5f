import { Form } from 'ant-design-vue';
import type { validateInfos } from 'ant-design-vue/lib/form/useForm';
import { reactive } from 'vue';

export interface IOptionItem {
  title: string;
  desc: string;
  images: string[];
  expand?: boolean;
}

export interface IOptionForm {
  state: IOptionItem;
  validateInfos: validateInfos;
  validate: <T = any>(names?: any, option?: any) => Promise<T>;
  resetFields: (newValues?: any) => void;
}

export default function (defaultData?: IOptionItem): IOptionForm {
  const state = reactive<IOptionItem>({
    title: '',
    desc: '',
    images: [],
  });
  Object.assign(state, defaultData);

  const rules = {
    title: [
      {
        required: true,
        message: '请输入评估项标题',
      },
    ],
    images: [
      {
        validator: (_: any, value: string[]) => {
          if (!state.desc.trim()) return Promise.resolve();
          else if (value.length) return Promise.resolve();
          else return Promise.reject('请至少配置一张图片');
        },
      },
    ],
  };

  const { resetFields, validate, validateInfos } = Form.useForm(state, rules);

  return {
    state,
    resetFields,
    validate,
    validateInfos,
  };
}
