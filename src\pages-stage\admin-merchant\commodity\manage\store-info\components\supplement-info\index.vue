<script setup lang="ts">
import { onMounted, ref, shallowRef } from 'vue';
import { ContainerOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';

import LogDrawer from './components/log-drawer.vue';
import { formGroup, renderMap } from './config';
import { getIsServerOperate, getSupplementInfo } from './services';

const hasSave = ref(false);

const descriptionGroup = shallowRef([]);

function formatOriginData(data) {
  const result: Record<string, any> = {};
  for (const key in data) {
    result[key] = data[key]?.label;
  }
  return result;
}

function getDescriptionGroup(formGroup, config, record) {
  const result = [];
  formGroup.forEach(item => {
    const { key, children = [], childrenDisplay } = item;
    const { label, is_read, is_edit, value } = config[key] || {};
    if (is_read || is_edit) {
      const otherInfo = record[`${key}_other`] || record[`${key}_info`] || '';
      const valueLabel = renderMap[key]
        ? renderMap[key](label, record)
        : (label || '-') + (otherInfo ? `（${otherInfo}）` : '');
      result.push({
        ...item,
        value: valueLabel,
      });
    }
    if (children.length && childrenDisplay?.({ [key]: value })) {
      result.push(...getDescriptionGroup(children, config, record));
    }
  });
  return result;
}

onMounted(() => {
  Promise.all([getIsServerOperate(), getSupplementInfo({ edit_channel: '21', port: '3' })]).then(
    ([{ data }, { data: config }]) => {
      hasSave.value = data.save || data.submit;
      descriptionGroup.value = getDescriptionGroup(formGroup, config, formatOriginData(config));
    },
  );
});
</script>

<template>
  <div class="container">
    <a-space style="margin-bottom: 24px">
      <a-button
        type="primary"
        @click="$router.push('/merchant/commodity/manage/supplement-info?role')"
      >
        {{ hasSave ? '修改资料' : '填写资料' }}
      </a-button>
      <a-button @click="$refs.logDrawerRef?.open()">
        <ContainerOutlined />
        <span>修改记录</span>
      </a-button>
      <a-tooltip title="每月提交经营档案资料次数上限为2次">
        <QuestionCircleOutlined style="color: var(--ant-warning-color); font-size: 16px" />
      </a-tooltip>
    </a-space>

    <a-descriptions
      bordered
      class="custom-descriptions"
      :column="2"
      :content-style="{}"
      :label-style="{
        width: '300px',
      }"
      title="商家经营档案"
    >
      <a-descriptions-item
        v-for="item in descriptionGroup"
        :key="item.key"
        :label="item.label"
      >
        {{ item.value }}
      </a-descriptions-item>
    </a-descriptions>

    <LogDrawer ref="logDrawerRef" />
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 8px 0 24px;

  .custom-descriptions {
    :deep(.ant-descriptions-header) {
      margin: 0;
      padding: 16px 24px;
      background: #fafafa;
      border: 1px solid rgba(6, 21, 51, 0.04);
      border-bottom: none;
    }

    :deep(.ant-descriptions-view) {
      border-radius: 0;
    }
  }
}
</style>
