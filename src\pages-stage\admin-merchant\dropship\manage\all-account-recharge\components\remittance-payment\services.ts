import { GET, POST } from '@/services/api';

export function getViewData(params: { id: string | number }) {
  return GET('/supply-chain-account-corporate-pay/view', params);
}

// 检测子账户充值是否重复提交
export function checkRepeatSubmit(data: any) {
  return POST('/supply-chain-account-corporate-pay/check-repeat', data);
}
export function getAlipayAccountList() {
  return GET('/alipay-server-account/list');
}
