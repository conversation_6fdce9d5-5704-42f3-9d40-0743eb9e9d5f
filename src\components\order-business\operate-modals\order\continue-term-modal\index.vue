<template>
  <a-modal
    v-model:visible="bindVisible"
    class="tenancy-modal"
    :ok-button-props="{ disabled: !isPass }"
    title="续租操作"
    @ok="submit"
  >
    <a-spin :spinning="loading">
      <div class="alert-box">
        <a-alert
          message="续租无需下单，只需延长租期，通过免密代扣/预授权代扣扣款，点击预览显示续租价格"
          type="warning"
        />
      </div>
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="开始租期"
          name="time_start"
        >
          <a-date-picker
            v-model:value="formData.time_start"
            disabled
            style="width: 100%"
            value-format="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item
          label="结束租期"
          name="time_end"
        >
          <a-date-picker
            v-model:value="formData.time_end"
            :allow-clear="false"
            :disabled-date="disabledDate"
            style="width: 100%"
            value-format="YYYY-MM-DD"
            @change="changePriceImpact"
          />
          <template
            v-if="tip && !isPass"
            #extra
          >
            <div style="color: #ff4d4f">
              {{ tip }}
            </div>
          </template>
        </a-form-item>
        <a-form-item
          v-show="formData.time_end"
          class="full"
          name="time_end"
        >
          <a-row>
            <a-col
              class="ant-form-item-label"
              :span="6"
            >
              <a-button
                type="link"
                @click="previewRenewPrice"
              >
                价格预览：
              </a-button>
            </a-col>
            <a-col :span="18">
              <a-input-number
                v-model:value="formData.pre_price"
                disabled
                :precision="2"
                style="width: 100%"
              />
            </a-col>
          </a-row>
        </a-form-item>
      </a-form>

      <ExpandBox
        v-if="buyoutPriceList?.length"
        class="list-content"
        :target-height="46"
      >
        <template #custom="{ isOutRange, hasExtendMore, toggleExtendMore }">
          <div class="flex-wrap flex-y-center flex-x-justify">
            <div class="title">
              购买尾款(以当前已付租金计算)
            </div>
            <div
              v-if="isOutRange"
              class="flex-wrap flex-y-center"
              style="color: #3777ff; cursor: pointer"
              @click="toggleExtendMore"
            >
              <div>{{ hasExtendMore ? '收起' : '查看更多' }}</div>
              <img
                alt="图标"
                :src="
                  hasExtendMore
                    ? 'https://img1.rrzuji.cn/uploads/scheme/2301/09/m/XE9z8QeDuAw9y3QO9Grs.png'
                    : 'https://img1.rrzuji.cn/uploads/scheme/2301/09/m/AhlqYzpmw9nw41GSTW2X.png'
                "
                style="width: 16px; height: 16px"
              >
            </div>
          </div>
        </template>
        <template #content>
          <div
            v-for="(item, index) in buyoutPriceList"
            :key="index"
            class="flex-wrap flex-x-justify list-item"
          >
            <div>
              {{ item.period_num }}期
              <span style="color: rgba(6, 21, 51, 0.65)">({{ item.start_at }} ~ {{ item.end_at }})</span>
            </div>
            <div>{{ item.need_pay }} 元</div>
          </div>
        </template>
      </ExpandBox>
    </a-spin>
  </a-modal>

  <a-modal
    v-model:visible="nextModalVisible"
    cancel-text="上一步"
    class="after-assist-modal"
    ok-text="确定续租"
    title="续租申请"
    :width="640"
    @cancel="nextModalVisible = false"
    @ok="onNextModalTipsOk"
  >
    <a-spin
      class="modal-content"
      :spinning="loading"
    >
      <a-alert
        message="温馨提示"
        show-icon
        style="margin-bottom: 24px"
        type="warning"
      >
        <template #icon>
          <info-circle-filled
            :style="{
              fontSize: '14px',
              marginTop: '4px',
            }"
          />
        </template>
        <template #description>
          <div class="description-box">
            <span>该订单当前剩余预授权金额小于续租金额，请谨慎续租。</span>
            <span>1.续租后该订单无法通过预授权进行代扣，续租后若逾期无法收回租金的风险较高；</span>
            <template v-if="nextModalTipsData.is_merchant">
              <span>2.申请续租后，系统将生成续租账单发送给用户，需引导用户通过主动支付整期续租账单，支付后才续租成功；若存在未支付的续租账单，则无法再次申请新的续租账单。</span>
              <span>3.订单支持最长续租天数：剩余预授权金额可支持续租天数+30天</span>
            </template>
            <template v-else>
              <span>2.申请续租后，系统将生成续租账单发送给用户，用户可选择授权免密代扣方式进行分期支付账单；或是主动支付整个续租账单，续租才生效；</span>
              <span>3.用户若无授权免密代扣，且存在未支付的续租账单，则无法再新申请续租。</span>
            </template>
          </div>
        </template>
      </a-alert>
      <div class="info">
        <div
          v-if="nextModalTipsData.is_merchant"
          class="base-data"
        >
          <div class="sub-title">
            续租信息
          </div>
          <div class="good-info">
            <div class="good-info-title">
              当前申请续租天数：{{ nextModalTipsData?.renew_apply_day }}天
            </div>
            <div class="row">
              <div
                class="row-label"
                style="flex-basis: max-content"
              >
                剩余预授权金额可支持续租天数：
              </div>
              <div class="row-value">
                {{ nextModalTipsData?.withhold_day }}日
              </div>
            </div>
            <div class="row">
              <div
                class="row-label"
                style="flex-basis: max-content"
              >
                最长续租天数：
              </div>
              <div class="row-value">
                {{ nextModalTipsData?.max_renew_total_day }}日
              </div>
            </div>
            <div class="row">
              <div
                class="row-label"
                style="flex-basis: max-content"
              >
                可申请最晚结束租期：
              </div>
              <div class="row-value">
                {{ nextModalTipsData?.max_renew_date }}
              </div>
            </div>
          </div>
        </div>
        <div class="form-data">
          <div class="info-form">
            <div class="buyout">
              <div class="sub-title">
                续租账单
              </div>
              <div class="buyout-item">
                <div
                  v-for="(item, index) in nextModalTipsData.bills"
                  :key="index"
                  class="bills-text"
                >
                  <div>
                    <span class="num">{{ item.period_num }}期</span>
                    <span style="color: rgba(6, 21, 51, 0.45)">（{{ item.time_start }}～{{ item.time_end }}）</span>
                  </div>
                  <div>{{ item.bill_money }}元</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { createVNode, h, PropType, ref, watch } from 'vue';
import { FormInstance, message, Modal } from 'ant-design-vue';
import { InfoCircleFilled, QuestionCircleFilled } from '@ant-design/icons-vue';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import moment from 'moment';

import ExpandBox from '@/components/expand-box';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

import {
  createRenew,
  getPreviewRenewPrice,
  orderContinueRenew,
  orderContinueValidate,
  previewRenewBuyoutPrice,
} from './services';

interface IExtraData {
  renew_start?: string;
  renew_end?: string;
}

interface IFormData {
  time_start?: string;
  time_end?: string;
  pre_price?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const loading = ref(false);
const formData = ref<IFormData>({});
const bindVisible = useVModel(props, 'visible', emit);

/** 禁用日期，禁用开始租期之后的日期 */
const disabledDate = (current: Dayjs) => {
  return dayjs(current).isBefore(dayjs(formData.value.time_start));
};

watch(
  bindVisible,
  () => {
    if (bindVisible.value) {
      validateContinue().then(value => {
        // 检查可续租后，初始化表单
        if (value) {
          formData.value = {
            time_start: props.extraData.renew_start,
            time_end: props.extraData.renew_end,
          };
          changePriceImpact();
        } else {
          bindVisible.value = false;
        }
      });
    }
  },
  { immediate: true },
);

/** 检查订单是否可续租 */
function validateContinue() {
  loading.value = true;
  return orderContinueValidate({ order_id: props.orderId })
    .then(res => {
      if (!res.data?.hasCanContinue) {
        message.error('该订单不支持续租!');
        return false;
      }
      if (res.data?.hasRenewNotClosed !== 0) {
        Modal.warning({
          title: h('div', { style: { fontWeight: 'bold' } }, '续租提示'),
          icon: createVNode(QuestionCircleFilled),
          content: h(
            'div',
            { style: { color: 'rgba(6, 21, 51, 1)' } },
            '当前订单存在未处理「续租工单」，请先处理「续租工单」再执行续租操作；',
          ),
        });
        return false;
      }
      return true;
    })
    .catch(e => {
      console.error(e);
      return false;
    })
    .finally(() => {
      loading.value = false;
    });
}

interface IBuyoutPriceItem {
  need_pay: number;
  period_num: number;
  end_at: string;
  start_at: string;
}

/** 购买价列表 */
const buyoutPriceList = ref<IBuyoutPriceItem[]>([]);
/** 提示信息 */
const tip = ref('');
/** 是否通过 */
const isPass = ref(false);

/** 更新续租预估价格、提示、允许判断、购买价格列表 */
function changePriceImpact() {
  checkTimeData();
  previewRenewPrice();
}

/** 根据续租最后日期获取对应续租购买价列表与提示 */
function checkTimeData() {
  previewRenewBuyoutPrice({
    order_id: props.orderId,
    end_date: formData.value.time_end,
  }).then((res: any) => {
    const { buyout_need_pay_data, rental_gt_buyout_check } = res.data;
    buyoutPriceList.value = buyout_need_pay_data.map(item => {
      item.start_at = moment.unix(item.start_at).format('YYYY-MM-DD');
      item.end_at = moment.unix(item.end_at).format('YYYY-MM-DD');
      return item;
    });
    tip.value = rental_gt_buyout_check?.msg || '';
    isPass.value = Boolean(rental_gt_buyout_check?.is_pass);
  });
}

/** 根据续租最后日期获取对应续租价格预览 */
function previewRenewPrice() {
  loading.value = true;
  getPreviewRenewPrice({
    id: props.orderId,
    end: formData.value.time_end,
  })
    .then(res => {
      formData.value.pre_price = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

const formRef = ref<FormInstance>();
const rules = {
  time_start: [{ required: true, message: '开始租期不能为空', trigger: 'blur' }],
  time_end: [{ required: true, message: '结束租期不能为空', trigger: 'blur' }],
};
/** 下一步弹窗 */
const nextModalVisible = ref(false);
/** 下一步弹窗信息 */
const nextModalTipsData = ref<any>({});

/** 提交 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      order_id: props.orderId,
      time_start: formData.value.time_start,
      time_end: formData.value.time_end,
      pre_price: formData.value.pre_price,
    };
    orderContinueRenew({ biz_content: JSON.stringify(params) })
      .then(res => {
        // ================= 需要下一步确认 =================
        if (res.data.is_apply_renew_action) {
          nextModalVisible.value = true;
          nextModalTipsData.value = res.data;
          return;
        }
        // =================== 续租成功 =================
        message.success(res.data.title);
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data', 'all_remark']);
      })
      .catch(err => {
        console.error(err);
        if (err.status === 1001) {
          Modal.error({
            title: '申请续租失败',
            content: '该订单已存在未生效的续租申请，请引导用户进行付款后再发起新的续租需求',
          });
        } else if (err.status === 1002) {
          Modal.error({
            title: '申请续租失败',
            content: h('div', {}, [
              h('p', '失败原因：当前申请续租天数超过该订单最长可申请续租时间'),
              h(
                'p',
                { style: { color: '#ff4d4f' } },
                '(1) 由于订单申请的续租总金额大于剩余可支配预授权金额时，系统将限制该订单最长可续租天数；',
              ),
              h('p', { style: { color: '#ff4d4f' } }, '(2) 订单最长可续租天数=剩余预授权金额可支持天数+30天。'),
            ]),
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

function onNextModalTipsOk() {
  loading.value = true;
  createRenew({
    order_id: props.orderId,
    time_end: formData.value.time_end,
  })
    .then(() => {
      message.success('操作成功');
      emit('refresh', props.orderId, ['data', 'all_remark']);
      bindVisible.value = false;
      nextModalVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<style scoped lang="less">
.tenancy-modal {
  .alert-box {
    margin-bottom: 24px;
  }

  .full {
    .ant-form-item-label {
      text-align: right;

      .ant-btn {
        padding: 0;
      }
    }

    :deep(.ant-form-item-control) {
      width: 100%;
      max-width: 100%;
    }
  }

  .list-content {
    padding-top: 24px;
    border-top: 1px solid rgba(6, 21, 51, 0.06);

    .title {
      color: rgb(6, 21, 51);
      font-weight: bold;
    }

    .list-item {
      margin-top: 8px;
      padding: 12px 16px;
      color: rgb(6, 21, 51);
      background: #f5f7fa;
      border-radius: 4px;
    }
  }
}

.after-assist-modal {
  .sub-title {
    margin-bottom: 12px;
    padding-left: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: bold;

    &::before {
      position: relative;
      top: 2px;
      right: 5px;
      display: inline-block;
      width: 4px;
      height: 14px;
      background: #3777ff;
      border-radius: 2px;
      content: '';
    }
  }

  .row {
    display: flex;
    align-items: center;
    margin-top: 8px;

    &-label {
      flex-basis: 84px;
      color: rgba(6, 21, 51, 0.45);
    }

    &-value {
      flex: 1;
      color: rgba(6, 21, 51, 0.85);
    }
  }

  .modal-content {
    height: 500px;
    padding-right: 8px;
    overflow-y: auto;
  }

  .info {
    .base-data {
      .good-info {
        padding: 16px;
        background-color: #f9f9fb;
        border-radius: 4px;

        &-title {
          color: rgba(6, 21, 51, 0.85);
          font-weight: bold;
        }
      }

      .normal-info {
        margin-top: 16px;
        padding: 0 16px;
      }
    }

    .form-data {
      margin-top: 24px;

      .info-form {
        &-content {
          padding: 0 16px;
        }

        .custom-list-item {
          width: 500px;
          margin: 6px 0;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .custom-flex {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .err_msg {
          margin-top: 10px;
          color: #ff4d4f;
        }

        .right-row {
          display: block;
          width: 16px;
          height: 16px;
          margin: 0 8px;
          background: url('https://img1.rrzuji.cn/uploads/scheme/2212/01/m/L2ZjcybULbgHg1PRUOhl.png') no-repeat;
          background-size: 100% 100%;
        }

        .buyout {
          margin-bottom: 12px;
        }

        .buyout-item {
          display: flex;
          flex-direction: column;
          margin-top: 8px;
          padding: 8px 16px;
          background-color: rgb(245, 247, 250);
          border-radius: 4px;
        }
      }
    }
  }
}

.bills-text {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;

  .num {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
  }
}

.description-box {
  display: flex;
  flex-direction: column;
}
</style>
