<template>
  <a-spin
    :spinning="loading"
    tip="Loading..."
  >
    <card-box
      hidden-handle
      title="今日付款"
    >
      <template #suffix>
        （总计：{{ chartInfo.count_num }}元）
      </template>
      <template #datePicker>
        <span>选择日期：</span>
        <a-range-picker
          v-model:value="searchParams.date"
          value-format="YYYY-MM-DD"
          @change="createCharts"
        />
      </template>
      <flex-container
        :columns-number="2"
        :gap="8"
      >
        <card-data-item
          title="待付金额"
          unit="元"
          :value="chartInfo.wait_pay_num"
        >
          <template #suffix>
            <a-tooltip placement="top">
              <template #title>
                <span>统计时间内，个人账单+企业账单表中，全部待结算账单定价金额+结算失败账单定价金额的总和，精确到小数点后俩位，四舍五入。</span>
              </template>
              <InfoCircleOutlined />
            </a-tooltip>
          </template>
        </card-data-item>
        <card-data-item
          title="已付金额"
          unit="元"
          :value="chartInfo.finish_pay_num"
        >
          <template #suffix>
            <a-tooltip placement="top">
              <template #title>
                <span>统计时间内，个人账单+企业账单表中，全部已结算账单定价金额的总和，精确到小数点后俩位，四舍五入。</span>
              </template>
              <InfoCircleOutlined />
            </a-tooltip>
          </template>
        </card-data-item>
        <template #body>
          <div
            v-if="!isEmpty"
            id="pay_dataConatiner"
          />
          <EmptyBlock
            v-else
            class="empty-box"
          />
        </template>
      </flex-container>
    </card-box>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { IPayData } from '../data.d';
import usePillarData from '../composables/use-pillar-data';
import FlexContainer from './flex-container.vue';
import CardBox from './card-box.vue';
import CardDataItem from './card-data-item.vue';
import EmptyBlock from './empty-block.vue';

const defaultTimeType = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];

const searchParams = ref({
  date: defaultTimeType,
});

const { chartInfo, loading, isEmpty, createCharts } = usePillarData<IPayData>({
  element: 'pay_dataConatiner',
  url: '/super/recycle/data-count/order-pay-data',
  searchForm: searchParams.value,
});
</script>

<style lang="less" scoped>
.containerCss() {
  width: 100%;
  height: 300px;
  margin-top: 40px;
}

#pay_dataConatiner {
  .containerCss();
}

.empty-box {
  .containerCss();
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
