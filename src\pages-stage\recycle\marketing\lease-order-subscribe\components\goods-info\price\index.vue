<template>
  <span
    class="img"
    :style="{ backgroundImage: `url(${src})` }"
  >
    <a-image
      :preview="preview"
      :src="src"
    />
  </span>
</template>

<script setup lang="ts">
defineProps({
  src: {
    type: String,
    required: true,
  },
  preview: {
    type: Boolean,
    default: true,
  },
});
</script>

<style scoped lang="less">
.img {
  display: inline-block;
  width: 72px;
  height: 72px;
  overflow: hidden;
  background-color: rgb(238, 238, 238);
  background-size: cover;
  :deep(.ant-image) {
    width: 100%;
    height: 100%;
    img {
      display: none;
    }
  }
}
</style>
