import { createVNode } from 'vue';
import { useRoute } from 'vue-router';
import { Modal } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import { checkOrderClosing, superCheckOrderClosing } from '../services';

interface IExtraData {
  status: number;
  twice_deposit: boolean;
}

export function useGenBill(orderId: string) {
  const route = useRoute();
  const domain = route.query.origin || window.location.origin;
  const isSuper = route.query.role === 'super';

  /** 显示生成订单前提示订单存在「未完成」的重新冻结押金申请弹窗 */
  function depositTips() {
    return new Promise(resolve => {
      Modal.confirm({
        title: '温馨提示',
        content: '订单「代扣」或「支付」后，将「撤销」当前订单的重新冻结押金申请',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '你确认生成账单吗？',
        okType: 'danger',
        onOk() {
          resolve();
        },
      });
    });
  }

  /** 警告弹出 */
  function warningAlert(status: number) {
    return new Promise(resolve => {
      Modal.confirm({
        title: '生成账单',
        content: {
          2: '该用户上传的信息匹配不通过，请谨慎操作',
          9: '该用户上传的信息还未审核，请先去审核才能操作',
          11: '该用户的认证资料已过期，请谨慎操作',
        }[status],
        icon: createVNode(ExclamationCircleOutlined),
        okText: {
          2: '执意操作',
          9: '去审核',
          11: '执意操作',
        }[status],
        okType: 'danger',
        onOk() {
          resolve(null);
        },
      });
    });
  }

  /** 生成账单 */
  async function goGebBill(extra?: IExtraData) {
    if (!extra) {
      message.error('查看生成账单失败');
      console.error('api未返回（button_list(11).extra）');
      return;
    }
    const api = isSuper ? superCheckOrderClosing : checkOrderClosing;
    // 检测状态
    const { data } = await api({
      orderId: orderId,
    });
    if (data?.isClose) {
      message.success('该订单已关闭或正在申请关闭');
      return;
    }
    // 弹出温馨提示：订单「代扣」或「支付」后，将「撤销」当前订单的重新冻结押金申请
    if (extra.status !== 9 && extra.twice_deposit) {
      await depositTips();
    }
    // 用户信息存在问题，弹出警告，用户需再次确认
    if ([2, 9, 11].includes(extra.status)) {
      await warningAlert(extra.status);
    }
    if (isSuper) {
      window.open(domain + '/super/v2-bill/generate?order_id=' + orderId);
    } else {
      window.open(domain + '/v2-bill/generate?order_id=' + orderId);
    }
  }

  return { goGebBill };
}
