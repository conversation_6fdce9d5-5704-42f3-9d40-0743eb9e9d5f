<template>
  <a-spin
    :spinning="loading"
    tip="Loading..."
  >
    <card-box
      page-url="/super/recycle/data-count/warehouse-page"
      title="库存情况"
    >
      <flex-container
        :columns-number="3"
        :gap="8"
      >
        <card-data-item
          title="库存金额"
          :title-style="itemTitleStyle"
          unit="元"
          :value="chartInfo.purchased_price"
          :value-style="itemValueStyle"
        />
        <card-data-item
          :precision="0"
          title="仓库库存"
          :title-style="itemTitleStyle"
          unit="台"
          :value="chartInfo.stock"
          :value-style="itemValueStyle"
        />
        <card-data-item
          title="设备周转时效"
          :title-style="itemTitleStyle"
          unit="时"
          :value="chartInfo.device_turnover_time"
          :value-style="itemValueStyle"
        />
        <template #body>
          <a-select
            v-model:value="typeValue"
            :options="typeOptions"
            style="width: 180px; margin-top: 8px"
            @change="createCharts"
          />
          <div
            v-show="!isEmpty"
            id="inventory_dataConatiner"
          />
          <EmptyBlock
            v-show="isEmpty"
            class="empty-box"
          />
        </template>
      </flex-container>
    </card-box>
  </a-spin>
</template>

<script lang="ts" setup>
import { IInventoryData } from '../data.d';
import useThetaData from '../composables/use-theta-data';
import FlexContainer from './flex-container.vue';
import CardBox from './card-box.vue';
import CardDataItem from './card-data-item.vue';
import EmptyBlock from './empty-block.vue';

const {
  itemTitleStyle,
  itemValueStyle,
  typeOptions,
  typeValue,
  chartInfo,
  createCharts,
  loading,
  isEmpty,
} = useThetaData<IInventoryData>({
  element: 'inventory_dataConatiner',
  url: '/super/recycle/data-count/warehouse-detail-data',
  totalText: '今日库存成本',
  totalField: 'purchased_price',
});
</script>

<style lang="less" scoped>
.containerCss() {
  width: 100%;
  height: 320px;
  margin: 0 auto;
  margin-top: 24px;
}
#inventory_dataConatiner {
  .containerCss();
}
.empty-box {
  .containerCss();
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
