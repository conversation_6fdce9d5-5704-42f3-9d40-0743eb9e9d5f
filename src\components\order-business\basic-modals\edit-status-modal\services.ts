import { GET, POST } from '@/services/api';

/**
 * @description: 修改状态
 * @param {any} data
 * @return {*}
 */
export function editOrderStatus(data: any) {
  const params = {
    order_id: String(data.orderId),
    order_status: Number(data.order_status),
  };
  return POST('/order/transfer', params, { hostType: 'Golang' });
}

/** 获取订单是否有取消限（运营端） */
export function superGetCancelOrderLimit(params: any) {
  return GET('/super/v3-order/order-cancel-compatible-supply', params);
}
