import { POST, RequestConfig } from '@/services/api';
const config: RequestConfig = {
  headers: { 'content-type': 'application/json;charset=utf-8' },
  hostType: '<PERSON><PERSON><PERSON>',
};
export function ApiGetResult(data: any) {
  return POST('/merchant/rDI/serverAssessment/result', data, config);
}

export function ApiGetTabel(data: any) {
  return POST('/merchant/rDI/serverAssessment/order', data, config);
}

export function ApiExport(data: any) {
  return POST('/merchant/rDI/serverAssessment/export', data, config);
}

export function ApiGetDistributeDate() {
  return POST('/merchant/rDI/serverAssessment/myDistributeDate', {}, config);
}
