/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2023-03-15 10:13:33
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-22 10:23:36
 * @FilePath: \lease_digital_intelligence\src\pages\assess-board\composables\use-search.ts
 * @Description: 搜索栏
 */

import { reactive } from 'vue';

import type { TabsItemType } from '../data';

type TabsType = {
  activeKey: number;
  tabsList: TabsItemType[];
};

export default function (): TabsType {
  const tabs: TabsType = reactive({
    activeKey: 0,
    tabsList: [
      {
        label: '风控通过发货率',
        value: 0,
        key: 'risk_pass_delivery_rate',
        icon: 'https://img1.rrzuji.cn/uploads/scheme/2311/07/m/yJbcDKmxCdgqMuDXeJV7.png',
        status: false,
        statusKey: 'is_pass_risk_pass_delivery_rate',
      },
      {
        label: '自发订单发货超48H',
        value: 0,
        key: 'no_mark_deliver_pass_48h_num',
        icon: 'https://img1.rrzuji.cn/uploads/scheme/2311/07/m/Hiut4XKSmdVDsbpzCf2y.png',
        status: false,
        statusKey: 'is_pass_no_mark_deliver_pass_48_h_num',
      },
      {
        label: '风控通过发货前平均收取期数',
        value: 0,
        key: 'avg_risk_pass_sent_pay_num',
        icon: 'https://img1.rrzuji.cn/uploads/scheme/2311/07/m/9V8rDYPk2ckXBqv774cz.png',
        status: false,
        statusKey: 'is_pass_avg_risk_pass_sent_pay_num',
      },
      {
        label: '响应平均时效(工作时间)',
        value: 0,
        key: 'avg_valid_response_second',
        icon: 'https://img1.rrzuji.cn/uploads/scheme/2401/03/m/HtDK3Lh3N4Tig655F0xI.png',
        status: false,
        statusKey: 'is_pass_avg_valid_response_second',
      },
    ],
  });

  return tabs;
}
