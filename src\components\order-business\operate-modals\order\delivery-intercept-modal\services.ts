import { AxiosResponse } from 'axios';

import { GET, POST } from '@/services/api';
import { getCookie } from '@/utils/cookies';

import { getRole } from './config';

/** 订单拦截&物流拦截 */
export function sendIntercept(data: any) {
  const isSuper = getRole() === 'super';
  let url = '/order/order-intercept';
  if (isSuper) {
    url = '/super/warehouse/oms-order-work/order-intercept';
  }
  return POST(url, data);
}

/**
 * 商家端 物流拦截&发货拦截限制发起次数
 * @param data orderId
 * @returns
 */
export function limitSendApi(data: any) {
  return GET('/order/surplus-can-apply-num', data);
}

/**
 * 获取拦截模板
 * @param params order_id
 * @returns
 */
export function getInterceptTemplateApi(params: any) {
  const isSuper = getRole() === 'super';
  return GET(`${isSuper ? '/super' : ''}/v3-order/get-intercept-template`, params);
}

export function createInterceptWorkOrder(data: any): Promise<AxiosResponse<any>> {
  const isSuper = getRole() === 'super';
  return POST(`${isSuper ? '/super' : ''}/work-order-inter/create`, data, {
    headers: {
      Authorization: getCookie('Go-Token'),
    },
  });
}
