<template>
  <a-modal
    :footer="null"
    title="执行日志"
    :visible="visible"
    width="714px"
    @cancel="onCancel"
  >
    <a-table
      class="table-box"
      :columns="execLogColumns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="page"
      :scroll="{ y: '100%' }"
      @change="tableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <div class="status-block">
            <div
              class="point"
              :style="{ background: record.status === 1 ? '#00C8BE ' : '#FF4A57 ' }"
            />
            {{ record.status_text }}
          </div>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup lang="ts">
import { onMounted } from 'vue';
import { useTable } from '@/hook/component/use-table';
import { execLogColumns } from '../config';
import { getLogList } from '../service';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  versionId: {
    type: String,
    default: '0',
  },
});

const emits = defineEmits(['update:visible']);

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  api: getLogList,
  totalKey: 'data.pageInfo.count',
  formatSearchValue: () => {
    const queryParams = {
      version_id: props.versionId,
    };
    return queryParams;
  },
});

const onCancel = () => emits('update:visible', false);

onMounted(() => {
  getTableList('search');
});
</script>
<style scoped lang="less">
.status-block {
  display: flex;
  align-items: center;
  .point {
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 8px;
  }
}
</style>
