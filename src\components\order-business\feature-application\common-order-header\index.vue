<template>
  <a-space
    :size="8"
    wrap
  >
    <template #split>
      <a-divider
        style="margin: 0"
        type="vertical"
      />
    </template>
    <!-- 关联订单 -->
    <span
      v-if="orderData.base_info?.related_order"
      style="cursor: pointer"
      @click="checkSuppleOrder"
    >
      <img
        alt="关联订单"
        class="icon"
        src="https://img1.rrzuji.cn/uploads/scheme/2304/14/o/xFxG1DLUxg6moTwEU33T.png"
      >
    </span>
    <!-- 拒单 -->
    <span
      v-if="orderData.base_info?.support_prosecute"
      style="cursor: pointer"
      @click="checkRefuseOrder"
    >
      <img
        alt="拒单"
        class="icon"
        src="https://img1.rrzuji.cn/uploads/scheme/2304/14/o/80diJACxadvyXjeRprNa.png"
      >
    </span>
    <!--  续租次数  -->
    <div
      v-if="orderData.base_info?.renew_num"
      class="text-link"
      @click="checkRenewHistory"
    >
      续租{{ orderData.base_info.renew_num }}次
    </div>
    <!-- 续租开关 -->
    <div v-if="orderData.base_info?.show_renewal">
      <span>订单续租：</span>
      <span
        class="text-link"
        @click="changeRenewEnable"
      >
        {{ orderData.base_info?.buyout_rate ? '开启' : '关闭' }}
      </span>
    </div>
    <!-- 订单结算比例 -->
    <div>订单结算比例：{{ orderData.base_info?.settlement_ratio || '暂无' }}</div>
    <!-- 快递物流（寄出快递） -->
    <a-space v-if="orderData.base_info?.logistic_number">
      <span>寄出单号:</span>
      <span v-if="String(orderData.base_info?.logistic_number) === '-1'">无</span>
      <span
        v-else
        class="text-link"
        @click="checkLogisticTracks(orderData.logistics_relation_info.is_iss_order)"
      >
        {{ orderData.base_info.logistic_number }}
      </span>
      <span v-if="orderData.base_info?.logistic_name">【{{ logisticCourierName }}】</span>
      <FormOutlined
        class="text-link"
        @click="changeLogistic"
      />
    </a-space>
    <!-- 快递物流（寄回快递） -->
    <a-space
      v-if="orderData.base_info?.return_logistic_number && String(orderData.base_info?.return_logistic_number) !== '-1'"
    >
      <span>寄回单号:</span>
      <span
        class="text-link"
        @click="checkReturningTracks"
      >
        {{ orderData.base_info.return_logistic_number }}
      </span>
      <span v-if="orderData.base_info?.return_logistic_number">【{{ returningCourierName }}】</span>
    </a-space>
    <!-- 发货时间 -->
    <div v-if="showSendTime">
      发货时间：{{ orderData.base_info.send_date }}
    </div>
    <!-- 续租每期费率（运营端需要控制显示） -->
    <a-space v-if="!isSuper || orderData.base_info?.support_renewal">
      <span>续租每期费率：{{ orderData.base_info.buyout_rate || '不支持续租' }}</span>
      <QuestionCircleOutlined
        class="text-link"
        @click="openTipModal"
      />
    </a-space>
    <!-- 寄出方式 -->
    <a-space>
      <span>寄出方式：{{ sendTypeText }}</span>
    </a-space>
    <a-space>
      <span>归还方式：{{ returnTypeText }}</span>
      <a-tooltip>
        <template #title>
          取用户下单商品时的配送方式配置
        </template>
        <QuestionCircleOutlined class="text-link" />
      </a-tooltip>
    </a-space>
  </a-space>
</template>

<script setup lang="ts">
import { computed, createVNode, h } from 'vue';
import { useRoute } from 'vue-router';
import { Modal } from 'ant-design-vue';
import { FormOutlined, QuestionCircleFilled, QuestionCircleOutlined } from '@ant-design/icons-vue';

import { basicModal } from '@/components/order-business/basic-modals';
import { useLogisticOption } from '@/components/order-business/composables/use-logistic-option';
import type { IOrderItem } from '@/components/order-business/typing';
import { useUnityModal } from '@/components/unity-modal';
import { EOrderStatus } from '@/typing';

import { getLogisticsTrajectory } from './services';

const props = defineProps<{
  orderData: IOrderItem;
}>();

const isSuper = useRoute().query.role === 'super';
const { onToggleComponent, visible } = useUnityModal();

/** 显示发货时间 */
const showSendTime = computed(() => {
  const { order_status, send_date } = props.orderData.base_info || {};
  return (
    [
      EOrderStatus.WaitReceive,
      EOrderStatus.CloseSystem,
      EOrderStatus.CloseUser,
      EOrderStatus.CloseServer,
      EOrderStatus.Deleted,
    ].includes(order_status) && send_date
  );
});

/** 查看关联订单 */
function checkSuppleOrder() {
  onToggleComponent(basicModal.supple, {
    orderId: props.orderData.base_info?.order_id,
  });
}

/** 查看拒单 */
function checkRefuseOrder() {
  // 仅管理员使用
  if (isSuper) {
    onToggleComponent(basicModal.refundList, {
      orderId: props.orderData.base_info?.order_id,
    });
  }
}

/** 查看续租历史记录 */
function checkRenewHistory() {
  onToggleComponent(basicModal.renewLog, {
    orderId: props.orderData.base_info?.order_id,
  });
}

/** 修改续租启用 */
function changeRenewEnable() {
  onToggleComponent(basicModal.renewEnable, {
    orderId: props.orderData.base_info?.order_id,
    renewContinue: props.orderData.base_info?.buyout_rate ? 1 : 0,
  });
}

function openTipModal() {
  Modal.warning({
    title: h('div', { style: { fontWeight: 'bold' } }, '续租费率说明'),
    icon: createVNode(QuestionCircleFilled),
    content:
      '仅生效中状态下，订单租期+续租租期>1年1个月时触发，按“购买价*（1+费率*应计费率期数”）”影响续租后该订单的购买价',
  });
}

// =============================  物流信息  ===============================
const { logisticOptions } = useLogisticOption();
/** 寄出快递公司名字 */
const logisticCourierName = computed(() => {
  return logisticOptions.value?.find(item => String(item.value) === props.orderData.base_info?.logistic_name)?.label;
});
/** 寄回快递公司名字 */
const returningCourierName = computed(() => {
  return logisticOptions.value?.find(
    item => String(item.value) === props.orderData.base_info?.return_logistic_shipper_code,
  )?.label;
});

/** 获取寄出快递的物流轨迹 */
async function getLogisticTrajectoryHtml() {
  const res: any = await getLogisticsTrajectory({
    biz_content: JSON.stringify({
      type: props.orderData.base_info?.logistic_name,
      number: props.orderData.base_info?.logistic_number,
      phone: props.orderData.base_info?.logistic_receive_phone,
    }),
    ordinary_delivery: 1,
  });
  return res?.html;
}

/** 查看寄出快递物流轨迹 */
function checkLogisticTracks(isShanSong: boolean) {
  if (isShanSong) {
    // 闪送物流轨迹
    onToggleComponent(basicModal.shanSongLogisticsTrackModal, {
      orderId: props.orderData.base_info?.order_id,
    });
  } else {
    onToggleComponent(basicModal.html, {
      title: '物流轨迹',
      getContent: getLogisticTrajectoryHtml,
    });
  }
}

/** 修改物流信息 */
function changeLogistic() {
  const images = props.orderData.base_info?.logistic_image ? JSON.parse(props.orderData.base_info.logistic_image) : [];
  onToggleComponent(basicModal.logisticInfo, {
    orderId: props.orderData.base_info?.order_id,
    shipperCode: props.orderData.base_info?.logistic_name,
    logisticCode: props.orderData.base_info?.logistic_number,
    orderLogisticImage: images,
  });
}

/** 获取寄回快递的物流轨迹 */
async function getReturningTrajectoryHtml() {
  const res: any = await getLogisticsTrajectory({
    biz_content: JSON.stringify({
      type: props.orderData.base_info?.return_logistic_shipper_code,
      number: props.orderData.base_info?.return_logistic_number,
      phone: props.orderData.base_info?.return_logitic_phone,
    }),
    ordinary_delivery: 1,
  });
  return res?.html;
}

/** 查看寄回快递物流轨迹 */
function checkReturningTracks() {
  onToggleComponent(basicModal.html, {
    title: '物流轨迹',
    getContent: getReturningTrajectoryHtml,
  });
}

/** 获取物流方式文本 */
function getLogisticTypeText(type: string | number, fullDays: number, typeMap: Record<string | number, string>) {
  if (!type) return ''; // 如果类型为空，返回空字符串
  const maplength = Object.keys(typeMap).length;
  if ((maplength > 4 && Number(type) === 7) || (maplength === 4 && Number(type) === 4)) {
    return `租满${fullDays}天寄出包邮`;
  }
  return typeMap[type] || '';
}

const sendTypeMap = {
  1: '包邮',
  2: '到付',
  3: '上门安装',
  4: '到店自取',
  5: '统一运费',
  6: '运费模板',
  7: '租满XX天寄出包邮',
  8: '上门自提',
  9: '同城闪送'
};
const returnTypeMap = {
  1: '包邮',
  2: '自付',
  3: '无需归还',
  4: '租满XX天归还包邮',
};
/** 寄出方式的文本 */
const sendTypeText = computed(() => {
  const { send_type, send_full_days } = props.orderData.base_info?.order_spu_logistics || {};
  return getLogisticTypeText(send_type, send_full_days, sendTypeMap);
});

/** 归还方式的文本 */
const returnTypeText = computed(() => {
  const { return_type, return_full_days } = props.orderData.base_info?.order_spu_logistics || {};
  return getLogisticTypeText(return_type, return_full_days, returnTypeMap);
});

function closeModal() {
  visible.value = false;
}

function openTrackModal() {
  if (props.orderData.base_info?.logistic_number) {
    checkLogisticTracks();
  }
}

defineExpose({
  openTrackModal,
  closeModal,
});
</script>
