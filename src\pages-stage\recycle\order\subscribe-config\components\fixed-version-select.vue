<template>
  <div class="version-box">
    <span>规则版本：</span>
    <a-select
      v-if="versionValue"
      v-model:value="versionValue"
      :disabled="props.isEdit"
      :field-names="{
        label: 'version',
        value: 'id',
      }"
      :options="versionOptions"
      placeholder="请选择"
      style="width: 120px; margin-right: 16px"
      @change="onChange"
    />
    <div
      v-if="currentVersionItem"
      class="version-record"
    >
      创建记录：{{ currentVersionItem.created_name }}
      {{ currentVersionItem.created_at }}
    </div>
    <template v-if="defaultVersionId === versionValue">
      <tempplate v-if="props.isEdit">
        <a-button
          :disabled="submitLoading"
          style="width: 65px; margin-right: 8px"
          @click="onCancel"
        >
          取消
        </a-button>
        <a-button
          :disabled="disabledSubmit"
          :loading="submitLoading"
          style="width: 65px"
          type="primary"
          @click="onSubmit"
        >
          保存
        </a-button>
      </tempplate>
      <template v-else>
        <a-button
          style="width: 65px"
          type="primary"
          @click="onEdit"
        >
          编辑
        </a-button>
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { getRuleVersionSelect } from '../service';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: true,
  },
  submitLoading: {
    type: Boolean,
    default: false,
  },
  disabledSubmit: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:loading', 'update:is-edit', 'onLoad', 'confirm']);

let requestParams: any = null;

const versionValue = ref<string | null>(null);
const versionOptions = ref<any[]>([]);

const defaultVersionId = ref<string>('');

const currentVersionItem = computed(() => {
  const item = versionOptions.value.find((item: any) => item.id === versionValue.value);
  return item;
});

const init = async (type = 'init', params?: any) => {
  if (params) {
    requestParams = params;
  }
  emits('update:loading', true);
  emits('update:is-edit', false);
  if (type === 'init') {
    const res = await getVersionList();
    const data = res.data || [];
    versionOptions.value = data;
    versionValue.value = data[0].id || null;
    defaultVersionId.value = data[0].id || null;
  }
  triggerOnLoad();
};

const triggerOnLoad = () => {
  emits('onLoad', {
    id: currentVersionItem.value.id,
    cb: () => {
      emits('update:loading', false);
    },
  });
};

const validateVersionList = async () => {
  const res = await getVersionList(false);
  if (res.data.length != versionOptions.value.length) {
    message.error('当前规则的版本发生的修改，请重新获取最新版本情况');
    return false;
  }
  return true;
};

const getVersionList = (isLoading = true): Promise<any> => {
  if (isLoading) emits('update:loading', isLoading);
  return getRuleVersionSelect(requestParams);
};

const onChange = () => {
  init('change');
};

const onEdit = () => {
  emits('update:is-edit', true);
};

const onCancel = () => {
  init();
};

const onSubmit = async () => {
  const result = await validateVersionList();
  if (!result) return;
  emits('confirm');
};

defineExpose({
  init,
  validateVersionList,
});
</script>

<style lang="less" scoped>
.version-box {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background: #f5f7fa;
  border-radius: 4px;
  .version-record {
    position: relative;
    display: inline-flex;
    flex: 1;
    align-items: center;
    padding-left: 17px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    &::after {
      position: absolute;
      top: 50%;
      left: 0;
      width: 1px;
      height: 12px;
      margin-top: -6px;
      background: rgba(6, 21, 51, 0.25);
      content: '';
    }
  }
}
</style>
