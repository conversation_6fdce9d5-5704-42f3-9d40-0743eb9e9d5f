import { ref } from 'vue';

export default function useDrag() {
  // 当前拖拽的数据
  const activeDragData = ref<unknown[]>([]);
  const onDragStart = (ev: any, data: unknown[]) => {
    activeDragData.value = data;
    // 拖拽的index
    const index = ev.target.dataset.index;
    // 标记
    ev.dataTransfer.setData('text', index);
  };
  const onDrop = (ev: any) => {
    ev.preventDefault();
    const dragIdx = ev.dataTransfer.getData('text');
    const dropIdx = ev.target.dataset.index;
    const arr: any[] = [...activeDragData.value];
    arr.splice(dragIdx, 1);
    arr.splice(dropIdx, 0, activeDragData.value[dragIdx]);
    activeDragData.value.splice(0, activeDragData.value.length, ...arr);
  };
  const onDragOver = (ev: any) => {
    ev.preventDefault();
  };
  const onDragLeave = (ev: any) => {
    ev.target.classList.remove('dragover_bottom');
    ev.target.classList.remove('dragover_top');
  };
  return {
    activeDragData,
    onDragStart,
    // onDrag,
    onDrop,
    onDragOver,
    // onDragEnter,
    onDragLeave,
  };
}
