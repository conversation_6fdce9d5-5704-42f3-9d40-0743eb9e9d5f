<template>
  <card-box
    page-url="/super/recycle/data-count/index"
    title="用户情况"
  >
    <flex-container
      :columns-number="2"
      :gap="8"
    >
      <card-data-item
        :precision="0"
        title="新增下单用户(个人)"
        unit="人"
        :value="info.single_user_count"
      />
      <card-data-item
        :precision="0"
        title="新增下单用户(企业)"
        unit="人"
        :value="info.batch_user_count"
      />
      <card-data-item
        :precision="0"
        title="下单用户总数"
        unit="人"
        :value="info.user_count"
      />
      <card-data-item
        :precision="0"
        title="平均估价"
        unit="元"
        :value="info.average_evaluate_price"
      />
    </flex-container>
  </card-box>
</template>

<script lang="ts" setup>
import useData from '../composables/use-data';
import FlexContainer from './flex-container.vue';
import CardBox from './card-box.vue';
import CardDataItem from './card-data-item.vue';

const { info } = useData();
</script>
