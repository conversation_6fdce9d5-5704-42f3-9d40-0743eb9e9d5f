<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px' }"
    title="地址库"
    :top-style="{ padding: '24px 24px 12px 24px' }"
  >
    <a-tabs
      v-model:activeKey="activeKey"
      @change="tabChange"
    >
      <template #rightExtra>
        <a-space>
          <a-button
            v-for="(item, index) in btns[activeKey]"
            :key="index"
            :type="item.type"
            @click="item.onClick"
          >
            <template
              v-if="item.icon"
              #icon
            >
              <component :is="item.icon" />
            </template>
            {{ item.text }}
          </a-button>
        </a-space>
      </template>

      <a-tab-pane
        :key="ETabKeys.LeaseAddress"
        tab="租赁地址"
      >
        <LeaseAddress ref="leaseAddressRef" />
      </a-tab-pane>
      <a-tab-pane
        :key="ETabKeys.ReturnAddress"
        tab="归还地址设置"
      >
        <ReturnAddress ref="returnAddressRef" />
      </a-tab-pane>
    </a-tabs>
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { PlusOutlined } from '@ant-design/icons-vue';

import LeaseAddress from './components/lease-address/lease-address.vue';
import ReturnAddress from './components/return-address/return-address.vue';

/** 地址库 */
enum ETabKeys {
  /** 租赁地址 */
  LeaseAddress = 'leaseAddress',
  /** 归还地址设置 */
  ReturnAddress = 'returnAddress',
}
const route = useRoute();
const tabKey = route.query.tabKey || ETabKeys.LeaseAddress;

const activeKey = ref(tabKey);
const leaseAddressRef = ref();
const returnAddressRef = ref();
const refs = {
  [ETabKeys.LeaseAddress]: leaseAddressRef,
  [ETabKeys.ReturnAddress]: returnAddressRef,
};

/** tabs对应的按钮 */
const btns = ref({
  [ETabKeys.LeaseAddress]: [
    {
      text: '添加租赁地址',
      type: 'primary',
      icon: PlusOutlined,
      onClick: () => handleAdd(ETabKeys.LeaseAddress),
    },
  ],
  [ETabKeys.ReturnAddress]: [
    {
      text: '添加归还地址',
      type: 'primary',
      icon: PlusOutlined,
      onClick: () => handleAdd(ETabKeys.ReturnAddress),
    },
  ],
});

/** 添加事件 */
function handleAdd(key: ETabKeys) {
  refs[key].value.handleAdd();
}
</script>
