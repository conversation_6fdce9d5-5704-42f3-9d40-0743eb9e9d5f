<template>
  <a-modal
    v-model:visible="bindVisible"
    :title="`订单【${orderId}】租金结算详情`"
    :width="800"
  >
    <a-table
      :columns="columns"
      :data-source="dataList"
      :loading="loading"
      :pagination="false"
    />
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { TableColumnType } from 'ant-design-vue';

import { useVModel } from '@/hook';

import { getStoreRentSettlementDetail, getSuperRentSettlementDetail } from './services';

interface ISettledRentItem {
  created_date: string;
  money: string;
  trade_no: string;
  username: string;
}

const props = defineProps({
  orderId: {
    type: String,
    required: true,
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const dataList = ref<ISettledRentItem[]>([]);
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const isSuper = useRoute().query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  const api = isSuper ? getSuperRentSettlementDetail : getStoreRentSettlementDetail;
  api({ order_id: props.orderId, type: 1 })
    .then(res => {
      dataList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

const columns: TableColumnType[] = [
  {
    title: '结算金额',
    dataIndex: 'money',
    key: 'money',
  },
  {
    title: '结算人',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '结算时间',
    dataIndex: 'created_date',
    key: 'created_date',
  },
  {
    title: '关联流水',
    dataIndex: 'trade_no',
    key: 'trade_no',
  },
];
</script>
