import { ref, watch } from 'vue';
import { IOptionItem } from '../data.d';
import { getAddressLevelData } from '../service';

export const useAddress = () => {
  const provinceValue = ref(null);
  const cityValue = ref(null);
  const areaValue = ref(null);
  const addressValue = ref(null);

  watch(
    () => provinceValue.value,
    val => {
      if (val) {
        requestAddressData({ name: val, level: 1 });
      } else {
        cityOptions.value = [];
        areaOptions.value = [];
      }
    },
  );

  watch(
    () => cityValue.value,
    val => {
      if (val) {
        requestAddressData({ name: val, level: 2 });
      } else {
        areaOptions.value = [];
      }
    },
  );

  const provinceOptions = ref<IOptionItem<string>[]>([]);
  const cityOptions = ref<IOptionItem<string>[]>([]);
  const areaOptions = ref<IOptionItem<string>[]>([]);

  const optionsMap = {
    0: provinceOptions,
    1: cityOptions,
    2: areaOptions,
  };

  const requestAddressData = (params: { name?: string; level: 0 | 1 | 2 }) => {
    const { name: parent_name, level: parent_level } = params;
    return new Promise(async resolve => {
      const requestParams = {
        parent_name,
        parent_level,
      };
      try {
        const res = await getAddressLevelData(requestParams);
        const data = res.data || [];
        if (optionsMap[parent_level]) {
          optionsMap[parent_level].value = data;
        }
        resolve(true);
      } catch (e) {
        resolve(false);
        console.error(e);
      }
    });
  };
  requestAddressData({ level: 0 });

  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  return {
    provinceValue,
    cityValue,
    areaValue,
    addressValue,

    provinceOptions,
    cityOptions,
    areaOptions,

    filterOption,
  };
};
