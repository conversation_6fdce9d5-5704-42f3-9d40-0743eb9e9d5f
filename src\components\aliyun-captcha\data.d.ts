export type WindowWithInitCaptchaType = Window & typeof globalThis & { initAliyunCaptcha: any };

export interface IAliyunCaptchaConfig {
  SceneId?: string;
  prefix?: string;
  mode?: string;
  element?: string;
  button?: string;
  captchaVerifyCallback?: (captchaVerifyParam: ICaptchaVerifyParam) => IVerifyResult;
  onBizResultCallback?: (bizResult: boolean) => void;
  getInstance?: (res: any) => void;
  slideStyle?: ISlideStyle;
  language?: string;
  region?: string;
}

interface ISlideStyle {
  width?: number;
  height?: number;
}

interface IVerifyResult {
  captchaResult: boolean;
  bizResult?: boolean;
}

export interface ICaptchaVerifyParam {
  sceneId: string;
  certifyId: string;
  deviceToken: string;
  data: string;
}

export interface IConfig {
  SceneId?: string;
  prefix?: string;
  mode?: string;
  element?: string;
  button?: string;

  url?: string;
  params?: { [key: string]: any };
  formatHandle?: (res?: { [key: string]: any }) => { [key: string]: any };
  beforeShow?: () => boolean;
  requestCallback?: (res: any) => boolean;
  resultSuccessCallback?: (res: any) => void;
}
export interface IProps {
  config: IConfig;
  buttonId: string;
  bntText?: string;
  showCountDown?: boolean;
  countDown?: number;
}
