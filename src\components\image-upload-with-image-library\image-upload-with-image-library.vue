<template>
  <div style="line-height: initial">
    <drag
      direction="horizontal"
      :disabled="disabled"
      :items="items"
      padding="0"
      :prop-list-item-wrap-style="{ marginRight: '8px', marginBottom: '8px' }"
      @on-sort="update"
    >
      <template #item="{ item, index }">
        <div :class="['drag-image-wrap', { 'drag-image-wrap-red': item.error }]">
          <image-item
            fit="cover"
            :height="height"
            :img="item.src"
            :padding="padding"
            :preview="showPreview"
            :width="width"
          />
        </div>
        <slot
          :index="index"
          :item="item"
          name="action"
        />
        <div
          v-if="!disabled && showDelete"
          class="delete-btn"
          @click="removeImage(index)"
        >
          <CloseCircleFilled />
        </div>
      </template>
      <template
        v-if="items.length < max"
        #after
      >
        <div
          v-if="!disabled"
          class="add-btn-wrap"
        >
          <div
            class="add-btn"
            :style="{ width: `${width}px`, height: `${height}px` }"
            @click="openImageLibrary"
          >
            <PlusOutlined />
            <div class="text">
              上传图片
            </div>
          </div>
        </div>
      </template>
    </drag>
    <ImageLibrary
      v-model:visible="imageLibraryModal.visible"
      :img-size="imgSize"
      :max="canSelectMax"
      :role="role"
      :sort="sort"
      @cancel="onCancel"
      @confirm="addImage"
    />
  </div>
</template>
<script>
import { message } from 'ant-design-vue';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons-vue';
import { debounce } from 'lodash';

import ImageLibrary from '../image-library/image-library.vue';

export default {
  components: {
    PlusOutlined,
    ImageLibrary,
    CloseCircleFilled,
  },
  props: {
    // 传入 [{ src }, { src } , { src }, ...]
    value: {
      type: Array,
      default() {
        return [];
      },
    },
    max: {
      type: [Number, String],
      default: 5,
    },
    width: {
      type: [Number, String],
      default: 100,
    },
    height: {
      type: [Number, String],
      default: 100,
    },
    padding: {
      type: [Number, String],
      default: 8,
    },
    showDelete: {
      type: Boolean,
      default: true,
    },
    showPreview: {
      type: Boolean,
      default: false,
    },
    role: {
      type: String,
      default: '',
    },
    sort: {
      type: Boolean,
      default: false,
    },
    // 图片尺寸规格，默认m。
    imgSize: {
      type: String,
      default: 'm',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 上传图片最小尺寸 {width:number,height:number}
    limitSmall: {
      type: Object,
      default: () => undefined,
    },
    limitSquare: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:value', 'edit', 'change', 'sort-change', 'cancel'],
  data() {
    return {
      items: [],
      imageLibraryModal: {
        visible: false,
      },
    };
  },
  computed: {
    canSelectMax() {
      return this.max - this.items.length;
    },
  },
  watch: {
    value: {
      // 直接引用了内存地址
      handler(value) {
        if (!Array.isArray(value) || this.items === value) {
          return;
        }
        this.id = `list_${Math.random().toString(16).slice(-6)}${new Date().getTime()}`;
        this.items = value;
      },
      immediate: true,
    },
  },
  methods: {
    update() {
      this.$emit('update:value', this.items);
      this.$emit('sort-change', this.items);
    },
    openImageLibrary() {
      this.imageLibraryModal.visible = true;
    },
    addImage(imgs) {
      for (const img of imgs) {
        if ((this.limitSmall || this.limitSquare) && !this.isValidSize(img)) {
          this.messageErrorSize();
          continue;
        }
        if (this.items.length < this.max) {
          this.items.push({
            src: img.url,
            dragId: `${this.items.length}${this.getUid()}`,
            name: img.name,
          });
        } else {
          break;
        }
      }
      this.$emit('change', this.items);
    },
    messageErrorSize: debounce(function () {
      const size = `${this.limitSmall.width}*${this.limitSmall.width}px以上的`;
      const shape = this.limitSquare ? '正方形' : '';

      message.error(`图片需为${size}${shape}图片，已过滤不符合图片，请重新上传`, 2);
    }),
    isValidSize(data) {
      if (!data || !data.spec) {
        return false;
      }
      const dimensions = data.spec.split('x');
      if (dimensions.length !== 2) {
        return false; // 格式错误
      }
      const width = parseInt(dimensions[0], 10);
      const height = parseInt(dimensions[1], 10);
      if (this.limitSquare && width !== height) {
        return false;
      }
      if (this.limitSmall) {
        // 检查尺寸是否在大于最小尺寸
        return width >= this.limitSmall.width && height >= this.limitSmall.height;
      }
      return true;
    },
    removeImage(index) {
      this.items.splice(index, 1);
      this.$emit('change', this.items);
    },
    getUid() {
      return `list_${Math.random().toString(16).slice(-6)}${new Date().getTime()}`;
    },
    onCancel() {
      this.$emit('cancel');
    },
  },
};
</script>
<style scoped lang="less">
.drag-image-wrap {
  position: relative;
  overflow: hidden;
  border: 1px solid rgb(217, 217, 217);
  border-radius: 2px;
}

.drag-image-wrap-red {
  border: 1px dashed red;
}

.add-btn-wrap {
  display: inline-block;
  vertical-align: top;
  border: 1px dashed rgb(217, 217, 217);
  border-radius: 2px;

  &:hover {
    border-color: rgb(0, 200, 190);
  }

  .add-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    background-color: rgb(250, 250, 250);
    cursor: pointer;
    transition: border-color 0.3s ease 0s;

    .text {
      margin-top: 8px;
      color: rgb(102, 102, 102);
      line-height: initial;
    }
  }
}

.delete-btn {
  position: absolute;
  top: 2px;
  right: 4px;
  display: inline-block;
  color: rgb(191, 191, 191);
  font-size: 18px;
  cursor: pointer;
}
</style>
