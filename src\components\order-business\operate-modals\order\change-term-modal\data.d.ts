import { SelectProps } from 'ant-design-vue';

export type ComplaintInfoType = {
  base_info: {
    item_name: string;
    item_num: number;
    sku_name: string;
    freight: string;
    insurance_money: string;
  };
  deposit: {
    amount: string;
  };
  rental_type: string;
  overdue: string;
  work_type_text: string;
  order_id: string;
  rental_need_pay: string; // 订单租金
  rental_money: string; //日租金
  deposit_amount: string; // 订单押金
  time_date: string;
  rental_type_text: string;
  renewal_time_start: string; // 续租日期
  renewal_time_end?: string;
  honour_type_text: string;
  diffDay: string; // 续费租期差
  type: number; // 弹窗类型

  is_pass: number; // 1通过 0不通过
  time: {
    start: string;
    end: string; // 完结日
    tenancy_text: string; // 订单总租期
  };

  new_data_end: string; // 新完结日

  err_msg: string; // 不通过的文案
  most_refund_money: number; // 最大退款金额
  periodOptions: SelectProps['options'];
  buyout_need_pay_data: {
    end_at: number;
    need_pay: number;
    period_num: number;
    start_at: number;
  };
};
