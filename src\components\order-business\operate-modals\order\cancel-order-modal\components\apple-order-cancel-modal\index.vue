<!-- 苹果订单取消弹窗 -->
<template>
  <a-modal
    v-model:visible="bindVisible"
    class="agree-cancel-modal"
    :confirm-loading="submitLoading || reflowLoading"
    destroy-on-close
    title="取消订单"
    :width="1000"
    @cancel="cancelHandle"
    @ok="confirmHandle"
  >
    <a-spin :spinning="loading">
      <a-alert
        class="alert-wrapper"
        message="该订单已纳入订单取消管控，申请取消后由平台审批，结果可点击取消工单详情查看"
        show-icon
        type="warning"
      />
      <a-form>
        <div class="refund-info-box">
          <a-form-item>
            <!-- 选择退款流水 -->
            <SelectRefundStatement
              ref="selectRefundStatementRef"
              :order-id="orderId"
              :type="ERefundType.CancelOrder"
              :visible-dependency="bindVisible"
            />
          </a-form-item>
          <template v-if="isMerchantCancel">
            <a-form-item
              v-if="showDeposit"
              label="退款押金"
              name="refund_deposit"
            >
              <a-input-number
                v-model:value="cancel.refund_deposit"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </template>
          <template v-else>
            <a-form-item label="退款类型">
              用户申请取消
            </a-form-item>
            <a-form-item
              v-if="cancel?.reasonLabel"
              label="取消原因"
            >
              {{ cancel?.reasonLabel }}
            </a-form-item>
            <a-form-item
              v-if="cancel?.subReasonLabel"
              label="二级取消原因"
            >
              {{ cancel.subReasonLabel }}
            </a-form-item>
            <div class="reminder">
              确认已核实扣取客户的费用和需要退还的费用额度，无售后问题
            </div>
          </template>
        </div>
        <div class="reason-info-box">
          <a-form-item
            label="取消类型"
            name="reason_type"
            v-bind="cancelFormConfig.validateInfos?.['reason_type']"
          >
            <group-radio
              v-model="cancel.reason_type"
              :options="cancelFormConfig.reasonTypeOptions"
              @change="reasonTypeChange"
            />
          </a-form-item>
          <a-form-item
            :class="{ 'margin-bottom-0': !templateData.common_data.length }"
            label="取消原因"
            name="reason"
            v-bind="cancelFormConfig.validateInfos?.['reason']"
          >
            <a-select
              v-model:value="cancel.reason"
              :allow-clear="true"
              :disabled="!cancel.reason_type"
              placeholder="请选择"
            >
              <a-select-option
                v-for="item in cancelFormConfig.reasonOptions[cancel.reason_type]"
                :key="item.id"
                :value="item.id"
              >
                {{ item.title }}
              </a-select-option>
            </a-select>
            <template #extra>
              <div style="color: #ff4d4f">
                为准确客观评估商家等级，请选择如实上报取消类型
              </div>
            </template>
          </a-form-item>
        </div>
      </a-form>
      <template v-if="cancel?.reason_type">
        <LargeTicketTemplate
          ref="largeTicketTemplateRef"
          :order-id="orderId"
          :template-data="templateData"
          @settemplate-data="settemplateData"
        />
      </template>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, nextTick, PropType, ref, watch } from 'vue';
import { Form, message } from 'ant-design-vue';
import { safeJsonStringify } from '@rrzu/utils';

import GroupRadio from '@/components/group-radio/group-radio.vue';
import { IReflowParams, useOrderReflow } from '@/components/order-business/composables/use-order-reflow';
import { ERefundType } from '@/components/order-business/feature-application/select-refund-statement/data.d';
import SelectRefundStatement from '@/components/order-business/feature-application/select-refund-statement/index.vue';
import { useVModel } from '@/hook';
import type { TFieldItem, TTemplate } from '@/pages-stage/user-ticket-v2/components/large-ticket-template/data';
import LargeTicketTemplate from '@/pages-stage/user-ticket-v2/components/large-ticket-template/index.vue';
import { deepClone } from '@/utils/base';

import type { IExtraData } from '../../data.d';
import { fetchOrderCancelReason, getRefundMoney } from '../../services';
import type { TAgreeCancelForm, TAgreeCancelFormConfig } from './data.d';
import { createWorkOrder, getCateTempDetail, getOrderCancelReasonOptionsApi } from './service';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<Partial<IExtraData>>,
    default: () => ({}),
  },
});
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success', partialRefresh?: boolean): void;
}>();

//是否为商家取消订单
const isMerchantCancel = computed(() => {
  const { apply_cancel_status } = props.extraData;
  return [0, 11, 12, 16].includes(apply_cancel_status as number);
});
//是否显示押金输入框
const showDeposit = computed(() => {
  const { deposit_has_pay, deposit_has_refund } = props.extraData;
  return deposit_has_pay && deposit_has_refund && Number(deposit_has_pay) > Number(deposit_has_refund);
});

const bindVisible = useVModel(props, 'visible', emit);
const selectRefundStatementRef = ref();

const submitLoading = ref(false);
const loading = ref(false);
//模版信息
const tempCate = ref<[number, number]>();
const templateData = ref<TTemplate>({
  common_data: [],
  operate_data: [],
  link_data: [],
});

const largeTicketTemplateRef = ref<any>();

//公共区赋值
function settemplateData(info: TFieldItem[]) {
  templateData.value.common_data = info;
}

const cancel = ref<TAgreeCancelForm>({
  refund_deposit: 0,
  reason_type: undefined,
  reason: undefined,
});

const cancelFormConfig = ref<TAgreeCancelFormConfig>({
  rules: {
    reason_type: [{ required: true, message: '请选择取消类型' }],
    reason: [{ required: true, message: '请选择取消原因' }],
  },
  reasonTypeOptions: {},
  reasonOptions: {},
  validateInfos: undefined,
});

const { validate, resetFields, validateInfos } = Form.useForm(cancel, cancelFormConfig.value.rules);

const haveTemplateData = ref(false);

const { loading: reflowLoading, checkOrderReflow, handleOrderReflow } = useOrderReflow();

// 取消原因类型改变
async function reasonTypeChange() {
  cancel.value.reason = undefined;
  const targetItem = Object.keys(cancelFormConfig.value.reasonTypeOptions).find((key: any) => {
    return cancelFormConfig.value.reasonTypeOptions[key].some((item: any) => {
      return Number(item.id) === Number(cancel.value.reason_type);
    });
  });
  //重新获取对应类型的模版
  if (targetItem) {
    submitLoading.value = true;
    const { data } = await getCateTempDetail({ type: targetItem });
    tempCate.value = [data.type_id, data.id];
    templateData.value = deepClone(data?.json_data || {});
    haveTemplateData.value = !!data?.json_data?.common_data?.length;
    nextTick(() => {
      submitLoading.value = false;
    });
  }
}

//获取选择的一级与二级原因
function getReasonLabel() {
  const tempReasonType = Object.values(cancelFormConfig.value.reasonTypeOptions)
    .flat()
    .find(optionItem => {
      return Number(optionItem.id) === Number(cancel.value.reason_type);
    });
  if (tempReasonType) {
    const reason = tempReasonType.child.find(item => Number(item.id) === Number(cancel.value.reason));
    return tempReasonType.title + ' - ' + reason?.title;
  }
  return '';
}

async function executeOrderReflow() {
  try {
    // 判断订单是否符合触发回流
    const { reason_type, reason } = cancel.value;
    const params = { order_id: props.orderId, reason_type, reason } as IReflowParams;
    const { data } = await checkOrderReflow(params);
    // 不符合回流，跳出去继续执行订单取消
    if (!data.is_ok) return Promise.resolve();
    // 执行订单回流
    await handleOrderReflow(params);
    cancelHandle();
    emit('success', false);
    return Promise.reject('订单触发回流');
  } catch (error) {
    return Promise.reject(error);
  }
}

async function confirmHandle() {
  await validate();
  await executeOrderReflow();
  await largeTicketTemplateRef.value?.setData();
  if (haveTemplateData.value && !templateData.value.common_data.length) {
    return message.error('请检查表单信息');
  }
  const depositHasPay = Number(props.extraData?.deposit_has_pay);
  const depositHasRefund = Number(props.extraData?.deposit_has_refund);
  submitLoading.value = true;
  const { refund_deposit, reason_type, reason } = cancel.value;
  //取消订单独有参数cancel_reason_json
  const cancel_reason_json = {
    field_id: reason_type,
    field_type: 'order_cancel_reason',
    field_value: '商家取消原因',
    field_key: 'order_cancel_reason',
    input_value: reason,
    input_value_text: getReasonLabel(),
  };
  const refundparams = (await selectRefundStatementRef.value?.requestParams(false)) || { refund_rental: 0 };
  const params = {
    ...refundparams,
    refund_deposit,
    order_id: props.orderId,
    new_type_id: tempCate.value?.[0],
    template_id: tempCate.value?.[1],
    json_data: safeJsonStringify(templateData.value),
    cancel_reason_json: safeJsonStringify(cancel_reason_json),
    version: 2,
  };
  if (!isMerchantCancel.value && depositHasPay > depositHasRefund) {
    params.refund_deposit = Math.round((depositHasPay - depositHasRefund) * 100) / 100;
  }
  await createWorkOrder(params).finally(() => {
    submitLoading.value = false;
  });
  cancelHandle();
  emit('success');
}

function cancelHandle() {
  templateData.value = {
    common_data: [],
    operate_data: [],
    link_data: [],
  };
  resetFields();
  bindVisible.value = false;
}

async function initData() {
  cancelFormConfig.value.validateInfos = validateInfos;
  // 每次打开弹框重置一下
  loading.value = true;
  try {
    if (isMerchantCancel.value) {
      //若是商家取消需获取退款押金、退款租金，
      getRefundMoney({
        order_id: props.orderId,
      }).then(res => {
        cancel.value.refund_deposit = Number(res.data.refund_deposit);
      });
    } else {
      //若是用户取消需获取用户申请选择的原因
      const { apply_cancel_reason, sub_cancel_reason_text } = props.extraData;
      const tempReason = {
        cancel_reason: '',
        cancel_sub_reason: '',
      };
      if (apply_cancel_reason && sub_cancel_reason_text) {
        tempReason.cancel_reason = apply_cancel_reason;
        tempReason.cancel_sub_reason = sub_cancel_reason_text || '';
      } else {
        const { data } = await fetchOrderCancelReason({ order_id: [props.orderId] });
        Object.assign(tempReason, data?.list?.[props.orderId]);
      }
      cancel.value.subReasonLabel =
        tempReason.cancel_sub_reason?.[0] === '['
          ? JSON.parse(tempReason.cancel_sub_reason)
              .map((item: any) => item.title)
              .join('&')
          : tempReason.cancel_sub_reason;
      cancel.value.reasonLabel = tempReason.cancel_reason;
    }
    //获取取消原因配置
    getOrderCancelReasonOptionsApi().then(({ data }) => {
      cancelFormConfig.value.reasonTypeOptions = data;
      Object.values(data)
        .flat()
        .forEach(({ id, child }: any) => {
          cancelFormConfig.value.reasonOptions[id] = child;
        });
    });
  } finally {
    loading.value = false;
  }
}

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      initData();
    } else {
      cancelHandle();
    }
  },
  { immediate: true },
);
</script>

<style lang="less" scoped>
.alert-wrapper {
  margin-bottom: 24px;

  :deep(.ant-alert-message) {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
  }
}

:deep(.ant-alert-warning) {
  align-items: flex-start;
}

:deep(.ant-alert-icon) {
  margin-top: 5px;
}

.refund-info-box {
  margin-bottom: 32px;
  padding: 16px;
  background: #f9f9fb;

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }

  .prompt {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
  }

  .reminder {
    color: #ff4d4f;
  }
}

.reason-info-box {
  :deep(.ant-form-item-control) {
    flex-basis: 100%;
  }
}

.margin-bottom-0 {
  margin-bottom: 0;
}

.setp-content {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .setp-content-item {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .title {
      display: flex;
      gap: 8px;
      align-items: center;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }

    .title::before {
      width: 4px;
      height: 16px;
      background: #3777ff;
      border-radius: 2px 2px 2px 2px;
      content: '';
    }
  }
}
</style>
