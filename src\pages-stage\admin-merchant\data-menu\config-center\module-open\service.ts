import { GET, POST } from '@/services/api';

// 查询模块开通列表
export function getOpenList() {
  return GET(
    '/merchant/rDI/module/openList',
    {},
    {
      hostType: '<PERSON><PERSON><PERSON>',
    },
  );
}

// 开通模块
export function openModule(data: any) {
  return POST('/merchant/rDI/module/openModule', data, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
    hostType: '<PERSON><PERSON><PERSON>',
  });
}
