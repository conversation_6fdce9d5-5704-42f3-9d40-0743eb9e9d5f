<template>
  <div class="recycle-sale-customer-table">
    <customer-table
      :columns="saleListColunms"
      :data-source="list"
      :loading="listLoading"
      :table-thead-class="['recycle-sale-table-thead']"
    >
      <template #rowHeader="{ record }">
        <order-head :common-head="{ ...record.commonHead, ...record.orderLogisticData }" />
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'goods_info'">
          <!-- 商品信息 -->
          <goods-info :goods-data="record.goodsData" />
        </template>
        <template v-if="column.key === 'price'">
          <!-- 价格合计 -->
          <total-price :price-data="record.priceData" />
        </template>
        <template v-if="column.key === 'real'">
          <!-- 实付款 -->
          <real-pay :pay-data="record.payData" />
        </template>
        <template v-if="column.key === 'address'">
          <!-- 收货信息 -->
          <address-info :custom-data="record.customData" />
        </template>
        <template v-if="column.key === 'order_status'">
          <!-- 订单状态 -->
          <order-status :base-data="record.baseData" />
        </template>
        <template v-if="column.key === 'operation'">
          <!-- 操作 -->
          <operations />
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <!-- 备注栏 -->
        <tr class="expanded-row">
          <td :colspan="saleListColunms.length">
            <order-remark
              :order-id="record.commonHead.rs_order_id"
              :order-remarks="orderRemarks"
              type="order.recycle"
            >
              <template #emptyText>
                <span>无备注</span>
              </template>
            </order-remark>
          </td>
        </tr>
      </template>
    </customer-table>
    <!-- 分页 -->
    <list-pagination
      :on-table-change="tableChange"
      :page="page"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted,PropType } from 'vue';
import { AxiosResponse } from 'axios';

import { useTable } from '@/hook/component/use-table';
import useCeiling from '@/pages-stage/recycle/common/use-ceiling';
import { isNotEmptyArray } from '@/pages-stage/recycle/common/util';

import {
  AddressInfo,
  CustomerTable,
  GoodsInfo,
  ListPagination,
  Operations,
  OrderHead,
  OrderRemark,
  OrderStatus,
  RealPay,
  TotalPrice,
} from './component-imports';
import useRemark from './composables/use-remark';
import { saleListColunms } from './config';
import { IOrderList, ISearchParams } from './data';

const props = defineProps({
  searchParams: {
    type: Object as PropType<Partial<ISearchParams>>,
    default: () => ({}),
  },
});

const { loadRemark, orderRemarks } = useRemark();

const { list, listLoading, page, getTableList, tableChange } = useTable<IOrderList, Partial<ISearchParams>>({
  url: '/super/recycle/sale-order/list',
  totalKey: 'data.pageInfo.count',
  formatSearchValue: () => {
    return formatPrams(props.searchParams) as ISearchParams;
  },
  formatHandle: (res: AxiosResponse<{ list: IOrderList[] }>) => {
    // 加载备注
    res.data.list.length && loadRemark(res.data.list);
    return res.data.list;
  },
});

// 处理特殊参数
const formatPrams = (searchParams: Partial<ISearchParams>) => {
  const data = {
    category_brand_model: '',
    start_at: '',
    end_at: '',
  };
  //  类目
  if (isNotEmptyArray(searchParams.category)) {
    data.category_brand_model = (searchParams.category as number[]).join('_');
  }

  // 创建时间
  if (isNotEmptyArray(searchParams.create_time)) {
    data.start_at = searchParams.create_time?.[0] ?? '';
    data.end_at = searchParams.create_time?.[1] ?? '';
  }
  return { ...data, ...searchParams };
};

onMounted(() => {
  useCeiling();
  getTableList();
});

defineExpose({
  getTableList,
  formatPrams,
});
</script>

<style lang="less" scoped>
.expanded-row {
  border-top: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;

  > td {
    padding: 16px;
  }

  &-td {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }
}

:deep(.recycle-sale-table-thead tr th) {
  background: #e6fff9 !important;
}

.recycle-sale-customer-table {
  overflow-x: auto;
}
</style>
