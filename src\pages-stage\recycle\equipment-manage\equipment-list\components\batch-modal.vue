<template>
  <a-modal
    :destroy-on-close="true"
    :title="type === 'imei' ? '批量筛选IMEI码' : '批量筛选SN码'"
    :visible="visible"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-form>
      <a-form-item :label="labelType">
        <a-textarea
          v-model:value="content"
          :placeholder="`多个${labelType}需要隔行；`"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: '',
  },
  labelType: {
    type: String,
    default: '',
  },
});
const content = ref('');
const emits = defineEmits(['update:visible', 'change']);
const onCancel = () => emits('update:visible', false);
const handleOk = () => {
  if (content.value) {
    const data = content.value.split('\n');
    if (data.some(item => item.length >= 20)) {
      message.warn('极限值为20字符');
      return;
    }
    emits('change', data);
  }

  onCancel();
};
</script>
