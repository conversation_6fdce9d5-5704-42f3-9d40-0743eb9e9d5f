<template>
  <layout-admin-page
    :navs="['趣回收', '经营管理', '回收日报']"
    title="回收日报"
  >
    <div class="container">
      <SearchGroup
        v-model:value="searchInfo"
        @confirm="getTableList"
      />
      <a-table
        class="table-box bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: 1112 }"
        @change="tableChange"
      >
        <template #headerCell="{ column }">
          <template v-if="column.tipsText">
            {{ column.title }}
            <a-tooltip placement="top">
              <template #title>
                <span>{{ column.tipsText }}</span>
              </template>
              <ExclamationCircleOutlined style="color: grey; font-size: 14px" />
            </a-tooltip>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="['commit_num', 'finish_num', 'purchased_price_num'].includes(column.dataIndex)">
            <span
              :class="{
                active:
                  requestParamsData.source_type && record[column.dataIndex] && Number(record[column.dataIndex]) != 0,
              }"
              @click="onOpenPage(column.dataIndex, record)"
            >{{ record[column.dataIndex] }}</span>
          </template>
          <template v-if="['in_warehouse_num'].includes(column.dataIndex)">
            <div class="flex-box">
              <div>
                {{ record[column.dataIndex] }}
              </div>
              <a-button
                class="column-button"
                @click="onOpenPage(column.dataIndex, record)"
              >
                <div>
                  {{ `关联${record.in_warehouse_device_num}台设备` }}
                  <a-tooltip placement="top">
                    <template #title>
                      <span>{{ column.equipmentTipsText }}</span>
                    </template>
                    <ExclamationCircleOutlined style="font-size: 14px" />
                  </a-tooltip>
                </div>
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { reactive, onMounted } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useTable } from '@/hook/component/use-table';
import { ISearchInfo, IDataSource } from '.data.d';
import { columns } from './config';
import SearchGroup from './components/search-group.vue';

const defaultTimeType = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];

const searchInfo = reactive<ISearchInfo>({
  source_type: '0',
  data_type: '1',
  timeType: defaultTimeType,
});

// 每次发起请求，都会储存本次请求的参数；
let requestParamsData = null;

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable<IDataSource, any>({
  url: '/super/recycle/data-count/order-data',
  searchForm: searchInfo,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatSearchValue: (params: ISearchInfo) => {
    const requestParams = { ...params };
    delete requestParams.timeType;
    if (params.timeType || params.timeType?.length) {
      // 按日统计，默认数据格式为数组； 按月统计，则数据是字符串，选择单个年份；
      if (requestParams.data_type === '1') {
        requestParams.start_at = params.timeType[0];
        requestParams.end_at = params.timeType[1];
      } else {
        requestParams.start_at = dayjs(params.timeType).startOf('year').format('YYYY-MM-DD');
        requestParams.end_at = dayjs(params.timeType).endOf('year').format('YYYY-MM-DD');
      }
    }
    requestParamsData = requestParams;
    return requestParams;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const onOpenPage = (key: string, record: IDataSource) => {
  const value = record[key];
  const { source_type } = searchInfo;
  // 没有数据来源、值为0或空，点击不做跳转；
  if (!source_type || !value || Number(value) === 0) return;

  const pageUrl = {
    commit_num: source_type === '1' ? '/super/recycle/order/index' : '/super/recycle/batch-order/index',
    finish_num: source_type === '1' ? '/super/recycle/order/index' : '/super/recycle/batch-order/index',
    purchased_price_num: source_type === '1' ? '/super/recycle/order-pay/index' : '/super/recycle/server-bill/index',
    in_warehouse_num: '/super/recycle/device-management/index',
  };
  const url = pageUrl[key];
  const recordDateList = [record['date'], record['date']];
  const dateList = recordDateList.map((val, index) => {
    const array = val.split('-');
    if (array.length < 3) {
      val = dayjs(val)[index === 0 ? 'startOf' : 'endOf']('month').format('YYYY-MM-DD');
    } else {
      val = dayjs(val).format('YYYY-MM-DD');
    }
    return val;
  });
  const params = {
    startTime: dateList[0],
    endTime: dateList[1],
  };
  if (key === 'finish_num') params['orderStatus'] = '10';
  if (key === 'purchased_price_num') params['statusList'] = '0,1';

  const urlParamsStr = Object.keys(params).reduce((pre, cur) => {
    return pre ? `${pre}&${cur}=${params[cur]}` : `?${cur}=${params[cur]}`;
  }, '');

  window.parent.postMessage(
    {
      action: 'blank',
      href: url + urlParamsStr,
    },
    '*',
  );
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
.container {
  padding: 2px 24px 26px 24px;
}
.table-box {
  margin-top: 24px;
  .active {
    color: #3777ff;
    cursor: pointer;
  }
}
.column-button {
  display: flex;
  align-items: center;
  width: 133px;
  height: 22px;
}

.flex-box {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
