interface IFooterStyle {
  position: string;
  right: number;
  bottom: number;
  width: string;
  borderTop: string;
  padding: string;
  background: string;
  textAlign: string;
  zIndex: number;
}

interface IBankList {
  id?: string;
  server_id?: string;
  bank_user_name?: string;
  bank_name?: string;
  bank_branch_name?: string;
  bank_card_num?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
  created_at_text?: string;
  is_hide?: number;
}

interface IItemDetails {
  account_id?: string | number;
  acc_amount?: string;
  acc_name?: string;
  dis_amount?: string;
  newness?: string;
  remind_amount?: string;
  min_rec_amount?: string;
  freeze_amount?: string;
  type?: string;
  categories?: Category[];
  amount?: string | number;
  real_money?: string | number;
  handling_charge?: string | number;
  account_name?: string;
  acc_tips?: string[];
}
interface Category {
  category_name: string;
  brand_name: string;
}

type TAddBankModalForm = {
  alipay_payuser?: string;
  alipay_account?: string;
};

interface IRechargeConfig {
  id: number | string;
  server_id: number;
  account_id: string | number;
  account_config_id: number;
  supplier_id: number;
  supplier_name: string;
  bank_user_name: string;
  bank_card_num: string;
  bank_name: string;
  edit_account: string;
  reject_reason: string;
  amount: string | number;
  audit_amount: string;
  rec_amount: string;
  change_status: number;
  config_status: number;
  pay_status: number;
  created_at: number;
  acc_updated_at: number;
  created_by: number;
  updated_at: number;
  updated_by: number;
}

interface Accountcredit {
  credit_amount: string;
  surplus_credit_amount: string;
  freeze_credit_amount: string;
}
interface Accounts {
  accounts: Account[];
  total_amount: string;
  total_deposit_amount: string;
  total_account_count: number;
  type: number;
}
interface Account {
  account_id: string | number;
  acc_amount: string;
  acc_name: string;
  dis_amount: string;
  newness: string;
  remind_amount: string;
  min_rec_amount: string;
  freeze_amount: string;
  type: string;
  categories: Category[];
  acc_tips?: string[];
}
interface Category {
  category_name: string;
  brand_name: string;
}

export enum EServerType {
  //企业
  ENTERPRISE = 1,
  //个人
  PERSONAL = 2,
}
interface IPageData {
  accounts?: Accounts;
  accounts_party?: Accounts;
  account_credit?: Accountcredit;
  is_replaced_send?: boolean;
  server_name?: string;
  bank_card_count?: string;
  server_type: EServerType;
}

export enum EPayType {
  //企业对公支付
  COMPANY = '1',
  //支付宝
  ALIPAY = '2',
}

export { IBankList, IFooterStyle, IItemDetails, IPageData, IRechargeConfig, TAddBankModalForm };
