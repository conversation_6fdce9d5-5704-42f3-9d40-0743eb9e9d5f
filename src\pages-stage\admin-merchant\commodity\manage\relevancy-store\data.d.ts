interface IForm {
  activeSec: number;
  activeTimes: null | any;
  activePhone: string;
  activeCode: string;
  activeHasClick: boolean;
  passiveSec: number;
  passiveTimes: null | any;
  passivePhone: string;
  passiveCode: string;
  passiveHasClick: boolean;
  relation_id?: number | string | undefined;
}

export interface IUnbindForm {
  visible: boolean;
  confirmLoading: boolean;
  form: IForm;
}

interface IRForm {
  sec: number;
  times: null | any;
  phone: string;
  code: string;
  hasClick: boolean;
}
export interface IRelevancyForm {
  visible: boolean;
  confirmLoading: boolean;
  form: IRForm;
}
