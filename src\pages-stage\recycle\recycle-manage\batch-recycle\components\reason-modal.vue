<template>
  <a-modal
    :destroy-on-close="true"
    title="取消订单"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-form
      ref="formRef"
      :label-col="{
        style: { width: '82px' },
      }"
      style="width: 100%"
    >
      <a-form-item
        label="取消原因"
        required
      >
        <a-select
          v-model:value="value"
          :options="reasonOptions"
          placeholder="请选择"
          style="width: 352px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { useVModel } from '@/hook';
import { message } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import { reasonFilter } from '../service';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:visible', 'onOk']);
const visible = useVModel(props, 'visible', emits);
const value = ref(null);
const onCancel = () => emits('update:visible', false);

const reasonOptions = ref<any>([]);
const handleOk = () => {
  if (value.value) {
    emits('onOk', value.value);
  } else {
    message.error('请选择取消原因');
  }
};
onMounted(async () => {
  const res = await reasonFilter();
  reasonOptions.value = res.data;
});
</script>
<style lang="less" scoped>
.info-item {
  display: flex;
  margin-bottom: 24px;
  &:nth-last-child(1) {
    margin-bottom: 0;
  }
  &__label {
    flex-shrink: 0;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  &__value {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
