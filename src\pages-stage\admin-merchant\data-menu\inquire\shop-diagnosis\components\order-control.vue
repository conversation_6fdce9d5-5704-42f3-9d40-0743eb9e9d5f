<!-- 订单管控预警 -->
<template>
  <div class="container">
    <div class="warning-top flex-wrap">
      <div class="flex-con">
        {{ props.title }}
      </div>
      <a
        class="link"
        href="javascript:void(0)"
        style="font-weight: normal"
        @click="openNewPage('/order/delivery-index-new')"
      >去处理</a>
    </div>
    <div
      class="font-black-65"
      style="margin: 12px 0"
    >
      {{ props.desc }}
    </div>
    <a-space>
      <a
        v-for="(item, index) in props.orderList.slice(0, 5)"
        :key="index"
        class="link"
        href="javascript:void(0)"
        @click="openNewPage(`/order/v4-order-index?order_id=${item}`)"
      >{{ item }}</a>
      <a
        v-if="props.orderList?.length > 5"
        class="link"
        href="javascript:void(0)"
        @click="moreOpen(props.orderList)"
      >查看详情</a>
    </a-space>

    <a-modal
      v-model:visible="moreVisible"
      :footer="null"
      title="查看所有订单"
    >
      <template
        v-for="orderItem in currentOrderIDs"
        :key="orderItem"
      >
        <a-button
          class="link"
          type="link"
          @click="openNewPage(`/order/v4-order-index?order_id=${orderItem}`)"
        >
          {{ orderItem }}
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useModal } from 'rrz-web-design';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  desc: {
    type: String,
    default: '',
  },
  orderList: {
    type: Array as PropType<number[]>,
    default: () => [],
  },
});

const route = useRoute();
const domain = route.query.origin || window.location.origin;

const currentOrderIDs = ref<number[]>([]);
const { visible: moreVisible, open: moreOpen } = useModal(undefined, {
  beforeOpen: (ids: number[]) => {
    currentOrderIDs.value = ids;
  },
});

function openNewPage(url: string) {
  window.parent.postMessage(
    {
      action: 'blank',
      href: domain + url,
    },
    '*',
  );
}
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

.link {
  color: #3777ff;
  &:hover {
    text-decoration: underline;
  }
}

.container {
  padding: 10px 16px;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 8px;
  &:not(:last-child) {
    margin-bottom: 16px;
  }
}
.warning-top {
  color: @font-black-85;
  font-weight: bold;
  &::before {
    display: inline-block;
    width: 4px;
    height: 14px;
    margin: 4px 8px 0 0;
    background: #fa8c16;
    border-radius: 2px;
    content: '';
  }
}
</style>
