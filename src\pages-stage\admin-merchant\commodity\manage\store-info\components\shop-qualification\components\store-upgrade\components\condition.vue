<template>
  <section class="upgrade-condition-container">
    <!-- 入驻前数据 -->
    <a-spin :spinning="beforeEnterDataLoading">
      <div class="prerequisite">
        <div class="line">
          <div class="feild">
            店铺经营类目
          </div>
          <div class="value">
            {{ beforeEnterData.category }}
          </div>
        </div>
        <div class="line">
          <div class="feild">
            <a-space>
              <span>入驻类目</span>
              <a-tooltip
                arrow-point-at-center
                placement="topRight"
                title="平台会根据商家经营范围判断入驻码商的类目"
              >
                <QuestionCircleOutlined />
              </a-tooltip>
            </a-space>
          </div>
          <div class="value">
            <a-radio-group
              disabled
              :value="beforeEnterData.enter_category"
            >
              <a-radio :value="beforeEnterData.enter_category">
                {{ enterCategoryMap[beforeEnterData.enter_category] }}
              </a-radio>
            </a-radio-group>
          </div>
        </div>
        <div
          v-if="beforeEnterData.enter_category != EEnterCategory.NonMobileComputer"
          class="line"
        >
          <div class="feild">
            <a-space>
              <span>门店类型</span>
              <a-tooltip
                arrow-point-at-center
                placement="topRight"
              >
                <template #title>
                  <div>1.线下门店：门店数量大于或等于1家</div>
                  <div>2.线上门店：拥有实际经营的网店或平台推广账户</div>
                </template>
                <QuestionCircleOutlined />
              </a-tooltip>
            </a-space>
          </div>
          <div class="value">
            <a-radio-group
              v-model:value="queryParams.store_type"
              @change="handleQuery"
            >
              <a-radio
                v-for="option of (beforeEnterData.store_type as IOption[])"
                :key="option.value"
                :value="option.value"
              >
                <template v-if="beforeEnterData.enter_category == EEnterCategory.MobilePhone && option.value === 11">
                  <!-- 入驻类目为手机且为线下门店选项，额外增加气泡提示语 -->
                  <a-space>
                    <span>{{ option.label }}</span>
                    <a-tooltip>
                      <template #title>
                        <div>1.门店经营与手机数码行业相关（如手机零售、移动/联通/电信营业厅、手机维修店等）</div>
                        <div>2.门店面积大于10平米，门店经营主体为入驻商家经营主体的全资子公司</div>
                      </template>
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </a-space>
                </template>
                <template v-else>
                  {{ option.label }}
                </template>
              </a-radio>
            </a-radio-group>
          </div>
        </div>
      </div>
    </a-spin>

    <div class="alert">
      <span>您当前店铺类型为</span>
      <Tag
        ghost
        tag="平台型商家"
        type="primary"
      >
        <template #prefix>
          <i class="icon-platform" />
        </template>
      </Tag>
      <span>将升级为</span>
      <Tag
        ghost
        tag="码商型商家"
        type="primary"
      >
        <template #prefix>
          <i class="icon-mashang" />
        </template>
      </Tag>
      <span>需要满足以下条件才可以进行店铺升级流程</span>
    </div>

    <!-- 查询区 -->
    <template v-if="beforeEnterData.enter_category != EEnterCategory.NonMobileComputer && isNotYetQueried">
      <div class="query-wrapper">
        <img
          class="query-image"
          src="https://img1.rrzuji.cn/uploads/scheme/2503/12/m/trKM8KyLSJ8vHJV9Ca6E.png"
        >
        <div class="query-content">
          <i class="icon-query" />
          <span class="query-description">选择 门店类型 后查看条件是否满足</span>
        </div>
      </div>
    </template>

    <!-- 条件区 -->
    <a-table
      v-if="queryData.length"
      bordered
      :columns="conditionTableColumns"
      :data-source="queryData"
      :loading="queryLoading"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-space>
            <i
              v-if="record.result"
              class="status status-ok"
            >√</i>
            <i
              v-else
              class="status status-no"
            >x</i>
            <span>{{ getStatusText(record.key, record.value, record.result) }}</span>
          </a-space>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <span v-if="record.result">-</span>
          <a
            v-else
            class="text-link"
            @click="handle(record.key)"
          >去处理</a>
        </template>
      </template>
    </a-table>

    <!-- 结果区-不符 -->
    <template v-if="isNotSatisfied">
      <div class="not-satisfied-box">
        未满足店铺升级的所有条件
      </div>
    </template>

    <!-- 结果区-满足 -->
    <template v-if="isSatisfied">
      <div class="satisfied-box">
        <div>恭喜你！已满足所有店铺升级条件~</div>
        <a-button
          type="primary"
          @click="handleStartUpgrade"
        >
          开始升级
        </a-button>
      </div>
    </template>
  </section>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

import { useTopWindow } from '@/hook/common/use-top-window';

import { useSharedData } from '../composables/use-shared-data';
import { conditionTableColumns, enterCategoryMap } from '../config';
import { EConditionStatus, EEnterCategory, EShowComponent, IBeforeEnterData, ICondition, IOption } from '../data.d';
import { getBeforeEnterData, postCheck } from '../services';
import Tag from './tag.vue';

type ConditionKey = 'shop_status' | 'server_face_review' | 'enter_day' | 'guarantee_money' | 'gua_money';

const { navigate } = useTopWindow();
const { updateSharedData } = useSharedData();

const currentStatus = ref(EConditionStatus.NotYetQueried);

const dedupe = (status: EConditionStatus) => computed(() => currentStatus.value === status);
const isNotYetQueried = dedupe(EConditionStatus.NotYetQueried);
const isNotSatisfied = dedupe(EConditionStatus.NotSatisfied);
const isSatisfied = dedupe(EConditionStatus.Satisfied);

const beforeEnterDataLoading = ref(false);
const beforeEnterData = ref({} as IBeforeEnterData);

async function initBeforeEnterData() {
  try {
    beforeEnterDataLoading.value = true;
    const { data } = await getBeforeEnterData();
    beforeEnterData.value = data;
    if (beforeEnterData.value.enter_category == EEnterCategory.NonMobileComputer) {
      // 非手机电脑类目没有门店类型，直接进行查询
      nextTick(handleQuery);
    }
  } finally {
    beforeEnterDataLoading.value = false;
  }
}

const queryParams = reactive<Partial<{ store_type?: number }>>({});
const queryLoading = ref(false);
const queryData = ref<ICondition[]>([]);

async function handleQuery() {
  try {
    queryLoading.value = true;
    if (beforeEnterData.value.enter_category != EEnterCategory.NonMobileComputer && !queryParams.store_type) {
      message.warn('请先选择门店类型');
      return;
    }
    const { data } = await postCheck({ store_type: queryParams.store_type });
    determineSatisfyOrNot((queryData.value = data));
  } finally {
    queryLoading.value = false;
  }
}

function determineSatisfyOrNot(data: ICondition[]) {
  currentStatus.value = data.every(item => !!item.result) ? EConditionStatus.Satisfied : EConditionStatus.NotSatisfied;
}

function handleStartUpgrade() {
  const store_type = String(queryParams.store_type);
  const enter_category = String(beforeEnterData.value.enter_category);

  const store_type_text = (beforeEnterData.value.store_type as IOption[]).find(
    item => item.value === queryParams.store_type,
  )?.label;
  const enter_category_text = enterCategoryMap[beforeEnterData.value.enter_category];
  // 更新共享数据，其他组件使用
  updateSharedData({
    activeComponent: EShowComponent.StepManagement,
    ...beforeEnterData.value,
    store_type,
    store_type_text,
    enter_category,
    enter_category_text,
  });
}

function getStatusText(key: ConditionKey, value: string, result?: boolean) {
  switch (key) {
    case 'enter_day':
      return `入驻平台天数为${value}天`;
      break;
    case 'shop_status':
    case 'server_face_review':
    case 'guarantee_money':
    case 'gua_money':
      return result ? '满足' : '不满足';
      break;
    default:
      break;
  }
}

function handle(key: ConditionKey) {
  switch (key) {
    case 'shop_status':
      // 开店状态
      Modal.info({ title: '提示', content: '请联系运营处理' });
      break;
    case 'server_face_review':
      // 已完成线下面审
      navigate('blank', '/site/detail');
      break;
    case 'guarantee_money':
    case 'gua_money':
      // 开通质保金
      // 质保金余额不小于10000元
      navigate('blank', '/account/asset-index');
      break;
    default:
      break;
  }
}

onMounted(() => {
  initBeforeEnterData();
});
</script>

<style lang="less" scoped>
.upgrade-condition-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 120px;
}

.prerequisite {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background-color: #********;
}

.line {
  display: flex;
}

.feild {
  flex-shrink: 0;
  width: 120px;
}

.alert {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 14px 16px;
  background: linear-gradient(90deg, #6b9bff1f 0%, #3471f30d 100%);
  border-radius: 4px;
}

.icon-platform {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/12/m/4pggoZ1ApudQVbPC0EFv.png') no-repeat 100% / cover;
}

.icon-mashang {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/12/m/l40bR6ysybfDBrhLVD6B.png') no-repeat 100% / cover;
}

.query-wrapper {
  position: relative;
}

.query-image {
  width: 100%;
}

.query-content {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon-query {
  display: inline-block;
  width: 54px;
  height: 54px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2504/14/m/oXRvaIqTxbnRWLfrisLg.png') 100% / cover;
}

.query-description {
  margin-bottom: 24px;
  color: #061533d9;
}

.status {
  display: inline-block;
  width: 26px;
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  text-align: center;
  border-radius: 4px;
}

.status-ok {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.status-no {
  color: #ff4d4f;
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
}

.not-satisfied-box {
  padding: 24px 0;
  color: #faad14;
  text-align: center;
  background-color: #fff8f4;
  border-radius: 4px;
}

.satisfied-box {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  padding: 24px;
  color: #061533d9;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/12/m/tqFPDxT26Nv3aClHVTnu.png') center / cover;
}
</style>
