<template>
  <div class="rule-item">
    <div class="title">
      {{ title }}
    </div>
    <div
      v-if="showLine"
      class="line"
    />
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: '',
  },
  showLine: {
    type: Boolean,
    default: true,
  },
});
</script>

<style lang="less" scoped>
.rule-item {
  display: flex;
  margin-bottom: 36px;
  .title {
    min-width: 112px;
    height: 24px;
    margin: 0 72px 0 0;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: PingFangSC-Medium, PingFang SC;
    line-height: 24px;
  }
  .line {
    width: 4px;
    margin-right: 24px;
    background: #3371f3;
    border-radius: 4px;
  }
  .content {
    line-height: 22px;
  }
}
</style>
