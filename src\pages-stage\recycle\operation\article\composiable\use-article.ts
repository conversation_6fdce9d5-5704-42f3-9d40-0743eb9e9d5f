import { useModal } from '@/hook/component/use-modal';
import { h, reactive, ref } from 'vue';
import { IFormModal } from '../data';
import { createAction, getDetailAction, editAction } from '../service';
import { FormInstance, Modal, message } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { IAxiosResponse } from '@/services/api';

type TEmit = (event: string, ...args: unknown[]) => void | Promise<void>;

export const useArticle = () => {
  const formState = reactive<Partial<IFormModal>>({});
  const type = ref(1); // 1创建 2 编辑
  const currentId = ref('');

  const editActionHandler = (id: string) => {
    type.value = 2;
    currentId.value = id;
    open();
  };

  const checkRepeat = ({ status, data, error, message: msg }: Partial<IAxiosResponse<{ id?: string }>>) => {
    if (status === 20003) {
      return Modal.confirm({
        title: h(
          'div',
          {
            style: {
              fontWeight: 'bold',
            },
          },
          error ?? '',
        ),
        content: h('div', null, [h('div', null, '“前往编辑”去编辑已经存在的文章')]),
        icon: h(ExclamationCircleOutlined),
        okText: '前往编辑',
        cancelText: '',
        onOk: async () => {
          if (data && data?.id) {
            // 切换为编辑状态
            type.value = 2;
            currentId.value = data?.id;
            await getDetailActionHandler();
          }
        },
        width: 408,
      });
    } else {
      message.error(error || msg || '请求失败');
    }
  };

  const addActionHanlder = () => {
    type.value = 1;
    open();
  };

  const { open, loading, visible, close, confirm } = useModal(
    async (formRef: FormInstance, emits: TEmit) => {
      await formRef.validate();
      const { process_question_type, question_type } = formState;
      if ((process_question_type === '0' && question_type === '0') || (!question_type && !process_question_type)) {
        message.warn('请选择一个所属类目');
        return;
      }
      try {
        const { status } =
          type.value === 1
            ? await createAction(formState, { closeErrorMessage: true })
            : await editAction({ ...formState, id: currentId.value }, { closeErrorMessage: true });
        if (status === 0) {
          message.success('操作成功');
          emits('reset-table');
          close();
        }
      } catch (error: any) {
        checkRepeat(error);
      }
    },
    {
      afterClose: () => {
        for (const key in formState) {
          formState[key] = undefined;
        }
      },
      afterOpen: async () => {
        if (type.value === 2) {
          await getDetailActionHandler();
        }
      },
    },
  );

  // 获取降价趋势详情
  const getDetailActionHandler = async () => {
    try {
      loading.value = true;
      const { data } = await getDetailAction({ id: currentId.value });
      Object.assign(formState, data);
    } finally {
      loading.value = false;
    }
  };

  return {
    open,
    loading,
    visible,
    close,
    confirm,
    editActionHandler,
    addActionHanlder,
    type,
    formState,
  };
};
