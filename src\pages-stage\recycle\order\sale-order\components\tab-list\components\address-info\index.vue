<template>
  <div class="address-info recycle-customer-table-flex-col">
    <div class="user-info">
      <span class="name">{{ customData.receive_name }}

        <DecryptField
          v-if="viewPlaintextAuth.recycleShowViewHideBtn"
          :field-item="{
            box: 'eye',
            id: customData.zl_order_id,
            field: 'receive_name',
            field_type: 'name',
            type: 136,
          }"
        />
      </span>
      <span>{{ customData.phone }}
        <DecryptField
          v-if="viewPlaintextAuth.recycleShowViewHideBtn"
          :field-item="{
            box: 'eye',
            id: customData.zl_order_id,
            field: 'phone',
            field_type: 'phone',
            type: 136,
          }"
        />
      </span>
    </div>
    <div class="address">
      <!-- <tooltips
        :content="[customData.address]"
        :style="{ color: 'rgba(6, 21, 51, 0.65)', display: 'inline-block' }"
      /> -->
      {{ customData.address }}
      <DecryptField
        v-if="viewPlaintextAuth.recycleShowViewHideBtn"
        :field-item="{
          box: 'eye',
          id: customData.zl_order_id,
          field: 'address',
          field_type: 'address',
          type: 136,
        }"
      />
    </div>
    <div class="logistic-code">
      {{ customData.zl_order_id }}
    </div>
    <div class="server-info">
      <a-tag color="blue">
        {{ searchOptions.customerTypeOtions.find(item => item.value === customData.custom_type)?.label ?? '' }}
      </a-tag>
      <span class="server-name">{{ customData.server_id }}｜{{ customData.server_name }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
// import Tooltips from '../tooltips/index.vue';
import { ICustomData } from '../../data';
import { searchOptions } from '../../config';
import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
import { useViewPlaintextAuth } from '@/composables/use-decrypt-data';
const { viewPlaintextAuth } = useViewPlaintextAuth();

defineProps({
  customData: {
    type: Object as PropType<ICustomData>,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.address-info {
  width: 390px;
  font-weight: 400;
  .user-info {
    color: rgba(6, 21, 51, 0.65);
    .name {
      margin-right: 16px;
    }
  }
  .logistic-code {
    color: rgba(6, 21, 51, 0.45);
  }
  .server-info {
    .server-name {
      color: rgba(6, 21, 51, 0.45);
    }
  }
  div {
    margin-bottom: 4px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
