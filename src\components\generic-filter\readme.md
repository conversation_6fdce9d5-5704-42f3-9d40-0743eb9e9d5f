### GenericFilter 筛选器组件

`GenericFilter` 是基于 `RForm` 组件结合后端 api 封装的筛选器组件，传入 `formId` (表单 id) 作为调用后端 api 数据渲染筛选器选项。

组件筛选器内置搜索、重置、展开收缩功能，支持通过插槽扩展功能。

#### 代码演示

```vue
<template>
  <GenericFilter
    collapse
    :display="display"
    :form-dependencies="formDependencies"
    :form-id="formId"
    @reset="handleReset"
    @search="handleSearch"
  >
    <template #before>
      <a-button>按钮前</a-button>
    </template>
    <template #after>
      <a-button>按钮后</a-button>
    </template>
    <template #extra="{ searchData }">
      <a-button @click="handleExtra(searchData)"> 额外功能 </a-button>
      <a-button @click="toggleBtn"> 切换订单ID表单项显隐 </a-button>
    </template>
  </GenericFilter>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

import GenericFilter from '@/components/generic-filter/generic-filter.vue';

const formId = ref('order_v4');

const toggle = ref(false);

/**
 * 表单依赖项
 */
const formDependencies = {
  order_id: [toggle],
};

/**
 * 根据依赖项或表单数据显隐
 */
const display = {
  order_id: (formData, [toggle]) => {
    return formData.phone || toggle;
  },
};

function toggleBtn() {
  toggle.value = !toggle.value;
}

function handleSearch(searchForm) {
  console.log('-------search-----');
  console.log(searchForm);
}

function handleReset() {
  console.log('-------reset-----');
}

function handleExtra(searchData: Record<string, any>) {
  console.log('-------extra-----');
  console.log(searchData);
}
</script>
```

#### 组件属性

| 属性名 | 类型 | 默认值 | 必填 | 描述 |
| --- | --- | --- | --- | --- |
| formId | number | - | 是 | 表单 ID |
| collapse | boolean | false | 否 | 是否展示展开收缩 |
| display | Record<string, (formData: Record<string, unknown>, deps: Record<string, any>) => boolean> | {} | 否 | 是否展示展开收缩 |
| formDependencies | Record<string, any> | {} | 否 | 是否展示展开收缩 |
| storageKey | string | '' | 否 | 当前页面搜索数据缓存标识 |

#### 组件事件

<!-- ## 组件事件 -->

| 属性名 | 类型    | 默认值               | 必填 | 描述 |
| ------ | ------- | -------------------- | ---- | ---- |
| search | number  | (searchData) => void | 是   | 搜索 |
| reset  | boolean | () => void           | 否   | 重置 |

#### 组件插槽

<!-- ## 组件插槽 -->

| 插槽名 | 描述               |
| ------ | ------------------ |
| before | 查询重置按钮前插槽 |
| after  | 查询重置按钮后插槽 |
| extra  | 额外功能           |
