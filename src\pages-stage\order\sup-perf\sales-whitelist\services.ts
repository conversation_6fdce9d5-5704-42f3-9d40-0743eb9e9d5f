import { GET, IAxiosResponse, POST } from '@/services/api';
import {
  IApiAddUserWhitelistParams,
  IApiDelUserWhitelistParams,
  IApiGetUserWhitelistParams,
  IApiAddSalesBindMerchParams,
  IApiUserWhitelistData,
  IApiDelSalesBindMerchParams,
} from './data';
/**
 * 获取销售白名单
 */
export const APIGetUserWhitelist = (
  params: IApiGetUserWhitelistParams,
): Promise<IAxiosResponse<IApiUserWhitelistData[]>> => {
  return GET('/super/user-white-list/get-list', params);
};
/**
 * 新增销售白名单
 */
export const APIAddUserWhitelist = (params: IApiAddUserWhitelistParams) => {
  return GET('/super/user-white-list/add', params);
};
/**
 * 删除销售白名单
 */
export const APIDelUserWhitelist = (params: IApiDelUserWhitelistParams) => {
  return GET('/super/user-white-list/del', params);
};

/**
 * 新增码商绑定白名单
 */
export const APIAddSalesBindMerch = (params: IApiAddSalesBindMerchParams) => {
  return POST('/super/sales-bind-merch/do-bind', params);
};

/**
 * 获取码商绑定白名单
 */
export const APIGetSalesBindMerch = (params: IApiGetUserWhitelistParams) => {
  return GET('/super/sales-bind-merch/index', params);
};

/**
 * 删除码商绑定白名单
 */
export const APIDelSalesBindMerch = (params: IApiDelSalesBindMerchParams) => {
  return GET('/super/sales-bind-merch/un-bind', params);
};

// 获取部门列表
export const APIGetListSalesDepartment = (params: any) => {
  return GET('/super/sales-perform-financer/list-sales-department', params);
};
