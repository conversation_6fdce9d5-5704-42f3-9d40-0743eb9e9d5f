import { GET, POST } from '@/services/api';

import { getRole } from '../delivery-intercept-modal/config';

/** 订单拦截&物流拦截 */
export function getWorkOrder(data: any) {
  return GET(
    '/warehouse/logisticschanges/orderDeliveryWork/list',
    {
      work_status: 0, // 获取所有状态的工单
      page_no: 1,
      page_size: 999,
      ...data,
    },
    {
      hostType: 'Golang',
    },
  );
}

/** 修改工单状态 */
export function dealOrderWorkStatus(data: any) {
  const isSuper = getRole() === 'super';
  let url = '/order/deal-order-work-status';
  if (isSuper) {
    url = '/super/warehouse/oms-order-work/deal-order-work-status';
  }
  return POST(url, {
    ...data,
  });
}

/** 获取关联订单 */
export function getAssociationWorkNumber(params: { association_id: string; type: 1 }) {
  const isSuper = getRole() === 'super';
  return GET(`${isSuper ? '/super' : ''}/new-work-order/association-work-number`, params);
}
