import { computed, Ref } from 'vue';
import type { FormGroupItem } from 'rrz-web-design';

import type { TFormGroup } from '@/components/gather-search/src/data';
import { IFormListGroup } from '@/components/gather-search/src/data';

/** 搜索表单控件配置 */
export function useFormGroup(formGroup: Ref<TFormGroup[] | undefined>) {
  /** 为表单项的添加默认配置 */
  function setDefaultConfig(item: FormGroupItem): FormGroupItem {
    const defaultElProps: FormGroupItem['elProps'] = {};

    // 提示文案
    if (['renderSelect', 'renderCascader'].includes(item.fragmentKey)) {
      defaultElProps.optionFilterProp = 'label';
      defaultElProps.showSearch = true;
    }

    // 日期选择项的转化值
    if (['renderRangePicker', 'renderDatePicker'].includes(item.fragmentKey)) {
      defaultElProps.valueFormat = 'YYYY-MM-DD';
      defaultElProps.format = 'YYYY-MM-DD';
    }

    return {
      ...item,
      elProps: { ...defaultElProps, ...item.elProps },
    };
  }

  const bindFormGroup = computed<IFormListGroup[]>(() => {
    const group: IFormListGroup[] = [];
    let list: FormGroupItem[] = [];
    formGroup.value?.forEach(item => {
      if ('items' in item) {
        if (list.length) {
          group.push({ items: list.map(setDefaultConfig) });
          list = [];
        }
        group.push({ ...item, items: item.items?.map(setDefaultConfig) });
      } else {
        list.push(setDefaultConfig(item));
      }
    });
    if (list.length) {
      group.push({ items: list.map(setDefaultConfig) });
    }
    return group;
  });

  return { bindFormGroup };
}
