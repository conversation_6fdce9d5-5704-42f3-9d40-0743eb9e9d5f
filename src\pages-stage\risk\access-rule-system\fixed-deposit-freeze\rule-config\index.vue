<template>
  <layout-admin-page title="固定押金冻结配置">
    <template #extra>
      <a-space>
        <a-button @click="openOperationLogModal">
          操作日志
        </a-button>
        <a-button @click="openOrderExportModal">
          订单导出
        </a-button>
        <a-button
          type="primary"
          @click="openEditRuleModal()"
        >
          新建
        </a-button>
      </a-space>
    </template>
    <r-table
      ref="RTableRef"
      :api="apiGetFixedDepositAllocation"
      :columns="columns"
      :table-props="{
        scroll: { x: 1250 },
      }"
    >
      <template #tableBodyCell="{ record, text, column, index }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch
            :checked="Boolean(text)"
            checked-children="开"
            :loading="statusLoadingList[record.id]"
            un-checked-children="关"
            @change="changeStatus(record, index)"
          />
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <a-space>
            <span
              class="text-link"
              @click="openEditRuleModal(record.id)"
            >编辑</span>
            <span
              class="text-link"
              @click="goRuleDetail(record.rule_id)"
            >规则</span>
            <a-popconfirm @confirm="deleteRuleItem(record.id)">
              <template #title>
                <p>确定删除该规则吗？</p>
              </template>
              <span
                class="text-link danger"
                danger
              >删除</span>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </r-table>
  </layout-admin-page>
  <EditRuleModal
    :id="editRuleId"
    v-model:visible="editRuleVisible"
    @ok="handleEditRule"
  />
  <OrderExportModal v-model:visible="orderExportVisible" />
  <OperationLogModal v-model:visible="operationLogVisible" />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { ProTableColumnType } from '@/global-components/rrz-table/data.d';
import { apiChangeConfigStatus, apiDeleteFixedDepositConfig, apiGetFixedDepositAllocation } from './services';

import { EditRuleModal, OperationLogModal, OrderExportModal } from './components';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const RTableRef = ref();
const getTableList = (type: string = 'search') => RTableRef.value?.getTableList(type);
// 导出模块
const orderExportVisible = ref(false);

function openOrderExportModal() {
  orderExportVisible.value = true;
}

// 新建/编辑模块
const editRuleVisible = ref(false);
const editRuleId = ref('');

function openEditRuleModal(id?: string) {
  editRuleVisible.value = true;
  editRuleId.value = id || '';
}

function handleEditRule() {
  const type = editRuleId.value ? '' : 'search';
  getTableList(type);
}

// 操作日志模块
const operationLogVisible = ref(false);

function openOperationLogModal() {
  operationLogVisible.value = true;
}

const columns: ProTableColumnType[] = [
  {
    title: '排序',
    dataIndex: 'sort',
    width: 68,
    hideInSearch: true,
  },
  {
    title: '项目ID',
    dataIndex: 'id',
    width: 68,
  },
  {
    title: '项目名称',
    dataIndex: 'title',
    width: 100,
  },
  {
    title: '绑定类型',
    dataIndex: 'bind_type',
    width: 220,
    customRender({ text }: any) {
      return text.join('、');
    },
    hideInSearch: true,
  },
  {
    title: '修改人',
    dataIndex: 'updated_by',
    width: 88,
    hideInSearch: true,
  },
  {
    title: '修改时间',
    dataIndex: 'updated_at',
    width: 180,
    hideInSearch: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    fixed: 'right',
    hideInSearch: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 110,
    fixed: 'right',
  },
];

// 规则
function goRuleDetail(id: string) {
  //riskAccessRuleSystemFixedDepositFreezeDetail
  router.push({
    name: 'riskAccessRuleSystemFixedDepositFreezeDetail',
    params: {
      id,
    },
  });
}

const statusLoadingList = ref<boolean[]>([]);

function changeStatus(record) {
  statusLoadingList.value[record.id] = true;
  apiChangeConfigStatus({ id: record.id })
    .then(() => {
      // 刷新列表
      record.status = record.status === 1 ? 0 : 1;
      message.success('操作成功');
    })
    .finally(() => {
      statusLoadingList.value[record.id] = false;
    });
}

// 删除
function deleteRuleItem(id: string) {
  return apiDeleteFixedDepositConfig({ id }).then(() => {
    // 刷新列表
    getTableList();
  });
  // Modal.confirm({
  //   title: '删除规则',
  //   content: '确定删除该规则吗？',
  //   onOk() {
  //
  //   },
  // });
}
</script>
