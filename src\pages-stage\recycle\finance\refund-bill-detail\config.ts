import type { ColumnType } from 'ant-design-vue/lib/table';

export const columns: ColumnType[] = [
  {
    title: '退款账户',
    key: 'pay_account',
    dataIndex: 'pay_account',
    width: 160,
  },
  {
    title: '退款方式',
    key: 'pay_type',
    dataIndex: 'pay_type',
    width: 160,
  },
  {
    title: '退款金额',
    key: 'amount',
    dataIndex: 'amount',
    width: 160,
  },

  {
    title: '交易号/票据号',
    key: 'payment_flow_sn',
    dataIndex: 'payment_flow_sn',
    width: 280,
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
    width: 280,
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 60,
    fixed: 'right',
  },
];

export const sourceColumns: ColumnType[] = [
  {
    title: '源单日期',
    key: 'date',
    dataIndex: 'date',
    width: 120,
    fixed: 'left',
  },
  {
    title: '源单类型',
    key: 'order_type_label',
    dataIndex: 'order_type_label',
    width: 120,
  },
  {
    title: '源单编号',
    key: 'after_sale_no',
    dataIndex: 'after_sale_no',
    width: 180,
  },
  {
    title: '源单结算日期',
    key: 'finish_at',
    dataIndex: 'finish_at',
    width: 120,
  },
  {
    title: '源单币别',
    key: 'money_type_label',
    dataIndex: 'money_type_label',
    width: 88,
  },
  {
    title: '源单金额',
    key: 'pay_price',
    dataIndex: 'pay_price',
    width: 120,
  },

  {
    title: '源单已核销金额',
    key: 'refund_price',
    dataIndex: 'refund_price',
    width: 130,
    fixed: 'right',
  },
  {
    title: '源单未核销金额',
    key: 'not_refund_price',
    dataIndex: 'not_refund_price',
    width: 130,
    fixed: 'right',
  },
  {
    title: '本次核销金额',
    key: 'current_refund_price',
    dataIndex: 'current_refund_price',
    width: 130,
    fixed: 'right',
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 60,
    fixed: 'right',
  },
];

export const sourceSelectColumns: ColumnType[] = [
  {
    title: '单据日期',
    key: 'date',
    dataIndex: 'date',
    width: 120,
    fixed: 'left',
  },
  {
    title: '单据编号',
    key: 'after_sale_no',
    dataIndex: 'after_sale_no',
    width: 200,
    fixed: 'left',
  },
  {
    title: '客户',
    key: 'server_name',
    dataIndex: 'server_name',
    width: 120,
  },
  {
    title: '退款状态',
    key: 'refund_status_label',
    dataIndex: 'refund_status_label',
    width: 120,
  },
  {
    title: '币别',
    key: 'money_type_label',
    dataIndex: 'money_type_label',
    width: 88,
  },
  {
    title: '成交金额',
    key: 'pay_price',
    dataIndex: 'pay_price',
    width: 130,
  },

  {
    title: '已退款金额',
    key: 'refund_price',
    dataIndex: 'refund_price',
    width: 130,
  },
  {
    title: '未退款金额',
    key: 'not_refund_price',
    dataIndex: 'not_refund_price',
    width: 130,
  },
];
