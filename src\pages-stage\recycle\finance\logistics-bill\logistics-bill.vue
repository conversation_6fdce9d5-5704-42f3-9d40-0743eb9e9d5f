<template>
  <layout-shops-page :navs="['趣回收', '财务管理', '物流账单']">
    <div class="container">
      <div class="search-bar">
        <a-form
          ref="formRef"
          layout="inline"
          :model="formState"
          style="width: 100%"
        >
          <a-row style="width: 100%">
            <a-col :span="6">
              <a-form-item
                label="物流单号"
                name="waybill_no"
              >
                <a-input
                  v-model:value="formState.waybill_no"
                  placeholder="请输入物流单号"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="时间"
                name="day"
              >
                <a-range-picker
                  v-model:value="formState.day"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item
                label="订单号"
                name="order_id"
              >
                <a-input
                  v-model:value="formState.order_id"
                  placeholder="请输入订单号"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="账单类型"
                name="express_type"
              >
                <a-select
                  v-model:value="formState.express_type"
                  :options="[
                    {
                      label: '全部账单',
                      value: '',
                    },
                    {
                      label: '收件',
                      value: '0',
                    },
                    {
                      label: '寄回',
                      value: '1',
                    },
                  ]"
                  placeholder="请选择账单类型"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row style="width: 100%; padding-top: 24px">
            <a-col :span="6">
              <a-form-item
                label="用户id"
                name="user_id"
              >
                <a-input
                  v-model:value="formState.user_id"
                  placeholder="请输入用户id"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="用户名称"
                name="username"
              >
                <a-input
                  v-model:value="formState.username"
                  placeholder="请输入用户名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item>
                <a-button
                  type="primary"
                  @click="onHandleControl().search"
                >
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索
                </a-button>
                <a-button
                  style="margin-left: 10px"
                  @click="onHandleControl().reset"
                >
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
                <a-button
                  :loading="exportLoading"
                  style="margin-left: 8px"
                  type="primary"
                  @click="onExport"
                >
                  <template #icon>
                    <DownloadOutlined />
                  </template>
                  导出
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="main">
        <a-table
          class="list-table"
          :columns="columns"
          :data-source="tableList"
          :loading="isLoading"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.count,
            defaultPageSize:30,
            pageSizeOptions:['30'],
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total: number) => `共 ${total} 条数据 第${pagination.current}页`
          }"
          row-key="id"
          @change="onChangePageSize"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'username'">
              {{ record.username }}

              <DecryptField
                v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                :field-item="{
                  box: 'text',
                  id: record.user_id,
                  field: 'username',
                  field_type: 'name',
                  type: 139,
                }"
              />
            </template>
            <template v-if="column.dataIndex === 'updated_at'">
              {{ moment(Number(record.updated_at) * 1000).format('YYYY-MM-DD hh:mm') }}
            </template>
            <template v-if="column.dataIndex === 'order_id'">
              <a-popover
                v-if="Array.isArray(record.order_id)"
                color="rgba(6,21,51,0.75)"
                :overlay-style="{ width: '230px' }"
                placement="bottom"
              >
                <template #content>
                  <div style="color: #fff">
                    {{ record.order_id.join('、') }}
                  </div>
                </template>
                <span>{{ record.order_id.join('、') }}</span>
              </a-popover>
            </template>
            <!-- 物流备注 -->
            <template v-if="column.dataIndex === 'remarks'">
              <crm-remark v-bind="crmPropsMap[record.logistic_batch_no]" />
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </layout-shops-page>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import moment from 'moment';
import { clearObject } from '@/utils';
import useAsync from '@/hook/common/use-async';
import { getTableList, exportData } from './service';
import { useRemarks } from '@/pages-stage/recycle/common/use-remark';
import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
import { useViewPlaintextAuth } from '@/composables/use-decrypt-data';
const { viewPlaintextAuth } = useViewPlaintextAuth();
const { loadRemarks, crmPropsMap } = useRemarks('_recySf');

const columns: any[] = [
  {
    title: '物流单号',
    dataIndex: 'waybill_no',
    width: 60,
  },
  {
    title: '用户id',
    dataIndex: 'user_id',
    width: 100,
  },

  {
    title: '用户名称',
    dataIndex: 'username',
    width: 60,
  },
  {
    title: '账单生成时间',
    dataIndex: 'updated_at',
    width: 100,
  },

  {
    title: '支付费用',
    dataIndex: 'total',
    width: 90,
  },
  {
    title: '订单号',
    dataIndex: 'order_id',
    width: 100,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    width: 120,
  },
];
const { run, dataList: tableList, isLoading } = useAsync();
// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  waybill_no: '',
  day: [],
  order_id: '',
  express_type: '',
  user_id: '',
  username: '',
});

// 分页器
let pagination = reactive({
  current: 1,
  pageSize: 30,
  count: 0,
});

// 获取table数据
const onLoadList = async () => {
  const page = pagination.current;
  const page_size = pagination.pageSize;
  const payload = {
    ...formState.value,
    date_from: formState.value.day[0],
    date_to: formState.value.day[1],
    day: null,
  };
  await run(getTableList(clearObject({ page, page_size, ...payload })), {
    callback(data: any) {
      pagination.current = data.pageInfo.page;
      pagination.pageSize = data.pageInfo.pageSize;
      pagination.count = data.pageInfo.count;
      const ids = data.list.map(({ logistic_batch_no }: { logistic_batch_no: string }) => logistic_batch_no);
      loadRemarks(ids);
      return data.list;
    },
  });
};

const onChangePageSize = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  // 再次发起网络请求
  onLoadList();
};

const onHandleControl = () => ({
  search() {
    pagination.current = 1;
    onLoadList();
  },
  reset() {
    // 1.重置所有字段
    formRef.value?.resetFields();
    // 2. 初始化页码
    pagination.current = 1;
    pagination.pageSize = 10;
    // 3. 重新获取table数据
    onLoadList();
  },
});

const exportLoading = ref<boolean>(false);
const route = useRoute();
const onExport = () => {
  exportLoading.value = true;
  const payload = {
    ...formState.value,
    date_from: formState.value.day[0],
    date_to: formState.value.day[1],
    day: null,
  };
  exportData(payload)
    .then(() => {
      Modal.confirm({
        title: '提示',
        content: '已在后台异步导出，需要跳转到我的导出查看吗？',
        onOk: () => {
          // 统一跳转到这个页面去排队导出
          window.open(`${route.query.origin}/super/async-export/index`);
        },
      });
    })
    .finally(() => {
      exportLoading.value = false;
    });
};

onMounted(onLoadList);
</script>
<style scoped lang="less">
@import '../../common/base.less';
</style>
