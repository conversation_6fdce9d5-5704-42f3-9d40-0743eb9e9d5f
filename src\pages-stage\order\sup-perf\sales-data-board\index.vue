<template>
  <div class="sales-data-board-container">
    <layout-admin-page v-bind="pageLayoutConfig">
      <template #extra>
        <a-space>
          <template v-if="extraBtnList.length">
            <a-button
              v-for="(item, index) in extraBtnList"
              :key="index"
              :type="item.type"
              @click="item.callback"
            >
              <template
                v-if="item.icon"
                #icon
              >
                <component :is="item.icon" />
              </template>
              {{ item.text }}
            </a-button>
            <a-button
              style="margin-left: 4px"
              type="primary"
              @click="() => (showModal = true)"
            >
              <PlusOutlined />
              反馈工单
            </a-button>
          </template>
        </a-space>
      </template>
      <div class="search-wrap">
        <transition
          mode="out-in"
          name="fade"
        >
          <search-form
            v-show="isShowFilter"
            ref="searchFormRef"
            v-model="searchForm"
            :sales-dept-list="salesList"
          />
        </transition>
        <div class="search-btn-wrap">
          <a-space>
            <template v-if="searchBtnList.length">
              <a-button
                v-for="(item, index) in searchBtnList"
                :key="index"
                :style="item.btnStyle"
                :type="item.type"
                @click="item.callback"
              >
                {{ isFunction(item.text) }}
                <template
                  v-if="item.icon"
                  #icon
                >
                  <component :is="isFunction(item.icon)" />
                </template>
              </a-button>
            </template>
          </a-space>
        </div>
      </div>
    </layout-admin-page>
    <template v-if="isShowPage">
      <a-spin :spinning="loadingSpin">
        <!-- card区域 -->
        <card-item
          ref="cardItemRef"
          :search-form="searchForm"
        />
        <!-- 图表区域 -->
        <card-chart
          ref="cardChartRef"
          :search-form="searchForm"
        />
        <!-- 底部表格区域 -->
        <table-list
          ref="tableListRef"
          :search-form="searchForm"
        />
      </a-spin>
    </template>
    <!-- 设定目标 -->
    <setting-target-drawer
      v-model:visible="performanceTargetInfo.visible"
      :sales-list="salesList"
    />
    <!-- 导出区域 -->
    <export-modal
      v-model:visible="exportInfo.visible"
      :dept-id="searchForm.dept_id"
    />
    <!-- 创建工单 -->
    <CreatWorkOrder
      :category-id="1"
      :visible="showModal"
      @cancel="() => (showModal = false)"
      @ok="() => (showModal = false)"
    />
  </div>
</template>

<script lang="ts" setup>
import { CSSProperties, reactive, ref, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { AimOutlined, UpOutlined, DownOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { getPageBaseConfig } from './config';
import { apiGetSalesDeptList } from './services';
import { loadG2, loadG2Plot } from '@/utils/static-load';
import SearchForm from './components/search-form/index.vue';
import SettingTargetDrawer from './components/setting-target-drawer/index.vue';
import ExportModal from './components/export-modal/index.vue';
import TableList from './components/table-list/index.vue';
import CardItem from './components/card-item/index.vue';
import CardChart from './components/card-chart/index.vue';

import CreatWorkOrder from '@/pages-stage/company-sell/sales-feedback-work-order/work-order-list/components/create-work-order/index.vue';
import type { TFn, IBtnItem, TFnReturnType } from '../data-board-common-components/data';
import { ISelectItem, IFormData } from './data';

const { pageLayoutConfig } = getPageBaseConfig();

// 扩展按钮区域
const performanceTargetInfo = reactive<{ visible: boolean }>({
  visible: false,
});

const tableListRef = ref<InstanceType<typeof TableList>>();
const cardItemRef = ref<InstanceType<typeof CardItem>>();
const cardChartRef = ref<InstanceType<typeof CardChart>>();
const showModal = ref(false);

// fromData 修改为外部传入
const searchForm = ref<IFormData>({
  dept_id: null,
  sales_ids: [],
  dept_type: 1,
  month: '',
  time_type: 1,
});

const exportInfo = reactive<{ visible: boolean }>({
  visible: false,
});

const handleShowPerformanceTargetDrawer = () => {
  performanceTargetInfo.visible = true;
};

const handleShowExportModal = () => {
  exportInfo.visible = true;
};
const extraBtnList = ref<IBtnItem[]>([
  {
    text: '导出',
    field: 'export',
    callback: handleShowExportModal,
  },

  {
    text: '设定目标',
    field: 'add',
    callback: handleShowPerformanceTargetDrawer,
    icon: AimOutlined,
    type: 'primary',
  },
]);

// 搜索区域
const searchFormRef = ref<InstanceType<typeof SearchForm>>();
const isShowFilter = ref(true);
const filterStyle: CSSProperties = {
  color: '#3777FF',
};

const isShowPage = ref(false);
const loadingSpin = ref(false);
const isFunction = (field: string | TFn<TFnReturnType>) => {
  return typeof field === 'function' ? field() : field;
};

const resetData = async () => {
  searchForm.value = {
    dept_id: salesList.value.length === 1 ? salesList.value[0].value : null,
    sales_ids: [],
    dept_type: 1,
    month: '',
    time_type: 1,
  };
  await nextTick();
  if (searchForm.value.dept_id) {
    getData();
  } else {
    isShowPage.value = false;
  }
};

const getData = async () => {
  if (!searchForm.value.dept_id) {
    message.warning('请选择销售部门');
    return;
  }
  isShowPage.value = true;
  if (isShowPage.value) {
    loadingSpin.value = true;
  }
  await nextTick();
  Promise.allSettled([
    cardItemRef.value?.getData(),
    tableListRef.value?.getData(),
    cardChartRef.value?.getData(),
  ]).finally(() => {
    loadingSpin.value = false;
  });
};

const searchBtnList = ref<IBtnItem[]>([
  {
    text: '查询',
    field: 'query',
    type: 'primary',
    callback: getData,
  },

  {
    text: '重置',
    field: 'reset',
    callback: resetData,
  },
  {
    text: () => `${isShowFilter.value ? '收起' : '展开'}筛选`,
    field: 'filter',
    type: 'link',
    btnStyle: filterStyle,
    icon: () => (isShowFilter.value ? UpOutlined : DownOutlined),
    callback: () => (isShowFilter.value = !isShowFilter.value),
  },
]);

const salesList = ref<ISelectItem[]>([]);

const handleGetSalesDeptList = async () => {
  const { data } = await apiGetSalesDeptList();
  salesList.value = data.map((item: any) => ({
    label: item.name,
    value: item.id,
  }));
};

onMounted(async () => {
  Promise.allSettled([loadG2(), loadG2Plot(), handleGetSalesDeptList()]).then(() => {
    // 当前销售主管只有一个部门时 默认请求数据
    if (salesList.value.length === 1) {
      searchForm.value.dept_id = salesList.value[0].value;
      getData();
    }
  });
});
</script>

<style lang="less" scoped>
.sales-data-board-container {
  padding: 16px;
  .search-wrap {
    margin-top: -24px;
    padding: 24px;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  .fade-from,
  .fade-to {
    opacity: 1;
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.2s ease;
  }
}
</style>
