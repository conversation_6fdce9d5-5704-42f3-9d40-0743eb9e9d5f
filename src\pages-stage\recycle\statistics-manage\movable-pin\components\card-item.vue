<template>
  <div class="card-item">
    <div class="card-item__title">
      {{ title }}
    </div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.card-item {
  padding: 24px 32px;
  background: #fff;
  border-radius: 2px;
  &__title {
    margin-bottom: 24px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
