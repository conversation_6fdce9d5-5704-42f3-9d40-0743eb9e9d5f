<!-- 成交记录配置 -->
<template>
  <layout-admin-page
    :navs="['趣回收', '运营管理', '成交记录配置']"
    title="成交记录配置"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="
          () => {
            actionModalRef?.disPatch({
              action: 'CREATE',
            });
          }
        "
      >
        <template #icon>
          <plus-outlined />
        </template>
        添加记录展示
      </a-button>
    </template>
    <div class="page-container">
      <div class="page-searchForm">
        <form-create
          ref="searchFormRef"
          v-model:value="searchParams"
          class="search-form-grid"
          :form-group="searchFormGroup"
          :origin-props="{
            layout: 'inline',
          }"
        >
          <template #buttons>
            <a-button
              style="margin-right: 10px"
              type="primary"
              @click="getTableList"
            >
              查询
            </a-button>
            <a-button @click="reloadContext">
              重置
            </a-button>
          </template>
        </form-create>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table"
          :columns="columns"
          :data-source="list"
          :loading="listLoading"
          :pagination="page"
          :scroll="{ x: '100%' }"
          sticky
          @change="tableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button
                class="primary-link-btn"
                style="padding-left: 0"
                type="link"
                @click="reviewActionModal(record.id)"
              >
                查看
              </a-button>
              <a-button
                danger
                type="link"
                @click="removeAction(record.id)"
              >
                删除
              </a-button>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-switch
                :checked="record.status"
                checked-children="启用"
                checked-value="1"
                un-checked-children="停用"
                un-checked-value="2"
                @change="statusChange(record.id)"
              />
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 弹窗 -->
    <action-modal ref="actionModalRef" />
  </layout-admin-page>
</template>

<script setup lang="ts">
import { reactive, ref, createVNode, onMounted } from 'vue';
import { useTable } from '@/hook/component/use-table';
import { message } from 'ant-design-vue';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { columns, searchFormGroup } from './config';
import { deleteAction, toggleRecordStatus } from './service';

import ActionModal from './components/record-modal.vue';

import type { ITransactionTableList, ISearchParams } from './data';

/** 搜索参数 */
const searchParams = reactive<Partial<ISearchParams>>({});

/** form实例 */
const searchFormRef = ref<any>(null);

/** 弹窗组件实例 */
const actionModalRef = ref<InstanceType<typeof ActionModal> | null>(null);

const { list, listLoading, page, getTableList, tableChange } = useTable<ITransactionTableList, Partial<ISearchParams>>({
  url: '/super/recycle/order-record/list',
  totalKey: 'data.pageInfo.count',
  searchForm: searchParams,
});

/** 重置 */
const reloadContext = () => {
  searchFormRef.value?.getFormRef()?.resetFields();
  getTableList();
};

/** 删除 */
const removeAction = (id: string) => {
  Modal.confirm({
    icon: createVNode(ExclamationCircleOutlined),
    title: '你确定要删除这条记录吗？',
    content: '删除后将无法恢复',
    onOk: async () => {
      await deleteAction({ id });
      getTableList();
      message.success('操作成功');
    },
  });
};

/** 查看操作 */
const reviewActionModal = (id: string) => {
  actionModalRef.value?.disPatch({
    action: 'VIEW',
    params: {
      id,
    },
  });
};

/** 切换状态 */
const statusChange = async (id: string) => {
  try {
    listLoading.value = true;
    await toggleRecordStatus({ id });
    message.success('操作成功');
    getTableList();
  } finally {
    listLoading.value = false;
  }
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
.page-container {
  margin: 24px;
  .page-searchForm {
    margin: 24px 0;
  }
  .primary-link-btn {
    color: #3777ff;
  }
}
</style>
