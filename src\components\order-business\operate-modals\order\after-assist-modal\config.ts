export const goodItem = [
  {
    label: '商品名称',
    key: 'item_name',
  },
  {
    label: '数量',
    key: 'item_num',
  },
  {
    label: '套餐',
    key: 'sku_name',
  },
  {
    label: '运费',
    key: 'freight',
  },
  {
    label: '保险',
    key: 'insurance_money',
  },
];

export const normalItem = [
  {
    label: '工单状态',
    key: 'work_status',
  },
  {
    label: '工单类型',
    key: 'work_type_text',
  },
  {
    label: '订单ID',
    key: 'order_id',
  },
  {
    label: '订单租金',
    key: 'total_rental_money',
  },
  {
    label: '订单押金',
    key: 'deposit_amount',
  },
  {
    label: '租期',
    key: 'time_date',
  },
  {
    label: '租期类型',
    key: 'rental_type_text',
  },
  {
    label: '履约状态',
    key: 'honour_type_text',
  },
];

export const SUBTYPE = {
  补押金: [21, 41],
  逾期扣款: [22, 42],
  续租: [23, 43],
  赔偿款: [24, 44],
  运费: [25, 45],
  补租金: [26, 46],
  购买订单: [27, 47],
  新订单: [28, 48],
};

// 目前用到的只有rule
export const modalConfigData = {
  condition: {
    title: '申请条件说明',
    alertShow: false,
    cardList: [],
  },
  rule: {
    title: '申请规则说明',
    alertShow: true,
    cardList: [
      {
        cardTitle: '账单逾期',
        content: [
          {
            title: '前提条件',
            icon: 'smile',
            content: [
              '当存在预收租金时，逾期租金必须大于预收租金，才可申请转支付。',
              '例如：',
              '1.当逾期租金=62元，预收租金=60元————最多只支持转支付2元',
              '2.当逾期租金=59元，预收租金=60元————不支持转支付',
            ],
          },
          {
            title: '规则',
            icon: 'file',
            content: [
              '（1）逾期未超30天，冻结押金仅支持转租金',
              '（2）逾期超过30天，冻结押金仅支持转租金或抵扣购买款；',
            ],
          },
        ],
      },
      {
        cardTitle: '订单逾期',
        content: [
          {
            title: '前提条件',
            icon: 'smile',
            content: [
              '若同时存在账单逾期和订单逾期，优先按账单逾期规则。',
              '若无逾期账单，则无法转租金，需前往续租后再申请。',
            ],
          },
          {
            title: '规则',
            icon: 'file',
            content: [
              '·当订单处于未归还：',
              '（1）租赁到期未超15天，不支持申请',
              '（2）15＜租赁到期天数≤30，冻结押金仅支持转租金',
              '（3）租赁到期大于30天，冻结押金支持转租金或抵扣购买款；',
              '·订单处于已归还',
              '冻结押金支持转租金，自动生成逾期账单并进行抵扣',
            ],
          },
        ],
      },
    ],
  },
};

export const delayReasonTypeOptions = [
  // { label: '存在未支付赔偿款（货损）', value: 1 },
  // { label: '存在未支付违约金（包括逾期、提前归还）', value: 2 },
  { label: '订单/账单出现逾期', value: 5 },
  { label: '运费未支付（代扣失败、寄回到付）', value: 3 },
  { label: '其他', value: 4 },
];

export const changeBillPeriodColumns = [
  {
    title: '更换前期数',
    dataIndex: 'before_period',
  },
  {
    title: '更换后期数',
    dataIndex: 'after_period',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 120,
  },
];
