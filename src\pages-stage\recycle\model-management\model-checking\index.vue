<template>
  <layout-admin-page
    :navs="['趣回收', '模型管理', '模型校验']"
    title="模型校验"
  >
    <div class="form-wrap">
      <form-create
        ref="searchFormRef"
        v-model:value="searchParams"
        class="search-form-col"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="handleSearch"
          >
            查询
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleReset"
          >
            重置
          </a-button>
        </template>
      </form-create>
      <a-table
        class="table-box bottom-fix-table"
        :columns="listColumns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: '100%' }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <div class="status-block">
              <div
                class="point"
                :style="{ background: record.status === 1 ? '#00C8BE ' : '#FF4A57 ' }"
              />
              {{ record.status_text }}
            </div>
          </template>
          <template v-if="column.key === 'operator'">
            <div class="operator-box">
              <a
                style="color: #3777ff"
                type="link"
                @click="onHandleDetail(record.id)"
              > 详情 </a>
              <a
                style="color: #3777ff"
                @click="onHandleEdit(record.id)"
              > 编辑 </a>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useTable } from '@/hook/component/use-table';
import { IModelSearchParams } from './data';
import { searchFormGroup, listColumns } from './config';
import { getModelList } from './service';

const router = useRouter();

const searchParams = ref<IModelSearchParams>({
  model_name: '',
  status: null,
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  api: getModelList,
  searchForm: searchParams.value,
  totalKey: 'data.pageInfo.count',
});

const handleSearch = () => {
  getTableList('search');
};

const searchFormRef = ref<any>(null);
const handleReset = () => {
  searchFormRef.value.getFormRef().resetFields();
  getTableList('search');
};

const onHandleDetail = (id: string) => {
  router.push({ path: '/recycle/model-management/model-checking-detail', query: { type: 'look', id } });
};

const onHandleEdit = (id: string) => {
  router.push({ path: '/recycle/model-management/model-checking-detail', query: { type: 'edit', id } });
};

onMounted(() => {
  getTableList();
});
</script>
<style scoped lang="less">
.form-wrap {
  padding: 0 24px 24px;
  background-color: #ffff;
}

.table-box {
  padding-top: 24px;
}

.operator-box {
  display: flex;
  gap: 16px;
}

.status-block {
  display: flex;
  align-items: center;
  .point {
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 8px;
  }
}
</style>
