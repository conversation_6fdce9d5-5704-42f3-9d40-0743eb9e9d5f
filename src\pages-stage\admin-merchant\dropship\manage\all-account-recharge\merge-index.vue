<template>
  <layout-shops-page
    :navs="['采购账户', isReplacedSend ? `${isPlatform ? '平台' : '第三方'}采购账户充值` : '充值']"
    :title="isReplacedSend ? `${isPlatform ? '平台' : '第三方'}采购账户充值` : '充值'"
  >
    <template #title-prefix>
      <ArrowLeftOutlined
        :style="{ fontSize: '16px', color: '#9FA4AF', marginRight: '16px' }"
        @click="routerBack"
      />
    </template>
    <template #extra>
      <a-space :size="12">
        <template v-if="isReplacedSend">
          <a-button
            type="primary"
            @click="handleAlipaySetting"
          >
            支付宝配置
          </a-button>
          <a-button
            type="primary"
            @click="handleAddBankCard"
          >
            银行卡配置
          </a-button>
        </template>
        <a-button
          v-if="isPlatform"
          type="primary"
          @click="goCountList"
        >
          企业对公账号列表
        </a-button>
      </a-space>
    </template>
    <div class="container">
      <a-spin :spinning="loading">
        <template v-if="isReplacedSend">
          <a-alert
            v-if="isPlatform"
            banner
            message="账户存在透支时，必须充值完所有透支账户后，才可以充值其他账户；不输入充值金额则代表不充值该账户，充值金额不可低于最低充值金额"
          />
          <a-alert
            v-else
            message="说明:"
            show-icon
            type="warning"
          >
            <template #description>
              <div class="text-box">
                <div class="text">
                  1.第三方采购账户充值金额及收款账户需由运营人员配置，如无法支付或存在疑问请联系运营人员；
                </div>
                <div class="text">
                  2.商家需按照充值金额和收款账户进行充值，充值完成后，将会重新配置新的收款账户；
                </div>
                <div class="text">
                  3.若已存在配置的收款账户，平台需修改新的收款账户时，需商家同意后才支持修改;
                </div>
                <div class="text">
                  4.第三方采购账户所有支出均不支持开票
                </div>
              </div>
            </template>
          </a-alert>
          <div
            v-if="pageData"
            class="card-list"
          >
            <div class="platform-title">
              {{ (isPlatform ? '平台' : '第三方') + '采购总账户充值' }}
            </div>
            <div class="card-box">
              <div class="card-top">
                <div class="card-moeny">
                  <div class="card-title">
                    <i class="icon" />
                    <span class="title">账户余额(元)</span>
                  </div>
                  <div class="card-price">
                    <div>
                      <a-statistic
                        :precision="2"
                        :value="pageData[isPlatform ? 'accounts' : 'accounts_party']?.total_amount"
                      />
                    </div>
                  </div>
                </div>
                <div class="card-moeny ver-line">
                  <div class="card-title">
                    <i class="icon" />
                    <span class="title">冻结中余额(元)</span>
                  </div>
                  <div class="card-price">
                    <div>
                      <a-statistic
                        :precision="2"
                        :value="pageData[isPlatform ? 'accounts' : 'accounts_party']?.total_deposit_amount"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="pageData[isPlatform ? 'accounts' : 'accounts_party']?.accounts[0].acc_tips?.length"
                class="infer"
              >
                <div class="infer-title">
                  限制条件
                </div>
                <div class="infer-content">
                  <span
                    v-for="(option, opIndex) in pageData[isPlatform ? 'accounts' : 'accounts_party']?.accounts[0]
                      .acc_tips"
                    :key="opIndex"
                  >
                    {{ opIndex + 1 }}.{{ option }}；
                  </span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div
            v-if="pageData"
            class="account-balance"
          >
            <div class="balance-title">
              <i class="icon" />
              <span>账号余额(元)</span>
            </div>
            <div class="balance-content">
              <span>{{ pageData.accounts?.accounts[0].acc_amount.split('.')[0] ?? '0' }}</span>
              <span class="price-small">.{{ pageData.accounts?.accounts[0].acc_amount.split('.')[1] ?? '00' }}</span>
            </div>
          </div>
        </template>
        <div class="money-box">
          <div class="title">
            <div class="line-l" />
            <span>充值金额</span>
          </div>
          <template v-if="isPlatform">
            <form-create
              v-model:value="formData"
              class="search-form-grid"
              :form-group="formGroup"
            />
          </template>
          <template v-else>
            <a-form
              layout="horizontal"
              :model="thirdPartyFormData"
            >
              <a-form-item
                v-if="!isReplacedRecord"
                label="选择子账户"
              >
                {{ accounts_name }}
              </a-form-item>
              <a-form-item
                v-else
                label="第三方采购账户"
              >
                <a-input-number
                  v-model:value="rechargeConfig.amount"
                  :min="minRecAmount"
                  placeholder="请输入"
                  precision="2"
                />
                <div
                  v-if="!isNoMinRecAmount"
                  style="margin-top: 8px; color: red"
                >
                  充值金额小于最小充值金额
                </div>
              </a-form-item>
              <a-form-item label="充值金额">
                ￥{{ rechargeConfig?.amount ?? 0 }}
              </a-form-item>

              <a-button
                :disabled="!rechargeConfig?.amount"
                @click="handleRemittancePayment"
              >
                汇款支付
              </a-button>
              <div style="margin-top: 8px; font-size: 12px">
                温馨提示:由于第三方收款账号不唯一，点击“汇款支付”后，请在
                <span style="color: red">24小时内</span>上传支付凭证
              </div>
            </a-form>
          </template>
        </div>
        <template v-if="isPlatform">
          <PayMethodSelect
            v-model:currentSelectMethod="currentSelectMethod"
            :page-data="pageData"
          />
        </template>
        <!-- 只针对平台账号 -->
        <div
          v-if="isPlatform"
          class="footer"
        >
          <div
            class="center-box"
            :style="{ 'align-items': isNeedComputed && computedMoney ? 'flex-start' : 'center' }"
          >
            <div class="tips-box">
              <span
                v-if="currentSelectMethod === EPayType.ALIPAY"
                class="tips-text"
              >支付宝支付：立即到账</span>
              <span
                v-else
                class="tips-text"
              >企业对公支付：无需审核，立即到账</span>
            </div>
            <div class="amount-info">
              <div class="amount-box">
                <span>{{ isNeedComputed ? '总支付金额：' : '总充值金额：' }}</span><span class="money">￥{{ totalPrice }}元</span>
              </div>
              <div
                v-if="isNeedComputed && computedMoney"
                class="amount-desc"
              >
                ￥{{ computedMoney.amount }}+{{ computedMoney.amount + '*0.6%' }}={{ computedMoney.real_money }}元
              </div>
            </div>
            <a-button
              class="pay-btn"
              :disabled="!totalPrice || isPayBtnDisabled"
              type="primary"
              @click="payHandle"
            >
              去支付
            </a-button>
          </div>
        </div>
      </a-spin>
    </div>
    <BankCardSetting
      ref="bankCardSettingRefs"
      v-model:visible="bankCardSettingVisible"
    />
    <AlipaySetting
      ref="alipaySettingRef"
      v-model:visible="alipaySettingVisible"
    />
    <RemittancePayment
      v-model:visible="remittancePaymentVisible"
      :form-data="formData"
      :page-data="pageData"
      :recharge-config="rechargeConfig"
      :total-price="totalPrice"
      :type="(route.query.type as string)"
      @ok="getAccountsDetail"
    />
    <AccountDetailsModal
      v-model:visible="accountDetailsVisible"
      :item-details="itemDetails"
      :type="(route.query.type as string)"
    />
    <AccountListModal
      v-model:visible="accountDetailsListVisible"
      :accounts="pageData.accounts?.accounts"
      :accounts-party="pageData.accounts_party?.accounts"
      :is-show-recharge="false"
      :type="(route.query.type as string)"
    />
    <template v-if="route.query.type !== 'platform'">
      <UpdateNotificationModal />
    </template>
  </layout-shops-page>
  <ModalZhaoAccount
    ref="refModalZhaoAccount"
    :hide-tip-bottom="true"
  />
</template>

<script setup lang="ts">
import { computed, createVNode, h, nextTick, onMounted, Ref, ref, watch } from 'vue';
import { LocationQueryRaw, useRoute, useRouter } from 'vue-router';
import { Modal } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { ArrowLeftOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

import { FormGroupItem } from '@/components/form-create/src/typing';
import ModalZhaoAccount from '@/pages-stage/admin-merchant/dropship/manage/company-public-pay/components/modal-zhao-account.vue';
import AccountListModal from '@/pages-stage/admin-merchant/dropship/manage/purchasing-management/components/platform-sub-account/index.vue';
import UpdateNotificationModal from '@/pages-stage/admin-merchant/dropship/manage/purchasing-management/components/update-notification-modal/index.vue';
import floatCalculator from '@/utils/float-calculator';

import useTourGuide from '../composables/use-tour';
import AccountDetailsModal from './components/account-details-modal/index.vue';
import AlipaySetting from './components/alipay-setting/index.vue';
import BankCardSetting from './components/bank-card-setting/index.vue';
import PayMethodSelect from './components/pay-method-select/index.vue';
import RemittancePayment from './components/remittance-payment/index.vue';
import { EPayType, EServerType, IItemDetails, IPageData, IRechargeConfig } from './data.d';
import {
  createConfigByMoney,
  getAccounts,
  getIssetRepayment,
  getPayComputeMoney,
  getRechargeConfig,
  getRechargeRecords,
  isFixedAccount,
  postAliPayAccount,
  rechargeByCmb,
  savePayTag,
} from './services';

const tourGuide = useTourGuide();

const router = useRouter();
const route = useRoute();

function routerBack() {
  router.push('/merchant/dropship/manage/purchasing-management');
}

//是否为平台账户
const isPlatform = computed(() => route.query.type === 'platform');

//是否代发商家
const isReplacedSend = computed(() => route.query.isReplacedSend === '1');

const bankCardSettingVisible = ref<boolean>(false);
const bankCardSettingRefs = ref();

function handleAddBankCard() {
  bankCardSettingVisible.value = true;
  nextTick(() => {
    bankCardSettingRefs.value.getBankData();
  });
}
const alipaySettingVisible = ref<boolean>(false);
const alipaySettingRef = ref();
function handleAlipaySetting() {
  alipaySettingVisible.value = true;
  nextTick(() => {
    alipaySettingRef.value.getBankData();
  });
}

const remittancePaymentVisible = ref<boolean>(false);
async function handleRemittancePayment() {
  if (isReplacedRecord.value) {
    const res = await createConfigByMoney({ money: rechargeConfig.value?.amount });
    rechargeConfig.value = res.data;
  }
  remittancePaymentVisible.value = true;
}

const accountDetailsVisible = ref<boolean>(false);
const itemDetails = ref<IItemDetails>();

async function validatePass(value: number, min_rec_amount: number, acc_amount: number, id: string | number) {
  if (issetRepayment.value) {
    isPayBtnDisabled.value = true;
    return Promise.reject('需还清本月信用账单后，才可充值');
  }

  checkPaymentButtonDisablement();

  if (typeof value === 'number' && !isNaN(value) && acc_amount >= 0) {
    if (value < min_rec_amount) {
      return Promise.reject('不可低于最低充值额度：' + min_rec_amount + '元');
    }
  }

  if (acc_amount < 0 && !isNaN(value)) {
    if (!Number(formData.value[`${id}`])) {
      formData.value[`${id}`] = acc_amount * -1;
    }
  }

  return Promise.resolve();
}

const currentSelectMethod = ref<EPayType>(EPayType.COMPANY);
const pageData = ref<IPageData>({});
const formGroup = ref<(FormGroupItem & { exist?: (params: LocationQueryRaw) => boolean })[]>([]);
const loading = ref<boolean>(false);
const isPayBtnDisabled = ref<boolean>(false);

async function getAccountsDetail() {
  loading.value = true;
  totalPrice.value = 0;
  const res = await getAccounts().finally(() => {
    loading.value = false;
  });
  const accountsMap = res.data[isPlatform.value ? 'accounts' : 'accounts_party']?.accounts || [];
  formGroup.value = accountsMap.map((item: any) => {
    return {
      key: item.account_id,
      originProps: {
        label: item.acc_name,
        name: item.account_id,
        rules: [
          {
            validator: (_rule: Rule, value: number) =>
              validatePass(value, Number(item.min_rec_amount), Number(item.acc_amount), item.account_id),
            trigger: 'change',
          },
        ],
      },
      elProps: {
        allowClear: true,
        style: { width: '100%' },
        placeholder: '请输入',
        controls: true,
        mode: 'multiple',
        precision: 2,
        min: Number(item.acc_amount) < 0 ? Number(item.acc_amount) * -1 : 0,
      },
      fragmentKey: 'renderInputNumber',
      colspan: { md: 14, lg: 14, xl: 12, xxl: 8, xxxl: 6 },
    };
  });
  const { acc_amount: queryAccAmount, id: queryId} = route.query;
  accountsMap.forEach(item => {
    if (Number(item.acc_amount) < 0) {
      const isNeedDefault = isPlatform.value && queryAccAmount && queryId === item.account_id;
      formData.value[`${item.account_id}`] = isNeedDefault ? Number(queryAccAmount) : Number(item.acc_amount) * -1;
    }
  });
  pageData.value = res.data;
  if (Number(pageData.value.server_type) === EServerType.PERSONAL) {
    currentSelectMethod.value = EPayType.ALIPAY;
  }
  if (!isReplacedRecord.value) {
    handleSelectChange();
  }
}

const formData = ref<Record<string, number>>({});
const thirdPartyFormData = ref({
  accountId: null,
});

const totalPrice = ref(0);
const computedMoney = ref<Partial<{ real_money: string; handling_charge: string; amount: string }>>();

const isNeedComputed = computed(
  () => currentSelectMethod.value === EPayType.ALIPAY && Number(pageData.value.server_type) === EServerType.ENTERPRISE,
);

watch(
  () => [formData.value, isNeedComputed.value],
  async () => {
    let sum = 0;
    for (let key in formData.value) {
      sum = floatCalculator.add(sum, formData.value[key]);
    }
    if (isNeedComputed.value && sum) {
      const { data } = await getPayComputeMoney({ money: sum });
      totalPrice.value = data?.real_money || 0;
      computedMoney.value = data;
    } else {
      totalPrice.value = sum;
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
async function payHandle() {
  if (currentSelectMethod.value === EPayType.COMPANY) {
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: h('div', [
        h('p', null, '1、打款需一次性打款，不能分多次打款 （金额一致）'),
        h('p', null, '2、需用企业账户打款（勿用支付宝账号），账户需检查是否和信息库一致（特别是含空格的情况）'),
      ]),
      async onOk() {
        await companyPublicPay();
        savePayTag({ type: currentSelectMethod.value });
      },
      onCancel() {},
    });
  } else {
    await handleAliPay();
    savePayTag({ type: currentSelectMethod.value });
  }
}

async function handleAliPay() {
  let params: IItemDetails[] = [];
  const data = pageData.value[isPlatform.value ? 'accounts' : 'accounts_party'];
  for (let key in formData.value) {
    if (formData.value[key] && Number(formData.value[key]) > 0) {
      let reqObj = {};
      if (isNeedComputed.value) {
        const { data: resObj } = await getPayComputeMoney({ money: formData.value[key] });
        if (resObj) {
          reqObj = resObj;
        }
      }
      params.push({
        account_id: key,
        amount: formData.value[key],
        account_name: data?.accounts?.find((item: any) => item.account_id === key)?.acc_name as string,
        type: String(data?.type) || '',
        ...reqObj,
      });
    }
  }

  if (params.length) {
    const res = await postAliPayAccount({ recharge_acc: JSON.stringify(params) });
    window.open(res.data.url);
  }
}

const rechargeConfig = ref<IRechargeConfig>({
  amount: 0,
});
const accounts_name = ref('');
async function handleSelectChange() {
  const { account_id, acc_name } = pageData.value.accounts_party?.accounts[0];
  const res = await getRechargeConfig(account_id);
  rechargeConfig.value = res.data;
  accounts_name.value = acc_name;
}

const accountDetailsListVisible = ref<boolean>(false);

const issetRepayment = ref<boolean>(false);

async function getIssetRepaymentData() {
  const res = await getIssetRepayment();
  issetRepayment.value = res.data.isset_repayment;
  if (issetRepayment.value) {
    Modal.confirm({
      title: '暂时无法充值',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '前往还款',
      cancelButtonProps: { style: { display: 'none' } } as any,
      content: createVNode(
        'div',
        { style: 'color:red;' },
        '本月存在未还款的信用账单，您需还清本月信用账单后，才可充值。',
      ),
      onOk() {
        router.push({
          path: '/merchant/dropship/manage/refund',
          query: {
            the_month_repayment: route.query.the_month_repayment ?? '',
            total_repayment: route.query.total_repayment ?? '',
          },
        });
      },
    });
  }
}

function checkPaymentButtonDisablement() {
  const accountsMap = pageData.value[isPlatform.value ? 'accounts' : 'accounts_party']?.accounts || [];
  let isAnyAccountDisabled = false; // 标志变量，用于跟踪是否有账户被禁用

  for (const item of accountsMap) {
    if (formData.value[`${item.account_id}`]) {
      // 检查金额是否小于最小接收金额
      if (Number(formData.value[`${item.account_id}`]) < Number(item.min_rec_amount)) {
        isPayBtnDisabled.value = true;
        isAnyAccountDisabled = true; // 标记找到了一个不符合条件的账户
        break; // 终止循环
      }
    }
  }

  // 如果在循环中没有找到任何不符合条件的账户，则将isPayBtnDisabled设为false
  if (!isAnyAccountDisabled) {
    isPayBtnDisabled.value = false;
  }
}

const refModalZhaoAccount = ref<Ref<InstanceType<typeof ModalZhaoAccount>>>();

async function companyPublicPay() {
  const resFix = await isFixedAccount();
  if (resFix.data) {
    refModalZhaoAccount.value!.open({
      expire_at: '长期有效',
      bill_money: totalPrice,
      ...resFix.data,
    });
    return;
  }
  const data = pageData.value.accounts;
  //账号充值（招商银行）
  const params = Object.entries(formData.value)
    // eslint-disable-next-line
    .filter(([_, value]) => value && Number(value) > 0)
    .map(([key, value]) => {
      const account = data?.accounts.find((item: any) => item.account_id === key);
      return {
        account_id: key,
        amount: value,
        account_name: account?.acc_name || '',
        type: data?.type,
      };
    });
  await rechargeByCmb({ recharge_acc: JSON.stringify(params) });
  goCountList();
}

function goCountList() {
  router.push({
    path: '/merchant/dropship/manage/company-public-pay',
  });
}
const isReplacedRecord = ref(false);
async function getRechargeRecordsApi() {
  const res = await getRechargeRecords();
  isReplacedRecord.value = res.data;
}
const minRecAmount = computed(() => {
  return pageData.value.accounts_party?.accounts?.[0]?.min_rec_amount || '0.00';
});
const isNoMinRecAmount = computed(() => {
  return Number(rechargeConfig.value?.amount) >= Number(minRecAmount.value);
});
onMounted(() => {
  getAccountsDetail();
  getIssetRepaymentData();
  nextTick(() => {
    isPlatform.value && tourGuide.run();
  });
  getRechargeRecordsApi();
  nextTick(() => {
    const { acc_amount, id }  = route.query;
    if (isPlatform.value && acc_amount) {
      formData.value[id] = Number(acc_amount);
    }
  })
});
</script>

<style scoped lang="less">
.container {
  padding: 0 24px;

  .card-list {
    margin-top: 24px;

    .platform-title {
      margin-bottom: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }

    .card-box {
      width: 560px;
      padding: 24px 10px 16px 10px;
      background: #f9f9fb;
      border-radius: 8px;

      .card-top {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        padding: 0 14px;

        .ver-line {
          padding-left: 24px;
          border-left: 2px solid rgba(6, 21, 51, 0.06);
        }

        .card-moeny {
          box-sizing: border-box;
          width: 50%;

          .card-title {
            display: flex;
            align-items: center;
            color: rgba(6, 21, 51, 0.65);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;

            .icon {
              width: 4px;
              height: 16px;
              margin-right: 8px;
              background: #3777ff;
              border-radius: 2px;
            }
          }

          .card-price {
            display: flex;
            padding-top: 6px;

            .price {
              color: rgba(6, 21, 51, 0.85);
              font-weight: 500;
              font-size: 32px;
              line-height: 40px;
            }

            .price-small {
              margin-top: 6px;
              color: rgba(6, 21, 51, 0.85);
              font-weight: 500;
              font-size: 24px;
              line-height: 32px;
            }
          }
        }
      }

      .infer {
        width: 540px;
        padding: 16px 14px;
        background: #fefefe;
        border-radius: 4px;
      }
    }
  }

  .warn {
    color: #faad14 !important;
    background: #fef3dc !important;
  }

  .bill-box {
    display: flex;
    margin-bottom: 24px;
    padding: 16px 24px 10px;
    border: 1px solid rgba(6, 21, 51, 0.06);
    border-radius: 8px;

    .bill-item {
      display: flex;
      flex: 1;
      flex-direction: column;

      .title {
        display: flex;
        align-items: center;
        margin-bottom: 18px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;

        .line-l {
          display: block;
          width: 4px;
          height: 16px;
          margin-right: 8px;
          background: #3777ff;
          border-radius: 2px;
        }
      }

      .bill-content {
        margin-bottom: 6px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }

    .line-c {
      width: 1px;
      height: inherit;
      margin: 0 24px;
      background: rgba(6, 21, 51, 0.06);
    }
  }

  .money-box {
    margin: 24px 0;

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;

      .line-l {
        width: 4px;
        height: 16px;
        margin-right: 8px;
        background: #3777ff;
        border-radius: 2px;
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: flex-start;
    width: 100vw;
    height: 88px;
    padding: 14px 24px;
    background: #fff;
    border-top: 1px solid rgba(6, 21, 51, 0.06);

    .center-box {
      display: flex;
      align-items: flex-start;
      margin-right: auto;
      margin-left: auto;
      .amount-info {
        margin-right: 24px;
        font-weight: 500;
        .amount-box {
          color: rgba(6, 21, 51, 0.85);
          font-size: 20px;
          line-height: 28px;

          .money {
            color: #ff4d4f;
            font-size: 24px;
            line-height: 32px;
          }
        }
        .amount-desc {
          color: rgba(6, 21, 51, 0.65);
          font-size: 16px;
          line-height: 28px;
        }
      }

      .tips-box {
        position: relative;
        margin-right: 32px;
        padding: 5px 0;
        color: rgba(6, 21, 51, 0.45);
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        .tips-text::after {
          position: absolute;
          top: 50%;
          right: -16px;
          width: 1px;
          height: 16px;
          background: rgba(6, 21, 51, 0.15);
          transform: translateY(-50%);
          content: ' ';
        }
      }
      .pay-btn {
        width: 128px;
        height: 40px;
        margin: 10px 0;
      }
    }
  }
}

.account-balance {
  display: flex;
  flex-direction: column;
  width: 400px;
  height: 112px;
  margin-bottom: 24px;
  padding: 16px 24px 0;
  background: #3777ff;
  border-radius: 8px;

  .balance-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    color: #fff;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;

    .icon {
      display: block;
      width: 24px;
      height: 24px;
      margin-right: 4px;
      background: url('https://img1.rrzuji.cn/uploads/scheme/2402/20/m/BAiHMoFbZxuEehh4FmM6.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .balance-content {
    display: flex;
    color: #fff;
    font-weight: 500;
    font-size: 32px;
    line-height: 40px;

    .price-small {
      margin-top: 6px;
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }
  }
}

.footer-tips {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  color: rgba(6, 21, 51, 0.25);
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}

.infer {
  margin-bottom: 8px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;

  .infer-content {
    display: flex;
    flex-direction: column;
    color: rgba(6, 21, 51, 0.45);
  }
}
</style>
