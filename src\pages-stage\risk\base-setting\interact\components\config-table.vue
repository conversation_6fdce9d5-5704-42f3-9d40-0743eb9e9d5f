<template>
  <div class="config-table">
    <table class="table-content">
      <thead>
        <tr>
          <template
            v-for="(item, index) in columns"
            :key="index"
          >
            <td
              v-if="item.colspan !== 0"
              :colspan="item.colspan"
            >
              {{ item.title }}
            </td>
          </template>
        </tr>
      </thead>
      <tbody @click="tbodyClick">
        <tr
          v-for="(row, index) in dataSource"
          :key="index"
          :class="[
            row.isAdd && Number(index) % row.rowspan == 0 ? 'change-add-first' : '',
            row.isAdd && Number(index) % row.rowspan == row.rowspan - 1 ? 'change-add-last' : '',
            row.isDel && Number(index) % row.rowspan == 0 ? 'change-del-first' : '',
            row.isDel && Number(index) % row.rowspan == row.rowspan - 1 ? 'change-del-last' : '',
          ]"
        >
          <template
            v-for="(item, i) in columns"
            :key="i"
          >
            <td
              v-if="!item.rowspanKey || !(Number(index) % row[item.rowspanKey])"
              :class="[
                row[`${item.key}_change`] ? 'change' : '',
                row.isAdd ? 'change-add' : '',
                row.isDel ? 'change-del' : '',
              ]"
              :data-display="item.displayKey"
              :data-index="index"
              :data-key="item.key"
              :rowspan="item.rowspanKey ? row[item.rowspanKey] : 1"
              :style="{ width: item.width ? `${item.width}px` : undefined }"
            >
              <template v-if="item.slot === 'modelTag'">
                <div class="tags-box">
                  <a-tag
                    v-for="tag in row[item.key]"
                    :key="tag.id"
                    :class="tag.classType"
                    :closable="isEdit"
                    @close.prevent="closeTag(index, tag.id)"
                  >
                    {{ tag.name }}
                  </a-tag>
                  <a-tag
                    v-if="row[item.key].length && isEdit"
                    style="background: #fff; border-style: dashed"
                    @click="toAddCategory(Number(index))"
                  >
                    <plus-outlined />
                    {{ tableType == 'model' ? '新增型号' : '新增类目' }}
                  </a-tag>
                </div>
                <span
                  v-if="!row[item.key].length"
                  class="category-default-text"
                >兜底规则</span>
              </template>
              <template v-else-if="item.slot === 'level'">
                <div class="level-text">
                  {{ row.level.name }}
                </div>
              </template>
              <template v-else-if="item.slot === 'condition'">
                {{ row.condition.name }}
              </template>
              <template v-else>
                <a-select
                  v-if="isEdit"
                  :default-active-first-option="false"
                  :field-names="{ label: 'name', value: 'id' }"
                  :filter-option="false"
                  :not-found-content="null"
                  :options="select"
                  placeholder="input search text"
                  :show-arrow="false"
                  show-search
                  style="width: 120px"
                  :value="row[item.displayKey]"
                  @change="(value:number) => handleChange(value,row,item.displayKey,index)"
                />
                <div v-else>
                  {{ selectObj[row[item.displayKey]] }}
                </div>
              </template>
            </td>
          </template>
        </tr>
      </tbody>
    </table>
    <!-- <div
      v-show="moveVisible"
      class="move-box"
      :style="{ ...moveStyle }"
    >
      <a-input
        ref="moveInputRef"
        v-model:value="value"
        class="move-input"
        @blur="onBlur"
      />
    </div> -->
  </div>
</template>

<script lang="ts">
import { CSSProperties, defineComponent, nextTick, ref, Ref, PropType } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';

interface columnsItem {
  colspan?: number;
  title: string;
  rowspanKey: string;
  slot: string;
  isEdit: boolean;
  key: string;
  width: number;
  [key: string]: any;
}
interface pageSetupData {
  value: Ref<string>;
  moveInputRef: Ref;
  moveVisible: Ref<boolean>;
  moveStyle: Ref<CSSProperties>;
  tbodyClick: (e: Event) => void;
  closeTag: (index: number | string, id: number) => void;
  toAddCategory: (index: number) => void;
  dispatchHandle: ({ key }: { key: string }, index: number | string) => void;
  handleChange: (value: any, row: string, key: string, idx: number | string) => void;
}

export default defineComponent({
  components: {
    PlusOutlined,
  },
  props: {
    columns: {
      type: Array as PropType<columnsItem[]>,
      default: () => [],
    },
    dataSource: {
      type: Array as PropType<{ [key: string]: any }>,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    select: {
      type: Array as PropType<{ id: number; name: string }[]>,
      default: () => [],
    },
    selectObj: {
      type: Object as PropType<{ [key: number]: string }>,
      default: () => ({}),
    },
    tableType: {
      type: String,
      default: '',
    },
  },
  emits: ['change', 'tagHandle', 'handleChange'],
  setup(props, { emit }): pageSetupData {
    const value = ref<string>('');
    const moveInputRef = ref();
    const moveVisible = ref<boolean>(false);
    const moveStyle = ref<CSSProperties>({
      width: '108px',
      height: '54px',
      top: '0px',
      left: '0px',
    });
    const activeEdit: { index: number | string; key: string; display?: string } = {
      index: 0,
      key: '',
      display: '',
    };
    function tbodyClick(e: Event) {
      const targetDOM = e.target as HTMLElement;
      const { edit, index = 0, key = '', display } = targetDOM.dataset;
      if (edit === '1') {
        activeEdit.index = index;
        activeEdit.key = key;
        activeEdit.display = display;
        const { offsetTop, offsetLeft, offsetHeight, offsetWidth } = targetDOM;
        moveStyle.value = {
          width: `${offsetWidth}px`,
          height: `${offsetHeight}px`,
          top: `${offsetTop}px`,
          left: `${offsetLeft}px`,
        };
        moveVisible.value = true;
        nextTick(() => {
          value.value = props.dataSource[index][key];
          moveInputRef.value.focus();
        });
      }
    }

    function closeTag(index: number | string, id: number) {
      emit('tagHandle', { type: 'close', index, id });
    }
    function toAddCategory(index: number) {
      emit('tagHandle', { type: 'add', index });
    }
    function dispatchHandle({ key }: { key: string }, index: number | string) {
      emit('change', { key: 'zm_category', value: key, index });
    }
    // const obj = ref<{ [key: number]: string }>({});
    // watchEffect(() => {
    //   obj.value = props.selectObj1;
    // });
    function handleChange(value: any, row: string, key: string, idx: string | number) {
      emit('handleChange', { val: value, row, key, idx });
    }
    return {
      value,
      moveInputRef,
      moveVisible,
      moveStyle,
      tbodyClick,
      closeTag,
      toAddCategory,
      dispatchHandle,
      handleChange,
    };
  },
});
</script>

<style scoped lang="less">
.config-table {
  position: relative;
}
.table-content {
  width: 100%;
  text-align: center;
  border: 1px solid #e8e8e8;
  thead {
    height: 54px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 14px;
    line-height: 22px;
    background-color: #fafafa;
    td {
      border-right: 1px solid #e8e8e8;
    }
  }
  tbody {
    tr {
      border-top: 1px solid #e8e8e8;
      td {
        position: relative;
        height: 54px;
        border-right: 1px solid #e8e8e8;
        cursor: pointer;
        &.change {
          box-shadow: 0 0 0 1px #00c8be inset;
        }
        &.change-add:last-child {
          &::after {
            position: absolute;
            top: -1px;
            right: 0;
            bottom: -1px;
            width: 1px;
            background-color: #3777ff;
            content: '';
          }
        }
        &.change-del:last-child {
          &::after {
            position: absolute;
            top: -1px;
            right: 0;
            bottom: -1px;
            width: 1px;
            background-color: #ff4d4f;
            content: '';
          }
        }
      }
      &:hover {
        background-color: #e6f7ff;
      }
      &.change-add-first {
        border-top: 1px solid #3777ff;
        td:first-child {
          border-left: 1px solid #3777ff;
        }
      }
      &.change-add-last {
        border-bottom: 1px solid #3777ff;
      }
      &.change-del-first {
        border-top: 1px solid #ff4d4f;
        td:first-child {
          border-left: 1px solid #ff4d4f;
        }
      }
      &.change-del-last {
        border-bottom: 1px solid #ff4d4f;
      }
    }
  }
}
.move-box {
  position: absolute;
  .move-input {
    height: 100%;
    text-align: center;
  }
}
.tags-box {
  :deep(.ant-tag) {
    margin-bottom: 8px;
    &.add {
      border: 1px solid #3777ff;
    }
    &.delete {
      border: 1px solid #ff4d4f;
    }
  }
}
.level-text {
  padding: 0 30px;
}
:deep(.ant-select-selector) {
  border: none !important;
}
</style>
