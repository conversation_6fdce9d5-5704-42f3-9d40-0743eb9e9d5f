<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px' }"
    title="文章管理"
    :top-style="{ padding: '24px 24px 12px 24px' }"
  >
    <a-tabs
      v-model:activeKey="activeKey"
      @change="tabChange"
    >
      <template #rightExtra>
        <a-space>
          <a-button
            v-for="(item, index) in btns[activeKey]"
            :key="index"
            :type="item.type"
            @click="item.onClick"
          >
            <template
              v-if="item.icon"
              #icon
            >
              <component :is="item.icon" />
            </template>
            {{ item.text }}
          </a-button>
        </a-space>
      </template>

      <a-tab-pane
        :key="ETabKeys.ArticleList"
        tab="文章列表"
      >
        <ArticleList ref="articleListRef" />
      </a-tab-pane>
      <a-tab-pane
        :key="ETabKeys.ArticleCategoryManagement"
        tab="文章类目管理"
      >
        <ArticleCategory ref="articleCategoryRef" />
      </a-tab-pane>
    </a-tabs>
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { PlusOutlined } from '@ant-design/icons-vue';

import ArticleCategory from './components/article-category/article-category.vue';
import ArticleList from './components/article-list/article-list.vue';

/** 文章管理 */
enum ETabKeys {
  /** 文章列表 */
  ArticleList = 'articleList',
  /** 文章类目 */
  ArticleCategoryManagement = 'articleCategoryManagement',
}

const route = useRoute();
const tabKey = route.query.tabKey || ETabKeys.ArticleList;
const activeKey = ref(tabKey);
const articleListRef = ref();
const articleCategoryRef = ref();
const refs = {
  [ETabKeys.ArticleList]: articleListRef,
  [ETabKeys.ArticleCategoryManagement]: articleCategoryRef,
};

/** tabs对应的按钮 */
const btns = ref({
  [ETabKeys.ArticleList]: [
    {
      text: '添加文章',
      type: 'primary',
      icon: PlusOutlined,
      onClick: () => handleAdd(ETabKeys.ArticleList),
    },
  ],
  [ETabKeys.ArticleCategoryManagement]: [
    {
      text: '文章类目管理',
      type: 'primary',
      icon: PlusOutlined,
      onClick: () => handleAdd(ETabKeys.ArticleCategoryManagement),
    },
  ],
});

/** 添加事件 */
function handleAdd(key: ETabKeys) {
  refs[key].value.handleAdd();
}
</script>
