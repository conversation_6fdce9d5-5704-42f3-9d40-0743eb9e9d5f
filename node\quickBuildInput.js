import fs from 'node:fs';
import { join, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import { loadEnv } from 'vite';

const env = loadEnv('production', process.cwd(), 'VITE_');
export const parseEnvJSON = key => {
  try {
    return JSON.parse(env[key] || 'null');
  } catch (e) {
    console.log(e, 'e');
    return null;
  }
};

const quickEnvModules = parseEnvJSON('VITE_QUICK_BUILD_MODULES') || [];

if (quickEnvModules.length) {
  console.log('本次用户输入的需要构建发布的前端模块 ', quickEnvModules);
}

const result = {
  modules: [],
};

const deleteFile = filePath => {
  fs.unlink(filePath, err => {
    if (err) {
      console.error(`删除文件失败: ${err.message}`);
    } else {
      console.log('文件已成功删除');
    }
  });
};
export const writeRouter = extraModules => {
  const __filename = fileURLToPath(import.meta.url);
  const projectRoot = resolve(__filename, '..', '..');
  const indexPath = join(projectRoot, 'src', 'router', 'routers-stage', 'index.ts');
  const devIndexPath = join(projectRoot, 'src', 'pages-stage', 'base', 'index', 'index.tsx');
  console.log(devIndexPath, 'devIndexPath');
  deleteFile(devIndexPath);
  fs.readFile(indexPath, 'utf8', (err, data) => {
    if (err) {
      console.error('读取文件时出错:', err);
      return;
    }

    const conditionalBlockRegex = /\/\/\s*#ifdef\s+([\w-]+)([\s\S]*?)\/\/\s*#endif/g;

    const quickModules = [
      ...new Set(
        result.modules.concat(extraModules).map(item => {
          const idx = item.indexOf('（');
          return idx !== -1 ? item.slice(0, idx).trim() : item.trim();
        }),
      ),
    ];
    console.log('✅ 合并最终发布编译模块::->', quickModules);

    const processedData = data.replace(conditionalBlockRegex, (match, moduleName, content) => {
      if (quickModules.includes(moduleName)) {
        console.log(`✅ Enabled: ${moduleName}`);
        return content.trim(); // 保留实际内容
      } else {
        return '';
      }
    });

    const cleanedData = processedData
      .split('\n')
      .filter(line => line.trim() !== '')
      .join('\n');
    console.log('router.ts::\n', cleanedData);
    fs.writeFile(indexPath, cleanedData, 'utf8', err => {
      if (err) {
        console.error('写入文件时出错:', err);
        return;
      }
      console.log('模块加载成功即将发布！');
    });
  });
};

writeRouter(quickEnvModules);
