<template>
  <div class="page page-deposit-config">
    <page-breadcrumb :items="navs" />
    <div class="top-util-box flex-wrap flex-x-justify">
      <div class="top-util-left flex-wrap flex-y-center">
        <div class="top-util-text">
          交互式风控配置
        </div>
        <a-radio-group
          v-model:value="listType"
          @change="createPageData"
        >
          <a-radio-button value="0">
            预览版
          </a-radio-button>
          <a-radio-button value="1">
            线上版
          </a-radio-button>
        </a-radio-group>
      </div>
      <div
        v-if="tableEdit"
        class="top-util-right flex-wrap"
      >
        <a-dropdown>
          <a-button>
            更多
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu @click="dispatchHandle">
              <a-menu-item key="categary">
                添加类目规则
              </a-menu-item>
              <a-menu-item key="model">
                添加型号规则
              </a-menu-item>
              <a-menu-item key="log">
                修改日志
              </a-menu-item>
              <a-menu-item key="out">
                导出
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button
          style="margin: 0 8px"
          @click="resetConfig"
        >
          重置
        </a-button>
        <a-button
          type="primary"
          @click="submitConfig"
        >
          立即生效
        </a-button>
      </div>
    </div>
    <a-spin :spinning="pageLoading">
      <div class="config-content-box">
        <a-alert
          banner
          closable
          :show-icon="false"
        >
          <template #message>
            新版本上线后，旧版本会在新版本配置完成后关闭，请尽快完成新版本配置。<a
              class="link-text"
              @click="goNewVersion"
            >
              点击跳转新版本
            </a>
          </template>
        </a-alert>
        <div class="config-item">
          <div class="config-item-title">
            型号规则
          </div>
          <config-table
            :columns="modelCols"
            :data-source="modelRuleList"
            :is-edit="tableEdit"
            :select="interactive_type"
            :select-obj="selectObj"
            table-type="model"
            @handle-change="(state: any) => updateDataSource(state, 'model')"
            @tag-handle="updateRuleTag($event, MODEL)"
          />
        </div>
        <div class="config-item">
          <div class="config-item-title">
            类目规则
          </div>
          <config-table
            :columns="categoryCols"
            :data-source="categoryRuleList"
            :is-edit="tableEdit"
            :select="interactive_type"
            :select-obj="selectObj"
            table-type="category"
            @handle-change="(state: any) => updateDataSource(state, 'category')"
            @tag-handle="updateRuleTag($event, CATEGORY)"
          />
        </div>
      </div>
    </a-spin>

    <!-- 选择弹窗 -->
    <category-select
      :options="categorySelectOptions"
      @success="categorySelectSuccess"
    />

    <!-- 导出弹窗 -->
    <a-modal
      v-model:visible="exportData.visible"
      title="导出"
      @ok="handleExport"
    >
      <a-form>
        <a-form-item label="导出时间">
          <a-date-picker
            v-model:value="exportData.form.startTime"
            format="YYYY-MM-DD"
            placeholder="开始时间"
            style="width: 320px; margin-bottom: 16px"
            @open-change="handleStartOpenChange"
          />
          <br>
          <a-date-picker
            v-model:value="exportData.form.endTime"
            :disabled-date="disabledEndDate"
            format="YYYY-MM-DD"
            :open="exportData.endOpen"
            placeholder="结束时间"
            style="width: 320px"
            @open-change="handleEndOpenChange"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 修改日志 -->
    <log-vue v-model:visible="logVisible" />
  </div>
</template>

<script lang="ts">
import { defineComponent, VNodeChild, reactive, toRefs, Ref, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { DownOutlined } from '@ant-design/icons-vue';
import configTable from './components/config-table.vue';
import logVue from './components/log/index.vue';
import categorySelect from './components/categary-select.vue';
import { getDepositConfig, online2Preview, preview2Online, delCategoryFromRule, saveConfig } from './service';
import { selectType, modelCols, categoryCols } from './config';
import { formatModelSource2List, formatCategorySource2List } from './utils';
import { message, Modal } from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import qs from 'qs';
import { asyncEduce } from '@/utils/educe';

interface MenuInfo {
  key: string;
  keyPath: string[];
  item: VNodeChild;
  domEvent: MouseEvent;
}
interface ExportState {
  visible: boolean;
  form: {
    startTime: any;
    endTime: any;
  };
  endOpen: boolean;
}
interface pageSetupData {
  selectObj: Ref<{ [key: number]: string }>;
  interactive_type: Ref<any[]>;
  navs: string[];
  MODEL: selectType.MODEL;
  logVisible: Ref<boolean>;
  CATEGORY: selectType.CATEGORY;
  listType: Ref<'0' | '1'>;
  pageLoading: Ref<boolean>;
  modelCols: Ref<any[]>;
  modelRuleList: Ref<any[]>;
  categoryCols: Ref<any[]>;
  categoryRuleList: Ref<any[]>;
  tableEdit: Ref<boolean>;
  exportData: ExportState;
  createPageData: () => void;
  dispatchHandle: ({ key }: MenuInfo) => void;
  updateRuleTag: (options: { type: string; index: number; id: string }, type: selectType) => void;
  categorySelectOptions: {
    visible: boolean;
    type: selectType;
    addItemId: string;
    tags: string[];
    config: any;
  };
  categorySelectSuccess: ({ type }: { type: 'refresh' | 'close' }) => void;
  resetConfig: () => void;
  submitConfig: () => void;
  disabledEndDate: (current: Dayjs) => void;
  handleExport: () => void;
  handleEndOpenChange: (open: boolean) => void;
  handleStartOpenChange: (open: boolean) => void;
  updateDataSource: (state: any, type: string) => void;
  goNewVersion: () => void;
}
const { MODEL, CATEGORY } = selectType;

export default defineComponent({
  components: {
    DownOutlined,
    configTable,
    categorySelect,
    logVue,
  },
  setup(): pageSetupData {
    const route = useRoute();
    const interactive_type = ref<{ id: number; name: string }[]>([]);
    const selectObj = ref<{ [key: number]: string }>({});
    const navs = ['风控', '风控基础设置', '交互式风控配置'];
    const logVisible = ref(false);
    const state = reactive<{
      listType: '0' | '1';
      pageLoading: boolean;
      modelCols: any[];
      modelRuleList: any[];
      categoryCols: any[];
      categoryRuleList: any[];
      tableEdit: boolean;
    }>({
      listType: '0',
      pageLoading: false,
      modelCols: [],
      modelRuleList: [],
      categoryCols: [],
      categoryRuleList: [],
      tableEdit: false,
    });

    const exportData = reactive<ExportState>({
      visible: false,
      form: {
        startTime: '',
        endTime: '',
      },
      endOpen: false,
    });
    function dispatchHandle({ key }: MenuInfo) {
      switch (key) {
        case 'categary':
          categorySelectOptions.type = CATEGORY;
          categorySelectOptions.addItemId = '';
          categorySelectOptions.visible = true;
          break;
        case 'model':
          categorySelectOptions.type = MODEL;
          categorySelectOptions.addItemId = '';
          categorySelectOptions.visible = true;
          break;
        case 'out':
          exportData.form.startTime = '';
          exportData.form.endTime = '';
          exportData.visible = true;
          break;
        case 'log':
          logVisible.value = true;
          break;
      }
    }

    function handleExport() {
      const { startTime, endTime } = exportData.form;
      if (!startTime || !endTime) {
        message.warning('请完善导出信息!');
        return;
      }
      const params = {
        create_start_time: startTime.format('YYYY-MM-DD'),
        create_end_time: endTime.format('YYYY-MM-DD'),
      };
      asyncEduce(`/super/ant-audit-result/report-export?${qs.stringify(params)}`, '', route.query.origin as string);
    }
    function disabledEndDate(current: Dayjs) {
      if (!exportData.form.startTime) {
        return false;
      }
      return (
        exportData.form.startTime.valueOf() > current.valueOf() ||
        exportData.form.startTime.valueOf() + 604800000 < current.valueOf()
      );
    }
    function handleStartOpenChange(open: boolean) {
      if (!open) {
        exportData.endOpen = true;
      }
    }

    function handleEndOpenChange(open: boolean) {
      exportData.endOpen = open;
    }
    // 返还后端需要的数据
    function getModelList(id: string) {
      const listData = state.modelRuleList.filter((e: any) => e.id == id);
      const config: any = {};
      listData.forEach((item: any) => {
        const zm_ratio: any = {};
        staticOptions.zmRiskLevel.forEach((level: { id: string }) => {
          zm_ratio[level.id] = Number(item[`wholeRatio${level.id}`]) || '';
        });
        config[item.condition.id] = {
          risk_grade: zm_ratio,
        };
      });
      return config;
    }
    function getCatgoryList(id: string) {
      const listData = state.categoryRuleList.filter((e: any) => e.id == id);
      const config: any = {};
      listData.forEach((item: any) => {
        const zm_ratio: any = {};
        staticOptions.zmRiskLevel.forEach((level: { id: string }) => {
          zm_ratio[level.id] = Number(item[`wholeRatio${level.id}`]) || '';
        });

        if (!config[item.level.id]) {
          config[item.level.id] = {};
        }
        if (!config[item.level.id].is_new_product) {
          config[item.level.id]['is_new_product'] = {};
        }
        config[item.level.id]['is_new_product'][item.condition.id] = {
          risk_grade: zm_ratio,
        };
      });
      return config;
    }
    const categorySelectOptions = reactive<{
      visible: boolean;
      type: selectType;
      addItemId: string;
      tags: string[];
      config: string;
    }>({
      visible: false,
      type: CATEGORY,
      addItemId: '',
      tags: [],
      config: '',
    });
    function categorySelectSuccess({ type }: { type: 'refresh' | 'close' }) {
      if (type === 'refresh') {
        createPageData();
      }
      categorySelectOptions.visible = false;
    }

    const staticOptions: { [key: string]: any } = {
      condition: {},
      depositLevel: {},
      zmRiskLevel: [],
    };
    function createPageData() {
      state.pageLoading = true;
      getDepositConfig({ version: state.listType })
        .then(res => {
          interactive_type.value = res.data.interactive_type;
          interactive_type.value.forEach(item => {
            selectObj.value[item.id] = item.name;
          });

          res.data.is_new_product.forEach((item: { id: string; name: string }) => {
            staticOptions.condition[`con${item.id}`] = item;
          });
          res.data.deposit_grade.forEach((item: { id: string; name: string }) => {
            staticOptions.depositLevel[`dep${item.id}`] = item;
          });
          staticOptions.zmRiskLevel = res.data.risk_grade;
          const editCols: any[] = [];
          res.data.risk_grade.forEach((item: { id: string; name: string }) => {
            editCols.push({
              title: item.name,
              key: `ratio${item.id}`,
              displayKey: `wholeRatio${item.id}`, // TODO 这个 display 可以写成一个回调函数的形式，更灵活，待优化
              isEdit: true,
            });
          });

          // 处理model数据

          state.modelRuleList = formatModelSource2List(res.data.list.model_rule_info, staticOptions);
          state.modelCols = [...modelCols, ...editCols];
          console.log(res.data.list.category_rule_info);
          state.categoryRuleList = formatCategorySource2List(res.data.list.category_rule_info, staticOptions);
          state.categoryCols = [...categoryCols, ...editCols];
          state.tableEdit = state.listType === '0';
        })
        .finally(() => {
          state.pageLoading = false;
        });
    }

    function updateRuleTag(options: { type: string; index: number; id: string }, type: selectType) {
      let item = state.modelRuleList[options.index];

      if (type === CATEGORY) {
        item = state.categoryRuleList[options.index];
      }
      // 如果是删除，直接发请求处理
      // 如果是新增，赋值addItemId，并且弹窗处理
      if (options.type === 'close') {
        Modal.confirm({
          title: '提示',
          content: '确认删除吗？',
          onOk: () => {
            let params: any = {};
            params.rule_id = item.id;
            if (type === MODEL) {
              params.model_id = options.id;
            } else {
              params.category_id = options.id;
              // let is_new_product = getCatgoryList(item.id);
              // let category_rule_conf = { category_ids, deposit_grade: is_new_product };
              // let config = JSON.stringify(category_rule_conf);
            }
            return delCategoryFromRule(params).then(() => {
              message.success('删除成功！');
              createPageData();
            });
          },
        });
      } else if (options.type === 'add') {
        let model_ids: string[] = [];
        item.tags.forEach((tag: { id: string }) => {
          model_ids.push(tag.id);
        });

        if (type == '3') {
          let config = getModelList(item.id);
          categorySelectOptions.config = JSON.parse(JSON.stringify(config));
        } else if (type == '2') {
          let config = getCatgoryList(item.id);
          categorySelectOptions.config = JSON.parse(JSON.stringify(config));
        }

        categorySelectOptions.type = type;
        categorySelectOptions.addItemId = item.id;
        categorySelectOptions.visible = true;
        categorySelectOptions.tags = model_ids;
      }
    }

    // 重置
    function resetConfig() {
      Modal.confirm({
        title: '提示',
        content: '确定将【线上版】配置同步到【预览版】配置吗？',
        onOk: () => {
          return online2Preview({ version: '0' }).then(() => {
            message.success('同步成功！');
            createPageData();
          });
        },
      });
    }

    // 立即生效
    function submitConfig() {
      Modal.confirm({
        title: '提示',
        content: '确定将【预览版】配置同步到【线上版】配置吗？',
        onOk: () => {
          return preview2Online({ version: '0' }).then(() => {
            message.success('同步成功！');
          });
        },
      });
    }
    // 更新选项传给后端
    function updateDataSource(rulelist: any, type: string) {
      const key = rulelist.key;
      let params: any = {};
      if (type == 'model') {
        state.modelRuleList[rulelist.idx][key] = rulelist.val;
        let item = state.modelRuleList[rulelist.idx];
        params.rule_id = item.id;
        params.type = '1';
        let is_new_product = getModelList(item.id);
        let model_rule_conf = { is_new_product };
        let config = JSON.stringify(model_rule_conf);
        params.tmp_config = config;
      } else {
        state.categoryRuleList[rulelist.idx][key] = rulelist.val;
        let item = state.categoryRuleList[rulelist.idx];
        params.rule_id = item.id;
        params.type = '2';
        let is_new_product = getCatgoryList(item.id);
        let category_rule_conf = { deposit_grade: is_new_product };
        let config = JSON.stringify(category_rule_conf);
        params.tmp_config = config;
      }
      params.version = '0';
      console.log(params);
      return saveConfig(params);
    }

    onMounted(() => {
      createPageData();
    });

    function goNewVersion() {
      window.open(`${route.query.origin}/super/interactive-risk-route/index`);
    }

    return {
      selectObj,
      interactive_type,
      navs,
      MODEL,
      CATEGORY,
      exportData,
      ...toRefs(state),
      createPageData,
      dispatchHandle,
      updateRuleTag,
      categorySelectOptions,
      categorySelectSuccess,
      resetConfig,
      submitConfig,
      disabledEndDate,
      handleExport,
      handleEndOpenChange,
      handleStartOpenChange,
      updateDataSource,
      logVisible,
      goNewVersion,
    };
  },
});
</script>

<style scoped lang="less">
.page-deposit-config {
  padding: 16px;
}

.top-util-box {
  padding: 20px 32px;
  background-color: #fff;

  .top-util-text {
    margin-right: 24px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
  }
}

.config-content-box {
  padding: 24px 32px;
  background-color: #fff;
  border-top: 1px solid #e9e9e9;

  .config-item {
    margin-bottom: 24px;
  }

  .config-item-title {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 14px;
    line-height: 22px;
  }
}
.ant-alert-warning {
  margin-bottom: 24px;
}
.link-text {
  color: #1890ff;
}
</style>
