import { FormGroupItem } from '@/components/form-create/src/typing';

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'goods_name',
    fragmentKey: 'renderInput',
    originProps: { label: '商品搜索', name: 'goods_name' },
    elProps: { placeholder: '请输入编码/名称', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'third_platform_price_status',
    fragmentKey: 'renderSelect',
    originProps: { label: '今日市场价波动', name: 'third_platform_price_status' },
    options: [
      { label: '全部', value: '0' },
      { label: '是', value: '1' },
      { label: '否', value: '2' },
    ],
    elProps: { placeholder: '请选择', style: { width: '160px' } },
  },
];

export const columns: any = [
  {
    title: '商品ID',
    key: 'goods_id',
    dataIndex: 'goods_id',
    width: 116,
  },
  {
    title: '商品名称',
    key: 'name',
    dataIndex: 'name',
    width: 224,
  },
  {
    title: '个人回收价',
    key: 'single_price_between',
    dataIndex: 'single_price_between',
    width: 120,
    tipsText:
      '【该商品不同sku套餐内的最低价与最高价的价格区间】，更新频率【实时更新】，数据源【趣回收-回收商品表-估价配置】',
  },
  {
    title: '企业回收价',
    key: 'batch_price_between',
    dataIndex: 'batch_price_between',
    width: 120,
    tipsText: '【该商品不同sku套餐内的最低价与最高价的价格区间】，更新频率【实时更新】，数据源【趣回收- B端报价单】',
  },
  {
    title: '销售价格',
    key: 'sele_price_between',
    dataIndex: 'sele_price_between',
    width: 130,
    tipsText: '【销售订单数对应销售价格的总和】，更新频率【实时更新】，数据源【供应链】',
  },
  {
    title: '总库存',
    key: 'stock',
    dataIndex: 'stock',
    width: 92,
    tipsText: '【sku下所有在库实物库存数】，更新频率【实时更新】，数据源【供应链库存数】',
  },
  {
    title: '今日市场价异动',
    key: 'market_change',
    dataIndex: 'market_change',
    width: 130,
    tipsText: '【统计时间内爬虫平台对应的商品sku价格是否发生变更】，更新频率【实时更新】，数据源【爬虫任务系统】',
  },
  {
    title: '状态',
    key: 'auto_evaluate_price_status',
    dataIndex: 'auto_evaluate_price_status',
    width: 66,
  },
  {
    title: '操作',
    key: 'handle',
    dataIndex: 'handle',
    width: 114,
  },
];

// 计算规则展示的表格列配置
export const computedRuleTableColumns = [
  {
    title: '最新库存状态',
    key: 'now_stock_text',
    dataIndex: 'now_stock_text',
    width: 135,
    // 定制值控制是否渲染
    isHidden: (val: boolean) => val,
  },
  {
    title: '历史库存状态',
    key: 'stock_text',
    dataIndex: 'stock_text',
    width: 135,
    isHidden: (val: boolean) => val,
  },
  {
    title: '库存数',
    key: 'stock',
    dataIndex: 'stock',
    width: 90,
  },
  {
    title: '平均库存成本',
    key: 'avg_purchased_price',
    dataIndex: 'avg_purchased_price',
    width: 120,
  },
  {
    title: '回收商品id',
    key: 'goods_id',
    dataIndex: 'goods_id',
    width: 102,
  },
  {
    title: '近3周日均销售量',
    key: 'day_sale_num',
    dataIndex: 'day_sale_num',
    width: 102,
  },
  {
    title: '颜色',
    key: 'base1',
    dataIndex: 'base1',
    width: 162,
  },
  {
    title: '内存',
    key: 'base2',
    dataIndex: 'base2',
    width: 124,
  },
  {
    title: '价格类型',
    key: 'price_type',
    dataIndex: 'price_type',
    width: 124,
  },
  // 无标题，仅占位展示
  {
    title: '',
    key: 'symbol',
    dataIndex: 'symbol',
    width: 32,
  },
  {
    title: '调价依据',
    key: 'change_price_type',
    dataIndex: 'change_price_type',
    width: 162,
  },
  {
    title: '关系',
    key: 'arithmetic_type',
    dataIndex: 'arithmetic_type',
    width: 124,
  },
  {
    title: '数值',
    key: 'sku_base_price',
    dataIndex: 'sku_base_price',
    width: 162,
  },
];

// 计算规则表格-内容需要展示为select控件的key
export const keyToSelectConfigMap = {
  base1: {
    style: {
      width: '130px',
    },
    optionsKey: 'base1_list',
  },
  base2: {
    style: {
      width: '92px',
    },
    optionsKey: 'base2_list',
  },
  price_type: {
    style: {
      width: '92px',
    },
    optionsKey: 'price_type',
  },
  change_price_type: {
    style: {
      width: '130px',
    },
    optionsKey: 'change_price_type',
  },
  arithmetic_type: {
    style: {
      width: '92px',
    },
    optionsKey: 'arithmetic_list',
  },
};
