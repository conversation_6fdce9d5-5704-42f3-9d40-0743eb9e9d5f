<template>
  <div class="page">
    <div class="page-header">
      {{ shopNavInfo.pay_status === 1 ? '开通旺铺' : '旺铺模板' }}
    </div>
    <div class="page-content">
      <div class="left">
        <a-alert
          v-if="shopNavInfo.pay_status === 1"
          closable
          message="旺铺权益"
          show-icon
          style="margin-bottom: 24px"
          type="info"
        >
          <template #description>
            <div class="description">
              <div>1、开通旺铺一次性收费，后续永久免费</div>
              <div>2、开通旺铺可得 3套模板 + 27 种颜色</div>
              <div>3、开通旺铺支持二维码分享</div>
              <div>4、旺铺目前兼容（手机、微信、平板、电脑等各大平台）</div>
              <div>5、数据显示，开通旺铺租户停留时间3分钟以上，转化率提升300%</div>
              <div>6、详情可咨询020-2805-0999</div>
            </div>
          </template>
        </a-alert>
        <div>
          <div class="h3">
            模版样式
          </div>
          <a-radio-group
            v-model:value="colorModel.templateValue"
            name="radioGroup"
          >
            <a-radio
              v-for="item in templateList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-radio>
          </a-radio-group>
        </div>
        <div class="mt-24">
          <div class="h3">
            主题颜色
          </div>
          <div class="colors">
            <div
              v-for="(item, index) in colors"
              :key="index"
              class="mb-8"
            >
              <a-space :size="16">
                <div
                  v-for="(item2, index2) in item"
                  :key="index2"
                >
                  <div
                    class="item"
                    :style="{ background: item2.color }"
                    @click="checkColor(index, index2)"
                  />
                </div>
              </a-space>
            </div>
            <CheckCircleFilled
              v-show="iconTransform.isCheck"
              class="check"
              :style="{
                color: '#fff',
                left: iconTransform.left + 'px',
                top: iconTransform.top + 'px',
              }"
            />
          </div>
        </div>
        <a-space
          class="mt-16"
          :size="8"
          style="padding-bottom: 80px"
        >
          <a-button
            v-if="shopNavInfo.pay_status === 1"
            type="primary"
            @click="openShop"
          >
            ￥499开通旺铺
          </a-button>
          <a-button
            v-else
            type="primary"
            @click="saveSelect"
          >
            保存选择
          </a-button>
          <a-button
            class="preview"
            type="default"
            @click="preview"
          >
            预览首页效果
          </a-button>
          <!-- <div class="preview-button">

          </div> -->
        </a-space>
      </div>
      <div
        v-if="shopNavInfo.pay_status === 1"
        class="right"
      >
        <div class="title">
          旺铺案例展示
        </div>
        <div class="page-box">
          <div
            v-for="item in shopList"
            :key="item.shop_id"
            class="button-wrapper"
            @click="toShopPage(item.shop_id)"
          >
            <div class="button">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <form
      id="temp-preview"
      ref="formSubmitRef"
      :action="`${frontDomain}/shop/page-preview/shop?id=${shopNavInfo.shop_id}`"
      method="post"
      role="form"
      target="_blank"
    >
      <input
        id="preview-shop_name"
        name="shop_name"
        type="hidden"
        :value="shopNavInfo.shop_name"
      >
      <input
        id="preview-template"
        name="shop_template"
        type="hidden"
        :value="colorModel.templateValue"
      >
      <input
        id="preview-color"
        name="shop_color"
        type="hidden"
        :value="colorModel.baseValue.slice(1)"
      >
    </form>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { CheckCircleFilled } from '@ant-design/icons-vue';
import { fetchSelect, updateSelect, createShop } from './service';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';

type ShopType = {
  shop_id: number;
  name: string;
};

const { query } = useRoute();
const { origin, tips = undefined } = query;
tips && message.success(tips);

const shopList = ref<ShopType[]>([]);
const shopDomain = ref<string>('');
const frontDomain = ref<string>('');
const shopNavInfo = ref<Record<string, any>>({});
const templateList = ref<any[]>([]);
const shopTemplateList = ref({});
const getSelect = async () => {
  const res = await fetchSelect();
  const {
    showShop,
    shopDomain: s_domain,
    frontDomain: f_domain,
    shopNavInfo: s_info,
    shopTemplateList: s_t_list,
  } = res.data;
  templateList.value = [];
  shopList.value = showShop;
  shopDomain.value = s_domain;
  frontDomain.value = f_domain;
  shopNavInfo.value = s_info;
  shopTemplateList.value = s_t_list;
  colorModel.templateValue = s_info.shop_template;
  getColorIndex(s_info.shop_color);

  for (let key in s_t_list) {
    templateList.value.push({
      value: key,
      label: s_t_list[key],
    });
  }
  console.log(templateList.value);
};
getSelect();
const colorModel = reactive({
  baseValue: '',
  colorValue: '',
  templateValue: '',
  // isCustomCheck: false,
});

const getColorIndex = (color: string | number) => {
  let i = 0;
  let j = 0;

  let result = colors.every((item1, index1) => {
    i = index1;
    return item1.every((item2, index2) => {
      j = index2;
      if (item2.color === `#${color}`) {
        return false;
      }
      return true;
    });
  });
  if (!result) checkColor(i, j);
  else colorModel.baseValue = '';
  // else setCustomColor(color);
};

const colors = [
  [
    { color: '#666' },
    { color: '#ffff00' },
    { color: '#ffcc00' },
    { color: '#ffa500' },
    { color: '#cccc00' },
    { color: '#99cc66' },
    { color: '#66cc99' },
  ],
  [
    { color: '#66cc66' },
    { color: '#008000' },
    { color: '#339999' },
    { color: '#0099cc' },
    { color: '#44a4ff' },
    { color: '#336699' },
    { color: '#666699' },
  ],
  [
    { color: '#996699' },
    { color: '#663399' },
    { color: '#800080' },
    { color: '#ffc0cb' },
    { color: '#ff6666' },
    { color: '#a52a2a' },
    { color: '#ff0033' },
  ],
  [{ color: '#cc0033' }, { color: '#ff0000' }],
];
const iconTransform = reactive({
  isCheck: false,
  left: 0,
  top: 0,
});
const checkColor = (i1: number, i2: number) => {
  iconTransform.isCheck = true;
  const iWidth = 64;
  const iHeight = 32;
  const iSpace = 16;
  const icon = 16;
  colorModel.baseValue = colors[i1][i2].color;

  iconTransform.left = i2 * (iWidth + iSpace) + iWidth / 2 - icon / 2;
  iconTransform.top = i1 * (iHeight + iSpace / 2) + iHeight / 2 - icon / 2;
};

const getColor = () => {
  if (!!!colorModel.baseValue) return false;
  return colorModel.baseValue;
};

const openShop = async () => {
  const { status } = await createShop();
  if (status === 301) toPayWeb();
};

const saveSelect = async () => {
  let color = getColor();
  if (!color) return message.error('请选择颜色');
  const res = await updateSelect({
    TblShopNav: {
      shop_name: shopNavInfo.value.shop_name,
      shop_template: colorModel.templateValue,
      shop_color: color.slice(1),
    },
  });
  message.success(res.message);
  console.log(res);
};

const formSubmitRef = ref();
const preview = () => {
  formSubmitRef.value.submit();
};

const toPayWeb = () => {
  const url = `${origin}/shop/template/open`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};

const toShopPage = (id: number) => {
  window.open(`${shopDomain.value}/shop/${id}`);
};
</script>
<style lang="less" scoped src="./index.less"></style>
