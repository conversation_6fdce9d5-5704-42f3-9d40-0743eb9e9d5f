<template>
  <div class="generic-form">
    <RModalForm
      ref="refRModalForm"
      :form-group="formGroup"
      :form-props="formProps"
      :loading="loading"
      :modal-props="modalProps"
      :submit="onSubmit"
      :title="title"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { RModalFormInstance } from 'rrz-web-design';
import { RModalForm } from 'rrz-web-design';

import { useGenericForm } from '../../composables/generic-form';
import type { IGenericFormProps } from '../../composables/generic-form/data';
const props = withDefaults(defineProps<IGenericFormProps>(), {
  modalType: 'add',
  id: 0,
  titlePrefix: '',
});

const emits = defineEmits(['update:open', 'success']);

const refRModalForm = ref<RModalFormInstance | null>(null);

function afterClose() {
  emits('update:open', false);
}
const modalProps = { afterClose, destroyOnClose: true, bodyStyle: { maxHeight: '520px', overflow: 'auto' } };
const formProps = { labelCol: { style: { width: '120px' } } };
const disabled = computed(() => props.modalType === 'view');
watch(
  () => props.open,
  val => {
    if (val) {
      modalProps.footer = disabled.value ? null : undefined;
      refRModalForm.value.open();
      afterOpen();
    }
  },
);

const { formGroup, title, getFormConfig, fetchFormData, updateFormData, addFormData } = useGenericForm(props, disabled);

const loading = ref(false);
async function afterOpen() {
  loading.value = true;
  try {
    if (formGroup.value.length === 0) {
      await getFormConfig();
    }
    if (props.modalType !== 'add') {
      await echoingData();
    }
  } finally {
    loading.value = false;
  }
}

async function echoingData() {
  const data = await fetchFormData();
  const useForm = refRModalForm.value!.useForm;
  useForm.setFieldsValue(data);
}

async function onSubmit(params) {
  const submit = props.modalType === 'add' ? addFormData : updateFormData;
  await submit(params);
  emits('success');
}
</script>
