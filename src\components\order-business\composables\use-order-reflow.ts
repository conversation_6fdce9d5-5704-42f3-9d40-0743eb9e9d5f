import { useRoute } from 'vue-router';
import { Modal } from 'ant-design-vue';
import { useBoolean } from 'rrz-web-design';

import { postCheckReuseDistribute, postHandleReuseDistribute } from '@/components/order-business/composables/services';

export interface IReflowParams {
  order_id: string;
  reason_type: number;
  reason: number;
}

export function useOrderReflow() {
  const [loading, setLoading] = useBoolean();
  const isSuper = useRoute().query.role === 'super';

  /** 触发订单回流 */
  function handleOrderReflow(params: IReflowParams) {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '提示',
        content:
          '选择该取消原因成功取消后，订单号将会变更成新的订单号，订单相关的部分信息(例如用户信息、crm备注)将会消失，同时本订单依旧计入发货率考核，确定吗',
        onOk() {
          const { order_id, reason_type, reason } = params;
          const cancel_reason = [reason_type, reason].join('-');
          setLoading(true);
          postHandleReuseDistribute({ order_id, cancel_reason }, isSuper)
            .then((...args) => {
              resolve(...args);
              // 回流成功后商家后台打开消息弹窗
              !isSuper && alertBackendNotification();
            })
            .catch(reject)
            .finally(() => setLoading(false));
        },
        onCancel() {
          reject('取消订单回流');
        },
      });
    });
  }

  /** 查询是否符合回流 */
  function checkOrderReflow(params: IReflowParams) {
    return new Promise((resolve, reject) => {
      const { order_id, reason_type, reason } = params;
      const cancel_reason = [reason_type, reason].join('-');
      setLoading(true);
      postCheckReuseDistribute({ order_id, cancel_reason }, isSuper)
        .then(resolve)
        .catch(reject)
        .finally(() => setLoading(false));
    });
  }

  /** 通知yii端口打开消息通知 */
  function alertBackendNotification() {
    setTimeout(() => {
      window.parent.postMessage({ action: 'openNotificationBox' }, '*');
    }, 1500);
  }

  return { loading, handleOrderReflow, checkOrderReflow };
}
