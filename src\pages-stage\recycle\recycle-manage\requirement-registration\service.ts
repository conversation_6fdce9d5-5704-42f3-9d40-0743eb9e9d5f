import { POST, GET } from '@/services/api';
import { IExcelModel } from '../../quotation-manage/quotation-registration/data';
import { IRequireForm } from './data';

// 报价登记
export const addRequirements = (params: IExcelModel[]) => {
  return POST<IExcelModel[], []>('/super/recycle/demand-regis-quotation/save', params);
};
// 获取今日价格  || 获取代发商品回收报价
export const getTodayPrice = () => {
  return POST<void, { content: IExcelModel[]; id: string }>('/demand-regis-quotation/quotation');
};
// 商家添加回收需求
export const demandRequirements = (params: IRequireForm) => {
  return POST<IRequireForm, []>('/demand-regis/save', params);
};

//获取非代发商品回收报价
export const getNoDropPrice = () => {
  return GET<void, { content: IExcelModel[]; id: string }>('/demand-regis-quotation/quotation-for-not-replace');
};
