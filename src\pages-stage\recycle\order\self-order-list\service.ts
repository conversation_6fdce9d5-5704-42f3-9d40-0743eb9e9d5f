import { GET, POST } from '@/services/api';

type status = 1 | 0;
export type sfParams = {
  order_id: string;
  is_insure: status;
  is_packed: status;
  sf_address: string;
  send_start_time: `${string} ${string}`;
  is_change_receive: number;
  receive_address: string;
};

// 获取table数据
export function getTableList(params: any) {
  return POST('/super/recycle/order/list', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 状态筛选器
export function getStatusOptions() {
  return GET('/common/form-filter?page_from=recycleOrder_list');
}

// 查看分类详情
export function searchCategory(orderId: string, price: number) {
  return POST('/super/recycle/order/set-purchased-price', {
    orderId,
    price,
  });
}

// 地址筛选器
export function getAddressOptions(params: any) {
  return GET('/super/recycle/order-sf-express/get-address-list', params);
}

// 顺丰取件
export function sfExpress(params: sfParams) {
  return POST('/super/recycle/order-sf-express/return', params);
}

// 查看预估信息
export function getEvaluateInfo(orderId: string) {
  return GET(`/super/recycle/order/view-evaluate?orderId=${orderId}`);
}

// 查看检测报告
export function getQuality(orderId: string) {
  return GET(`/super/recycle/order/view-quality-report?orderId=${orderId}`);
}

// 关闭订单
export function closeOrder(orderId: string) {
  return GET(`/super/recycle/order/set-order-close?orderId=${orderId}`);
}

/**
 * @description: 获取物流轨迹
 * @param {any} data
 * @return {*}
 */
export function getLogisticsTrajectory(data: any) {
  data.method = 'logistic.code.get';
  return POST('/api/gateway', data, {
    successStatusCheckValue: -3,
  });
}

// 获取省市区 - 只单独获取一层
export function getAddressLevelData(params: any) {
  return GET('/super/recycle/order-sf-express/sf-province', params);
}

// 获取脱敏数据
export function getHideInfo(params: any) {
  return GET('/super/recycle/order/get-hide-info', params);
}

/**
 * Description 顺丰取消预约
 * @param {any} data:any
 * @returns {any}
 */
export function cancelDelivery(data: any) {
  return POST('/super/recycle/order-sf-express/cancel-return', data);
}

//导出
export function exportList(params: any) {
  return POST('/super/recycle/order/export', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// c端批量回收订单修改IMEI
export function updateImei(params: any) {
  return POST('/super/recycle/order/save-imei', params);
}
