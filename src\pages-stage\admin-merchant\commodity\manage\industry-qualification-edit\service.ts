import { GET, POST } from '@/services/api';
const hostType = 'Golang';
//创建行业资质
export function createCredentialIndustryApi(data: any) {
  return POST('/server/ServerCredentialIndustry/create', data, { hostType });
}
//获取行业资质详情
export function getIndustryInfoDetailApi(params: any) {
  return GET('/server/ServerCredentialIndustry', params, { hostType });
}
//修改行业资质
export function updateCredentialIndustryApi(data: any) {
  return POST('/server/ServerCredentialIndustry/update', data, { hostType });
}
