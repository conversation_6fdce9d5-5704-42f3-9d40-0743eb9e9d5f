<template>
  <div ref="groupRef">
    <a-tag
      v-for="item in orderGroupData"
      :key="item?.id"
      closable
      @close="deleteGroup(item.id)"
    >
      {{ item?.name }}
    </a-tag>
    <a-button
      size="small"
      style="font-size: 12px"
      type="dashed"
      @click="showModal"
    >
      <span style="margin-right: 8px">+ </span>
      添加分组
    </a-button>
  </div>
</template>
<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue';
import { message } from 'ant-design-vue';

import { basicModal } from '@/components/order-business/basic-modals';
import { resetLazyLoadData } from '@/components/order-business/feature-merchant/order-add-group/helper';
import { useUnityModal } from '@/components/unity-modal';
import { useLazyLoader } from '@/composables/use-lazy-loader';

import { TOrderGroupData } from './data';
import { ApiDelOrderGroup, getOrderGroupBindList } from './services';

const props = defineProps({
  orderId: {
    type: String,
    default: '',
  },
});

const groupRef = ref();
const orderGroupData = ref<TOrderGroupData[]>([]);

const { onToggleComponent } = useUnityModal();

function showModal() {
  onToggleComponent(basicModal.addGroup, {
    orderId: props.orderId,
    onChangeOrderGroup: loadData,
  });
}

async function loadData() {
  const { data } = await getOrderGroupBindList({ order_id: props.orderId });
  orderGroupData.value = data.filter(item => item.is_bind).map(item => ({ id: item.id, name: item.name }));
}

/** 删除分组 */
async function deleteGroup(group_id: string) {
  await ApiDelOrderGroup({ id: group_id, order_id: Number(props.orderId) });
  message.success('删除成功');
  loadData();
}

// 设置数据懒加载
const { setupObserver } = useLazyLoader(loadData, groupRef);
// 添加重置方式，用于外部对组件内的数据进行刷新
const { setResetFn, deleteResetFn } = resetLazyLoadData();
setResetFn(props.orderId, setupObserver);
onBeforeUnmount(() => {
  deleteResetFn(props.orderId);
});
</script>
