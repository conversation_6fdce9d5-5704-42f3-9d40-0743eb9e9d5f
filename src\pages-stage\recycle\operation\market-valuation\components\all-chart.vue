<template>
  <div class="table-box flex-wrap flex-vertical flex-con">
    <a-spin
      :spinning="loading"
      wrapper-class-name="flex-con"
    >
      <a-row
        v-if="chartList.length"
        :gutter="16"
        :wrap="true"
      >
        <template
          v-for="(item, index) in chartList"
          :key="item.category_id"
        >
          <a-col
            :sm="12"
            :xs="24"
            :xxl="8"
          >
            <a-card
              :body-style="{ padding: '16px' }"
              class="chart-item"
            >
              <div class="item-top">
                <div class="title">
                  {{ item.title }}
                </div>
                <week-picker
                  v-model:value="item.range"
                  class="block"
                  @pick="(timeArr: dayjs.Dayjs[]) => onRangeChange(item,index,timeArr)"
                />
              </div>
              <a-row
                :gutter="16"
                :wrap="true"
              >
                <template
                  v-for="chart in item.memory_list"
                  :key="chart.memory"
                >
                  <a-col :span="12">
                    <div class="chart-top">
                      <div class="chart-top-left">
                        <div class="chart-memory">
                          {{ chart.memory }}
                        </div>
                        <a-select
                          v-model:value="chart.current_new_ratio"
                          :options="chart.new_ratio"
                          size="small"
                          @change="
                            handleRatioChange(
                              {
                                category_id: Number(item.category_id),
                                brand_id: Number(item.brand_id),
                                model_id: Number(item.model_id),
                                start_at: item.start_date,
                                end_at: item.end_date,
                                memory: chart.memory,
                                new_ratio: chart.current_new_ratio,
                              },
                              chart.chart_key,
                            )
                          "
                        />
                      </div>
                      <div
                        class="chart-top-right"
                        @click="handleRoute(2, item, chart.memory)"
                      >
                        <div class="text">
                          所有成新
                        </div>
                        <RightOutlined
                          class="right-icon"
                          style="width: 12px; height: 12px"
                        />
                      </div>
                    </div>
                    <div
                      class="chart-container"
                      @click="handleToDegree(item, chart)"
                    >
                      <div
                        :id="chart.chart_key"
                        class="chart-container-item"
                      />
                    </div>
                  </a-col>
                </template>
              </a-row>
              <div
                v-if="item.has_more === 1"
                class="model-more"
              >
                <a-button @click="handleRoute(1, item)">
                  <template #icon>
                    <AppstoreOutlined />
                  </template>
                  查看所有内存
                </a-button>
              </div>
            </a-card>
          </a-col>
        </template>
      </a-row>
      <a-empty v-else />
    </a-spin>
    <a-row>
      <a-pagination
        v-model:current="state.page"
        v-model:page-size="state.page_size"
        hide-on-single-page
        :show-total="(total: number) => `共 ${total} 个型号`"
        :total="state.total"
        @change="changePage"
      />
    </a-row>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { AppstoreOutlined, RightOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { queryChartList, queryChartDetail } from '../service';
import type { IAllChart, IState, IBreadCrumb } from '../data';
import WeekPicker from '../../../common/components/week-picker.vue';
import { useLineChart } from '../../../common/use-line-chart';

const props = defineProps({
  modelList: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['update:breadCrumbList']);

const loading = ref(true);
const chartList = ref<IAllChart[]>([]);
const dateFormat = 'YYYY-MM-DD';

const state = reactive<Partial<IState>>({
  page: 1,
  page_size: 10,
  total: 0,
});

// 渲染折线图表
const { render: renderLineChart } = useLineChart();
const generateChart = async (item: any, key?: string) => {
  let currentContainer, chart_key, source;
  //是否改变单个图表
  if (key) {
    chart_key = key;
    currentContainer = document.querySelector(`#${key}`);
  } else {
    chart_key = item.chart_key;
    currentContainer = document.querySelector(`#${chart_key}`);
  }

  source = item.platform_list.flatMap(option => option.platform_data).flat();

  if (currentContainer) {
    currentContainer.innerHTML = '';
  }
  const chart = await renderLineChart({
    container: chart_key,
    source,
    height: 189,
  });
  item.chart = chart;
};

const fetchCharts = async () => {
  loading.value = true;
  chartList.value.length = 0;

  //show_more===2：返回一个型号4条数据
  const params = { page: state.page, page_size: state.page_size, show_more: 2 };

  if (props.modelList.length === 3) {
    const [category_id, brand_id, model_id] = props.modelList;
    Object.assign(params, { category_id, brand_id, model_id });
  }

  const { data } = await queryChartList(params);
  const tempData = data.list;
  chartList.value = tempData.map(item => {
    item.range = [dayjs(item.start_date, dateFormat), dayjs(item.end_date, dateFormat)];
    return item;
  });
  chartList.value.forEach(item => {
    item.memory_list.forEach(option => {
      option.chart_key = `r_${Math.ceil(Math.random() * 10e5).toString(36)}`;
      generateChart(option);
    });
  });
  state.total = data.pageInfo.count;
  loading.value = false;
};

const changePage = page => {
  state.page = page;
  fetchCharts();
};

//图表按日期筛选-该型号下所有内存图表
const onRangeChange = (row: any, index: number, timeArr: dayjs.Dayjs[]) => {
  const [start_date, end_date] = timeArr.map(_ => _.format('YYYY-MM-DD'));
  const { category_id, brand_id, model_id } = row;
  const params = {
    category_id: Number(category_id),
    brand_id: Number(brand_id),
    model_id: Number(model_id),
    start_at: start_date,
    end_at: end_date,
  };
  chartList.value[index].start_date = start_date;
  chartList.value[index].end_date = end_date;
  //该型号内存-chart_key
  const chartKeyList = row.memory_list.map(item => ({ chart_key: item.chart_key, memory: item.memory }));
  queryChartList(params).then(({ data }) => {
    const tempData = data.list[0].memory_list;
    if (!tempData.length) {
      message.error('该区间暂无数据');
      return;
    }
    tempData.forEach(item => {
      item.chart_key = chartKeyList.find(op => op.memory === item.memory).chart_key;
      generateChart(item);
    });
  });
};

//更改某个图表
const handleRatioChange = (params: any, chart_key: string | undefined) => {
  queryChartDetail(params).then(({ data }) => {
    generateChart(data, chart_key);
  });
};

const handleToDegree = (item: any, chart: any) => {
  const { category_id, brand_id, model_id, title } = item;
  const { current_new_ratio, memory } = chart;
  const newBreadList = [
    {
      type: 'brand_id',
      label: title,
      value: [Number(category_id), Number(brand_id), Number(model_id)],
    },
    {
      type: 'memory',
      label: memory,
      value: memory,
    },
    {
      type: 'degree',
      label: current_new_ratio,
      value: current_new_ratio,
    },
  ];
  emits('update:breadCrumbList', newBreadList);
};

const handleRoute = (index: number, item: any, memory?: string) => {
  const { title, category_id, brand_id, model_id } = item;
  const newBreadList: IBreadCrumb[] = [
    {
      type: 'brand_id',
      label: title,
      value: [Number(category_id), Number(brand_id), Number(model_id)],
    },
  ];

  if (index !== 1) {
    newBreadList.push({
      type: 'memory',
      label: memory as string,
      value: memory as string,
    });
  }
  emits('update:breadCrumbList', newBreadList);
};

const pageInit = () => {
  state.page = 1;
};

onMounted(() => {
  fetchCharts();
});

defineExpose({
  fetchCharts,
  pageInit,
});
</script>
<style scoped lang="less">
.table-box {
  background: #f0f2f5;
  .chart-item {
    box-sizing: border-box;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;

    :deep(.ant-col-12):not(:nth-last-child(-n + 2)) {
      margin-bottom: 16px;
    }

    .item-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      .title {
        position: relative;
        padding-left: 12px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 4px;
          height: 16px;
          background: #00c8be;
          border-radius: 2px;
          transform: translateY(-50%);
          content: '';
        }
      }
      .menu-icon {
        padding-left: 12px;
        &:hover {
          color: #00c8be;
        }
      }
    }

    .chart-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .chart-top-left {
      display: flex;
      gap: 12px;
      align-items: center;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
    .chart-top-right {
      display: flex;
      gap: 4px;
      align-items: center;
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      .right-icon {
        width: 12px;
        height: 12px;
      }
      &:hover {
        color: #00c8be;
        cursor: pointer;
      }
    }

    .model-more {
      display: flex;
      justify-content: center;
    }
  }
  .ant-pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding: 24px 0;
    .ant-pagination-total-text {
      align-content: flex-start;
    }
  }
}

.chart-container {
  box-sizing: border-box;
  margin-top: 12px;
  padding: 12px;
  border: 1px solid rgba(6, 21, 51, 0.06);
  border-radius: 4px;
  &:hover {
    border: 1px solid #00c8be;
    box-shadow: 0 0 12px 0 rgba(0, 200, 190, 0.2);
    cursor: pointer;
  }
  .chart-container-item {
    width: 100%;
  }
}
</style>
