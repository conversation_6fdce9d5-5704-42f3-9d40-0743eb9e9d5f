<template>
  <div
    class="page-foot"
    data-html2canvas-ignore
  >
    <!-- 左侧 -->
    <a-checkbox
      v-if="pagination.type === 'control'"
      :checked="showPageCount"
      class="left-total"
      @change="handleChangeShowPageCount"
    >
      <a-tooltip>
        <template #title>
          可能会导致页面卡顿, 请合理勾选
        </template>
        显示总数和页码
      </a-tooltip>
    </a-checkbox>

    <!-- 右侧分页工具栏 -->
    <div class="right-pagination">
      <!-- 显示完整的页码操作功能 -->
      <a-space v-if="showPagination">
        <span style="color: rgba(6, 21, 51, 0.65)">
          共 <span>{{ pagination.total || 0 }}</span> 条记录
        </span>
        <a-pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :page-size-options="pageSizeOptions"
          show-quick-jumper
          :total="pagination.total || 0"
          @change="handleChangePage"
        />
      </a-space>
      <!-- 仅显示上下页操作按钮 -->
      <a-space v-else>
        <span
          v-if="pagination.type !== 'control' && pagination.total"
          style="color: rgba(6, 21, 51, 0.65)"
        >
          共 <span>{{ pagination.total }}</span> 条记录
        </span>
        <a-button
          :disabled="pagination.current === 1"
          :icon="h(LeftOutlined)"
          @click="handleChangePage(pagination.current - 1, pagination.pageSize)"
        />
        <a-button :icon="h('span', pagination.current)" />
        <a-button
          :disabled="disabledNext"
          :icon="h(RightOutlined)"
          @click="handleChangePage(pagination.current + 1, pagination.pageSize)"
        />
      </a-space>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, h, PropType } from 'vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';

import type { IPagination } from '../data';

const props = defineProps({
  /** 分页配置 */
  pagination: {
    type: Object as PropType<IPagination>,
    default: {
      current: 1,
      pageSize: 10,
    },
  },
  showPageCount: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'changePage', current: number, pageSize: number): void;
  (e: 'changeShowPageCount', showPageCount: boolean): void;
}>();

function handleChangePage(current: number, pageSize: number) {
  emit('changePage', current, pageSize);
}

function handleChangeShowPageCount() {
  emit('changeShowPageCount', !props.showPageCount);
}

const showPagination = computed(() => {
  if (props.pagination.type === 'control') {
    return props.showPageCount;
  }
  return props.pagination.type !== 'simple';
});

// 是否禁用下一页，需要兼容两种情况，一种是会给查询到的总数，一种是只给当前页面查询的数量
const disabledNext = computed(() => {
  if (props.pagination.total > props.pagination.pageSize) {
    return Number(props.pagination.total) < props.pagination.pageSize * props.pagination.current;
  }
  return props.pagination.total < props.pagination.pageSize;
});

/** 分页大小下拉选择项 */
const pageSizeOptions = [...new Set([10, 20, props.pagination.pageSize].sort((a, b) => a - b).map(String))];

/** 分页下拉框位置 */
const dropdown = pageSizeOptions.length * -34 + 'px';
</script>

<style lang="less" scoped>
.page-foot {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 24px;
  background-color: white;

  .left-total {
    margin-right: auto;
  }

  .right-pagination {
    margin-left: auto;

    :deep(.ant-select-dropdown) {
      top: v-bind(dropdown) !important;
    }
  }
}
</style>
