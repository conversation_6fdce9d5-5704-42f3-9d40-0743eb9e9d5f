<template>
  <a-drawer
    v-model:visible="isShow"
    class="add-account-modal"
    :footer-style="{ textAlign: 'right' }"
    :title="data.id ? '编辑账号' : '添加账号'"
    :width="640"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="Form"
        label-align="left"
        layout="vertical"
        :model="modelData"
        :rules="rules"
      >
        <a-form-item
          label="姓名"
          name="name"
        >
          <a-input
            v-model:value.trim="modelData.name"
            placeholder="请输入姓名"
          />
        </a-form-item>
        <a-form-item
          label="登录手机号"
          name="phone"
        >
          <a-input
            v-model:value.trim="modelData.phone"
            :disabled="!!data.id"
            :maxlength="11"
            placeholder="请输入"
            type="number"
          />
        </a-form-item>
        <a-form-item v-if="!isRegistered">
          <div class="unregistered-tips">
            <exclamation-circle-outlined />
            <span style="margin-left: 8px">该账号未注册，请输入密码进行注册绑定。</span>
          </div>
        </a-form-item>
        <a-form-item
          v-show="!isRegistered"
          label="输入密码"
          name="password"
        >
          <a-input-password
            v-model:value="modelData.password"
            placeholder="字母+数字+特殊字符任选两种及以上组合，长度不低于6位"
          />
        </a-form-item>
        <a-form-item
          v-show="isShowPasswordAgain"
          label="再次输入密码"
          name="passwordAgain"
        >
          <a-input-password
            v-model:value="modelData.passwordAgain"
            placeholder="请再次输入密码"
          />
        </a-form-item>
        <a-form-item
          label="数据权限"
          name="pms"
        >
          <a-checkbox-group v-model:value="permission">
            <a-checkbox
              :disabled="true"
              value="QUERY"
            >
              查询数据
            </a-checkbox>
            <a-checkbox value="EXPORT">
              导出数据明细
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-spin :spinning="configLoading">
          <a-form-item
            label="功能权限"
            name="auth"
          >
            <a-form-item-rest>
              <function-item
                v-for="item in configurationList"
                :key="item.module_id"
                :item="item"
                @set-check-list="setCheckList"
              />
            </a-form-item-rest>
          </a-form-item>
        </a-spin>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button
        style="margin-right: 8px"
        @click="cancel"
      >
        取消
      </a-button>
      <a-button
        type="primary"
        @click="submit"
      >
        {{ (!isRegistered && isVerify) || isRegistered ? '确定' : '下一步' }}
      </a-button>
    </template>
  </a-drawer>

  <pwd-modal
    v-model:is-verify="isVerify"
    v-model:visible="pwdVisible"
    :phone="modelData.phone"
    @account-submit="submit"
  />
</template>

<script setup lang="ts">
import { checkPhoneExistence, addAccount } from './service';

import { ref, PropType, computed, watch } from 'vue';
import { Account, AuthorityCenterRow, ConfigurationItem } from './data';
import { deepClone } from '@/utils/base';

import { message } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import FunctionItem from './components/function-item/index.vue';
import PwdModal from './components/pwd-modal/index.vue';

type PermissionType = 'QUERY' | 'EXPORT';
interface ModelDataType extends Account {
  passwordAgain?: string;
}

const props = defineProps({
  value: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object as PropType<AuthorityCenterRow>,
    required: true,
  },
  configurationList: {
    type: Array as PropType<ConfigurationItem[]>,
    default() {
      return [];
    },
  },
  configLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:value', 'resetList', 'update:data', 'update:configurationList']);

const rules: Record<string, any> = {
  name: [
    {
      trigger: 'blur',
      required: true,
      message: '姓名不能为空',
    },
  ],
  phone: [
    {
      trigger: 'blur',
      required: true,
      validator: (rule: any, value: string, callback: any) => {
        if (!/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error('手机号格式不正确'));
        }
        callback();
      },
    },
  ],
  password: [
    {
      trigger: 'blur',
      required: true,
      validator: (rule: any, value: string, callback: any) => {
        if (
          !props.data.id &&
          !isRegistered.value &&
          !/^(?![\d]+$)(?![a-zA-Z]+$)(?![~!@#$%^&*\-+=\[\]_,.{}|\\/"'<>《》]+$)[\da-zA-Z~!@#$%^&*\-+=\[\]_,.{}|\\/"'<>《》]{6,24}$/.test(
            value,
          )
        )
          callback(new Error('密码该由字母+数字+特殊字符任选两种及以上组合，长度不低于6位'));
        callback();
      },
    },
  ],
  passwordAgain: [
    {
      trigger: 'blur',
      required: true,
      validator: (rule: any, value: string, callback: any) => {
        if (!props.data.id && !isRegistered.value && value !== modelData.value.password)
          callback(new Error('两次输入密码不一致'));
        callback();
      },
    },
  ],
};
const loading = ref(false);
const pwdVisible = ref<boolean>(false);
const isVerify = ref<boolean>(false);
const isShow = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit('update:value', value);
  },
});

const modelDataDefault: ModelDataType = {
  name: '',
  phone: '',
  password: '',
  passwordAgain: '',
  auth_search: 1,
  auth_export: 0,
  auth: {},
};
const modelData = ref<ModelDataType>(deepClone(modelDataDefault));
const permission = computed<PermissionType[]>({
  get() {
    const list: PermissionType[] = [];
    modelData.value.auth_search && list.push('QUERY');
    modelData.value.auth_export && list.push('EXPORT');
    return list;
  },
  set(arr) {
    modelData.value.auth_search = arr.includes('QUERY') ? 1 : 0;
    modelData.value.auth_export = arr.includes('EXPORT') ? 1 : 0;
  },
});
const isRegistered = ref(true); // 是否已注册
const isShowPasswordAgain = computed(() => {
  return (
    !isRegistered.value &&
    /^(?![\d]+$)(?![a-zA-Z]+$)(?![~!@#$%^&*\-+=\[\]_,.{}|\\/"'<>《》]+$)[\da-zA-Z~!@#$%^&*\-+=\[\]_,.{}|\\/"'<>《》]{6,24}$/.test(
      modelData.value.password,
    )
  );
});
const Form = ref<any>(null);

watch(
  () => props.value,
  value => {
    if (value) {
      modelData.value = deepClone(props.data);
      Form.value && Form.value.clearValidate();
    }
  },
);

watch(
  () => modelData.value.phone,
  (value: string) => {
    if (/^1[3-9]\d{9}$/.test(value) && props.data.phone !== value) {
      loading.value = true;
      checkPhoneExistence({ phone: value })
        .then(res => {
          isRegistered.value = res.data;
          isVerify.value = res.data;
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      isVerify.value = true;
      isRegistered.value = true;
    }
  },
);

// 提交
async function submit() {
  if (loading.value) return;
  await Form.value.validate();
  if (isRegistered.value || (!isRegistered.value && isVerify.value)) {
    const list = props.configurationList;
    const modules = list.map(item1 => {
      const funcItem = item1.function.map(item2 => ({
        function_name: item2.func_name,
        is_conf: item2.is_conf,
        config_at: item2.config_at,
      }));
      return {
        module_name: item1.module_name,
        config_at: item1.config_at,
        is_conf: item1.is_conf,
        function: funcItem,
      };
    });
    modelData.value.auth = { modules };
    addAccount(modelData.value).then(() => {
      message.success(props.data.id ? '保存成功' : '账号添加成功');
      emit('resetList');
      cancel();
      isShow.value = false;
    });
  } else {
    pwdVisible.value = true;
  }
}

// 关闭弹框
function cancel() {
  modelData.value = deepClone(modelDataDefault);
  isVerify.value = false;
  isRegistered.value = true;
  isShow.value = false;
  emit('update:data', modelData.value);
}

// 获取当前时间戳
function getTimeStamp() {
  return Math.floor(new Date().getTime() / 1000);
}

/**
 * @description: 控制选择框
 * @param {*} value checkList的值
 * @param {*} isAll 是否为全选框更改
 * @param {*} module_id 模块id
 * @param {*} checked 多选框的状态
 * @param {*} onlyAll 只改变一级选项的状态
 * @return {*}
 */
function setCheckList(value: number[], isAll = false, module_id: number, checked = false, onlyAll = false) {
  const list = props.configurationList.map(i => {
    // 同一个模块下
    if (i.module_id === module_id) {
      isAll && (i.is_conf = checked) && checked && (i.config_at = getTimeStamp());

      !onlyAll &&
        i.function.forEach(item => {
          value.includes(item.func_id) && (item.is_conf = checked) && (item.config_at = getTimeStamp());
        });
    }
    return i;
  });

  emit('update:configurationList', list);
}
</script>

<style lang="less">
.add-account-modal {
  .ant-drawer-close {
    position: absolute;
    right: 0;
    margin-right: 10px;
  }

  .ant-modal-title {
    font-weight: bold;
  }

  .ant-form-item-control-input {
    min-height: auto;
  }
  .unregistered-tips {
    color: #faad14;
    font-size: 14px;
  }
}
</style>
