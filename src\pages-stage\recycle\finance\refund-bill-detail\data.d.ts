export interface IBaseData {
  coin_type: number;
  refund_order_id?: string | undefined;
  server_id?: string | undefined;
  date: string;
  price: string;
  remark: string;
}

interface IOptionList {
  value: number;
  name: string;
}

export interface IRefundOptionList {
  coin_type_list: IOptionList[];
  pay_account_list: IOptionList[];
  pay_type_list: IOptionList[];
}

export interface IRefundData {
  pay_account?: number | string;
  pay_type?: number | string;
  amount?: string;
  payment_flow_sn?: string;
  remark?: string;
  id?: number;
}

export interface IServerOptionParams {
  value: number;
  label: string;
}
