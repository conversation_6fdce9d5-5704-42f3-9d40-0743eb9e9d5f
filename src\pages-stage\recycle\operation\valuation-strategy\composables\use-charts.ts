import { ref } from 'vue';
import { loadG2 } from '@/utils/static-load';
import { getSkuChartDetail } from '../service';

type TParams = {
  element: string;
};

type TRequestParams = {
  goods_id: string;
  setting_type: string;
  sku: string;
  start_at: string;
  end_at: string;
};

export default function (params: TParams) {
  const { element } = params;

  const loading = ref<boolean>(false);
  const isEmpty = ref<boolean>(false);

  let chart: any = null;
  let chartInfo: any[] = [];

  async function createCharts(requestParams: TRequestParams) {
    chart?.destroy();
    loading.value = true;
    const res = await getSkuChartDetail(requestParams);
    const data = res.data || [];
    chartInfo = data;
    const container = document.getElementById(element) as HTMLDivElement;
    container.innerHTML = '';
    await loadG2();
    if (window.G2) {
      chart = new G2.Chart({
        container: element,
        autoFit: true,
        height: 210,
      });
      render();
    }
    loading.value = false;
  }

  function render() {
    chart.data(chartInfo || []);

    chart.tooltip({
      showCrosshairs: true,
      shared: true,
    });

    chart.scale({
      price: {
        nice: true,
      },
    });

    chart.legend({
      position: 'top-left', // 将图例位置设置为上方
      flipPage: false,
    });

    chart.line().position('date*price').color('condition').shape('line');

    chart
      .point()
      .position('date*price')
      .color('condition')
      .shape('hollowCircle') // 使用 'hollowCircle' 形状
      .style((item: any) => {
        return {
          fill: item.condition === 'hovered' ? '#000' : '#fff', // 鼠标移入时填充黑色，其他时候填充白色
          lineWidth: 1, // 边框宽度
        };
      });

    chart.render();
  }

  return {
    createCharts,
    loading,
    isEmpty,
  };
}
