<template>
  <div>
    <!--  周运营信息  -->
    <WeekBusiness
      v-if="kind !== 'day' && calenderInfo?.show_panel === true"
      :calendar="calenderInfo"
      style="margin-bottom: 10px"
      @refresh="resetCalenderInfo"
    />
    <!--  日运营信息  -->
    <DayBusiness v-if="kind !== 'week' && calenderInfo?.show_panel === false" />
  </div>
</template>
<script setup lang="ts">
import { onMounted, PropType } from 'vue';

import DayBusiness from './components/day-business.vue';
import WeekBusiness from './components/week-business.vue';
import { useCalenderInfo } from './composables/use-calender-info';

defineProps({
  kind: {
    type: String as PropType<'week' | 'day'>,
    default: undefined,
  },
});

const { calenderInfo, getCalenderInfo, resetCalenderInfo } = useCalenderInfo();

// 初始化
onMounted(() => {
  getCalenderInfo();
});
</script>
