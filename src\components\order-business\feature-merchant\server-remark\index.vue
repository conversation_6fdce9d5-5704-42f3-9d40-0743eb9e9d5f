<template>
  <!-- 订单备注 -->
  <div class="remark-data">
    订单备注：{{ remark }}
    <span
      class="edit-btn"
      @click="changeRemark"
    >
      <FormOutlined />
    </span>
  </div>
</template>
<script setup lang="ts">
import { FormOutlined } from '@ant-design/icons-vue';

import { basicModal } from '@/components/order-business/basic-modals';
import { useUnityModal } from '@/components/unity-modal';

const props = defineProps({
  orderId: {
    type: String,
    default: '',
  },
  remark: {
    type: String,
    default: '',
  },
});

const { onToggleComponent } = useUnityModal();

function changeRemark() {
  onToggleComponent(basicModal.serverRemark, {
    orderId: props.orderId,
    remark: props.remark,
  });
}
</script>

<style scoped lang="less">
.remark-data {
  color: rgba(6, 21, 51);
  font-size: 14px;
  text-align: center;
}

.edit-btn {
  margin-left: 4px;
  color: #3777ff;
  cursor: pointer;

  &:hover {
    color: #6198ff;
  }
}
</style>
