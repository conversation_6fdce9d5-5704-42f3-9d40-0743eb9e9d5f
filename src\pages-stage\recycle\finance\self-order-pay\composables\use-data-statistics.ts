import { ref } from 'vue';
import { IDataStatistics } from '../data.d';

type TParams = {
  request: Promise<any>;
  title: string;
  tips: string[];
};

export default function (params: TParams) {
  const { request, title, tips } = params;
  const loading = ref<boolean>(false);
  const info = ref<IDataStatistics>({
    title: '',
    tips: [],
    wait_pay_num: 0,
    finish_pay_num: 0,
  });

  info.value.title = title;
  info.value.tips = tips;

  const init = (reqParams: any) => {
    loading.value = true;
    request(reqParams)
      .then(res => {
        const data = res.data || {};
        info.value.wait_pay_num = data.wait_pay_num;
        info.value.finish_pay_num = data.finish_pay_num;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  return {
    loading,
    info,
    init,
  };
}
