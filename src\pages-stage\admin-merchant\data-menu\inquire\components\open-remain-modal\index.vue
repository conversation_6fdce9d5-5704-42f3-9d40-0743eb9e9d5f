<template>
  <RThemeModal
    v-model:open="bindVisible"
    class="open-remain-modal"
    logo="https://img1.rrzuji.cn/uploads/scheme/2501/24/m/i62Ji3t8CX5JVP8LB7JD.png"
    ok-text="我知道了"
    :show-cancel-button="false"
    title="开通提醒"
    :type="ERThemeModalType.Primary"
    :width="600"
    @ok="submit"
  >
    <div class="modal-content">
      <div class="modal-content-top-wrap">
        <div class="title">
          开通租赁数智体验更完善的数据服务
        </div>
        <div class="normal-text">
          1. 若您无租赁数智账号：
          <template v-if="serverType === 'server'">
            <span class="grey-text">当前为商家后台主账号</span>，您可在<span class="grey-text">
              商家后台->数据->租赁数智->配置中心 </span><span>为您的店铺开通更多数据维度和账号权限，点此 </span><a
              class="link"
              data-sensors="$OpenRemindToConfig"
              @click="openParentPage(domain + '/shuzhi/auth')"
            >跳转至配置中心</a>
          </template>
          <template v-else>
            <span class="grey-text">当前为商家后台子账号</span>，请联系商家主账号通过<span class="grey-text">
              商家后台->数据->租赁数智->配置中心 </span><span>为您开通账号权限，配置成功后重新进入 </span>
          </template>
        </div>
        <div class="normal-text">
          2. 若您已拥有租赁数智账号：可通过账号密码登录，点此
          <a
            class="link"
            data-sensors="$OpenRemindToShuzhi"
            :href="'https://' + shuzhiLink"
            target="_blank"
          >跳转至租赁数智登录页</a>
        </div>
      </div>

      <div class="modal-content-tips-wrap">
        <div class="flex-wrap flex-y-center">
          <img
            alt=""
            class="tips-icon"
            src="https://img1.rrzuji.cn/uploads/scheme/2501/26/m/FbeX6fzbRe5LQqsstLh9.png"
            :width="40"
          >
          <div class="tips-icon-title">
            人人租
          </div>
        </div>
        <div class="tips">
          <div class="tips-text">
            租赁数智从数据查询、数据分析、数据预测等模块为您提供更多维度的数据体验，同时提供订单明细导出服务，我们致力于为商家打造精准实时的数据获取途径，欢迎所有商家参与内测使用。
          </div>
        </div>
      </div>

      <!-- 暂时屏蔽技术支持二维码 -->
      <div
        v-if="false"
        class="support"
      >
        <MessageOutlined class="support-icon" />
        <span
          @mouseenter="showCode = true"
          @mouseleave="showCode = false"
        >联系专业技术支持</span>

        <div
          v-show="showCode"
          class="code"
        >
          <div class="code-left">
            <div style="color: rgba(6, 21, 51, 0.85); font-weight: bold">
              添加技术支持
            </div>
            <div style="color: rgba(6, 21, 51, 0.45); font-size: 12px; text-align: left">
              我们为你答疑
            </div>
            <div class="green-text">
              <scan-outlined style="margin-right: 4px; font-size: 16px" /><span style="font-size: 12px">微信扫码</span>
            </div>
          </div>

          <img
            alt=""
            src="https://img1.rrzuji.cn/uploads/scheme/2303/15/m/XlxZmJUoQbdbaSHYcYNM.jpg"
            :width="80"
          >
        </div>
      </div>
    </div>
  </RThemeModal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { MessageOutlined, ScanOutlined } from '@ant-design/icons-vue';
import { ERThemeModalType } from 'rrz-web-design';

const route = useRoute();
const domain = route.query.origin || window.location.origin;
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  serverType: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

const showCode = ref<boolean>(false);

const shuzhiLink = ((referrer: string) => {
  if (import.meta.env.DEV || /dev\d*\-/.test(referrer)) {
    return 'dev1-shuzhi.rrzuji.com';
  } else if (/test\d*\-/.test(referrer)) {
    return 'test1-shuzhi.rrzuji.com';
  } else {
    return 'shuzhi.rrzu.com';
  }
})(document.referrer);

const openParentPage = (url: string) => {
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};

function submit() {
  bindVisible.value = false;
}
</script>

<style scoped lang="less">
.open-remain-modal {
  .modal-content {
    .modal-content-top-wrap {
      margin-bottom: 16px;
      padding: 16px;
      background: #f4f8ff;
      border-radius: 8px 8px 8px 8px;
      .title {
        margin-bottom: 8px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: bold;
        font-size: 14px;
        line-height: 22px;
      }
      .normal-text {
        color: rgba(6, 21, 51, 0.45);
        text-align: left;
        &:last-child {
          margin-top: 8px;
        }
      }
      .grey-text {
        color: rgba(6, 21, 51, 0.65);
      }
      .link {
        text-decoration: underline;
      }
    }
    .modal-content-tips-wrap {
      padding: 16px;
      background: rgba(67, 111, 199, 0.02);
      border: 1px solid rgba(6, 21, 51, 0.06);
      border-radius: 8px 8px 8px 8px;
      .tips-icon {
        width: 21px;
        height: 18px;
      }
      .tips-icon-title {
        margin-left: 4px;
        font-weight: bold;
      }
      .tips {
        margin-top: 10px;
        &-text {
          color: rgba(6, 21, 51, 0.45);
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
  .support {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 24px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-decoration: underline;
    cursor: pointer;
    &-icon {
      margin-right: 7px;
      color: #3777ff;
    }
    .code {
      position: absolute;
      right: -109px;
      bottom: -120px;
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 9px 28px 8px rgba(6, 21, 51, 0.05), 0 6px 16px 0 rgba(6, 21, 51, 0.08),
        0 3px 6px -4px rgba(6, 21, 51, 0.12);
      transform: translate(-40%, 0);

      &-left {
        margin-right: 16px;
      }

      .green-text {
        display: flex;
        align-items: center;
        margin-top: 18px;
        color: #52c41a;
      }
    }
  }
}
</style>
