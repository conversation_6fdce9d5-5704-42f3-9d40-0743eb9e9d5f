<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px 24px' }"
    title="配送方式设置"
    top-fixed
  >
    <a-alert
      closable
      show-icon
      type="info"
    >
      <template #description>
        <div class="alert-desc">
          若未设置配送方式，产品将默认显示“顺丰/德邦到付，部分城市可自提”。
        </div>
        <div class="alert-desc">
          为避免误导用户，请及时修改为合适的配送方式和说明。
        </div>
      </template>
      <template #message>
        <div class="alert-msg">
          配送方式设置须知
        </div>
      </template>
    </a-alert>
    <div class="form-wrapper">
      <a-form
        layout="vertical"
        :model="formState"
      >
        <a-form-item
          label="配送方式"
          name="delivery"
        >
          <a-input
            v-model:value="formState.delivery"
            :maxlength="50"
            placeholder="请输入"
            show-count
          />
        </a-form-item>
        <a-form-item
          label="详细说明"
          name="delivery_more"
        >
          <a-textarea
            v-model:value="formState.delivery_more"
            :auto-size="{ minRows: 4, maxRows: 8 }"
            :maxlength="255"
            placeholder="请输入"
            show-count
          />
        </a-form-item>
        <a-form-item>
          <a-button
            type="primary"
            @click="handleUpdateAddress"
          >
            保存设置
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { fetchAddressInfo, updateAddressInfo } from './service';

const formState = ref<{
  delivery: string;
  delivery_more: string;
}>({
  delivery: '',
  delivery_more: '',
});

const fetchAddress = async () => {
  const {
    data: { delivery, delivery_more },
  } = await fetchAddressInfo();
  const obj = {
    delivery: delivery ? delivery : '顺丰/德邦到付，部分城市可自提',
    delivery_more,
  };
  formState.value = Object.assign(formState.value, obj);
};

fetchAddress();

const handleUpdateAddress = async () => {
  const res = await updateAddressInfo(formState.value);
  message.success(res.message || res.error);
  fetchAddress();
};
</script>
<style lang="less" scoped>
.alert {
  margin-bottom: 24px;
  .alert-desc {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
.form-wrapper {
  width: 600px;
  margin-top: 24px;
}
.alert-desc {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
.alert-msg {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
}
</style>
