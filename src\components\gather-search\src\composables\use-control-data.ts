import { Ref, ref, watch } from 'vue';
import { cloneDeep } from 'lodash-es';

import { useSearchHistory } from '@/composables/use-search-history';

/**
 * 创建一个数据控制器
 * @param params.data 绑定的数据
 * @param params.onChange 数据变化回调
 * @param params.historyKey 需要开启历史记录控制所需的key
 * @returns { bindData, resetData }
 * */
export function useControlData<T>(params: { data: Ref<T>; onChange?: (value: T) => void; historyKey?: string }) {
  const { data, onChange, historyKey } = params;

  /** 绑定的数据 */
  const bindData = ref<T>(data.value);
  // 开启搜索历史记录，控制数据变化
  if (historyKey) {
    const { initHistoryData } = useSearchHistory();
    initHistoryData<T>({
      key: historyKey,
      pushFn: () => bindData.value,
      changeFn: data => (bindData.value = data),
    });
  }

  const originData = cloneDeep(data.value); // 保存原始数据

  // 双向绑定
  watch(bindData, val => onChange?.(val), { deep: true });
  watch(data, val => (bindData.value = val), { deep: true });

  /** 刷新重置表单数据 */
  function resetData() {
    bindData.value = cloneDeep(originData);
  }

  return { bindData, resetData };
}
