<template>
  <a-drawer
    v-model:visible="drawerVisible"
    class="notarization-modal"
    :footer-style="{ textAlign: 'right' }"
    title="发起公证"
    :width="1100"
  >
    <div class="notarization-info">
      <table class="notarization-info-table">
        <tr colspan="6">
          <th colspan="6">
            订单信息
          </th>
        </tr>
        <tr>
          <th>订单编号</th>
          <td>{{ notarizationInfo.order_id }}</td>
          <th>收货人名称</th>
          <td>{{ notarizationInfo.name }}</td>
          <th>收货联系电话</th>
          <td>{{ notarizationInfo.phone }}</td>
        </tr>
        <tr>
          <th>收货电子邮箱</th>
          <td>{{ notarizationInfo.user_email }}</td>
          <th>收货送达地址</th>
          <td colspan="3">
            {{ notarizationInfo.address }}
          </td>
        </tr>
        <tr>
          <th>设备名称</th>
          <td>{{ notarizationInfo.item_name }}</td>
          <th>套餐名称</th>
          <td>{{ notarizationInfo.sku_name }}</td>
          <th>设备数量</th>
          <td>{{ notarizationInfo.item_num }}</td>
        </tr>
        <tr>
          <th>租期</th>
          <td>{{ notarizationInfo.lease_time }}天</td>
          <th>租金单价</th>
          <td>{{ notarizationInfo.fenqi_money }}</td>
          <th>总租金</th>
          <td>{{ notarizationInfo.rental_money }}</td>
        </tr>
        <tr>
          <th>商品押金</th>
          <td>{{ notarizationInfo.deposit_money }}</td>
          <th>已付押金（已冻结）</th>
          <td>{{ notarizationInfo.deposit_has_pay }}</td>
          <th>购买价</th>
          <td>{{ notarizationInfo.buyout }}</td>
        </tr>
        <tr style="text-align: center">
          <th colspan="6">
            个人信息
          </th>
        </tr>
        <tr>
          <th>申请人角色</th>
          <td>个人</td>
          <th>申请人身份</th>
          <td>{{ notarizationInfo.role }}</td>
          <th>申请人名称</th>
          <td>{{ notarizationInfo.name }}</td>
        </tr>
        <tr>
          <th>性别</th>
          <td>{{ notarizationInfo.sex === 0 ? '女' : '男' }}</td>
          <th>出生日期</th>
          <td>{{ notarizationInfo.birth }}</td>
          <th>身份证类型</th>
          <td>{{ notarizationInfo.identity_type }}</td>
        </tr>
        <tr>
          <th>身份证号码</th>
          <td>{{ notarizationInfo.id_card }}</td>
          <th>联系手机</th>
          <td>{{ notarizationInfo.user_phone }}</td>
          <th>电子邮箱</th>
          <td>{{ notarizationInfo.user_email }}</td>
        </tr>
        <tr>
          <th>人脸识别认证时间</th>
          <td>{{ notarizationInfo.face_verify_time }}</td>
          <th>联系地址</th>
          <td colspan="3">
            {{ notarizationInfo.user_address }}
          </td>
        </tr>
        <tr>
          <th>法定代表人证明</th>
          <td>
            <template v-if="notarizationInfo.is_return">
              <RUpload
                v-model:value="fileList"
                :upload-api="uploadToOssCompatibility"
                :upload-props="{
                  maxCount: 1,
                  listType: 'picture',
                }"
              >
                <a-button v-if="!fileList.length">
                  <UploadOutlined /> 请上传
                </a-button>
              </RUpload>
              点击
              <span
                class="download-template"
                @click="downloadTemplate"
              >法定代表人身份证明书</span>下载模板，加盖公章后上传<br>
              <span style="color: red">法定代表人证明被退回，请重新上传。<template v-if="notarizationInfo.return_mark">退回原因：{{ notarizationInfo.return_mark }}</template></span>
            </template>
            <template v-else>
              <a-image
                fit="contain"
                :src="notarizationInfo.certificate"
                style="width: 256px; height: 194px; border-radius: 6px"
              />
            </template>
          </td>
        </tr>
      </table>
    </div>

    <template #footer>
      <span style="margin-right: 8px; color: rgb(250, 173, 20)">为避免不必要的损失，请确认一下信息是否准确</span>
      <a-button
        :loading="loading"
        style="margin-right: 8px"
        @click="drawerVisible = false"
      >
        取消
      </a-button>
      <a-button
        :loading="loading"
        type="primary"
        @click="submit"
      >
        确定
      </a-button>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';
import { uploadToOssCompatibility } from '@/utils/oss-helper';

import type { NotarizationInfoType } from './data';
import {
  createNotarizationPingShan,
  createNotarizationRecordLong,
  createNotarizationRecordShort,
  notarizationRecord,
  pingShanRecord,
  uploadNotarization,
} from './services';

interface IExtraData {
  /** 短租的公证id */
  aea_id?: number;
  scene_id?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const bindVisible = useVModel(props, 'visible', emit);

const drawerVisible = ref(false);
const notarizationInfo = ref<NotarizationInfoType>({});
const fileList = ref<string[]>([]);
const loading = ref(false);

const route = useRoute();
const origin = route.query.origin;

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      bindVisible.value = false;
      loadData();
    }
  },
  { immediate: true },
);

function downloadTemplate() {
  window.open(`${origin}/cai-hua-notarization-application/download-certificate-template`);
}

/**
 * 显示发起公证信息
 */
async function loadData() {
  loading.value = true;

  try {
    const api = Number(props.extraData?.scene_id) == 2 ? pingShanRecord : notarizationRecord;
    const res = await api({
      order_id: props.orderId,
      id: props.extraData.aea_id,
      type: props.extraData.aea_id ? 2 : 1,
      scene_id: props.extraData?.scene_id,
    });
    notarizationInfo.value = res.data;
    drawerVisible.value = true;
  } catch (e) {
    switch (e.status) {
      case 1201:
        // 未授权短租公证，弹窗提示
        return Modal.confirm({
          title: '提示',
          content: '平台公证进行了全面升级，请前往公证开通入口重新进行开通后使用',
          okText: '前往开通',
          onOk() {
            window.open(`${origin}/application-center/page?id=6`);
          },
        });
      default:
        throw e;
    }
  } finally {
    loading.value = false;
  }
}

/**
 * @description: 发起公证提交
 * @return {*}
 */
async function submit() {
  if (fileList.value.length === 0 && notarizationInfo.value.is_return) return message.error('请上传法定代表人证明');

  loading.value = true;
  try {
    fileList.value[0] && (await uploadNotarization({ path: fileList.value[0] }));
    const { aea_id, scene_id } = props.extraData;
    const distLongApi = Number(scene_id) === 2 ? createNotarizationPingShan : createNotarizationRecordLong;
    const res = await (aea_id
      ? createNotarizationRecordShort({ id: aea_id })
      : distLongApi({ order_id: props.orderId }));
    drawerVisible.value = false;
    message.success(res.message || '提交成功');
  } catch (error) {
    throw error;
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="less" scoped>
.notarization-info {
  margin-top: 24px;
  margin-bottom: 81px;
}

.notarization-info-table {
  width: 100%;
  border-color: #f0f0f0;
}

.notarization-info-table th,
td {
  padding: 16px;
  vertical-align: top;
  border: 1px solid #f0f0f0;
  border-collapse: collapse;
}

.notarization-info-table th {
  width: 160px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  background-color: #fafafa;
}

.notarization-info-table td {
  color: rgba(6, 21, 51, 0.65);
}

.download-template {
  color: #009dd9;
  text-decoration: underline;
  cursor: pointer;
}
</style>
