import { message } from 'ant-design-vue';

import type { IConfig } from '@/components/aliyun-captcha/data';

interface FormModal {
  value: {
    [key: string]: any;
  };
}

/** 生成一个阿里云验证码配置 */
export function initAliyunCaptchaConfig(formModal: FormModal, placeholder: string, type?: string): IConfig {
  return {
    url: '/user-info/ali-verify',
    params: formModal,
    formatHandle: (params: any) => {
      if (type === 'old') {
        return {
          type: 'old',
        };
      }
      return {
        phone: params.value[placeholder],
      };
    },
    beforeShow: () => {
      if (type === 'old') {
        return true;
      }
      if (!formModal.value[placeholder] || !/^1[3456789]\d{9}$/.test(formModal.value[placeholder])) {
        message.error('请输入正确的手机号');
        return false;
      }
      return true;
    },
    requestCallback: res => res.status === 0, // 请求结果，返回业务是否成功的结果,boolean类型
  };
}
