/** 因子数据 */
export interface IFactorData {
  relation?: 'rulesRelation';
  /** 且|或（默认为且） */
  type?: 'and' | 'or';
  /** 规则 */
  rules?: IFactorRule[];
}

/** 因子规则 */
export interface IFactorRule {
  type: 'profileRule';
  /** 因子code */
  factor_code: string;
  /** 对应方法：[eq|neq|lt|let|gt|gte|in|nin|btw] */
  function: string;
  /** 结果值 */
  value: any[];
}

export interface IFactorLibraryProps {
  /** 应用数据 */
  value?: IFactorData;
  /** 模块序号 */
  code?: string | number;
  /** 标题 */
  title?: string;
  /** 禁用 */
  disabled?: boolean;
  /** 最多条件数据 */
  max?: number;
  /** 只读 */
  readonly?: boolean;
  /** 条件选择宽度,单位px，默认180 */
  contentWidth?: string | number;
}
