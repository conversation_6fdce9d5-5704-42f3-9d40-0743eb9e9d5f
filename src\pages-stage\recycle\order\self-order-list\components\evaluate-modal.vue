<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :destroy-on-close="true"
    :footer="false"
    title="预估信息"
    :visible="visible"
    width="560px"
    @cancel="onCancel"
  >
    <a-spin :spinning="isLoading">
      <div class="card">
        <div class="title">
          普通选项:
        </div>
        <template
          v-for="(item, index) of normal"
          :key="index"
        >
          <div class="item">
            <div class="label">
              {{ item?.label }}:
            </div>
            <div class="value">
              {{ item.value?.[0] }}
            </div>
          </div>
        </template>
      </div>
      <div class="card">
        <div
          class="title"
          style="margin-top: 10px"
        >
          非必填可多选项:
        </div>
        <template
          v-for="(item, index) of multiEmpty"
          :key="index"
        >
          <div class="item">
            <div class="label">
              {{ item }}:
            </div>
            <div class="value">
              是
            </div>
          </div>
        </template>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getEvaluateInfo } from '../service';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible']);
const onCancel = () => emits('update:visible', false);

const isLoading = ref(false);

// 单选的题目以及答案
const normal = ref<any[]>([{}]);

// 多选的答案
const multiEmpty = ref<string[]>([]);

onMounted(() => {
  isLoading.value = true;
  getEvaluateInfo(props.orderId)
    .then(({ data }) => {
      normal.value = data.normal;
      multiEmpty.value = data.multiEmpty?.[0].value;
    })
    .finally(() => (isLoading.value = false));
});
</script>
<style lang="less" scoped>
.title {
  padding-bottom: 12px;
  color: rgba(0, 0, 0, 0.65);
  font-weight: 600;
}
.item {
  display: flex;
  padding-bottom: 12px;
  .label {
    padding-right: 18px;
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
