import { IOption } from '@/composables/commodity-category/data';
import { ref } from 'vue';

/** 远程加载分级 */
export function useLevelLoad(params: {
  asyncSearch?: boolean;
  /** 是否多选 */
  multiple?: boolean;
}) {
  // 没有设置远程搜索时，不需要分级加载
  if (!params.asyncSearch) {
    return {};
  }
  /** 分级加载的id组 */
  const levelIds = ref<number[] | number[][]>();

  /** 根据数据变化获取需要加载的层级数据 */
  function setLevelIdsByValue<V extends number[] | number[][]>(value?: V) {
    // levelIds只需要取选择的id的父级id，最后一级为非父级id不要传
    if (params.multiple) {
      levelIds.value = value?.map(item => (item as number[]).slice(0, -1));
    } else {
      levelIds.value = value?.slice(0, -1);
    }
  }

  /** 分级加载，主动点击显示下级数据 */
  function setLevelIdsByLoad(node: IOption[]) {
    if (!node[node.length - 1].children?.length) {
      levelIds.value = node.map(item => item.value);
    }
  }

  return {
    levelIds,
    setLevelIdsByLoad,
    setLevelIdsByValue,
  };
}
