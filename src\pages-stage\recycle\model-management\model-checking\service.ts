import { GET, POST } from '@/services/api';

// 模型校验-列表
export function getModelList(params: any) {
  return GET('/super/recycle/replenishment-model/list', params);
}

// 模型校验-详情
export function getModelDetail(params: any) {
  return GET('/super/recycle/replenishment-model/detail', params);
}
// 模型校验-详情修改
export function updateModelDetail(params: any) {
  return POST('/super/recycle/replenishment-model/update', params);
}
// 模型校验-版本列表
export function getVersionList(params: any) {
  return GET('/super/recycle/replenishment-model/version-list', params);
}
// 模型校验-日志列表
export function getLogList(params: any) {
  return GET('/super/recycle/replenishment-model/log-list', params);
}
//模型校验-日志日期列表
export function getLogDateList(params: any) {
  return GET('/super/recycle/replenishment-model/log-date-list', params);
}
