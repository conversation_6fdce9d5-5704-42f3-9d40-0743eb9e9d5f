<template>
  <div class="model flex-wrap flex-vertical flex-con">
    <div class="top">
      <div class="top-left">
        <a-button @click="handleAll">
          全部机型
        </a-button>
        <div class="title">
          {{ breadCrumbList[0].label }}
        </div>
      </div>
      <week-picker
        v-model:value="dateRange"
        class="block"
        @pick="(timeArr: dayjs.Dayjs[]) => onRangeChange(timeArr)"
      />
    </div>
    <a-spin
      :spinning="modelLoading"
      wrapper-class-name="flex-con model-chart"
    >
      <a-row
        :gutter="16"
        :wrap="true"
      >
        <template
          v-for="item in modelChartList"
          :key="item.memory"
        >
          <a-col
            :lg="8"
            :sm="12"
            :xl="6"
            :xs="24"
          >
            <div class="chart-item">
              <div class="item-top">
                <div class="item-top-left">
                  <div class="memory">
                    {{ item.memory }}
                  </div>
                  <a-select
                    v-model:value="item.current_new_ratio"
                    :options="item.new_ratio"
                    size="small"
                    @change="
                      handleRatioChange({
                        chart_key: item.chart_key,
                        memory: item.memory,
                        new_ratio: item.current_new_ratio,
                      })
                    "
                  />
                </div>
                <div
                  class="item-top-right"
                  @click="handleRoute(item.memory as string)"
                >
                  <div class="text">
                    所有成新
                  </div>
                  <RightOutlined
                    class="right-icon"
                    style="width: 12px; height: 12px"
                  />
                </div>
              </div>
            </div>
            <div
              class="chart-container"
              @click="handleToDegree( item.memory as string,item.current_new_ratio as string)"
            >
              <div
                :id="item.chart_key"
                class="chart-container-item"
              />
            </div>
          </a-col>
        </template>
      </a-row>
    </a-spin>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { PropType } from 'vue';
import { message } from 'ant-design-vue';
import { RightOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import type { IBreadCrumb, IRequestModelParams, IMemoryData } from '../data';
import { queryChartList, queryChartDetail } from '../service';
import WeekPicker from '../../../common/components/week-picker.vue';
import { useLineChart } from '../../../common/use-line-chart';

const props = defineProps({
  breadCrumbList: {
    type: Array as PropType<IBreadCrumb[]>,
    default: () => [],
  },
});

const emits = defineEmits(['update:breadCrumbList']);

const dateFormat = 'YYYY-MM-DD';
const dateRange = ref<[Dayjs, Dayjs]>([dayjs(), dayjs()]);
const modelLoading = ref(false);
const modelChartList = ref<IMemoryData[]>([]);

const { render: renderLineChart } = useLineChart();
const generateChart = async (item: any, key?: string) => {
  let currentContainer, chart_key, source;
  //是否改变单个图表
  if (key) {
    chart_key = key;
    currentContainer = document.querySelector(`#${key}`);
    source = item.flat();
  } else {
    chart_key = item.chart_key;
    currentContainer = document.querySelector(`#${chart_key}`);
    source = item.platform_list.flatMap(option => option.platform_data).flat();
  }
  if (currentContainer) {
    currentContainer.innerHTML = '';
  }
  const chart = await renderLineChart({
    container: chart_key,
    source,
    height: 270,
  });
  item.chart = chart;
};

const generateParams = (breadCrumbList: IBreadCrumb[]): IRequestModelParams => {
  const [category_id, brand_id, model_id] = breadCrumbList[0].value;
  return {
    category_id,
    brand_id,
    model_id,
    page: 1,
    page_size: 100,
  };
};

const processQueryChart = async (params: IRequestModelParams) => {
  const { data } = await queryChartList(params);
  const tempData = data.list[0];
  if (!tempData) {
    message.error('该区间暂无数据');
    return;
  }
  dateRange.value = [dayjs(tempData.start_date, dateFormat), dayjs(tempData.end_date, dateFormat)];
  modelChartList.value = tempData.memory_list.map(item => {
    item.chart_key = `r_${Math.ceil(Math.random() * 10e5).toString(36)}`;
    generateChart(item);
    return item;
  });
};

// 图表按日期筛选
const onRangeChange = (timeArr: dayjs.Dayjs[]) => {
  const [start_date, end_date] = timeArr.map(_ => _.format('YYYY-MM-DD'));
  modelChartList.value.length = 0;
  const params = generateParams(props.breadCrumbList);
  params.start_at = start_date;
  params.end_at = end_date;
  processQueryChart(params);
};

//获取所有图表详情
const fetchCharts = () => {
  modelChartList.value.length = 0;
  const params = generateParams(props.breadCrumbList);
  processQueryChart(params);
};

const handleAll = () => {
  emits('update:breadCrumbList', []);
};

//去到该内存所有成新
const handleRoute = (memory: string) => {
  const newBreadList = JSON.parse(JSON.stringify(props.breadCrumbList));
  newBreadList.push({ type: 'memory', label: memory, value: memory });
  emits('update:breadCrumbList', newBreadList);
};

const handleRatioChange = (item: any) => {
  const [category_id, brand_id, model_id] = props.breadCrumbList[0].value;
  const { memory, new_ratio, chart_key } = item;
  const params = {
    category_id,
    brand_id,
    model_id,
    memory,
    new_ratio,
    start_at: dayjs(dateRange.value[0]).format(dateFormat),
    end_at: dayjs(dateRange.value[1]).format(dateFormat),
  };
  queryChartDetail(params).then(({ data }) => {
    const platformDataArray = data.platform_list.flatMap(option => option.platform_data);
    generateChart(platformDataArray, chart_key);
  });
};

const handleToDegree = (memory: string, current_new_ratio: string) => {
  const newBreadList = JSON.parse(JSON.stringify(props.breadCrumbList));
  newBreadList.push(
    { type: 'memory', label: memory, value: memory },
    { type: 'degree', label: current_new_ratio, value: current_new_ratio },
  );
  emits('update:breadCrumbList', newBreadList);
};

onMounted(() => {
  fetchCharts();
});
</script>
<style scoped lang="less">
.model {
  background-color: #fff;
  border-radius: 4px;
}
.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 0 16px;
  .top-left {
    display: flex;
    gap: 12px;
    align-items: center;
    .title {
      position: relative;
      padding-left: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        background: #00c8be;
        border-radius: 2px;
        transform: translateY(-50%);
        content: '';
      }
    }
  }
}

.model-chart {
  padding: 0 16px;
  .chart-item {
    box-sizing: border-box;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
    .item-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .item-top-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }
    .item-top-right {
      display: flex;
      gap: 4px;
      align-items: center;
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      .right-icon {
        width: 12px;
        height: 12px;
      }
      &:hover {
        color: #00c8be;
        cursor: pointer;
      }
    }
  }
}

.chart-container {
  box-sizing: border-box;
  margin-top: 12px;
  padding: 12px;
  border: 1px solid rgba(6, 21, 51, 0.06);
  border-radius: 4px;
  &:hover {
    border: 1px solid #00c8be;
    box-shadow: 0 0 12px 0 rgba(0, 200, 190, 0.2);
    cursor: pointer;
  }
  .chart-container-item {
    width: 100%;
  }
}
</style>
