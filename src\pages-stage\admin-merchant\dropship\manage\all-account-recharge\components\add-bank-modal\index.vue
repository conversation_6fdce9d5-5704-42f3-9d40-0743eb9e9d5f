<!-- src\pages-stage\stock\purchasing-manage\accessory-bill\components\submit-proof.vue 已引用 -->
<template>
  <a-modal
    v-model:visible="addBankModalVisible"
    cancel-text="取消"
    ok-text="添 加"
    title="添加付款银行卡"
    @cancel="handleAddBankCancel"
    @ok="handleAddBankOk"
  >
    <a-alert
      banner
      message="由于国家监管要求，公司性质商家店铺只能添加对公银行卡「数据来源：商家信息库：企业名称」，个体户商家可添加法人银行卡或对公银行卡。「数据来源：商家信息库：法人姓名」"
      style="margin-bottom: 12px"
    />
    <form-create
      ref="addBankModalFormRef"
      v-model:value="addBankModalForm"
      :form-group="formGroup"
      :origin-props="{ layout: 'vertical', rules: addBankModalRules }"
    >
      <template #bank_user_name>
        <a-form-item
          label="开户名称"
          name="bank_user_name"
          required
        >
          <a-input
            v-model:value="addBankModalForm.bank_user_name"
            :allow-clear="true"
            :controls="false"
            :placeholder="textOpenBank"
            style="width: 100%"
          />
        </a-form-item>
      </template>
      <template
        v-if="route.query.type === 'thirdParty'"
        #bank_card_type
      >
        <a-col v-bind="{ md: 8, xl: 6 }">
          <a-form-item
            label="请选择账号类型"
            name="bank_card_type"
            required
          >
            <a-radio-group v-model:value="addBankModalForm.bank_card_type">
              <div class="radio-flex">
                <a-radio :value="2">
                  对私账户
                </a-radio>
                <a-radio :value="1">
                  对公账户
                </a-radio>
              </div>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </template>
    </form-create>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { addBankModalFormGroup } from './config';
import type { TAddBankModalForm } from '../../data';
import { message } from 'ant-design-vue';
import { useVModel } from '@/hook';
import { postCreateBank } from '../../services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:visible', 'ok']);
const route = useRoute();
const formGroup = computed(() => {
  return route.query.type === 'thirdParty'
    ? [
        {
          key: 'bank_card_type',
          fragmentKey: 'renderInput',
        },
        ...addBankModalFormGroup,
      ]
    : addBankModalFormGroup;
});
const addBankModalVisible = useVModel(props, 'visible', emits);

const addBankModalForm = ref<TAddBankModalForm>({
  bank_card_type: 2,
});

const addBankModalRules = {
  bank_user_name: [
    {
      required: true,
      message: '请输入个人/单位名称',
    },
  ],
  bank_name: [
    {
      required: true,
      message: '请输入银行名称，如：中国建设银行',
    },
  ],
  bank_branch_name: [
    {
      required: true,
      message: '请输入开户支行全称',
    },
  ],
  bank_card_num: [
    {
      required: true,
      message: '请输入银行卡号',
    },
  ],
};

const addBankModalFormRef = ref();

function handleAddBankCancel() {
  const formRef = addBankModalFormRef.value?.getFormRef();
  formRef?.resetFields();
  addBankModalVisible.value = false;
}

async function handleAddBankOk() {
  const formRef = addBankModalFormRef.value?.getFormRef();
  await formRef?.validate();
  if (route.query.type === 'platform') {
    addBankModalForm.value.bank_card_type = 1;
  }
  await postCreateBank(addBankModalForm.value);
  message.success('添加成功！');
  handleAddBankCancel();
  emits('ok');
}

const textOpenBank = computed(() => {
  const isThirdPartyType = addBankModalForm.value.bank_card_type === 2 && route.query.type === 'thirdParty';
  const text = isThirdPartyType ? '请输入个人名称' : '请输入个人/单位名称';
  return text;
});
</script>

<style scoped lang="less">
.ant-alert {
  align-items: baseline !important;
}
.ant-alert-icon {
  margin-top: 2px;
}
.radio-flex {
  display: flex;
  width: 480px;
}
</style>
