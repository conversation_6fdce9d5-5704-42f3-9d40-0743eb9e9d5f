<template>
  <a-drawer
    :body-style="{ padding: 0 }"
    class="__custom-valuation-drawer"
    :closable="false"
    destroy-on-close
    :mask-closable="isCheck"
    title="设置评估内容"
    :visible="visible"
    width="85%"
    @close="onClose"
  >
    <template
      v-if="!isCheck"
      #footer
    >
      <div class="flex-wrap flex-x-end">
        <a-button
          style="margin-right: 8px"
          @click="onClose"
        >
          取消
        </a-button>
        <a-button
          :loading="submitLoading"
          type="primary"
          @click="onConfirm"
        >
          确认
        </a-button>
      </div>
    </template>
    <div
      class="trigger"
      :class="{ expand: !chartsPanelVisible }"
      @click="toggleChartsPanelVisible"
    >
      <template v-if="chartsPanelVisible">
        <span>收起价格趋势</span>
      </template>
      <template v-else>
        <span>展开价格趋势</span>
      </template>
      <DoubleLeftOutlined :rotate="chartsPanelVisible ? 180 : 0" />
    </div>
    <div class="main-container flex-wrap">
      <!-- 价格趋势面板 -->
      <ValuationPanel
        :spu-id="state.id"
        :visible="chartsPanelVisible"
      />
      <div class="form-container">
        <a-spin :spinning="loading">
          <a-form>
            <form-card>
              <a-form-item
                label="商品名称"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 12 }"
                v-bind="validateInfos.name"
              >
                <a-input
                  v-model:value="state.name"
                  disabled
                  :placeholder="rules.name[0].message"
                />
              </a-form-item>
              <a-form-item
                label="评估模板"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 12 }"
                v-bind="validateInfos.template_log_id"
              >
                <a-select
                  :disabled="isCheck"
                  :options="templateOptions"
                  :placeholder="rules.template_log_id[0].message"
                  :value="state.template_log_id"
                  @select="onTemplateSelect"
                />
              </a-form-item>
              <a-form-item
                label="商品排序"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 12 }"
                v-bind="validateInfos.sort"
              >
                <a-input-number
                  v-model:value="state.sort"
                  :disabled="isCheck"
                  :min="1"
                  :placeholder="rules.sort[0].message"
                  style="width: 100%"
                  type="number"
                />
              </a-form-item>
              <a-form-item
                label="最低回收价格"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 12 }"
                v-bind="validateInfos.min_price"
              >
                <a-input-number
                  v-model:value="state.min_price"
                  :disabled="isCheck"
                  :min="0"
                  :placeholder="rules.min_price[0].message"
                  style="width: 100%"
                  type="number"
                />
              </a-form-item>
            </form-card>
            <form-card title="基础评估内容">
              <template
                v-if="!isCheck"
                #extra
              >
                <a-tooltip>
                  <template #title>
                    <span>最多可添加3个基础评估项</span>
                  </template>
                  <ExclamationCircleOutlined class="tooltip" />
                </a-tooltip>
              </template>
              <a-form-item
                v-bind="validateInfos.base_option"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 14 }"
              >
                <a-select
                  :disabled="!state.condition_type.length || isCheck || state.base_option.length === 3"
                  mode="multiple"
                  :options="baseOptions"
                  :placeholder="rules.base_option[0].message"
                  :value="state.base"
                  @deselect="onBaseOptionDeselect"
                  @select="onBaseOptionSelect"
                >
                  <template #tagRender="{ label, option }">
                    <a-tooltip>
                      <template #title>
                        {{ option.label }}
                      </template>
                      <a-tag style="margin-right: 3px; cursor: default">
                        {{ label }}
                      </a-tag>
                    </a-tooltip>
                  </template>
                </a-select>
              </a-form-item>
              <template v-if="state.base_option.length">
                <div class="seperator" />
                <a-row
                  :gutter="24"
                  style="margin-top: -12px; margin-bottom: -12px"
                >
                  <a-col
                    v-for="(item, index) in state.base_option"
                    :key="index"
                    :span="8"
                    style="margin-top: 12px; margin-bottom: 12px"
                  >
                    <a-collapse
                      :bordered="false"
                      expand-icon-position="right"
                      ghost
                    >
                      <template #expandIcon="{ isActive }">
                        <a-space
                          :size="4"
                          style="display: inline-flex"
                        >
                          <down-outlined :rotate="isActive ? 180 : 0" />
                          <span>{{ isActive ? '收起' : '展开' }}</span>
                        </a-space>
                      </template>
                      <a-collapse-panel>
                        <template #header>
                          <div
                            class="flex-wrap flex-y-center"
                            style="width: 100%"
                          >
                            <a-popconfirm
                              v-if="!isCheck"
                              title="确定要删除该项吗？"
                              @click.stop="() => {}"
                              @confirm="() => onDeleteOption(item.origin_log_id, index)"
                            >
                              <MinusCircleOutlined class="minus-btn" />
                            </a-popconfirm>
                            <a-tooltip>
                              <template #title>
                                {{ item.name }}
                              </template>
                              <span
                                style="
                                  margin: 0 20px 0 8px;
                                  overflow: hidden;
                                  white-space: nowrap;
                                  text-overflow: ellipsis;
                                "
                              >{{ item.name }}</span>
                            </a-tooltip>
                          </div>
                        </template>
                        <ul>
                          <li
                            v-for="(option, i) in item.option"
                            :key="i"
                          >
                            {{ option }}
                          </li>
                        </ul>
                      </a-collapse-panel>
                    </a-collapse>
                  </a-col>
                </a-row>
              </template>
            </form-card>
            <form-card title="基础价格设置">
              <template v-if="!!state.condition_type.length">
                <a-table
                  bordered
                  :columns="state.columns"
                  :data-source="state.price_set"
                  :pagination="false"
                  :scroll="{ x: 1200 }"
                >
                  <template #emptyText>
                    <span>请先选择基础评估项</span>
                  </template>
                  <template #bodyCell="{ record, index, column }">
                    <template v-if="column?.dataIndex.includes('base')">
                      <template v-if="index > 0">
                        <span>{{ record[column?.dataIndex] }}</span>
                        <a-button
                          v-if="!isCheck"
                          style="position: absolute; top: 0; right: 0"
                          type="link"
                          @click="autoFillWithPreRow(index, column)"
                        >
                          复用上一行
                        </a-button>
                      </template>
                      <template v-else>
                        {{ record[column?.dataIndex] }}
                      </template>
                    </template>
                    <template v-else>
                      <template v-if="!isCheck">
                        <a-input-number
                          v-model:value="record[column?.dataIndex]"
                          :min="0"
                          type="number"
                          @change="() => (hasBeenModified = true)"
                        />
                        <div class="input-value">
                          <span>{{ record[column?.dataIndex] }}</span>
                          <FormOutlined />
                        </div>
                      </template>
                      <template v-else>
                        <span>{{ record[column?.dataIndex] }}</span>
                      </template>
                    </template>
                  </template>
                </a-table>
              </template>
            </form-card>
            <form-card title="功能价格设置">
              <template v-if="state.function_option_set.length">
                <a-row
                  v-for="(item, idx) in state.function_option_set"
                  :key="idx"
                >
                  <a-col :span="14">
                    <a-collapse
                      :bordered="false"
                      expand-icon-position="right"
                      ghost
                    >
                      <template #expandIcon="{ isActive }">
                        <a-space
                          :size="4"
                          style="display: inline-flex"
                        >
                          <down-outlined :rotate="isActive ? 180 : 0" />
                          <span>{{ isActive ? '收起' : '展开' }}</span>
                        </a-space>
                      </template>
                      <a-collapse-panel>
                        <template #header>
                          <div
                            class="flex-wrap flex-y-center"
                            style="width: 100%"
                          >
                            <a-tooltip>
                              <template #title>
                                {{ item.name + (item.is_multi ? '（非必选）' : '（单选）') }}
                              </template>
                              <span
                                :style="{
                                  margin: '0 20px 0 8px',
                                  overflow: 'hidden',
                                  'white-space': 'nowrap',
                                  'text-overflow': 'ellipsis',
                                  color: item.is_multi ? 'var(--ant-primary-color)' : 'inherit',
                                }"
                              >{{ item.name + (item.is_multi ? '（非必选）' : '（单选）') }}</span>
                            </a-tooltip>
                          </div>
                        </template>
                        <ul>
                          <li
                            v-for="(opt, i) in item.set"
                            :key="i"
                            class="func-opt-item"
                          >
                            <div class="flex-wrap flex-x-justify flex-y-center">
                              <span>{{ opt.option }}</span>
                              <a-input-number
                                v-show="!isCheck"
                                v-model:value="opt.price"
                                :min="0"
                                placeholder="请输入评估价格"
                                prefix="-"
                                style="width: 150px"
                                @change="() => (hasBeenModified = true)"
                              />
                              <span v-show="isCheck">{{ `-${opt.price}` }}</span>
                            </div>
                          </li>
                        </ul>
                      </a-collapse-panel>
                    </a-collapse>
                  </a-col>
                </a-row>
              </template>
            </form-card>
          </a-form>
        </a-spin>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import {
  ExclamationCircleOutlined,
  MinusCircleOutlined,
  DownOutlined,
  DoubleLeftOutlined,
  FormOutlined,
} from '@ant-design/icons-vue';
import ValuationPanel from './valuation-panel.vue';
import FormCard from '../../../common/components/form-card.vue';
import useAccessPrice from '../composables/use-access-price';

const emits = defineEmits(['update:visible', 'onLoadList', 'onCancel']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
  currentCommodity: {
    type: Object as PropType<{ id: string; name: string }>,
    default: () => ({}),
  },
  actionType: {
    type: String as PropType<'create' | 'update' | 'check'>,
    default: 'create',
  },
});

const {
  loading,
  submitLoading,
  isCheck,
  hasBeenModified,

  state,
  rules,
  validateInfos,

  chartsPanelVisible,
  toggleChartsPanelVisible,

  templateOptions,
  onTemplateSelect,

  baseOptions,
  onBaseOptionSelect,
  onBaseOptionDeselect,
  onDeleteOption,

  autoFillWithPreRow,

  onClose,
  onConfirm,
} = useAccessPrice(props, emits);
</script>

<style lang="less">
.__custom-valuation-drawer {
  .ant-drawer-content {
    overflow: inherit !important;
  }
}
</style>

<style scoped lang="less">
.block {
  width: 100%;
}
.ant-row + .ant-row {
  margin-top: 16px;
}
:deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
  background: #f7f9fc;
  border-radius: 4px !important;
}
:deep(.ant-collapse-content > .ant-collapse-content-box) {
  padding: 0;
  & > ul {
    margin: 0;
    padding-left: 34px;
    list-style: auto;
    border-left: 2px solid var(--ant-primary-color);
  }
  & > ul > li {
    padding-left: 2px;
  }
}

.minus-btn {
  color: #9298a6;
  transition: color 0.3s;
  &:active,
  &:hover,
  &:focus {
    color: var(--ant-error-color);
  }
}
.trigger {
  position: absolute;
  left: -40px;
  z-index: 9999999999999;
  display: flex;
  width: 40px;
  padding: 16px 12px;
  line-height: 1;
  letter-spacing: 5px;
  white-space: pre-line;
  background: #fff;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  writing-mode: vertical-rl;
  &.expand {
    color: #fff;
    background: var(--ant-primary-color);
  }
  & > span[role='img'] {
    transition: all 0.3s;
  }
}

.main-container {
  width: 100%;
  height: 100%;

  .form-container {
    flex: 1;
    height: 100%;
    padding: 0 24px 24px;
    overflow-y: scroll;
    .form-card {
      padding: 24px 0;
      & + .form-card {
        margin-top: 0;
        border-top: 1px solid #e9e9e9;
      }
      .tooltip {
        margin-left: 8px;
        color: rgba(6, 21, 51, 0.45);
        font-size: 12px;
      }
      .seperator {
        margin: 24px 0;
        border-top: 1px solid #e9e9e9;
      }
      :deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
        background: #f7f9fc;
        border-radius: 4px !important;
      }
      :deep(.ant-collapse-content > .ant-collapse-content-box) {
        padding-right: 0;
        padding-left: 0;
        & > ul {
          margin: 0;
          padding-left: 34px;
          list-style: auto;
          border-left: 2px solid var(--ant-primary-color);
        }
        & > ul > li {
          padding-left: 2px;
        }
      }
      :deep(.ant-table-cell) {
        &:has(.ant-input-number) {
          position: relative;
          padding: 0;
        }
        &:has(.ant-btn) {
          position: relative;
          & > .ant-btn {
            opacity: 0;
            transition: opacity 0.2s;
          }
          &:hover .ant-btn {
            opacity: 1;
          }
        }
        .ant-input-number {
          position: absolute;
          top: 0;
          bottom: 0;
          z-index: 1;
          width: 100%;
          border-radius: 0;
          opacity: 0;
          &:hover {
            & + .input-value > span[role='img'] {
              opacity: 1;
            }
          }
          .ant-input-number-input-wrap,
          .ant-input-number-input {
            height: 100%;
            cursor: pointer;
          }
          .ant-input-number-input {
            padding: 0 26px 0 16px;
          }
        }
        .input-value {
          position: absolute;
          top: 0;
          bottom: 0;
          width: 100%;
          padding: 16px 32px 16px 16px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          & > span[role='img'] {
            position: absolute;
            top: 50%;
            right: 16px;
            color: #3777ff;
            transform: translateY(-50%);
            opacity: 0;
            transition: opacity 0.2s;
          }
        }
        .ant-input-number-focused {
          cursor: text;
          opacity: 1;
          .ant-input-number-input-wrap,
          .ant-input-number-input {
            cursor: text;
          }
          & + .input-value {
            display: none;
          }
        }
      }
      .func-opt-item {
        & + .func-opt-item {
          margin-top: 12px;
        }
        :deep(.ant-input-number-input) {
          height: 24px;
        }
      }
    }
  }
}
</style>
