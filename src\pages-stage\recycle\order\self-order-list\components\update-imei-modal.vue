<template>
  <a-modal
    title="修改IMEI码确认"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item
        class="form-item"
        label="批量回收IMEI"
        name="imei"
        :rules="[{ required: true, message: '请输入批量回收imei' }]"
      >
        <a-input
          v-model:value="formState.imei"
          :maxlength="15"
          placeholder="请输入批量回收imei"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { updateImei } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  imei: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const submitLoading = ref<boolean>(false);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  imei: props.imei,
});

const onCancel = () => emits('update:visible', false);
const onOk = () => {
  formRef.value?.validate().then(() => {
    const params = {
      order_id: props.orderId,
      imei: formState.value.imei,
    };
    submitLoading.value = true;
    updateImei(params)
      .then(() => {
        onCancel();
        message.success('修改成功');
        emits('onLoadList');
      })
      .finally(() => (submitLoading.value = false));
  });
};
</script>
<style scoped lang="less">
.form-item {
  margin-bottom: 0;
}
</style>
