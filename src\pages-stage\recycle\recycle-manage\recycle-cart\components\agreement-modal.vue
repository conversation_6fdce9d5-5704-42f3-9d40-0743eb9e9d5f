<template>
  <a-modal
    v-model:visible="visible"
    :body-style="{ 'padding-top': '24px' }"
    destroy-on-close
    :mask-closable="false"
    title="补充协议"
    :width="640"
    @cancel="close"
  >
    <p>
      商家（以下简称“您”）将需回收的商品交给我们后，我们将尽快对该商品进行详细地评估，评估完成后，我们会及时将报价给到您。
    </p>
    <p>1、 如您的商品符合我们的回收标准，经过检测后结果合格，且您同意我们反馈的报价，回收商将直接进行回收您的商品；</p>
    <p>2、如您的商品不符合我们的回收标准，经过检测后结果不合格，您可按照个人情况勾选以下选项：</p>
    <a-form
      class="form-box"
      :model="formState"
    >
      <a-form-item
        name="agreement_type"
        style="margin: 0"
      >
        <a-radio-group v-model:value="formState.agreement_type">
          <a-radio
            v-for="item in agreementOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.text }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
    <p>我们希望能为您提供更好的回收服务。如果您有任何疑问或需要进一步的帮助，请随时与我们联系。谢谢您的理解和支持！</p>
    <div class="bottom-sign">
      回收运营团队
    </div>
    <template #footer>
      <div class="footer-container">
        <div>
          <a-checkbox v-model:checked="isAgree">
            <span style="color: #333">勾选即同意并遵守</span>
          </a-checkbox>
          <span
            class="text-link"
            @click="handleProtocol"
          >《回收代卖协议》</span>
        </div>

        <div>
          <a-button @click="close">
            取消
          </a-button>
          <a-button
            :disabled="!formState.agreement_type || !isAgree"
            type="primary"
            @click="confirm"
          >
            确认提交订单
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { reactive,ref } from 'vue';

import { agreementOptions } from '../config';
import { IArgeementForm } from '../data.d';

// 弹窗调起时传入的回调函数；
let callBack: any = null;

const visible = ref<boolean>(false);

const formState = reactive<IArgeementForm>({
  agreement_type: null,
});

const open = (cb: any) => {
  callBack = cb;
  visible.value = true;
};

const confirm = () => {
  callBack && callBack({ ...formState });
  close();
};

const close = () => {
  visible.value = false;
};

const isAgree = ref<boolean>(false);

const handleProtocol = async () => {
  let env = '';
  let devHostConfig;
  if (process.env.NODE_ENV === 'development') {
    devHostConfig = await import('../../../../../../devHostConfig');
    env = `${devHostConfig.defaultEnv}${devHostConfig.defaultTarget}-`;
  }
  const url = `https://${env}m.rrzuji.com/live/recycle/recycle-protocol`;
  window.open(url);
};

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
p {
  margin: 0;
  padding: 0;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
.form-box {
  margin: 8px 0;
  :deep(.ant-form-item) {
    margin: 0;
    .ant-radio-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .ant-radio-wrapper {
        color: rgba(6, 21, 51, 0.85);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }
  }
}
.bottom-sign {
  width: 100%;
  margin-top: 32px;
  color: rgba(0, 0, 0, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: right;
}

.footer-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
