<template>
  <div class="article-category">
    <RTable
      ref="refRTable"
      :api="apiSearchTable"
      :columns="columns"
      :group-handler="groupHandler"
    />

    <RModalForm
      ref="refRModalForm"
      v-model:open="addOrEditModal.visible"
      :form-group="addOrEditFormGroup"
      :submit="onSubmit"
      :title="addOrEditModal.isEdit ? '修改类目' : '添加类目'"
      :width="583"
    />

    <a-modal
      v-model:visible="viewModal.visible"
      title="查看类目"
    >
      <a-form>
        <a-form-item
          v-for="item in viewModal.formLabel"
          :key="item.key"
          :label="item.key"
        >
          {{ viewModal.formParams[item.value] }}
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, reactive, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import type { IGroupHandlerParams, RModalFormInstance, RTableInstance } from 'rrz-web-design';

import { addOrEditFormGroup, columns } from './config';
import { createCate, deleteCateById, getArticleCateIndexInf, updateCate } from './service';

const refRTable = ref<RTableInstance | null>(null);
const refRModalForm = ref<RModalFormInstance | null>(null);

async function onUpdate(record: any) {
  await nextTick();
  const useForm = refRModalForm.value!.useForm;
  useForm.setFieldsValue({
    name: record.name,
    id: record.id,
  });
}
const apiSearchTable = (searchParams: any) => {
  return new Promise(async resolve => {
    const params = {
      page: searchParams.page,
      pageSize: searchParams.page_size,
      ModArticleCateSearch: {
        shop_id: searchParams.shop_id,
        id: searchParams.id,
        name: searchParams.name,
      },
    };
    const { data } = await getArticleCateIndexInf(params);
    resolve({
      data: data.list,
      meta: {
        pagination: {
          page: data.page.page,
          page_size: data.page.pageSize,
          total: data.page.count,
        },
      },
    });
  });
};

const addOrEditParams = reactive({
  name: '',
  id: undefined,
});

const viewModal = reactive({
  visible: false,
  formParams: {
    shop_id: '',
    id: '',
    name: '',
  },
  formLabel: [
    { key: '类目ID', value: 'id' },
    { key: '店铺ID', value: 'shop_id' },
    { key: '类目名字', value: 'name' },
  ],
});

const onSubmit = (params: any) => {
  return new Promise(async (resolve, reject) => {
    try {
      const { name, id } = params;
      const data = {
        ModArticleCate: {
          name,
        },
        id,
      };
      const res = addOrEditModal.isEdit ? await updateCate(data) : await createCate(data);
      message.success(res.message);
      refRTable.value?.getTableList();
      addOrEditModal.visible = false;
      resolve();
    } catch (error) {
      reject(error);
    }
  });
};

const addOrEditModal = reactive({
  visible: false,
  isEdit: false,
});

const showModal = () => {
  addOrEditModal.visible = true;
  addOrEditParams.name = '';
  addOrEditParams.id = undefined;
};

const handleAdd = () => {
  showModal();
  addOrEditModal.isEdit = false;
};

const handleEdit = (params: IGroupHandlerParams) => {
  const { record } = params;
  showModal();
  addOrEditModal.isEdit = true;
  onUpdate(record);
};
const handleDelete = (params: IGroupHandlerParams) => {
  const { record } = params;
  Modal.confirm({
    title: '删除该数据将无法恢复，是否继续？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      const result = await deleteCateById({
        id: record.id,
      });
      message.success(result.message);
      refRTable.value?.getTableList();
    },
  });
};

const handleView = (params: IGroupHandlerParams) => {
  const { record } = params;
  viewModal.visible = true;
  const { name, id, shop_id } = record;
  viewModal.formParams.name = name;
  viewModal.formParams.id = id;
  viewModal.formParams.shop_id = shop_id;
};

const groupHandler = {
  handleView,
  handleEdit,
  handleDelete,
};

defineExpose({
  handleAdd,
});
</script>
<style lang="less" scoped>
.article-category {
  margin-top: 8px;
}

.article-category :deep(.r-search-content) {
  margin: 0 0 24px;
}

.article-category :deep(.r-table-content) {
  padding: 0;
}
</style>
