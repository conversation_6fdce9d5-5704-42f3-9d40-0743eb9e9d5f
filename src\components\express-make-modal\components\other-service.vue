<template>
  <a-form
    ref="formRef"
    layout="vertical"
    :model="formState"
  >
    <a-form-item
      label="快递公司"
      name="shipper_code"
      :rules="{ required: true, message: '请选择快递公司' }"
    >
      <a-select
        v-model:value="formState.shipper_code"
        :field-names="{
          label: 'name',
          value: 'type',
        }"
        :options="expressOptions"
        placeholder="请选择快递公司"
        @change="shipperChange"
      />
    </a-form-item>
    <a-form-item
      v-if="![-1, -2].includes(Number(formState.shipper_code))"
      label="快递单号"
      name="logistic_code"
      :rules="{ required: !visitValues.includes(formState.shipper_code), message: '请填写快递单号' }"
    >
      <a-input
        v-model:value="formState.logistic_code"
        placeholder="请输入单号"
      />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import type { FormInstance } from 'ant-design-vue';

import { GET } from '@/services/api';

// 免填快递单号白名单快递值
const visitValues = [-1, -2];

const formRef = ref<FormInstance>();

const formState = reactive({
  shipper_code: null,
  logistic_code: null,
});
const expressOptions = ref<{ type: number; name: string }[]>([]);

async function getOptions() {
  const res = await GET('/super/delivery/get-delivery-type-list');
  expressOptions.value = res.data || [];
}

function validate() {
  return formRef.value?.validate();
}

function shipperChange(val) {
  if (visitValues.includes(val)) {
    formRef.value?.resetFields(['logistic_code']);
  }
}

onMounted(getOptions);

defineExpose({
  validate,
});
</script>
