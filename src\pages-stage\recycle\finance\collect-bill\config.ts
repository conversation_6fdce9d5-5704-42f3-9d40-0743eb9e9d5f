import { FormGroupItem } from '@/components/form-create/src/typing';
import { TableColumnType } from 'ant-design-vue';
import { ISaleBillList } from './data';

// 搜索表单组件配置
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'order_id',
    originProps: { label: '单据编号', name: 'order_id' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入', maxlength: 50 },
    fragmentKey: 'renderInput',
  },
  {
    key: 'server_name',
    originProps: { label: '商户名称', name: 'server_name' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入', maxlength: 20 },
    fragmentKey: 'renderInput',
  },
  {
    key: 'server_id',
    originProps: { label: '商户ID', name: 'server_id' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入', maxlength: 20 },
    fragmentKey: 'renderInput',
  },
];

// 表格列
export const saleBillColumns: TableColumnType<ISaleBillList>[] = [
  {
    title: '单据日期',
    key: 'date',
    dataIndex: 'date',
    width: 120,
    fixed: 'left',
  },
  {
    title: '单据编号',
    key: 'order_id',
    dataIndex: 'order_id',
    width: 180,
    fixed: 'left',
  },
  {
    title: '审核状态',
    key: 'status',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '商户',
    key: 'server_name',
    dataIndex: 'server_name',
    width: 180,
  },
  {
    title: '商户ID',
    key: 'server_id',
    dataIndex: 'server_id',
    width: 100,
  },
  {
    title: '币别',
    key: 'coin_type_text',
    dataIndex: 'coin_type_text',
    width: 74,
  },
  {
    title: '收款金额合计',
    key: 'total_amount',
    dataIndex: 'total_amount',
    width: 120,
  },
  {
    title: '收款核销总金额',
    key: 'total_pay_price',
    dataIndex: 'total_pay_price',
    width: 130,
  },
  {
    title: '收款账户',
    key: 'pay_account_text',
    dataIndex: 'pay_account_text',
    width: 180,
  },
  {
    title: '收款方式',
    key: 'pay_type_text',
    dataIndex: 'pay_type_text',
    width: 100,
  },
  {
    title: '单据来源',
    key: 'source_type_text',
    dataIndex: 'source_type_text',
    width: 100,
  },
  {
    title: '制单人',
    key: 'created_by',
    dataIndex: 'created_by',
    width: 90,
  },
  {
    title: '审核人',
    key: 'check_by',
    dataIndex: 'check_by',
    width: 90,
  },
  {
    title: '修改人',
    key: 'updated_by',
    dataIndex: 'updated_by',
    width: 90,
  },

  {
    title: '制单时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 120,
  },
  {
    title: '修改时间',
    key: 'updated_at',
    dataIndex: 'updated_at',
    width: 120,
  },
];

export const receiptConfirmColumns: FormGroupItem[] = [
  {
    key: 'date',
    title: '单据日期',
    dataIndex: 'date',
    width: 120,
  },
  {
    key: 'source_order_id',
    title: '单据编号',
    dataIndex: 'source_order_id',
    width: 182,
  },
  {
    key: 'order_pay_status_text',
    title: '收款状态',
    dataIndex: 'order_pay_status_text',
    width: 96,
  },
  {
    key: 'server_name',
    title: '商户',
    dataIndex: 'server_name',
    width: 170,
  },
  {
    key: 'coin_type_text',
    title: '币别',
    dataIndex: 'coin_type_text',
    width: 74,
  },
  {
    key: 'pay_price',
    title: '本次应收账款',
    dataIndex: 'pay_price',
    width: 120,
  },
  {
    key: 'finish_pay_price',
    title: '已收账款',
    dataIndex: 'finish_pay_price',
    width: 120,
  },
  {
    key: 'wait_pay_price',
    title: '未收账款',
    dataIndex: 'wait_pay_price',
    width: 120,
  },
];
