<template>
  <a-drawer
    v-model:visible="visible"
    class="opening-record"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
    :get-container="false"
    placement="right"
    title="物流信息"
    width="850"
  >
    <a-spin :spinning="loading">
      <div class="logistic-details">
        <!-- 物流信息 -->
        <div class="logistic-info">
          <span class="label">物流公司</span>
          <span class="info">{{ trackingInfo?.exp_name }}</span>
        </div>
        <div class="logistic-info">
          <span class="label">物流单号</span>
          <span class="info">{{ trackingNumber }}</span>
        </div>
        <div class="logistic-list">
          <a-steps
            v-if="trackingInfo?.list?.length"
            direction="vertical"
            size="small"
          >
            <a-step
              v-for="item in trackingInfo?.list"
              :key="item"
              :description="item.time"
              :title="item.status"
            >
              <template #icon>
                <img src="https://img1.rrzuji.cn/uploads/scheme/2307/12/m/jALp6MKCtHew87B141YY.png">
              </template>
            </a-step>
          </a-steps>
          <a-empty
            v-else
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="close"
      >
        确定
      </a-button>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Empty } from 'ant-design-vue';

import { useModal } from '@/hook/component/use-modal';

import { getTrackingInfo } from '../../../../service';

const props = defineProps<{
  trackingNumber: string;
  phone?: string;
  type?: string;
}>();

const trackingInfo = ref<{ delivery_status: string; exp_name: string; list: any[] }>();

const { open, visible, close, loading } = useModal(undefined, {
  afterOpen: async () => {
    try {
      loading.value = true;
      const { trackingNumber, phone, type } = props;
      const res = await getTrackingInfo({
        tracking_number: trackingNumber,
        phone,
        type,
      });
      trackingInfo.value = res.data;
    } catch (error) {
      loading.value = false;
    } finally {
      loading.value = false;
    }
  },
});

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.logistic-details {
  .logistic-info {
    margin-bottom: 12px;
    .label {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      &::after {
        content: '：';
      }
    }
    .info {
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
    }
  }
  .logistic-list {
    margin-top: 12px;
    padding: 16px;
    background: #f6f7f8;
    border-radius: 2px;
    :deep(.ant-steps-item-tail::after) {
      width: 2px;
      margin-left: -7px;
      background: rgba(6, 21, 51, 0.06);
      border-radius: 2px;
    }
    :deep(.ant-steps-item-title, .ant-steps-item-description) {
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
    }
  }
}
</style>
