import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { getWithholdStatus } from '../services';

export function useWithHold(orderId: string) {
  const loading = ref(false);

  /** 已授权代 */
  function checkWithHold() {
    loading.value = true;
    getWithholdStatus({ orderId: orderId })
      .then(res => {
        message.success(`当前签约状态：${res.data.status_text}`);
      })
      .finally(() => {
        loading.value = false;
      });
  }

  return { loading, checkWithHold };
}
