//自动绑码按钮显示
import { GET, POST } from '@/services/api';

export const getAutoBindCodeBtn = (params: any): Promise<any> => {
  return GET('/super/quality-spu/auto-bind-btn', params);
};
//自动绑码检测
export const getAutoBindCodeBtnCheck = (params: any): Promise<any> => {
  return GET('/super/quality-spu/auto-bind-btn-check', params);
};
//自动绑码提交
export const getAutoBindCode = (params: any): Promise<any> => {
  return POST('/super/quality-spu/auto-bind-device-code', params);
};

//强制换货绑码
export const forceBindCode = (params: any) => {
  return POST('/super/quality-spu/forced-exchange-binding-code', params);
};
