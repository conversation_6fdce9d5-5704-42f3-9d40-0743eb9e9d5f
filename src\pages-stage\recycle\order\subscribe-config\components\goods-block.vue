<template>
  <div class="goods-block">
    <a-spin
      v-if="loading"
      class="page-loading"
      tip="正在加载中..."
    />
    <template v-else>
      <div
        v-for="(item, index) in goods_form"
        :key="index"
        class="select-row-wrap"
      >
        <div class="index-header">
          <div class="index-tags">
            {{ String.fromCharCode(index + 65) }}
          </div>
          场景{{ numberToString(String(index + 1)) }}
        </div>
        <!-- <div class="goods-row-wrap"> -->
        <SelectRow
          :left-attr="{ label: '商品类目', width: '160px' }"
          left-field="category"
          :left-options="goodsOptions"
          :right-attr="{ label: '商品品牌', width: '160px' }"
          right-field="brand"
          :value="item"
          @on-select-change="() => parentChange(index)"
        >
          <template #icon>
            <div style="min-width: 12px">
              <right-outlined
                v-if="item.child && item.child.length"
                :class="['left-arrow-icon', collapseValue.includes(index) && 'select-arrow']"
                @click="onClickArrow(index)"
              />
            </div>
          </template>
          <div class="handle-block">
            <minus-circle-outlined
              class="item-icon del-icon"
              @click="delParent(index)"
            />
            <div
              class="add-item"
              @click="addChild(index)"
            >
              <plus-circle-outlined
                class="item-icon"
                style="margin-right: 8px"
              />
              <span class="add-item-text">添加型号和成新</span>
            </div>
          </div>
        </SelectRow>
        <!-- </div> -->
        <div
          v-show="collapseValue.includes(index)"
          class="child-row"
        >
          <SelectRow
            v-for="(childItem, childIndex) in item.child"
            :key="childIndex"
            :is-relevance="true"
            :left-attr="{ label: '商品型号', width: '400px' }"
            left-field="model"
            :left-options="getFilterContentOptions(item, item.child, childIndex)"
            mode="multiple"
            :right-attr="{ label: '商品成新', width: '160px' }"
            right-field="new"
            :right-options="selectManage.new || []"
            :value="childItem"
          >
            <div class="handle-block">
              <minus-circle-outlined
                class="item-icon del-icon"
                @click="delChild(index, childIndex)"
              />
            </div>
          </SelectRow>
        </div>
      </div>
      <div
        class="add-box"
        :style="!goods_form.length ? 'margin-top: 12px' : ''"
        @click="onAddOrderConfig"
      >
        <plus-circle-filled style="margin-right: 4px; color: #00c8be; font-size: 16px" />
        添加场景
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { RightOutlined, PlusCircleFilled, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
import { getCategoryMenu } from '../service';
import SelectRow from './goods-select.vue';

defineProps({
  selectManage: {
    type: Object,
    default: () => ({}),
  },
});

const loading = ref<boolean>(false);

const collapseValue: number[] = reactive([]);

const onClickArrow = (index: number) => {
  if (collapseValue.includes(index)) {
    const currentIndex = collapseValue.findIndex(item => item === index);
    collapseValue.splice(currentIndex, 1);
    return;
  }
  collapseValue.push(index);
};

const goods_child_template: any = {
  model: [],
  modelCn: '',
  new: [],
  newCn: '',
};

const goods_template: any = {
  category: null,
  categoryCn: '',
  brand: null,
  brandCn: '',
  child: [{ ...goods_child_template }],
};

const goods_form: any = ref([]);

const goodsOptions = ref<any>([]);

// 商品类目、品牌下拉修改
const parentChange = (index: number) => {
  goods_form.value[index].child = [JSON.parse(JSON.stringify(goods_child_template))];
};

const onAddOrderConfig = () => {
  const obj = JSON.parse(JSON.stringify(goods_template));
  goods_form.value.push(obj);
  const length = goods_form.value.length;
  collapseValue.push(length - 1);
};

const delParent = (index: number) => {
  goods_form.value.splice(index, 1);
};

const addChild = (index: number) => {
  const obj = JSON.parse(JSON.stringify(goods_child_template));
  goods_form.value[index].child.push(obj);
  if (!collapseValue.includes(index)) {
    collapseValue.push(index);
  }
};

const delChild = (parentIndex: number, childIndex: number) => {
  goods_form.value[parentIndex].child.splice(childIndex, 1);
};

const getFilterContentOptions = (item: any, child: any, index: number) => {
  const { category, brand } = item;
  const currentBrand = goodsOptions.value.find((item: any) => item.id === category) || {};
  const currentCategoryOptions = JSON.parse(JSON.stringify(currentBrand));
  const currentModel = (currentCategoryOptions.child || []).find((item: any) => item.id === brand) || {};
  (currentModel.child || []).forEach((modelChildItem: any) => {
    child.forEach((childItem: any, childIndex: number) => {
      // 排除自身值，其余进行禁用判断
      if (childIndex != index) {
        const { model } = childItem;
        if (model.includes(modelChildItem.id)) {
          modelChildItem.disabled = true;
        }
      }
    });
  });
  return (currentModel && currentModel.child) || [];
};

const getParams = () => {
  const goodsForm = goods_form.value.filter((item: any) => item.category);
  // 过滤掉 型号、成新度填写不完整的数据
  goodsForm.forEach((item: any) => {
    if (item.child && item.child.length) {
      item.child = item.child.filter((childItem: any) => childItem.model.length && childItem.new.length);
    }
  });
  return JSON.parse(JSON.stringify(goodsForm));
};
const setFormData = (params: any) => {
  goods_form.value = params;
};

const getCategoryList = () => {
  loading.value = true;
  getCategoryMenu().then((res: any) => {
    if (res.status === 0) {
      const data = res.data || [];
      data.forEach((item: any) => {
        const oldList = [...goodsOptions.value];
        goodsOptions.value = [...oldList, ...item.child];
      });
      loading.value = false;
    }
  });
};

const numberToString = (number: string) => {
  if (number.match(/\D/) || number.length >= 14) return;
  let zhArray = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']; // 数字对应中文
  let baseArray = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万']; //进位填充字符，第一位是 个位，可省略
  const tenStr = ['十零', '二十零', '三十零', '四十零', '五十零', '六十零', '七十零', '八十零', '九十零'];
  let string = String(number)
    .split('')
    .reverse()
    .map((item, index) => {
      // 把数字切割成数组并倒序排列，然后进行遍历转成中文
      // 如果当前位为0，直接输出数字， 否则输出 数字 + 进位填充字符
      item = Number(item) == 0 ? zhArray[Number(item)] : zhArray[Number(item)] + baseArray[index];
      return item;
    })
    .reverse()
    .join(''); // 倒叙回来数组，拼接成字符串
  string = string.replace(/^一十/, '十'); // 如果以 一十 开头，可省略一
  string = string.replace(/零+/, '零'); // 如果有多位相邻的零，只写一个即可
  if (tenStr.includes(string)) {
    string = string.replace(/零/, '');
  }
  return string;
};

onMounted(() => {
  getCategoryList();
});

defineExpose({ getParams, setFormData });
</script>

<style lang="less" scoped>
.goods-block {
  .select-row-wrap {
    padding: 8px 16px;
    border-radius: 2px;
    &:hover {
      background: #f5f7fa;
    }
    .index-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      .index-tags {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        height: 22px;
        margin-right: 8px;
        color: #fff;
        background: #00c8be;
        border-radius: 50%;
      }
    }
    .goods-row-wrap {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
    .handle-block {
      display: inline-flex;
      align-items: center;
      margin-left: 16px;
      .item-icon {
        margin-right: 16px;
        font-size: 16px;
        &.del-icon {
          &:hover {
            color: #f5222d;
          }
        }
      }
      .add-item {
        display: inline-flex;
        align-items: center;
        .add-item-text {
          margin-top: 1px;
          color: rgba(6, 21, 51, 0.65);
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
        }
        &:hover {
          color: #3777ff;
          cursor: pointer;
          .add-item-text {
            color: #3777ff;
          }
        }
      }
    }
    .child-row {
      position: relative;
      padding-left: 40px;
      // :deep(.goods-row-wrap) {
      .select-row {
        &::after {
          position: absolute;
          top: 0;
          left: -16px;
          z-index: 10;
          width: 1px;
          height: 90%;
          margin-top: 4px;
          background: rgba(6, 21, 51, 0.15);
          content: '';
        }
        &:nth-child(n + 2) {
          &::before {
            position: absolute;
            top: -8px;
            left: -16px;
            width: 1px;
            height: 12px;
            background: rgba(6, 21, 51, 0.15);
            content: '';
          }
        }
        &:nth-last-child(1) {
          &::after {
            margin-top: 0;
            margin-bottom: 4px;
          }
          &::before {
            height: 8px;
          }
        }
      }
    }
  }
}
.left-arrow-icon {
  color: rgba(6, 21, 51, 0.65);
  font-size: 12px !important;
  transition: all 0.2s ease;
  &.select-arrow {
    transform: rotateZ(90deg);
  }
}
.add-box {
  display: inline-flex;
  align-items: center;
  margin-top: 4px;
  padding-left: 16px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;
}
.add-icon {
  margin-left: 8px;
  font-size: 20px;
  cursor: pointer;
}
</style>
