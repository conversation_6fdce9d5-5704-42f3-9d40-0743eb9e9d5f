import { GET, POST } from '@/services/api';

import { EPayType } from './data';

// 全部银行卡
export function getBankList(params: any = {}) {
  return GET('/server-bank-card/list', params);
}

// 创建银行卡
export function postCreateBank(params: any) {
  return POST('/server-bank-card/create', params);
}

// 删除银行卡
export function postDelBank(id: string) {
  return POST('/server-bank-card/delete', { id });
}

export function getAccounts() {
  return GET('/supply-chain-account/accounts');
}

// 支付宝充值
export function postAliPayAccount(params: any) {
  return POST('/pay/supply-chain-account', params);
}

// 创建汇款记录
export function postPayAccount(params: any) {
  return POST('/supply-chain-account-corporate-pay/create', params);
}

export function getRechargeConfig(account_id: string) {
  return GET('/supply-chain-account-recharge-records/get-recharge-config', { account_id });
}

export function getIssetRepayment() {
  return GET('/supply-chain-account-credit/isset-repayment');
}

export function rechargeByCmb(data: any) {
  return POST('/supply-chain-account/recharge-by-cmb', data);
}

export function deleteAlipayAccount(id: number) {
  return POST('/alipay-server-account/delete', { id });
}
export function getAlipayAccountList() {
  return GET('/alipay-server-account/list');
}
export function createAlipayAccount(data: any) {
  return POST('/alipay-server-account/create', data);
}

export function getRechargeRecords() {
  return GET('/supply-chain-account-recharge-records/check-config');
}
export function createConfigByMoney(data: any) {
  return POST('/supply-chain-account-recharge-records/create-config-by-money', data);
}
// 判断是否固定商家

export function isFixedAccount() {
  return GET('/supply-chain-account/fixed-account');
}

//根据充值金额获取手续费
export function getPayComputeMoney(params: { money: number }) {
  return GET('/pay/compute-money', params);
}
//商家支付方式统计
export function savePayTag(data: { type: EPayType }) {
  return POST('/pay/server-pay-tap', data);
}
