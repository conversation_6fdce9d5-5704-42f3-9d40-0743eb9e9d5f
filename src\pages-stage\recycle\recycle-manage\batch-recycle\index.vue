<template>
  <layout-shops-page title="设备回收">
    <template #extra>
      <div class="flex-box">
        <img
          src="https://img1.rrzuji.cn/uploads/scheme/2408/27/m/mDs29r45nQlT6BFictnr.png"
          width="193px"
        >
        <a-button
          v-if="!isHidden"
          type="primary"
          @click="onGoRecycleCart"
        >
          <template #icon>
            <plus-outlined />
          </template>
          企业回收
        </a-button>
      </div>
    </template>
    <div class="container">
      <div class="main">
        <a-alert
          v-if="isHidden"
          message="由于法定节假日影响，4.30-5.5公司放假，5.5号开始开放企业回收入口。"
          show-icon
          style="margin-bottom: 16px"
          type="warning"
        />
        <div class="tips-panel">
          <img
            class="tips-panel__icon"
            src="https://img1.rrzuji.cn/uploads/scheme/2305/29/m/6uRwbmq3kXSCUeLsEKo6.png"
          >
          <div>
            1.您可通过商家后台此页面、支付宝小程序趣回收进行查看及确认订单操作（趣回收小程序登录账号需为企业下所关联的账号）；<br>
            2.回收设备类型目前可支持从供应链代发设备及其他非代发设备；非代供应链发设备回收通道为限时开放；<br>
            3.回收商品收款渠道暂只支持直付通企业支付宝账户进行结算；<br>
            4.待确认订单请在120小时内进行定价确认，超时则会默认为同意定价；<br>
            5.回收商品交易未完成时会按照寄件地址原路退回。
          </div>
        </div>
        <a-form
          ref="form"
          class="search-group"
          layout="inline"
          :model="searchForm"
        >
          <a-form-item
            label="订单ID："
            name="orderId"
          >
            <a-input
              v-model:value="searchForm.orderId"
              placeholder="请输入"
              style="width: 214px"
            />
          </a-form-item>
          <a-form-item
            label="订单状态："
            name="orderStatus"
          >
            <a-select
              v-model:value="searchForm.orderStatus"
              allow-clear
              max-tag-count="responsive"
              mode="multiple"
              :options="orderStatusOptions"
              placeholder="请选择"
              style="width: 214px"
            />
          </a-form-item>
          <a-form-item
            label="检测结果"
            name="qualityResult"
          >
            <a-select
              v-model:value="searchForm.qualityResult"
              :options="qualityResultOptions"
              placeholder="请选择"
              style="width: 214px"
            />
          </a-form-item>
          <a-form-item
            label="批次号："
            name="batchNo"
          >
            <a-input
              v-model:value="searchForm.batchNo"
              placeholder="请输入"
              style="width: 214px"
            />
          </a-form-item>
          <a-form-item
            label="选择日期"
            name="rangDate"
          >
            <a-range-picker
              ref="rangePicker"
              v-model:value="rangDate"
              style="width: 240px"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
          <a-form-item
            label="活动订单类型"
            name="activity_code"
          >
            <a-select
              v-model:value="searchForm.activity_code"
              :options="[
                {
                  label: '暖冬补贴活动',
                  value: 'hot_winter_allowance',
                },
              ]"
              placeholder="请选择订单类型"
              style="width: 214px"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              style="margin-right: 8px"
              type="primary"
              @click="onSearch"
            >
              搜索
            </a-button>
            <a-button @click="onReset">
              重置
            </a-button>
          </a-form-item>
        </a-form>
        <customer-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :table-thead-class="['recycle-sale-table-thead']"
        >
          <template #rowHeader="{ record }">
            <div class="order-head">
              <span>订单批次：{{ record.batch_no }}</span>
              <span>订单号：{{ record.order_id }}</span>
              <span>创建日期：{{ record.created_at }}</span>
            </div>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'goods_image'">
              <div class="order-image">
                <a-image
                  :src="record.goods_image"
                  :width="80"
                />
                <span style="padding-left: 12px">{{ record.goods_name }}</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'logistics_info'">
              <div class="order-body">
                <div>
                  <span>寄出：</span>
                  <span v-if="record.logisticInfo.tracking_num">
                    <a
                      class="a-link-hover"
                      style="padding: 0"
                      @click="() => onSeeLogisticDetail(record.logisticInfo)"
                    >
                      {{ record.logisticInfo?.tracking_num }}
                    </a>
                  </span>
                  <span v-else>暂无</span>
                </div>
                <div>
                  <span>退回：</span>
                  <span v-if="record.returnLogisticInfo.tracking_num">
                    <a
                      class="a-link-hover"
                      style="padding: 0"
                      @click="() => onSeeLogisticDetail(record.returnLogisticInfo)"
                    >
                      {{ record.returnLogisticInfo?.tracking_num }}
                    </a>
                  </span>
                  <span v-else>暂无</span>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'evaluate_info'">
              <div class="order-body">
                <div>预估回收价：¥{{ record.evaluate_price }}</div>
                <div v-if="record.evaluate_advance_price">
                  {{ `（估价金额：¥${record.evaluate_base_price},补贴金额：¥${record.evaluate_advance_price})` }}
                </div>
                <a
                  class="a-link-hover"
                  @click="() => onSeeEvaluate(record.order_id)"
                > 查看估价报告 </a>
              </div>
            </template>
            <template v-if="column.dataIndex === 'purchased_info'">
              <div class="order-body">
                <div>实际回收价：¥{{ record.purchased_price }}</div>
                <div v-if="record.advance_purchased_price">
                  <div>{{ `(定价金额：¥${record.base_purchased_price}` }}</div>
                  <div>{{ `补贴金额：¥${record.advance_purchased_price})` }}</div>
                </div>
                <div v-else>
                  {{ `(定价金额：¥${record.base_purchased_price})` }}
                </div>
                <span v-if="Number(record.showBtnViewQuaReport) === 1">
                  <a
                    class="a-link-hover"
                    @click="() => onSeeQuality(record)"
                  >查看检测结果</a>
                </span>
                <span v-else> 暂无检测结果 </span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'quality_result_text'">
              <div class="order-center recycle-customer-table-flex-col">
                {{ record.quality_result_text }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'order_status_text'">
              <div class="order-body">
                <template v-if="record.order_status == 5">
                  <div style="color: #ff6231">
                    {{ record.order_status_text }}
                  </div>
                  <div style="margin-top: 8px; color: #ff6231">
                    还剩{{ record.countDown }}
                  </div>
                </template>
                <template v-else>
                  <div>{{ record.order_status_text }}</div>
                </template>
                <template v-if="record.cancel_reason_role">
                  <div>取消类型：{{ record.cancel_reason_role }}</div>
                  <div>取消原因：{{ record.cancel_reason_text }}</div>
                </template>
              </div>
            </template>
            <template v-if="column.dataIndex === 'handle'">
              <div class="order-center recycle-customer-table-flex-col">
                <a-button
                  v-if="record.canEditReturnInfo == 1"
                  class="handle-btn"
                  type="link"
                  @click="onUpdateAddr(record.order_id)"
                >
                  修改退货信息
                </a-button>
                <a-button
                  v-if="record.order_status === '1'"
                  class="handle-btn"
                  danger
                  type="link"
                  @click="onCloseOrder(record.order_id)"
                >
                  取消订单
                </a-button>
                <a-button
                  v-if="record.protocol_url"
                  class="handle-btn"
                  type="link"
                  @click="openView(record.protocol_url)"
                >
                  查看协议
                </a-button>
                <!--待确认操作 -->
                <template v-if="[5].includes(Number(record.order_status))">
                  <a-button
                    class="handle-btn"
                    danger
                    style="display: block"
                    type="link"
                    @click="onConfirm('fail', record.order_id)"
                  >
                    放弃回收
                  </a-button>
                  <a-button
                    class="handle-btn"
                    style="display: block"
                    type="link"
                    @click="onConfirm('ok', record.order_id)"
                  >
                    同意收款
                  </a-button>
                </template>
              </div>
            </template>
          </template>
          <template #expandedRowRender="{ record }">
            <tr class="expanded-row">
              <td
                :colspan="columns.length"
                style="padding: 12px 16px"
              >
                <logistics-info
                  :logistic-info="record.logisticInfo"
                  :return-logistic-info="record.returnLogisticInfo"
                />
              </td>
            </tr>
            <tr class="expanded-row">
              <td
                :colspan="columns.length"
                style="padding: 12px 16px"
              >
                <crm-remark
                  :id="`${record.order_id}_recySerBill`"
                  :block-flex="false"
                  :item="record.remarks"
                  layout="horizontal"
                  log-link="/crm/log"
                  push-link="/crm/push"
                  size="block"
                  @add-success="getTableList"
                >
                  <template #emptyText>
                    暂无备注
                  </template>
                </crm-remark>
              </td>
            </tr>
          </template>
        </customer-table>
        <div
          v-if="page.total"
          class="pagination-box"
        >
          <div class="total-box">
            <span class="total-num">共<span class="value">{{ page.total }}</span>条记录</span>
            <span class="page-num">第<span class="value">{{ page.current }} / {{ Math.ceil((page.total as number) / (page.pageSize as number)) }}</span>页</span>
          </div>
          <div class="pagination">
            <a-pagination
              v-model:current="page.current"
              v-model:page-size="page.pageSize"
              :page-size-options="page.pageSizeOptions"
              show-quick-jumper
              :total="page.total"
              @change="tableChange({ current: page.current, pageSize: page.pageSize })"
            />
          </div>
        </div>
      </div>
    </div>
  </layout-shops-page>
  <EvaluateModal
    v-if="evaluateModalVisible"
    v-model:visible="evaluateModalVisible"
    :order-id="currentOrderId"
    request-url="/recycle-batch-order/get-device-info"
  />
  <ReasonModal
    v-if="reasonModalVisible"
    v-model:visible="reasonModalVisible"
    @on-ok="handleReasonModalOk"
  />
  <UpdateAddrModal
    v-if="updateAddrModalVisible"
    v-model:visible="updateAddrModalVisible"
    :order-id="currentOrderId"
    @get-refresh="getUpdateAddr"
  />
  <QuaReportModal
    v-model:visible="qualityState.visible"
    :payload="qualityState.payload"
  />
  <LogisticDrawer ref="logisticRef" />
</template>

<script lang="ts" setup>
import { computed, createVNode, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue';
// import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { AxiosResponse } from 'axios';
import { debounce } from 'lodash-es';

import QuaReportModal from '@/components/quality-report-wrapper/qua-report-modal.vue';
import { useTable } from '@/hook/component/use-table';
import LogisticDrawer from '@/pages-stage/recycle/common/components/logistic-drawer.vue';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import useCountDown from '@/pages-stage/recycle/common/use-count-down';
import { POST } from '@/services/api';

import CustomerTable from './components/customer-table/index.vue';
import EvaluateModal from './components/evaluate-modal.vue';
import LogisticsInfo from './components/logistics-info/index.vue';
import ReasonModal from './components/reason-modal.vue';
import UpdateAddrModal from './components/update-addr-modal.vue';
import useCeiling from './composables/use-ceiling';
import { columns, orderStatusOptions } from './config';
import { ILogisticInfo } from './data';
import { cancelOrder, confirmOrder, refuseOrder } from './service';

const timers = ref<NodeJS.Timeout[]>([]);
const clearTicker = () => {
  timers.value.forEach(timer => {
    clearInterval(timer);
  });
  timers.value.length = 0;
};
onBeforeUnmount(clearTicker);

// const router = useRouter();

const form = ref<any>();

const searchForm = reactive<any>({
  orderId: null,
  qualityResult: '',
  orderStatus: [],
  batchNo: null,
  startAt: '',
  endAt: '',
});
const rangDate = ref<string[]>([]);
const currentOrderId = ref('');
const actionFn = ref('');

const qualityState = ref({
  visible: false,
  payload: {},
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  url: '/recycle-batch-order/list',
  searchForm,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  // formatHandle: (res: { data: { list: IDataSourceItem[] } }) => res.data.list,
  formatHandle: async (res: AxiosResponse<{ list: any[] }>) => {
    clearTicker();
    const ids = res.data.list.map((item: any) => item.order_id);
    let result = await POST('/crm/data', { unionIds: ids, unionSuffix: '_recySerBill' });
    let marks = {};
    result.data.forEach((item: any) => {
      marks[item.union_id] = item;
    });
    res.data.list.forEach((item: any) => {
      item.remarks = marks[`${item.order_id}_recySerBill`];
      if (item.order_status == 5 && item.remaining_time > 0) {
        const { text, timer } = useCountDown({
          seconds: item.remaining_time,
          callback: debounceFetchTable,
        });
        item.countDown = text;
        timers.value.push(timer);
      }
    });
    return res.data.list;
  },
  pagination: {
    showTotal: (): string => {
      let totalPages = Math.ceil((page.total ?? 0) / (page.pageSize ?? 0));
      return `共 ${page.total} 条记录   第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const debounceFetchTable = debounce(getTableList, 500);

// 选择日期修改，手动处理变量赋值；
const panelChange = (value: string[]) => {
  searchForm.startAt = value[0] || null;
  searchForm.endAt = value[1] || null;
};

const onSearch = () => {
  panelChange(rangDate.value || []);
  getTableList('search');
};

const onReset = () => {
  form.value?.resetFields();
  rangDate.value = [];
  onSearch();
};
const [evaluateModalVisible, { setTrue: showEvaluateModal }] = useBoolean();
const [reasonModalVisible, { setTrue: showReasonModalVisible, setFalse: hideReasonModalVisible }] = useBoolean();
const [updateAddrModalVisible, { setTrue: showUpdateAddrModalVisible }] = useBoolean();

// 查看评估
const onSeeEvaluate = (order_id: string) => {
  currentOrderId.value = order_id;
  showEvaluateModal();
};

const qualityResultOptions = ref([
  { value: '', label: '全部' },
  { value: '1', label: '合格' },
  { value: '2', label: '不合格' },
]);
// 查看检测
const onSeeQuality = (record: Record<string, any>) => {
  const { device_batch_no: batch_no, device_type, device_unicode: device_code } = record;

  qualityState.value = {
    visible: true,
    payload: {
      device_code,
      batch_no,
      device_type,
      hide_condition: true, // 隐藏成新度
    },
  };
};

// 查看物流详情
const logisticRef = ref();
const onSeeLogisticDetail = (logisticInfo: ILogisticInfo) => {
  const { tracking_num, logistic_code, logistic_phone, shipper_name } = logisticInfo;
  logisticRef.value.open({
    code: tracking_num,
    phone: logistic_phone,
    type: logistic_code || 'SF',
    shipper_name,
  });
};

// 关闭订单
const onCloseOrder = (order_id: string) => {
  currentOrderId.value = order_id;
  actionFn.value = 'cancelFn';
  showReasonModalVisible();
};

// 待确认 - 【放弃回收、同意收款】操作
const onConfirm = (type: string, order_id: string) => {
  const requestFn = {
    fail: {
      fn: refuseOrder,
      title: '请确认是否放弃该笔交易？',
    },
    ok: {
      fn: confirmOrder,
      title: '请确认是否对该笔订单进行收款',
    },
  };
  const currentObj = requestFn[type];
  if (!currentObj) return;
  const { fn, title } = currentObj;
  if (type === 'fail') {
    currentOrderId.value = order_id;
    actionFn.value = 'refuseFn';
    showReasonModalVisible();
  } else {
    Modal.confirm({
      title,
      content: createVNode('div', {
        style: 'color: rgba(0,0,0,0.65);',
      }),
      onOk() {
        return fn(order_id).then((res: any) => {
          message.success(res.message || '操作成功');
          getTableList();
        });
      },
    });
  }
};
//取消接口调用
const handleReasonModalOk = (type: number) => {
  const reqTypeMapFn = {
    refuseFn: refuseOrder,
    cancelFn: cancelOrder,
  };
  reqTypeMapFn[actionFn.value](currentOrderId.value, type).then((res: any) => {
    message.success(res.message || '操作成功');
    hideReasonModalVisible();
    getTableList();
  });
};

const onGoRecycleCart = () => {
  message.warning({
    content: () =>
      '尊敬的商户：因平台业务调整需要，即日起暂停趣回收商家端的回收业务（个人用户端不受影响）。商家如有设备处置要求，请参加平台的送检、循环租赁业务或自行处置。业务暂停不影响历史回收订单的结算，待结算部分将于6月25日前完成打款，请耐心等待。感谢对回收业务一直以来的支持。人人租回收业务组，2025年6月21日。',
    style: {
      textAlign: 'left',
      width: '800px',
      margin: '0 auto',
    },
  });
  // router.push({
  //   path: '/recycle/recycle-manage/recycle-cart',
  // });
};

// 打开协议
const openView = (url: string) => {
  window.open(url);
};

const getUrlParams = () => {
  nextTick(() => {
    const search = decodeURIComponent(window.location.search);
    if (search) {
      const urlRequestParams = {};
      const urlArr = search.split('&');
      urlArr.forEach((item: string) => {
        item = item.replace('?', '');
        const itemArr = item.split('=');
        if (itemArr[1] && !['role', 'origin'].includes(itemArr[0])) {
          urlRequestParams[itemArr[0]] = itemArr[1];
        }
      });
      const keyArr = Object.keys(urlRequestParams);
      keyArr.forEach(key => {
        if (searchForm.hasOwnProperty(key)) {
          searchForm[key] = urlRequestParams[key];
        }
      });
    }
    getTableList('search');
  });
};

getUrlParams();

// 27号后隐藏，7号后恢复显示;
const isHidden = computed(() => {
  const beforeTime = new Date('2024/04/30 00:00:00').getTime();
  const afterTime = new Date('2024/05/05 00:00:00').getTime();
  const curTime = new Date().getTime();
  return beforeTime < curTime && curTime < afterTime;
});

//修改退货信息
const onUpdateAddr = (order_id: string) => {
  currentOrderId.value = order_id;
  showUpdateAddrModalVisible();
};
const getUpdateAddr = () => {
  getTableList();
};

onMounted(() => {
  useCeiling();
});
</script>

<style lang="less" scoped>
@import '../../common/base.less';

.flex-box {
  display: flex;
  gap: 8px;
  align-items: center;
  img {
    margin-top: -15px;
  }
}
.tips-panel {
  display: flex;
  padding: 9px 45px 9px 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background: #f0f7ff;
  border-radius: 2px;
  &__icon {
    width: 16px;
    height: 16px;
    margin-top: 3px;
    margin-right: 7px;
  }
}
.search-group {
  gap: 16px 0;
  margin: 24px 0;
}

.handle-btn {
  padding: 4px 0;
}

//订单头部
.order-head {
  display: flex;
  gap: 16px;
  align-items: center;
  height: 54px;
  padding-left: 16px;
}
:deep(.recycle-sale-table-thead tr th) {
  background: #fafafb !important;
}

// body列表
.order-image {
  display: flex;
  align-items: center;
  padding: 16px;
}
.order-body {
  display: flex;
  flex-direction: column;
  padding: 16px;
}
.order-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
//扩展部分
.expanded-row {
  border-top: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;
  > td {
    padding: 12px;
  }
}

.a-link-hover {
  color: #061533d9;
  font-weight: 400;
  font-size: 14px;
  text-decoration: underline;
}
.a-link-hover:hover {
  color: #3777ff;
  font-weight: 400;
  font-size: 14px;
  text-decoration: underline;
}

.pagination-box {
  position: fixed;
  right: 16px;
  bottom: 0;
  left: 16px;
  z-index: 9;
  height: 64px;
  margin: 0;
  padding: 21px 24px;
  background-color: #fff;
  border-top: 1px solid rgba(6, 21, 51, 0.06);
  .ant-pagination-total-text {
    flex: 1;
  }
  .total-box {
    float: left;
    color: rgba(6, 21, 51, 0.45);
    .page-num {
      margin-left: 16px;
    }
    .value {
      margin: 0 4px;
      color: rgba(6, 21, 51, 0.65);
    }
  }
  .pagination {
    float: right;
  }
}
.custom-class {
  display: flex;
  flex-wrap: wrap;
}
</style>
