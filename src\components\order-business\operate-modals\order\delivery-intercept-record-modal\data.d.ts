export interface IWorkListItem {
  id: number;
  v3_order_id: number;
  sku_info: string;
  order_status: number;
  order_status_text: string;
  delivery_status: number;
  delivery_status_text: string;
  warehouse_id: number;
  warehouse_name: string;
  work_type: number;
  work_type_text: string;
  device_and_status: null | string;
  wait_process_num: number;
  finish_time: string;
  logistic_code: string;
  current_node: string;
  created_at: string;
  logistis_list_status: number;
  logistis_list_status_text: string;
  new_address: string;
  work_status: number;
  work_status_text: string;
  name: string;
  phone: string;
  server_id: number;
  server_name: string;
  work_verify_at: string;
  deal_name: string;
  deal_at: string;
  show_detail_btn: boolean;
  reject_remark: string;
  apply_remark: string;
  images: string;
  work_verify_result: string;
}
export type TAssociationWorkNumber = {
  number: string;
  sort_name: string;
  template_name: string;
};
