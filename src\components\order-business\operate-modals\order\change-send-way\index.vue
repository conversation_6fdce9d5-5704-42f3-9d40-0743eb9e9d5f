<template>
  <a-modal
    :ok-button-props="{
      disabled: confirmLoading,
    }"
    title="修改寄出方式"
    :visible="visible"
    width="540px"
    @cancel="$emit('update:visible', false)"
    @ok="onSubmit"
  >
    <h3 class="title-text">
      当前寄出方式和运费
    </h3>
    <div>
      <span class="self-label">寄出方式：</span><span>{{ sendType }}</span>
    </div>
    <div>
      <span class="self-label">运费：</span><span>{{ money }}</span>
    </div>

    <h3 style="margin-top: 16px">
      修改后寄出方式和运费
    </h3>
    <a-form
      ref="refForm"
      :model="sendForm"
      radio-group
    >
      <a-form-item
        label="寄出方式"
        name="send_type"
        :rules="[{ required: true }]"
      >
        <a-radio-group
          v-model:value="sendForm.send_type"
          :options="sendOptions"
        />
      </a-form-item>
    </a-form>
    <div><span class="self-label">运费：</span><span>0.00</span></div>
    <div style="color: red">
      <span class="self-label">退还用户运费：</span><span>{{ money }}</span>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { type PropType, reactive, ref, watch } from 'vue';
import { debounce } from 'lodash-es';
import { RForm, useCompRef } from 'rrz-web-design';

import {
  getSendDetail,
  updateSendType,
} from '@/components/order-business/operate-modals/order/change-send-way/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

const money = ref('');
const sendType = ref('');
const sendOptions = ref([]);

interface IExtraData {
  delivery_mark_id?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});
const emits = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const sendForm = reactive({
  send_type: undefined,
});

const refForm = useCompRef(RForm);
const onSubmit = debounce(async () => {
  await refForm.value.validate();
  await updateSendType({
    order_id: props.orderId,
    send_type: sendForm.send_type,
  });
  emits('update:visible', false);
  emits('refresh', props.orderId);
}, 200);


async function init() {
  const res = await getSendDetail(props.orderId);
  money.value = res.data.freight?.toFixed(2);
  sendType.value = res.data.send_type_label;
  sendOptions.value = res.data?.support_send_type_list?.map(item => ({
    label: item.value,
    value: item.key,
  }));
}

watch(
  () => props.visible,
  val => {
    if (val) {
      init();
    }
  },
  {
    immediate: true,
  },
);
</script>
<style lang="less" scoped>
:deep(.ant-radio-wrapper-in-form-item) {
  display: block;
}

.self-label {
  font-weight: 600;
}
</style>
