<template>
  <div class="money-box">
    <div class="title">
      <div class="line-l" />
      <span>支付方式选择</span>
    </div>
    <template
      v-for="(item, index) in payMethod"
      :key="index"
    >
      <div
        :class="{ 'select-item': true, 'active-select': item.value === currentValue }"
        @click="handleSelect(item.value)"
      >
        <div class="left-box">
          <img
            class="icon-box"
            :src="item.icon"
          >
          <div class="item-info">
            <div class="item-title">
              <span>{{ item.title }}</span>
              <div class="safty-box">
                <img
                  class="icon-box"
                  src="https://img1.rrzuji.cn/uploads/scheme/2412/20/m/A315akBL3N8xbsG0QkKt.png"
                >
                安全
              </div>
            </div>
            <div class="item-desc">
              <span>
                {{ Array.isArray(item.desc) ? item.desc[serverType] : item.desc }}
              </span>
              <span
                v-if="item.warnDesc"
                class="warn-desc"
              >{{ item.warnDesc }}</span>
              <template v-if="item.expand && item.expand[serverType]">
                <span class="expand">{{ item.expand[serverType] }}</span>
                <span>，建议使用企业对公支付</span>
              </template>
            </div>
          </div>
        </div>
        <div class="right-box">
          <CheckCircleFilled
            v-if="item.value === currentValue"
            class="active-icon"
          />
          <CheckCircleOutlined
            v-else
            class="commod-icon"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { CheckCircleFilled, CheckCircleOutlined } from '@ant-design/icons-vue';

import { EPayType, IPageData } from '../../data.d';
import { payMethod } from './config';
const props = defineProps<{
  currentSelectMethod: EPayType;
  pageData: IPageData;
}>();
const emits = defineEmits(['update:currentSelectMethod']);
const currentValue = computed({
  get: () => props.currentSelectMethod,
  set: val => {
    emits('update:currentSelectMethod', val);
  },
});
//server_type 1:企业，2:个人
const serverType = computed(() => Number(props.pageData?.server_type) - 1);

function handleSelect(value: EPayType) {
  currentValue.value = value;
}
</script>

<style scoped lang="less">
.money-box {
  margin: 24px 0;
  padding-bottom: 150px;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;

    .line-l {
      width: 4px;
      height: 16px;
      margin-right: 8px;
      background: #3777ff;
      border-radius: 2px;
    }
  }
}
.active-select {
  border: 1px solid #3777ff !important;
}
.select-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 800px;
  height: 98px;
  margin-bottom: 16px;
  padding: 21px 32px;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 8px;
  .left-box {
    display: flex;
    align-items: center;
    .icon-box {
      width: 32px;
      height: 32px;
      margin-right: 16px;
    }
    .item-info {
      .item-title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        .safty-box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 24px;
          margin-left: 16px;
          padding: 4px 8px;
          color: #52c41a;
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
          background: #f0ffe1;
          border-radius: 4px;
          .icon-box {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
        }
      }
      .item-desc {
        color: rgba(6, 21, 51, 0.45);
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        .expand {
          margin-left: 4px;
          color: #faad14;
        }
        .warn-desc {
          color: #ff4d4f;
        }
      }
    }
  }
  .right-box {
    font-size: 32px;
    .active-icon {
      color: #3777ff;
    }
    .commod-icon {
      color: #c1c4cc;
    }
  }
}
</style>
