import axios from 'axios';

import { GET, POST } from '@/services/api';
import { formatUrl } from '@/services/format-url';

/**
 * @description: 租后协助上传图片
 * @param {any} data
 * @return {*}
 */
export function commonImage(data: any) {
  const url = formatUrl({ url: '/common/image?pathName=activity&oss=1' });
  return axios.post(url, data, {
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * @description: 租后协助分割文件
 * @param {any} data
 * @return {*}
 */
export function sliceFileUpload(data: any) {
  const url = formatUrl({ url: '/common/sliceFileUpload' });
  return axios.post(url, data, {
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * @description: 租后协助上传文件
 * @param {any} data
 * @return {*}
 */
export function commonFile(data: any) {
  const url = formatUrl({ url: '/common/mergeSliceFile' });
  return axios.post(url, data, {
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * @description: 获取租后协助订单详情
 * @param {any} data
 * @return {*}
 */
export function getOrderDetail(data: any) {
  return GET('/order/v2-detail', data);
}

/**
 * @description: 创建租后协助
 * @param {any} data
 * @return {*}
 */
export function createWork(data: any) {
  return POST('/work-order/create', data);
}
