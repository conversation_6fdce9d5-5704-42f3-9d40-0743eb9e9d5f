<template>
  <div class="mini_decoration">
    <div class="page_header">
      <span class="title">小程序店铺装修</span>
      <div class="action">
        <div class="switch">
          <span>店铺装修</span>
          <a-switch
            v-model:checked="decorationInfo.use_status"
            :checked-value="1"
            :un-checked-value="0"
          />
        </div>
        <a-button
          style="margin-left: 24px"
          @click="onCancelSave"
        >
          取消
        </a-button>
        <a-button
          :loading="saveLoading"
          style="margin-left: 8px"
          type="primary"
          @click="onSave"
        >
          保存
        </a-button>
      </div>
    </div>

    <div class="content_wrap">
      <div class="upload_item">
        <div class="upload_header">
          <span class="title">店铺背景图</span>
          <span class="des">建议尺寸：750*400</span>
        </div>
        <upload-item v-model:imgSrc="decorationInfo.shop_back_image" />
      </div>
      <div class="upload_item">
        <div class="upload_header">
          <span class="title">搜索店铺背景图</span>
          <span class="des">建议尺寸：710*320<span
            class="add"
            @click="openAddModal('find_shop_spu_id')"
          ><plus-outlined />&nbsp;添加商品</span></span>
        </div>
        <upload-item
          v-if="decorationInfo.find_shop_image"
          v-model:imgSrc="decorationInfo.find_shop_image"
          :is-active-goods-edit="true"
          @on-del="delShopBackSpu"
          @on-edit="onActivityGoodsEdit('find_shop_spu_id', decorationInfo.find_shop_spu_id ?? '', 0)"
        >
          <template #picTitle>
            <div class="goods_id">
              商品id: {{ decorationInfo.find_shop_spu_id }}
            </div>
          </template>
        </upload-item>
        <template v-else>
          <REmpty />
        </template>
      </div>
      <div class="upload_item">
        <div class="upload_header">
          <span class="title">商品详情商家入口页背景图</span>
          <span class="des">建议尺寸：750*174</span>
        </div>
        <upload-item v-model:imgSrc="decorationInfo.goods_detail_image" />
      </div>
      <div class="upload_item">
        <div class="upload_header">
          <span class="title">活动商品</span>
          <span class="des">建议尺寸：670*360</span>
          <span
            class="add"
            @click="openAddModal('activity_goods_images')"
          ><plus-outlined />&nbsp;添加商品</span>
        </div>
        <div class="active_goods">
          <template v-if="decorationInfo.activity_goods_images.length">
            <upload-item
              v-for="(item, index) in decorationInfo.activity_goods_images"
              :key="item.id"
              v-model:imgSrc="item.src"
              class="active_goods_item"
              :is-active-goods-edit="true"
              @on-del="onActivityGoodsDel(index)"
              @on-edit="onActivityGoodsEdit('activity_goods_images', item.id, index)"
            >
              <template #picTitle>
                <div class="goods_id">
                  商品id: {{ item.id }}
                </div>
              </template>
            </upload-item>
          </template>
          <template v-else>
            <div class="empty_status">
              <REmpty />
            </div>
          </template>
        </div>
      </div>
    </div>

    <add-goods-modal-vue
      v-model:visible="addModal.visible"
      :options="addModal.options"
      :title="addModal.title"
      @confirm="onConfirm"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode, onMounted, reactive, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import addGoodsModalVue from './components/add-goods-modal.vue';
import UploadItem from './components/upload-item.vue';
import { APIGetDecorationInfo, APISetDecorationInfo } from './services';
interface IDecorationInfo {
  shop_back_image: string;
  find_shop_image: string;
  goods_detail_image: string;
  activity_goods_images: { id: number; src: string }[];
  use_status: 0 | 1;
  find_shop_spu_id: number;
}
// 装修开关
const decorationInfo = ref<IDecorationInfo>({
  shop_back_image: '',
  find_shop_image: '',
  goods_detail_image: '',
  activity_goods_images: [],
  use_status: 0,
  find_shop_spu_id: 0,
});
// 编辑和添加
const addModal = reactive({
  visible: false,
  title: '',
  key: '',
  options: {
    idx: -1,
    id: '',
    size: '',
    activeGoodsIds: [],
  },
});

const openAddModal = (key: string) => {
  const uploadItem = {
    find_shop_spu_id: {
      title: '搜索店铺背景图',
      size: '710*320',
      activeGoodsIds: decorationInfo.value.find_shop_spu_id ? [decorationInfo.value.find_shop_spu_id] : [],
    },
    activity_goods_images: {
      title: '活动商品',
      size: '670*360',
      activeGoodsIds: decorationInfo.value.activity_goods_images.map(item => item.id),
    },
  };
  addModal.visible = true;
  addModal.title = uploadItem[key].title;
  addModal.key = key;
  addModal.options.size = uploadItem[key].size;
  addModal.options.activeGoodsIds = uploadItem[key].activeGoodsIds;
};

const onConfirm = ({ id, src }: { id: number; src: string }) => {
  if (addModal.options.id) {
    // 编辑
    const confirmEditFn = {
      find_shop_spu_id: () => {
        decorationInfo.value.find_shop_spu_id = id;
        decorationInfo.value.find_shop_image = src;
      },
      activity_goods_images: () => {
        decorationInfo.value.activity_goods_images[addModal.options.idx] = { id, src };
      },
    };
    confirmEditFn[addModal.key]();
  } else {
    const confirmAddFn = {
      find_shop_spu_id: () => {
        decorationInfo.value.find_shop_spu_id = id;
        decorationInfo.value.find_shop_image = src;
      },
      activity_goods_images: () => {
        decorationInfo.value.activity_goods_images.push({ id: Number(id), src });
      },
    };
    confirmAddFn[addModal.key]();
  }
};

// 删除商品
const onActivityGoodsDel = (index: number) => {
  decorationInfo.value.activity_goods_images.splice(index, 1);
};
// 获取装修数据
const initData = async () => {
  function objToArray(target: { [key: number]: string } | null) {
    if (!target) return [];
    const keys = Object.keys(target);
    return keys.map(key => ({ id: Number(key.slice(3)), src: target[key] }));
  }
  const res = await APIGetDecorationInfo();
  decorationInfo.value = {
    ...res.data,
    use_status: Number(res.data.use_status),
    activity_goods_images: objToArray(res.data.activity_goods_images),
  };
};

const delShopBackSpu = () => {
  decorationInfo.value.find_shop_image = '';
  decorationInfo.value.find_shop_spu_id = 0;
};
onMounted(() => {
  initData();
});

const onCancelSave = () => {
  Modal.confirm({
    icon: createVNode(ExclamationCircleOutlined),
    content: '确定要取消吗?',
    onOk() {
      initData();
    },
  });
};

// 保存校验
const saveValidate = () => {
  const { shop_back_image, goods_detail_image, activity_goods_images } = decorationInfo.value;

  if (!shop_back_image || !goods_detail_image) {
    message.warning('请上传所有图片后再保存');
    return false;
  }

  if (activity_goods_images.length < 3) {
    message.warning('您还有活动商品未上传（最少要上传3个活动商品），请上传后再提交');
    return false;
  }

  return true;
};

// 保存
const saveLoading = ref(false);
const onSave = async () => {
  const validateRes = saveValidate();
  if (!validateRes) return;
  saveLoading.value = true;
  function extractSrc(value: unknown) {
    if (typeof value === 'string') {
      const matchStr = value.match(/^.*(\/uploads.*)/);
      return matchStr ? matchStr[1] : null;
    } else if (Array.isArray(value)) {
      const res = {};
      value.forEach(item => {
        let key = 'id_' + item.id;
        res[key] = extractSrc(item.src);
      });
      return res;
    } else {
      return value;
    }
  }
  let params = {};
  Object.keys(decorationInfo.value).forEach(key => {
    params[key] = extractSrc(decorationInfo.value[key]);
  });
  try {
    await APISetDecorationInfo(params);
    message.success('保存成功');
  } catch (error) {}
  saveLoading.value = false;
};

// 编辑活动商品
const onActivityGoodsEdit = (key: string, id: number | string, index: number) => {
  addModal.options.idx = index;
  addModal.options.id = String(id);
  openAddModal(key);
};
</script>

<style scoped lang="less">
.page_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  padding: 0 24px;
  background-color: #fff;
  border: 1px solid #eeeff3;
  border-right-color: transparent;
  .title {
    height: 28px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }

  .action {
    display: flex;
    .switch {
      display: flex;
      align-items: center;

      span {
        margin-right: 8px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 400;
        font-size: 14px;
        line-height: 23px;
      }
    }
  }
}

.content_wrap {
  height: calc(100vh - 80px);
  margin-left: 9px;
  padding: 37px 0 0 24px;
  overflow-y: auto;
  background-color: #fff;

  .upload_item {
    margin-bottom: 24px;
  }
}

.upload_header {
  margin-bottom: 16px;
  .title {
    position: relative;
    padding: 0 4px 0 12px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 14px;
      background: #3777ff;
      border-radius: 2px;
      content: '';
    }
  }
  .des {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
  }

  .add {
    margin-left: 8px;
    color: #3f7dff;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;

    :deep(svg) {
      width: 16px;
      height: 16px;
    }
  }
}

.goods_id {
  width: 100%;
  height: 37px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 19px;
  text-align: center;
  border: 1px solid #f0f7ff;
  border-top-color: transparent;
  border-radius: 8px 8px 0 0;
}

.active_goods {
  display: flex;
  flex-wrap: wrap;
  .active_goods_item {
    margin-right: 24px;
    margin-bottom: 24px;
  }
}

.empty_status {
  width: 282px;
  height: 178px;
  padding-top: 20px;
  border: 1px solid #f0f7ff;
  border-radius: 8px 8px 0 0;
}
.ant-empty {
  width: 282px;
  height: 185px;
  line-height: 185px;
  border: 2px dashed #f0f7ff;
  &:hover {
    border-color: #3777ff;
  }
}
</style>
