import { ref } from 'vue';

export const tbCol = ref([
  {
    title: '类目或型号',
    dataIndex: 'type',
    key: 'type',
    customCell: (_: unknown, index: number): unknown => {
      return index === 0 ? { rowSpan: 14 } : { rowSpan: 0 };
    },
    width: 165,
    responsive: ['md'],
  },
  {
    title: '押金',
    dataIndex: 'deposit',
    key: 'deposit',
    customCell: (_: unknown, index: number) => {
      return (index + 1) % 2 === 0 ? { rowSpan: 0 } : { rowSpan: 2 };
    },
    width: 160,
  },
  {
    title: '成新',
    dataIndex: 'condition',
    key: 'condition',
    width: 30,
  },
]);
const TB_COL_BASE_LENGTH = tbCol.value.length;
export function useConfig() {
  const handleTbCol = (data: { id: number; name: string }[]) => {
    const tbOptions: any = data.map(item => {
      return {
        title: item.name,
        dataIndex: item.id,
        key: item.id,
        width: 45,
        renderType: 'inputNumber',
      };
    });
    tbCol.value.splice(TB_COL_BASE_LENGTH, tbCol.value.length - TB_COL_BASE_LENGTH, ...tbOptions);
  };
  return {
    handleTbCol,
  };
}
