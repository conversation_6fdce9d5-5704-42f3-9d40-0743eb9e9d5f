import { GET } from '@/services/api';

import type { IAPIResult } from '@/utils/hooks';
import type { IFormState } from './components/button-add-address';

export interface IAddressDataSource {
  id: number;
  address: string;
  name: string;
  phone: string;
}

export function getReturnAddress(): Promise<IAPIResult<IAddressDataSource[]>> {
  // return GET('', params);
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        status: 0,
        data: [
          {
            id: 1,
            address: '上海市浦东新区',
            name: '张三',
            phone: '13888888888',
          },
          {
            id: 2,
            address: '上海市浦东新区',
            name: '张三1',
            phone: '13888888888',
          },
        ],
        message: '成功',
        error: '',
      });
    }, 1000);
  });
}
export function postConfirmReturn(): Promise<IAPIResult<any>> {
  // return GET('', params);
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        status: 0,
        data: {},
        message: '成功',
        error: '',
      });
    }, 1000);
  });
}

export function postConfirmReceive(id: string): Promise<IAPIResult<any>> {
  // return POST('', { id});
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        status: 0,
        data: {
          id,
        },
        message: '成功',
        error: '',
      });
    }, 1000);
  });
}

export function postConfirmDelete(id: string): Promise<IAPIResult<any>> {
  // return POST('', { id});
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        status: 0,
        data: {
          id,
        },
        message: '成功',
        error: '',
      });
    }, 1000);
  });
}

export function postAddAddress({ address, name, phone }: IFormState): Promise<IAPIResult<any>> {
  // return POST('', { id});
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        status: 0,
        data: {
          address,
          name,
          phone,
        },
        message: '成功',
        error: '',
      });
    }, 1000);
  });
}

export function getHasDraft(params: { unique_code: string; device_type: number; batch_no: string }) {
  return GET('/tbl-warehouse/has-draft', params);
}
