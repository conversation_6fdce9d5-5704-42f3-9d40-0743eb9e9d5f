<template>
  <div
    ref="createCreative"
    class="page"
  >
    <div class="page-header p-wrap">
      <div class="flex-wrap flex-y-center">
        <ArrowLeftOutlined
          class="back-icon"
          @click="$router.back()"
        />
        <span class="text">成员权限有效期</span>
      </div>
      <a-button
        type="primary"
        @click="addAuthPerious"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        添加权限有效期
      </a-button>
    </div>
    <div class="tip-box">
      <div class="tip">
        <div style="display: flex">
          <info-circle-filled style="width: 35px; height: 20px; padding-top: 4px; color: #3777ff" />
          <div>
            1."分配权限" 时设置的有效期为所有权限的统一有效期，本界面可为权限单独设置其他有效期（优先级更高）
            <br>
            2.单次只能为一个用户设置权限有限期，且设置的权限必须为分配过的权限
          </div>
        </div>
      </div>
    </div>
    <div class="search-wrap">
      <a-form
        layout="inline"
        :model="searchFormState"
      >
        <a-form-item label="权限名">
          <a-tree-select
            key="key"
            v-model:value="searchFormState.auth_name"
            allow-clear
            :field-names="searchFieldNames"
            :filter-tree-node="fillterFUN"
            :height="233"
            :max-tag-count="10"
            multiple
            :show-checked-strategy="SHOW_CHILD"
            show-search
            style="width: 200px"
            tree-checkable
            :tree-data="treeData"
          >
            <template #title="{ value }">
              <span>{{ value }}</span>
            </template>
          </a-tree-select>
        </a-form-item>
        <a-form-item label="用户名">
          <a-select
            v-model:value="searchFormState.user_id_str"
            allow-clear
            :filter-option="filterOption"
            :max-tag-count="10"
            mode="multiple"
            :options="userOptions"
            placeholder="请选择用户"
            show-search
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="search"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleSet"
          >
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </div>
    <div class="table-wrap">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :expand-column-width="20"
        :expand-icon-as-cell="true"
        :pagination="page"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'user_name_list'">
            <div v-if="record.user_name_list?.length <= 4">
              {{ record.user_name_list?.filter((item: any) => !!item).join('\n') }}
            </div>
            <div v-else-if="!record.user_expand">
              {{ record.user_name_list?.filter((item: any) => !!item).slice(0,4).join('\n') }}
              <a-button
                type="link"
                @click="record.user_expand = !record.user_expand"
              >
                展开
              </a-button>
            </div>
            <div v-else>
              {{ record.user_name_list?.filter((item: any) => !!item).join('\n') }}
              <a-button
                type="link"
                @click="record.user_expand = !record.user_expand"
              >
                收起
              </a-button>
            </div>
          </template>
          <template v-if="column.key === 'rule_name_list2'">
            <div v-if="record.rule_name_list2?.length <= 4">
              {{ record.rule_name_list2?.map((item: any) => item.superior_name).join('\n') }}
            </div>
            <div v-else-if="!record.rule_expand">
              {{ record.rule_name_list2?.map((item: any) => item.superior_name).slice(0,4).join('\n') }}

              <a-button
                type="link"
                @click="record.rule_expand = !record.rule_expand"
              >
                展开<double-right-outlined style="margin-left: 6px; padding-top: 2px; transform: rotate(90deg)" />
              </a-button>
            </div>
            <div v-else>
              {{ record.rule_name_list2?.map((item: any) => item.superior_name).join('\n') }}

              <a-button
                type="link"
                @click="record.rule_expand = !record.rule_expand"
              >
                收起<double-left-outlined style="margin-left: 8px; padding-top: 2px; transform: rotate(90deg)" />
              </a-button>
            </div>
          </template>
          <template v-if="column.key === 'status'">
            <div :class="record.status === '0' ? 'close' : 'open'" />
            {{ record.status === '0' ? '停用' : '开启' }}
          </template>
          <template
            v-if="column.key === 'operation'"
            style="text-align: left"
          >
            <a-space :size="8">
              <a-button
                class="operation-btn"
                type="link"
                @click="editAuthPerious(record)"
              >
                修改
              </a-button>
              <a-button
                class="operation-btn"
                type="link"
                @click="handleStatus(record.id, record.status === '0' ? '开启' : '停用')"
              >
                {{ record.status === '0' ? '开启' : '停用' }}
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 分配权限弹框 -->
    <a-modal
      v-model:visible="treeModal.treeModalVisible"
      style="width: 550px"
      :title="addOrEdit === 'add' ? '添加权限有效期' : '编辑权限有效期'"
      @cancel="close"
      @ok="addEditForm"
    >
      <div style="position: relative; max-height: 600px; overflow-y: scroll">
        <a-form
          ref="formRef"
          :label-col="{ span: 24 }"
          :model="formState"
          name="basic"
          :rules="rules"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item
            ref="user_id_str"
            class="form-label-bottom"
            label="选择用户"
            name="user_id_str"
          >
            <a-select
              v-if="addOrEdit == 'add'"
              v-model:value="formState.user_id_str"
              :filter-option="filterOption"
              :max-tag-count="10"
              mode="multiple"
              placeholder="请选择用户"
              show-search
              style="width: 150%"
              @change="handleChange"
            >
              <a-select-option
                v-for="item in userOptions"
                :key="item.value"
                :disabled="
                  formState.user_id_str.length >= 1 && formState.user_id_str.findIndex(o => o === item.value) === -1
                "
                :label="item.label"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-input
              v-else
              disabled
              style="width: 150%"
              :value="
                userOptions.find(item => {
                  return item.value == formState.user_id_str[0];
                })?.label
              "
            />
          </a-form-item>
          <a-form-item
            ref="auth_url_str"
            label="选择权限"
            name="auth_url_str"
          >
            <a-tree-select
              v-model:value="formState.auth_url_str"
              :default-value="formState.auth_url_str"
              :field-names="fieldNames"
              :height="233"
              :max-tag-count="10"
              placeholder="请选择权限"
              :show-checked-strategy="SHOW_CHILD"
              style="width: 150%"
              tree-checkable
              :tree-data="formTreeData"
              tree-node-filter-prop="title"
            >
              <template #title="{ value }">
                <span
                  v-if="value === '0-0-1-0'"
                  style="color: #1890ff"
                >{{ value }}</span>
                <template v-else>
                  {{ value }}
                </template>
              </template>
            </a-tree-select>
          </a-form-item>
          <a-form-item
            ref="validityDate"
            label="有效期"
            name="validityDate"
          >
            <a-range-picker
              v-model:value="formState.validityDate"
              :allow-clear="false"
              :get-popup-container="createMod"
              :placeholder="['开始日期', '结束日期']"
              :ranges="preset"
              :show-time="{ format: 'HH:mm:ss' }"
              style="width: 150%"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button
          key="back"
          @click="close"
        >
          取消
        </a-button>
        <a-button
          key="submit"
          :loading="loading"
          type="primary"
          @click="addEditForm"
        >
          确定
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { message, Modal, TreeProps, TreeSelect } from 'ant-design-vue';
import {
  ArrowLeftOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  InfoCircleFilled,
  PlusOutlined,
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { deepClone } from '@/utils/base';

import { columns } from './config';
import {
  addAuthTime,
  editAuthTime,
  getRouteForAuth,
  getUserCommonalityAuth,
  getUserNameOption,
  getValidityList,
  switchStatus,
} from './service';

const SHOW_CHILD = TreeSelect.SHOW_CHILD;

// 搜索栏
const searchFormState = ref<any>({
  auth_name: [],
  user_id_str: [],
});
// 获取权限(全部)

const treeData = ref<TreeProps['treeData']>([]);
getRouteForAuth().then(res => {
  treeData.value = res.data.auth;
});

const fillterFUN = (searchVal: string, treeNode: unknown) => {
  return treeNode.value.includes(searchVal);
};
// 获取用户名字下拉框
const userOptions = ref<any>([]);
getUserNameOption().then(res => {
  userOptions.value = res.data.map((item: any) => {
    return { value: item.id, label: item.username };
  });
});

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const dataSource = ref<any>([]);
const listLoading = ref<boolean>(false);
const page = ref<any>({
  current: 1,
  pageSize: 15,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '15', '20', '30'],
});
const findNodeByValue = (treeData, key) => {
  // 遍历当前层级的所有节点
  for (let i = 0; i < treeData.length; i++) {
    let node = treeData[i];
    // 如果当前节点的 ID 匹配目标节点的 ID，则返回当前节点
    if (node.key === key) {
      return node;
    }
    // 如果当前节点有子节点，则递归调用当前函数继续查找子节点
    if (node.children && node.children.length > 0) {
      let foundNode = findNodeByValue(node.children, key);
      // 如果在子节点中找到了目标节点，则返回找到的节点
      if (foundNode) {
        return foundNode;
      }
    }
  }

  // 如果遍历完所有节点仍未找到目标节点，则返回 null
  return null;
};

const search = () => {
  let auth_name = [];
  searchFormState.value.auth_name.forEach(item => {
    const title = findNodeByValue(treeData.value, item)?.value;
    if (!!title) {
      auth_name.push(title);
    }
  });

  const params = {
    auth_name: auth_name.join(','),
    user_id_str: searchFormState.value.user_id_str.join(','),
    page: page.value.current,
    page_size: page.value.pageSize,
  };
  listLoading.value = true;
  getValidityList(params)
    .then(res => {
      listLoading.value = false;
      dataSource.value = res.data.list?.map(item => {
        item.rule_name_list2 = item.rule_name_list2?.map(i => {
          i.groupChild.forEach((ele, index) => {
            if (index === 0) {
              i.superior_name = i.superior_name + '-' + ele;
            } else if (index === i.groupChild.length - 1) {
              i.superior_name = i.superior_name + '、' + ele + ';';
            } else {
              i.superior_name = i.superior_name + '、' + ele;
            }
          });
          return i;
        });
        return {
          ...item,
          user_expand: false,
          rule_expand: false,
        };
      });
      page.value.total = res.data.count;
    })
    .finally(() => {
      listLoading.value = false;
    });
};
const handleSet = () => {
  page.value = {
    current: 1,
    pageSize: 15,
    total: 0,
    showSizeChanger: true,
  };
  searchFormState.value.auth_name = [];
  searchFormState.value.user_id_str = [];
  search();
};

search();

const tableChange = (pagenation: any) => {
  page.value.current = pagenation.current;
  page.value.pageSize = pagenation.pageSize;
  search();
};

// 切换状态
const handleStatus = (id: number | string, text: string) => {
  Modal.confirm({
    title: text === '开启' ? '确定开启该项设置么?' : '确定停用该项设置么',
    okText: '确认',
    cancelText: '取消',
    onOk: resolve => {
      switchStatus({
        id: id,
        statusVal: text === '开启' ? '1' : '0',
      }).then(res => {
        if (res.status === 0) {
          message.success('修改成功');
          search();
          return resolve();
        } else {
          message.error('修改失败');
        }
      });
    },
  });
};
const searchFieldNames = {
  label: 'value',
  value: 'key',
  children: 'children',
};
const fieldNames = {
  label: 'value',
  value: 'key',
  children: 'children',
};

// 添加
let addOrEdit = ref<string>('add');
const addAuthPerious = () => {
  addOrEdit.value = 'add';
  treeModal.treeModalVisible = true;
};

const formState = ref<any>({
  auth_url_str: [],
  user_id_str: [],
  validityDate: [dayjs().format('YYYY-MM-DD HH:mm:ss'), '2099-01-01 23:59:59'],
});

// 编辑获取树形回显数据
const editAuthPerious = async (record: any) => {
  handleChange(record.user_id_list);
  addOrEdit.value = 'edit';
  treeModal.treeModalVisible = true;
  formState.value.auth_url_str = deepClone(record.rule_url_list);
  formState.value.user_id_str = deepClone(record.user_id_list);
  formState.value.validityDate = [deepClone(record.validity_start), deepClone(record.validity_end)];
  formState.value.id = record.id;
};

const treeModal = reactive<{
  treeModalVisible: boolean;
  id: undefined | string | number;
  auth_rule: string[];
}>({
  treeModalVisible: false,
  id: undefined,
  auth_rule: [],
});

// DOM元素
const formRef = ref();

// 添加、编辑校验
const rules = {
  user_id_str: [{ required: true, message: '请选择用户', trigger: 'change' }],
  auth_url_str: [{ required: true, message: '请选择权限', trigger: 'change' }],
  validityDate: [{ required: true, message: '请选择有效期', trigger: 'change' }],
};
const loading = ref<boolean>(false);
const addEditForm = () => {
  formRef.value.validate().then(() => {
    loading.value = true;
    const start =
      typeof formState.value.validityDate[0] === 'string'
        ? formState.value.validityDate[0]
        : formState.value.validityDate[0].format('YYYY-MM-DD HH:mm:ss');
    const end =
      typeof formState.value.validityDate[1] === 'string'
        ? formState.value.validityDate[1]
        : formState.value.validityDate[1].format('YYYY-MM-DD HH:mm:ss');
    if (addOrEdit.value === 'add') {
      const params = {
        time_label: '',
        validity_start: start,
        validity_end: end,
        auth_url_str: formState.value.auth_url_str?.join(','),
        user_id_str: formState.value.user_id_str?.join(','),
      };
      addAuthTime(params)
        .then(res => {
          loading.value = false;
          close();
          message.success(res.message);
          search();
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      const params = {
        time_label: '',
        validity_start: start,
        validity_end: end,
        auth_url_str: formState.value.auth_url_str?.join(','),
        user_id_str: formState.value.user_id_str?.join(','),
        id: formState.value.id,
      };
      editAuthTime(params)
        .then(res => {
          loading.value = false;
          close();
          message.success(res.message);
          search();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};
const formTreeData = ref<TreeProps['treeData']>([]);
const filterRootNodes: any = (data, arr) => {
  if (!data || data.length === 0) {
    // 判断数据是否存在并且不为空，如果是，则直接返回空数组
    return [];
  }
  const filteredData = []; // 定义过滤后的数据数组
  for (let i = 0; i < data.length; i++) {
    // 遍历每个节点
    const node = data[i];
    if (arr.includes(node.key)) {
      filteredData.push(node);
    }
    if (node.children && node.children.length > 0) {
      // 如果当前节点存在子节点，则递归遍历子节点
      const filteredChildren = filterRootNodes(node.children, arr); // 对子节点进行递归过滤
      if (filteredChildren.length > 0) {
        // 如果过滤后的子节点不为空，则将该节点加入到过滤后的数据数组中，并将过滤后的子节点作为其 children 属性的值
        filteredData.push({
          value: node.value,
          key: node.key,
          children: filteredChildren,
        });
      }
    }
  }
  return filteredData;
};
const handleChange = (selection: any) => {
  formState.value.auth_url_str = [];
  if (selection.length === 0) {
    formTreeData.value = [];
    return;
  }
  getUserCommonalityAuth({ user_id_str: selection.join(',') })
    .then(res => {
      if (res.data.length !== 0) {
        const arr = filterRootNodes(treeData.value, res.data);
        formTreeData.value = arr;
      } else {
        message.error('该用户与前面用户没有权限交集');
        formState.value.user_id_str.pop();
      }
    })
    .catch(() => {
      formState.value.user_id_str.pop();
    });
};
// 日期选择器

const preset = {
  永久: [dayjs(), dayjs('2099-01-01 23:59:59')],
  一个月: [dayjs(), dayjs().add(1, 'month')],
  三个月: [dayjs(), dayjs().add(3, 'months')],
  六个月: [dayjs(), dayjs().add(6, 'months')],
  一年: [dayjs(), dayjs().add(1, 'year')],
  两年: [dayjs(), dayjs().add(2, 'years')],
  三年: [dayjs(), dayjs().add(3, 'years')],
};

// 关闭新增编辑弹窗
const close = () => {
  formRef.value.resetFields();
  formState.value = {
    auth_url_str: [],
    user_id_str: [],
    validityDate: [dayjs().format('YYYY-MM-DD HH:mm:ss'), '2099-01-01 23:59:59'],
  };
  formTreeData.value = [];
  treeModal.treeModalVisible = false;
};
const createCreative = ref(null);
const createMod = () => {
  return createCreative.value;
};
</script>

<style scoped lang="less">
.page {
  min-height: 100vh;
  background-color: #fff;
}
.p-wrap {
  padding: 24px;
  background: #fff;
  .back-icon {
    margin-right: 20px;
    cursor: pointer;
  }
}
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}

.search-wrap {
  display: flex;
  flex-wrap: wrap;
  padding: 0 24px;
  background-color: #fff;
  :deep(.ant-form-item) {
    margin-bottom: 24px;
  }
}
.table-wrap {
  padding: 0 24px 24px;
  background-color: #fff;
}

.no-tree-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.tip-box {
  padding: 0 24px;
  .tip {
    box-sizing: border-box;
    width: 100%;
    height: 70px;
    margin-bottom: 24px;
    padding: 12px 0;
    line-height: 26px;
    background-color: #e6f7ff;
    border: 1px solid #bae7ff;
    border-radius: 0;
  }
}
.close {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #ff1e10;
  border-radius: 100%;
}
.open {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #5ac421;
  border-radius: 100%;
}
:deep(.ant-form-vertical .ant-form-item-label, .ant-col-24.ant-form-item-label, .ant-col-xl-24.ant-form-item-label) {
  padding: 0 !important;
}
:deep(.ant-table-tbody > tr > td) {
  white-space: pre-wrap;
}
:deep(.ant-picker-range-wrapper) {
  display: block;
}
.operation-btn {
  padding: 0;
}
</style>
