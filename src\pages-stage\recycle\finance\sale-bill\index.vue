<template>
  <layout-admin-page
    :navs="['趣回收', '财务管理', '销售账单']"
    title="销售账单"
  >
    <template #extra>
      <!-- 导出 -->
      <a-button
        type="primary"
        @click="exportAction"
      >
        <template #icon>
          <export-outlined />
        </template>
        导出查询结果
      </a-button>
    </template>
    <div class="container">
      <div class="search-bar pb-0">
        <!-- 搜索 -->
        <search-box
          v-model:value="searchParams"
          :search-form-group="searchFormGroup"
          @fetch-table="fetchTable"
          @reset-table="resetTable"
        />
      </div>
      <a-divider :style="{ margin: '15px 0 0 0' }" />
      <div class="tabs">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane
            v-for="item in searchOptions.orderStatusOptions"
            :key="item.value"
            :tab="item.label"
          />
        </a-tabs>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table table-list"
          :columns="saleBillColumns"
          :data-source="list"
          :loading="listLoading"
          :pagination="false"
          row-key="rsp_order_id"
          :row-selection="rowSelection"
          :scroll="{ x: '100%' }"
          :sticky="true"
          @change="tableChange(page)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'remark'">
              <!-- 备注 -->
              <remark
                :order-id="record.rsp_order_id"
                :order-remarks="orderRemarks"
                type="pay.recycle"
              />
            </template>
            <template v-if="column.key === 'rsp_order_id'">
              <span
                class="link"
                @click="toBillDetail(record.rsp_order_id)"
              >
                {{ record.rsp_order_id }}
              </span>
            </template>
            <template v-if="column.key === 'rs_order_id'">
              <span
                class="link"
                @click="toOrerList(record.rs_order_id)"
              >
                {{ record.rs_order_id }}
              </span>
            </template>
          </template>
        </a-table>
        <div class="table-pagination">
          <div class="all-checked">
            <a-checkbox
              v-model:checked="checkAllState.checkAll"
              :indeterminate="checkAllState.indeterminate"
              @change="onCheckAllChange"
            >
              全选
            </a-checkbox>
            <div class="check-num">
              已选 <span class="num">{{ checkAllState.checkedList.length }}</span> 条
            </div>
            <a-button
              :disabled="checkAllState.checkedList.length === 0"
              style="margin-left: 12px"
              type="primary"
              @click="handleSaleBill"
            >
              收款
            </a-button>
          </div>
          <a-pagination
            v-model:current="page.current"
            :default-page-size="10"
            :page-size="page.pageSize"
            :page-size-options="[10, 30]"
            show-quick-jumper
            :total="page.total"
            @change="tableChange(page)"
          />
        </div>
      </div>
      <!-- 账单详情 -->
      <bill-details
        ref="billDetailsRef"
        :rsp-order-id="currentRspId"
      />
    </div>
  </layout-admin-page>
</template>

<script setup lang="ts">
import { AxiosResponse } from 'axios';
import { ref, onMounted, reactive, ComputedRef } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { saleBillColumns, searchOptions, searchFormGroup } from './config';
import SearchBox from '../../order/sale-order/components/search-box/index.vue';
import { ISearchParams, ISaleBillList } from './data';
import { asyncEduce } from '@/utils/educe';
import { isNotEmptyArray } from '../../common/util';
import { getTopWindowUrl } from '@/utils/url';
import { isEmpty } from 'lodash-es';
import { useTable } from '@/hook/component/use-table';
import { ExportOutlined } from '@ant-design/icons-vue';
import useTab from './composables/use-tab';
import BillDetails from './components/bill-details/index.vue';
import useRemark from './composables/use-remark';
import useCheckAll from './composables/use-check-all';
import Remark from './components/remark/index.vue';

const route = useRoute();
const router = useRouter();

// 账单详情组件
const billDetailsRef = ref<InstanceType<typeof BillDetails> | null>(null);

// 搜索参数
const searchParams = reactive<Partial<ISearchParams>>({
  category: [],
  custom_type: [],
  source_type: [],
  create_time: [],
});

const { loadRemark, orderRemarks } = useRemark();
/*---------------- table hooks  --------------------*/
const { list, listLoading, page, getTableList, tableChange } = useTable<ISaleBillList, Partial<ISearchParams>>({
  url: '/super/recycle/sale-order-pay/list',
  searchForm: searchParams,
  totalKey: 'data.pageInfo.count',
  formatHandle: (res: AxiosResponse<{ list: ISaleBillList[] }>) => {
    res.data.list && loadRemark(res.data.list);
    return res.data.list;
  },
  formatSearchValue: () => {
    return formatParams();
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
    pageSize: 10,
  },
});
const { checkAllState, onCheckAllChange, rowSelection } = useCheckAll(list);

const formatParams = () => {
  const data = {
    start_at: '',
    end_at: '',
    category_brand_model: '',
  };

  //  类目
  if (isNotEmptyArray(searchParams.category)) {
    data.category_brand_model = (searchParams.category as number[]).join('_');
  }

  // 创建时间
  if (isNotEmptyArray(searchParams.create_time)) {
    data.start_at = searchParams.create_time?.[0] ?? '';
    data.end_at = searchParams.create_time?.[1] ?? '';
  }

  return { ...data, ...searchParams };
};

// 导出
const exportAction = async () => {
  if (Object.values(searchParams).every(item => isEmpty(item)) && isEmpty(searchParams.create_time)) {
    return message.warn('请选择筛选条件或时间');
  }
  await asyncEduce('/super/recycle/sale-order-pay/list-export', formatParams(), route.query.origin as string);
};

// 跳账单列表
const toOrerList = (id: string) => {
  window.parent.postMessage(
    {
      action: 'blank',
      href: `/super/recycle/sale-order/index?rsp_id=${id}`,
    },
    '*',
  );
};

// tab keys
const { activeKey, hasLoad } = useTab({
  fetchTable: getTableList,
  searchParams: searchParams,
});

// 查询
const fetchTable = async (bindValue: ComputedRef<Partial<ISearchParams>>) => {
  try {
    hasLoad.value = true;
    // 联动更改订单状态
    activeKey.value = bindValue.value.order_pay_status as string;
    await getTableList();
  } finally {
    hasLoad.value = false;
  }
};

// 重置
const resetTable = async () => {
  try {
    hasLoad.value = true;
    // 联动更改订单状态
    activeKey.value = '';
    await getTableList();
  } finally {
    hasLoad.value = false;
  }
};

// 账单id
const currentRspId = ref('');

// 账单详情
const toBillDetail = (id: string) => {
  currentRspId.value = id;
  billDetailsRef.value?.open();
};
const handleSaleBill = () => {
  let isSoleServerId: unknown = undefined;
  checkAllState.checkedList.forEach((item: any) => {
    if (item.order_pay_status !== '1') {
      throw Modal.confirm({
        title: '提示',
        content: '所选账单需为待支付账单，请检查后重新收款！',
        okText: '我知道了',
        cancelText: '取消',
      });
    }
    if (!isSoleServerId) {
      isSoleServerId = item.server_id;
    }
    if (isSoleServerId !== item.server_id) {
      throw Modal.confirm({
        title: '提示',
        content: '所选账单需为同一客户，请检查后重新收款！',
        okText: '我知道了',
        cancelText: '取消',
      });
    }
  });
  const ids = checkAllState.checkedList.map(item => item.rsp_order_id);
  console.log(checkAllState.checkedList, ids);
  const params = JSON.stringify({
    ids,
    serverId: isSoleServerId,
  });

  window.localStorage.setItem('sale-bill-item', params);
  router.push({ path: '/recycle/finance/collect-bill-detail', query: { type: 'cllect' } });
};
onMounted(async () => {
  const { params } = await getTopWindowUrl({ getParams: true });
  if (params?.rsp_id) {
    searchParams.rsp_order_id = params?.rsp_id;
  }
  getTableList();
});
</script>

<style lang="less" scoped>
@import url('../../common/base.less');
.table-list {
  font-weight: 500;
}
.search-bar.pb-0 {
  padding-bottom: 0;
}
.tabs {
  padding-left: 25px;
  font-weight: 500;
  font-size: 14px;
  :deep(.ant-tabs-nav) {
    margin-bottom: 24px;
  }
}
.link {
  padding: 4px 0;
  color: #3777ff;
}
.table-pagination {
  position: fixed;
  right: 16px;
  bottom: 0;
  left: 16px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: #fff;
}
.all-checked {
  display: flex;
  align-items: center;
}
.check-num {
  margin-left: 28px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
}
.num {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
}
</style>
