import { ref } from 'vue';

import isSufficientWarehouse from '@/utils/is-sufficient-warehouse';

import {
  getPurchaseInfo,
  getReplacementProposal,
  getStoreSendWarehouseInfo,
  issueStock,
  subwarehouseService,
} from '../../../../services'
import { generatePriceList } from './use-exchange-advice';

export default function useAutoDelivery() {
  const isAutoDelivery = ref(false); //是否自动下发

  const autoSendResult = ref({}); // 自动下发结果

  /**
   * 分仓是否有库存
   * @param params 分仓参数，注意送检的is_checked会更改
   * @param is_checked 是否送检
   * @returns 租赁非租赁库存状态,仓库类型，ID
   */
  async function warehouseInventoryResult({ params, is_checked }:{params: any, is_checked: boolean}) {
    const [{ data: regulatoryDeviceInformation }, { data: nonRegulatoryDeviceInformation }] = await Promise.all([
      // 监管机
      subwarehouseService({ ...params, is_checked, is_lock_machine: true }),
      // 非监管机
      subwarehouseService({ ...params, is_checked, is_lock_machine: false }),
    ]);
    const {
      warehouse_type: regulatoryWarehouseType,
      warehouse_id: rentalWarehouseId,
      sku_id: rentalSkuId,
      is_checked: rentalIsChecked,
      is_check_white_list: rentalIsWhite,
    } = regulatoryDeviceInformation;
    const {
      warehouse_type: nonRegulatoryWarehouseType,
      warehouse_id: noRentalWarehouseId,
      sku_id: noRentalSkuId,
      is_checked: noRentalIsChecked,
      is_check_white_list: noRentalIsWhite,
    } = nonRegulatoryDeviceInformation;
      // 监管机库存状态
    const rentalStatus = isSufficientWarehouse({
      warehouse_type: regulatoryWarehouseType,
      regulation: true,
    });
    // 非监管机库存状态
    const noRentalStatus = isSufficientWarehouse({
      warehouse_type: nonRegulatoryWarehouseType,
      regulation: false,
    });
    return {
      rental: {
        status: rentalStatus,
        warehouse_type: regulatoryWarehouseType,
        warehouse_id: rentalWarehouseId,
        sku_id: rentalSkuId,
        is_checked: rentalIsChecked,
        is_white: rentalIsWhite,
      },
      noRental: {
        status: noRentalStatus,
        warehouse_type: nonRegulatoryWarehouseType,
        warehouse_id: noRentalWarehouseId,
        sku_id: noRentalSkuId,
        is_checked: noRentalIsChecked,
        is_white: noRentalIsWhite,
      },
    }
  }

/**
 * 换货建议-补贴升级
 * @param upgradeParams 补贴升级参数建议，租赁、非租赁
 * @returns 租赁库存、非租赁换货列表
 */
  async function getUpgradeService(upgradeParams) {
    const [{ data: rentalUpgradeInventory },{ data:noRentalUpgradeInventory }] = await Promise.all([
      getReplacementProposal({ ...upgradeParams, is_lock_machine: true}),
      getReplacementProposal({ ...upgradeParams, is_lock_machine: false}),
    ]);

    const rentalUpgradeData = generatePriceList(rentalUpgradeInventory, true);
    const noRentalUpgradeData = generatePriceList(noRentalUpgradeInventory, false);
    return {
      rentalUpgradeData,
      noRentalUpgradeData,
    }
  }

  function filterMoneyMark(price: string){
    const amountString = price.replace('￥', '');
    const amount = Number(amountString);
    return amount;
  }

  /**
   * 获取商家项目余额 - 非送检才要查余额-与补贴发货价格对比
   * @param rental  非送检租赁
   * @param noRental 非送检非租赁
   * @param orderId 订单id
   * @param ori_sku_id 原始skuid
   * @returns 租赁非租赁账户余额
   */
  async function getMerchantPrice(rental, noRental, orderId, ori_sku_id) {
    const {
      warehouse_id: lock_warehouse_id,
      warehouse_type: lock_warehouse_type,
      sku_id: lock_sku_id,
     } = rental;
    const {
      warehouse_id: unlock_warehouse_id,
      warehouse_type: unlock_warehouse_type,
      sku_id: unlock_sku_id,
    } = noRental;

    const need_condition_skuIds = [lock_sku_id, unlock_sku_id].filter(sku_id => sku_id != ori_sku_id).join(',');
    const params = {
      order_id: orderId,
      is_activity_only: 0,
      is_checked: 0,
      lock_sku_id,
      lock_warehouse_id,
      lock_warehouse_type,
      unlock_sku_id,
      unlock_warehouse_id,
      unlock_warehouse_type,
      need_condition_skuIds,
    };
    const { data } = await getPurchaseInfo(params);
    return data?.accountAmount || {};
  }

  /**
   * 下发
   * @param config 当前参数
   * @param addParams 补贴升级额外参数
   */
  async function deliveryDeviceService(config: any, addParams:any = {}) {
    const { orderId, sku_id, is_checked, is_support, warehouseInfo: { warehouse_type,warehouse_id} } = config;
    const params = {
      type: 'mark',
      markType: 299,
      value: 1,
      is_lock: 2,
      is_grayscale: true, // 标记是灰度版本的下发仓库
      c_type: 1, // 标记是灰度版本的下发仓库的正常查询
      isServer: 1, //商家
      orderId,
      sku_id,
      is_support: is_support ? 1 : 0, //租赁非租赁
      activate_only: 0,
      is_checked: is_checked ? 1: 0,
      warehouse_type, //从分仓来，租赁非租赁
      warehouse_id,
      click_auto_send: 1, //自动下发标记
    }
    const { auto_send_result } = await issueStock({ ...params, ...addParams });
    autoSendResult.value = auto_send_result || {};
    isAutoDelivery.value = true;
  }

  /**
   * 主流程：有skuid且可平台代发
   *  1.有送检非代发权限，查租赁非租赁送检库存，满足则下发，否则打开发货弹窗。
   *  2.
   *    2.1 查看租赁非租赁分仓库存，返回的是要非送检的库存状态，满足则打开发货弹窗，否则进行下一步。
   *    2.2 有送检权限-查看租赁非租赁送检库存，满足则下发，否则下一步判断。没有送检权限则进行下一步
   *    2.3 有升级发货权限，满足则进行下一步，否则打开发货弹窗。
   *    2.4 查看非送检，租赁非租赁升级发货建议，满足则进行下一步，否则打开去发货弹窗。
   *    2.5 有发货建议，查询租赁非租赁项目余额，去对比租赁非租赁发货建议，满足则下发。
   */
  async function getCheckAuth(config: any) {
    const { orderId, server_id } = config;
    const { data: alertData } = await getStoreSendWarehouseInfo(orderId);
    const {
      sku_id,
      is_checked,
      storage_memory_up_auth,
      item_num,
      is_new,
      replace_auth,
      is_can_distribute,
      can_send_self,
    } = alertData;
    const hasReplaceAuth = is_new || replace_auth || is_checked; //下发类型
    if (!sku_id || !is_can_distribute || !orderId || !hasReplaceAuth || can_send_self) {
      isAutoDelivery.value = false;
      return;
    }

    const params = {
      sku_id,
      item_num: Number(item_num),
      order_id: orderId,
      up_stock: true,
      is_activate_only: false,
      server_id: Number(server_id),
      created_port: 2,
    };

    //送检且非代发直接查送检库存,跳出函数
    // if (!!is_checked && !replace_auth) {
    //   const { rental, noRental } = await warehouseInventoryResult({ params, is_checked: true });
    //   //看是否有实物库存 is_white走兜底 不自动下发
    //   const rentalStatus = !rental.is_white && rental.status;
    //   const noRentalStatus = !noRental.is_white && noRental.status;
    //   if (rentalStatus || noRentalStatus) {
    //     const is_support = !!rentalStatus;
    //     await deliveryDeviceService({
    //       orderId,
    //       sku_id,
    //       is_checked,
    //       is_support,
    //       warehouseInfo: is_support ? rental : noRental,
    //      });
    //     //下发成功 准备自动下发弹窗
    //     return;
    //   }
    //   isAutoDelivery.value = false;
    //   return;
    // }

    // 轮询租赁/非租赁分仓结果、是否有货,库存充足(!!!非送检库存)，展示去发货弹窗，无货走下一步送检
    const { rental, noRental } = await warehouseInventoryResult({ params, is_checked: false });
    if (rental.status || noRental.status) {
      isAutoDelivery.value = false;
      return;
    }
    console.log('是否走到了这一步---过掉分仓')

    //轮询送检库存 1.先看是否有送检权限：送检 && 非特殊品类 2.满足库存，自动下发(租赁-非租赁)
    // if (!!is_checked && !special_category) {
    //   const { rental: checkRental, noRental: checkNoRental } = await warehouseInventoryResult({ params, is_checked: true});
    //   //看是否有实物库存 is_white走兜底 不自动下发
    //   const checkRentalStatus = !checkRental.is_white && checkRental.status;
    //   const checkNoRentalStatus = !checkNoRental.is_white && checkNoRental.status;
    //   if (checkRentalStatus || checkNoRentalStatus) {
    //     console.log('下发这里')
    //     await deliveryDeviceService({
    //       orderId,
    //       sku_id,
    //       is_checked,
    //       is_support: !!checkRentalStatus,
    //       warehouseInfo: !!checkRentalStatus ? checkRental : checkNoRental,
    //     });
    //     //下发成功 准备自动下发弹窗
    //     return;
    //   }
    //   //不满足送检库存走下一步
    // }
    //不满足升级发货或非代发权限或非送检 去发货弹窗
    if (!storage_memory_up_auth || !is_can_distribute) {
      isAutoDelivery.value = false;
      return;
    }

    // 检查是否有满足升级发货的库存 调发货建议接口 不满足直接去发货弹窗
    // 发货建议库存满足-检查商家钱包是否有钱，有钱自动下发(租赁-非租赁)-没钱-去发货弹窗
    const upgradeParams = {
      ...params,
      is_checked: false,
      get_other_sku_type: 2,
    };
    const { rentalUpgradeData, noRentalUpgradeData } = await getUpgradeService(upgradeParams)
    //租赁非租赁都没有补贴升级建议
    if (!rentalUpgradeData.length && !noRentalUpgradeData.length) {
      isAutoDelivery.value = false;
      return;
    }

    //检查商家钱包是否有钱 与租赁建议的价格进行对比
    // {lock:{canShow:false,amount:''},unlock:{canShow:false,amount:''}}
    const { lock: lockAmount, unlock: unlockAmount } = await getMerchantPrice(rental, noRental, orderId, sku_id);
    //是否采用租赁补贴建议,如果租赁先从租赁开始查起，不满足再确认非租赁是否有建议
    if (!!rentalUpgradeData.length) {
      if (!lockAmount.canShow) {
        // 自动下发第一个
        await deliveryDeviceService({
          orderId,
          sku_id: rentalUpgradeData[0].sku_id,
          is_checked: false,
          is_support: true,
          warehouseInfo: rental,
        }, {
          storage_memory_up: 1,
          before_up_sku_id: sku_id,
          c_type: 22,
        });
        return;
      }
      const rentalAmount = filterMoneyMark(lockAmount.amount);
      if (rentalAmount > 0) {
        const skuItem = rentalUpgradeData.find(item =>
          item.price === '暂无' || rentalAmount > filterMoneyMark(item.price)
        );
        if (!!skuItem?.sku_id) {
          await deliveryDeviceService({
            orderId,
            sku_id: skuItem?.sku_id,
            is_checked: false,
            is_support: true,
            warehouseInfo: noRental,
          }, {
            storage_memory_up: 1,
            before_up_sku_id: sku_id,
            c_type: 22,
          });
        }
        return;
      }
    }
    if (!!noRentalUpgradeData.length) {
      if (!unlockAmount.canShow) {
        // 自动下发第一个
        console.log('测试是不是走非租赁的升级发货建议')
        await deliveryDeviceService({
          orderId,
          sku_id: noRentalUpgradeData[0].sku_id,
          is_checked: false,
          is_support: false,
          warehouseInfo: noRental,
        }, {
          storage_memory_up: 1,
          before_up_sku_id: sku_id,
          c_type: 22,
        });
        return;
      }
      const noRentalAmount = filterMoneyMark(unlockAmount.amount);
      if (noRentalAmount > 0) {
        const skuItem = noRentalUpgradeData.find(item =>
          item.price === '暂无' || noRentalAmount > filterMoneyMark(item.price)
        );
        if (!!skuItem?.sku_id) {
          await deliveryDeviceService({
            orderId,
            sku_id: skuItem?.sku_id,
            is_checked: false,
            is_support: false,
            warehouseInfo: noRental,
          }, {
            storage_memory_up: 1,
            before_up_sku_id: sku_id,
            c_type: 22,
          });
        }
        return;
      }
    }
  }
  return {
    isAutoDelivery,
    getCheckAuth,
    autoSendResult,
  }
}
