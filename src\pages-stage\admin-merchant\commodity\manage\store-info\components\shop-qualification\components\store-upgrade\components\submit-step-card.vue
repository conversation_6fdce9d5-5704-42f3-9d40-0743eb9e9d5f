<template>
  <StepCard
    background-image-type="submit"
    :btn="data.btn"
    :instruction="data.instruction"
    :instruction-style="data.instructionStyle"
    show-arrow
    :tag="data.tag"
    title="1.提交申请"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { EEnterStatus, EShowComponent, ISharedData } from '../data.d';
import StepCard from './step-card.vue';

const props = defineProps<{
  sharedData: ISharedData;
  updateSharedData: (data: Partial<ISharedData> | null) => void;
}>();

const data = computed(() => {
  const { audit_status } = props.sharedData;

  if (audit_status === EEnterStatus.PendingAudit) {
    return {
      instruction: '您的申请已提交，请耐心等待平台审核',
      tag: {
        name: '已提交',
        type: 'primary',
      },
    };
  } else if (audit_status === EEnterStatus.Reject) {
    return {
      instruction: '您的申请未通过，请完善材料重新提交',
      instructionStyle: {
        color: '#ff4d4f',
      },
      tag: {
        name: '已驳回',
        type: 'danger',
      },
      btn: {
        name: '请完善材料',
        type: 'primary',
        onClick: () => {
          props.updateSharedData({
            activeComponent: EShowComponent.ApplicationForm,
          });
        },
      },
    };
  } else {
    return {
      instruction: '提交申请后需等待平台审核',
      btn: {
        name: '去提交',
        type: 'primary',
        onClick: () => {
          props.updateSharedData({
            activeComponent: EShowComponent.ApplicationForm,
          });
        },
      },
    };
  }
});
</script>
