import { POST, GET } from '@/services/api';

// 下单成功页弹窗-创建
export function orderSuccessPopCreate(params: any) {
  return POST('/super/recycle/order-success-pop/create', params);
}

// 下单成功页弹窗-编辑
export function orderSuccessPopEdit(params: any) {
  return POST('/super/recycle/order-success-pop/edit', params);
}
// 下单成功页弹窗-编辑
export function orderSuccessPopDelete(params: any) {
  return GET('/super/recycle/order-success-pop/delete', params);
}
// 下单成功页弹窗-修改状态
export function orderSuccessPopUpdate(params: any) {
  return GET('/super/recycle/order-success-pop/update-status', params);
}
// 下单成功页弹窗-列表
export function orderSuccessPopList(params: any) {
  return GET('/super/recycle/order-success-pop/list', params);
}
// 下单成功页弹窗-详情
export function orderSuccessPopDetail(params: any) {
  return GET('/super/recycle/order-success-pop/detail', params);
}
