<template>
  <div class="list-pagination">
    <a-pagination
      v-show="/^\d+$/.test(total + '')"
      :current="current"
      :page-size="pageSize"
      show-quick-jumper
      :total="total"
      @change="changeHandle"
    />
    <div
      v-show="!/^\d+$/.test(total + '')"
      class="custom-pagination"
    >
      <a-button
        class="btn"
        :class="{
          disabled: current === 1,
        }"
        @click="() => current !== 1 && changeHandle(current - 1, pageSize)"
      >
        <left-outlined />
      </a-button>
      <a-button class="btn">
        {{ current }}
      </a-button>
      <a-button
        class="btn"
        :class="{
          disabled: listLength < pageSize,
        }"
        @click="() => listLength >= pageSize && changeHandle(current + 1, pageSize)"
      >
        <right-outlined />
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';

defineProps({
  current: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 15,
  },
  total: {
    type: Number,
    default: 0,
  },
  listLength: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['change']);

function changeHandle(page: number, pageSize: number) {
  emit('change', page, pageSize);
}
</script>

<style scoped lang="less">
.btn {
  width: 32px;
  height: 32px;
  padding: 0;
}
.btn + .btn {
  margin-left: 8px;
}
.btn.disabled {
  color: #00000040;
  border-color: #d9d9d9;
  cursor: not-allowed;
}
</style>
