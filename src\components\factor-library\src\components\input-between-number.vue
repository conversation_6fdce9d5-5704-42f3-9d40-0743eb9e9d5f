<template>
  <div class="input-between-number">
    <a-input-number
      v-model:value="bindValue[0]"
      class="input-item"
      :controls="false"
      placeholder="请输入"
      v-bind="attrs"
      @blur="handleChange(0)"
    />
    <div>{{ split }}</div>
    <a-input-number
      v-model:value="bindValue[1]"
      class="input-item"
      :controls="false"
      placeholder="请输入"
      v-bind="attrs"
      @blur="handleChange(1)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, useAttrs, watch } from 'vue';

type TValue = [number | undefined, number | undefined];

defineOptions({
  inheritAttrs: true,
});

const props = withDefaults(
  defineProps<{
    value?: TValue;
    split?: string;
  }>(),
  {
    value: () => [],
    split: '~',
  },
);

const emit = defineEmits<{
  (e: 'update:value', value?: TValue): void;
}>();

const bindValue = ref([...props.value]);

watch(
  () => props.value,
  val => {
    bindValue.value = [...val];
  },
);

const attrs = useAttrs();

function handleChange(index: 0 | 1) {
  const changeKey = index;
  const otherKey = index === 0 ? 1 : 0;
  if (bindValue.value[0] !== undefined && bindValue.value[1] !== undefined) {
    if (bindValue.value[0] > bindValue.value[1]) {
      bindValue.value[otherKey] = bindValue.value[changeKey];
    }
  }
  emit('update:value', [...bindValue.value]);
}
</script>

<style scoped lang="less">
.input-between-number {
  display: flex;
  flex: 1;
  gap: 6px;
  align-items: center;

  .input-item {
    flex: 1;
  }
}
</style>
