<template>
  <a-modal
    v-model:visible="visible"
    :footer="null"
    title="查看条形码"
    :width="480"
  >
    <div class="bar-code-container">
      <img id="barCodeInModal">
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import JsBarcode from 'jsbarcode';
import { useModal } from 'rrz-web-design';

const { visible, open } = useModal(undefined, { afterOpen: handleAfterOpen });

function handleAfterOpen(waybill_no: string) {
  JsBarcode('#barCodeInModal', waybill_no, { format: 'CODE128B' });
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.bar-code-container {
  text-align: center;
}
</style>
