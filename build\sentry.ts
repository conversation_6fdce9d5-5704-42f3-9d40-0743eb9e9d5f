import { sentryVitePlugin } from '@sentry/vite-plugin';

const authToken =
  '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';
const buildVersion = process.env.VITE_BUILD_VERSION;

export function createSentryPlugin() {
  console.log('sentry plugin create');
  return sentryVitePlugin({
    org: 'rrzu',
    project: 'admin_vue',
    authToken,
    url: 'https://sentry.rrzu.com',
    sourcemaps: {
      filesToDeleteAfterUpload: ['dist/**/*.js.map'],
    },
    release: {
      name: buildVersion,
      cleanArtifacts: true,
      deploy: {
        env: 'production',
      },
      setCommits: {
        auto: true,
        ignoreMissing: true,
      },
    },
  });
}
