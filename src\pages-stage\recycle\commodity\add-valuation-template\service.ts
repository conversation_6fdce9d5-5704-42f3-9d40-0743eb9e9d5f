import { GET, IAxiosResponse, POST } from '@/services/api';
import { ISelectOption, ITemplate } from './data';

/**
 * 查询估价模板详情
 * @param {any} id:string|number
 * @returns {any}
 */
export const queryTemplateDetail = (id: string | number): Promise<IAxiosResponse<ITemplate[]>> => {
  return GET('/super/recycle/evaluate-template/detail', { id });
};

/**
 * 根据type获取评估项下拉框列表
 * @param {any} type:string|number
 * @returns {any}
 */
export const queryOptionList = (type: string | number): Promise<IAxiosResponse<ISelectOption[]>> => {
  return GET('/super/recycle/evaluate-option/option-filter', { type });
};

/**
 * @Description 修改/保存估价模板
 * @param {any} data:ITemplate
 * @returns {any}
 */
export const saveTemplate = (data: ITemplate): Promise<IAxiosResponse<any>> => {
  return POST('/super/recycle/evaluate-template/save', data, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
};
