import type { FormGroupItem } from '@/components/form-create/src/typing';
import type { ColumnType } from 'ant-design-vue/lib/table';

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'model_name',
    originProps: { label: '模型名称', name: 'model_name' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'status',
    originProps: { label: '模型状态', name: 'status' },
    options: [
      { label: '启用中', value: 1 },
      { label: '异常', value: 2 },
    ],
    elProps: { placeholder: '请选择', allowClear: true, style: { width: '188px' } },
    fragmentKey: 'renderSelect',
  },
];

export const listColumns: ColumnType[] = [
  {
    title: '模型名称',
    dataIndex: 'model_name',
    key: 'model_name',
    width: 180,
  },
  {
    title: '应用范围',
    dataIndex: 'scope',
    key: 'scope',
    width: 120,
  },
  {
    title: '模型状态',
    dataIndex: 'status',
    key: 'status',
    width: 96,
  },
  {
    title: '最新版本',
    dataIndex: 'model_version',
    key: 'model_version',
    width: 88,
  },
  {
    title: '修改人',
    dataIndex: 'username',
    key: 'username',
    width: 90,
  },
  {
    title: '修改时间',
    dataIndex: 'updated_at',
    key: 'updated_at',
    width: 120,
    className: 'vertical-middle',
  },
  {
    title: '操作',
    dataIndex: 'operator',
    key: 'operator',
    width: 104,
    className: 'vertical-middle',
    fixed: 'right',
  },
];

export const modelDetailColumns: ColumnType[] = [
  {
    title: '最新版本',
    dataIndex: 'model_version',
    key: 'model_version',
    width: 88,
  },
  {
    title: '版本描述',
    dataIndex: 'model_description',
    key: 'model_description',
    width: 666,
  },
  {
    title: '执行日志',
    dataIndex: 'runtime_num',
    key: 'runtime_num',
    width: 90,
    fixed: 'right',
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    key: 'updated_at',
    width: 120,
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operator',
    key: 'operator',
    width: 148,
    fixed: 'right',
  },
];

export const execLogColumns: ColumnType[] = [
  {
    title: '运行开始时间',
    dataIndex: 'start_at',
    key: 'start_at',
    width: 120,
  },
  {
    title: '执行时间(分钟)',
    dataIndex: 'runtime',
    key: 'runtime',
    width: 126,
  },
  {
    title: '执行状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '执行成功sku覆盖率',
    dataIndex: 'complete_percentage',
    key: 'complete_percentage',
    width: 160,
  },
  {
    title: '执行失败sku覆盖率',
    dataIndex: 'fail_percentage',
    key: 'fail_percentage',
    width: 160,
  },
];

export const dateListColumns: ColumnType = [
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date',
    width: 282,
  },
  {
    title: '操作',
    dataIndex: 'operator',
    key: 'operator',
    width: 140,
  },
];
