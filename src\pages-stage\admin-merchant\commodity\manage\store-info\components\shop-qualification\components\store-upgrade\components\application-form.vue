<template>
  <div class="application-form-contiainer">
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :colon="false"
        :model="formState"
      >
        <div
          v-if="reasons.length"
          class="reason-wrapper"
        >
          <div class="title">
            审核原因
          </div>
          <a-space>
            <div
              v-for="(reason, index) of reasons"
              :key="index"
            >
              {{ `${index + 1}、${reason}` }}
            </div>
          </a-space>
        </div>

        <Block title="申请入驻类目">
          <div>
            {{ sharedData.enter_category_text }}
            <span v-if="formType !== EFormType.NonMobileComputer">、{{ sharedData.store_type_text }}</span>
          </div>
        </Block>

        <Block title="审核材料">
          <!-- 手机/电脑类目&线下门店 -->
          <template v-if="formType === EFormType.MobileComputer_Offline">
            <SubTitle
              desc="入驻平台时已提供过的经营信息"
              name="入驻平台商家经营信息"
            />

            <a-form-item
              label="企业类型"
              required
            >
              {{ sharedData.company_type }}
            </a-form-item>

            <a-form-item
              label="营业执照"
              required
            >
              <a-space :size="8">
                <div class="image-wrapper">
                  <a-image
                    v-for="(license, index) in castArray(sharedData.license_image)"
                    :key="index"
                    :height="62"
                    :src="license"
                    :width="62"
                  />
                </div>
              </a-space>
            </a-form-item>

            <SubTitle
              desc="入驻商家的经营主体与线下门店经营主体一致"
              name="线下门店经营信息"
            />

            <a-form-item
              label="企业类型"
              name="offline_company_type"
              required
            >
              <a-radio-group
                v-model:value="formState.offline_company_type"
                :options="companyTypes"
              />
            </a-form-item>

            <a-form-item
              label="营业执照"
              name="offline_license_image"
              required
            >
              <CustomUpload v-model:value="formState.offline_license_image" />
            </a-form-item>

            <SubTitle
              desc="入驻平台时已收集的面审信息。若资料内容有更新，商家可接着补充完善"
              name="线下门店面审信息"
            />

            <a-form-item
              label="场地租赁合同"
              name="offline_rent_contract"
              required
            >
              <CustomUpload
                v-model:value="formState.offline_rent_contract"
                no-limit
              />
            </a-form-item>

            <a-form-item
              label="门店视频"
              name="offline_store_video"
              required
            >
              <CustomUpload
                v-model:value="formState.offline_store_video"
                no-limit
                type="video"
              />
            </a-form-item>

            <a-form-item
              label="采购凭证"
              name="offline_purchase_certificate"
              required
            >
              <CustomUpload
                v-model:value="formState.offline_purchase_certificate"
                no-limit
              />
            </a-form-item>
          </template>

          <!-- 手机/电脑类目&线上门店 -->
          <template v-else-if="formType === EFormType.MobileComputer_Online">
            <SubTitle
              desc="商家须要提供下述任一门店类型资料供平台审核"
              name="线上门店类型"
            />

            <a-form-item
              label="门店类型"
              name="online_company_type"
              required
            >
              <a-radio-group
                v-model:value="formState.online_company_type"
                :options="onlineCompanyTypes"
              />
            </a-form-item>

            <!-- 自媒体 -->
            <template v-if="formState.online_company_type === '10'">
              <SubTitle
                desc="自媒体推广账户包含且不仅限于抖音、快手、闲鱼、小红书、今日头条等"
                name="自媒体推广账户"
              />

              <a-form-item
                label="门店类型"
                required
              >
                自媒体
              </a-form-item>

              <a-form-item
                name="online_promotion_image"
                :rules="[{ required: true, message: '请输入推广页截图' }]"
              >
                <template #label>
                  <a-space>
                    推广页截图
                    <a-tooltip title="自媒体推广页面-首页截图">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </a-space>
                </template>
                <CustomUpload v-model:value="formState.online_promotion_image" />
                <template #extra>
                  <ImageExample src="https://img1.rrzuji.cn/uploads/scheme/2504/23/7CLxdNAHSutb9nW3bwby.png" />
                </template>
              </a-form-item>

              <a-form-item
                name="promote_account_information"
                :rules="[{ required: true, message: '请输入推广账户信息' }]"
              >
                <template #label>
                  <a-space>
                    推广账户信息
                    <a-tooltip title="自媒体推广账户信息-个人实名认证截图，需与公司法人一致">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </a-space>
                </template>
                <CustomUpload v-model:value="formState.promote_account_information" />
                <template #extra>
                  <ImageExample src="https://img1.rrzuji.cn/uploads/scheme/2504/23/A6NE2iEUaWxs0Vk4Fn1F.JPG" />
                </template>
              </a-form-item>
            </template>

            <!-- 网店 -->
            <template v-if="formState.online_company_type === '11'">
              <SubTitle
                desc="实际经营网店包含且不仅限于拼多多、淘宝、天猫、京东等"
                name="经营网店账户"
              />

              <a-form-item
                label="门店类型"
                required
              >
                网店
              </a-form-item>

              <a-form-item
                name="online_promotion_image"
                :rules="[{ required: true, message: '请输入推广页截图' }]"
              >
                <template #label>
                  <a-space>
                    推广页截图
                    <a-tooltip title="网店页面-首页截图">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </a-space>
                </template>
                <CustomUpload v-model:value="formState.online_promotion_image" />
                <template #extra>
                  <ImageExample src="https://img1.rrzuji.cn/uploads/scheme/2504/23/VJ4tejFfidpnJ1Aac31X.png" />
                </template>
              </a-form-item>

              <a-form-item
                name="promote_account_information"
                :rules="[{ required: true, message: '请输入推广账户信息' }]"
              >
                <template #label>
                  <a-space>
                    推广账户信息
                    <a-tooltip title="网店账户信息-个人实名认证截图，需与公司法人一致">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </a-space>
                </template>
                <CustomUpload v-model:value="formState.promote_account_information" />
                <template #extra>
                  <ImageExample src="https://img1.rrzuji.cn/uploads/scheme/2504/23/svS5mZSSRqGqxi2Si5Hn.png" />
                </template>
              </a-form-item>
            </template>
          </template>

          <!-- 非手机、电脑类目 -->
          <template v-else>
            <SubTitle name="您经营的门店类型为" />

            <a-form-item
              name="store_type"
              required
            >
              <template #label>
                <a-space>
                  门店类型
                  <a-tooltip>
                    <template #title>
                      <div>1.线下门店：门店数量大于或等于1家</div>
                      <div>2.线上门店：拥有实际经营的网店或平台推广账户</div>
                    </template>
                    <QuestionCircleOutlined />
                  </a-tooltip>
                </a-space>
              </template>
              <a-radio-group
                v-model:value="formState.store_type"
                :options="storeTypes"
              />
            </a-form-item>

            <a-form-item
              v-if="formState.store_type === '10'"
              label="线上门店类型"
              name="online_company_type"
              required
            >
              <a-radio-group
                v-model:value="formState.online_company_type"
                :options="onlineCompanyTypes"
              />
            </a-form-item>

            <a-form-item
              v-else-if="formState.store_type === '11'"
              label="线下门店类型"
              name="offline_store_type"
              required
            >
              <a-radio-group
                v-model:value="formState.offline_store_type"
                :options="offlineStoreTypes"
              />
            </a-form-item>
          </template>
        </Block>

        <Block
          v-if="formType !== EFormType.NonMobileComputer && sharedData.enter_category === EEnterCategory.MobilePhone"
          title="证明材料"
        >
          <SubTitle
            desc="请下载模版填写，并签字盖章后拍照上传，如无公章可法人签字并且摁指纹替代公章"
            name="码商承诺函签署"
          >
            <template #suffix>
              <a-space
                :size="4"
                @click="downloadCommitment"
              >
                <DownloadOutlined style="color: #1677ff" />
                <a class="text-link">下载承诺函</a>
              </a-space>
            </template>
          </SubTitle>

          <a-form-item
            label="上传承诺函"
            name="commitment"
            required
          >
            <CustomUpload v-model:value="formState.commitment" />
            <template #extra>
              <ImageExample src="https://img1.rrzuji.cn/uploads/scheme/2504/23/suRse61RvHMGB6aVrj6V.png" />
            </template>
          </a-form-item>
        </Block>

        <Block
          v-if="isReject && formState.supplement?.length"
          title="其余补充材料"
        >
          <SubTitle desc="提供其他平台认为合理的根据法律、业务的需求要补充提交的材料" />

          <div class="material-collection">
            <template
              v-for="supplement of formState.supplement"
              :key="supplement.supplement_config_id"
            >
              <div class="material-card">
                <div class="material-card-header">
                  {{ supplement.config_name }}
                </div>
                <div class="material-card-body">
                  <div class="material-card-title">
                    材料上传
                  </div>
                  <div class="small-upload">
                    <CustomUpload
                      v-model:value="supplement.supplement_info"
                      type="mixed"
                    />
                  </div>
                </div>
              </div>
            </template>
          </div>
        </Block>
      </a-form>
    </a-spin>

    <div class="footer">
      <a-button
        size="large"
        @click="handleBack"
      >
        上一步
      </a-button>
      <a-button
        :loading="submitLoading"
        size="large"
        type="primary"
        @click="submit"
      >
        提交审核
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import { castArray, cloneDeep, pick } from 'lodash-es';

import { downloadURL } from '@/utils/base';

import { useSharedData } from '../composables/use-shared-data';
import { companyTypes, imageProps, offlineStoreTypes, onlineCompanyTypes, storeTypes } from '../config';
import { EEnterCategory, EEnterStatus, EFormType, EShowComponent, EStoreType } from '../data.d';
import { create, getEnterInfo, getFaceReviewData, update } from '../services';
import Block from './application-block.vue';
import CustomUpload from './custom-upload.vue';
import ImageExample from './image-example.vue';
import SubTitle from './sub-title.vue';

const { sharedData, updateSharedData } = useSharedData();

/** 是否驳回 */
const isReject = computed(() => sharedData.audit_status === EEnterStatus.Reject);
/** 根据入驻条件确定的表单类型 */
const formType = computed<EFormType>(() => {
  if (sharedData.enter_category !== EEnterCategory.NonMobileComputer && sharedData.store_type) {
    // 手机、电脑类目分为线上、线下门店
    return sharedData.store_type === EStoreType.Offline
      ? EFormType.MobileComputer_Offline
      : EFormType.MobileComputer_Online;
  } else {
    // 非手机电脑类目
    return EFormType.NonMobileComputer;
  }
});

const formRef = ref();
const formState = reactive<any>({});

const loading = ref(false);
const submitLoading = ref(false);

const reasons = ref<string[]>([]);
const URLPathMap = new Map<string, string>();

function handleBack() {
  updateSharedData({
    activeComponent: EShowComponent.StepManagement,
  });
}

function downloadCommitment() {
  downloadURL('https://img1.rrzuji.cn/uploads/scheme/2504/09/rwAWp6BBESM2J0LJgjNL.pdf', '码商承诺函.pdf');
}

async function submit() {
  try {
    submitLoading.value = true;
    await formRef.value.validate();
    const params: any = cloneDeep(formState);

    const api = isReject.value ? update : create;
    await api(normalizeParams(params));

    message.success('提交成功');
    updateSharedData({
      activeComponent: EShowComponent.StepManagement,
      audit_status: EEnterStatus.PendingAudit,
    });
  } finally {
    submitLoading.value = false;
  }
}

function normalizeParams(params: any) {
  for (const prop of imageProps) {
    if (params[prop]) {
      params[prop] = params[prop].map(normalizeUploadItem).join(',');
    }
  }
  if (params.supplement) {
    params.supplement = JSON.stringify(
      params.supplement.map(item => {
        if (item.supplement_info) {
          item.supplement_info = item.supplement_info.map(normalizeUploadItem).join(',');
        }
        return item;
      }),
    );
  }
  params.store_type ??= sharedData.store_type;
  return params;
}

function normalizeUploadItem(item: any) {
  if (typeof item === 'object' && 'response' in item) {
    return '/' + item.response.path[0];
  } else {
    const url = typeof item === 'string' ? item : item.url;
    return URLPathMap.get(url) || url.replace(/https:\/\/[^\/]+/, '');
  }
}

async function assignData() {
  const { data } = await getEnterInfo();
  const {
    id,
    enter_info,
    enter_category,
    enter_category_text,
    store_type,
    store_type_text,
    company_type_text,
    audit_reason,
    sp_reason,
    supplement,
  } = data;
  const { license_image_show } = enter_info;

  // 驳回原因
  reasons.value = [audit_reason, sp_reason].filter(Boolean);
  // 更新表单
  formState.id = id;
  formState.supplement = [];

  imageProps.forEach(prop => {
    const show = enter_info[`${prop}_show`];
    if (show) {
      formState[prop] = show;
      const origin = enter_info[prop].split(',');
      show.forEach((url, index) => URLPathMap.set(url, origin[index]));
    }
  });

  supplement.forEach(item => {
    const { config_name, supplement_config_id, supplement_info, supplement_info_show } = item;
    formState.supplement.push({
      config_name,
      supplement_config_id,
      supplement_info: supplement_info_show,
    });
    if (supplement_info_show) {
      const origin = supplement_info.split(',');
      supplement_info_show.forEach((url, index) => URLPathMap.set(url, origin[index]));
    }
  });

  Object.assign(formState, pick(enter_info, ['offline_company_type', 'online_company_type', 'offline_store_type']));

  // 更新数据
  updateSharedData({
    store_type,
    store_type_text,
    enter_category,
    enter_category_text,
    company_type: company_type_text,
    license_image: license_image_show,
  });
}

async function init() {
  const { data } = await getFaceReviewData();
  const { venue_contract, store_video, purchase_document, business_license } = data;
  formState.offline_rent_contract = venue_contract;
  formState.offline_store_video = store_video;
  formState.offline_purchase_certificate = purchase_document;
  formState.offline_license_image = business_license;
}

onMounted(() => {
  if (isReject.value) {
    loading.value = true;
    assignData().finally(() => (loading.value = false));
  } else {
    init();
  }
});
</script>

<style lang="less" scoped>
.application-form-contiainer {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 200px);
  padding-bottom: 120px;
}

.image-wrapper {
  padding: 8px;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 4px;
}

.reason-wrapper {
  margin-bottom: 16px;
  padding: 16px;
  color: #061533a6;
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
}

.reason-wrapper .title {
  margin-bottom: 2px;
  color: #ff4d4f;
  font-size: 16px;
  line-height: 24px;
}

.material-collection {
  display: inline-flex;
  flex-wrap: nowrap;
  gap: 24px;
  max-width: calc(100% - 132px);
  margin-left: 132px;
  padding: 16px;
  overflow-x: scroll;
  background-color: #0615330f;
  border-radius: 2px;
  box-shadow: inset -16px 0 16px 0 #e6e9ef;
}

.material-card {
  flex-shrink: 0;
  width: 380px;
  background-color: #fff;
}

.material-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 14px;
  background-color: #06153305;
}

.material-card-body {
  padding: 16px 14px 8px;
}

.material-card-title {
  margin-bottom: 12px;
}

.footer {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 16px;
  background-color: #fff;
  box-shadow: 8px 0 8px 0 rgba(0, 0, 0, 0.15);
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  width: 160px;
}

:deep(.ant-upload),
:deep(.ant-upload-list-picture-card-container) {
  width: 80px;
  height: 80px;
}

:deep(.small-upload .ant-upload),
:deep(.small-upload .ant-upload-list-picture-card-container) {
  width: 64px;
  height: 64px;
}
</style>
