<template>
  <a-input-number
    v-if="type === 'number'"
    v-model:value="bindValue"
    :controls="false"
    placeholder="请输入"
    v-bind="attrs"
  />
  <a-input
    v-else
    v-model:value="bindValue"
    placeholder="请输入"
    v-bind="attrs"
  />
</template>

<script lang="ts" setup>
import { computed, useAttrs } from 'vue';

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  type?: 'number';
  value?: (string | number)[];
  readonly?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:value', value: (string | number)[]): void;
}>();

const bindValue = computed({
  get() {
    return props.value?.[0];
  },
  set(value) {
    if (!value && value !== 0) {
      // 空数据
      emit('update:value', []);
      return;
    }
    // 只取一个
    emit('update:value', [value]);
  },
});

const attrs = useAttrs();
</script>
