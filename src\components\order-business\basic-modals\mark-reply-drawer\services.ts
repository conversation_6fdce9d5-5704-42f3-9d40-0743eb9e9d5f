import { GET, POST } from '@/services/api';

/** 标记待回复 */
export function fetchChangeOrderMark(data: any) {
  return POST('/super/v2-order/mark', data);
}

/** 获取选择的下发仓库 */
export function getOrderWarehouse(params: { order_id: string }) {
  return GET('/super/v2-order/get-order-warehouse', params);
}

/** 获取下发仓库选项 */
export function getWarehouseOptions() {
  return GET('/super/tbl-warehouse/get-warehouse?is_quality=1');
}

/** 获取标记信息 */
export function getOrderMark(params: { order_id: string[] }) {
  return GET('/order/mark', params, { hostType: 'Golang' });
}
