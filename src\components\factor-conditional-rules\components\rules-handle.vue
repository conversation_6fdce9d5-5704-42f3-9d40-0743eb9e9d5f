<template>
  <div class="rules-handle">
    <template v-if="addShow">
      <span
        v-if="showAdd"
        class="add-box"
        @click="addHandle"
      >
        <PlusCircleOutlined style="color: #00c8be" />
        添加
      </span>
      <span
        v-if="showAddChild"
        class="add-box"
        @click="addChildRules"
      >
        <PlusCircleOutlined style="color: #00c8be" />
        子级
      </span>
    </template>
    <span
      class="del-box"
      @click="delHandle"
    > <DeleteOutlined /> </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
import { isEmptyValue } from '@rrzu/utils';

import { TRulesInfo } from '../data.d';
const props = defineProps<{
  level: number;
  currentItemInfo: TRulesInfo;
  isLast: boolean;
}>();
const emits = defineEmits(['del', 'add', 'addChildRules']);

const showAdd = computed(() => {
  return props.currentItemInfo.level >= 1 && props.isLast;
});

const showAddChild = computed(() => {
  return props.level > 1 && props.currentItemInfo.level < props.level;
});

const addShow = computed(() => {
  return !!(props.level > 1 && allHave(props.currentItemInfo.rules));
});

function allHave(rules: any) {
  return (
    rules &&
    Object.keys(rules).length &&
    Object.keys(rules).every((key: any) => {
      return !isEmptyValue(rules[key]);
    })
  );
}
function addHandle() {
  emits('add');
}

function addChildRules() {
  emits('addChildRules');
}

function delHandle() {
  emits('del');
}
</script>

<style lang="less" scoped>
.rules-handle {
  display: flex;
  gap: 8px;
  align-items: center;
  .add-box {
    display: inline-block;
    width: 50px;
    cursor: pointer;
  }
  .del-box {
    display: inline-block;
    width: 14px;
    cursor: pointer;
  }
  .del-box:hover {
    color: red;
  }
}
</style>
