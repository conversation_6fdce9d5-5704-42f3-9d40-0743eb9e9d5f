<template>
  <a-space
    :size="8"
    style="margin: 0"
    wrap
  >
    <template
      v-for="(item, index) in renderButtonGroup"
      :key="index"
    >
      <!--  有子级时渲染为下拉菜单  -->
      <a-dropdown v-if="item.children?.length">
        <a-button
          v-if="!item.display || item.display()"
          v-bind="{ ...buttonAttrs, ...getAttrs(item) }"
        >
          <template v-if="isVNode(item.label)">
            <component :is="item.label" />
          </template>
          <template v-else>
            {{ item.label }}
          </template>
          <DownOutlined />
        </a-button>
        <template #overlay>
          <a-menu>
            <template
              v-for="(child, i) in item.children"
              :key="i"
            >
              <a-menu-item
                v-if="!child.display || child.display()"
                v-bind="getAttrs(child)"
              >
                <template v-if="isVNode(child.label)">
                  <component :is="child.label" />
                </template>
                <template v-else>
                  {{ child.label }}
                </template>
              </a-menu-item>
            </template>
          </a-menu>
        </template>
      </a-dropdown>
      <!--  无子级时渲染为普通按钮  -->
      <a-button
        v-else-if="!item.display || item.display()"
        v-bind="{ ...buttonAttrs, ...getAttrs(item) }"
      >
        <template v-if="isVNode(item.label)">
          <component :is="item.label" />
        </template>
        <template v-else>
          {{ item.label }}
        </template>
      </a-button>
    </template>
  </a-space>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, isVNode, unref } from 'vue';
import { Modal } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';

import { useUnityModal } from '../../../unity-modal';
import { IButtonData, IButtonGroupItem, TButtonAttrs } from './data';

const props = defineProps({
  /** 按钮组 */
  buttonGroup: {
    type: Array as PropType<IButtonGroupItem[]>,
    default: () => [],
  },
  /** 接口返回的对应按钮关联数据 */
  buttonData: {
    type: Array as PropType<IButtonData[]>,
    default: () => [],
  },
  /** 按钮排序根据buttonData的进行排序 */
  buttonSort: {
    type: Boolean,
    default: false,
  },
  /** 按钮组公共属性 */
  buttonAttrs: {
    type: Object as PropType<TButtonAttrs>,
    default: () => ({}),
  },
  /** 弹窗公共属性 */
  modalAttrs: {
    type: Object as Record<string, any>,
    default: () => ({}),
  },
});

const { onToggleComponent } = useUnityModal();

const renderButtonGroup = computed(() => {
  return getRenderButtonGroup(props.buttonGroup);
});

function getRenderButtonGroup(buttonGroup: IButtonGroupItem[]) {
  const newGroup: IButtonGroupItem[] = [];
  try {
    buttonGroup.forEach((buttonItem, buttonIndex) => {
      let extra: Record<string, any> | undefined;
      // ================== 如果配置button，但buttonData没有数据，则过滤 ================
      if (buttonItem.buttonCom?.key) {
        const dataItem = props.buttonData?.find(data => data.button === buttonItem.buttonCom?.key);
        if (!dataItem) {
          // 未包含该按钮，则过滤
          return;
        }
        if (dataItem?.extra) {
          try {
            extra = JSON.parse(dataItem.extra.replaceAll('\n', '\\n').replaceAll('\r', '\\r'));
          } catch (e) {
            console.error(`button(${buttonItem.buttonCom.key}).extra:${dataItem.extra}`, e);
          }
        }
      }
      // ============= 定义按钮文本 ============
      let label = buttonItem.buttonCom?.label || `未定义(${buttonIndex})`;
      if (buttonItem.label) {
        if (typeof buttonItem.label === 'function') {
          const str = buttonItem.label(extra);
          label = str || label;
        } else {
          label = buttonItem.label;
        }
      }
      // ============= 获取按钮属性 ============
      let buttonItemAttrs = buttonItem.buttonAttrs;
      if (typeof buttonItemAttrs === 'function') {
        buttonItemAttrs = buttonItem.buttonAttrs(extra);
      }
      // ============= 定义按钮的点击事件 ================
      const onClick = () => {
        // 自定义了点击事件
        if (buttonItemAttrs?.onClick) {
          buttonItemAttrs.onClick(extra);
          return;
        }
        // 调用弹窗
        if (buttonItem.buttonCom?.component) {
          if (extra?.confirm_text) {
            Modal.confirm({
              title: '温馨提示',
              content: extra.confirm_text,
              onOk: () => {
                onToggleComponent(buttonItem.buttonCom, {
                  extraData: extra,
                  ...props.modalAttrs,
                  ...buttonItem.modalAttrs,
                });
              },
            });
          } else {
            onToggleComponent(buttonItem.buttonCom, {
              extraData: extra,
              ...props.modalAttrs,
              ...buttonItem.modalAttrs,
            });
          }
        }
      };
      // ============ 循环子级 =============
      const children = getRenderButtonGroup(buttonItem.children || []);
      // ============ 添加按钮 ==============
      newGroup.push({
        ...buttonItem,
        buttonAttrs: { ...buttonItemAttrs, onClick },
        label,
        children: children,
      });
    });

    // ============= 按钮排序 ==============
    if (props.buttonSort) {
      newGroup.sort((a, b) => {
        const aIndex = props.buttonData?.findIndex(data => data.button === a.buttonCom?.key) || 0;
        const bIndex = props.buttonData?.findIndex(data => data.button === b.buttonCom?.key) || 0;
        return aIndex - bIndex;
      });
    }
  } catch (e) {
    console.error(e);
  }
  return newGroup;
}

function getAttrs(buttonItem: IButtonGroupItem) {
  // 实现按钮属性的动态变化控制
  const newAttrs = {};
  for (const key in buttonItem.buttonAttrs) {
    newAttrs[key] = unref(buttonItem.buttonAttrs[key]);
  }
  return newAttrs;
}
</script>
