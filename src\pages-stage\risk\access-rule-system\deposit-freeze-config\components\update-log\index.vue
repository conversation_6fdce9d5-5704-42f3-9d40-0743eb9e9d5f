<template>
  <a-drawer
    placement="right"
    title="修改历史"
    :visible="props.value"
    width="100%"
    @close="emit('update:value', false)"
  >
    <a-table
      :columns="tbCol"
      :data-source="list"
      :loading="listLoading"
      :pagination="page"
      @change="tableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'actions'">
          <a @click="openLogDetailModal(record.id)"> 查看</a>
        </template>
      </template>
    </a-table>
  </a-drawer>
  <LogDetail
    :id="logDetailId"
    v-model:value="logDetailVisible"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useTable } from '@/hook/component/use-table';
import LogDetail from '../log-detail/index.vue';
const props = defineProps({
  value: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits<{
  (event: 'update:value', value: boolean): void;
}>();

const tbCol = [
  {
    title: '版本',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '修改时间',
    dataIndex: 'created_at',
    key: 'created_at',
  },
  {
    title: '修改量',
    dataIndex: 'update_num',
    key: 'update_num',
  },
  {
    title: '修改人',
    dataIndex: 'created_by',
    key: 'created_by',
  },
  {
    title: '操作',
    key: 'actions',
  },
];
const { list, listLoading, page, getTableList, tableChange } = useTable({
  url: '/super/tbl-deposit-freeze-rule/get-rule-list-log',
  formatHandle: res => {
    return res.data.list;
  },
  totalKey: 'data.page.totalCount',
  pageSizeKey: 'pageSize',
  pageKey: 'currentPage',
});

const logDetailVisible = ref(false);
const logDetailId = ref('');
function openLogDetailModal(id: string) {
  logDetailId.value = id;
  logDetailVisible.value = true;
}
watch(
  () => props.value,
  (value: boolean) => value && getTableList(),
);
</script>
