import { GET, POST, RequestConfig } from '@/services/api';
import { IFormModal } from './data';

// 修改状态
export const updataStatus = <T extends { id: string; status: string }>(data: T) => {
  return GET<T, []>('/super/recycle/common-question/update-status', data);
};

// 删除
export const deleteAction = (id: string) => {
  return GET('/super/recycle/common-question/delete', { id });
};

// 获取详情
export const getDetailAction = <T extends { id: string }>(data: T) => {
  return GET<T, IFormModal>('/super/recycle/common-question/detail', data);
};

// 创建
export const createAction = <T extends Partial<IFormModal>>(data: T, config?: RequestConfig) => {
  return POST<T, { id?: string }>('/super/recycle/common-question/create', data, config);
};

// 编辑
export const editAction = <T extends Partial<IFormModal> & { id: string }>(data: T, config?: RequestConfig) => {
  return POST<T, { id?: string }>('/super/recycle/common-question/edit', data, config);
};
