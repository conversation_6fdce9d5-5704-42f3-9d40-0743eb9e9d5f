/*
 * @Description: 吸顶效果设置
 */
import useElementCeiling from './use-element-ceiling';

export default function () {
  const theadCeiling = useElementCeiling({
    targetClass: 'table-thead',
    type: 'top',
    setWidth: target => {
      const targetThs = target.querySelectorAll('th');
      for (let i = 0; i < targetThs.length; i++) {
        targetThs[i].style.width = targetThs[i].clientWidth + 'px';
      }
    },
    clearWidth(target: HTMLElement) {
      const ths = target.querySelectorAll('th');
      for (let i = 0; i < ths.length; i++) {
        ths[i].style.width = '';
      }
    },
  });

  /**
   * @description: 吸顶效果
   * @return {*}
   */
  function ceilling() {
    theadCeiling.targetEleCeiling();
  }

  /**
   * 监听元素突变
   */
  function listenerMutation(ele: HTMLElement, callback: () => void) {
    const observer = new MutationObserver(callback);
    observer.observe(ele, {
      childList: true,
      subtree: true,
    });
  }

  window.addEventListener('scroll', ceilling);
  listenerMutation(document.body, () => {
    theadCeiling.updateTargetTop();
    theadCeiling.targetEleCeiling();
  });
}
