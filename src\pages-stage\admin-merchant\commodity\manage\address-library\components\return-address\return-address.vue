<template>
  <div class="return-address">
    <div class="alter-wrap alter">
      <a-alert
        show-icon
        type="info"
      >
        <template #message>
          <div class="message">
            1. 为方便租户归还设备或申请退货，您应当设置至少一个归还地址。
          </div>
        </template>
        <template #description>
          <div class="description">
            2. 您当前拥有多个地址，请及时 为待归还订单指定合适的归还地址。
          </div>
        </template>
      </a-alert>
    </div>
    <div class="table-wrap">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <a-space :size="16">
              <a-button
                class="btn-link"
                type="link"
                @click="updateById(record)"
              >
                修改
              </a-button>
              <a-button
                class="btn-link"
                type="link"
                @click="deleteById(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:visible="addAndEditModalVisible"
      :after-close="handleClose"
      closable
      title="添加归还地址"
      @ok="handleOk"
    >
      <form-create
        ref="addAndEditRef"
        v-model:value="addAndEditParams"
        :form-group="addOrEditFormGroup"
        :origin-props="{
          layout: 'vertical',
        }"
      >
        <template #customer0>
          <a-form-item
            label="所在地区"
            name="union_id"
            :rules="[
              {
                message: '请输入所在地区',
                required: true,
                trigger: 'blur',
              },
            ]"
          >
            <a-cascader
              v-model:value="addAndEditParams.union_id"
              allow-clear
              :options="cityData"
              placeholder="请选择省份/城市/区"
              @change="handleChange"
            />
          </a-form-item>
          <a-form-item
            label="地址详细信息"
            name="address"
            :rules="[
              {
                message: '请输入地址详细信息',
                required: true,
                trigger: 'blur',
              },
            ]"
          >
            <a-textarea
              v-model:value="addAndEditParams.address"
              :maxlength="100"
              placeholder="请输入"
              show-count
            />
          </a-form-item>
        </template>
      </form-create>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';

import { FormCreate } from '@/components/form-create';
import { loadChineseDistricts } from '@/utils/static-load';

import { addOrEditFormGroup, columns } from './config';
import { addReturnAddress, deleteReturnAddress, getListApi, updateReturnAddress } from './service';

const addAndEditRef = ref<InstanceType<typeof FormCreate>>();

const addAndEditParams = ref<any>({
  address: '',
  union_id: [],
  phone: '',
  name: '',
});

const unionId = ref<string>('');

const serverInfo = ref<{
  username: string;
  phone: string;
}>({
  username: '',
  phone: '',
});

type TnumberKeyObject = {
  [key: string]: string;
};
interface treeItem {
  label: string;
  value: string;
  union_id?: string | number;
  children?: treeItem[];
}
const cityData = ref<any[]>([]);

function formatCityData(cityData: TnumberKeyObject, prefix: string, level: number): treeItem[] {
  const result: treeItem[] = [];
  const keys: string[] = Object.keys(cityData);
  keys.forEach((key: string) => {
    const item: treeItem = {
      label: cityData[key],
      value: cityData[key],
      union_id: key,
    };
    if (window['ChineseDistricts'][key] && level < 3) {
      item.children = formatCityData(window['ChineseDistricts'][key], item.value, level + 1);
    }
    result.push(item);
  });
  return result;
}

const list = ref<any[]>([]);
const listLoading = ref<boolean>(true);

const fetchList = async () => {
  listLoading.value = true;
  const {
    data: { list: tableList, userInfo },
  } = await getListApi();
  serverInfo.value = userInfo;
  list.value = tableList;
  listLoading.value = false;
};

const addAndEditModalVisible = ref<boolean>(false);
const isEdit = ref<boolean>(false);

const showAddModal = () => {
  isEdit.value = false;
  addAndEditModalVisible.value = true;
  addAndEditParams.value = {
    address: '',
    union_id: [],
    phone: serverInfo.value.phone,
    name: serverInfo.value.username,
  };
};

const handleChange = (value: any, selectedOptions: any) => {
  unionId.value = selectedOptions[selectedOptions.length - 1].union_id;
};

const handleClose = () => {
  addAndEditModalVisible.value = false;
};

const handleOk = () => {
  (addAndEditRef.value?.getFormRef() as any).validate().then(async () => {
    const submitParam = {
      ...addAndEditParams.value,
      union_id: unionId.value,
    };
    try {
      isEdit.value ? await updateReturnAddress(submitParam) : await addReturnAddress(submitParam);
      message.success('更新成功');
      await fetchList();
    } catch (error) {
      message.success('更新失败');
    } finally {
      addAndEditModalVisible.value = false;
    }
  });
};

const deleteById = async (record: any) => {
  console.log(record);
  Modal.confirm({
    title: '您确定删除改地址吗？',
    content: '注意：删除后已使用此地址的订单将同步移除地址',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      const { status, error } = await deleteReturnAddress({
        id: record.id,
      });
      if (status === 0) {
        message.success('删除成功');
        await fetchList();
      } else {
        message.error(error);
      }
    },
  });
};

const updateById = async (record: any) => {
  isEdit.value = true;
  addAndEditModalVisible.value = true;
  const { province, city, region, address, union_id, phone, name } = record;
  unionId.value = union_id;
  addAndEditParams.value = {
    address,
    union_id: [province + ' / ' + city + ' / ' + region],
    phone,
    name,
    id: record.id,
  };
};

async function getTableList() {
  await loadChineseDistricts();
  cityData.value = formatCityData(window['ChineseDistricts'][86], '', 1);
  fetchList();
}

onMounted(() => {
  getTableList();
});

defineExpose({
  handleAdd: showAddModal,
});
</script>
<style lang="less" scoped>
.return-address {
  margin-top: 8px;

  .alter-wrap {
    :deep(.ant-alert-info) {
      background-color: var(--ant-primary-1);
      border: none;
    }
    :deep(.ant-alert-icon) {
      color: var(--ant-primary-6);
    }
    :deep(.ant-alert-message) {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: var(--ant-heading-1-size);
    }
    .description {
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }

  .table-wrap {
    margin-top: 24px;
  }
  // .search-wrap {
  //   display: flex;
  //   flex-wrap: wrap;
  //   margin: 0 -8px;
  //   padding: 24px 0;
  //   :deep(.ant-form-item) {
  //     flex-basis: 25%;
  //     margin: 0 0 28px 0;
  //     padding: 0 8px;
  //   }
  // }
}
:deep(.ant-alert-message) {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
}
:deep(.ant-alert-description) {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
}
:deep(.ant-alert-info) {
  border: none;
}
.btn-link {
  padding: 0;
}
</style>
