import { AxiosResponse } from 'axios';

import { GET, POST } from '@/services/api';
import { getCookie } from '@/utils/cookies';
// 获取运营后台分类列表
export function getSuperCateListApi(): Promise<AxiosResponse<any>> {
  return GET('/super/newWorkOrder/new-work-order/server-port-cate-list');
}

// 获取商家后台分类列表
export function getCateListApi(): Promise<AxiosResponse<any>> {
  return GET('/new-work-order/server-port-cate-list');
}
// 获取运营后台分类模板内容
export function getSuperCateTempDetail(params: { id: string }): Promise<AxiosResponse<any>> {
  return GET('/super/newWorkOrder/new-work-order/new-template-detail', params);
}

// 获取商家后台分类模板内容
export function getCateTempDetail(params: { type: string }): Promise<AxiosResponse<any>> {
  return GET('/newWorkOrder/new-template/order-cancel-reason-get-template', params);
}

// 运营后台创建
export function superCreateWorkOrder(data: any): Promise<AxiosResponse<any>> {
  return POST('/super/work-order-inter/save-data', data, {
    headers: {
      Authorization: getCookie('Go-Token'),
    },
  });
}

// 商家后台创建
export function createWorkOrder(data: any): Promise<AxiosResponse<any>> {
  return POST('/work-order-inter/save-data', data);
}
//获取取消原因options
export const getOrderCancelReasonOptionsApi = () => {
  return GET('/newWorkOrder/new-template/order-cancel-reason-option');
};
