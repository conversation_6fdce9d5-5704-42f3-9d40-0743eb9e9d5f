import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';

const statusOptions = [
  {
    label: '使用中',
    value: '1',
    color: '#00C8BE',
  },
  {
    label: '未使用',
    value: '2',
    color: '#FF4A57',
  },
];
export const columns: ColumnType[] = [
  {
    title: '订单号',
    key: 'recycle_order_id',
    dataIndex: 'recycle_order_id',
    width: 203,
  },
  {
    title: '创建时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 203,
  },
  {
    title: '好评等级',
    key: 'comment_level_text',
    dataIndex: 'comment_level_text',
    width: 92,
  },
  {
    title: '估价金额（元）',
    key: 'evaluate_price',
    dataIndex: 'evaluate_price',
    width: 167,
  },

  {
    title: '定价金额（元）',
    key: 'purchased_price',
    dataIndex: 'purchased_price',
    width: 167,
  },
  {
    title: '展示状态',
    key: 'status',
    dataIndex: 'status',
    width: 95,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 178,
    fixed: 'right',
  },
];
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'recycle_order_id',
    originProps: { label: '订单号', name: 'recycle_order_id' },
    elProps: { allowClear: true, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'status',
    originProps: { label: '使用状态', name: 'status' },
    elProps: { allowClear: true, placeholder: '请输入' },
    fragmentKey: 'renderSelect',
    options: statusOptions,
  },
];
