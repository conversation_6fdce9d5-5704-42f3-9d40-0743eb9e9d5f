import './index.less';

import { defineComponent } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal, TableColumnType } from 'ant-design-vue';
import type { TransformCellTextProps } from 'ant-design-vue/lib/table/interface';

import { renderFragmentInput } from '@/pages-stage/stock/manage/device/components/components-fragment';
import { useInjectUrlQuery } from '@/pages-stage/stock/manage/device/composables/use-inject-url-query';
import type { IUseTableResult } from '@/utils/hooks';
import { useTablePagination } from '@/utils/hooks';

import type { IFilter } from './config';
import { dataTableColumns, EFilterType, filterConfig } from './config';
import { getHasDraft, postConfirmDelete, postConfirmReceive } from './services';

interface ISetupData extends IUseTableResult<IFilter, any> {
  columns: TableColumnType[];

  confirmReceiveModal: (id: string) => void;
  confirmDeleteModal: (id: string) => void;

  toRelease: (imei: string, batch: string) => void;
  toDetail: (id: string) => void;
}

export default defineComponent({
  setup(): ISetupData {
    const route = useRoute();
    // 分页筛选
    const tablePagination = useTablePagination<IFilter, any>({
      url: '/tbl-warehouse/release-device-list',
      filters: {
        batch: undefined,
        device_id: undefined,
        type: EFilterType.待发布,
      },
      dropdownField: ['unrelease_num', 'release_num'],
      handle: res => {
        res.data.list = res.data.list.map(item => ({
          ...item,
          sell_stock: item.sell_stock + '',
          real_stock: item.real_stock + '',
        }));
        return res;
      },
    });
    (async () => {
      await useInjectUrlQuery(tablePagination.filters);
      tablePagination.startRequest();
    })();

    const confirmReceiveModal = (id: string) => {
      Modal.confirm({
        title: '确认签收',
        content: '签收后，系统将使用核对结果作为真实设备信息进行检测和发布，不再支持修改，是否确认签收？',
        okText: '确认',
        cancelText: '取消',
        async onOk() {
          try {
            const res = await postConfirmReceive(id);
            message.success(res.message);
            tablePagination.startRequest();
          } catch (err) {
            console.log(err);
          }
        },
      });
    };

    const confirmDeleteModal = (id: string) => {
      Modal.confirm({
        title: '确认删除',
        content: '删除后，设备将从批次清单中删除，是否确认删除？',
        okText: '确认',
        cancelText: '取消',
        async onOk() {
          try {
            const res = await postConfirmDelete(id);
            message.success(res.message);
            tablePagination.startRequest();
          } catch (err) {
            console.log(err);
          }
        },
      });
    };

    const toRelease = (imei: string, batch: string) => {
      getHasDraft({ unique_code: imei, device_type: 1, batch_no: batch }).then(res => {
        if (res.data.has_draft === 1) {
          window.open(
            `${route.query.origin}/spu-view/create?quality=1&id=${res.data.spu_id}&imei=${imei}&batch=${batch}&server_id=${res.data.server_id}`,
          );
        } else {
          window.open(
            `${route.query.origin}/spu-view/create?quality=1&imei=${imei}&batch=${batch}&server_id=${res.data.server_id}`,
          );
        }
      });
    };
    const toDetail = (imei: string) => {
      window.open(`${route.query.origin}/quality-spu/warehouse-quality-index?imei=${imei}`);
    };

    return {
      columns: dataTableColumns,
      ...tablePagination,

      confirmReceiveModal,
      confirmDeleteModal,

      toRelease,
      toDetail,
    };
  },
  render() {
    // 表格筛选
    const renderTableFilter = () => {
      return (
        <>
          {filterConfig.map(i => !i.none && renderFragmentInput(this, i.title, i.field))}
          <a-space size={8}>
            <a-button type="primary" onClick={() => this.search()}>
              搜索
            </a-button>
            <a-button onClick={this.reset}>重置</a-button>
          </a-space>
        </>
      );
    };
    // 表格tab
    const renderTableTabs = () => {
      return (
        <a-tabs vModel={[this.filters.type, 'activeKey']} onChange={() => this.search()} animated={false}>
          <a-tab-pane key={EFilterType.待发布} tab={`待发布 (${this.filtersDropdown.unrelease_num || 0})`} />
          <a-tab-pane key={EFilterType.已发布} tab={`已发布 (${this.filtersDropdown.release_num || 0})`} />
        </a-tabs>
      );
    };
    // 表格内容
    const renderTableContainer = () => {
      return (
        <a-table
          columns={this.columns}
          dataSource={this.dataSource}
          pagination={this.pagination}
          rowKey="id"
          loading={this.loading}
          scroll={{ x: '100%' }}
          sticky={true}
          onChange={this.handlePaginationChange}
          vSlots={{
            text: ({ record, column }: TransformCellTextProps) => (
              <span title={record[column.dataIndex as string] || '-'}>{record[column.dataIndex as string] || '-'}</span>
            ),
            action: ({ record }: TransformCellTextProps) => (
              <div>
                {this.filters.type === EFilterType.待发布 && (
                  <a-button type="link" onClick={() => this.toRelease(record.imei, record.batch_no)}>
                    去发布
                  </a-button>
                )}
                {this.filters.type === EFilterType.已发布 && (
                  <a-button type="link" onClick={() => this.toDetail(record.imei)}>
                    查看商品
                  </a-button>
                )}
              </div>
            ),
          }}
        ></a-table>
      );
    };

    return (
      <div class="data-table">
        <div class="data-table-filter">{renderTableFilter()}</div>
        <div class="data-table-tabs">{renderTableTabs()}</div>
        <div class="data-table-container">{renderTableContainer()}</div>
      </div>
    );
  },
});
