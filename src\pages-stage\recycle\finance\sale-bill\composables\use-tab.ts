import { ref, watch } from 'vue';
import { ISearchParams } from '../data';
export interface IOptions {
  fetchTable?: (type?: string | undefined) => void;
  searchParams?: Partial<ISearchParams>;
}

export default (options: IOptions) => {
  const activeKey = ref('');
  const hasLoad = ref(false);
  watch(
    () => activeKey.value,
    (val: string) => {
      if (!hasLoad.value) {
        if (options.searchParams) {
          options.searchParams.order_pay_status = val;
        }
        options.fetchTable && options.fetchTable();
      }
    },
  );
  return {
    activeKey,
    hasLoad,
  };
};
