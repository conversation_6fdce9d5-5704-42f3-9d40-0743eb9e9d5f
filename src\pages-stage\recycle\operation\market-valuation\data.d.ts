export interface IBreadCrumb {
  type: string;
  label: string;
  value: number[] | string;
}

export interface IRequestModelParams {
  category_id: number | string;
  brand_id: number | string;
  model_id: number | string;
  new_ratio?: number[] | string;
  memory?: number[] | string;
  start_at?: string;
  end_at?: string;
  platform?: string;
  page?: number;
  page_size?: number;
}

interface IPlatFormData {
  x: string;
  y: number;
  error: string;
  capacity: string;
  price_ratio: string;
}

interface IPlatFormList {
  market_id: string;
  platform: string;
  platform_name: string;
  platform_data: IPlatFormData[][];
}

export interface IMemoryData {
  chart?: any;
  chart_key?: any;
  new_ratio: string;
  memory?: string;
  current_new_ratio?: string;
  IPlatFormList: [];
}

interface INewratioList {
  label: string;
  value: string;
}

interface IAllMemoryList {
  chart?: any;
  chart_key?: string;
  memory: string;
  new_ratio: INewratioList[];
  current_new_ratio: string;
  platform_list: Platformlist[];
}

export interface IAllChart {
  title: string;
  category_id: string;
  brand_id: string;
  model_id: string;
  data_start_date: string;
  data_end_date: string;
  start_date: string;
  end_date: string;
  range?: any;
  has_more: number;
  memory_list: IAllMemoryList[];
}

export interface IApplicableModelType {
  option_name: string;
  value: string;
  options: IOptionType[];
}

export interface IOptionDict {
  option_name: string;
  options: IOptionList[];
}

export interface IOptionList {
  option_name: string;
  option_id: number;
}

export interface IModelOptionConfig {
  title: string;
  category_id: string;
  brand_id: string;
  model_id: string;
  new_ratio: string;
  platform: string;
  platform_name: string;
  number_id: string;
  market_id: string;
  option_list: IOptionList[];
  memory_option_ids: number[];
  options_dict: IOptionDict[];
}

interface IOptionType {
  option_name: string;
  option_id: number;
}

export interface IState {
  breed: number[][];
  page: number;
  page_size: number;
  total: number;
}
