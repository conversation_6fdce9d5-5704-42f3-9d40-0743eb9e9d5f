<template>
  <layout-admin-page
    :navs="['趣回收', '报价管理', '报价单']"
    title="报价单"
  >
    <template #extra>
      <a-button
        style="margin-right: 8px"
        @click="handleExport"
      >
        <ExportOutlined />
        导出
      </a-button>
      <a-button
        type="primary"
        @click="formFile.click()"
      >
        <UploadOutlined />
        上传Excel文件
        <input
          ref="formFile"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          multiple
          style="display: none"
          type="file"
          @change="readExcelFile"
        >
      </a-button>
    </template>
    <div class="container">
      <div class="main">
        <div class="intrduce">
          <img
            class="intrduce-img"
            src="https://img1.rrzuji.cn/uploads/scheme/2305/29/m/6uRwbmq3kXSCUeLsEKo6.png"
          >
          <div>
            特殊说明：<br>
            1.iPhone13、iPhone12的红色机型需在以上价格基础上减100元回收；iPhone11、iPhoneXR的红色/珊瑚色机型需在以上价格基础上减50元回收。<br>
            2.无报价机型暂不支持回收。 <br>
            3.以上报价均为二手苹果国行手机回收价，其他版本（含港版）暂不支持回收。
          </div>
        </div>
        <div class="today">
          {{ today }} 更新
        </div>
        <div
          v-if="errorList.length > 0"
          class="error-list"
        >
          <span
            v-for="(item, key) in errorList"
            :key="key"
            class="error-text"
          >
            {{ item }}
          </span>
        </div>
        <a-table
          bordered
          class="bottom-fix-table"
          :columns="clueListColumns"
          :data-source="mergedDataSource"
          :loading="listLoading"
          :pagination="false"
          :row-key="getRowKey"
          :scroll="{ x: '100%' }"
          :sticky="true"
          style="margin-top: 12px"
        />
      </div>
    </div>
    <div
      v-if="showConfirm"
      class="fixed-bottom"
    >
      <a-button
        style="margin-right: 8px"
        type="primary"
        @click="confirm"
      >
        确认报价
      </a-button>
      <a-button @click="cancel">
        取消
      </a-button>
    </div>
  </layout-admin-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ExportOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { IExcelModel } from './data';
import { Modal, message } from 'ant-design-vue';
import { addRequirements, getQuitationList, checkByWarehouse } from './service';
import { useTodayPrice } from '../../common/use-today-price';
import { loadXLSX } from '@/utils/static-load';
const { listLoading, clueListColumns, mergedDataSource, dataSource, getRowKey, fetchTableData, today } = useTodayPrice({
  fetchDataHook: getQuitationList,
  onMounted: true,
});
const formFile = ref();
const showConfirm = ref(false);

const errorList = ref<string[]>([]);

// 读取表格
const readExcelFile = async () => {
  const file = formFile.value.files[0];
  if (!file) return;
  const reader = new FileReader();
  reader.onload = async function (e) {
    if (e.target) {
      const data = e.target.result as ArrayBuffer;
      const workbook = XLSX.read(data, { type: 'binary' });
      const columnMap = {
        A: 'goods_id',
        B: 'model',
        C: 'version',
        D: 'memory',
        E: 'color',
        F: 'quasi_new',
        G: 'new99',
        H: 'new95',
        I: 'new90',
      };
      let result: IExcelModel[] = [];
      const makeDataSource = () => {
        const splitColorData: IExcelModel[] = [];
        result.forEach((item: IExcelModel) => {
          const { color } = item;
          const colorArr = color.split('、');
          colorArr.forEach((colorItem: string) => {
            splitColorData.push({
              ...item,
              color: colorItem,
            });
          });
        });
        return splitColorData;
      };

      // 转换excel第一张表的数据，过滤掉标题和表头；
      const sheetData = XLSX.utils
        .sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], {
          header: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'],
          defval: '',
        })
        .filter((_: any, index: number) => {
          return index > 1;
        })
        .filter((item: any) => {
          const keyArr = Object.keys(item);
          return keyArr.every((key: string) => {
            return typeof item[key] === 'string' ? !item[key].includes('特殊说明') : true;
          });
        });

      if (!sheetData || !sheetData.length) return message.warning('暂未检测到有效数据');

      dataSource.value = [];
      errorList.value = [];

      // 将每一项的key（A、B、C...）转换为字段名
      const sheetDataSource = sheetData.map((item: any) => {
        const info = {};
        const keys = Object.keys(item);
        keys.forEach((key: string) => {
          info[columnMap[key]] = item[key];
        });
        return info;
      });

      let defaultModel = '';
      let defaultVersion = '';

      sheetDataSource.forEach((item: IExcelModel, index: number) => {
        const isNumber = typeof item.goods_id === 'number' || !isNaN(Number(item.goods_id));
        if (!defaultVersion || item.version) defaultVersion = item.version;
        if (!defaultModel || item.model) defaultModel = item.model;
        item.version = item.version || defaultVersion;
        item.model = item.model || defaultModel;
        if (!item.goods_id || !isNumber || (item.goods_id && String(item.goods_id).indexOf('.') >= 0)) {
          errorList.value.push(`第${index + 4}行，基础商品id存在错误`);
        } else {
          result.push({ ...item, row_index: index + 4 });
        }
      });
      const validateResult = await checkByWarehouse({ content: makeDataSource() });
      const { error_id, error } = validateResult.data;
      errorList.value = error;
      result = result.filter((resultItem: IExcelModel) => {
        return !error_id.includes(resultItem.goods_id);
      });
      dataSource.value = makeDataSource();
      message.success('导入加载成功');
      showConfirm.value = true;
    }
  };
  await loadXLSX();
  reader.readAsBinaryString(file);
  formFile.value.value = '';
};

const confirm = async () => {
  Modal.confirm({
    title: '确认上传该回收报价吗？',
    content: '是否确认对该报价上传，上传完成后全部商家可见。',
    onOk: async () => {
      try {
        listLoading.value = true;
        initExportRecord();
        await addRequirements({ content: dataSource.value });
        message.success(`导入成功，共${dataSource.value.length}条数据`);
        await fetchTableData();
        showConfirm.value = false;
      } catch (error) {
        listLoading.value = false;
      } finally {
        listLoading.value = false;
      }
    },
  });
};
const cancel = async () => {
  Modal.confirm({
    title: '确认取消该回收报价吗？',
    content: '是否取消对该报价上传，取消后不会保留此次修改。',
    onOk: async () => {
      showConfirm.value = false;
      initExportRecord();
      await fetchTableData();
    },
  });
};

const initExportRecord = () => {
  errorList.value = [];
};

const handleExport = async () => {
  await loadXLSX();

  const tableData = [clueListColumns.map(item => item.title)]; //先生成表头
  const tableKey = clueListColumns.map(item => item.key);

  //根据key添加每行数据
  dataSource.value.forEach(item => {
    let rowData: (number | string)[] = tableKey.map(key => item[key as string]);
    tableData.push(rowData);
  });

  const workbook = XLSX.utils.book_new();
  const sheet = XLSX.utils.aoa_to_sheet(tableData);

  //根据goods_id合并设备型号表格
  const idPositions = {};
  dataSource.value.forEach((item, index) => {
    let id = item.goods_id;
    if (id in idPositions) {
      idPositions[id].count++;
    } else {
      idPositions[id] = { count: 1, position: index };
    }
  });

  const mergeArr = Object.keys(idPositions).map(key => {
    const { position, count } = idPositions[key];
    return {
      s: { r: position + 1, c: 1 },
      e: { r: position + count, c: 1 },
    };
  });

  sheet['!merges'] = mergeArr;
  sheet['!cols'] = tableKey.map(key => (['model'].includes(key as string) ? { wpx: 140 } : { wpx: 70 }));

  XLSX.utils.book_append_sheet(workbook, sheet, 'sheet');
  XLSX.writeFile(workbook, '报价单.xlsx');
};
</script>

<style lang="less" scoped>
@import '../../common/base.less';
.intrduce {
  display: flex;
  padding: 9px 45px 9px 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 22px;
  background: #f0f7ff;
  border: 1px solid #b3d2ff;
  border-radius: 2px;
  .intrduce-img {
    width: 14px;
    height: 14px;
    margin-top: 3px;
    margin-right: 7px;
  }
}
.today {
  margin-top: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 22px;
}
.fixed-bottom {
  position: fixed;
  bottom: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 72px;
  background: #fff;
  :deep(.ant-btn) {
    height: 40px;
    padding: 0 20px;
  }
}
.error-text {
  display: block;
  margin-top: 8px;
  color: #ff4d4f;
  font-weight: bold;
}
.error-list {
  height: 100px;
  overflow: auto;
}
</style>
