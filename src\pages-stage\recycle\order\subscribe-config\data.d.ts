export interface IDataSourceItem {
  id: string;
  rule_name: string;
  rule_detail: string;
  status: string;
  status_text: string;
  created_at: string;
  order_total: string;
  order_total_today: string;
  // 自定义生成的变量
  statusValue?: boolean;
}

export interface IRuleOrderFormItem {
  attr: string;
  attr_label: string;
  content: number[];
  content_label: string[];
}

export interface IRuleGoodsFormChildItem {
  model: string[];
  modelCn: string[];
  new: number[];
  newCn: string[];
}

export interface IRuleGoodsFormItem {
  brand: string;
  brandCn: string;
  category: string;
  categoryCn: string;
  child: IRuleGoodsFormChildItem[];
}

export interface IRuleSubmitData {
  id?: string;
  name: string;
  detail: string;
  order_form: IRuleOrderFormItem[];
  goods_form: IRuleGoodsFormItem[];
  activity: { label: string; value: number }[];
}
