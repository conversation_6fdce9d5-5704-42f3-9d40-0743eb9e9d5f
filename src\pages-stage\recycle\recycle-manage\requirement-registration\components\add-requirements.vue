<template>
  <a-button
    type="primary"
    @click="open"
  >
    <template #icon>
      <plus-outlined />
    </template>
    添加回收需求
  </a-button>
  <a-modal
    v-model:visible="visible"
    destroy-on-close
    title="回收需求登记"
    width="550px"
    @cancel="close"
    @ok="confirm"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="form"
      >
        <a-form-item
          name="phone"
          required
          :rules="[
            { required: true, message: '联系号码不能为空', trigger: 'blur' },
            {
              pattern: /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
              message: '请输入合法的手机号',
              trigger: 'blur',
            },
          ]"
        >
          <template #label>
            联系号码
          </template>
          <a-input
            v-model:value.trim="form.phone"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          name="model"
          required
          :rules="{ required: true, message: '设备型号、数量不能为空', trigger: 'blur', validator: handleValidate }"
        >
          <template #label>
            <div>设备型号、数量</div>
          </template>
          <select-row
            v-for="(item, index) in model"
            :key="item.id"
            :current-index="index"
            :item="item"
            :model="model"
            :slelct-options="slelctOptions"
            @add-row="addRow"
            @delete-row="deleteRow"
            @number-change="numberChange"
          />
          <div class="add-model">
            <img
              class="add-img"
              :src="
                model.length >= 20
                  ? 'https://img1.rrzuji.cn/uploads/scheme/2305/26/m/g5hLDo6kZ5thRUxxamwo.png'
                  : 'https://img1.rrzuji.cn/uploads/scheme/2306/06/m/YXhHwOKSeWxzBznG0VZi.png'
              "
              @click="addRow({ type: 'add' })"
            >
            <span>添加设备</span>
          </div>
        </a-form-item>
        <a-form-item
          label="备注"
          name="remark"
        >
          <a-textarea
            v-model:value.trim="form.remark"
            :auto-size="{ minRows: 3 }"
            :maxlength="50"
            placeholder="请输入"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { useModal } from '@/hook/component/use-modal';
import { FormInstance } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { IRequireForm, IModel } from '../data';
import { demandRequirements, getTodayPrice } from '../service';
import SelectRow from './select-row.vue';
import { ISelectOptions, formatExcelData } from '@/pages-stage/recycle/common/use-today-price';
import { PlusOutlined } from '@ant-design/icons-vue';
const emits = defineEmits(['refresh']);
const formRef = ref<FormInstance>();
const form = reactive<IRequireForm>({
  phone: '',
  demand_content: [], // 机型
  quotation_id: '',
  remark: undefined,
});
const slelctOptions = ref<ISelectOptions[]>([]);
const model = ref<IModel[]>([]);
const onSubmit = async () => {
  try {
    await formRef.value?.validate();
    form.demand_content = model.value;
    await demandRequirements(form);
    close();
    emits('refresh');
  } catch (error) {}
};
const { open, close, loading, confirm, visible } = useModal(onSubmit, {
  beforeClose: () => {
    formRef?.value?.resetFields();
    model.value = [];
    initModel();
  },
});
const getTody = async () => {
  try {
    const { data } = await getTodayPrice();
    form.quotation_id = data.id;
    slelctOptions.value = formatExcelData(data.content);
  } catch (error) {
    throw error;
  }
};
const addRow = (value: { type: string; currentIndex?: number; data?: IModel }) => {
  if (model.value.length >= 20) return;
  const { data, currentIndex, type } = value;
  if (type === 'add') {
    model.value.push({
      id: Math.random() + new Date().getTime(),
      model: '',
      memory: '',
      new: '',
      count: 1,
    });
  } else {
    if (data && (currentIndex === 0 || currentIndex)) {
      model.value[currentIndex] = data;
    }
  }
};
const numberChange = (value: number, currentIndex: number) => {
  model.value[currentIndex].count = value;
};
const deleteRow = (currentIndex: number) => {
  model.value.splice(currentIndex, 1);
};
const handleValidate = () => {
  if (!model.value.length) return Promise.reject();
  const isEmpty = model.value.some(item => {
    for (const key in item) {
      if (!item[key]) {
        return true;
      }
    }
    return false;
  });
  if (isEmpty) {
    return Promise.reject();
  } else {
    return Promise.resolve();
  }
};
const initModel = () => {
  model.value.push({ id: -1, model: '', memory: '', new: '', count: 1 });
};
onMounted(async () => {
  initModel();
  await getTody();
});
</script>

<style lang="less" scoped>
.add-model {
  display: flex;
  align-items: center;
  .add-img {
    margin-left: 4px;
  }
}
.ant-form-item {
  flex-direction: column;
  width: 100%;
  padding: 0 24px;
}
:deep(.ant-col) {
  flex: 1;
  min-height: min-content;
}
:deep(.ant-form-item-label) {
  align-self: flex-start;
}
</style>
