<template>
  <a-drawer
    v-model:visible="bindVisible"
    placement="right"
    title="标记待回复"
    :width="720"
  >
    <template #extra>
      <a-button
        v-if="riskManageBtnDisplay"
        type="primary"
        @click="gotoMarkPage"
      >
        标记配置管理
      </a-button>
    </template>
    <a-spin
      class="mark-checkboxs"
      :spinning="loading"
    >
      <a-checkbox-group v-model:value="selectedMark">
        <a-row>
          <a-col
            v-for="item in options"
            v-show="!item.is_deleted || selectedMark.includes(item.id)"
            :key="item.id"
            :span="6"
            style="margin-bottom: 12px"
          >
            <a-checkbox
              :disabled="checkDisabled(item)"
              :value="item.id"
              @change="handleSelect"
            >
              {{ item.title }}
            </a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-spin>
    <DistributeWarehouseModal
      ref="distributeWarehouseModalRef"
      :order-id="orderId"
      @success="changeDistributeWarehouse"
    />
  </a-drawer>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import { EOrderStatus } from '@/typing';

import DistributeWarehouseModal from './components/distribute-warehouse-modal.vue';
import { fetchChangeOrderMark, getOrderMark } from './services';

interface IMarkItem {
  id: string;
  title: string;
  is_deleted: number;
}

/** 检测发货专用id */
const QUALITY_DELIVERY_ID = '299';

const props = defineProps<{
  visible?: boolean;
  orderId: string;
  orderStatus: number;
  options?: Record<number, IMarkItem>;
  riskManageBtnDisplay?: 0 | 1;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const route = useRoute();
const domain = route.query.origin || window.location.origin;

/** 跳转到标志管理页面 */
function gotoMarkPage() {
  window.open(domain + '/super/risk-manage/index');
}

/** 选择的标记 */
const selectedMark = ref<string[]>([]);
/** 正在处理请求的标记 */
const requestingMark = ref<string[]>([]);
/** 抽屉控制 */
const bindVisible = useVModel(props, 'visible', emit);
/** 加载 */
const loading = ref(false);

watch(
  bindVisible,
  val => {
    if (val) {
      loading.value = true;
      getOrderMark({ order_id: [props.orderId] })
        .then(res => {
          const markList = res.data?.list?.[props.orderId] || [];
          selectedMark.value = markList.map(item => String(item.mark_type));
        })
        .finally(() => {
          loading.value = false;
        });
    }
  },
  { immediate: true },
);

/** 检查禁用 */
function checkDisabled(item: IMarkItem) {
  if (item.is_deleted) {
    return true; // 已删除的标识不可选择
  }
  if (requestingMark.value.includes(item.id)) {
    return true; // 标记正在请求修改时不可选择
  }
  // 检测发货专用在订单特殊状态下或者已经选中时不可更改
  return (
    item.id === QUALITY_DELIVERY_ID &&
    ([EOrderStatus.CloseUser, EOrderStatus.CloseServer, EOrderStatus.CloseSystem].includes(props.orderStatus) ||
      selectedMark.value.includes(QUALITY_DELIVERY_ID))
  );
}

const distributeWarehouseModalRef = ref<InstanceType<typeof DistributeWarehouseModal>>();

/** 手动选择修改标记 */
function handleSelect(event: { target: { checked: boolean; value: string } }) {
  const { value, checked } = event.target;
  // 检测发货专用选中时特殊处理
  if (value === QUALITY_DELIVERY_ID && checked) {
    // 取消选中
    nextTick(() => {
      const index = selectedMark.value.findIndex(item => item === value);
      if (index !== -1) {
        selectedMark.value.splice(index, 1);
      }
    });
    // 打开下发仓库弹框
    distributeWarehouseModalRef.value?.openModal();
  } else {
    changeMarkSelect(value, checked);
  }
}

/** 修改选中下发仓库 */
function changeDistributeWarehouse() {
  selectedMark.value.push(QUALITY_DELIVERY_ID);
  emit('refresh', props.orderId, ['all_remark']);
}

/** 请求修改标记选择 */
function changeMarkSelect(value: string, checked: boolean) {
  // 添加正在处理请求的标记
  requestingMark.value.push(value);
  // 请求修改标记
  fetchChangeOrderMark({
    orderId: props.orderId,
    type: 'mark',
    markType: value,
    value: checked ? 1 : 0,
  })
    .then(res => {
      message.success(res.data?.msg);
    })
    .catch(() => {
      // 添加失败时需还原选择
      if (checked) {
        const index = selectedMark.value.findIndex(item => item === value);
        if (index !== -1) {
          selectedMark.value.splice(index, 1);
        }
      } else {
        selectedMark.value.push(value);
      }
    })
    .finally(() => {
      // 移除正在处理请求的标记
      const index = requestingMark.value.findIndex(item => item === value);
      if (index !== -1) {
        requestingMark.value.splice(index, 1);
      }
    });
}
</script>
