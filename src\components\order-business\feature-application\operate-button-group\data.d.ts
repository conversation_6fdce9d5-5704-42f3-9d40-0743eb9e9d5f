import type { Ref, VNode } from 'vue';
import { Button } from 'ant-design-vue';

type TAttrs = InstanceType<typeof Button>['$props'] | InstanceType<typeof Button>['$attrs'];

export type TButtonAttrs = {
  [P in keyof TAttrs]: Readonly<Ref<TAttrs[P]>> | TAttrs[P]; // 每个属性 P 的类型为 Ref<TAttrs[P]> | TAttrs[P]
};

/** 支持VNode类型与字符串 */
type TLabel = string | VNode;

export interface IButtonGroupItem {
  /** 判断是否显示 */
  display?: () => boolean;
  /** 按钮文本（可通过传入函数根据extra动态渲染） */
  label?: TLabel | ((extra: Record<string, any> | undefined) => TLabel);
  /** 按钮属性（可通过传入函数根据extra动态渲染） */
  buttonAttrs?: TButtonAttrs | ((extra: Record<string, any> | undefined) => TButtonAttrs);
  /** 对应的按钮 */
  buttonCom?: {
    key?: string;
    label?: string;
    component?: () => any;
  };
  /** 弹窗属性 */
  modalAttrs?: Record<string, any>;
  /** 子级下拉菜单，仅支持一级,子级菜单非按钮元素，因此不支持href属性 */
  children?: IButtonGroupItem[];
}

export interface IButtonData {
  button: number;
  extra?: string;
}
