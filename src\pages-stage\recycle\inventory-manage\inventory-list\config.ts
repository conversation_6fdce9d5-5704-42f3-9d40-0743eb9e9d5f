import { ColumnProps } from 'ant-design-vue/lib/table';

export const columns: ColumnProps = [
  {
    dataIndex: 'category_name',
    title: '品类',
    width: 116,
  },
  {
    dataIndex: 'brand_name',
    title: '品牌',
    width: 116,
  },
  {
    dataIndex: 'model_name',
    title: '型号',
    width: 160,
  },
  {
    dataIndex: 'sku_info',
    title: 'SKU信息',
    width: 160,
  },
  {
    dataIndex: 'sku_id',
    title: 'SKUID',
    width: 160,
  },
  {
    dataIndex: 'stock',
    title: '仓库库存',
    width: 100,
  },
  {
    dataIndex: 'can_sale_stock',
    title: '可销售库存',
    width: 100,
  },
  {
    dataIndex: 'average_purchased_price',
    title: '单位成本',
    width: 100,
    tipsText: '系统生成，即时统计，仓库库存对应设备的回收订单账单的总定价金额之和/仓库库存。',
  },
  {
    dataIndex: 'purchased_price',
    title: '成本',
    width: 100,
    tipsText: '系统生成，即时统计，仓库库存对应设备的回收订单账单的总定价金额之和。',
  },
];
