<template>
  <div class="form-card">
    <div
      v-if="title"
      class="title"
    >
      <span>{{ title }}</span>
      <slot name="extra" />
    </div>
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title?: string;
}>();
</script>

<style lang="less" scoped>
.form-card {
  padding: 24px;
  background: #fff;
  & + & {
    margin-top: 16px;
  }
  & > .title {
    position: relative;
    padding: 0 0 16px 12px;
    overflow: hidden;
    font-size: 16px;
    line-height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;
    &::before {
      position: absolute;
      top: 4px;
      left: 0;
      display: inline-block;
      width: 4px;
      height: 16px;
      padding-top: 4px;
      background: var(--ant-primary-color);
      border-radius: 2px;
      content: '';
    }
  }
}
</style>
