<template>
  <layout-admin-page
    :navs="['趣回收', '运营管理', '估价策略设置']"
    title="估价策略设置"
  >
    <template #extra>
      <adjust-price-btn />
    </template>
    <div class="page-body">
      <form-create
        ref="searchFormRef"
        v-model:value="searchInfo"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="getTableList('search')"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            type="default"
            @click="onReset"
          >
            重置
          </a-button>
        </template>
      </form-create>
      <a-table
        class="bottom-fix-table table-wrapper"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        row-key="goods_id"
        :scroll="{ x: 1300 }"
        sticky
        @change="tableChange"
      >
        <template #headerCell="{ column }">
          <template v-if="column.tipsText">
            {{ column.title }}
            <a-tooltip placement="top">
              <template #title>
                <span>{{ column.tipsText }}</span>
              </template>
              <question-circle-outlined style="color: grey; font-size: 16px" />
            </a-tooltip>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'name'">
            <a-image
              :src="record.image && record.image[0]"
              style="
                width: 52px;
                height: 52px;
                object-fit: scale-down;
                overflow: hidden;
                background-color: #f5f5f7;
                border: 1px solid rgba(6, 21, 51, 0.06);
                border-radius: 4px;
              "
            />
            <span style="padding-left: 12px">{{ record.name }}</span>
          </template>
          <template v-if="column.dataIndex === 'market_change'">
            <span :class="['status-render', { danger: record[column.dataIndex] === 1 }]">
              {{ record[column.dataIndex] === 1 ? '是' : '否' }}
            </span>
          </template>
          <template v-if="column.dataIndex === 'auto_evaluate_price_status'">
            <a-switch
              v-model:checked="record.auto_evaluate_price_status"
              checked-children="开"
              checked-value="1"
              un-checked-children="关"
              un-checked-value="0"
              @change="value => onChangeStatus(record, value)"
            />
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <a-button
              class="handle-btn"
              type="link"
              @click="onOpenDrawer({ setting_type: '1', goods_id: record.goods_id, goods_name: record.name })"
            >
              C端估价策略
            </a-button>
            <a-button
              class="handle-btn"
              type="link"
              @click="onOpenDrawer({ setting_type: '2', goods_id: record.goods_id, goods_name: record.name })"
            >
              B端估价策略
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
    <tactics-drawer ref="tacticsDrawerRef" />
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { useTable } from '@/hook/component/use-table';
import { ISearch, IDataSource, IOpenDrawerParams } from './data.d';
import { searchFormGroup, columns } from './config';
import { updateStatus } from './service';
import AdjustPriceBtn from './components/adjust-price-btn.vue';
import TacticsDrawer from './components/tactics-drawer/index.vue';

const searchFormRef = ref<any>(null);
const searchInfo = reactive<ISearch>({
  goods_name: '',
  third_platform_price_status: '0',
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable<IDataSource, ISearch>({
  url: '/super/recycle/purchased-goods-price/list',
  searchForm: searchInfo,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});
function onReset() {
  searchFormRef.value.formRef.resetFields();
  getTableList('search');
}

async function onChangeStatus(record: IDataSource, value: any) {
  const { goods_id } = record;
  const res = await updateStatus({ goods_id, auto_evaluate_price_status: value });
  message.success(res.message || '操作成功');
  getTableList();
}

const tacticsDrawerRef = ref<any>(null);
function onOpenDrawer(params: IOpenDrawerParams) {
  tacticsDrawerRef.value.open(params);
}

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
.page-body {
  padding: 0 24px;
}
.table-wrapper {
  margin-top: 24px;
  .status-render {
    position: relative;
    padding-left: 16px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 8px;
      height: 8px;
      background: #00c8be;
      border-radius: 50%;
      transform: translateY(-50%);
      content: '';
    }
    &.danger {
      &::before {
        background: #ff4a57;
      }
    }
  }
  .handle-btn {
    color: #3777ff;
  }
}
</style>
