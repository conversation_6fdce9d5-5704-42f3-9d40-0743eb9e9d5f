// shop/template/select
import { GET, POST } from '@/services/api';
export function getWxConfig(): Promise<any> {
  return GET('/server-relation/get-wx-bind-params');
}

export function getList(): Promise<any> {
  return GET('/server-relation/get-list');
}

export function getIsBind(): Promise<any> {
  return GET('/server-relation/get-relation-status');
}

export function getRelevancyCode(data: { phone: number | string }): Promise<any> {
  return POST('/server-relation/get-add-relation-code', data);
}
export function addServerRelation(data: { phone: number | string; code: string }): Promise<any> {
  return POST('/server-relation/add-server-relation', data);
}

export function getUnbindCode(data: {
  type: string;
  relation_id: number | string;
  phone: number | string;
}): Promise<any> {
  return POST('/server-relation/get-delete-relation-code', data);
}

export function getServerRelationPhone(data: { relation_id: number | string }): Promise<any> {
  return POST('/server-relation/get-server-relation-phone', data);
}

export function deleteServerRelation(data: {
  relation_id: any;
  active_user_phone: string;
  active_user_code: string;
  passive_user_phone: string;
  passive_user_code: string;
}): Promise<any> {
  return POST('/server-relation/delete-server-relation', data);
}
