import { POST, GET } from '@/services/api';

// 修改使用状态
export function changeItemStatus(params: any) {
  return POST('//super/recycle/coupon-market/use-or-stop', params);
}

// 获取操作记录
export function getModifyLog(params: any) {
  return GET('/super/recycle/coupon-market/modify-log', params);
}

// 删除营销券
export function delItem(params: any) {
  return GET('/super/recycle/coupon-market/del', params);
}

// 创建营销券
export function createCoupon(params: any) {
  return POST('/super/recycle/coupon-market/create', params);
}

// 修改营销券
export function modifyCoupon(params: any) {
  return POST('/super/recycle/coupon-market/edit', params);
}

// 获取适用商品类型
export function getTypeOptions() {
  return GET('/super/recycle/coupon-market/create-filter');
}
