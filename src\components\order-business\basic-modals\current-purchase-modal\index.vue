<template>
  <RThemeModal
    v-model:open="bindVisible"
    :mask-closable="false"
    ok-text="知道了"
    :show-cancel-button="false"
    title="购买价说明"
    type="primary"
    :width="800"
    @ok="bindVisible = false"
  >
    <a-radio-group
      v-model:value="activeSelect"
      button-style="solid"
      style="margin-bottom: 24px"
    >
      <a-radio-button value="1">
        到期购买价说明
      </a-radio-button>
      <a-radio-button
        v-if="isGradsBuyoutPrice"
        value="2"
      >
        当期购买价说明
      </a-radio-button>
    </a-radio-group>
    <template v-if="activeSelect === '1'">
      <div class="tip-text">
        <p class="tip-text-title">
          尊敬的商家伙伴，您好!
        </p>
        <p>为解决商家经营困惑，人人租订单页面将展示 <span>「到期购买价」</span> 字眼。</p>
        <p>
          <span>「到期购买价」</span>：指订单在总租期到期后购买商品需付的金额（即租户支付完全部租期账单后，需额外支付该金额才能购买商品）。
        </p>
        <p>人人租将持续优化服务和租赁价格体系，请留意平台最新通知。</p>
      </div>
    </template>
    <template v-else>
      <div class="tip-text">
        <p class="tip-text-title">
          尊敬的商家伙伴，您好!
        </p>
        <p>为解决商家经营困惑，人人租订单的弹窗页面将展示 <span>「当期购买价」</span> 字眼。</p>
        <p>
          <span>「当期购买价」</span>：指订单在当期租期内购买商品需付的金额（即租户在租赁期间，有购买意向时，需额外支付该金额才能购买商品）
        </p>
        <p>人人租将持续优化服务和租赁价格体系，请留意平台最新通知。</p>
      </div>
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="false"
        :scroll="{ y: 300 }"
      />
    </template>
  </RThemeModal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { TableColumnType } from 'ant-design-vue/lib/components';

import { useVModel } from '@/hook';

import { getGradsBuyout } from './services';

interface IGradsBuyoutItem {
  tenancy: string;
  time_radius: string;
  buyout_price: string;
}

const props = defineProps({
  isGradsBuyoutPrice: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const activeSelect = ref('1');

const title = ref('');
const dataList = ref<IGradsBuyoutItem[]>([]);
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      activeSelect.value = '1';
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  getGradsBuyout({
    order_id: props.orderId,
  })
    .then(res => {
      dataList.value = res.data.buyout_list;
      title.value = res.data.sku_name;
    })
    .finally(() => {
      loading.value = false;
    });
}

const columns: TableColumnType[] = [
  {
    title: '期数',
    dataIndex: 'tenancy',
    key: 'tenancy',
  },
  {
    title: '当期购买价',
    dataIndex: 'current_purchase_price',
    key: 'current_purchase_price',
  },
];
</script>

<style lang="less" scoped>
.title {
  margin-bottom: 24px;
  text-align: center;
}

.tip-text {
  padding: 16px;
  background: #f4f8ff;
  border-radius: 8px;

  .tip-text-title {
    margin-bottom: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    line-height: 22px;
  }

  p {
    color: rgba(6, 21, 51, 0.65);

    span {
      color: #3777ff;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.tip-light {
  margin-top: 12px;
  padding: 16px;
  color: rgba(6, 21, 51, 0.45);
  background: rgba(67, 111, 199, 0.02);
  border: 1px solid rgba(6, 21, 51, 0.04);
  border-radius: 8px;

  p:last-child {
    margin-bottom: 0;
  }
}

// 运营后台不调整颜色
:deep(.ant-radio-button-wrapper) {
  &:hover {
    color: #3777ff !important;
  }
}

:deep(.ant-radio-button-wrapper-checked) {
  color: #fff;
  background: #3777ff !important;
  border-color: #3777ff !important;

  &:hover {
    color: #fff !important;
  }
}
</style>
