<template>
  <a-modal
    :confirm-loading="loading"
    :mask-closable="false"
    title="云仓绑码"
    :visible="visible"
    width="620px"
    @cancel="handleCancel"
    @ok="handleConfirm"
  >
    <a-form
      class="form"
      layout="horizontal"
    >
      <div class="base-info">
        <a-form-item
          label="货品信息"
          style="margin-bottom: 0"
        >
          <span>{{ skuInfo }}</span>
          <span style="color: rgba(6, 21, 51, 0.45)"> （发货以货品信息为准） </span>
        </a-form-item>
        <a-form-item
          label="货品数"
          style="margin-bottom: 0"
        >
          <span>{{ num }}</span>
        </a-form-item>
        <a-form-item
          label="当前发货仓库"
          style="margin-bottom: 0"
        >
          <div>云仓公共池 <span class="c-red">(不校验设备码和库存票据，请确保当前发货仓库准确！)</span></div>
        </a-form-item>
      </div>

      <a-form-item
        v-if="codeType === 1"
        label="IMEI码"
        v-bind="validateInfos['imei']"
      >
        <MultipleInput
          v-model:value="state.imei"
          :error-data="errorImei"
          placeholder="请输入IMEI码，按空格确认输入"
        />
      </a-form-item>
      <a-form-item
        v-if="codeType === 2"
        label="SN码"
        v-bind="validateInfos['sn']"
      >
        <MultipleInput
          v-model:value="state.sn"
          placeholder="请输入SN码，按空格确认输入"
        />
      </a-form-item>
      <a-form-item
        :label="`${codeType === 1 ? 'SN码' : 'IMEI码'}`"
        v-bind="validateInfos['other_code']"
      >
        <MultipleInput
          v-model:value="state.other_code"
          :placeholder="`请输入${codeType === 1 ? 'SN码' : 'IMEI码'}，按空格确认输入`"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { Form, message } from 'ant-design-vue';

import MultipleInput from '@/components/multiple-input/multiple-input.vue';

import { postOrderBindCode } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  num: {
    type: Number,
    default: 1,
  },
  orderId: {
    type: String,
    default: '',
  },
  skuInfo: {
    type: String,
    default: '',
  },
  cloudImei: {
    type: String,
    default: '',
  },
  cloudSn: {
    type: String,
    default: '',
  },
  isIphone: {
    type: Boolean,
    default: false,
  },
  codeType: {
    type: Number,
    default: '',
  },
  // 不同场景需在提交接口多提交的参数合集
  otherParams: {
    type: Object,
    default: () => ({}),
  },
  isMobilePhone: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['refresh', 'update:visible']);

const validateImei = (val: string) => /^(\d{15})$/.test(val);
const splitAndFilter = (str: string) =>
  str
    .trim()
    .split(',')
    .filter(s => !!s);

const rules = computed(() => {
  return {
    imei: [
      {
        validator: (_: any, _val: string) => {
          const sn = state.sn
            .trim()
            .split(',')
            .filter(s => !!s);
          const val = _val
            .trim()
            .split(',')
            .filter((s: any) => !!s);
          const errArr = [];
          if (!!props.isIphone && !val.length) {
            return Promise.reject('请输入IMEI码');
          }
          for (let i = 0; i < val.length; i++) {
            if (!/^(\d{15})$/.test(val[i])) errArr.push(val[i]);
          }
          errorImei.value = errArr;
          if (!val.every((imei: string) => /^(\d{15})$/.test(imei))) {
            return Promise.reject('请输入正确格式的IMEI码（15位数字）');
          }
          if ((!!props.isIphone || !sn.length || val.length) && val.length !== Number(props.num)) {
            return Promise.reject(`当前已输入${val.length}个，应当输入${props.num}个`);
          }
          if (sn.length && val.length && sn.length !== val.length) {
            return Promise.reject('IMEI码数量需和SN码数量一致');
          }
          return Promise.resolve();
        },
      },
    ],
    sn: [
      {
        validator: (_: any, _val: string) => {
          const imei = state.imei
            .trim()
            .split(',')
            .filter(s => !!s);
          const val = _val
            .trim()
            .split(',')
            .filter((s: any) => !!s);
          if ((!imei.length || val.length) && val.length !== Number(props.num)) {
            return Promise.reject(`当前已输入${val.length}个，应当输入${props.num}个`);
          }
          if (imei.length && val.length && imei.length !== val.length) {
            return Promise.reject('SN码数量需和IMEI码数量一致');
          }
          return Promise.resolve();
        },
      },
    ],
    other_code: [
      {
        validator: (_: any, _val: string) => {
          //props.codeType为1是sn，为2是imei
          const imei = splitAndFilter(state.imei);
          const sn = splitAndFilter(state.sn);
          const val = splitAndFilter(_val);
          if (!props.isMobilePhone) {
            // 不为手机品类且other_code有值才校验
            if (props.codeType === 2 && val.length && !val.every(validateImei)) {
              return Promise.reject('请输入正确格式的IMEI码（15位数字）');
            }
            return Promise.resolve();
          } else {
            if (!val.length) {
              return Promise.reject(`请输入${props.codeType === 1 ? 'SN码' : 'IMEI码'}`);
            }
          }
          if (props.codeType === 2) {
            if (!val.every(validateImei)) {
              return Promise.reject('请输入正确格式的IMEI码（15位数字）');
            }
            if ((!!props.isIphone || !sn.length || val.length) && val.length !== Number(props.num)) {
              return Promise.reject(`当前已输入${val.length}个，应当输入${props.num}个`);
            }
            if (sn.length && val.length && sn.length !== val.length) {
              return Promise.reject('IMEI码数量需和SN码数量一致');
            }
          } else {
            if ((!imei.length || val.length) && val.length !== Number(props.num)) {
              return Promise.reject(`当前已输入${val.length}个，应当输入${props.num}个`);
            }
            if (imei.length && val.length && imei.length !== val.length) {
              return Promise.reject('SN码数量需和IMEI码数量一致');
            }
          }
          return Promise.resolve();
        },
      },
    ],
  };
});

const state = reactive({
  imei: '',
  sn: '',
  other_code: '',
});

const errorImei = ref<string[]>([]);

const { validate, validateInfos, resetFields } = Form.useForm(state, rules);

watch(
  () => state.imei,
  () => validate('sn'),
);
watch(
  () => state.sn,
  () => validate('imei'),
);
watch(
  () => state.other_code,
  () => validate('other_code'),
);

const handleCancel = () => emit('update:visible', false);

const loading = ref(false);
const handleConfirm = () => {
  loading.value = true;
  const { imei, sn, other_code } = state;
  validate()
    .then(() => {
      const payload = {
        order_id: +props.orderId,
        imei: JSON.stringify(imei.split(',').filter(val => !!val)),
        sn: JSON.stringify(sn.split(',').filter(val => !!val)),
        other_code: JSON.stringify(other_code.split(',').filter(val => !!val)),
        ...props.otherParams,
      };
      if (props.codeType === 1) {
        payload.sn = JSON.stringify([]);
      } else {
        payload.imei = JSON.stringify([]);
      }
      postOrderBindCode(payload).then(() => {
        message.success('操作成功');
        emit('refresh', props.orderId);
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

watch(
  () => props.visible,
  val => {
    if (val) {
      const { codeType, cloudSn, cloudImei } = props;
      state.other_code = codeType === 1 ? cloudSn : cloudImei;
      if (codeType === 1) {
        state.imei = cloudImei;
      } else {
        state.sn = cloudSn;
      }
    } else resetFields();
  },
  {
    immediate: true,
  },
);
</script>
<style lang="less" scoped>
.base-info {
  margin-bottom: 24px;
  padding-bottom: 24px;
  color: rgba(6, 21, 51, 0.85);
  border-bottom: 1px solid rgba(6, 21, 51, 0.06);
}

.form {
  &:deep(.ant-form-item-label > label) {
    justify-content: end;
    width: 71px;
  }
}

.device-item {
  padding: 16px 24px 0;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;

  .title {
    margin-bottom: 12px;
    color: rgba(6, 21, 51, 0.45);
  }

  &:not(:last-of-type) {
    margin-bottom: 12px;
  }
}

.c-red {
  color: #d8281b;
}
</style>
