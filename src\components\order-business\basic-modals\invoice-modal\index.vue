<template>
  <a-modal
    v-model:visible="bindVisible"
    :mask-closable="false"
    title="修改发票抬头"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="发票抬头"
          name="invoice"
        >
          <a-textarea
            v-model:value="formData.invoice"
            :auto-size="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { ref } from 'vue';
import { useVModel } from '@/hook';
import { FormInstance, message } from 'ant-design-vue';
import { fetchGateway } from '@/components/order-business/basic-modals/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

interface IFormData {
  invoice?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  invoice: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const formData = ref<IFormData>({});
const bindVisible = useVModel(props, 'visible', emit);
const rules = {
  invoice: [{ type: 'string', required: true, message: '发票抬头不能为空', trigger: 'blur' }],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      formData.value = {
        invoice: props.invoice,
      };
    }
  },
  { immediate: true },
);

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      orderId: props.orderId,
      ordinary_delivery: 1,
      invoice: formData.value.invoice,
      method: 'v2.order.edit.invoice',
    };
    fetchGateway(params)
      .then(() => {
        message.success('操作成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data']);
      })
      .finally(() => (loading.value = false));
  });
}
</script>
