<template>
  <a-date-picker
    v-model:value="dateValue"
    v-bind="attrs"
    value-format="X"
  />
</template>

<script setup lang="ts">
import { computed, useAttrs } from 'vue';

import { formatRangeTime } from '@/utils/time';

defineOptions({
  inheritAttrs: true,
});

const props = defineProps<{
  value?: string[];
}>();

const emit = defineEmits<{
  (e: 'update:value', value?: string[]): void;
}>();

const dateValue = computed({
  get() {
    if (props.value) {
      return props.value[0];
    }
    return undefined;
  },
  set(value?: string) {
    const rangeDate = formatRangeTime([value * 1000, value * 1000]);
    emit('update:value', rangeDate);
  },
});

const attrs = useAttrs();
</script>
