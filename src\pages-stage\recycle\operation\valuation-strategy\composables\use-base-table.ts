import { ref, computed, Ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { keyToSelectConfigMap, computedRuleTableColumns } from '../config';
import { IComputedTableDataSource } from '../data.d';

export default function (props: any, isNowVersion: Ref<boolean>, isEdit: Ref<boolean>) {
  const stockColorMap = {
    1: 'danger',
    3: 'grey',
  };

  const dataSource = ref<IComputedTableDataSource[]>([]);

  // 动态调整表格列展示
  const computedColumns = computed(() => {
    const columns = cloneDeep(computedRuleTableColumns);
    const filterColumns = columns.filter((item: any) => {
      if (!['now_stock_text', 'stock_text'].includes(item.dataIndex) && !item.hasOwnProperty('isHidden')) {
        return true;
      } else {
        const { isHidden } = item;
        const val = item.dataIndex === 'now_stock_text' ? isNowVersion.value : !isEdit.value;
        const result = isHidden(val);
        return result;
      }
    });
    return filterColumns;
  });

  const computedOptions = computed(() => {
    return (curIndex: number, optionKey: string, key: string) => {
      const curOptions = cloneDeep(props.optionsManage[optionKey] || []);
      // 只有内存需要处理选项禁用
      if (['base2'].includes(key)) {
        let tableList: IComputedTableDataSource[] = dataSource.value.filter((item, index) => index !== curIndex);
        // 过滤出与当前颜色选择一致的数据
        const curBase1 = dataSource.value[curIndex]['base1'];
        tableList = tableList.filter(item => item['base1'] === curBase1);
        const valueArray: string[] = tableList.map(item => item[key]);
        // 校验已选选项进行禁用
        curOptions.forEach((item: { label: string; value: string; disabled?: boolean }) => {
          const { value } = item;
          item.disabled = valueArray.includes(value);
        });
      }
      return curOptions;
    };
  });

  // 过滤出有效的数据，用于校验sku情况
  function filterFullData() {
    return dataSource.value.filter(item => {
      const selectKeys = Object.keys(keyToSelectConfigMap);
      const selectIsOk = selectKeys.every(key => item[key]);
      return selectIsOk && typeof item['sku_base_price'] === 'number';
    });
  }

  return {
    stockColorMap,
    dataSource,
    computedColumns,
    computedOptions,
    filterFullData,
  };
}
