<template>
  <layout-admin-page
    :content-style="{ padding: '0', backgroundColor: '#f0f2f5' }"
    :navs="['趣回收', '订单管理', '企业回收订单']"
    title="企业回收订单"
    :top-style="{ marginBottom: 0 }"
  >
    <template #extra>
      <a-dropdown>
        <a-button
          class="green-btn"
          @click.prevent
        >
          <template #icon>
            <vertical-align-bottom-outlined />
          </template>
          导出
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <a
                href="javascript:;"
                @click="onExport('1')"
              >导出订单</a>
            </a-menu-item>
            <a-menu-item>
              <a
                href="javascript:;"
                @click="onExport('2')"
              >导出检测报告</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <div class="search-form">
      <a-form
        ref="formRef"
        layout="inline"
        :model="formState"
        :rules="formRules"
        style="width: 100%"
      >
        <a-row style="width: 100%">
          <a-col :span="6">
            <a-form-item
              label="订单状态"
              name="orderStatus"
            >
              <a-select
                v-model:value="formState.orderStatus"
                :options="orderStatusOptions"
                placeholder="请选择订单类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="检测结果"
              name="qualityResult"
            >
              <a-select
                v-model:value="formState.qualityResult"
                :options="qualityResultOptions"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="商家ID"
              name="serverId"
            >
              <a-input
                v-model:value="formState.serverId"
                :controls="false"
                :maxlength="18"
                placeholder="请输入商家ID"
                style="width: 100%"
                @press-enter="onHandleControl().search"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="订单ID"
              name="orderId"
            >
              <a-input
                v-model:value="formState.orderId"
                :controls="false"
                :maxlength="18"
                placeholder="请输入订单ID"
                style="width: 100%"
                @press-enter="onHandleControl().search"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row style="width: 100%; margin-top: 20px">
          <a-col :span="6">
            <a-form-item
              label="订单日期"
              name="time"
            >
              <a-range-picker
                v-model:value="formState.time"
                format="YYYY-MM-DD"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="检测imei码"
              name="imei"
            >
              <a-input
                v-model:value="formState.imei"
                :controls="false"
                :maxlength="18"
                placeholder="请输入imei"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="寄出运单号"
              name="tracking_num"
            >
              <a-input
                v-model:value="formState.tracking_num"
                :controls="false"
                :maxlength="18"
                placeholder="请输入寄出运单号"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="imei"
              name="orderImei"
            >
              <a-input
                v-model:value="formState.orderImei"
                :controls="false"
                :maxlength="18"
                placeholder="请输入imei"
                style="width: 100%"
                @press-enter="onHandleControl().search"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row style="width: 100%; margin-top: 20px">
          <a-col :span="6">
            <a-form-item
              label="取消原因"
              name="cancelReason"
            >
              <a-select
                v-model:value="formState.cancelReason"
                allow-clear
                :options="cancelReasonOptions"
                placeholder="请选择取消原因"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="取消类型"
              name="cancelRole"
            >
              <a-select
                v-model:value="formState.cancelRole"
                allow-clear
                :options="cancelRoleOptions"
                placeholder="请选择取消类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="订单协议类型"
              name="agreement_type"
            >
              <a-select
                v-model:value="formState.agreement_type"
                mode="multiple"
                :options="agreementOptions"
                placeholder="请选择订单类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="活动订单类型"
              name="activity_code"
            >
              <a-select
                v-model:value="formState.activity_code"
                :options="activeStatusOptions"
                placeholder="请选择订单类型"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row style="width: 100%; margin-top: 20px">
          <a-col :span="6">
            <a-form-item
              label="回收渠道"
              name="recycle_type"
            >
              <a-select
                v-model:value="formState.recycle_type"
                :options="typeOptions"
                placeholder="请选择回收渠道"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="分类"
              name="category_id"
            >
              <ProdutionCascader v-model:modelValue="formState.category_id" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="订单完成时间"
              name="complete_time"
            >
              <a-range-picker
                v-model:value="formState.complete_time"
                format="YYYY-MM-DD"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="订单类型"
              name="is_replace_device"
            >
              <a-select
                v-model:value="formState.is_replace_device"
                :options="[
                  { label: '代发设备回收', value: 1 },
                  { label: '非代发设备回收', value: 2 },
                ]"
                placeholder="请选择订单类型"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row style="width: 100%; margin-top: 20px">
          <a-col :span="6">
            <a-form-item>
              <a-button
                style="border-radius: 4px"
                type="primary"
                @click="onHandleControl().search"
              >
                查询
              </a-button>
              <a-button
                style="margin-left: 10px; border-radius: 4px"
                @click="onHandleControl().reset"
              >
                重置
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="list-mian padd-bottom">
      <customer-table
        :columns="listColumns"
        :data-source="tableList"
        :loading="isLoading"
        :table-thead-class="['recycle-sale-table-thead']"
      >
        <template #rowHeader="{ record }">
          <div class="order-head">
            <a-checkbox
              v-model:checked="record.checked"
              @change="handleCheckBox"
            />
            <span class="order-text">订单号：{{ record.order_id }}</span>
            <span class="order-text">创建日期：{{ record.created_at }}</span>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'goods_info'">
            <!-- 商品信息 -->
            <div class="goods-info">
              <a-image
                :src="record.goods_image"
                :width="60"
              />
              <span style="padding-left: 8px">{{ record.goods_name }}</span>
            </div>
          </template>
          <template v-if="column.key === 'logisticInfo'">
            <!-- 物流信息 -->
            <div class="logistic-info">
              <div>
                <span class="label">寄出：</span>
                <span v-if="record.logisticInfo.tracking_num">
                  <a
                    class="a-link-hover"
                    style="padding: 0"
                    @click="() => onSeeLogisticDetail(false, record.logisticInfo, record)"
                  >
                    {{ record.logisticInfo.tracking_num }}
                  </a>
                  <DecryptField
                    v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                    :field-item="{
                      box: 'text',
                      method: 'click',
                      id: record.order_id,
                      field: 'tracking_num',
                      field_type: 'SF',
                      type: 135,
                    }"
                    @get-view-data="value => getViewData(value, record.logisticInfo.tracking_num_text)"
                  />
                </span>
                <span v-else>暂无数据</span>
              </div>
              <div>
                <span class="label">退回：</span>
                <span v-if="record.returnLogisticInfo.return_tracking_num">
                  <a
                    class="a-link-hover"
                    style="padding: 0"
                    @click="() => onSeeLogisticDetail(false, record.returnLogisticInfo, record)"
                  >
                    {{ record.returnLogisticInfo.return_tracking_num }}
                  </a>
                  <DecryptField
                    v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                    :field-item="{
                      box: 'text',
                      method: 'click',
                      id: record.order_id,
                      field: 'return_tracking_num',
                      field_type: 'SF',
                      type: 135,
                    }"
                    @get-view-data="value => getViewData(value, record.returnLogisticInfo.return_tracking_num_text)"
                  />
                </span>
                <span v-else>暂无数据</span>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'evaluate_price'">
            <!-- 估计结果 -->
            <div class="evaluate-price">
              <div class="evaluate-price-block">
                <div
                  v-if="!record.evaluate_advance_price"
                  class="evaluate-price-block-non"
                >
                  <span class="evaluate-price-text">估价金额</span>
                  <span class="evaluate-price-text">¥{{ makeDecimalPoint(record.evaluate_price) }}</span>
                </div>
                <div v-else>
                  <div class="evaluate-price-text">
                    预估回收价：¥{{ makeDecimalPoint(record.evaluate_price) }}
                  </div>
                  <span class="evaluate-price-text-two">（估价金额：¥{{ makeDecimalPoint(record.evaluate_base_price) }}，补贴金额：¥{{
                    makeDecimalPoint(record.evaluate_advance_price)
                  }}）</span>
                </div>
                <div class="evaluate-device-type">
                  {{ record.is_replace_device_trans }}
                </div>
                <a
                  class="a-link-hover"
                  @click="() => onSeeEvaluate(record.order_id)"
                > 查看估价报告 </a>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'fixprice_info'">
            <!-- 定价结果 -->
            <div class="evaluate-price">
              <div
                v-if="Number(record.showBtnViewQuaReport) === 1"
                class="evaluate-price-block"
              >
                <span class="evaluate-price-text">实际回收价：¥{{ makeDecimalPoint(record.purchased_price) }}</span>
                <span
                  v-if="record.advance_purchased_price"
                  class="evaluate-price-text-two"
                >（定价金额：¥{{ makeDecimalPoint(record.base_purchased_price) }}，补贴金额：¥{{
                  makeDecimalPoint(record.advance_purchased_price)
                }}）</span>
                <span
                  v-else
                  class="evaluate-price-text-two"
                >（定价金额：¥{{ makeDecimalPoint(record.base_purchased_price) }}）</span>
                <a
                  class="a-link-hover"
                  @click="() => onSeeQuality(record)"
                > 查看检测结果 </a>
              </div>
              <span
                v-else
                class="evaluate-price-text"
              >暂无定价</span>
            </div>
          </template>
          <template v-if="column.key === 'quality_result_text'">
            <!-- 检测结果 -->
            <div class="quality-result-text">
              {{ record.quality_result_text || '/' }}
            </div>
          </template>
          <template v-if="column.key === 'order_status'">
            <!-- 订单状态 -->
            <div class="order-status">
              <div>{{ record.order_status_text }}</div>
              <template v-if="record.cancel_reason_role">
                <div>取消类型：{{ record.cancel_reason_role }}</div>
                <div>取消原因：{{ record.cancel_reason_text }}</div>
              </template>
            </div>
          </template>
          <template v-if="column.key === 'operation'">
            <!-- 操作 -->
            <!-- <operations /> -->
            <div class="operation">
              <a-button
                v-if="record.canSetPurchasedPrice"
                class="text-blue"
                type="link"
                @click="() => onPricing(record.order_id)"
              >
                定价
              </a-button>
              <a-button
                v-if="!!record.showOrderPayBtn"
                class="text-blue"
                type="link"
                @click="() => onSeebill(record.order_id)"
              >
                查看账单
              </a-button>
              <a-button
                v-if="Number(record.canCloseOrder) === 1"
                danger
                type="link"
                @click="() => onCloseOrder(record.order_id)"
              >
                关闭订单
              </a-button>
              <a-button
                v-if="record.order_protocol_url"
                type="link"
                @click="openEvidence({ url: record.order_protocol_url, time: record.order_protocol_created_at })"
              >
                查看证据
              </a-button>
              <a-button
                v-if="record.protocol_url"
                type="link"
                @click="openView(record.protocol_url)"
              >
                订单协议
              </a-button>
              <a-button
                v-if="record.showBtnOrderSf"
                class="text-blue"
                type="link"
                @click="() => onDelivery(record.order_id)"
              >
                顺丰取件
              </a-button>
              <a-button
                v-if="record.showBtnCancelOrderSf"
                danger
                type="link"
                @click="() => onCancelDelivery(record.order_id)"
              >
                取消预约
              </a-button>
            </div>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <tr class="expanded-row">
            <td :colspan="listColumns.length">
              <div class="remark">
                <span class="merchant-info-text"> 商家ID:{{ record.server_id }} </span>
                <span class="merchant-info-text">
                  商家手机:{{ record.phone }}

                  <DecryptField
                    v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                    :field-item="{
                      box: 'eye',
                      id: record.order_id,
                      field: 'phone',
                      field_type: 'merchant_phone',
                      type: 135,
                    }"
                  />
                </span>
                <span class="merchant-info-text"> 收款账号:{{ record.payment_account }}, </span>
                <span class="merchant-info-text">
                  收款人姓名:{{ record.payment_name }}

                  <DecryptField
                    v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                    :field-item="{
                      box: 'eye',
                      id: record.order_id,
                      field: 'payment_name',
                      field_type: 'name',
                      type: 135,
                    }"
                  />,
                </span>
                <span class="merchant-info-text">
                  商家收件地址:{{ record.logisticInfo?.addr }}

                  <DecryptField
                    v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                    :field-item="{
                      box: 'eye',
                      id: record.order_id,
                      field: 'addr',
                      field_type: 'merchant_address',
                      type: 135,
                    }"
                  />,
                </span>
              </div>
            </td>
          </tr>
          <!-- 备注栏 -->
          <tr class="expanded-row">
            <td :colspan="listColumns.length">
              <div class="remark">
                <crm-remark
                  :id="`${record.order_id}_recySerBill`"
                  :block-flex="false"
                  :item="record.remarks"
                  layout="horizontal"
                  log-link="/crm/log"
                  push-link="/crm/push"
                  size="block"
                  @add-success="onLoadList"
                >
                  <template #emptyText>
                    暂无备注
                  </template>
                </crm-remark>
              </div>
            </td>
          </tr>
        </template>
      </customer-table>
      <!-- 分页 -->
      <list-pagination
        :page="page"
        @table-change="tableChange"
      />
    </div>
  </layout-admin-page>
  <pricing-modal
    v-if="pricingModalVisible"
    v-model:visible="pricingModalVisible"
    :order-id="currentOrderId"
    @on-load-list="onLoadList"
  />
  <delivery-modal
    v-if="deliveryModalVisible"
    v-model:visible="deliveryModalVisible"
    :order-id="currentOrderId"
    @on-load-list="onLoadList"
  />
  <bulk-delivery-modal
    v-if="balkdeliveryModalVisible"
    v-model:visible="balkdeliveryModalVisible"
    :order-id="selectedRowKeys.map((item:any) => item.order_id)"
    @on-load-list="handleonLoadList"
  />

  <evaluate-modal
    v-if="evaluateModalVisible"
    v-model:visible="evaluateModalVisible"
    :order-id="currentOrderId"
    request-url="/super/recycle/batch-order/get-device-info"
  />
  <QuaReportModal
    v-model:visible="qualityState.visible"
    :payload="qualityState.payload"
  />
  <LogisticDraw
    v-model:visible="logisticDrawVisible"
    :logistic-info="logisticInfo"
  />
  <EvidenceModal
    v-model:visible="evidenceModalVisible"
    :evidence-obj="evidenceObj"
  />
  <div class="fix-bottom-box">
    <div class="checkout-block">
      <a-checkbox
        v-model:checked="allChecked"
        :indeterminate="indeterminate"
        @change="handleAllCheck"
      >
        全选 ｜ 已选{{ selectedRowKeys.length }}项
      </a-checkbox>
    </div>
    <a-button
      :disabled="selectedRowKeys.length === 0"
      style="margin-left: 8px; border-radius: 4px"
      type="primary"
      @click="handleBulkReturn"
    >
      批量退货
    </a-button>
  </div>
</template>
<script lang="ts" setup>
import { createVNode, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { FormInstance, message, Modal } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { AxiosResponse } from 'axios';
import dayjs from 'dayjs';

import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
import QuaReportModal from '@/components/quality-report-wrapper/qua-report-modal.vue';
import { useViewPlaintextAuth } from '@/composables/use-decrypt-data';
import { useTable } from '@/hook/component/use-table';
import CustomerTable from '@/pages-stage/recycle/common/components/customer-table.vue';
import useCeiling from '@/pages-stage/recycle/common/use-ceiling';
import useExportModalConfirm from '@/pages-stage/recycle/finance/self-order-pay/use-export-modal-confirm';
import LogisticDraw from '@/pages-stage/recycle/order/self-order-list/components/logistic-drawer.vue';
import EvaluateModal from '@/pages-stage/recycle/recycle-manage/batch-recycle/components/evaluate-modal.vue';
import { POST } from '@/services/api';

import useBoolean from '../../common/use-boolean';
import BulkDeliveryModal from './components/bulk-delivery-modal.vue';
import DeliveryModal from './components/delivery-modal.vue';
import ListPagination from './components/list-pagination.vue';
import PricingModal from './components/pricing-modal.vue';
import { listColumns } from './config';
import { cancelDelivery, closeOrder, exportList, getBatchStatusOptions, getTableList } from './service';

const { viewPlaintextAuth } = useViewPlaintextAuth();
import useSensitiveLookModel from '@/composables/use-sensitive-look-model';
import ProdutionCascader from '@/pages-stage/recycle/base-data/commodity-list/components/prodution-cascader.vue';

import { agreementOptions } from '../../recycle-manage/recycle-cart/config';
import EvidenceModal from './components/evidence-modal.vue';

const route = useRoute();

const [pricingModalVisible, { setTrue: showPricingModal }] = useBoolean();

const [deliveryModalVisible, { setTrue: showDeliveryModal }] = useBoolean();

const [balkdeliveryModalVisible, { setTrue: showBalkDeliveryModal }] = useBoolean();

const [evaluateModalVisible, { setTrue: showEvaluateModal }] = useBoolean();

const [logisticDrawVisible, { setTrue: showLogisticDraw }] = useBoolean();

const [evidenceModalVisible, { setTrue: showEvidenceModal }] = useBoolean();

const allChecked = ref(false);
const indeterminate = ref(false);
const currentOrderId = ref('');
const selectedRowKeys = ref<any>([]);
const orderStatusOptions = ref([]);
const activeStatusOptions = ref([]);
const cancelReasonOptions = ref([]);
const cancelRoleOptions = ref([]);
const typeOptions = ref([]);
const qualityResultOptions = [
  { value: '', label: '全部' },
  { value: '1', label: '合格' },
  { value: '2', label: '不合格' },
];

const qualityState = ref({
  visible: false,
  payload: {},
});

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  qualityResult: '',
  orderStatus: 'all',
  serverId: '',
  orderId: '',
  tracking_num: '',
  imei: '',
  time: [],
  orderImei: '',
  cancelReason: undefined,
  cancelRole: undefined,
  agreement_type: [],
  activity_code: undefined,
  recycle_type: undefined,
  category_id: [],
  complete_time: [],
  is_replace_device: undefined,
});

// 分页器
let pagination = reactive({
  page: 1,
  page_size: 10,
  count: 0,
});

const { list: tableList, listLoading: isLoading, page, getTableList: onLoadList, tableChange } = useTable<any, any>({
  api: getTableList,
  totalKey: 'data.pageInfo.count',
  searchForm: formState,
  formatSearchValue: (params: any) => {
    const newParams = {
      ...params.value,
      startAt: (params.value.time && params.value.time[0]) || '',
      endAt: (params.value.time && params.value.time[1]) || '',
      complete_start_at: (params.value.complete_time && params.value.complete_time[0]) || '',
      complete_end_at: (params.value.complete_time && params.value.complete_time[1]) || '',
    };
    delete newParams.time;
    delete newParams.complete_time;
    return newParams;
  },
  formatHandle: async (res: AxiosResponse<{ list: any[] }>) => {
    const ids = res.data.list.map((item: any) => item.order_id);
    let result = await POST('/crm/data', { unionIds: ids, unionSuffix: '_recySerBill' });
    let marks = {};
    result.data.forEach((item: any) => {
      marks[item.union_id] = item;
    });
    res.data.list.forEach((item: any) => {
      item.remarks = marks[`${item.order_id}_recySerBill`];
    });
    return res.data.list;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const onHandleControl = () => ({
  search() {
    pagination.page = 1;
    onLoadList();
    resetSelectedKey();
  },
  reset() {
    // 1.重置所有字段
    formRef.value?.resetFields();
    // 2. 初始化页码
    pagination.page = 1;
    pagination.page_size = 10;
    // 3. 重新获取table数据
    onLoadList();
    resetSelectedKey();
  },
});

const onPricing = (order_id: string) => {
  currentOrderId.value = order_id;
  showPricingModal();
};

// 查看账单
const onSeebill = (order_id: string) => {
  window.parent.postMessage(
    {
      action: 'blank',
      href: `/super/recycle/server-bill/index?orderId=${order_id}`,
    },
    '*',
  );
};

// 查看评估
const onSeeEvaluate = (order_id: string) => {
  currentOrderId.value = order_id;
  showEvaluateModal();
};

// 查看检测
const onSeeQuality = (record: Record<string, any>) => {
  const { device_batch_no: batch_no, device_type, device_unicode: device_code } = record;
  qualityState.value = {
    visible: true,
    payload: {
      device_code,
      batch_no,
      device_type,
    },
  };
};

// 顺丰取件
const onDelivery = (order_id: string) => {
  currentOrderId.value = order_id;
  showDeliveryModal();
};

// 取消预约
const onCancelDelivery = (order_id: string) => {
  Modal.confirm({
    title: '确定要取消预约吗？',
    onOk() {
      cancelDelivery({ order_id, order_type: 1 }).then((res: any) => {
        message.success(res.message || '操作成功');
        onLoadList();
      });
    },
  });
};

// 关闭订单
const onCloseOrder = (order_id: string) => {
  Modal.confirm({
    title: '是否关闭此订单?',
    content: createVNode(
      'div',
      {
        style: 'color: rgba(0,0,0,0.65);',
      },
      '关闭订单后将不能恢复，确认关闭订单吗？',
    ),
    onOk() {
      return closeOrder(order_id).then(() => {
        message.success('订单已关闭');
        onLoadList();
      });
    },
  });
};

type TEvideceObj = {
  url: string;
  time: string;
};

const evidenceObj = ref<TEvideceObj>(); //查看证据
const openEvidence = (params: TEvideceObj) => {
  evidenceObj.value = params;
  showEvidenceModal();
};

const onExport = (type: string) => {
  //导出订单
  const { down: downOrder } = useExportModalConfirm(
    async () => {
      const res = await exportList({
        orderStatus: formState.value.orderStatus,
        serverId: formState.value.serverId,
        orderId: formState.value.orderId,
        qualityResult: formState.value.qualityResult,
        tracking_num: formState.value.tracking_num,
        imei: formState.value.imei,
        startAt: formState.value.time[0] ? dayjs(formState.value.time[0]).format('YYYY-MM-DD') : '',
        endAt: formState.value.time[1] ? dayjs(formState.value.time[1]).format('YYYY-MM-DD') : '',
        orderImei: formState.value.orderImei,
        cancelReason: formState.value.cancelReason,
        cancelRole: formState.value.cancelRole,
        agreement_type: formState.value.agreement_type,
        export_type: type,
        recycle_type: formState.value.recycle_type,
        complete_start_at: formState.value.complete_time[0]
          ? dayjs(formState.value.complete_time[0]).format('YYYY-MM-DD')
          : '',
        complete_end_at: formState.value.complete_time[1]
          ? dayjs(formState.value.complete_time[1]).format('YYYY-MM-DD')
          : '',
        category_id: formState.value.category_id,
        is_replace_device: formState.value.is_replace_device,
      });
      return res;
    },
    `${route.query.origin}/super/async-export/index`,
    {
      title: `企业${type === '1' ? '订单数据' : '检测报告'}导出`,
      content: `请确认是否导出该订单${type === '1' ? '数据' : '检测报告'}？`,
    },
  );
  downOrder();
};

// 查看物流信息
const logisticInfo = ref();
const onSeeLogisticDetail = (isReturn: boolean, _logisticInfo: any, record: any) => {
  logisticInfo.value = {
    isReturn,
    ..._logisticInfo,
    order_id: record.order_id,
    showViewHideBtn: viewPlaintextAuth.value.recycleShowViewHideBtn,
    type: '企业',
  };
  showLogisticDraw();
};
const onBaltDelivery = () => {
  //批量新弹窗
  showBalkDeliveryModal();
};
//批量顺丰取件
const handleBulkReturn = () => {
  const visibleServer = Array.from(new Set(selectedRowKeys.value.map((item: any) => item.server_id)));
  const visible = selectedRowKeys.value.filter((item: any) => item.showBtnOrderSf === 0);
  if (visible.length > 0) {
    Modal.warning({
      title: '提示',
      content: createVNode(
        'div',
        {
          style: 'color: rgba(0,0,0,0.65);',
        },
        '请检查所选订单状态，批量退货的订单需要为在库设备？',
      ),
    });
  } else {
    if (visibleServer.length > 1) {
      Modal.confirm({
        title: '确认',
        content: createVNode(
          'div',
          {
            style: 'color: rgba(0,0,0,0.65);',
          },
          '您选择的退货设备中，同时包含了多个商家设备，请确认是否要继续完成批量退货？',
        ),
        onOk() {
          onBaltDelivery();
        },
      });
    } else {
      onBaltDelivery();
    }
  }
};
const resetSelectedKey = () => {
  selectedRowKeys.value = [];
};
const handleonLoadList = () => {
  onLoadList();
  resetSelectedKey();
};
const handleCheckBox = () => {
  selectedRowKeys.value = tableList.value.filter(item => item.checked);
  indeterminate.value = !!selectedRowKeys.value.length && selectedRowKeys.value.length < tableList.value.length;
};
const handleAllCheck = (e: any) => {
  const { checked } = e.target;
  tableList.value.forEach((ite: any) => {
    ite.checked = checked;
  });
  handleCheckBox();
};
//当小数点后面为00时，去掉
const makeDecimalPoint = (num: string) => {
  if (num.indexOf('.') !== -1) {
    const numArr = num.split('.');
    if (numArr[1] === '00') {
      return numArr[0];
    } else {
      return num;
    }
  } else {
    return num;
  }
};
const validator18Num = (rule: Rule, id: string) => {
  if (!isNaN(parseFloat(id)) && isFinite(id)) {
    return Promise.resolve();
  }
  formState.value[rule.field] = '';
  return Promise.reject('请输入数字且长度不超过18个字符!');
};
const formRules = {
  orderImei: [{ required: false, validator: validator18Num, trigger: 'change' }],
  serverId: [{ required: false, validator: validator18Num, trigger: 'change' }],
};
const getViewData = (value, field) => {
  useSensitiveLookModel(field);
};

// 打开协议
const openView = (url: string) => {
  window.open(url);
};

const getUrlParams = () => {
  const query = route.query || {};
  const keysArr = Object.keys(query).filter(key => !['role', 'origin'].includes(key));
  if (keysArr.length) {
    keysArr.forEach(key => {
      const value = query[key];
      switch (key) {
        case 'startTime':
          formState.value.complete_time[0] = dayjs(value).format('YYYY-MM-DD');
          break;
        case 'endTime':
          formState.value.complete_time[1] = dayjs(value).format('YYYY-MM-DD');
          break;
        default:
          if (formState.value.hasOwnProperty(key)) {
            formState.value[key] = value;
          }
          break;
      }
    });
  }
};

onMounted(() => {
  getUrlParams();
  getBatchStatusOptions().then(({ data }) => {
    orderStatusOptions.value = data.orderStatus.map(item => {
      item.value = String(item.value);
      return item;
    });
    cancelReasonOptions.value = data.cancelReason;
    cancelRoleOptions.value = data.cancelRole;
    activeStatusOptions.value = data.activityCode;
    typeOptions.value = data.recycleType;
    onLoadList();
    useCeiling();
  });
});
</script>
<style lang="less" scoped>
@import '../../common/base.less';

:deep(.text-blue) {
  // 文字颜色调整为蓝色
  &.ant-btn-link {
    color: #3777ff !important;
  }
}

.a-text-blue {
  color: #3777ff !important;
}

.fix-bottom-box {
  position: fixed;
  bottom: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 64px;
  background: #fff;

  .checkout-block {
    position: absolute;
    left: 48px;
  }
}

.padd-bottom {
  padding-bottom: 64px;
}

.acton-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-mian {
  width: 100%;
  height: 100%;
  padding: 16px;

  .page-title {
    padding-top: 24px;

    .page-title-text {
      margin: 0;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 20px;
      /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
      font-family: PingFangSC-Medium, PingFang SC;
      line-height: 32px;
    }
  }
}

.list-mian {
  width: 100%;
  margin-top: 16px;
  padding-top: 16px;
  //padding: 16px;
  background: #fff;

  .order-head {
    display: flex;
    align-items: center;
    width: 100%;
    height: 54px;
    padding-left: 20px;
    background: rgba(6, 21, 51, 0.02);

    .order-text {
      margin-left: 32px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 14px;
    }
  }

  .goods-info {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
    background: #fff;
  }

  .logistic-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
  }

  .evaluate-price {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;

    .evaluate-price-block {
      display: flex;
      flex-direction: column;
    }
  }

  .order-status {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
    background: #fff;
  }

  .quality-result-text {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
    background: #fff;
  }

  .operation {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.merchant-info-text {
  margin-right: 8px;
}

:deep(.recycle-sale-table-thead tr th) {
  background: #fafafb !important;
}

.expanded-row {
  border-top: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;

  > td {
    padding: 16px;
  }

  &-td {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.a-link-hover {
  color: #061533d9;
  font-weight: 400;
  font-size: 14px;
  text-decoration: underline;
}

.a-link-hover:hover {
  color: #3777ff;
  font-weight: 400;
  font-size: 14px;
  text-decoration: underline;
}

.evaluate-price-text2 {
  color: #00c8be;
}

:deep(.ant-checkbox-indeterminate .ant-checkbox-inner::after) {
  height: 2px !important;
}

:deep(.spacing) {
  height: 16px;
}

:deep(.customer-table) {
  padding: 0 24px 24px;
}

.search-form {
  margin-bottom: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
}

.evaluate-price-text-two {
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
}

.evaluate-price-block-non {
  display: flex;
  flex-direction: column;
}

.evaluate-device-type {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
</style>
