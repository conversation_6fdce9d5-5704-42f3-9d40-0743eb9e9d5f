import type { TableColumnType } from 'ant-design-vue';

export const columns: (TableColumnType & { tooltip?: string; discountKey?: string })[] = [
  {
    dataIndex: 'trade_no',
    title: '支付流水号',
    width: 250,
  },
  {
    dataIndex: 'receipt_amount',
    title: '支付金额',
    width: 110,
    tooltip: '支付金额与订单金额不一致时，表示当前流水受支付宝优惠影响，支付金额即【实付金额】',
  },
  {
    dataIndex: 'money',
    title: '订单金额',
    width: 150,
    tooltip: '订单金额即流水优惠前的金额',
  },
  {
    dataIndex: 'conducted_has_refund',
    title: '在途退款金额',
    width: 150,
    discountKey: 'record_conducted_refund_discount',
    tooltip: '申请退款但未退款完成的金额',
  },
  {
    dataIndex: 'total_has_refund',
    title: '总已退款金额',
    width: 150,
    discountKey: 'record_refund_discount',
    tooltip: '退款完成的金额',
  },
  {
    dataIndex: 'surplus_has_refund',
    title: '剩余可退款金额',
    width: 150,
    discountKey: 'surplus_has_refund_discount',
    tooltip: '剩余可退金额=【订单金额】-【已退款金额】-【在途退款金额】',
  },
  {
    dataIndex: 'money_type_text',
    title: '流水类型',
    width: 100,
  },
  {
    dataIndex: 'bill_id_text',
    title: '账单ID',
    width: 130,
  },
  {
    dataIndex: 'created_at_text',
    title: '支付时间',
    width: 200,
  },
];
