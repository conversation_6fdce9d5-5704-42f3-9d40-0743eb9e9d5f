import { FormGroupItem } from 'rrz-web-design';

/** 多级表单 */
export interface IFormListGroup<T = any, D = Array<any>> {
  /** 表单分组名称 */
  title?: string;
  /** 表单分组项 */
  items: FormGroupItem<T, D>[];
}

/** 搜索表单 */
export type TFormGroup<T = any, D = Array<any>> = FormGroupItem<T, D> | IFormListGroup<T, D>;

/** 路由参数配置项 */
export interface IQueryGroupItem {
  /** 参数名 */
  key: string;
  /** 有参数时页面内渲染的tag文本 */
  text?: string | ((value: string) => string);
}

/** 表格标签栏配置项 */
export interface ITabGroupItem {
  /** 标签名 */
  label: string;
  /** 对应请求项key，无时取状态名做key */
  value: string | number;
}

/** 对象类型 */
export type TObject = Record<string, any>;

/** 分页配置 */
export interface IPagination {
  /** 当前页 */
  current?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总条数 */
  total?: number;
  /** 显示类型，简易simple，可控control，简易模式下也需要传total，至少得是页面的数量，作为可否翻页的判断 */
  type?: 'default' | 'simple' | 'control';
}
