<template>
  <layout-admin-page
    :navs="['趣回收', '经营管理', '回收估价分析表']"
    title="回收估价分析表"
  >
    <div class="page-body">
      <form-create
        ref="searchFormRef"
        v-model:value="searchInfo"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="getTableList('search')"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            type="default"
            @click="onReset"
          >
            重置
          </a-button>
        </template>
      </form-create>
      <a-table
        class="bottom-fix-table table-wrapper"
        :columns="parentColumns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        row-key="date"
        :scroll="{ x: 944 }"
        sticky
        @change="tableChange"
      >
        <template #headerCell="{ column }">
          <template v-if="column.tipsText">
            {{ column.title }}
            <a-tooltip placement="top">
              <template #title>
                <span>{{ column.tipsText }}</span>
              </template>
              <question-circle-outlined style="color: grey; font-size: 16px" />
            </a-tooltip>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-table
            :columns="childColumns"
            :data-source="record.sku_list"
            :pagination="false"
          />
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="linkDataIndexList.includes(column.dataIndex)">
            <span
              :class="{ 'link-color': record[column.dataIndex] && hasSelectSourceType }"
              @click="openPage(record, column.dataIndex)"
            >{{ record[column.dataIndex] }}</span>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useTable } from '@/hook/component/use-table';
import { ISearch, IDataSource } from './data.d';
import { searchFormGroup, parentColumns, childColumns } from './config';

const linkDataIndexList = ['finish_num', 'finish_price'];

const searchFormRef = ref<any>(null);

const searchInfo = reactive<ISearch>({
  source_type: '0',
});
// 记录上一次触发搜索所选的数据来源
const oldSourceType = ref<string>(searchInfo.source_type);
const hasSelectSourceType = computed(() => {
  // 需要选中个人、企业时才命中
  return ['1', '2'].includes(oldSourceType.value);
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable<IDataSource, any>({
  url: '/super/recycle/data-count/evaluate-analysis-list',
  searchForm: searchInfo,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatSearchValue: (params: ISearch) => {
    oldSourceType.value = params.source_type;
    return params;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const onReset = () => {
  searchFormRef.value.formRef.resetFields();
  getTableList('search');
};

// 跳转个人/企业订单列表、个人/企业对账单
const openPage = (record: IDataSource, key: string) => {
  if (!record[key] || !hasSelectSourceType.value) return;

  const orderPagePathMap = {
    1: '/super/recycle/order/index',
    2: '/super/recycle/batch-order/index',
  };
  const billPagePathMap = {
    1: '/super/recycle/order-pay/index',
    2: '/super/recycle/server-bill/index',
  };
  const pathMap = key === 'finish_num' ? orderPagePathMap : billPagePathMap;
  const url = pathMap[searchInfo.source_type];
  const dateText = dayjs(record.date).format('YYYY-MM-DD');

  const otherParams = key === 'finish_num' ? '&orderStatus=10' : '';

  const href = `${url}?startTime=${dateText}&endTime=${dateText}${otherParams}`;

  window.parent.postMessage(
    {
      action: 'blank',
      href,
    },
    '*',
  );
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
.page-body {
  padding: 0 24px;
}
.table-wrapper {
  margin-top: 24px;
}
.link-color {
  color: #3777ff;
  cursor: pointer;
}
</style>
