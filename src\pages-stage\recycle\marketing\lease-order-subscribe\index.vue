<template>
  <layout-admin-page
    :navs="['趣回收', '营销', '租赁订单预约回收']"
    title="租赁订单预约回收"
  >
    <div class="container">
      <a-tabs
        v-model:activeKey="searchForm.status"
        class="lease-order-subscribe-tabs"
        @change="getTableList('search')"
      >
        <a-tab-pane
          v-for="item in tabList"
          :key="item.value"
          :tab="item.label"
        />
      </a-tabs>
      <div
        v-if="searchForm.status === '4'"
        class="container-status-btn"
      >
        <a-radio-group
          v-model:value="searchForm.status_child"
          button-style="solid"
          @change="getTableList('search')"
        >
          <a-radio-button
            primary
            value="10"
          >
            规则匹配失败
          </a-radio-button>
          <a-radio-button value="11">
            状态匹配失败
          </a-radio-button>
        </a-radio-group>
      </div>
      <customer-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
      >
        <template #rowHeader="{ record }">
          <order-head
            :common-head="record.headerData"
            :order-id="record.foundationData.order_id"
          />
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'good_info'">
            <goods-info
              :item-data="record.itemData"
              :other="record.otherData"
              :tenancy="record.tenancyTagData"
            />
          </template>
          <template v-if="column.key === 'deposit_rent_quantity'">
            <rent-info
              :deposit="record.depositData"
              :foundation-data="record.foundationData"
              :other="record.otherData"
              :overdue-record="record.rentalData"
              :rental="record.rentalData"
            />
          </template>
          <template v-if="column.key === 'actual_payment'">
            <disbursements
              :actual-pay="record.actualPayData"
              :tenancy="record.tenancyTagData"
            />
          </template>
          <template v-if="column.key === 'user_address'">
            <user-address
              :order-id="record.foundationData.order_id"
              :receiving-data="record.receivingData"
            />
          </template>
          <template v-if="column.key === 'transaction_status'">
            <span
              class="font--color"
              style="padding-left: 16px"
            >{{ record.tranStateData.order_status_txt }}</span>
          </template>
          <template v-if="column.key === 'handle'">
            <div style="padding: 0 20px">
              <div :class="['status-block', statusColor[ruleOrderMap[record.foundationData.order_id].status]]">
                {{ ruleOrderMap[record.foundationData.order_id].status_text }}
              </div>
              <a-button
                v-if="['1', '2'].includes(ruleOrderMap[record.foundationData.order_id].status)"
                type="primary"
                @click="onSubscribe(record)"
              >
                {{ ruleOrderMap[record.foundationData.order_id].status === '1' ? '预约回收' : '取消预约回收' }}
              </a-button>
              <!-- 预约成功 -->
              <template v-if="ruleOrderMap[record.foundationData.order_id].status === '3'">
                <a-button
                  style="margin-bottom: 8px"
                  type="primary"
                  @click="onToPage(record)"
                >
                  查看回收订单
                </a-button>
                <a-button
                  type="primary"
                  @click="onToPageV3Order(record)"
                >
                  查看租赁订单
                </a-button>
              </template>
            </div>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <tr class="expanded-row">
            <td
              :colspan="columns.length"
              style="padding: 0"
            >
              <!-- 备注 -->
              <div>
                <div class="expanded-row-td">
                  <crm-remark
                    :id="`${record.foundationData.order_id}_recyBROrdLi`"
                    :block-flex="false"
                    :item="record.remarks"
                    layout="horizontal"
                    log-link="/crm/log"
                    push-link="/crm/push"
                    size="block"
                    @add-success="getTableList"
                  >
                    <template #emptyText>
                      暂无备注
                    </template>
                  </crm-remark>
                </div>
              </div>
            </td>
          </tr>
        </template>
      </customer-table>
      <div class="pagination-box">
        <div class="total-box">
          <span class="total-num">共<span class="value">{{ pagination.total }}</span>条记录</span>
          <span class="page-num">第<span class="value">{{ pagination.page }}/{{ Math.ceil(pagination.total / pagination.pageSize) }}</span>页</span>
        </div>
        <list-pagination
          class="pagination"
          :current="pagination.page"
          :list-length="dataSource.length"
          :page-size="pagination.pageSize"
          show-quick-jumper
          :total="pagination.total"
          @change="tableChange"
        />
      </div>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { createVNode, reactive, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { AxiosResponse } from 'axios';

import { useTable } from '@/hook/component/use-table';
import { POST } from '@/services/api';

import CustomerTable from './components/customer-table/index.vue';
import Disbursements from './components/disbursements/index.vue';
import GoodsInfo from './components/goods-info/index.vue';
import ListPagination from './components/list-pagination/index.vue';
import OrderHead from './components/order-head/index.vue';
import RentInfo from './components/rent-info/index.vue';
import UserAddress from './components/user-address/index.vue';
import { columns, tabList } from './config';
import { orderBook, orderCancelBook } from './service';

interface IRuleOrder {
  id: string;
  recycle_order_id: string;
  status: string;
  status_text: string;
}

const searchForm = reactive({
  status: '1',
  status_child: '10',
});

const pagination = reactive({
  page: 1,
  pageSize: 15,
  total: 0,
});

const ruleOrderMap = ref<Partial<IRuleOrder>>({});
const statusColor = {
  1: 'primary',
  2: 'finish',
  3: 'finish',
};

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  url: '/booking-recycle-rule-order/list',
  searchForm,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatSearchValue: () => {
    const params: any = {
      'status': searchForm.status,
      'page': pagination.page,
      'per-page': pagination['pre-page'],
    };
    if (searchForm.status === '4') {
      params['status_child'] = searchForm.status_child;
    }
    return params;
  },
  formatHandle: async (res: AxiosResponse<any>) => {
    const { data } = res;
    const { pageInfo, ruleOrderMap: ruleOrderMapData } = data;
    pagination.page = pageInfo.page;
    pagination.pageSize = pageInfo['per-page'];
    pagination.total = pageInfo.count;
    ruleOrderMap.value = ruleOrderMapData;
    const ids = res.data.list.map((item: any) => item.foundationData.order_id);
    let result = await POST('/crm/data', { unionIds: ids, unionSuffix: '_recyBROrdLi' });
    let marks = {};
    result.data.forEach((item: any) => {
      marks[item.union_id] = item;
    });
    res.data.list.forEach((item: any) => {
      item.remarks = marks[`${item.foundationData.order_id}_recyBROrdLi`];
    });
    return res.data.list;
  },
  pagination: {
    showTotal: (): string => {
      let totalPages = Math.ceil((page.total ?? 0) / (page.pageSize ?? 0));
      return `共 ${page.total} 条记录   第 ${page.current} / ${totalPages} 页`;
    },
  },
});

getTableList();

const onSubscribe = (item: any) => {
  const orderId = item.foundationData.order_id;
  const ruleOrderData = ruleOrderMap.value[orderId];
  const type = ruleOrderData.status === '1' ? 'start' : 'cancel';
  const title = type === 'start' ? '是否对该订单进行预约回收？' : '是否对该订单进行取消预约回收？';
  const content = type === 'start' ? '若预约后用户进行续租和购买操作，则该预约回收操作会被自动取消' : '';
  const okText = type === 'start' ? '继续预约' : '确定取消';
  Modal.confirm({
    title,
    icon: createVNode(ExclamationCircleOutlined),
    content,
    cancelText: '取消',
    okText,
    onOk() {
      const params = {
        id: ruleOrderData.id,
        status: searchForm.status,
      };
      if (searchForm.status === '4') {
        params['status_child'] = searchForm.status_child;
      }
      const request = type === 'start' ? orderBook : orderCancelBook;
      return request(params).then(res => {
        message.success(res.message);
        getTableList();
      });
    },
  });
};

const onToPage = (item: any) => {
  const orderId = item.foundationData.order_id;
  const recycle_order_id = ruleOrderMap.value[orderId].recycle_order_id;
  const url = `/recycle-batch-order/index?orderId=${recycle_order_id}`;
  window.parent.postMessage(
    {
      action: 'blank',
      href: url,
    },
    '*',
  );
};

// 打开v3订单列表
const onToPageV3Order = (item: any) => {
  const { order_id } = item.foundationData;
  const url = `/order/v4-order-index?order_id=${order_id}`;
  window.parent.postMessage(
    {
      action: 'blank',
      href: url,
    },
    '*',
  );
};
</script>

<style lang="less" scoped>
.lease-order-subscribe-tabs {
  :deep(.ant-tabs-tab) {
    padding: 0 0 12px 0;
  }
}

.layout-admin-page {
  :deep(.page-top) {
    margin-bottom: 0;
    border-bottom: 0;
  }
}

.container {
  .container-status-btn {
    margin-bottom: 16px;
    padding-left: 24px;
  }
}

:deep(.ant-tabs-nav) {
  padding-left: 24px;
}

// 单元格
:deep(.td-box) {
  padding-top: 16px;
  padding-bottom: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-size: 12px;
  line-height: 20px;
  // 行样式
  .row,
  .row8 {
    box-sizing: content-box;
    padding-right: 16px;
    padding-left: 16px;
    line-height: 20px;

    .ant-btn-sm {
      height: 24px;
      line-height: 24px;
    }
  }

  .row + .row {
    margin-top: 4px;
  }

  .row + .row8,
  .row8 + .row,
  .row8 + .row8,
  .extend + .row,
  .extend + .row8 {
    margin-top: 8px;
  }

  .extend + .row.freeze {
    margin-top: 4px;
  }

  // 分界线
  .row + .divider,
  .row8 + .divider,
  .extend + .divider {
    width: calc(100% - 32px);
    height: 1px;
    margin: 16px;
    background: #f0f0f0;
  }

  // 标签
  .tags {
    .ant-tag {
      color: #00c8be;
      background-color: #ebfafa;
      border-color: #00c8be;
    }

    .tag {
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .red-bg-tag {
      background-color: #fff1f0;
    }
  }
}

.font--color {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.status-block {
  position: relative;
  margin-bottom: 8px;
  padding-left: 30px;
  color: #061533;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;

  &::after {
    position: absolute;
    top: 50%;
    left: 15px;
    width: 8px;
    height: 8px;
    margin-top: -4px;
    background: rgba(6, 21, 51, 0.25);
    border-radius: 100%;
    content: '';
  }

  &.primary {
    &::after {
      background: #3777ff;
    }
  }

  &.finish {
    &::after {
      background: #00c8be;
    }
  }
}

.expanded-row-td {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;
}

.pagination-box {
  width: 100%;
  height: 64px;
  padding: 16px 24px;
  background-color: #fff;
  border-top: 1px solid rgba(6, 21, 51, 0.06);

  .total-box {
    float: left;
    color: rgba(6, 21, 51, 0.65);
    line-height: 32px;

    .page-num {
      margin-left: 16px;
    }

    .value {
      margin: 0 4px;
      color: rgba(6, 21, 51, 1);
    }
  }

  .pagination {
    float: right;
  }
}

// 分页悬浮样式
.pagination-box.ceiling {
  // position: fixed;
  bottom: 0;
  z-index: 10;
}
</style>
