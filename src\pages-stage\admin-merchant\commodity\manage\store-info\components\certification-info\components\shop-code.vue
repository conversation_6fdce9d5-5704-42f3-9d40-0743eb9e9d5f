<template>
  <div class="shop-code">
    <div class="code-box">
      <img
        class="code"
        :src="code"
      >
    </div>
    <div class="tip">
      请用支付宝扫一扫打开
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps<{ code: string }>();
</script>

<style lang="less" scoped>
.code-box {
  .code {
    width: 144px;
    height: 144px;
  }
}
.tip {
  width: 200px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
}
</style>
