/* eslint-disable @typescript-eslint/no-var-requires */
import fs from 'fs';
import { resolve } from 'path';

const path = resolve('./aliyun.npm.config.json');
const FILE_VERSION = '0.0.1';

if (!fs.existsSync(path)) {
  write();
} else {
  console.log('aliyun.npm.config.json exists. \r\n');
  read();
}

function write() {
  const JsonContent = `
{
  "origin": "https://packages.aliyun.com/5f11945adf9df74e36afaa16/npm/npm-registry/",
  "username": "",
  "password": "",
  "email": ""
}
  `;

  fs.writeFileSync(path, JsonContent);
  console.log('aliyun.npm.config.json created. \r\n');
  console.log('点击下方链接查看云效npm制品库账号密码。');
  console.log('https://packages.aliyun.com/npm/npm-registry/guide');
}

function read() {
  const data = fs.readFileSync(path, 'utf8');
  if (data.match(/([1-9]\d*\.?\d*)|(0\.\d*[1-9])/)[0] !== FILE_VERSION) {
    console.log('aliyun.npm.config.json version check unpass. \r\n');
    write();
  } else {
    console.log('aliyun.npm.config.json version check pass. \r\n');
  }
}
