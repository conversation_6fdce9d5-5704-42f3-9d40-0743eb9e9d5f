import { SelectProps } from 'ant-design-vue';
import { Option } from '@/components/multiple-cascader/data';

export interface ListQueryType {
  order_created_date: {
    time_params: string;
    time_relation: string;
  };
  inspection_dimension: number[];
  risk_qualitative: number[];
  category?: number[];
  brand?: number[];
  model?: number[];
  page: number;
  page_size: number;
}

export interface OptionMapType {
  inspection_dimension?: SelectProps['options'];
  risk_qualitative?: SelectProps['options'];
  order_channels?: SelectProps['options'];
  goods_types?: Option[];
}
