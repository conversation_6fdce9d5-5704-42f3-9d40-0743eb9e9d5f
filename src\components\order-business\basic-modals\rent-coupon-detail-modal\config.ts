import { RTableColumnType } from 'rrz-web-design';
export const columns: RTableColumnType[] = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
    hideInTable: true,
    width: 60,
  },
  {
    title: '支付金额',
    dataIndex: 'money',
    key: 'money',
    hideInTable: true,
  },
  {
    title: '租金券明细',
    dataIndex: 'desc',
    key: 'desc',
  },
  {
    title: '报销状态',
    dataIndex: 'claim_type',
    key: 'claim_type',
    hideInSearch: true,
  },
  {
    title: '优惠券名称',
    dataIndex: 'name',
    key: 'name',
    hideInTable: true,
  },
  {
    title: '使用期数',
    dataIndex: 'period_num',
    key: 'period_num',
    customRender: ({ text }: { text: string }) => `第${text}期`,
  },
  {
    title: '使用时间',
    dataIndex: 'used_at',
    valueType: 'date',
    key: 'used_at',
  },
];
