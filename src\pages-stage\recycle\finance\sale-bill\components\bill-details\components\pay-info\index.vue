<template>
  <bill-detail-item title="支付信息">
    <a-table
      :columns="payInfoColumns"
      :data-source="payInfoList"
      :loading="loading"
      :pagination="false"
      :scroll="{ x: '100%' }"
      :sticky="true"
    >
      <template #bodyCell="{ column }">
        <template v-if="column.key === 'action'">
          <a-button
            class="link"
            type="link"
            @click="toRefund"
          >
            退款
          </a-button>
        </template>
      </template>
    </a-table>
  </bill-detail-item>
  <refund ref="refundRef" />
</template>

<script setup lang="ts">
import { PropType, ref } from 'vue';
import { payInfoColumns } from '../../config';
import { IPayInfoList } from '../../data';
import Refund from './components/refund.vue';
import BillDetailItem from '../bill-detail-item/index.vue';
import { message } from 'ant-design-vue';
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  payInfoList: {
    type: Array as PropType<IPayInfoList[]>,
    default: () => [],
  },
});

const refundRef = ref<InstanceType<typeof Refund> | null>(null);

const toRefund = () => {
  message.warn('功能正在维护中！');
};
</script>

<style lang="less" scoped>
.intrduce {
  display: flex;
  margin-bottom: 24px;
  padding: 9px 45px 9px 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 22px;
  background: #f0f7ff;
  border: 1px solid #b3d2ff;
  border-radius: 2px;
  .intrduce-img {
    width: 16px;
    height: 16px;
    margin-top: 3px;
    margin-right: 7px;
  }
}
.link {
  padding-left: 0;
  color: #3777ff;
}
</style>
