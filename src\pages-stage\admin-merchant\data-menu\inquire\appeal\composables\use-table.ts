import { PaginationProps, TableColumnType } from 'ant-design-vue';
import { reactive } from 'vue';
import { TableCacheItem } from '../data';

type ExamTableType = {
  loading: boolean;
  list: Record<string, any>[];
  pagination: PaginationProps;
  resetPagination: () => void;
  changeCacheData: (data: TableCacheItem) => void;
  changeSubStatus: (order_id: number, status: boolean) => void;
  tableMap: {
    risk_qualitative: Record<string, any>;
    reasonObj: Record<string, any>;
    auditStatus: Record<string, any>;
    submitStatus: Record<string, any>;
  };
  columns: TableColumnType[];
  tableCache: {
    1: Record<string, TableCacheItem>;
    2: Record<string, TableCacheItem>;
  };
};

export default function (): ExamTableType {
  const columns: TableColumnType[] = [
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id',
      width: 168,
    },
    {
      title: '风控定性',
      dataIndex: 'risk_qualitative',
      key: 'risk_qualitative',
      width: 88,
    },
    {
      title: '是否可发货订单',
      dataIndex: 'server_can_delivery_sign',
      key: 'server_can_delivery_sign',
      width: 130,
    },
    {
      title: '是否--前已发货订单',
      dataIndex: 'is_delivery',
      key: 'is_delivery',
      width: 220,
    },
    {
      title: '是否常规考核',
      width: 144,
      dataIndex: 'inspection_dimension',
      key: 'inspection_dimension',
    },
    {
      title: '不计入理由',
      width: 144,
      dataIndex: 'reason',
      key: 'reason',
    },
    {
      title: '自发订单发货超48H',
      width: 185,
      dataIndex: 'no_mark_deliver_pass_48h',
      key: 'no_mark_deliver_pass_48h',
    },
    {
      title: '发货时效',
      width: 100,
      dataIndex: 'delivery_second',
      key: 'delivery_second',
    },
    {
      title: '发货前收取期数',
      width: 130,
      dataIndex: 'sent_pay_num',
      key: 'sent_pay_num',
    },
    {
      title: '状态',
      width: 104,
      dataIndex: 'audit_status',
      key: 'audit_status',
    },
    {
      title: '操作',
      width: 96,
      dataIndex: 'op',
      key: 'op',
      fixed: 'right',
    },
  ];

  const resetPagination = () => {
    examTable.pagination.current = 1;
    examTable.pagination.pageSize = 10;
  };

  const changeCacheData = (data: TableCacheItem) => {
    examTable.tableCache[data.appeal_type as string][data.order_id as number] = data;
  };

  const changeSubStatus = (order_id: number, status: boolean) => {
    examTable.list.forEach(item => item.order_id === order_id && (item.isEvidence = status));
  };

  const examTable: ExamTableType = reactive({
    loading: false,
    list: [],
    tableCache: { 1: {}, 2: {} },
    tableMap: {
      risk_qualitative: {
        0: '未知',
        1: '通过',
        2: '预收',
        3: '风险高',
        4: '关闭',
        5: '跳过',
      },
      reasonObj: {
        1: '计入常规考核',
        2: '已删除订单',
        3: '码商订单',
        4: '风控关闭订单',
        5: '转单订单',
        6: '派单前取消订单',
        7: '订单创建时间距离订单生成时间超过48H',
        8: '预租',
      },
      auditStatus: {
        1: {
          label: '未确认',
          status: 'warning',
        },
        2: {
          label: '已通过',
          status: 'success',
        },
        3: {
          label: '不通过',
          status: 'error',
        },
      },
      submitStatus: {
        0: {
          label: '未上传',
          status: 'default',
        },
        1: {
          label: '已上传',
          status: 'success',
        },
      },
    },
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    columns,
    resetPagination,
    changeCacheData,
    changeSubStatus,
  });

  return examTable;
}
