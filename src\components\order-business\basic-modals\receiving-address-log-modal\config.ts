import { TableColumnType } from 'ant-design-vue';

import { ORDER_STATUS_TEXT } from '@/utils/constant/order';
import { formatDate } from '@/utils/time';

import { IEditAddressRecord } from './data.d';

const ADDRESS_ACTION_TYPE_TEXT_MAP = {
  0: '原始地址-未修改',
  1: '用户申请-系统修改',
  2: '商家人工修改',
  3: '平台人工修改',
};

export const tableColumns: TableColumnType[] = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 140,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 140,
  },
  {
    title: '收货地址',
    dataIndex: 'address',
    key: 'address',
    width: 280,
  },
  {
    title: '修改类型',
    dataIndex: 'action_type',
    key: 'action_type',
    width: 180,
    customRender: ({ record }: { record: IEditAddressRecord }) => {
      return ADDRESS_ACTION_TYPE_TEXT_MAP[record.action_type];
    },
  },
  {
    title: '订单状态',
    dataIndex: 'order_status',
    key: 'order_status',
    width: 110,
    customRender: ({ record }: { record: IEditAddressRecord }) => {
      return ORDER_STATUS_TEXT[record.order_status];
    },
  },
  {
    title: '修改时间',
    dataIndex: 'updated_at',
    key: 'updated_at',
    width: 120,
    customRender: ({ record }: { record: IEditAddressRecord }) => {
      if (!record.updated_at) {
        return '/';
      }
      const { year, month, day, hour, minute, second } = formatDate(Number(record.updated_at) * 1000);
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
  },
];
