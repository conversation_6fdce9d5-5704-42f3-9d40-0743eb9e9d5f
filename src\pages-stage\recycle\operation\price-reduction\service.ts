import { GET, POST, RequestConfig } from '@/services/api';
import { IReduction, RemarkType } from './data';

// 修改状态
export const updataStatus = <T extends { id: string; status: string }>(data: T) => {
  return GET<T, []>('/super/recycle/purchased-goods-price-decline/update-status', data);
};

// 删除
export const deleteReduction = (id: string) => {
  return GET('/super/recycle/purchased-goods-price-decline/delete', { id });
};

// 获取商品名称
export const getGoodsName = (id: string) => {
  return GET('/super/recycle/purchased-goods/detail', { id });
};

// 获取详情
export const getReducitonDetail = <T extends { id: string }>(data: T) => {
  return GET<T, IReduction>('/super/recycle/purchased-goods-price-decline/detail', data);
};

// 创建
export const createReduciton = <T extends Partial<IReduction>>(data: T, config?: RequestConfig) => {
  return POST<T, { id?: string }>('/super/recycle/purchased-goods-price-decline/create', data, config);
};

// 编辑
export const editReducitons = <T extends Partial<IReduction> & { id: string }>(data: T, config?: RequestConfig) => {
  return POST<T, { id?: string }>('/super/recycle/purchased-goods-price-decline/edit', data, config);
};

// 修改记录
export const ApiGetRemarks = <T extends { unionId: string }>(data: T) => {
  return GET<T, RemarkType[]>('/crm/log', data);
};
