<template>
  <div class="page-wrap">
    <div class="page-header p-wrap">
      <span class="text">通道管理</span>
    </div>
    <div class="table-wrap">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :pagination="false"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-switch
              v-model:checked="record.status"
              :checked-value="1"
              :un-checked-value="2"
              @change="onChangeStatus(record.id, record.status)"
            />
          </template>
          <template v-if="column.key === 'action'">
            <div>
              <a-button
                style="padding: 0"
                type="link"
                @click="showUpdateModel(record.id, record.message_type, record.server_id, record.wexin_qrcode)"
              >
                管理接收人
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <BindModal
    v-if="bindModalVisible"
    v-model:visible="bindModalVisible"
    :channel-id="channelId"
    :message-type="messageType"
    :server-id="server_id"
    :wexin-qrcode="wexinQrcode"
  />
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';

import { useTable } from '@/hook/component/use-table';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';

import BindModal from './components/bind-modal.vue';
import { columns } from './config';
import { channelList, channelUpdateStatus } from './service';
const { list, listLoading, getTableList, tableChange } = useTable({
  api: channelList,
});
getTableList();

const [bindModalVisible, { setTrue: showBindModal }] = useBoolean();
const onChangeStatus = async (id: string, status: number) => {
  const res = await channelUpdateStatus({ id: id, status: status });
  message.success(res.message || '操作成功');
};
const channelId = ref<string>('');
const messageType = ref<number>(0);
const server_id = ref<number>(0);
const wexinQrcode = ref<string>('');
const showUpdateModel = (id: string, type: number, serverId: number, weixin_qrcode: string) => {
  showBindModal();
  channelId.value = id;
  messageType.value = type;
  server_id.value = serverId;
  wexinQrcode.value = weixin_qrcode;
};
</script>
<style scoped lang="less">
.page-wrap {
  min-height: calc(100vh - 64px);
  background-color: #fff;
}
.p-wrap {
  padding: 24px;
  background: #fff;
}
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}
.search-wrap {
  padding: 0 24px;
  :deep(.ant-form) {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 0 -8px;
  }
  :deep(.ant-form-item) {
    flex-basis: 25%;
    margin-right: 0;
    margin-bottom: 24px;
    padding: 0 8px;
  }
}
.table-wrap {
  padding: 0 24px 24px;
}
</style>
