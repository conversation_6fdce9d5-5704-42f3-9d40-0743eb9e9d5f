<template>
  <a-modal
    v-model:visible="bindVisible"
    class="old-ship-modal"
    title="去发货"
    :width="650"
  >
    <div class="flex-wrap flex-vertical flex-gap-16">
      <SelfShipment
        ref="shipmentRef"
        :accessory-data="(accessoryData as any)"
        :data="data"
        :extra-data="extraData"
        :order-id="orderId"
        :set-is-pick-mode="setIsPickMode"
      />
    </div>
    <template #footer>
      <template v-if="isPickUpMode">
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button
          :loading="submitBtnLoading"
          type="primary"
          @click="openPopConfirm"
        >
          确定
        </a-button>
      </template>
      <template v-else>
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button
          v-if="deliveryStoreData.sku_id"
          @click="
            () => {
              emits('showExchangeApplyModal');
              onCancel();
            }
          "
        >
          申请换货
        </a-button>

        <a-popover
          placement="topLeft"
          title="发货确定"
          trigger="click"
          :visible="popoverVisible"
        >
          <template #content>
            <a-space direction="vertical">
              <div>商品信息：{{ extraData.spu_name + ' ' + extraData.sku_name }}</div>
              <div>货品信息：{{ deliveryStoreData.sku_info }}</div>
              <a-space class="popover-button-area">
                <a-button @click="popoverVisible = false">
                  取消
                </a-button>
                <a-button
                  :loading="submitBtnLoading"
                  type="primary"
                  @click="onIssueStock"
                >
                  确定
                </a-button>
              </a-space>
            </a-space>
          </template>
          <a-button
            type="primary"
            @click="openPopConfirm"
          >
            确定
          </a-button>
        </a-popover>
      </template>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, h, PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

import { useVModel } from '@/hook';
import { useTopWindow } from '@/hook/common/use-top-window';

import { saveJD, saveSF } from '../../../pickup-appoint-service-modal/pick-up-service-modal/services';
import type { IAccessoryData, IDeliverStore, IExtraData } from '../../data.d';
import SelfShipment from '../../grayscale-deliver-modal/components/self-shipment/index.vue';
import {
  cancelSfPickUp,
  getOrderLimitIssued,
  getStoreSendWarehouseInfo,
  goToShip,
  superGetSendWarehouseInfo,
} from '../../services';

const route = useRoute();
const isSuper = route.query.role === 'super';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
  data: {
    type: Object as PropType<IDeliverStore>,
    default: () => ({}),
  },
});

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', id: string, tags: string[]): void;
  (e: 'showExchangeApplyModal'): void;
}>();

const { navigate } = useTopWindow();

const bindVisible = useVModel(props, 'visible', emits);
function onCancel() {
  bindVisible.value = false;
}

const isPickUpMode = ref(false);
function setIsPickMode(bool: boolean) {
  isPickUpMode.value = bool;
}

const shipmentRef = ref();
const popoverVisible = ref(false);
const submitBtnLoading = ref(false);

const accessoryData = computed<IAccessoryData[]>(() => {
  const list: IAccessoryData[] = [
    { type: 1, list: [] },
    { type: 2, list: [] },
  ];
  // 根据accessory_type拆分两个分组
  if (Array.isArray(props.extraData.accessory_data)) {
    props.extraData.accessory_data.forEach(item => {
      const newData = {
        accessory_name: item.accessory_name,
        accessory_price: item.accessory_price,
        accessory_type: item.accessory_type,
        num: item.quantity,
      };
      if (newData.accessory_type === '1') {
        list[0].list.push(newData);
      } else if (newData.accessory_type === '2') {
        list[1].list.push(newData);
      }
    });
  }
  return list.filter(item => item.list.length);
});

const deliveryStoreData = ref<IDeliverStore>({});

/** 初始化商品信息并打开弹窗 */
function initDeliverGoods() {
  const api = isSuper ? superGetSendWarehouseInfo : getStoreSendWarehouseInfo;
  api(props.orderId).then(({ data }) => {
    // =============================== 初始化商品数据 ===============================
    deliveryStoreData.value = {
      order_id: props.orderId,
      special_category: props.extraData.special_category,
      ...data,
    };
  });
}

function openPopConfirm() {
  shipmentRef.value?.validate().then((formData: any) => {
    if (isPickUpMode.value) {
      submitPickUp(formData);
    } else {
      popoverVisible.value = true;
    }
  });
}

async function submitPickUp(formData: any) {
  submitBtnLoading.value = true;
  const url = formData.express_id === 'SF' ? saveSF : saveJD;
  let sendStartTime = formData.send_start_time[0] + ' ' + formData.send_start_time[1];
  if (formData.express_id === 'SF') {
    sendStartTime += ':00';
  }

  const params = {
    order_id: props.orderId,
    ...formData,
    is_sign_paper: formData.is_sign_paper ? 1 : 0,
    send_start_time: sendStartTime,
  };

  if (formData.express_id === 'SF' && props.data.scm_auth && !isSuper) {
    params.scm_device_codes = formData.device_codes ? formData.device_codes.split(',') : undefined;
  }
  delete params.device_codes;

  url(params)
    .then(res => {
      if (res.status === 0) {
        message.success(res.message);
        onCancel();
        emits('refresh', props.orderId, ['data']);
      }
    })
    .finally(() => {
      submitBtnLoading.value = false;
    });
}

async function onIssueStock() {
  const {
    data: { express_result },
  } = await getOrderLimitIssued({ order_id: props.orderId });

  shipmentRef.value?.validate().then(params => {
    if (props.data.scm_auth && !isSuper) {
      params.scm_device_codes = params.device_codes ? params.device_codes.split(',') : undefined;
    }
    delete params.device_codes;
    submitBtnLoading.value = true;
    goToShip(params)
      .then(async ({ data }) => {
        const childList = [h('span', '下发仓库成功')];
        if (data?.deviceOrderIds?.length && !isSuper) {
          const url = '/shop-purchased-device-order/index?ids=' + data.deviceOrderIds.join(',');
          childList.push(
            h('span', '，设备价格将按预估价进行冻结，采购记录可前往“'),
            h('span', { class: 'text-link', underlined: true, onClick: () => navigate('blank', url) }, '设备订单'),
            h('span', '”进行查看'),
          );
        }
        message.success({
          content: h('span', childList),
          duration: 5,
        });
        if (express_result === 1) {
          await cancelSfPickUp({ order_id: props.orderId });
        }
        onCancel();
        emits('refresh', props.orderId, ['data', 'all_remark']);
      })
      .finally(() => {
        submitBtnLoading.value = false;
        popoverVisible.value = false;
      });
  });
}

watch(
  () => props.visible,
  value => {
    if (value) {
      initDeliverGoods();
    }
  },
  { immediate: true },
);
</script>
