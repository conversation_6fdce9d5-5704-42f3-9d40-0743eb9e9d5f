<template>
  <a-button
    type="link"
    @click="onOpenDrawer"
  >
    编辑商品顺序
    <template #icon>
      <setting-outlined />
    </template>
  </a-button>

  <a-drawer
    v-model:visible="visible"
    :closable="false"
    placement="right"
    width="100%"
  >
    <template #title>
      <page-breadcrumb
        :items="['商品调序', '商品排序设置']"
        style="padding: 0"
      />
      <div class="cus-tit">
        <arrow-left-outlined
          class="icon"
          @click="() => (visible = false)"
        />
        <h2>商品排序设置</h2>
        <span class="des">下图可拖动排序，所有修改需要保存才生效</span>
      </div>
    </template>
    <template #extra>
      <div class="extra">
        <span> 上一次修改时间：{{ updateAt }} </span>
        <a-popconfirm
          cancel-text="取消"
          ok-text="确定"
          title="你确定要重置排序吗？"
          @confirm="onReset"
        >
          <a-button>
            重置排序
            <template #icon>
              <reload-outlined />
            </template>
          </a-button>
        </a-popconfirm>

        <a-popconfirm
          cancel-text="取消"
          ok-text="确定"
          title="你确定使用新排序并保存吗？"
          @confirm="onSave"
        >
          <a-button type="primary">
            保存
            <template #icon>
              <SaveOutlined />
            </template>
          </a-button>
        </a-popconfirm>
      </div>
    </template>

    <a-alert
      message="重置排序后，将清空所有排序记录，恢复初始排序"
      show-icon
      style="margin-bottom: 15px"
      type="warning"
    />
    <a-spin :spinning="spinning">
      <div class="drag-wrap">
        <draggable
          animation="300"
          chosen-class="chosenClass"
          ghost-class="ghost"
          item-key="id"
          :list="goodsList"
          style="display: flex; flex-wrap: wrap"
        >
          <template #item="{ element }">
            <div class="item">
              <div
                class="img"
                :style="{
                  backgroundImage: `url(${oss_prefix + element.cover})`,
                }"
              />
              <span class="id">{{ element.id }}</span>
              <span class="name">{{ element.title }}</span>
            </div>
          </template>
        </draggable>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import { SettingOutlined, SaveOutlined, ReloadOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import { ref } from 'vue';
import { resetSort, getCategoryGoods, updateSort } from '../services';
import { message } from 'ant-design-vue';
import draggable from 'vuedraggable';
import { GoodsItem } from '../data';
const props = defineProps<{
  code: string;
  id: string;
}>();
const updateAt = ref('');
const visible = ref(false);
const spinning = ref(false);
const oss_prefix = ref('');
const onOpenDrawer = () => {
  visible.value = true;
  getData();
};

const getData = async () => {
  spinning.value = true;
  const res = await getCategoryGoods({
    category: props.code,
  });
  goodsList.value = res.data.server_spu_list || [];
  updateAt.value = res.data.updated_at;
  oss_prefix.value = res.data.oss_prefix;
  spinning.value = false;
};

const goodsList = ref<GoodsItem[]>([]);

const onSave = async () => {
  await updateSort({
    spu_ids: goodsList.value.map(item => item.id),
  });
  message.success('保存成功');
};

const onReset = async () => {
  await resetSort({
    category_id: Number(props.id),
  });
  message.success('重置成功');
  getData();
};
</script>

<style lang="less" scoped>
.extra {
  padding-top: 40px;
  & > span {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  button {
    margin-left: 8px;
  }
}
.cus-tit {
  display: flex;
  align-items: center;
  .icon {
    color: rgba(6, 21, 51, 0.65);
  }
  h2 {
    margin: 0 12px 0 18px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
  }
  .des {
    padding-top: 6px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 160px;
  height: 236px;
  margin: 0 8px 12px;
  padding-top: 8px;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 8px;
  cursor: move;
  .img {
    width: 144px;
    height: 144px;
    background-size: cover;
    border-radius: 8px;
  }

  .id {
    margin: 8px 0 4px 0;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
  }

  .name {
    display: -webkit-box;
    width: 144px;
    overflow: hidden;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
.drag-wrap.disable {
  pointer-events: none;
}
</style>
