import { GET, POST } from '@/services/api';

/**
 * @description: 查询当前订单的部分原始数据
 * @param {string} order_id
 * @return {*}
 */
export function getOriginBaseInfo(order_id) {
  return GET('/warehouse/logisticschanges/originBaseInfo', { order_id }, { hostType: 'Golang' });
}

/**
 * @description: 物流变更申请
 * @param {any} data
 * @return {*}
 */
export function createLogisticschanges(data: any) {
  return POST('/warehouse/logisticschanges/create', data, { hostType: 'Golang' });
}
