.ant-alert-info {
  margin-bottom: 24px;
}
.has-been-associated {
  margin-bottom: 24px;
  color: rgba(6, 21, 51, 0.65);
  .text {
    margin-left: 4px;
  }
}

.relevancy-content {
  .relevancy-info {
    color: rgba(6, 21, 51, 0.65);
    img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .relevancy-title {
      color: rgba(6, 21, 51, 0.85);
      font-weight: bold;
      font-size: 14px;
      line-height: 22px;
    }
  }
  .relevancy-item {
    width: 320px;
    .input-phone {
      margin: 24px 0 16px 0;
    }
    .code-con {
      .input-code {
        width: 210px;
      }

      .get-code {
        box-sizing: border-box;
        width: 102px;
        height: 32px;
        color: #3777ff;
        line-height: 30px;
        text-align: center;
        background: #fff;
        border: 1px solid #3777ff;
        border-radius: 4px;
      }
      .show-pointer {
        cursor: pointer;
      }
    }
  }
}

.relevancy-footer {
  .user-agreement {
    color: #3777ff;
    border-bottom: 1px solid;
    cursor: pointer;
  }
}

.unbind-content {
  .unbind-item {
    width: 320px;
    margin-bottom: 24px;
    .item-title {
      margin-bottom: 8px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
    }
    .code-con {
      margin-top: 16px;
      .input-code {
        width: 210px;
      }

      .get-code {
        box-sizing: border-box;
        width: 102px;
        height: 32px;
        color: #3777ff;
        line-height: 30px;
        text-align: center;
        background: #fff;
        border: 1px solid #3777ff;
        border-radius: 4px;
      }
      .show-pointer {
        cursor: pointer;
      }
    }
  }
  .unbind-item + .unbind-item {
    margin-bottom: 0;
    padding-top: 24px;
    border-top: 1px solid rgba(6, 21, 51, 0.06);
  }
}
:deep(.table-no-data) {
  .ant-empty-image {
    height: unset;
    margin-top: 45px;
    margin-bottom: 10px;
    > img {
      width: 64px;
      height: 48px;
    }
  }
}
:deep(.ant-empty-description) {
  margin-bottom: 61px;
  color: rgba(6, 21, 51, 0.65);
}

:deep(.login-modal) {
  .ant-modal-body {
    width: 408px;
    height: 411px;
    padding: 0;
    overflow: hidden;
    text-align: center;
    background: #f0f1f3;
    border-radius: 8px;
    .login-title {
      margin: 36px auto 32px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
    }
    .form-demo {
      width: 312px;
      height: 279px;
      margin: 0 48px 40px;
      overflow: hidden;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 12px 48px 16px rgba(6, 21, 51, 0.03), 0 9px 28px 0 rgba(6, 21, 51, 0.05),
        0 6px 16px -8px rgba(6, 21, 51, 0.08);

      .title {
        margin: 32px auto 27px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: bold;
        font-size: 20px;
      }
      .line-item {
        width: 246px;
        height: 34px;
        margin: 0 auto;
        line-height: 34px;
        background: #fff;
        border: 1px solid rgba(6, 21, 51, 0.15);
        border-radius: 3px;
      }
      .line-item + .line-item {
        margin: 14px auto 35px;
      }
      .form-footer {
        > span {
          width: 103px;
          height: 34px;
          line-height: 34px;
          border-radius: 2px;
        }
        .enter {
          margin: 0 8px 0 50px;
          color: #fff;
          background: rgba(6, 21, 51, 0.15);
        }
        .go-back {
          color: rgba(6, 21, 51, 0.65);
          background-color: #fff;
          border: 1px solid rgba(6, 21, 51, 0.15);
        }
      }
    }
  }
}
