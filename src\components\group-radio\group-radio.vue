<template>
  <a-radio-group
    v-model:value="value"
    class="type-group"
    @change="onChange"
  >
    <template
      v-for="(groupKey, i) in Object.keys(options)"
      :key="i"
    >
      <div
        v-if="options[groupKey].length"
        class="type-group__item"
      >
        <div class="type-group__item__label">
          <a-tag>{{ typeTitle[groupKey] }}</a-tag>
        </div>
        <div class="type-group__item__controls">
          <a-radio
            v-for="(item, j) in options[groupKey]"
            :key="j"
            :value="item.id"
          >
            {{ item.title }}
          </a-radio>
        </div>
      </div>
    </template>
  </a-radio-group>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  modelValue: number | undefined;
  options: Record<
    string,
    {
      id: string;
      title: string;
    }[]
  >;
}>();

const emit = defineEmits(['update:modelValue', 'change']);

const typeTitle = {
  1: '用户',
  2: '平台',
  3: '其他',
  4: '风控',
  5: '发货',
  6: '商家',
};

const value = computed({
  get: () => props.modelValue,
  set: newVal => emit('update:modelValue', newVal),
});

const onChange = function () {
  emit('change', ...arguments);
};
</script>

<style scoped lang="less">
.type-group {
  &__item {
    display: flex;
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
    &__label {
      .ant-tag {
        margin-right: 16px;
        color: var(--ant-primary-6);
        line-height: 20px;
        background: var(--ant-primary-1);
        border: 1px solid var(--ant-primary-6);
        border-radius: 4px;
      }
    }
    &__controls {
      .ant-radio-wrapper {
        margin-right: 16px;
      }
    }
  }
}
</style>
