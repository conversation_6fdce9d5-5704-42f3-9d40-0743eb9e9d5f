import { GET, POST } from '@/services/api';

import { IChangePassword } from './data';

/** 获取用户信息 */
export function getUserInfo() {
  return GET('/account/get-user-info');
}

/** 修改密码 */
export function changePassword(data: IChangePassword) {
  return POST('/user-info/change-password', data);
}

/** 获取微信授权地址 */
export function getWxBindConfig(data: { type: string }) {
  return POST('/user-info/get-wx-bind-params', data);
}

/** 获取店铺微信绑定状态 */
export function getIsBind() {
  return GET('/server-relation/get-relation-status');
}

/** 解绑微信 */
export function unbindWx() {
  return GET('/frontend-user/unbind-wx');
}

/** 保存用户信息 */
export function apiSaveUserInfo(data: {
  'UserForm[username]': string;
  'UserForm[msgCode]'?: string;
  'UserForm[newPhone]'?: string;
  'UserForm[newMsgCode]'?: string;
}) {
  return POST('/user-info/change-info', data);
}
