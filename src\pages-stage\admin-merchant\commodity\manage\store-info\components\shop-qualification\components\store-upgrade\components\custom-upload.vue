<template>
  <div :class="clazz">
    <RUpload
      v-model:value="files"
      template="pictureCard"
      :upload-api="uploadToOss"
      :upload-props="{ multiple: true, maxCount: noLimit ? undefined : 5, accept }"
      value-type="file"
    >
      <a-space
        direction="vertical"
        :size="0"
      >
        <PlusOutlined />
        {{ uploadText }}
      </a-space>
    </RUpload>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { TUploadValue } from 'rrz-web-design';

import uploadToOss from '@/utils/oss-upload';

type TType = 'image' | 'video' | 'mixed';

const MEDIA_TYPE = {
  image: '.jpg,.png,.jpeg,.gif',
  video: '.mp3,.mp4,.wav,.ogg',
  mixed: '.jpg,.png,.jpeg,.gif,.mp3,.mp4,.wav,.ogg',
};

const emits = defineEmits<{ (e: 'update:value', value: TUploadValue[]): void }>();

const props = defineProps<{
  value: TUploadValue[] | undefined;
  type?: TType;
  noLimit?: boolean;
}>();

const files = computed({ get: () => props.value, set: val => emits('update:value', val) });
const clazz = computed(() => ({ 'hide-upload-component': !props.noLimit && files.value?.length === 5 }));
const uploadText = computed(() => (props.noLimit ? '上传' : `上传(${files.value?.length || 0}/5)`));
const accept = computed(() => MEDIA_TYPE[props.type || 'image']);
</script>

<style lang="less" scoped>
.hide-upload-component :deep(.ant-upload-select-picture-card) {
  display: none;
}
</style>
