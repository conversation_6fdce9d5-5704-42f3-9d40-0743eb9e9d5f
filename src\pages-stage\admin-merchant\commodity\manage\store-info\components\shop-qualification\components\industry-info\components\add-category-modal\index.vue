<template>
  <RModal
    v-model:open="open"
    title="添加分类"
    @ok="handleConfirm"
  >
    <stateCascader
      v-model:value="selectCategory"
      :config="cascaderConfig"
    />
  </RModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { RModal } from 'rrz-web-design';

import stateCascader from '@/components/form-create/src/state-cascader';

import { EHandleType } from '../../../../data.d';

const router = useRouter();
const cascaderConfig = {
  url: '/server/ServerCredentialDictCategory/navList',
  hostType: 'Golang',
  optionsFormat: (res: any) => {
    return res.data?.map((item: any) => {
      return {
        ...item,
        disabled: !item.children?.length,
      };
    });
  },
  elProps: {
    allowClear: true,
    style: {
      width: '100%',
    },
    fieldNames: { label: 'name', value: 'id', children: 'children' },
    placeholder: '请选择',
    onChange: handleModalCascaderChange,
  },
};

const open = ref(false);

const originAddCategoryState = {
  target_id: '',
  one_name: '',
  two_name: '',
};

const addCategoryState = ref(originAddCategoryState);

const selectCategory = ref([]);

function handleModalCascaderChange(value: string[] | undefined, selectedOptions: any[]) {
  if (selectedOptions) {
    const len = selectedOptions.length;
    const selectItem = selectedOptions[len - 1];
    addCategoryState.value = {
      target_id: selectItem.id,
      one_name: len > 1 ? selectedOptions[0]?.name : '',
      two_name: len > 1 ? selectedOptions[1]?.name : '',
    };
  } else {
    addCategoryState.value = originAddCategoryState;
  }
}

function handleConfirm() {
  router.push({
    path: '/merchant/commodity/manage/industry-qualification-edit',
    query: {
      type: EHandleType.ADD,
      cateInfo: JSON.stringify(addCategoryState.value),
    },
  });
  open.value = false;
}

function openModal() {
  selectCategory.value = [];
  addCategoryState.value = originAddCategoryState;
  open.value = true;
}

defineExpose({
  openModal,
});
</script>
