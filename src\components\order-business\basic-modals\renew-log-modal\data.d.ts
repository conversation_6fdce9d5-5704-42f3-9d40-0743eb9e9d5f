export interface INewLogData {
  base_buyout_price: number;
  list: INewLogListItem[];
}

export interface INewLogListItem {
  period_num: string;
  continue_log_id: string;
  date_start: string;
  date_end: string;
  bill_money: string;
  buyout_price: string;
  created_by: string;
  created_role: string;
  created_at: string;
  agree_user: string;
  agree_user_role: string;
  agree_at: string;
  protocol_url: string;
  is_has_file: number;
}

export interface IOldLogListItem {
  id: string;
  order_id: string;
  time_start: string;
  time_end: string;
  money: string;
  acc_rental: string;
  created_at: string;
}
