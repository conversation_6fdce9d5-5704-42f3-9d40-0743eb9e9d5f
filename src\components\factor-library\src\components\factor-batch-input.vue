<template>
  <a-select
    v-if="readonly"
    v-bind="attrs"
    mode="tags"
    :open="false"
    :token-separators="[',', '，', ' ', '\n', '\r']"
    :value="bindValue?.split(',')"
  />
  <BatchInput
    v-else
    v-model:value="bindValue"
    placeholder="请输入"
    v-bind="attrs"
  />
</template>

<script lang="ts" setup>
import { computed, useAttrs } from 'vue';

import BatchInput from '@/components/batch-input';

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  value?: string[];
  readonly?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:value', value: string[]): void;
}>();

const bindValue = computed({
  get() {
    return props.value?.join(',');
  },
  set(value) {
    // 字符串使用逗号分隔
    emit('update:value', value?.split(','));
  },
});

const attrs = useAttrs();
</script>
