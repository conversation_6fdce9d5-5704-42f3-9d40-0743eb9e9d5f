<template>
  <a-drawer
    :closable="false"
    destroy-on-close
    title="物流变更"
    :visible="bindVisible"
    width="500"
    @close="onClose"
  >
    <template #extra>
      <CloseOutlined @click="onClose" />
    </template>

    <a-spin :spinning="loading">
      <div
        class="details-item"
        style="padding-bottom: 24px; border-bottom: 1px solid #f0f0f0"
      >
        <div class="header">
          <div class="title">
            申请详情
          </div>
          <div
            class="right-record"
            @click="showChangeLog"
          >
            <img
              alt="申请记录"
              src="https://img1.rrzuji.cn/uploads/scheme/2405/22/m/2pajMEgUY4ecP73fQomZ.png"
              style="width: 16px; height: 16px; transform: translateY(-2px)"
            >
            申请记录
          </div>
        </div>

        <div class="info">
          <div class="label">
            变更类型 :
          </div>
          <div class="value">
            {{ changeDetail.change_type === 1 ? '物流拦截' : '地址变更' }}
          </div>
        </div>

        <template v-if="changeDetail.change_type === 1">
          <div class="info">
            <div class="label">
              拦截原因 :
            </div>
            <div class="value">
              {{ REASON_MAP[changeDetail.intercept_reason] || '' }}
            </div>
          </div>
        </template>

        <template v-if="changeDetail.change_type === 2">
          <div class="info">
            <div class="label">
              新收货人 :
            </div>
            <div class="value">
              {{ changeDetail.name }} {{ baseInfo.name !== changeDetail.name ? '(变更)' : '(不变)' }}
            </div>
          </div>

          <div class="info">
            <div class="label">
              新手机号 :
            </div>
            <div class="value">
              {{ changeDetail.phone }} {{ baseInfo.phone !== changeDetail.phone ? '(变更)' : '(不变)' }}
            </div>
          </div>

          <div class="info">
            <div class="label">
              新地址 :
            </div>
            <div class="value">
              {{ changeDetail.address }} {{ baseInfo.address !== changeDetail.address ? '(变更)' : '(不变)' }}
            </div>
          </div>
        </template>

        <div class="info">
          <div class="label">
            备注 :
          </div>
          <div class="value">
            {{ changeDetail.apply_remark }}
          </div>
        </div>

        <div class="info">
          <div class="label">
            申请时间 :
          </div>
          <div class="value">
            {{ changeDetail.created_at }}
          </div>
        </div>
      </div>
      <div
        class="details-item"
        style="padding-top: 24px"
      >
        <div
          class="title"
          style="padding-bottom: 12px"
        >
          平台处理
        </div>

        <div class="info">
          <div class="label">
            处理状态 :
          </div>
          <div
            class="value"
            :style="{ color: COLOR_MAP[changeDetail.status] }"
          >
            {{ STATUS_MAP[changeDetail.status] }}
          </div>
        </div>

        <div class="info">
          <div class="label">
            处理结果 :
          </div>
          <div
            class="value"
            :style="{ color: RESULT_MAP.color[changeDetail.result] || '' }"
          >
            {{ RESULT_MAP.text[changeDetail.result] || '-' }}
          </div>
        </div>

        <div
          class="info"
          :style="{ paddingBottom: changeDetail.images?.length ? '8px' : '16px' }"
        >
          <div class="label">
            图片凭证 :
          </div>
          <div class="images-wrap">
            <a-image-preview-group>
              <div
                v-for="src in changeDetail.images"
                :key="src"
                class="cover-image"
              >
                <a-image
                  :src="src"
                  :width="86"
                />
              </div>
            </a-image-preview-group>
          </div>
        </div>

        <div class="info">
          <div class="label">
            平台备注 :
          </div>
          <div class="value">
            {{ changeDetail.remark || '-' }}
          </div>
        </div>
      </div>
    </a-spin>

    <template #footer>
      <div style="display: flex; justify-content: end">
        <a-popover
          v-if="changeDetail.status === 0"
          v-model:visible="popoverVisible"
          trigger="click"
        >
          <template #content>
            <div style="line-height: 22px">
              <ExclamationCircleOutlined style="color: #faad14" />
              确定撤销物流变更申请吗？
            </div>
            <div style="display: flex; justify-content: end; padding-top: 8px">
              <a-button
                size="small"
                @click="popoverVisible = false"
              >
                取消
              </a-button>
              <a-button
                size="small"
                style="margin-left: 6px"
                type="primary"
                @click="onRepeal"
              >
                确认
              </a-button>
            </div>
          </template>
          <a-button
            danger
            ghost
            :loading="submitLoading"
            style="margin-right: 8px"
            type="primary"
          >
            撤销
          </a-button>
        </a-popover>

        <a-button
          type="primary"
          @click="onClose"
        >
          我知道了
        </a-button>
      </div>
    </template>
  </a-drawer>

  <RecodeListModal
    v-model:visible="modalVisible"
    :base-info="baseInfo"
    :order-id="orderId"
  />
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing/async-order-data.d';
import { useVModel } from '@/hook';

import { dealOrderWorkStatus } from '../delivery-intercept-record-modal/services';
import RecodeListModal from './components/record-list-modal.vue';
import { COLOR_MAP, REASON_MAP, RESULT_MAP, STATUS_MAP } from './config';
import { IBaseInfo, IDetailInfo } from './data';
import { getLogisticschangeDetail, getOriginBaseInfo } from './services';

interface IExtraData {
  change_type?: number;
  status?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);

function onClose() {
  bindVisible.value = false;
}

const loading = ref(false);

// ==================================  数据信息  ====================================
/** 拦截修改信息 */
const changeDetail = ref<IDetailInfo>({
  id: 0,
  address: '',
  apply_remark: '',
  change_type: 1,
  created_at: '',
  images: [],
  intercept_reason: undefined,
  logistic_code: '',
  name: '',
  phone: '',
  remark: '',
  result: 0,
  status: 0,
  updated_at: '',
});

/** 订单收货信息 */
const baseInfo = ref<IBaseInfo>({
  name: '',
  address: '',
  phone: '',
});

watch(
  bindVisible,
  value => {
    if (value) {
      getChangeDetail();
      getBaseInfo();
    }
  },
  { immediate: true },
);

/** 获取拦截修改信息 */
function getChangeDetail() {
  loading.value = true;
  getLogisticschangeDetail(props.orderId)
    .then(({ data }) => {
      changeDetail.value = data;
    })
    .catch(() => {
      bindVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 获取订单部分信息 */
function getBaseInfo() {
  getOriginBaseInfo(props.orderId).then(({ data }) => {
    baseInfo.value = {
      name: data.name,
      phone: data.phone,
      address: data.address.replace(/\s+/g, ''), // 去除所有的空格
    };
  });
}

// ==================================  撤销拦截  ====================================
const submitLoading = ref(false);
const popoverVisible = ref(false);

function onRepeal() {
  submitLoading.value = true;
  dealOrderWorkStatus({
    id: changeDetail.value.id,
    work_status: 4, // 撤销
    not_grayscale_server: 1, // 标记给后端是旧版本的物流拦截的撤销
  })
    .then(() => {
      message.success('撤销成功');
      onClose();
      emit('refresh', props.orderId, ['data', 'all_remark']);
    })
    .finally(() => {
      submitLoading.value = false;
    });
}

// ==================================  查看记录  ====================================
const modalVisible = ref(false);

function showChangeLog() {
  modalVisible.value = true;
}
</script>

<style lang="less" scoped>
@import './common';
</style>
