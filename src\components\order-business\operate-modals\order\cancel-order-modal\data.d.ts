export type CancelForm = {
  refund_rental?: number;
  refund_deposit?: number;
  credit_money?: number;
  money_type?: number;
  reason_type?: number;
  reason?: string | number;
  reason_text?: string;
  is_sms_send: boolean;
};

// 选项类型
export interface Option<T> {
  label: string;
  value: T;
  disabled?: boolean;
}

export interface CancelType {
  loading: boolean;
  data: CancelForm;
  rules: Record<string, any>;
  reasonTypeOptions: Option<number | ''>[];
  reasonOptions: Record<string, any>;
  validateInfos: any;
}

export type AgreeCancelForm = {
  refund_rental?: number;
};

export type AgreeCancelCondition = {
  reasonLabel: string;
  subReasonLabel: string;
};

export type AgreeCancelType = {
  loading: boolean;
  data: AgreeCancelForm;
  condition?: AgreeCancelCondition;
};

export interface ICancelReasonInfo {
  type?: number;
  apply_cancel_date?: string;
  cancel_reason?: string;
  cancel_sub_reason?: string;
  cancel_date?: string;
  auto_cancel_order_count_down?: string;
}

export interface IExtraData {
  rental_has_pay?: number | string;
  rental_has_refund?: number | string;
  deposit_has_pay?: number | string;
  deposit_has_refund?: number | string;
  apply_cancel_reason?: string;
  sub_cancel_reason_text?: string;
  apply_cancel_status?: number;
  pay_status?: number;
  deposit_money?: number | string;
  is_cancel_work_order_id?: number | string;
  key?: number;
  is_while_user?: boolean;
}
