<template>
  <template v-if="reviewDetail.has_review">
    <!-- 面审信息 -->
    <div>
      <InterviewStatus
        :error-msg="errorMsg"
        show-button
        :type="reviewDetail.status"
      >
        <template
          v-if="reviewDetail.status === EInterviewStatus.ERROR"
          #operate
        >
          <a-button
            type="primary"
            @click="toRefine"
          >
            马上完善
          </a-button>
        </template>
      </InterviewStatus>
    </div>
    <a-steps
      v-if="[EInterviewStatus.WAINTING].includes(reviewDetail.status) || isShowRefine"
      class="steps"
      :current="reviewDetail.supplement_link_arr.length"
      direction="vertical"
      progress-dot
    >
      <a-step
        v-for="item in reviewDetail.supplement_link_arr"
        :key="item.type"
        :title="item.name"
      >
        <template #description>
          <span
            v-if="[EInterviewStatus.WAINTING].includes(reviewDetail.status) && item.file_urls.length < 1"
            style="color: rgba(6, 21, 51, 0.45)"
          >无</span>
          <RUpload
            v-else
            v-model:value="item.file_urls"
            :hide-entry="[EInterviewStatus.WAINTING].includes(reviewDetail.status)"
            :remove-confirm="true"
            show-text="上传"
            template="pictureCard"
            :upload-api="uploadToOss"
            :upload-props="{
              multiple: true,
              maxCount: 10,
              accept: '.jpg,.jpeg,.png,.mp4',
              disabled: [EInterviewStatus.WAINTING].includes(reviewDetail.status),
            }"
            value-type="file"
          />
        </template>
      </a-step>
      <a-step class="steps-end" />
    </a-steps>
    <div
      v-if="isShowRefine"
      class="bottom-button"
    >
      <a-button
        size="large"
        @click="isShowRefine = false"
      >
        取消
      </a-button>
      <a-button
        size="large"
        type="primary"
        @click="submit"
      >
        提交审核
      </a-button>
    </div>
  </template>
  <REmpty v-else />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { message } from 'ant-design-vue';

import uploadToOss from '@/utils/oss-upload';

import { EInterviewStatus, IReviewData } from '../data.d';
import { getFaceReviewDetail, submitSupplementFile } from '../service';
import InterviewStatus from './interview-status.vue';

const reviewDetail = ref<IReviewData>({} as IReviewData);
const errorMsg = computed(() => {
  // 1、场地租赁合同缺失 2、门店视频数据缺失
  let errMsg = '';
  reviewDetail.value.supplement_link_arr.forEach((item, index) => {
    errMsg += `${index + 1}、${item.name}缺失 `;
  });
  errMsg = errMsg + `；${reviewDetail.value.review_reason || ''}`;
  return errMsg;
});
const getDetail = async () => {
  const res = await getFaceReviewDetail();
  reviewDetail.value = res.data;
};
getDetail();

const isShowRefine = ref<boolean>(false);
const toRefine = () => {
  isShowRefine.value = true;
};

const handleVerify = () => {
  const isVerify = reviewDetail.value.supplement_link_arr.every(item => {
    return item.file_urls.length > 0;
  });
  return isVerify;
};
// 处理提交参数
const handleFormatParams = showArr => {
  const fileArr = showArr.map(item => {
    const file_urls = item.file_urls.map(file => {
      // 其他类型，处理 URL 前缀
      const urlPrefixLength = reviewDetail.value.url_prefix.length;
      const url = typeof file === 'string' ? file : file.url;
      return url.slice(urlPrefixLength);
    });

    return {
      type: item.type,
      file_urls,
    };
  });
  return fileArr;
};
// 提交审核
const submit = async () => {
  if (!handleVerify()) return message.error('请上传补充资料');
  const submitArr = handleFormatParams(reviewDetail.value.supplement_link_arr);
  const res = await submitSupplementFile({ supplement_link_arr: submitArr });
  message.success(res.message || '提交成功');
  isShowRefine.value = false;
  getDetail();
};
</script>

<style scoped lang="less">
.steps {
  margin-bottom: 72px;
  :deep(.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title) {
    color: var(--ant-primary-color);
  }
  .steps-end {
    display: none;
  }
  :deep(.ant-steps-vertical > .ant-steps-item .ant-steps-item-description) {
    padding-bottom: 0;
  }
}
.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  gap: 16px;
  justify-content: center;
  //align-items: center;
  width: 100%;
  padding: 16px 0;
  background: #fff;
  box-shadow: 0 -2px 20px 0 rgba(0, 0, 0, 0.05);
}
:deep(.ant-btn-lg) {
  padding: 8px 32px;
}
:deep(.ant-steps-item-title) {
  font-size: 14px;
}
</style>
