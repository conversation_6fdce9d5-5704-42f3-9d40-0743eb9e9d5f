<template>
  <div :class="['step-tag', `step-tag-${type}`, { 'step-tag-ghost': ghost }]">
    <slot name="prefix" />
    <span>{{ tag }}</span>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  tag: string;
  type: 'primary' | 'success' | 'danger';
  ghost?: boolean;
}>();
</script>

<style lang="less" scoped>
.step-tag {
  display: inline-flex;
  gap: 6px;
  align-items: center;
  padding: 1px 8px;
  font-size: 12px;
  line-height: 20px;
  text-wrap: nowrap;
  border: 1px solid;
  border-radius: 4px;

  &-primary {
    color: #3777ff;
    background-color: #f0f7ff;
    border-color: #b3d2ff;
  }

  &-success {
    color: #52c41a;
    background-color: #f6ffed;
    border-color: #b7eb8f;
  }

  &-danger {
    color: #f5222d;
    background-color: #fff1f0;
    border-color: #ffa39e;
  }

  &-ghost {
    background-color: transparent;
  }
}
</style>
