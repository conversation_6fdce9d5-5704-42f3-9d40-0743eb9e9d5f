import type { FormGroupItem } from '@/components/form-create/src/typing';
import type { ColumnType } from 'ant-design-vue/lib/table';
export const columns: ColumnType[] = [
  {
    title: 'ID',
    key: 'id',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '用户手机号',
    key: 'phone',
    dataIndex: 'phone',
    width: 119,
  },
  {
    title: '问题描述',
    key: 'content',
    dataIndex: 'content',
    width: 658,
  },
  {
    title: '提交时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 171,
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 88,
  },
];
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'terminal',
    originProps: { label: '反馈渠道', name: 'terminal' },
    elProps: { allowClear: true, placeholder: '请选择' },
    fragmentKey: 'renderSelect',
    options: [
      {
        label: '支付宝',
        value: 'alipay.recycle',
      },
      {
        label: '微信',
        value: 'wx.recycle',
      },
    ],
  },
  {
    key: 'time',
    originProps: { label: '提交时间', name: 'time' },
    elProps: { allowClear: true, format: 'YYYY-MM-DD', valueFormat: 'YYYY-MM-DD', style: { width: '100%' } },
    fragmentKey: 'renderRangePicker',
  },
];
