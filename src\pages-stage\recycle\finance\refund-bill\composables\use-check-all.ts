import { reactive, watch, computed } from 'vue';

export default (list: any) => {
  const checkAllState = reactive({
    indeterminate: false,
    checkAll: false,
    checkedList: [],
    checkeIds: [],
  });
  const onCheckAllChange = (e: any) => {
    Object.assign(checkAllState, {
      checkedList: e.target.checked ? list.value : [],
      indeterminate: false,
    });
  };
  watch(
    () => checkAllState.checkedList,
    val => {
      checkAllState.indeterminate = !!val.length && val.length < list.value.length;
      checkAllState.checkAll = val.length === list.value.length;
    },
  );
  const rowSelection = computed(() => ({
    onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
      Object.assign(checkAllState, {
        checkedList: selectedRows,
        indeterminate: false,
      });
    },
    selectedRowKeys: checkAllState.checkedList.map((item: any) => item.refund_order_id),
  }));
  return {
    checkAllState,
    onCheckAllChange,
    rowSelection,
  };
};
