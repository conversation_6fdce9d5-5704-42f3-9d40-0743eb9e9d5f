import { RButtonType } from 'rrz-web-design';

export const columns: RButtonType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    customRender: ({ index }) => {
      return `${index + 1} `;
    },
    width: 90,
    hideInSearch: true,
  },
  { title: '姓名', dataIndex: 'name', width: 120 },
  { title: '联系手机', dataIndex: 'tell', width: 140 },
  { title: '省份', dataIndex: 'province', width: 120 },
  { title: '城市', dataIndex: 'city', width: 120 },
  { title: '详细地址', dataIndex: 'address', width: 300 },
  {
    title: '显示状态',
    dataIndex: 'status',
    hideInTable: true,
    valueType: 'select',
    formFieldProps: {
      options: [
        { label: '正常', value: '10' },
        { label: '已屏蔽', value: '11' },
        { label: '未启用', value: '12' },
      ],
    },
  },
  {
    title: '显示状态',
    dataIndex: 'status',
    sorter: {
      compare: (a, b) => a.status - b.status,
    },
    width: 120,
    hideInSearch: true,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 220,
    btnGroup: [
      {
        name: '查看',
        key: 'previewAddress',
        type: RButtonType.Primary,
      },
      {
        name: '编辑',
        key: 'editAddress',
        type: RButtonType.Primary,
      },
      {
        name: '删除',
        key: 'showDeleteModal',
        type: RButtonType.Primary,
      },
      {
        name: '设为默认地址',
        key: 'handleSetDefault',
        type: RButtonType.Primary,
        display: (record: any) => {
          return Number(record.is_default) !== 1;
        },
      },
    ],
  },
];

/**
 *
 */

interface viewFormItem {
  label: string;
  key: string;
}

interface viewForm {
  title: string;
  item: viewFormItem[];
}

export const viewFormGroup: viewForm[] = [
  {
    title: '基础信息',
    item: [
      {
        label: '自增ID',
        key: 'id',
      },
      {
        label: '服务商ID',
        key: 'server_id',
      },
    ],
  },
  {
    title: '地址信息',
    item: [
      {
        label: '省份',
        key: 'province',
      },
      {
        label: '城市',
        key: 'city',
      },
      {
        label: '详细地址',
        key: 'address',
      },
      {
        label: '经度',
        key: 'lon',
      },
      {
        label: '纬度',
        key: 'lat',
      },
    ],
  },
  {
    title: '联系信息',
    item: [
      {
        label: '公司形象图片',
        key: 'cover',
      },
      {
        label: '联系电话',
        key: 'tell',
      },
      {
        label: '服务承诺',
        key: 'rent_desc',
      },
    ],
  },
  {
    title: '系统信息',
    item: [
      {
        label: '创建时间',
        key: 'created_at',
      },
      {
        label: '更新时间',
        key: 'updated_at',
      },
      {
        label: '显示状态',
        key: 'status',
      },
    ],
  },
];
