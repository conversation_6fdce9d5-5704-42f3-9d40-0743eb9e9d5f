import { nextTick, ref } from 'vue';
import { message } from 'ant-design-vue';
import moment from 'moment/moment';

import printCss from '../components/print-item/index.css?raw';
import { IExpressData, IPrintData, IPrintOrders } from '../data';
import { getSFPrintData } from '../services';

const usePrint = (options: { expressData: Ref<IExpressData>; printContainerRef: Ref<HTMLElement | null> }) => {
  const printDataList = ref<IPrintOrders[]>([]);
  const printDate = ref({ date: moment().format('YYYY-MM-DD'), time: moment().format('HH:mm') });

  const initData = () => {
    printDataList.value = [];
    printDate.value = { date: moment().format('YYYY-MM-DD'), time: moment().format('HH:mm') };
  };

  const handlePrint = async (e: Event, index: number, isPlatformOrder: boolean, isNotWaitSend: boolean) => {
    e.stopPropagation();
    if (isPlatformOrder || isNotWaitSend) return;
    const order_id = options.expressData.value.server_send[index]?.order_id;
    // 格式化数据
    const handleFormat = (data: IPrintData) => {
      const result: IPrintOrders[] = [];
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          const orders = data[key].orders;
          result.push(...orders);
        }
      }
      return result;
    };

    // 创建iframe
    const handleCreateIframe = () => {
      const printContainer = options.printContainerRef.value;
      const printContent = `<style>.order { border: 1px solid #ccc; }${printCss}</style>` + printContainer.innerHTML;
      const iframe = document.createElement('iframe');
      iframe.style.position = 'fixed';
      iframe.style.right = '0';
      iframe.style.bottom = '0';
      iframe.style.width = '0';
      iframe.style.height = '0';
      iframe.style.border = '0';
      document.body.appendChild(iframe);
      const doc = iframe.contentWindow?.document;
      if (doc) {
        doc.open();
        doc.write(
          '<!DOCTYPE html><html><head><title>运输服务详情-打印快递单</title><style>body{margin:0;padding:0;}@media print{.print{page-break-after:avoid;}}</style></head><body>' +
            printContent +
            '</body></html>',
        );
        doc.close();
        iframe.contentWindow?.focus();
        iframe.contentWindow?.print();
      }
      initData();
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);
    };

    try {
      const { data } = await getSFPrintData({ order_id });
      if (!data || (data && Object.keys(data).length === 0)) {
        message.error('无可打印快递单');
        return;
      }
      printDataList.value = handleFormat(data as IPrintData);
      const m = moment();
      printDate.value = { date: m.format('YYYY-MM-DD'), time: m.format('HH:mm') };
      nextTick(() => {
        setTimeout(() => {
          handleCreateIframe();
        }, 200);
      });
    } catch (error) {
      message.error('获取打印数据失败');
    }
  };

  return {
    handlePrint,
    printDataList,
    printDate,
  };
};

export default usePrint;
