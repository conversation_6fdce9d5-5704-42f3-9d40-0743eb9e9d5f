<template>
  <div class="base-info-mian">
    <div class="title-block">
      <div class="line" />
      <div class="title">
        附件
      </div>
    </div>
    <image-upload
      ref="imageUpload"
      class="image-upload"
      quality="o"
      upload-text="上传附件"
      :value="imageValue"
      @change="imageUploadChange"
    />
  </div>
</template>

<script setup lang="ts">
import { useVModel } from '@/hook';
import ImageUpload from '@/components/image-upload/image-upload.vue';

const props = defineProps<{
  imageValue: any[];
}>();
const emit = defineEmits(['update:imageValue']);

const imageValue = useVModel(props, 'imageValue', emit);
const imageUploadChange = (arr: string[]) => {
  imageValue.value = arr;
};
</script>

<style scoped lang="less">
.base-info-mian {
  padding: 24px 24px 100px 24px;
  .title-block {
    display: flex;
    align-items: center;
    .line {
      width: 4px;
      height: 16px;
      background: #00c8be;
      border-radius: 2px;
    }
    .title {
      margin-left: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
    }
  }
  .form-mian {
    margin-top: 24px;
  }
}
.image-upload {
  margin-top: 16px;
}
</style>
