/** 当前显示组件 */
export enum EShowComponent {
  /** 升级面板-指示升级的开始状态和结束状态 */
  Panel,
  /** 步骤管理 */
  StepManagement,
  /** 升级条件 */
  Condition,
  /** 申请表单-组合多种升级类型表单 */
  ApplicationForm,
}

/** 入驻状态 */
export enum EEnterStatus {
  /** 已通过 */
  Passed = '10',
  /** 待审核 */
  PendingAudit = '11',
  /** 未通过 */
  Reject = '12',
}

/** 门店类型 */
export enum EStoreType {
  /** 线上门店 */
  Online = '10',
  /** 线下门店 */
  Offline = '11',
}

/** Condition 组件状态 */
export enum EConditionStatus {
  /** 还未查询，需点击查询按钮获取条件数据 */
  NotYetQueried,
  /** 满足 */
  Satisfied,
  /** 不满足 */
  NotSatisfied,
}

/** 入驻类目类型 */
export enum EEnterCategory {
  /** 手机 */
  MobilePhone = '10',
  /** 电脑 */
  Computer = '11',
  /** 非手机电脑 */
  NonMobileComputer = '12',
}

/** 申请表单类型 */
export enum EFormType {
  /** 手机/电脑类目&线下门店 */
  MobileComputer_Offline,
  /** 手机/电脑类目&线上门店 */
  MobileComputer_Online,
  /** 非手机、电脑类目 */
  NonMobileComputer,
}

/** */
export interface IOption {
  value: string | number;
  label: string;
}

/** 入驻前数据 */
export interface IBeforeEnterData {
  /** 店铺经营类目 */
  category: string;
  /** 入驻类目 */
  enter_category: EEnterCategory;
  /** 门店类型 */
  store_type: IOption[] | string;
  /** 企业类型 */
  company_type: string;
  /** 营业执照 */
  license_image: string;
}

/** 共享数据 */
export interface ISharedData extends IBeforeEnterData {
  /** 当前显示组件 */
  activeComponent: EShowComponent;
  /** 入驻状态 */
  audit_status: EEnterStatus;
  store_type: string;
  store_type_text: string;
  enter_category_text: string;
}

/** 入驻条件 */
export interface ICondition {
  /** 条件名称 */
  condition_name: string;
  /** 条件值 */
  value: number | string;
  /** 条件结果 */
  result: boolean;
  /** 条件 */
  key: string;
}
