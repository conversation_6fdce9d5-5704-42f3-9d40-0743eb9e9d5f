<template>
  <!-- 风控报告进阶版-内容 -->
  <a-spin
    size="large"
    :spinning="spinning"
  >
    <div class="report-advance">
      <div class="report-title-wrapper title-padding flex-wrap flex-y-center">
        <div class="title-icon title-icon-results" />
        <span class="title">风控结果</span>
        <span
          v-if="errorInfo.isRecommend"
          class="title-tips"
        >
          以下数据仅供示例参考，如果想获取详细信息请点击
          <a @click.prevent="goRiskReportPro">开通风控报告进阶版</a>
        </span>
        <span
          v-else
          class="title-tips"
        > 基于大数据结果预测 </span>
        <div
          v-if="!errorInfo.isRecommend"
          class="header-info"
        >
          <div>订单编号：{{ data.base_info.order_id }}</div>
          <div class="header-info-line" />
          <div>生成时间：{{ data.base_info.created_time }}</div>
        </div>
      </div>
      <div class="report-body">
        <div class="report-body-content">
          <!-- 风控结果 -->
          <div class="report-card risk-control-results">
            <div
              class="recommend-bg"
              style="padding: 24px 0"
            >
              <div
                v-if="errorInfo.isRecommend"
                class="recommend-content white-mask"
              >
                <div class="lock-icon" />
                <div class="lock-tip">
                  当前为展示数据，请先开通风控报告进阶版获取详细数据
                </div>
                <div
                  class="opened-btn"
                  @click="goRiskReportPro"
                >
                  开通风控报告进阶版
                </div>
              </div>
              <div :class="['header-bg-wrap', 'header-bg-wrap-' + getStatus()]">
                <div :class="['header-res-title', 'header-res-title-' + getStatus()]">
                  <div
                    class="header-icon-wrap"
                    :class="['header-icon-' + getStatus()]"
                  />
                  <template v-if="!errorInfo.isRecommend">
                    {{ data.result_info?.risk_levels?.value || `风控${data.result_info?.risk_qualitative?.value}` }}
                  </template>
                  <template v-else>
                    风控通过
                  </template>
                </div>
                <div
                  class="face-recognition"
                  :class="[isFace ? 'success' : 'error']"
                >
                  <div
                    class="face-recognition-icon"
                    :class="[isFace ? 'success' : 'error']"
                  />
                  <span>人脸识别</span> {{ isFace ? '通过' : '未通过' }}
                </div>
              </div>
              <div class="risk-control-results-log" />
              <div class="content-bg-wrap">
                <div class="content-wrap">
                  <template v-if="errorInfo.isRecommend">
                    【风控建议】已经通过
                  </template>
                  <template v-else>
                    {{ data.result_info?.risk_describe?.value || data.result_info.risk_result.value }}
                  </template>

                  <div class="precautions">
                    <a-tag class="tag">
                      注意事项
                    </a-tag>
                    <span>
                      <template v-if="errorInfo.isRecommend"> 已经通过 </template>
                      <template v-else-if="data.result_info.risk_advise.value || data.service_label_info">
                        <template v-if="data.service_label_info">支持&lt;{{ data.service_label_info }}&gt;。</template>
                        {{ data.result_info.risk_advise.value }}
                      </template>
                      <template v-else> 无 </template>
                    </span>
                  </div>
                </div>
              </div>
              <!-- 提示 -->
              <div class="attention-box">
                <InfoCircleOutlined class="attention-box-icon" />
                <span>风控报告涉及用户的个人隐私信息，不能向任何第三方透露（含租户），否则会构成侵犯公民个人信息，严重者将承担刑事责任。</span>
              </div>
            </div>
          </div>

          <!-- 风控复审 -->
          <div
            v-if="data?.review_info?.review_status"
            class="risk-result-plus-wrap"
          >
            <div class="top">
              <img
                class="icon"
                src="https://img1.rrzuji.cn/uploads/scheme/2409/02/m/V2fDvulBx5k7Hc5RjJdi.png"
              >
              <span class="title">{{ data?.review_info?.review_status === 8 ? '风控复审' : '风控复审结果' }}</span>
              <span class="desc">融入更多权威风控因子，进行更深层次的复审，为您提供更全面的风险评估</span>
            </div>
            <div class="bottom">
              <div class="status">
                <template v-if="data?.review_info?.review_status === 10">
                  <img
                    class="icon"
                    src="https://img1.rrzuji.cn/uploads/scheme/2409/02/m/noEvWyHCNFwad7sV1ovC.png"
                  >
                  复审通过
                </template>
                <template v-else-if="data?.review_info?.review_status === 11">
                  <img
                    class="icon"
                    src="https://img1.rrzuji.cn/uploads/scheme/2409/02/m/mhisYfSeEylQodjJ89R7.png"
                  >
                  复审不通过
                </template>
                <template v-else>
                  <a-spin
                    :indicator="
                      h(LoadingOutlined, {
                        style: {
                          fontSize: '16px',
                          color: 'rgba(6,21,51,0.85)',
                          marginRight: '4px',
                        },
                        spin: true,
                      })
                    "
                  />
                  评估中...
                </template>
              </div>
              <div
                v-if="data?.review_info?.content"
                class="suggest"
              >
                {{ data?.review_info?.content }}。
              </div>
            </div>
            <div
              v-if="data?.review_info?.created_at"
              class="result-time"
            >
              风控复审结果生成时间：{{ data?.review_info?.created_at }}
            </div>
          </div>
          <!-- 风控日志 -->
          <div class="report-card risk-log-wrap">
            <div
              class="report-title-wrapper flex-wrap flex-y-center"
              style="padding: 22px 16px 20px 16px"
            >
              <div class="title-icon title-icon-log" />
              <span class="title">风控日志</span>
              <span
                v-if="errorInfo.isRecommend"
                class="title-tips"
              >
                以下数据仅供示例参考，如果想获取详细信息请点击
                <a @click.prevent="goRiskReportPro"> 开通风控报告进阶版 </a>
              </span>
              <span
                v-else
                class="title-tips"
              >数据有效期截至：{{ data.valid_time }}</span>
            </div>
            <div
              class="recommend-bg"
              style="padding-bottom: 16px"
            >
              <div
                v-if="errorInfo.isRecommend"
                class="recommend-content white-mask"
              >
                <div class="lock-icon" />
                <div class="lock-tip">
                  当前为展示数据，请先开通风控报告进阶版获取详细数据
                </div>
                <div
                  class="opened-btn"
                  @click="goRiskReportPro"
                >
                  开通风控报告进阶版
                </div>
              </div>
              <!-- 欺诈风险 -->
              <template v-if="data.log_info.fraud_risk_info?.length || errorInfo.isRecommend">
                <div class="title-wrap">
                  <div class="line" />
                  欺诈风险
                </div>
                <a-table
                  bordered
                  class="fraud-risk"
                  :columns="fraudRiskColumns"
                  :data-source="showFraudRiskInfo"
                  :pagination="{ showLessItems: true, pageSize: 8 }"
                  style="min-height: 500px; padding: 0 12px"
                >
                  <template #bodyCell="{ record, column }">
                    <template v-if="column.dataIndex === 'label'">
                      <div
                        v-if="record.id === 42"
                        class="index"
                      >
                        <span>消费能力评分</span>
                        <a-tooltip v-if="record.desc">
                          <template #title>
                            {{ record.desc }}
                          </template>
                          <img
                            src="https://img1.rrzuji.cn/uploads/scheme/2301/04/m/oV7148Vfi8QUo6bo6t8f.png"
                            style="margin-left: 4px"
                          >
                        </a-tooltip>

                        <span class="label-des">分数越高消费能力越好</span>
                      </div>
                      <div
                        v-else
                        class="index"
                      >
                        <template v-if="data.is_view">
                          {{ record }}
                        </template>
                        <template v-else>
                          <span>{{ record.label }}</span>
                          <a-tooltip v-if="record.desc">
                            <template #title>
                              {{ record.desc }}
                            </template>
                            <img
                              src="https://img1.rrzuji.cn/uploads/scheme/2301/04/m/oV7148Vfi8QUo6bo6t8f.png"
                              style="margin-left: 4px"
                            >
                          </a-tooltip>
                        </template>
                      </div>
                    </template>
                    <template v-else-if="column.dataIndex === 'detail'">
                      <div
                        v-if="record.id === 42"
                        class="detail"
                      >
                        <div class="progress">
                          <a-progress
                            :percent="record.value"
                            :show-info="false"
                            status="active"
                            stroke-color="#3777FFFF"
                            :stroke-width="6"
                            style="width: 300px"
                          />
                          <span>能力评分：{{ record.value }}</span>
                        </div>
                      </div>
                      <div
                        v-else
                        class="detail"
                      >
                        <template v-if="data.is_view">
                          <a-tag>数据过期</a-tag>
                        </template>
                        <template v-else>
                          <div
                            v-if="record.type === 1"
                            class="progress"
                          >
                            <a-progress
                              :percent="PROGRESS_STATUS[record.value]['percent']"
                              :show-info="false"
                              status="active"
                              :stroke-color="PROGRESS_STATUS[record.value]['color']"
                              :stroke-width="6"
                              style="width: 300px"
                            />
                            <span :style="{ color: PROGRESS_STATUS[record.value]['textColor'] }">{{
                              PROGRESS_STATUS[record.value]['text']
                            }}</span>
                          </div>

                          <template v-else-if="record.type === 0">
                            <div :class="{ tag: true, hit: record.value >= 1 }">
                              {{ record.value >= 1 ? '命中' : '未命中' }}
                            </div>
                          </template>
                        </template>
                      </div>
                    </template>
                  </template>
                </a-table>
              </template>
              <!-- 偿还能力 -->
              <template v-if="data.log_info.solvency_info?.length || errorInfo.isRecommend">
                <div class="title-wrap">
                  <div class="line" />
                  偿还能力
                </div>
                <a-table
                  bordered
                  class="repay-capacity"
                  :columns="solvencyInfoColumns"
                  :data-source="showSolvencyInfo"
                  :pagination="{ showLessItems: true, pageSize: 8 }"
                  style="min-height: 500px; padding: 0 12px"
                >
                  <template #bodyCell="{ record, column }">
                    <template v-if="column.dataIndex === 'label'">
                      <div class="index">
                        <template v-if="data.is_view">
                          {{ record }}
                        </template>
                        <template v-else>
                          <span>{{ record.label }}</span>
                          <a-tooltip v-if="record.desc">
                            <template #title>
                              {{ record.desc }}
                            </template>
                            <img
                              src="https://img1.rrzuji.cn/uploads/scheme/2301/04/m/oV7148Vfi8QUo6bo6t8f.png"
                              style="margin-left: 4px"
                            >
                          </a-tooltip>
                        </template>
                      </div>
                    </template>
                    <template v-else-if="column.dataIndex === 'detail'">
                      <div class="detail">
                        <template v-if="data.is_view">
                          <a-progress
                            :percent="0"
                            :show-info="false"
                            style="width: 300px"
                          />
                        </template>
                        <template v-else>
                          <div
                            v-if="record.type === 1"
                            class="progress"
                          >
                            <template v-if="record.version === 1">
                              {{ record.value }}
                            </template>
                            <template v-else>
                              <a-progress
                                :percent="PROGRESS_STATUS[record.value]['percent']"
                                :show-info="false"
                                status="active"
                                :stroke-color="PROGRESS_STATUS[record.value]['color']"
                                :stroke-width="6"
                                style="width: 300px"
                              />
                              <span :style="{ color: PROGRESS_STATUS[record.value]['textColor'] }">{{
                                PROGRESS_STATUS[record.value]['text']
                              }}</span>
                            </template>
                          </div>
                          <div
                            v-if="record.type === 0"
                            :class="{ tag: true, hit: record.value >= 1 }"
                          >
                            {{ record.value >= 1 ? '命中' : '未命中' }}
                          </div>
                        </template>
                      </div>
                    </template>
                  </template>
                </a-table>
              </template>
              <!-- 平台交易记录 -->
              <button
                v-if="data.platform_trade_info?.[0]?.version !== 1"
                class="platform-trade-info"
                @click="showLog"
              >
                <img src="https://img1.rrzuji.cn/uploads/scheme/2301/05/m/QX81Q7Ez7UF0DtEdiRfg.png">
                <span>查看TA在该平台的交易记录</span>
              </button>
              <template v-else>
                <div class="title-wrap">
                  <div class="line" />
                  平台交易数据
                </div>
                <a-table
                  :columns="platformRiskColumns"
                  :data-source="data.platform_trade_info?.[0]?.value || []"
                  style="padding: 0 12px"
                />
              </template>

              <!-- 司法风险信息 -->
              <template v-if="isShowJudicial">
                <div class="title-wrap">
                  <div class="line" />
                  <span>司法风险信息</span>
                  <a-tooltip v-if="data.judicial_risk_info[0] && data.judicial_risk_info[0].desc">
                    <template #title>
                      {{ data.judicial_risk_info[0].desc }}
                    </template>
                    <img
                      src="https://img1.rrzuji.cn/uploads/scheme/2301/04/m/oV7148Vfi8QUo6bo6t8f.png"
                      style="margin-left: 8px"
                    >
                  </a-tooltip>
                </div>
                <div class="judicial-risk-info">
                  <div :class="['table', data.is_view === 1 ? 'blur' : '']">
                    <div class="t_header">
                      <div class="t_index">
                        <span>序号</span>
                        <span class="title">风险板块</span>
                        <span class="title">法院</span>
                        <span class="title">案号</span>
                        <span
                          v-if="data.judicial_risk_info[0].version === 1"
                          class="title"
                        >立案日期</span>
                        <span class="title">匹配度</span>
                        <span
                          v-if="data.judicial_risk_info[0].version === 1"
                          class="title"
                        >详情</span>
                      </div>
                    </div>

                    <div
                      v-if="data.judicial_risk_info[0]['value']['td']?.length"
                      class="t_body"
                    >
                      <div
                        v-for="item in data.judicial_risk_info[0]['value']['td']"
                        :key="item.id"
                        class="t_index"
                      >
                        <span>{{ item.id }}</span>
                        <span>{{ item.query_item }}</span>
                        <span>{{ item.court_name }}</span>
                        <span>{{ item.case_no }}</span>
                        <span v-if="data.judicial_risk_info[0].version === 1">{{ item.case_at }}</span>
                        <span>{{ item.match_degree }}</span>
                        <span v-if="data.judicial_risk_info[0].version === 1"><a-button
                          calss="r-btn-link"
                          type="link"
                          @click="onViewjudicialDetail(item.detail)"
                        >
                          详情
                        </a-button></span>
                      </div>
                    </div>
                    <div v-else>
                      <a-empty />
                    </div>
                  </div>
                  <div class="info_query_link">
                    <div class="info_query_link_tip">
                      注释：匹配度低于85%，不一定是本人，需进一步确认
                    </div>
                    <div>
                      信息查询：
                      <a
                        href="https://wenshu.court.gov.cn/website/wenshu/181029CR4M5A62CH/index.html?"
                        target="_blank"
                      >中国裁判文书网</a>
                      <span>|</span>
                      <a
                        href="http://zxgk.court.gov.cn/"
                        target="_blank"
                      >中国执行信息公开网</a>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="footer-wrap">
            <div
              v-if="errorInfo.isRecommend"
              class="opened-btn"
              style="margin-bottom: 12px"
              @click="goRiskReportPro"
            >
              开通风控报告进阶版
            </div>

            -未经许可禁止转载-
          </div>
          <div
            v-if="showAdvanceRateModel && data.evaluate == '0'"
            class="advance-rate"
          >
            <div class="rate_head">
              <h2>风控报告满意度调查</h2>
              <close-outlined
                class="close_icon"
                @click="showAdvanceRateModel = false"
              />
            </div>
            <span>请评价本次报告</span>
            <div class="risk_advance_report_rate">
              <a-rate
                v-model:value="advanceReportRate"
                allow-half
              />
            </div>
            <div class="rate_footer">
              <span :style="{ color: advanceRateInfo.color }">{{ advanceRateInfo.text }}</span>
              <a-button
                class="rate_btn"
                type="primary"
                @click="onSubAdvanceRate"
              >
                提交
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-spin>
  <PlatformLog
    v-model:visible="logVisible"
    :data="data.platform_trade_info[0]"
  />
</template>

<script setup lang="ts">
import { computed, defineProps, h, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { CloseOutlined, InfoCircleOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { router } from '@/router/router';

import PlatformLog from './components/platform-log.vue';
import {
  ADVANCE_RATE,
  demoRiskInfo,
  demoSolvencyInfo,
  fraudRiskColumns,
  platformRiskColumns,
  PROGRESS_STATUS,
  solvencyInfoColumns,
} from './config';
import { APIPreviewReport, evaluateReport, getAdvanceReport } from './service';

interface IProps {
  orderId?: string;
  previewParams?: { report_id: string; version_id: string } | { batchData: string } | null;
}

const props = withDefaults(defineProps<IProps>(), {
  orderId: '',
  previewParams: null,
});

const emit = defineEmits(['fail']);
const route = useRoute();
const isSuper = computed(() => route.query.role === 'super');

const origin = route.query.origin;

const spinning = ref(false);
const data = ref<any>({
  // 消费能力评分
  evaluate: null,
  base_info: {},
  log_info: {
    fraud_risk_info: [],
    solvency_info: [],
  },
  judicial_risk_info: [],
  result_info: {
    risk_advise: {},
    risk_debt_index: {},
    risk_level: {},
    risk_qualitative: {},
    risk_result: {},
    risk_solvency: {},
    risk_levels: {},
    risk_describe: {},
  },
  service_label_info: '',
  platform_trade_info: [],
  user_info: [],
  review_info: {
    title: '',
    content: '',
    review_status: undefined,
  },
});

const errorInfo = reactive({
  isRecommend: false,
  message: '',
  time: '',
});

const onViewjudicialDetail = (id: string) => {
  const { href } = router.resolve({
    name: 'caseDetail',
    query: { id, ...route.query },
  });
  window.open(href, '_blank');
};
const showAdvanceRateModel = ref(true);

const isFace = computed(() => {
  return data.value.user_info.some((item: any) => item.label === '活体检测' && item.value === '是');
});

const isShowJudicial = computed(() => {
  if (props.previewParams) {
    // 预览报告
    return data.value.judicial_risk_info?.length;
  } else {
    if (data.value.judicial_risk_info?.length) {
      return data.value.judicial_risk_info[0]?.version === 1 || data.value.judicial_risk_info[0]['value']['td']?.length;
    } else {
      return false;
    }
  }
});

const initReportData = async () => {
  spinning.value = true;
  let res;
  try {
    if (props.previewParams) {
      res = await APIPreviewReport(props.previewParams);
    } else {
      res = await getAdvanceReport({ ...props, isSuper: isSuper.value });
    }

    data.value = {
      ...res.data,
      platform_trade_info: res.data.platform_trade_info ? res.data.platform_trade_info : [],
    };
    errorInfo.isRecommend = false;
  } catch (error: any) {
    if ([10022, 10023].includes(error.status)) {
      return Modal.confirm({
        title: '提示',
        content: error.message,
        okText: '立即前往开通',
        cancelText: '知道了',
        onOk() {
          goRiskReportPro();
        },
      });
    }
    if ([10021].includes(error.status)) {
      errorInfo.isRecommend = true;
      errorInfo.message = error.message;
      errorInfo.time = dayjs().format('YYYY-MM-DD HH:mm:ss');
    } else {
      message.error(error.message);
      emit('fail');
    }
  } finally {
    spinning.value = false;
  }
};

const advanceReportRate = ref<number>(0);
const advanceRateInfo = computed(() => {
  let key = 0;
  if (advanceReportRate.value == 0) {
    return 0;
  } else if (advanceReportRate.value <= 2) {
    key = 1;
  } else if (advanceReportRate.value <= 3.5) {
    key = 2;
  } else if (advanceReportRate.value <= 4.5) {
    key = 3;
  } else {
    key = 4;
  }
  return ADVANCE_RATE[key];
});

const onSubAdvanceRate = () => {
  evaluateReport(
    {
      order_id: props.orderId,
      evaluate: advanceReportRate.value,
    },
    isSuper.value,
  ).then(res => {
    message.success(res.message);
    showAdvanceRateModel.value = false;
  });
};

const showFraudRiskInfo = computed(() => {
  return errorInfo.isRecommend ? demoRiskInfo : data.value.log_info.fraud_risk_info;
});

const showSolvencyInfo = computed(() => {
  return errorInfo.isRecommend ? demoSolvencyInfo : data.value.log_info.solvency_info;
});

const goRiskReportPro = () => {
  return window.open(`${origin}/application-center/page?id=7`);
};

onMounted(() => {
  initReportData();
});

// 平台交易记录弹窗
const logVisible = ref(false);
const showLog = () => {
  logVisible.value = true;
};

function getStatus() {
  if (errorInfo.isRecommend) {
    return 'success';
  }
  let type = '';
  if (data.value.result_info.risk_qualitative.result === '1') {
    type = 'success';
  } else if (data.value.result_info.risk_qualitative.result === '2') {
    type = 'warning';
  } else {
    type = 'error';
  }
  return type;
}
</script>

<style lang="less" scoped>
// 内容左右边距
@left-and-right-margin: 24px;
.report-advance {
  width: 100%;
  padding-top: 24px;
  // 禁止用户复制
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  -o-user-select: none;
  user-select: none;

  .report-title-wrapper {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;

    &.title-padding {
      padding: 0 @left-and-right-margin 16px @left-and-right-margin;
    }

    .title {
      margin-right: 8px;
      margin-left: 8px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }

    .title-icon {
      width: 16px;
      height: 16px;
    }

    .title-icon-results {
      background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/9Npb8emm9jjUHqJ0VxEU.png') no-repeat;
      background-size: 100%;
    }

    .title-icon-log {
      background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/0K6lsWxgPhulgPO4nGph.png') no-repeat;
      background-size: 100%;
    }

    .title-tips {
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 12px;
      line-height: 22px;
    }

    a {
      margin-left: 4px;
      color: #3777ff;
      text-decoration: underline;
    }

    .header-info {
      display: flex;
      align-items: center;
      margin-left: 32px;
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;

      .header-info-line {
        width: 1px;
        height: 10px;
        margin-right: 12px;
        margin-left: 12px;
        background: rgba(6, 21, 51, 0.15);
        border-radius: 0 0 0 0;
      }
    }
  }

  .report-body {
    width: 100%;
    padding-bottom: 24px;
    background: #fff;
    border-radius: 8px 8px 8px 8px;

    .report-body-content {
      position: relative;
      padding: 0 @left-and-right-margin;
    }

    .opened-btn {
      width: 198px;
      height: 30px;
      color: #fff;
      font-weight: 400;
      font-size: 14px;
      line-height: 30px;
      text-align: center;
      background: #3777ff;
      border-radius: 15px 15px 15px 15px;
      cursor: pointer;
    }

    // 未开通的遮罩
    .recommend-bg {
      position: relative;

      .recommend-content {
        position: absolute;
        top: 0;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        backdrop-filter: blur(4px);

        &.white-mask {
          background: linear-gradient(180deg, rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.65) 100%);
        }

        .lock-icon {
          width: 32px;
          height: 39px;
          background: url('https://img1.rrzuji.cn/uploads/scheme/2504/08/m/hFoMRbNkTduKtBsHjqxo.png') no-repeat;
          background-size: 100%;
        }

        .lock-tip {
          margin: 20px 0;
          color: rgba(6, 21, 51, 0.65);
          font-weight: 400;
          font-size: 18px;
          line-height: 22px;
        }
      }
    }

    // 人脸识别
    .face-recognition {
      display: flex;
      align-items: center;
      height: 22px;
      padding: 0 8px;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      border-radius: 4px 4px 4px 4px;

      &.success {
        color: #52c41a;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
      }

      &.error {
        color: #ff4d4f;
        background: #fff1f0;
        border: 1px solid #eb8f8f;
      }

      &-icon {
        width: 12px;
        height: 12px;
        margin-right: 8px;

        &.success {
          background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/w4KQK6KeZnCHSg4rFpXE.png') no-repeat;
          background-size: 100%;
        }

        &.error {
          background: url('https://img1.rrzuji.cn/uploads/scheme/2502/21/m/deVDbU5z9L5OYXrKmipn.png') no-repeat;
          background-size: 100%;
        }
      }
    }

    .report-card {
      background: linear-gradient(180deg, #fff 0%, #fff 100%);
      border-radius: 8px 8px 8px 8px;
      box-shadow: 0 4px 16px 0 rgba(6, 21, 51, 0.08);
    }

    // 风控结果
    .risk-control-results {
      position: relative;
      width: 100%;
      padding: 1px 0;

      &-log {
        position: absolute;
        top: 0;
        right: 0;
        width: 247px;
        height: 181px;
        background: url('https://img1.rrzuji.cn/uploads/scheme/2504/08/m/BUZvPQrSk7OFyWBkyDwM.png') no-repeat;
        background-size: 100%;
      }

      .header-bg-wrap {
        position: relative;
        display: flex;
        align-items: center;
        width: 640px;
        height: 41px;
        margin: 0 16px;
        padding: 2px 12px;
        border-radius: 4px 4px 4px 4px;

        &-success {
          background: linear-gradient(
            90deg,
            rgba(142, 225, 134, 0.06) 0%,
            rgba(142, 225, 134, 0.06) 81%,
            rgba(142, 225, 134, 0) 100%
          );
          background-size: 100%;
        }

        &-warning {
          background: linear-gradient(
            90deg,
            rgba(225, 179, 134, 0.06) 0%,
            rgba(225, 179, 134, 0.06) 81%,
            rgba(225, 179, 134, 0) 100%
          );
          background-size: 100%;
        }

        &-error {
          background: linear-gradient(
            90deg,
            rgba(251, 133, 133, 0.06) 0%,
            rgba(251, 133, 133, 0.06) 81%,
            rgba(251, 133, 133, 0) 100%
          );
          background-size: 100%;
        }

        .header-res-title {
          display: flex;
          align-items: center;
          margin-right: 12px;
          color: #fff;
          font-weight: bold;
          font-size: 24px;
          line-height: 36px;

          &-success {
            color: #52c41a;
          }

          &-warning {
            color: #faad14;
          }

          &-error {
            color: #ff4d4f;
          }

          .header-icon-wrap {
            width: 24px;
            height: 24px;
            margin-right: 8px;

            &.header-icon-success {
              background: url('https://img1.rrzuji.cn/uploads/scheme/2504/07/m/gOMfW9SASxVqPbpdsHQK.png') no-repeat;
              background-size: 100%;
            }

            &.header-icon-warning {
              background: url('https://img1.rrzuji.cn/uploads/scheme/2504/07/m/0Bl8D8GnWTcF7rpOFaML.png') no-repeat;
              background-size: 100%;
            }

            &.header-icon-error {
              background: url('https://img1.rrzuji.cn/uploads/scheme/2504/07/m/4oT9T2QyI9zhLRwtqEnK.png') no-repeat;
              background-size: 100%;
            }
          }
        }
      }

      .content-bg-wrap {
        .content-wrap {
          margin: 20px 16px 0;
          color: rgba(6, 21, 51, 0.85);
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          //注意事项
          .precautions {
            margin-top: 20px;
            padding-top: 20px;
            color: rgba(6, 21, 51, 0.65);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            border-top: 1px solid rgba(32, 32, 32, 0.04);

            .tag {
              color: #fa8c16;
              background: #fff7e6;
              border: 1px solid #ffd591;
            }
          }
        }
      }
    }

    // 提示
    .attention-box {
      margin-top: 17px;
      padding: 6px 16px;
      color: #ffa940;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      background: #fffcf5;
      border-radius: 8px 8px 8px 8px;

      &-icon {
        margin-right: 4px;
        color: #ffa940;
        font-size: 14px;
      }
    }

    // 风控复审
    .risk-result-plus-wrap {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-top: 24px;
      padding: 31px 32px 24px;
      overflow: hidden;
      background: url('https://img1.rrzuji.cn/uploads/scheme/2409/02/m/5NaKmL6dvYxUlSuRqIAN.png') no-repeat center/cover;
      background-size: 100% 100%;
      border-radius: 4px;

      .top {
        display: flex;
        align-items: center;

        & > .icon {
          width: 24px;
          height: 24px;
          object-fit: cover;
        }

        .title {
          margin-right: 10px;
          margin-left: 6px;
          color: #ffe7c3;
          font-weight: bold;
          font-size: 24px;
        }

        .desc {
          color: #ffe7c3;
          font-size: 14px;
        }
      }

      .bottom {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .status {
          display: flex;
          align-items: center;
          margin-right: 8px;
          padding: 4px 12px;
          color: #3e3018;
          font-weight: 500;
          font-size: 14px;
          background: linear-gradient(90deg, #ffe4b0 0%, #ffbe41 100%);
          border-radius: 2px;

          & > .icon {
            width: 14px;
            height: 14px;
            margin-right: 4px;
            object-fit: contain;
          }
        }

        .suggest {
          min-width: 548px;
          padding: 4px 8px;
          color: #fff;
          font-size: 14px;
          background: rgba(147, 124, 79, 0.65);
          border: 1px solid rgba(255, 231, 195, 0.65);
          border-radius: 2px;
        }
      }

      .result-time {
        margin-top: 16px;
        color: rgba(255, 231, 195, 0.65);
        font-size: 14px;
        line-height: 22px;
      }
    }

    // 风控日志
    .risk-log-wrap {
      margin-top: 24px;
      background: linear-gradient(180deg, #fff 0%, #fffdfb 100%);
      border-radius: 8px 8px 8px 8px;

      .blur {
        -webkit-filter: blur(3px);
        -moz-filter: blur(3px);
        -o-filter: blur(3px);
        -ms-filter: blur(3px);
        filter: blur(3px);
      }

      .title-wrap {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        margin-left: 16px;
        padding: 8px 16px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;

        .line {
          width: 3px;
          height: 12px;
          margin-right: 6px;
          background: #3777ff;
          border-radius: 2px 2px 2px 2px;
        }
      }

      .fraud-risk,
      .repay-capacity {
        .index {
          img {
            width: 16px;
            height: 16px;
          }

          .label-des {
            margin-left: 8px;
            color: rgba(6, 21, 51, 0.45);
            font-size: 12px;
          }
        }

        .detail {
          .tag {
            width: 52px;
            height: 22px;
            color: #3777ff;
            font-size: 12px;
            line-height: 22px;
            text-align: center;
            background: #f0f7ff;
            border: 1px solid #b3d2ff;
            border-radius: 4px;

            &.hit {
              color: #f5222d;
              background: #fff1f0;
              border: 1px solid #ffa39e;
            }
          }

          .progress {
            span {
              margin-left: 16px;
              color: #3777ff;
              font-weight: 400;
              font-size: 14px;
            }
          }
        }
      }

      //平台交易记录
      .platform-trade-info {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 292px;
        height: 46px;
        margin: 32px auto 32px;
        color: #3777ff;
        background: #fff;
        border: 1px solid #3777ff;
        border-radius: 23px;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }

        &:hover {
          color: #6198ff;
          border-color: #6198ff;
        }

        &:active {
          color: #153db3;
          border-color: #153db3;
        }
      }

      // 司法风险信息
      .judicial-risk-info {
        margin: 0 16px 24px 16px;

        .table {
          margin-top: 16px;
          font-size: 14px;

          .t_index {
            display: flex;
            align-items: stretch;
            color: rgba(6, 21, 51, 0.85);

            span {
              width: 200px;
              padding: 12px 10px 12px 16px;
            }

            span:nth-child(1) {
              width: 88px;
            }
          }

          .t_header {
            background: #f9f9fb;

            .t_index {
              color: rgba(6, 21, 51, 0.65);
              border-bottom: 1px solid rgba(6, 21, 51, 0.06);

              .title {
                position: relative;
                display: flex;
                align-items: center;
                height: 55px;
                color: rgba(6, 21, 51, 0.85);
                font-weight: 500;
                font-size: 14px;
                line-height: 22px;
                background: #f9f9fb;

                &::before {
                  position: absolute;
                  top: 50%;
                  left: 0;
                  width: 1px;
                  height: 1.6em;
                  background-color: rgba(0, 0, 0, 0.06);
                  transform: translateY(-50%);
                  transition: background-color 0.3s;
                  content: '';
                }
              }
            }
          }

          .t_body {
            color: #061533d9;
            border-radius: 4px;

            .t_index {
              border-bottom: 1px solid rgba(6, 21, 51, 0.06);

              span + span {
                background-color: #fff;
              }
            }
          }
        }

        .info_query_link {
          display: flex;
          justify-content: space-between;
          margin-top: 32px;
          color: rgba(6, 21, 51, 0.85);

          .info_query_link_tip {
            display: inline-block;
            color: rgba(6, 21, 51, 0.45);
            font-weight: 400;
            font-size: 14px;
            font-style: normal;
            line-height: 22px;
            text-align: justified;
            text-transform: none;
          }

          a {
            color: #3777ff;

            &:hover {
              text-decoration: underline;
            }
          }

          span {
            margin: 0 8px;
            color: rgba(6, 21, 51, 0.15);
          }
        }
      }
    }

    .footer-wrap {
      width: max-content;
      margin: 0 auto;
      margin-top: 24px;
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
    }

    .advance-rate {
      position: absolute;
      top: 0;
      right: -272px;
      width: 248px;
      height: 182px;
      padding: 20px 16px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 0.05), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 3px 6px -4px rgba(0, 0, 0, 0.12);

      .rate_head {
        display: flex;
        align-items: center;
        margin-bottom: 14px;
      }

      h2 {
        margin: 0 62px 0 0;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 600;
        font-size: 14px;
      }

      span {
        color: rgba(6, 21, 51, 0.45);
        font-size: 12px;
      }

      .rate_footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 11px;

        span {
          font-weight: 600;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
