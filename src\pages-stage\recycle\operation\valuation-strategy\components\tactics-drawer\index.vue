<template>
  <div
    v-if="visible"
    ref="drawerWrapperRef"
    class="drawer-wrapper"
  >
    <a-drawer
      v-model:visible="visible"
      :body-style="{
        padding: '0',
      }"
      class="tactics-drawer"
      :closable="false"
      destroy-on-close
      :get-container="drawerWrapperRef"
      :mask-closable="false"
      placement="right"
      :title="drawerTitle"
      width="85%"
    >
      <template #extra>
        <close-outlined
          style="font-size: 16px; cursor: pointer"
          @click="close"
        />
      </template>
      <div class="tactics-drawer-container">
        <div
          :class="['side-plate', { expand: !chartsPanelVisible }]"
          @click="toggleChartsPanelVisible"
        >
          <template v-if="chartsPanelVisible">
            <span>收起价格趋势</span>
          </template>
          <template v-else>
            <span>展开价格趋势</span>
          </template>
          <double-left-outlined :rotate="chartsPanelVisible ? 180 : 0" />
        </div>
        <ChartsPanel
          :goods-name="goodsName"
          :init-params="defaultParams"
          :visible="chartsPanelVisible"
        />
        <div class="container-right">
          <VersionBlock
            v-if="versionIsLoad && !isEdit"
            :detail-is-load="!detailLoading"
            :version-options="versionList"
            @on-change-edit="onChangeEdit"
            @on-version-change="versionChange"
          />
          <template v-if="versionIsLoad">
            <a-spin
              :spinning="detailLoading"
              tip="Loading..."
            >
              <ComputedRuleBlock
                ref="computedRuleRef"
                v-model:schemeType="schemeType"
                :detail-res="detailRes"
                :goods-id="goodsId"
                :init-params="defaultParams"
                :is-now-version="isNowVersion"
                @on-load-list="getDetail"
              />
              <SkuDetailPanel :sku-info-list="skuInfoList" />
            </a-spin>
          </template>
        </div>
      </div>
      <template
        v-if="isEdit"
        #footer
      >
        <div class="flex-wrap flex-x-end">
          <a-button
            style="margin-right: 8px"
            @click="onCancel"
          >
            取消
          </a-button>
          <a-popconfirm
            cancel-text="取消"
            ok-text="确认"
            placement="topRight"
            title="确认对该商品进行策略调价吗？?"
            @confirm="onSubmit"
          >
            <a-button
              :loading="submitLoading"
              type="primary"
            >
              确认调价
            </a-button>
          </a-popconfirm>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, provide, Ref } from 'vue';
import { message } from 'ant-design-vue';
import { CloseOutlined, DoubleLeftOutlined } from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';
import { useModal } from '@/hook/component/use-modal';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import { IOpenDrawerParams, ISubmitSettingPrice, ITableDetail, IGoodsSkuInfo, IVersionOption } from '../../data.d';
import { getDetailData, getSkuRecycleList, submitSetting } from '../../service';
import useVersion from '../../composables/use-version';
import ChartsPanel from './charts-panel.vue';
import VersionBlock from './version-block.vue';
import ComputedRuleBlock from './computed-rule-block.vue';
import SkuDetailPanel from './sku-detail-panel.vue';

const drawerWrapperRef = ref<HTMLElement | null>(null);

const isEdit = ref<boolean>(true);
// 1=>C端；2=>B端
const settingType = ref<string>('1');
const goodsId = ref<string>('');
const goodsName = ref<string>('');
//算法类型
const schemeType = ref('');

const drawerTitle = computed(() => {
  return `${settingType.value === '1' ? 'C' : 'B'}端估价策略设置`;
});

// 记录当前窗口一些必要固定的参数数据，用于组件、提交时可以直接使用
const defaultParams: Ref<Omit<IOpenDrawerParams, 'goods_name'>> = computed(() => {
  return {
    goods_id: goodsId.value,
    setting_type: settingType.value,
  };
});

const { visible, loading: submitLoading, open, close } = useModal(
  () => {
    return;
  },
  {
    beforeOpen: (params: IOpenDrawerParams) => {
      const { setting_type, goods_id, goods_name } = params;
      settingType.value = setting_type;
      goodsId.value = goods_id;
      goodsName.value = goods_name;
      visible.value = true;
      getVersionOptions();
    },
    afterClose: () => {
      versionList.value = [];
    },
  },
);

const [chartsPanelVisible, { toggle: toggleChartsPanelVisible }] = useBoolean(true);

// 获取调价版本
const { versionIsLoad, versionList, isNowVersion, getVersionOptions } = useVersion(
  defaultParams,
  (res: { data: IVersionOption[] }) => {
    isEdit.value = !res.data.length;
    versionIsLoad.value = true;
    if (!res.data?.length) {
      getDetail();
      onChangeEdit();
    }
  },
);
function versionChange(versionId: string, isNowVer: boolean) {
  isNowVersion.value = isNowVer;
  getDetail(versionId);
}

function onChangeEdit() {
  isEdit.value = true;
  // 编辑时需要获取最新预估每日回收量信息;
  getSkuRecycle('');
}

// 计算规则详情
const detailLoading = ref<boolean>(false);
const detailRes = ref<Partial<ITableDetail>>({});
async function getDetail(versionId = '') {
  const requestParams = {
    ...defaultParams.value,
    data_version_id: versionId,
    auto_evaluate_price_type: schemeType.value,
  };
  detailLoading.value = true;
  // 同步获取预估每日回收量信息;
  getSkuRecycle(versionId);
  const res = await getDetailData(requestParams);
  detailLoading.value = false;
  detailRes.value = res.data;
}

// 预估日回收量
const skuInfoList = ref<IGoodsSkuInfo[]>([]);
async function getSkuRecycle(versionId: string) {
  const params = {
    ...defaultParams.value,
    data_version_id: versionId,
  };
  const res = await getSkuRecycleList(params);
  skuInfoList.value = res.data || [];
}

const computedRuleRef = ref<any>(null);
function onSubmit() {
  computedRuleRef.value?.validator().then(async (submitParams: ISubmitSettingPrice) => {
    submitLoading.value = true;
    const requestParams = {
      ...defaultParams.value,
      ...submitParams,
      schemeType: schemeType.value,
    };
    try {
      await submitSetting(cloneDeep(requestParams));
    } finally {
      submitLoading.value = false;
    }
    reload();
  });
}

// 重新加载整个抽屉内容数据
function reload() {
  const params = {
    ...defaultParams.value,
    goods_name: goodsName.value,
    schemeType: schemeType.value,
  };
  open(params);
}

function onCancel() {
  if (submitLoading.value) {
    return message.warning('正在提交中，请稍后再试');
  }
  if (isEdit.value && versionList.value.length) {
    // 编辑情况下，若本身存在版本信息，则原地刷新，否则直接关闭抽屉即可；
    reload();
  } else {
    close();
  }
}

provide('isEdit', isEdit);
provide('settingType', settingType);
provide('isNowVersion', isNowVersion);

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.drawer-wrapper {
  :deep(.ant-drawer-content) {
    overflow: inherit !important;
  }
}
.tactics-drawer-container {
  display: flex;
  align-items: flex-start;
  height: 100%;
  overflow: hidden;
  .container-right {
    flex: 1;
    max-height: calc(100vh - 56px);
    padding-bottom: 44px;
    overflow-y: scroll;
    border-left: 1px solid #e9e9e9;
  }
}
.side-plate {
  position: absolute;
  top: 55px;
  left: -40px;
  z-index: 99;
  display: flex;
  width: 40px;
  padding: 16px 12px;
  line-height: 1;
  letter-spacing: 5px;
  white-space: pre-line;
  background: #fff;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  writing-mode: vertical-rl;
  &.expand {
    color: #fff;
    background: var(--ant-primary-color);
  }
  & > span[role='img'] {
    transition: all 0.3s;
  }
}
</style>
