<template>
  <div class="factor-feature">
    <component
      :is="conditionRender.target"
      v-bind="conditionRender.props || {}"
      v-model:value="factorContent.condition"
      :disabled="disabled"
      @change="handleConditionChange"
    />
    <a-select
      v-if="factorContent.condition"
      class="factor-judge"
      :disabled="disabled"
      :options="judgeOptions"
      placeholder="请选择"
      :value="factorContent.judge"
      @change="handleJudgeChange"
    />
    <div class="factor-result">
      <component
        :is="resultCom.target"
        v-if="factorContent.condition && factorContent.judge"
        v-model:value="factorContent.result"
        :disabled="disabled"
        v-bind="{ ...resultProps, ...resultCom.props }"
        style="width: 100%"
      />
    </div>
    <a-spin
      v-if="loading"
      class="factor-loading"
      size="small"
      :spinning="loading"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, shallowRef, watch } from 'vue';

import { useVModel } from '@/utils/hooks';

import { IConditionRender, IFactorCondition, IFactorContentRule, TConditionMap } from '../data';

interface IFactorFeatureProps {
  disabled?: boolean;
  content?: IFactorContentRule;
  conditionRender?: IConditionRender;
  conditionMap?: TConditionMap;
  judgeMap?: Record<string, string>;
  resultProps?: Record<string, any>;
}

const props = withDefaults(defineProps<IFactorFeatureProps>(), {
  disabled: false,
  content: () => ({}),
  conditionRender: () => ({}),
  conditionMap: () => ({}),
  judgeMap: () => ({}),
  resultProps: () => ({}),
});

const emit = defineEmits<{
  (e: 'update:content', value: IFactorContentRule): void;
}>();

const factorContent = useVModel(props, 'content', emit);

const factorCondition = shallowRef<IFactorCondition>();

const loading = ref(false);

// 更具条件字段，获取对应的配置
watch(
  () => factorContent.value.condition,
  async (value, old) => {
    // 值变化时，清空判断和结果
    if (value !== old) {
      try {
        loading.value = true;
        const updatedRes = await props.conditionRender.updated?.(value, old);
        if (updatedRes !== false) {
          // 返回的内容不为false或catch时，更新条件内容
          factorCondition.value = updatedRes;
          factorContent.value.judge ??= judgeOptions.value?.[0]?.value; // 默认选择第一个判断条件
        }
      } finally {
        loading.value = false;
      }
    }
  },
  { deep: true, immediate: true },
);

/** 修改条件时，清除已选 */
function handleConditionChange() {
  factorContent.value.judge = undefined;
  factorContent.value.result = undefined;
}

const judgeOptions = computed(() => {
  const judges = factorCondition.value?.judges || props.conditionMap[factorCondition.value?.key]?.judges;
  return judges?.map(item => ({ label: props.judgeMap[item], value: item }));
});

const resultCom = computed(() => {
  const conditionTarget = props.conditionMap[factorCondition.value?.key];
  const targetCom = factorCondition.value?.[factorContent.value.judge] || conditionTarget?.[factorContent.value.judge];
  if (targetCom) {
    // 有对应的判断条件组件使用对应配置的组件
    return {
      target: targetCom.target,
      props: Object.assign({}, factorCondition.value?.props, targetCom?.props),
    };
  }
  // 没有就使用默认配置的组件
  const defaultCom = factorCondition.value?.default || conditionTarget?.default;
  return {
    target: defaultCom?.target,
    props: Object.assign({}, factorCondition.value?.props, defaultCom?.props),
  };
});

/** 修改判断条件 */
function handleJudgeChange(value: string) {
  const oldTarget = resultCom.value.target;
  factorContent.value.judge = value;
  if (oldTarget && oldTarget !== resultCom.value.target) {
    // 修改判断条件后，如果影响到结果组件的变化时，清除结果值
    factorContent.value.result = undefined;
  }
}
</script>

<style lang="less" scoped>
.factor-feature {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  min-width: 400px;
  padding-right: 10px;

  .factor-judge {
    width: 100px;
  }

  .factor-result {
    display: flex;
    flex: 1;
  }

  .factor-loading {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
  }
}
</style>
