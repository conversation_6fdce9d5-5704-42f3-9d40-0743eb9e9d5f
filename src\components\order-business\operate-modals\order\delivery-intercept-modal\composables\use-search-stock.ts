import { Ref, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

import { EStockStatus } from '@/components/order-business/operate-modals/order/exchange-apply-modal/composables/use-check-stock';
import {
  IAccountState,
  IAttrItem,
  IInitData,
  IWarehouseInformation,
} from '@/components/order-business/operate-modals/order/exchange-apply-modal/data';
import {
  getPurchaseInfo,
  getPurchaseInfoSuper,
  searchSkuId,
} from '@/components/order-business/operate-modals/order/exchange-apply-modal/services';
// 去发货的分仓查询接口
import { subwarehouseService } from '@/components/order-business/operate-modals/order/go-ship-modal/services';
import isSufficientWarehouse from '@/utils/is-sufficient-warehouse';

import { useAdviceParams } from '../../go-ship-modal/grayscale-deliver-modal/components/platform-shipment/composables/use-exchange-advice';
import { getRole } from '../config';
export default function ({
  formState,
  attrList,
  initData,
  getTableData,
  invoice,
}: {
  formState: Ref<any>;
  attrList: Ref<IAttrItem[]>;
  initData: Ref<IInitData>;
  invoice: Ref<number>;
  getTableData?: (exchangeParams: any, upgradeParams: any, is_upgrade: boolean) => void;
}) {
  const isSuper = getRole() === 'super';

  const isForbid = ref(false);

  const searchStockState = ref({
    disabled: true,
    lodaing: false,
    isQueryStock: false,
  });

  /** 账户信息 */
  const accountState = ref<IAccountState>({
    loading: false,
    amount: {},
  });

  const warehouseInformation = ref<{
    regulatoryDeviceInformation?: IWarehouseInformation;
    nonRegulatoryDeviceInformation?: IWarehouseInformation;
  }>({});

  // 监听属性值变化，只有在所有的属性值都有选择的时候才能移除查询库存按钮、提交按钮的置灰状态
  watch(
    [() => formState.value.modalId, () => attrList.value],
    () => {
      if (formState.value.modalId) {
        searchStockState.value.disabled = !attrList.value.every(item => item.value);
      } else {
        searchStockState.value.disabled = true;
      }
      searchStockState.value.isQueryStock = false;
    },
    { deep: true },
  );

  function handleSearchStock() {
    if (!formState.value.modalId) {
      message.warning('请先选择型号!');
      return;
    }

    const params = {
      model_id: formState.value.modalId,
      brand_id: Number(initData.value.pdm_brand_id),
      attribute: [{ attribute_list: attrList.value.map(item => item.value) }],
    };

    searchStockState.value.lodaing = true;
    // 1. 根据所选的sku信息查找出对应的skuId
    searchSkuId(params)
      .then(res => {
        const skuId = res.data?.sku_list?.[0].sku_id || undefined;
        formState.value.skuId = skuId;
        if (!skuId) {
          formState.value.stockStatus = EStockStatus.Undefined;
          message.warning('未找到当前商品的sku_id');
        } else {
          // 2.查询分仓服务
          selectOptimalWarehouseService(skuId);
        }
        searchStockState.value.isQueryStock = true;
      })
      .finally(() => {
        searchStockState.value.lodaing = false;
      });
  }

  /*
  查询分仓服务,择优选择
  1. 「租赁服务设备」、「非租赁服务设备」(本质就是监管机和非监管机)，哪个有货就有些命中对应的那个
  2. 如果双方都有货，那么优先选择「租赁服务设备」
  3. 没货的需要置灰不允许切换
*/
  async function selectOptimalWarehouseService(skuId: string) {
    searchStockState.value.lodaing = true;
    const params = handleWarehouseBaseParams(skuId);

    try {
      const [{ data: regulatoryDeviceInformation }, { data: nonRegulatoryDeviceInformation }] = await Promise.all([
        // 监管机
        subwarehouseService({ ...params, is_lock_machine: true }),
        // 非监管机
        subwarehouseService({ ...params, is_lock_machine: false }),
      ]);

      const { warehouse_type: regulatoryWarehouseType } = regulatoryDeviceInformation;
      const { warehouse_type: nonRegulatoryWarehouseType } = nonRegulatoryDeviceInformation;

      // 监管机库存状态
      let regulatoryInventoryStatus = isSufficientWarehouse({
        warehouse_type: regulatoryWarehouseType,
        regulation: true,
      });

      // 非监管机库存状态
      const nonRegulatoryInventoryStatus = isSufficientWarehouse({
        warehouse_type: nonRegulatoryWarehouseType,
        regulation: false,
      });

      /** 舆论问题临时处理 */
      const { pdm_brand_id, pdm_category_id } = initData.value;
      let is_new = true;
      const findItem = attrList.value.find(item => item.name === '成新度');
      if (findItem) {
        const { value, options } = findItem;
        const selectItem = options.find(item => item.value === value);
        if (selectItem && selectItem.label !== '全新') {
          is_new = false;
        }
      }
      isForbid.value =
        regulatoryDeviceInformation.is_stock_out &&
        !is_new &&
        Number(pdm_category_id) === 34 &&
        Number(pdm_brand_id) !== 26;
      if (isForbid.value) {
        regulatoryInventoryStatus = false;
      }
      /** 舆论问题临时处理 */

      warehouseInformation.value = {
        regulatoryDeviceInformation: {
          ...regulatoryDeviceInformation,
          inventoryStatus: regulatoryInventoryStatus,
        },
        nonRegulatoryDeviceInformation: {
          ...nonRegulatoryDeviceInformation,
          inventoryStatus: nonRegulatoryInventoryStatus,
        },
      };
      if (regulatoryInventoryStatus || nonRegulatoryInventoryStatus) {
        // 如果有货
        formState.value.stockStatus = EStockStatus.Enough; // 充足状态
        formState.value.regulation = regulatoryInventoryStatus;
      } else {
        // 如果都没货
        formState.value.stockStatus = EStockStatus.NotEnough; // 缺货状态
        formState.value.regulation = true;
        // 缺货就查一下换货建议
        const exchangeParams = useAdviceParams(params, true);
        const upgradeParams = useAdviceParams(params, true, {
          get_other_sku_type: 2,
          skip_server_config: initData.value?.storage_memory_up_auth ? undefined : 1,
        });
        getTableData && getTableData(exchangeParams, upgradeParams, initData.value?.storage_memory_up_model);
      }

      /** 舆论问题临时处理 */
      if (isForbid.value) {
        formState.value.regulation = false;
      }
      /** 舆论问题临时处理 */

      // 查询预估价格、余额等信息
      getPriceInfo();
    } finally {
      searchStockState.value.lodaing = false;
    }
  }

  /**
   * @Description 查询预估价格、余额等信息
   * @returns {any}
   */
  function getPriceInfo() {
    const ori_sku_id = initData.value.sku_id || formState.value.skuId;
    if (!ori_sku_id) return;
    accountState.value.loading = true;
    const { regulatoryDeviceInformation, nonRegulatoryDeviceInformation } = warehouseInformation.value;
    const {
      warehouse_id: lock_warehouse_id,
      warehouse_type: lock_warehouse_type,
      sku_id: lock_sku_id,
    } = regulatoryDeviceInformation;
    const {
      warehouse_id: unlock_warehouse_id,
      warehouse_type: unlock_warehouse_type,
      sku_id: unlock_sku_id,
    } = nonRegulatoryDeviceInformation;
    const need_condition_skuIds = [lock_sku_id, unlock_sku_id].filter(sku_id => sku_id != ori_sku_id).join(',');

    const { activate_only, isChecked } = formState.value;
    const params = {
      order_id: initData.value.orderId,
      is_activity_only: activate_only ? 1 : 0,
      is_checked: isChecked ? 1 : 0,
      lock_sku_id,
      lock_warehouse_id,
      lock_warehouse_type,
      unlock_sku_id,
      unlock_warehouse_id,
      unlock_warehouse_type,
      need_condition_skuIds,
    };

    const request = isSuper ? getPurchaseInfoSuper : getPurchaseInfo;
    request(params)
      .then(({ data }) => {
        const {
          evaluatePriceItem: { lock, unlock },
          needConditionSkuIdsRet,
          accountAmount,
        } = data;
        accountState.value.amount = accountAmount;

        Object.assign(warehouseInformation.value.regulatoryDeviceInformation, {
          ...lock,
          showPrice: !(lock instanceof Array) && Number(lock?.totalPrice) !== 0,
          newTagText: needConditionSkuIdsRet?.[lock_sku_id]?.text ? '库存充足' : '',
        });
        Object.assign(warehouseInformation.value.nonRegulatoryDeviceInformation, {
          ...unlock,
          showPrice: !(unlock instanceof Array) && Number(unlock?.totalPrice) !== 0,
          newTagText: needConditionSkuIdsRet?.[unlock_sku_id]?.text ? '库存充足' : '',
        });
      })
      .finally(() => {
        accountState.value.loading = false;
      });
  }

  function handleWarehouseBaseParams(sku_id: string) {
    const { activate_only, isChecked, server_id } = formState.value;
    const { item_num, orderId, expected_warehouse_id, device_special_tag, is_new_online_server } = initData.value;
    const params: Record<string, any> = {
      sku_id,
      server_id,
      item_num: Number(item_num),
      order_id: orderId,
      up_stock: true,
      is_activate_only: activate_only,
      is_checked: isChecked,
      created_port: isSuper ? 1 : 2,
      expected_warehouse_id: expected_warehouse_id || 0, // 优先分到初次下发时的仓库
      device_special_tag,
      is_new_online_server,
    };
    if (is_new_online_server) {
      params.invoice = invoice.value;
    }
    return params;
  }

  return {
    isForbid,
    searchStockState,
    accountState,
    warehouseInformation,

    handleSearchStock,
    handleWarehouseBaseParams,
    getPriceInfo,
  };
}
