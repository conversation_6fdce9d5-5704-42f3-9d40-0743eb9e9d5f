<template>
  <a-form
    ref="formRef"
    layout="inline"
    :model="formInfo"
  >
    <a-form-item
      label="数据来源"
      name="source_type"
    >
      <a-select
        v-model:value="formInfo.source_type"
        :options="optionsManage.source_type"
        placeholder="请选择"
        style="width: 184px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="选择时间"
      name="data_type"
    >
      <a-select
        v-model:value="formInfo.data_type"
        :options="optionsManage.data_type"
        placeholder="请选择"
        style="width: 98px"
        @change="onChange"
      />
      <a-form-item-rest>
        <a-range-picker
          v-if="formInfo.data_type === '1'"
          v-model:value="formInfo.timeType"
          class="time-picker"
          style="width: 366px"
          value-format="YYYY-MM-DD"
        />
        <a-date-picker
          v-else
          v-model:value="formInfo.timeType"
          class="time-picker"
          picker="year"
        />
      </a-form-item-rest>
    </a-form-item>
    <a-form-item>
      <a-button
        style="margin-right: 8px"
        type="primary"
        @click="emits('confirm', 'search')"
      >
        查询
      </a-button>
      <a-button
        style="margin-right: 8px"
        @click="onReset"
      >
        重置
      </a-button>
      <a-button @click="exportAction">
        <template #icon>
          <vertical-align-bottom-outlined />
        </template>
        导出
      </a-button>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, ProppType } from 'vue';
import { useRoute } from 'vue-router';
import { VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import useExportModalConfirm from '@/pages-stage/recycle/finance/self-order-pay/use-export-modal-confirm';
import { ISearchInfo } from '../data.d';
import { getTableList, saleExport } from '../service.ts';

const route = useRoute();

const props = defineProps({
  value: {
    type: Object as ProppType<ISearchInfo>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:value', 'confirm']);

const formRef = ref<HTMLElement | any>(null);

const formInfo = ref<Partial<ISearchInfo>>({});

const optionsManage = reactive({
  source_type: [],
  data_type: [],
});

const getOptionsData = async () => {
  const res = await getTableList();
  const data = res.data || {};
  const keysArr = Object.keys(data);
  keysArr.forEach(key => {
    if (optionsManage.hasOwnProperty(key)) {
      optionsManage[key] = data[key] || [];
    }
  });
};

const onChange = () => {
  formInfo.value.timeType = null;
};

const onEmit = () => {
  emits('update:value', formInfo.value);
};

const onReset = () => {
  formRef.value?.resetFields();
  formInfo.value.timeType = [];
  onEmit();
  emits('confirm', 'search');
};

const exportAction = () => {
  const requestParams = { ...formInfo.value };
  delete requestParams.timeType;
  if (formInfo.value.timeType || formInfo.value.timeType?.length) {
    // 按日统计，默认数据格式为数组； 按月统计，则数据是字符串，选择单个年份；
    if (requestParams.data_type === '1') {
      requestParams.start_at = formInfo.value.timeType[0];
      requestParams.end_at = formInfo.value.timeType[1];
    } else {
      requestParams.start_at = dayjs(formInfo.value.timeType).startOf('year').format('YYYY-MM-DD');
      requestParams.end_at = dayjs(formInfo.value.timeType).endOf('year').format('YYYY-MM-DD');
    }
  }
  const { down: downOrder } = useExportModalConfirm(
    async () => {
      const res = await saleExport(requestParams);
      return res;
    },
    `${route.query.origin}/super/async-export/index`,
    {
      title: '销售日报数据导出',
      content: '请确认是否导出该销售日报的数据',
    },
  );
  downOrder();
};

watch(
  () => props.value,
  newValue => {
    formInfo.value = newValue || {};
  },
  {
    immediate: true,
  },
);

getOptionsData();
</script>

<style lang="less" scoped>
.time-picker {
  width: 170px;
  border-left: none;
  border-radius: 0 4px 4px 0;
}
</style>
