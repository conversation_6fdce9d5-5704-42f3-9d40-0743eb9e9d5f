import type { RTableColumnType } from 'rrz-web-design';
import { RButtonType } from 'rrz-web-design';

export const addOrEditFormGroup: FormGroupItem[] = [
  {
    key: 'name',
    originProps: { label: '类目名字', name: 'name', rules: [{ required: true, message: '请填写类目名字' }] },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
];

/**
 * 表格columns
 */

export const columns: RTableColumnType[] = [
  { title: '类目ID', dataIndex: 'id' },
  { title: '店铺ID', dataIndex: 'shop_id' },
  { title: '类目名字', dataIndex: 'name' },
  {
    title: '操作',
    dataIndex: 'operation',
    btnGroup: [
      {
        name: '预览',
        key: 'handleView',
        type: RButtonType.Primary,
      },
      {
        name: '修改',
        key: 'handleEdit',
        type: RButtonType.Primary,
      },
      {
        name: '删除',
        key: 'handleDelete',
        type: RButtonType.Primary,
      },
    ],
  },
];
