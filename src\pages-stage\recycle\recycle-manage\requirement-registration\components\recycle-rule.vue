<template>
  <div style="margin-top: 24px">
    <recycle-rule-item title="回收注意事项">
      1. 寄出前请解除设备监管锁。<br>
      2. 检测不合格的设备，平台将根据您在提交订单时所选择的处理方式进行处理。<br>
      3. 当前支持回收平台代发的到期归还、提前归还以及商户存量的设备。（代发设备需订单状态为归还中或已完成）<br>
      4. 设备寄出前请先查看报价，设备入库检测合格后将默认回收，回收完成后无法退货。<br>
    </recycle-rule-item>
    <recycle-rule-item title="回收要求">
      1. 支持的回收型号及成色要求以今日报价为准，无报价机型暂不支持回收。<br>
      2. 以上报价均为二手苹果国行手机回收价，其他版本（如港版、澳版、台版及其他外版）暂不支持回收。<br>
      3. 具体检测标准跟成色标准请查看以下文档： <br>
      <div class="links">
        <a-button
          type="link"
          @click="handleDowload(2)"
        >
          <img
            src="https://img1.rrzuji.cn/uploads/scheme/2305/26/m/1ogVV91BO6ZgNb1UQNYl.png"
            style="margin-right: 7px"
          >
          苹果设备检测标准.xls
        </a-button>
      </div>
      <div class="links">
        <a-button
          type="link"
          @click="handleDowload(1)"
        >
          <img
            src="https://img1.rrzuji.cn/uploads/scheme/2305/26/m/1ogVV91BO6ZgNb1UQNYl.png"
            style="margin-right: 7px"
          >
          二手苹果成色标准.xls
        </a-button>
      </div>
    </recycle-rule-item>
    <recycle-rule-item title="检测须知">
      <span style="color: #ff4d4f">
        检测要求：<br>
        - 如发现机器有更换/维修其他零件，平台将会以不合格退回处理； <br>
        - 如发现机器有更换/维修其他零件，手机底部带有人人租防拆标免责，没有防拆标的平台将会以不合格退回处理。<br>
        【严禁私自伪造人人租防拆标，私自粘贴。如发现私自伪造、粘贴人人租防拆标，平台将保留起诉权益】
      </span>
    </recycle-rule-item>
    <recycle-rule-item title="回收时效">
      自平台签收之日起， 5个工作日内办结（不含节假日）。
    </recycle-rule-item>
  </div>
</template>

<script lang="ts" setup>
import { formatUrl } from '@/services/format-url';
import { getProxyTarget } from '@/utils/url';

import * as devHost from '../../../../../../devHostConfig';
import RecycleRuleItem from './recycle-rule-item.vue';
const handleDowload = async (type: number) => {
  const env = process.env.NODE_ENV;
  if (env === 'development') {
    const target = getProxyTarget({
      target: devHost.defaultTarget,
      envType: devHost.defaultEnv,
      hostType: 'PHP',
    });
    const url = target + `/demand-regis/example-review?type=${type}`;
    window.open(url);
  } else {
    const url = formatUrl({ url: `/demand-regis/example-review?type=${type}` });
    window.open(url);
  }
};
</script>

<style lang="less" scoped>
.links {
  display: flex;
  align-items: center;
  :deep(.ant-btn) {
    padding-left: 0;
  }
}
.rule-img {
  width: 984px;
  height: 1288px;
}
</style>
