<template>
  <div class="charts-item">
    <div class="charts-item__header">
      {{ label }}
      <a-select
        v-model:value="colorValue"
        :options="colorOptions"
        placeholder="请选择"
        style="width: 128px"
        @change="() => requestChartsData()"
      />
    </div>
    <a-range-picker
      v-model:value="dateValue"
      :allow-clear="false"
      class="range-picker-item"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      @change="() => requestChartsData()"
    />
    <a-spin
      :spinning="loading"
      tip="Loading..."
    >
      <div
        :id="ELEMNT_ID"
        class="charts-render"
      />
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, PropType } from 'vue';
import dayjs from 'dayjs';
import { IOptions, IOpenDrawerParams } from '../../data.d';
import useCharts from '../../composables/use-charts';

interface IBaseRequestParams extends Omit<IOpenDrawerParams, 'goods_name'> {
  memory: string;
}

const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  colorOptions: {
    type: Array as PropType<IOptions<string>[]>,
    default: () => [],
  },
  baseRequestParams: {
    type: Object as PropType<IBaseRequestParams>,
    default: () => ({}),
  },
});

const ELEMNT_ID = `r_${Math.ceil(Math.random() * 10e5).toString(36)}`;

const colorValue = ref<string>('');
// 默认选择过去7天
const dateValue = ref<[string, string]>([
  dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD'),
]);

const { createCharts, loading } = useCharts({ element: ELEMNT_ID });

// 图标数据请求并绘制
function requestChartsData() {
  const hasDateSelect = !!dateValue.value.length;
  const { goods_id, setting_type, memory } = props.baseRequestParams;
  createCharts({
    goods_id,
    setting_type,
    start_at: hasDateSelect ? dateValue.value[0] : '',
    end_at: hasDateSelect ? dateValue.value[1] : '',
    sku: `${colorValue.value}&${memory}`,
  });
}

watch(
  () => props.colorOptions,
  val => {
    if (!val || !val.length) return;
    // 默认选中第一个颜色
    const item = val[0];
    colorValue.value = item.value;
  },
  {
    immediate: true,
  },
);

onMounted(() => {
  requestChartsData();
});
</script>

<style lang="scss" scoped>
.charts-item {
  box-sizing: border-box;
  width: 374px;
  margin-bottom: 24px;
  padding: 12px 16px 16px 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
  }
  .range-picker-item {
    width: 100%;
    margin-bottom: 8px;
  }
  .charts-render {
    width: 100%;
    height: 210px;
  }
}
</style>
