import type { FormGroupItem } from 'rrz-web-design';

export type OrderOption = {
  label: string;
  value: number;
};

export const exportRentOrderStatus = [
  {
    label: '待租客确认',
    value: 2,
  },
  {
    label: '待收货',
    value: 3,
  },
  {
    label: '在租中',
    value: 4,
  },
  {
    label: '归还中',
    value: 5,
  },
  {
    label: '已归还',
    value: 6,
  },
  {
    label: '待风控',
    value: 7,
  },
  {
    label: '已退租',
    value: 20,
  },
  {
    label: '订单关闭',
    value: 21,
  },
  {
    label: '已删除',
    value: 30,
  },
];

export const exportRentOrderOptions: OrderOption[] = [
  {
    label: '全部订单',
    value: 1,
  },
  {
    label: '有效订单',
    value: 2,
  },
];
export const exportOrderStatus = [
  {
    label: '待发货',
    value: 2,
  },
  {
    label: '待收货',
    value: 3,
  },
  {
    label: '待归还',
    value: 4,
  },
  {
    label: '归还中',
    value: 5,
  },
  {
    label: '已归还',
    value: 6,
  },
  {
    label: '待风控',
    value: 7,
  },
  {
    label: '交易完成',
    value: 20,
  },
  {
    label: '订单关闭',
    value: 21,
  },
  {
    label: '已删除',
    value: 30,
  },
];

export const exportOrderOptions: OrderOption[] = [
  {
    label: '全部订单',
    value: 1,
  },
  {
    label: '有效订单',
    value: 2,
  },
  {
    label: '盲发订单',
    value: 3,
  },
  {
    label: '购买订单',
    value: 5,
  },
  {
    label: '仅预授权的在租订单',
    value: 4,
  },
  {
    label: '极优免审必发订单',
    value: 6,
  },
];

export const exportOrderTimeOptions = [
  {
    label: '创建订单日期',
    value: 1,
  },
  {
    label: '更新订单日期',
    value: 2,
  },
];

export const formGroup: FormGroupItem[] = [
  {
    key: 'exportName',
    label: '自定义文件名称',
    fragmentKey: 'renderInput',
  },
  {
    key: 'exportDateType',
    label: '日期类型',
    options: exportOrderTimeOptions,
    fragmentKey: 'renderRadio',
    elProps: {
      optionType: 'default',
    },
  },
  {
    key: 'exportTime',
    label: '创建/更新订单日期',
    originProps: { required: true, message: '请选择订单日期' },
    fragmentKey: 'renderRangePicker',
  },
  {
    key: 'exportType',
    label: '订单范围',
    options: exportOrderOptions,
    originProps: { required: true, message: '请选择订单范围' },
    fragmentKey: 'renderRadio',
    elProps: {
      optionType: 'default',
    },
  },
  {
    key: 'exportStatus',
    label: '订单状态',
    options: exportOrderStatus,
    fragmentKey: 'renderSelect',
    elProps: {
      mode: 'multiple',
    },
  },
];
