<template>
  <a-modal
    v-model:visible="bindVisible"
    class="agree-cancel-modal"
    title="取消订单"
    :width="1000"
    @ok="submit"
  >
    <a-spin
      :spinning="agreeCancel.loading"
      tip="提交中..."
    >
      <a-form
        ref="refForm"
        :model="agreeCancel.data"
      >
        <a-form-item>
          <!-- 选择退款流水 -->
          <SelectRefundStatement
            ref="selectRefundStatementRef"
            :order-id="orderId"
            :type="ERefundType.CancelOrder"
            :visible-dependency="bindVisible"
          />
        </a-form-item>
        <a-form-item label="退款类型">
          用户申请取消
        </a-form-item>
        <a-form-item label="取消原因">
          {{ agreeCancel.condition?.reasonLabel }}
        </a-form-item>
        <a-form-item
          v-if="agreeCancel.condition?.subReasonLabel"
          label="二级取消原因"
        >
          {{ agreeCancel.condition?.subReasonLabel }}
        </a-form-item>
        <a-form-item>
          <div class="reminder">
            确认已核实扣取客户的费用和需要退还的费用额度，无售后问题
          </div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

import useCancelInsurance from '@/components/order-business/composables/use-cancel-insurance';
import { ERefundType } from '@/components/order-business/feature-application/select-refund-statement/data.d';
import SelectRefundStatement from '@/components/order-business/feature-application/select-refund-statement/index.vue';
import { useVModel } from '@/hook';

import type { AgreeCancelType } from '../data';
import { largeOrderRefund, orderRefund } from '../services';

interface IExtraData {
  rental_has_pay?: number | string;
  rental_has_refund?: number | string;
  deposit_has_pay?: number | string;
  deposit_has_refund?: number | string;
  apply_cancel_reason?: string;
  sub_cancel_reason_text?: string;
  is_while_user?: boolean;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}>();
const bindVisible = useVModel(props, 'visible', emit);

const refForm = ref();
const selectRefundStatementRef = ref();

const agreeCancel = reactive<AgreeCancelType>({
  data: {},
  loading: false,
});

const cancelInsurance = useCancelInsurance();

watch(
  () => bindVisible.value,
  value => {
    if (!value) {
      return;
    }
    const { apply_cancel_reason, sub_cancel_reason_text } = props.extraData;
    const subReasonLabel =
      sub_cancel_reason_text?.[0] === '['
        ? JSON.parse(sub_cancel_reason_text)
            .map((item: any) => item.title)
            .join('&')
        : sub_cancel_reason_text;
    agreeCancel.condition = {
      reasonLabel: apply_cancel_reason,
      subReasonLabel,
    };
  },
  { immediate: true },
);

async function submit() {
  await refForm.value?.validate();
  agreeCancel.loading = true;
  const applyCancelReason = props.extraData.apply_cancel_reason;
  const depositHasPay = props.extraData.deposit_has_pay;
  const depositHasRefund = props.extraData.deposit_has_refund;
  const data: any = {
    order_id: props.orderId,
    reason_type: 1,
    reason:
      {
        '计划有变': 1,
        '多拍错拍/未使用优惠券/信息有误': 2,
        '预收租金/补押金': 3,
        '运费问题': 4,
        '审核时间过长': 5,
        '商家没货/服务态度问题': 6,
        '其他': 7,
        '多拍/错拍/信息有误': 8,
        '押金问题(没有免押/押金过高)': 9,
        '租金问题(租金太高/租金预收)': 10,
        '审核/发货时间过长': 11,
        '产品/服务质量问题': 12,
        '我不想要了/计划有变': 13,
      }[applyCancelReason] || 7,
    ...agreeCancel.data,
  };
  if (depositHasPay > depositHasRefund) {
    data.refund_deposit = Math.round((depositHasPay - depositHasRefund) * 100) / 100;
  }
  const refundparams = (await selectRefundStatementRef.value?.requestParams(false)) || { refund_rental: 0 };
  if (props.extraData?.is_while_user) {
    largeOrderRefund({
      ...data,
      ...refundparams,
    })
      .then(() => {
        bindVisible.value = false;
        message.success('取消成功');
        emit('success');
      })
      .finally(() => {
        agreeCancel.loading = false;
      });
    return;
  }
  orderRefund({
    ...data,
    ...refundparams,
  })
    .then(res => {
      bindVisible.value = false;
      message.success('取消成功');
      emit('success');
      if (res.data?.is_supporting_policy_refund) {
        cancelInsurance.confirmCancel({
          orderId: props.orderId,
          successCallback: () => emit('success'),
        });
      }
    })
    .finally(() => {
      agreeCancel.loading = false;
    });
}
</script>

<style scoped lang="less">
.prompt {
  color: rgba(6, 21, 51, 0.65);
  font-size: 12px;
}

.reminder {
  color: #ff4d4f;
}

:deep(.ant-form-item) {
  margin-bottom: 0;
}
</style>
