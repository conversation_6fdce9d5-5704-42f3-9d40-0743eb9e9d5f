import { ref } from 'vue';
import { EApi, EExportApi, IListItem } from './data.d';

export const getExportConfig = () => {
  const selectList = ref<IListItem[]>([
    {
      label: '业绩&回款',
      value: 1,
      options: [],
      api: EApi.increaseAndOutstanding,
      exportApi: EExportApi.exportIncreaseAndOutstanding,
    },
    {
      label: '退租',
      value: 2,
      options: [],
      api: EApi.returnOf,
      exportApi: EExportApi.exportReturnOf,
    },
    {
      label: '逾期',
      value: 3,
      options: [],
      api: EApi.overdue,
      exportApi: EExportApi.exportOverdue,
    },
  ]);

  const rules = {
    type: [{ required: true, message: '请选择数据分类' }],
    month_range: [{ required: true, message: '请选择时间区间' }],
    month: [{ required: true, message: '请选择时间' }],
  };
  return {
    selectList,
    rules,
  };
};
