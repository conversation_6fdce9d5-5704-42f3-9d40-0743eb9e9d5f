import { createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';

import { TAnnouncementReadResponse } from './data';
import { getAnnouncementRead, getNotarizationScene } from './services';

async function goMessageNotification(announcementInfo: TAnnouncementReadResponse) {
  window.parent.postMessage(
    {
      action: 'blank',
      href: `/message-v3/index?open=/merchant/chat&id=${announcementInfo.id}&message_id=${announcementInfo.message_id}&msg_type=${announcementInfo.msg_type}`,
    },
    '*',
  );
}

//跳转进入【合同公证申请入驻】页面
function goApplyNotarize() {
  window.parent.postMessage({
    action: 'blank',
    href: '/application-center/page?id=6',
  });
}

export async function confirmNotarizeNotice() {
  const { data = {} as TAnnouncementReadResponse } = await getAnnouncementRead();
  if (!data.is_read) {
    message.warning({
      content: () => (
        <div style={{ width: 'fit-content' }}>
          <InfoCircleOutlined />
          公证告知书尚未确认，需确认后才能使用公证。
          <a onClick={() => goMessageNotification(data)}>查看</a>
        </div>
      ),
      icon: <div></div>,
    });
  }
  return data.is_read;
}

export async function confirmWhitePingShan() {
  const { data } = await getNotarizationScene();
  const { auth_status, status } = data?.info || {};
  let isAuth = true;
  if (Number(auth_status) === 0 && Number(status) === 1) {
    isAuth = false;
    Modal.warning({
      title: '提示',
      icon: createVNode(InfoCircleOutlined),
      content: '平台公证进行了全面升级，请前往公证开通入口重新进行开通后使用',
      okText: '前往开通',
      zIndex: 1002,
      closable: true,
      onOk: () => goApplyNotarize(),
    });
  }
  return isAuth;
}

// 发货提示
export function signJudgment() {
  return new Promise(resolve => {
    Modal.warning({
      content: '该订单公证未签署，请确认是否继续发货',
      okText: '确认',
      zIndex: 1002,
      closable: true,
      onOk: () => {
        resolve(true);
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

// 发货提示缓存，有缓存不再出现提示（临时，后续改为数据库存储）
const key = 'sign_judgment_cache_';
export function setSignJudgmentCache(order_id: string) {
  localStorage.setItem(key + order_id, 'true');
}
export function getSignJudgmentCache(order_id: string) {
  return localStorage.getItem(key + order_id);
}
