export interface IRefundListParams {
  /** 订单id */
  order_id: string;
  /** 退款类型 */
  type: ERefundType;
}

export interface IRefundItem {
  /** 索引（用于排序） */
  _index?: number;
  /** 实际剩余可退金额 */
  actuality_surplus_has_refund: string;
  /** 账单id */
  bill_id: number;
  /** 账单说明 */
  bill_id_text: string;
  /** 在途退款金额 */
  conducted_has_refund: string;
  /** 支付时间 */
  created_at: string;
  /** 优惠金额 */
  discount_money: string;
  /** 已退款金额 */
  has_refund: string;
  /** 流水id */
  id: string;
  /** 订单金额 */
  money: string;
  /** 流水类型 */
  money_type: number;
  /** 流水类型文案 */
  money_type_text: string;
  /** 支付金额 */
  receipt_amount: string;
  /** 在途退款优惠金额 */
  record_conducted_refund_discount: string;
  /** 已退款优惠金额 */
  record_refund_discount: string;
  /** 剩余可退款金额 */
  surplus_has_refund: string;
  /** 剩余可退款金额优惠 */
  surplus_has_refund_discount: string;
  /** 流水的最低退款值 */
  min_refund_money?: string;
  /** 总流水的最低退款值 */
  total_min_refund_money?: string;
  /** 支付流水号 */
  trade_no: string;
}

export interface IRefundData {
  /** 退款流水ID */
  account_pay_log_id: string;
  /** 申请退款优惠金额 */
  refund_discount_money: number;
  /** 申请退款金额（不含优惠金额） */
  refund_money: number;
}

export interface IExternalData {
  /** 流水列表 */
  account_pay_log_refund_list: IRefundItem[];
  /** 申请退款租金 */
  refund_money: number;
  /** 退款租金含优惠金额 */
  refund_discount_money: number;
  /** 用户实际到账 */
  actual_refund_money: number;
}

export enum ERefundType {
  /** 申请退款 */
  ApplyForRefund = 1,
  /** 订单退款 */
  OrderRefund = 2,
  /** 取消订单 */
  CancelOrder = 3,
  /** 工单申请 */
  WorkOrderApplication = 4,
}
