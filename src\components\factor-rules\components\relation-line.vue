<template>
  <div
    v-if="show"
    class="line"
  >
    <div class="capsule operator">
      <div
        v-for="(item, key) in relationMap"
        :key="key"
        v-bind="$attrs"
        :class="{ active: key === value }"
        style="transition: all 0.2s ease-in-out"
        @click="modelValue = key"
      >
        {{ item }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, PropType } from 'vue';
const props = defineProps({
  value: {
    type: [String, Number] as PropType<string | number>,
    default: 'and',
    required: true,
  },
  show: {
    type: Boolean,
    default: true,
  },
  trueValue: {
    type: [String, Number] as PropType<string | number>,
    default: 'and',
  },
  falseValue: {
    type: [String, Number] as PropType<string | number>,
    default: 'or',
  },
  norValue: {
    type: [String, Number] as PropType<string | number>,
    default: 'nor',
  },
});
const relationMap = {
  [props.trueValue]: 'and',
  // [props.falseValue]: 'or',
  // [props.norValue]: 'nor',
};
const emit = defineEmits(['update:value']);
const modelValue = computed({
  get: () => props.value,
  set: val => emit('update:value', val),
});
</script>
<style lang="less" scoped>
@theme-color: #00c8be;
.line {
  position: relative;
  width: 28px;
  height: inherit;
  margin: 0 8px;
  &[disabled='true'] {
    .operator {
      cursor: not-allowed;
    }
  }
  .operator {
    position: absolute;
    top: 50%;
    left: 50%;
    display: block;
    width: 28px;
    height: 28px;
    overflow: hidden;
    color: @theme-color;
    font-weight: 500;
    line-height: 28px;
    text-align: center;
    background-color: #e5f9f8;
    border-radius: 4px;
    transform: translate(-50%, -50%);
    cursor: pointer;
    *[disabled='true'] {
      pointer-events: none;
    }
  }
  &::before {
    position: absolute;
    left: 50%;
    display: block;
    width: 2px;
    height: 100%;
    margin: 0 auto;
    background-color: @theme-color;
    opacity: 0.2;
    content: '';
  }
  .capsule {
    // height: 84px; // and or nor高度
    height: 28px;
    border-radius: 4px;
    .active {
      color: #fff;
      background: @theme-color;
    }
  }
}
</style>
