import { FormItemProps } from 'ant-design-vue';
import { CSSProperties, Component, Ref } from 'vue';
import * as formFragment from './form-fragment';
import { IAxiosResponse } from '@/services/api';
import { HostType } from '@/utils/url';

type TObject = Record<string, any>;

export type Option = {
  label: string;
  value: string | number;
  disabled?: boolean;
};

export type fragmentKey = keyof typeof formFragment;

export interface FormGroupItem<T = any> {
  key: string;
  fragmentKey: fragmentKey;
  originProps?: FormItemProps;
  options?: TObject[] | Readonly<Ref<TObject[] | undefined>>;
  optionsFormat?: (res: IAxiosResponse<TObject>, value: string[]) => Option[];
  url?: string;
  style?: CSSProperties;
  component?: string | Component;
  elProps?: T;
  changeHandler?: (params: any) => void;
  params?: Record<string, unknown>;
  hostType?: HostType;
  colspan?: Record<string, number>;
  // 每次点击都请求url
  everyTimeLoad?: boolean;
  // 组合查询时，下拉框的配置项
  selectConfig?: FormGroupItem;
}

export interface SearchHeaderExtra {
  searchKey?: string;
  searchConfig?: FormGroupItem[];
  immediate?: boolean;
}
