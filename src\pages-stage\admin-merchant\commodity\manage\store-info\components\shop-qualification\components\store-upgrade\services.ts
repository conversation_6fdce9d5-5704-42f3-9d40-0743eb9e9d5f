import { GET, POST } from '@/services/api';

/** 入驻前数据 */
export function getBeforeEnterData() {
  return GET('/code-merchant-enter/before-enter-data');
}

/** 入驻前检测 */
export function postCheck(params: { store_type?: number }) {
  return POST('/code-merchant-enter/check', params);
}

/** 判断进驻情况 */
export function getEnterStatus() {
  return GET('/code-merchant-enter/is-enter');
}

/** 获取面审资料 */
export function getFaceReviewData() {
  return GET('/code-merchant-enter/face-review-data');
}

/** 提交入驻数据 */
export function create(params: any) {
  return POST('/code-merchant-enter/create', params);
}

/** 修改入驻信息 */
export function update(params: any) {
  return POST('/code-merchant-enter/update', params);
}

/** 查看入驻信息 */
export function getEnterInfo() {
  return GET('/code-merchant-enter/view');
}

/** 记录点击【去升级】按钮 */
export function recordUpgrade() {
  return POST('/code-merchant-enter/store-enter');
}
