<template>
  <a-modal
    v-model:visible="modalVisible"
    destroy-on-close
    style="max-width: none"
    title="平台交易记录"
    width="916px"
    :z-index="1001"
    @cancel="onCancel"
  >
    <div class="platform_log_wrap">
      <div class="table_th">
        <div class="temp item" />
        <div class="order_id item">
          订单
        </div>
        <div class="created_at item">
          申请时间
        </div>
        <div class="category item">
          类目
        </div>
        <div class="item_num item">
          台数
        </div>
        <div class="deposit_money item">
          <span>商品押金</span>
          <a-tooltip>
            <template #title>
              区间2千一档，大于1万，显示10000+
            </template>
            <question-circle-outlined />
          </a-tooltip>
        </div>
        <div class="rental_has_pay item">
          <span>已付租金</span>
          <a-tooltip>
            <template #title>
              区间范围：0-100、100-1000、1000-2000、2000-3000、3000-4000、4000-5000，5000+，高于5千显示5000+
            </template>
            <question-circle-outlined />
          </a-tooltip>
        </div>
        <div class="has_pay_tenancy item">
          已付期数/总期数
        </div>
        <div class="overdue_day item">
          <span> 当前逾期天数 </span>
          <a-tooltip>
            <template #title>
              0表示未有过逾期，1-3、4-7、8-30、31-90、90+，高于90天显示90+
            </template>
            <question-circle-outlined />
          </a-tooltip>
        </div>
      </div>
      <div
        v-if="data?.value?.rent?.length"
        class="table_tr"
      >
        <div class="temp item">
          进行中
        </div>
        <div>
          <div
            v-for="(item, index) in data.value.rent"
            :key="index"
            class="flex-wrap"
          >
            <div class="order_id item">
              {{ item.order_id }}
            </div>
            <div class="created_at item">
              {{ item.created_at }}
            </div>
            <div class="category item">
              {{ item.category }}
            </div>
            <div class="item_num item">
              {{ item.item_num }}
            </div>
            <div class="deposit_money item">
              {{ item.deposit_money }}
            </div>
            <div class="rental_has_pay item">
              {{ item.rental_has_pay }}
            </div>
            <div class="has_pay_tenancy item">
              {{ item.has_pay_tenancy }}
            </div>
            <div class="overdue_day item">
              {{ item.overdue_day }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="data?.value?.complete?.length"
        class="table_tr"
      >
        <div class="temp item">
          已完成
        </div>
        <div>
          <div
            v-for="(item, index) in data.value.complete"
            :key="index"
            class="flex-wrap"
          >
            <div class="order_id item">
              {{ item.order_id }}
            </div>
            <div class="created_at item">
              {{ item.created_at }}
            </div>
            <div class="category item">
              {{ item.category }}
            </div>
            <div class="item_num item">
              {{ item.item_num }}
            </div>
            <div class="deposit_money item">
              {{ item.deposit_money }}
            </div>
            <div class="rental_has_pay item">
              {{ item.rental_has_pay }}
            </div>
            <div class="has_pay_tenancy item">
              {{ item.has_pay_tenancy }}
            </div>
            <div class="overdue_day item">
              {{ item.overdue_day }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="!data?.value?.complete?.length && !data?.value?.rent?.length"
        class="flex-wrap"
      >
        <div class="temp item">
          -
        </div>
        <div class="order_id item">
          -
        </div>
        <div class="created_at item">
          -
        </div>
        <div class="category item">
          -
        </div>
        <div class="item_num item">
          -
        </div>
        <div class="deposit_money item">
          -
        </div>
        <div class="rental_has_pay item">
          -
        </div>
        <div class="has_pay_tenancy item">
          -
        </div>
        <div class="overdue_day item">
          -
        </div>
      </div>
    </div>

    <template #footer>
      <a-button
        style="background-color: rgba(55, 119, 255, 1); border-color: rgba(55, 119, 255, 1)"
        type="primary"
        @click="onCancel"
      >
        知道了
      </a-button>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

interface IProps {
  visible: boolean;
  data: {
    value: {
      complete: any[];
      rent: any[];
    };
  };
}

const props = withDefaults(defineProps<IProps>(), {
  data: () => {
    return {
      value: {
        complete: [],
        rent: [],
      },
    };
  },
});
const emit = defineEmits(['update:visible']);
const modalVisible = ref(false);
watch(
  () => props.visible,
  val => {
    modalVisible.value = val;
  },
);
const onCancel = () => {
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.platform_log_wrap {
  width: fit-content;
  color: rgba(6, 21, 51, 0.85);
  font-size: 14px;
  border-top: 1px solid rgba(240, 241, 243, 1);
}

.item {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  min-height: 54px;
  padding: 5px 16px;
  border: 1px solid rgba(240, 241, 243, 1);
  border-top: none;

  :deep(.anticon-question-circle) {
    margin-left: 4px;
  }
}

.table_th {
  display: flex;
  width: fit-content;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 600;
  background-color: rgba(249, 249, 251, 1);
}

.table_tr {
  display: flex;
}

.temp {
  justify-content: center;
  width: 120px;
  background-color: rgba(249, 249, 251, 1);
}

.order_id {
  width: 88px;
}

.created_at {
  width: 92px;
}

.category {
  width: 88px;
}

.item_num {
  width: 63px;
}

.deposit_money {
  width: 108px;
}

.rental_has_pay {
  width: 108px;
}

.has_pay_tenancy {
  width: 96px;
}

.overdue_day {
  width: 108px;
}
</style>
