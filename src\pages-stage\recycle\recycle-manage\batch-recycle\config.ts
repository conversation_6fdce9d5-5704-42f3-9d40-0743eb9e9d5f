const columns = [
  {
    title: '商品信息',
    dataIndex: 'goods_image',
    key: 'goods_image',
    width: 236,
    className: 'vertical-middle',
  },
  {
    title: '物流信息',
    dataIndex: 'logistics_info',
    key: 'logistics_info',
    width: 204,
    className: 'vertical-middle',
  },
  {
    title: '估价信息',
    dataIndex: 'evaluate_info',
    key: 'evaluate_info',
    width: 177,
    className: 'vertical-middle',
  },
  {
    title: '定价信息',
    dataIndex: 'purchased_info',
    key: 'purchased_info',
    width: 177,
    className: 'vertical-middle',
  },
  {
    title: '检测结果',
    dataIndex: 'quality_result_text',
    key: 'quality_result_text',
    width: 102,
    className: 'vertical-middle',
  },
  {
    title: '订单状态',
    dataIndex: 'order_status_text',
    key: 'order_status_text',
    width: 160,
    className: 'vertical-middle',
  },
  {
    title: '操作',
    dataIndex: 'handle',
    width: 88,
    fixed: 'right',
    className: 'vertical-middle',
    align: 'center',
  },
];
const orderStatusOptions = [
  {
    label: '待收件',
    value: 1,
  },
  {
    label: '待入库',
    value: 2,
  },
  {
    label: '待检测',
    value: 3,
  },
  {
    label: '检测中',
    value: 4,
  },
  {
    label: '待确认',
    value: 5,
  },
  {
    label: '待打款',
    value: 6,
  },
  {
    label: '待退货',
    value: 7,
  },
  {
    label: '交易完成',
    value: 10,
  },
  {
    label: '打款失败',
    value: 11,
  },
  // {
  //   label: '已取消',
  //   value: 12,
  // },
  {
    label: '订单关闭',
    value: 13,
  },
];

export { columns, orderStatusOptions };
