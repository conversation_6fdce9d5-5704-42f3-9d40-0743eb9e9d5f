<template>
  <div class="out-of-line">
    <slot />

    <template
      v-for="(penaltyItem, index) in warnData.warnList"
      :key="index"
    >
      <a-tag
        v-if="typeof penaltyItem.penalty === 'string'"
        class="red-tag"
      >
        <span>{{ penaltyItem.penalty }}</span>
      </a-tag>
    </template>

    <a-tag
      v-if="warnData.limitList?.length"
      class="red-tag"
    >
      <InfoCircleOutlined style="margin-right: 8px" />{{ btnText }}内容{{ warnData.limitList.length }}条
      <a-tooltip
        :get-popup-container="getPopupContainer"
        overlay-class-name="shop-diagnosis-detail-tooltip"
        placement="top"
      >
        <template #title>
          <div
            v-for="(penaltyItem, index) in warnData.limitList"
            :key="index"
          >
            {{ filterText(dataObj['penalty_content_type'][penaltyItem.penalty_type], penaltyItem.penalty as IPenaltyItem) }}
          </div>
        </template>
        <span class="detail">详情</span>
      </a-tooltip>
    </a-tag>
  </div>
</template>

<script lang="ts" setup>
import { PropType, reactive, watch } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';

import { IAffectItem, IDataObj, IPenaltyContentsItem, IPenaltyItem, IValueItem } from '../data';
import { filterCateText } from '../utils';

const props = defineProps({
  item: {
    type: Object as PropType<IValueItem>,
    required: true,
  },
  btnText: {
    type: String,
    default: '限流',
  },
  affectValueItem: {
    type: Array as PropType<IAffectItem[]>,
    default: () => ({
      penalty_contents: [],
    }),
  },
  dataObj: {
    type: Object as PropType<IDataObj>,
    required: true,
  },
});

function filterText(penaltyType: string, penalty: IPenaltyItem) {
  if (typeof penalty === 'string') return penalty;
  return filterCateText(penaltyType, penalty);
}

function getPopupContainer(trigger: HTMLElement) {
  return trigger.parentElement;
}

const warnData = reactive<Record<'limitList' | 'warnList', IPenaltyContentsItem[]>>({
  limitList: [],
  warnList: [],
});

watch(
  () => props.affectValueItem,
  value => {
    const limitList: IPenaltyContentsItem[] = [];
    const warnList: IPenaltyContentsItem[] = [];
    value.forEach(i => {
      const obj: Record<string, any> = {};
      i.penalty_contents?.forEach((penaltyItem, penaltyIndex) => {
        const penalty = penaltyItem.penalty;
        penaltyItem.penalty_type = i.penalty_type;
        if (typeof penalty === 'string') {
          warnList.push(penaltyItem);
        } else {
          let flag = Object.keys(obj).some(objKey => {
            const item = obj[objKey];
            const itemPenalty = item.penalty;
            // 需要合并相同的
            if (
              item.source === penaltyItem.source &&
              itemPenalty.days === penalty.days &&
              itemPenalty.end_date === penalty.end_date &&
              itemPenalty.instance === penalty.instance &&
              itemPenalty.start_date === penalty.start_date
            ) {
              if (!itemPenalty.new_old_type.includes(penalty.new_old_type)) {
                itemPenalty.new_old_type = itemPenalty.new_old_type + '、' + penalty.new_old_type;
              }
              return true;
            } else {
              return false;
            }
          });
          obj[penaltyIndex] = penaltyItem;
          if (!flag) {
            limitList.push(penaltyItem);
          }
        }
      });
    });

    warnData.warnList = warnList;
    warnData.limitList = limitList;
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

.font-black-65 {
  color: @font-black-65;
}

.out-of-line {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .red-tag {
    color: #f5222d;
    background: #fff1f0;
    border-color: transparent;

    .detail {
      margin-left: 6px;
      color: #3777ff;
      cursor: pointer;
    }
  }

  .tag {
    margin-right: 12px;
  }

  :deep(.shop-diagnosis-detail-tooltip) {
    max-width: unset;

    .ant-tooltip-inner {
      max-height: 250px;
      overflow: auto;
      background: rgba(6, 21, 51, 0.75);
    }

    .ant-tooltip-arrow-content {
      --antd-arrow-background-color: rgba(6, 21, 51, 0.05);
      background: rgba(6, 21, 51, 0.75);
    }
  }
}
</style>
