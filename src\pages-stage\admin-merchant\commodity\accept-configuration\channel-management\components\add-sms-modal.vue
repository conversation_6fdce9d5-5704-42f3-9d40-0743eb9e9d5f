<template>
  <a-modal
    :destroy-on-close="true"
    :title="channelId === 20 ? '添加短信账号' : '添加通知号码'"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-form
      ref="addFormRef"
      :hide-required-mark="true"
      :label-col="{ span: 6 }"
      :model="addFormState"
      :rules="addRules"
      style="width: 100%"
    >
      <a-form-item
        label="用户昵称"
        name="name"
      >
        <a-input
          v-model:value="addFormState.name"
          placeholder="请输入"
          style="width: 230px"
        />
      </a-form-item>
      <a-form-item
        label="手机号码"
        name="contact"
      >
        <a-input-number
          v-model:value="addFormState.contact"
          :controls="false"
          :min="0"
          placeholder="请输入"
          :precision="0"
          style="width: 230px"
        />
      </a-form-item>
      <a-form-item
        label="验证码"
        name="code"
      >
        <a-input
          v-model:value="addFormState.code"
          placeholder="请输入"
          style="width: 138px; border-radius: 4px 0 0 4px"
        />
        <a-button
          class="btnCode"
          :disabled="getCodeBtnDisable"
          style="width: 92px; padding: 4px 12px; border-radius: 0 4px 4px 0"
          @click="getCode"
        >
          <span v-show="!getCodeBtnDisable">获取验证码</span>
          <span
            v-show="getCodeBtnDisable"
            class="count"
          >{{ count }} s</span>
        </a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';

import { addFormState } from '../config';
import { channelContactAdd, messageTepSend } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  channelId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'getClose']);
const onCancel = () => {
  emits('update:visible', false);
  emits('getClose');
};

const getCodeBtnDisable = ref<boolean>(false);
const phoneRegExp = /^1[3456789]\d{9}$/;
const timer = ref<any>();
const count = ref<number>(0);
const getCode = async () => {
  if (!phoneRegExp.test(addFormState.value.contact)) {
    return message.error('【手机号码】请填写有效格式');
  }
  const res = await messageTepSend({ phone: addFormState.value.contact });
  message.success(res.message || '操作成功');
  const TIME_COUNT = 60;
  if (!timer.value) {
    count.value = TIME_COUNT;
    getCodeBtnDisable.value = true;
    timer.value = setInterval(() => {
      if (count.value > 0 && count.value <= TIME_COUNT) {
        count.value--;
      } else {
        getCodeBtnDisable.value = false;
        clearInterval(timer.value); // 清除定时器
        timer.value = null;
      }
    }, 1000);
  }
};

//检查手机号
const checkReturnPhone = (_: any, return_phone: string) => {
  const phoneRegExp = /^1[3456789]\d{9}$/;
  if (!phoneRegExp.test(return_phone)) {
    return Promise.reject(new Error('请输入正确的手机格式!'));
  } else {
    return Promise.resolve();
  }
};

//校验
const addRules = {
  name: [{ required: true, message: '请输入用户昵称' }],
  contact: [{ required: true, message: '请输入正确号码', validator: checkReturnPhone }],
  code: [{ required: true, message: '请输入验证码' }],
};

//确定
const addFormRef = ref<FormInstance>();
const handleOk = () => {
  // 1.校验
  addFormRef.value.validateFields().then(async () => {
    const res = await channelContactAdd({ channel_id: props.channelId, ...addFormState.value });
    message.success(res?.message || '操作成功');
    addFormRef.value.resetFields();
    onCancel();
  });
};
</script>
