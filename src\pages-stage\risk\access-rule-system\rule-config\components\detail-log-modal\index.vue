<template>
  <a-drawer
    :destroy-on-close="true"
    get-container=".layout-admin-page"
    title="详细设置日志"
    :visible="props.visible"
    :width="850"
    @close="emits('update:visible', false)"
  >
    <search-table
      ref="searchTableRef"
      class="detail-log"
      :search-api="apiGetRuleDetailLog"
      :search-config="searchFormConfig"
      :tb-col="tbCol"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'preview'">
          <span
            class="preview-btn"
            @click="goDetailPreview(record.line_id)"
          >预览</span>
        </template>
      </template>
    </search-table>
  </a-drawer>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';

import { apiGetRuleDetailLog } from '../../services';
import { ISearchConfigItem } from '@/components/search-table/src/data';
const router = useRouter();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  ruleId: {
    type: String,
    default: '',
  },
});
const emits = defineEmits<{
  (event: 'update:visible', value: boolean): void;
}>();
const searchTableRef = ref();
const tbCol = [
  {
    title: '修改时间',
    dataIndex: 'created_at',
    key: 'created_at',
  },
  {
    title: '修改人',
    dataIndex: 'created_by',
    key: 'created_by',
  },
  {
    title: '预览',
    key: 'preview',
  },
];
const searchFormConfig = ref<ISearchConfigItem[]>([
  {
    label: '规则id',
    key: 'rule_id',
    renderType: 'input',
    default: '',
  },
  {
    label: '时间',
    key: 'created_at',
    renderType: 'datePicker',
    default: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  },
]);
function goDetailPreview(id: string) {
  // emits('update:visible', false);
  router.push({
    name: 'riskAccessRuleSystemRuleDetailSetting',
    params: {
      id,
    },
    query: {
      isHistory: '1',
    },
  });
}
watch(
  () => props.visible,
  (val: boolean) => {
    searchFormConfig.value[0].default = props.ruleId;
    val && searchTableRef.value?.searchFormData();
  },
);
</script>

<style lang="less">
.detail-log .ant-form {
  grid-template-columns: repeat(3, 1fr);
  .ant-picker {
    width: 200px;
  }
}
.preview-btn {
  color: #1890ff;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
</style>
