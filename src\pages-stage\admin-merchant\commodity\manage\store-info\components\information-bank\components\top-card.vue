<template>
  <a-card
    :bordered="true"
    class="table-card"
  >
    <div class="flex-justify">
      <div class="text">
        店铺运营数据
      </div>
      <a-range-picker
        v-model:value="timeValue"
        @change="handleChangeTime"
      />
    </div>
    <div
      ref="parentContent"
      class="chart-box"
    >
      <div id="renderMon" />
    </div>
  </a-card>
  <a-card
    :bordered="true"
    class="card-list"
    style="margin: 24px 0"
  >
    <div
      v-if="Object.keys(rateInfo).length > 0"
      class="flex-justify items"
    >
      <div
        v-for="item in rateOption"
        :key="item.title"
        class="item"
      >
        <div class="line" />
        <div class="data-set">
          <div class="title">
            {{ item.title }}
          </div>
          <div class="num">
            {{
              item.title.indexOf('率') > -1
                ? formatRate(rateInfo[item.key][item.countKey])
                : rateInfo[item.key][item.countKey]
            }}
          </div>
          <a-space :size="8">
            <span class="per-title">日环比</span>
            <span :class="[rateInfo[item.key][item.one] > 0 ? 'up' : 'down']">
              <CaretUpOutlined v-if="rateInfo[item.key][item.one] > 0" />
              <CaretDownOutlined v-else />
              {{ formatRate(rateInfo[item.key][item.one]) }}
            </span>
            <span
              class="per-title"
              style="margin-left: 8px"
            >周环比</span>
            <span :class="[rateInfo[item.key][item.week] > 0 ? 'up' : 'down']">
              <CaretUpOutlined v-if="rateInfo[item.key][item.week] > 0" />
              <CaretDownOutlined v-else />
              {{ formatRate(rateInfo[item.key][item.week]) }}
            </span>
          </a-space>
        </div>
      </div>
    </div>
  </a-card>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue';

import { loadG2 } from '@/utils/static-load';
const props = defineProps<{
  salesInfo?: any;
  rateInfo?: any;
}>();
const emit = defineEmits(['on-change-time']);
const timeValue = ref([]);

const handleChangeTime = (date: any, dateString: string) => {
  emit('on-change-time', dateString);
};

const fetchSideInfo = async () => {
  const { categories, series } = props.salesInfo;
  const result: { name: any; category: any; data: any }[] = [];
  categories.map((item1: any, index: number) => {
    series.map((item2: any) => {
      const obj = {
        name: item2.name,
        category: item1,
        data: item2.data[index].y,
      };
      result.push(obj);
    });
  });
  if (!chart) {
    await createCharts();
    await initCharts(result);
  } else {
    chart.changeData(result);
  }
};

watch(
  () => props.salesInfo,
  value => {
    if (value) {
      fetchSideInfo();
    }
  },
);

const rateOption = [
  {
    title: '累计订单数',
    key: 'delivery_rate',
    countKey: 'total_num',
    week: 'diff_seven_order_num',
    one: 'diff_one_order_num',
  },
  {
    title: '发货率',
    key: 'delivery_rate',
    countKey: 'delivery_rate',
    week: 'diff_seven_rate',
    one: 'diff_one_rate',
  },
  {
    title: '申诉率',
    key: 'appeal_rate',
    countKey: 'appeal_rate', // & 100 %
    week: 'diff_seven_rate',
    one: 'diff_one_rate',
  },
];

function formatRate(rate: number, format = false) {
  if (format) {
    return Math.abs(rate * 100).toFixed(2) + '%';
  }
  return Math.abs(rate * 100) + '%';
}

interface IChart extends Window {
  G2?: any;
  G2Plot?: any;
  L7?: any;
}

const parentContent = ref();

let chart: any = null;

const createCharts = async () => {
  await nextTick();
  await loadG2();
  const { G2 } = window as IChart;
  if (G2) {
    chart = new G2.Chart({
      container: 'renderMon',
      forceFit: true,
      autoFit: true,
      height: 240,
    });
  }
};

async function initCharts(data: any) {
  chart.data(data);
  chart.tooltip({
    crosshairs: {
      type: 'line',
    },
  });
  chart.scale('temperature', {
    min: 0,
    nice: true,
  });
  chart.legend({
    position: 'top-left',
  });
  chart.axis('temperature', {
    label: {
      formatter: function formatter(val: number) {
        return val + '°C';
      },
    },
  });
  chart.line().position('category*data').color('name').shape('smooth');
  chart.point().position('category*data').color('name').size(4).shape('circle').style({
    stroke: '#fff',
    lineWidth: 1,
  });
  chart.render();
}
</script>
<style scoped lang="less">
.flex-justify {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}

.items {
  width: 100%;
  margin: 24px -36px;
  .item {
    display: flex;
    flex-basis: 24%;
    height: 80px;
    padding: 0 24px;
    &:not(:first-child) {
      border-left: 1px solid rgba(6, 21, 51, 0.06);
    }
    .data-set {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 12px;
      font-weight: 400;
      white-space: nowrap;
      .title {
        color: rgba(6, 21, 51, 0.65);
        font-size: 14px;
      }
      .num {
        margin-top: 2px;
        color: rgba(48, 51, 58, 0.85);
        font-weight: 500;
        font-size: 24px;
      }
      .per-title {
        color: rgba(6, 21, 51, 0.25);
      }
      .down {
        color: #52c41a;
      }
      .up {
        color: #ff4d4f;
      }
    }
  }
}

.table-card {
  :deep(.ant-card-body) {
    padding: 24px 24px 0;
  }
}
.card-list {
  :deep(.ant-card-body) {
    padding: 0 24px;
  }
}

.chart-box {
  width: 100%;
  height: 280px;
  margin-top: 24px;
}
</style>
