<template>
  <!-- 逾期租金 -->
  <a-modal
    v-model:visible="bindVisible"
    title="修改逾期租金"
    width="583px"
    @ok="submit"
  >
    <a-spin :spinning="overdueData.loading">
      <!-- @on-visible-change="beforeOverdueRentalClose" -->
      <div class="overdue-dule-box">
        <div class="overdue-dule-title">
          修改须知：归还时，用户需要先缴清「逾期租金」才可以进行归还；当订单产生「逾期租金」后，商家可通过该功能对「逾期租金」进行部分或者全部免除；
        </div>
        <div class="overdue-dule-now-money">
          当前待缴纳逾期租金：<span v-if="extraData.wait_pay">￥{{ Number(extraData.wait_pay).toFixed(2) }}</span>
        </div>
        <div class="overdue-dule-change-money flex-wrap">
          <div style="flex-shrink: 0">
            待缴纳逾期租金修改为：
          </div>
          <div class="flex-wrap overdue-input-box">
            <a-input
              v-model:value="overdueData.changeData"
              placehoder="请输入"
              type="text"
              @input="judgeChangeData"
            />
            <div
              v-show="!overdueData.isCompliance"
              class="error-text"
            >
              输入不合规
            </div>
          </div>
        </div>
        <div class="overdue-dule-bottom-text">
          <InfoCircleOutlined stle="font-size:15px" />
          最大值不能超过当前订单的待缴纳逾期租金；最小值不能小于0；输入0时，表示不需要用户缴纳逾期租金；
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { PropType, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { useVModel } from '@/hook';
import { overDueNet } from './services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

interface IExtraData {
  wait_pay?: number | string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const overdueData = reactive<{
  changeData: string;
  isCompliance: boolean;
  loading: boolean;
}>({
  changeData: '',
  isCompliance: true,
  loading: false,
});

const bindVisible = useVModel(props, 'visible', emit);
watch(
  () => bindVisible.value,
  value => {
    if (!value) {
      overdueData.changeData = '';
    }
  },
  { immediate: true },
);

// 判断输入的值是否合规
function judgeChangeData() {
  const changeData = Number(overdueData.changeData);
  if (overdueData.changeData === '') {
    overdueData.isCompliance = true;
    return;
  }
  if (isNaN(changeData) || changeData < 0 || changeData > Number(props.extraData.wait_pay)) {
    overdueData.isCompliance = false;
    return;
  }
  overdueData.isCompliance = true;
}

function submit() {
  if (!overdueData.isCompliance || overdueData.changeData === '') {
    return message.error('请正确填写后提交');
  }
  overdueData.loading = true;
  overDueNet({
    method: 'overdue.rent.update.overdue.rent',
    order_id: props.orderId,
    wait_pay: overdueData.changeData,
  })
    .then(() => {
      message.success('修改成功');
      bindVisible.value = false;
      emit('refresh', props.orderId, ['data', 'all_remark']);
    })
    .finally(() => {
      overdueData.loading = false;
    });
}
</script>

<style lang="less" scoped>
.overdue-dule-title {
  height: 44px;
  margin-bottom: 24px;
  color: #061533;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.overdue-dule-change-money,
.overdue-dule-now-money {
  height: 22px;
  margin-bottom: 13px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.overdue-dule-change-money input {
  padding-left: 4px;
}

.overdue-dule-now-money span {
  width: 50px;
  height: 22px;
  color: #ff4d4f;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.overdue-dule-bottom-text {
  height: 40px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}

.overdue-data-add {
  color: #ff4d4f;
}

.overdue-data-subtract {
  color: #00c8be;
}

.overdue-page {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.title-box {
  height: 106px;
  margin-bottom: 24px;
  padding: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background: #f0f7ff;
}

.title-box .little-text {
  margin-top: 8px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.overdue-red {
  margin-bottom: 8px;
  color: #ff4d4f;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

.overdue-input-box {
  position: relative;

  .error-text {
    position: absolute;
    top: 0;
    right: -80px;
    margin-left: 4px;
    color: red;
  }
}
</style>
