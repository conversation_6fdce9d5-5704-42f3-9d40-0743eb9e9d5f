import { Modal, message } from 'ant-design-vue';
import { apiDeleteRuleConfig } from '../services';
export function useRuleDel(refreshFn: () => void) {
  function delRuleItem(id: string) {
    Modal.confirm({
      title: '提示',
      content: '确定删除该规则吗？',
      onOk: () => {
        return apiDeleteRuleConfig({ id }).then(() => {
          message.success('操作成功');
          refreshFn();
        });
      },
    });
  }
  return {
    delRuleItem,
  };
}
