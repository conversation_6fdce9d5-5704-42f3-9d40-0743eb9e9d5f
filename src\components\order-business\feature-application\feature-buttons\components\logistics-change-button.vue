<template>
  <div>
    <a-popover
      v-model:visible="popoverVisible"
      :overlay-style="{ width: '250px' }"
      trigger="click"
    >
      <template #content>
        <div style="line-height: 22px">
          <ExclamationCircleOutlined style="color: #faad14" />
          {{ popoverText }}
        </div>
        <div class="popover-btn">
          <template v-if="!isOverstep">
            <a-button
              size="small"
              @click="popoverVisible = false"
            >
              取消
            </a-button>
            <a-button
              size="small"
              style="margin-left: 6px"
              type="primary"
              @click="showModal()"
            >
              确认
            </a-button>
          </template>
          <a-button
            v-else
            size="small"
            type="primary"
            @click="popoverVisible = false"
          >
            我知道了
          </a-button>
        </div>
      </template>
    </a-popover>
    <div
      class="text-btn"
      @click="onCheckChangeRecord"
    >
      物流变更
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import { basicModal } from '@/components/order-business/basic-modals';
import { useUnityModal } from '@/components/unity-modal';

import { logisticsChangeCheckCount } from '../services';

const props = defineProps<{
  orderId?: string;
}>();

const popoverVisible = ref(false);
const isOverstep = ref(false);
const buttonLoading = ref(false);

/** 冒泡提示 */
const popoverText = computed(() => {
  return isOverstep.value
    ? '该订单已超过申请次数（3次）'
    : '该订单已发起过物流变更申请，确定再次提交申请吗（原申请自动取消）？';
});

function onCheckChangeRecord() {
  if (buttonLoading.value) {
    return;
  }
  buttonLoading.value = true;
  popoverVisible.value = false;
  logisticsChangeCheckCount(props.orderId)
    .then(({ data }) => {
      if (data?.count) {
        popoverVisible.value = true;
        isOverstep.value = data.count >= 3;
      } else {
        showModal();
      }
    })
    .finally(() => {
      buttonLoading.value = false;
    });
}

const { onToggleComponent } = useUnityModal();

function showModal() {
  popoverVisible.value = false;
  onToggleComponent(basicModal.logisticChange, { orderId: props.orderId });
}
</script>

<style scoped lang="less">
.text-btn {
  padding: 4px 15px;
}

.popover-btn {
  display: flex;
  justify-content: end;
  padding-top: 4px;
}
</style>
