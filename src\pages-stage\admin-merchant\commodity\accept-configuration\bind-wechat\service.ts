import { POST } from '@/services/api';

export const updateWxStatus = (data: { id: any }): Promise<any> => {
  return POST('/v2-tool-setting/wx-status', data);
};

export const deleteWx = (data: { id: any }): Promise<any> => {
  return POST('/v2-tool-setting/wx-delete', data);
};

export const updateWx = (data: { id: any; field: 'remark_name'; value: string }): Promise<any> => {
  return POST('/v2-tool-setting/wx-update', data);
};
