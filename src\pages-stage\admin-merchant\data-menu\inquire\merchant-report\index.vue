<template>
  <layout-admin-page
    class="merchant-report"
    :navs="['数据', '数据查询', '考核成绩单']"
    title="考核成绩单"
  >
    <a-spin :spinning="loading">
      <div class="data-result">
        <div class="data-result-header">
          <span>成绩单ID：{{ detailData.transcript }}</span>
          <span>考核周期：近9前7，即接单时间在{{ detailData.time }}的订单</span>
        </div>

        <form-create
          ref="searchFormRef"
          v-model:value="searchValue"
          :form-group="searchFormGroup"
          :origin-props="{
            layout: 'inline',
          }"
        />

        <template v-if="searchValue.date">
          <div class="data-result-content">
            <div class="data-result-content-title">
              数据结果
            </div>
            <a-row
              v-for="(item, index) in tableList"
              :key="index"
              class="data-result-table"
            >
              <a-col
                class="result-table-title"
                :span="3"
              >
                {{ item.title }}
              </a-col>
              <a-col :span="21">
                <template
                  v-for="key in ['label', 'reachValue', 'realityValue']"
                  :key="key"
                >
                  <a-row>
                    <a-col
                      v-for="(tableKeyItem, tableKeyIndex) in item[key]"
                      :key="tableKeyIndex"
                      class="col-border"
                      :flex="tableKeyIndex === 0 ? '112px' : '1'"
                    >
                      <template v-if="['realityValue'].includes(key) && tableKeyIndex !== 0">
                        <span :class="item['is_pass'][tableKeyIndex] ? 'green' : 'red'">{{ tableKeyItem }}</span>
                      </template>
                      <template v-else>
                        {{ tableKeyItem }}
                      </template>
                    </a-col>
                  </a-row>
                </template>
              </a-col>
            </a-row>
          </div>

          <div
            v-if="Object.keys(penaltyResultList).length"
            class="data-result-content"
          >
            <div class="data-result-content-title">
              奖惩结果
            </div>

            <div
              v-for="(item, key) in penaltyResultList"
              :key="key"
              class="penalty-result"
            >
              <div
                class="penalty-item"
                style="font-weight: bold"
              >
                考核【{{ item.resultChild.name }}】不达标，命中处罚
              </div>
              <div class="penalty-item">
                处罚内容【{{ item.resultChild.penalty_content }}】预计生效时间段【{{
                  item.resultChild.date_range_str
                }}】
              </div>
              <div
                v-if="item.is_invalid"
                class="penalty-item primary"
              >
                该处罚已解除，解除时间为：{{ item.resultChild.invalid_time_str }}
              </div>
            </div>
          </div>
        </template>

        <REmpty v-else />
      </div>
    </a-spin>
  </layout-admin-page>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import dayjs from 'dayjs';

import type { FormGroupItem } from '@/components/form-create/src/typing';

import { hKeys, indicatorLabelObj, rateKeys, symbolObj } from './config';
import type { IPenaltyResult, ITableItemType } from './data';
import { apiGetOptions, apiGetReportDetail } from './service';

const searchValue = reactive<{ date?: number }>({
  date: undefined,
});

const tableList = ref<ITableItemType[]>([]);
const penaltyResultList = ref<Record<string, IPenaltyResult>>({});

const loading = ref<boolean>(false);
const detailData = reactive({
  transcript: '--',
  time: '--',
});

const filterIndicator = (key, text, symbol?): string => {
  let resNum: string | number = (text = Number(text));
  if (rateKeys.includes(key)) resNum = (text * 100).toFixed(2) + '%';
  if (hKeys.includes(key)) resNum = (text / 3600).toFixed(2) + 'h';
  return (symbol ? symbolObj[symbol] : '') + resNum;
};

const loadData = () => {
  if (!searchValue.date) return;
  tableList.value.length = 0;
  loading.value = true;
  apiGetReportDetail(searchValue)
    .then(res => {
      const date_str = (searchValue.date as number).toString();
      detailData.transcript = res.data.others.transcript;
      detailData.time = ` ${dayjs(date_str).subtract(9, 'days').format('YYYY-MM-DD')} - ${dayjs(date_str)
        .subtract(3, 'days')
        .format('YYYY-MM-DD')} `;

      const list = res.data.lines || [];
      const penalty_result: Record<string, IPenaltyResult> = {};
      list.forEach(item => {
        const tableItem: ITableItemType = {
          title: '',
          label: [''],
          reachValue: ['达标值'],
          realityValue: ['实际值'],
          is_pass: [false],
        };
        tableItem.title = item.name;
        Object.keys(indicatorLabelObj).forEach(key => {
          if (item[key].is_set) {
            tableItem.label.push(indicatorLabelObj[key]);
            tableItem.reachValue.push(filterIndicator(key, item[key].pass_line.val, item[key].pass_line.symbol));
            tableItem.realityValue.push(filterIndicator(key, item[key].val));
            tableItem.is_pass.push(item[key].is_pass);
          }
        });
        item.penalty_result.forEach(i => {
          i.name = item.name;
          const resultItem = penalty_result[i.penalty_content];
          if (!resultItem) {
            penalty_result[i.penalty_content] = { is_invalid: i.is_invalid, children: [i] };
          } else {
            const newChild = [...resultItem.children!, i];
            const newValid = newChild.every(c => c.is_invalid);
            penalty_result[i.penalty_content] = { is_invalid: newValid, children: newChild };
          }
        });
        tableList.value.push(tableItem);
      });
      // 特殊处理奖惩结果
      Object.keys(penalty_result).forEach(key => {
        let min;
        let max;
        let maxInvalid;
        penalty_result[key].children!.forEach(item => {
          const date_range = item.date_range_str.split('~');
          const [minDate, maxDate] = date_range;
          !min && (min = minDate);
          !max && (max = maxDate);
          !maxInvalid && (maxInvalid = item.invalid_time_str);
          maxInvalid &&
            (maxInvalid = dayjs(maxInvalid).isAfter(item.invalid_time_str) ? maxInvalid : item.invalid_time_str);
          if (min && max) {
            min = dayjs(min).isBefore(minDate) ? min : minDate;
            max = dayjs(max).isAfter(minDate) ? max : maxDate;
          }
        });
        penalty_result[key].resultChild = {
          ...penalty_result[key].children![0],
          invalid_time_str: maxInvalid,
          date_range_str: min + '~' + max,
        };
        delete penalty_result[key].children;
      });
      penaltyResultList.value = penalty_result;
    })
    .finally(() => {
      loading.value = false;
    });
};

const searchFormGroup = ref<FormGroupItem[]>([
  {
    key: 'date',
    fragmentKey: 'renderSelect',
    originProps: { label: '下发日期', name: 'date' },
    options: [],
    elProps: { placeholder: '请选择', style: { width: '224px' }, onChange: loadData },
  },
]);

const getOptions = () => {
  loading.value = true;
  apiGetOptions()
    .then(res => {
      searchFormGroup.value[0].options = res.data.map(item => ({
        value: item.date,
        label: item.label,
      }));
      if (res.data.length) {
        searchValue.date = res.data[0].date;
        loadData();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  getOptions();
});
</script>

<style lang="less" scoped>
.merchant-report {
  .data-result {
    padding: 0 24px;

    &-header {
      display: flex;
      gap: 24px;
      align-items: center;
      margin-bottom: 24px;
    }

    &-content {
      margin-top: 24px;
      padding: 16px;
      border: 1px solid rgba(0, 0, 0, 0.06);

      &-title {
        position: relative;
        margin-bottom: 16px;
        padding-left: 12px;
        font-weight: bold;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 4px;
          height: 14px;
          background: #3777ff;
          border-radius: 2px;
          transform: translate(0, -50%);
          content: '';
        }
      }

      .penalty-result {
        margin-bottom: 24px;

        .penalty-item {
          margin-bottom: 8px;

          &.primary {
            color: var(--ant-primary-color);
          }
        }
      }
    }

    &-table {
      max-width: 808px;
      margin-bottom: 16px;
      border-top: 1px solid #f0f1f3;
      border-left: 1px solid #f0f1f3;

      .result-table-title {
        display: flex;
        align-items: center;
        padding: 0 16px;
        font-weight: bold;
        background: #f9f9fb;
        border-right: 1px solid #f0f1f3;
        border-bottom: 1px solid #f0f1f3;
      }

      .col-border {
        display: flex;
        align-items: center;
        padding: 16px;
        border-right: 1px solid #f0f1f3;
        border-bottom: 1px solid #f0f1f3;

        .green {
          color: #52c41a;
        }

        .red {
          color: #ff4d4f;
        }
      }
    }
  }
}
</style>
