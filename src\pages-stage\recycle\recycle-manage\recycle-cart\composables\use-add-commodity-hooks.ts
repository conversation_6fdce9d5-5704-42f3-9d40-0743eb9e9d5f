import { ref, computed, watch } from 'vue';
import dayjs from 'dayjs';
import { filterItemOptions, deviceInfoByImei, evaluatePriceByFilter } from '../service';

export const useAddCommondityHooks = () => {
  // 调用弹窗类型 add / detail
  const enterType = ref<string>('');
  // 基础信息
  const baseFormState = ref<any>({
    imei: null,
  });

  // 关联信息
  const argumentFormState = ref<any>({
    model: null,
    memory: null,
    color: null,
    version: null,
    condition: null,
    is_replace_device: undefined, //1是代发设备，2是非代发设备
  });

  //非代发是否超过五十个设备 1：true 2:false
  const no_replace_device_max = ref(false);

  // 预估回收价
  const price = ref<number>(0);
  //补贴价
  const subsidyPrice = ref<number>(0);
  //总价
  const basePrice = ref<number>(0);
  // 是否触发过IMEI码请求
  const isFirstEnter = ref<boolean>(true);

  const modelOptions = ref<any>([]);
  // 内存下拉
  const memoryOptions = computed(() => {
    const modelItem = modelOptions.value.find((item: any) => item.value === argumentFormState.value.model) || {};
    const options = modelItem.child || [];
    return options;
  });
  // 颜色下拉
  const colorOptions = computed(() => {
    const memoryItem = memoryOptions.value.find((item: any) => item.value === argumentFormState.value.memory) || {};
    return memoryItem.child?.color || [];
  });
  // 版本下拉
  const versionOptions = computed(() => {
    const memoryItem = memoryOptions.value.find((item: any) => item.value === argumentFormState.value.memory) || {};
    return memoryItem.child?.version || [];
  });
  // 成新度下拉
  const conditionOptions = computed(() => {
    const memoryItem = memoryOptions.value.find((item: any) => item.value === argumentFormState.value.memory) || {};
    return memoryItem.child?.condition || [];
  });

  // 获取参数下拉；
  const getFilterItemOptions = async (optionParams = {}, cb?: any) => {
    const res = await filterItemOptions(optionParams);
    const data = res.data || {};
    modelOptions.value = data.filterItem || [];
    no_replace_device_max.value = data.no_replace_device_max === 1 ? true : false;
    cb && cb();
  };

  // 根据IMEI码获取供应链商品情况
  const onIMEIToInfo = async () => {
    const params = { imei: baseFormState.value.imei };
    isFirstEnter.value = false;
    const res = await deviceInfoByImei(params);
    if (!res.data) {
      await getFilterItemOptions({
        imei: baseFormState.value.imei,
      });
      //data为null为非代发设备
      Object.keys(argumentFormState.value).forEach(key => {
        argumentFormState.value[key] = key === 'is_replace_device' ? 2 : null;
      });
      getPrice();
      return;
    }
    argumentFormState.value.is_replace_device = 1;
    await getFilterItemOptions({
      imei: baseFormState.value.imei,
    });
    const data = res.data;
    const { model } = data;
    const hasModel = modelOptions.value.find(
      (item: any) => item.value.replaceAll(/\s+/g, '').toLowerCase() === model.replaceAll(/\s+/g, '').toLowerCase(),
    );
    if (hasModel) {
      const keyArr = Object.keys(argumentFormState.value).filter(key => key !== 'is_replace_device');
      const optionsMap = {
        model: () => modelOptions.value,
        memory: () => memoryOptions.value,
        color: () => colorOptions.value,
        version: () => versionOptions.value,
        condition: () => conditionOptions.value,
      };
      const promisList: any[] = [];
      keyArr.forEach((key: string) => {
        const fn = new Promise((resolve, reject) => {
          const value = data[key].replaceAll(/\s+/g, '').toLowerCase();
          const options = (optionsMap[key] && optionsMap[key]()) || [];
          const hasItem = options.find((item: any) => {
            const itemValue = item.value.replaceAll(/\s+/g, '').toLowerCase();
            return itemValue === value;
          });
          if (!hasItem) {
            console.log('无效key--', key);
            console.log('无效value--', value);
            console.log('下拉数据为：', options);
            reject();
            return;
          } else {
            argumentFormState.value[key] = hasItem.value;
            resolve();
          }
        });
        promisList.push(fn);
      });
      Promise.all(promisList)
        .catch(e => {
          console.error(e);
        })
        .finally(() => {
          getPrice();
        });
    } else {
      getPrice();
    }
  };

  // 动态清空部分表单值;
  const clearKeyValue = (keysArr: string[]) => {
    keysArr.forEach((key: string) => {
      if (argumentFormState.value.hasOwnProperty(key)) {
        argumentFormState.value[key] = null;
      }
    });
  };

  // imei码填写完整时远程获取关联信息；
  const imeiChange = (val: number) => {
    price.value = 0;
    subsidyPrice.value = 0;
    basePrice.value = 0;
    if (String(val).length === 15) {
      onIMEIToInfo();
    }
  };

  const getPrice = async () => {
    const params = {
      ...baseFormState.value,
      ...argumentFormState.value,
    };
    const keysArr = Object.keys(params);
    const isOk = keysArr.every((key: string) => params[key]);
    if (isOk) {
      const res = await evaluatePriceByFilter(params);
      const data = res.data || {};
      price.value = data.evaluate_price || 0;
      subsidyPrice.value = data.evaluate_advance_price || 0;
      basePrice.value = data.evaluate_base_price || 0;
    }
  };

  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 距离本周结束还剩几时几分
  const dateEndMessage = ref('');
  watch(
    () => no_replace_device_max.value,
    val => {
      if (val) {
        const weekEnd = dayjs().endOf('week');
        const now = dayjs();
        // 计算距离本周结束的剩余时间
        const diffMs = weekEnd - now;
        // 将毫秒数转换为小时和分钟
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        dateEndMessage.value = `非代发货回收每周限制50台设备，请等待 ${hours}小时${minutes} 分再次提交`;
      }
    },
  );

  return {
    enterType,
    baseFormState,
    argumentFormState,
    price,
    basePrice,
    subsidyPrice,
    isFirstEnter,

    modelOptions,
    memoryOptions,
    colorOptions,
    versionOptions,
    conditionOptions,

    getFilterItemOptions,
    clearKeyValue,
    imeiChange,
    getPrice,
    filterOption,

    no_replace_device_max,
    dateEndMessage,
  };
};
