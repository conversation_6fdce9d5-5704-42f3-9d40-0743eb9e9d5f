import type { ListQueryType, OptionMapType, MultipleCascaderItemType } from '../data';

import { getSearchMap } from '../service';

import { reactive, computed, watch } from 'vue';
import { deepClone } from '@/utils/base';
import dayjs from 'dayjs';

type SearchType = {
  listQueryDefault: ListQueryType;
  listQuery: ListQueryType;
  optionMap: OptionMapType;
  optionMapLoading: boolean;
  isExpandSearch: boolean;
  isShowExport: boolean;
  buyAgeStart?: number;
  buyAgeEnd?: number;
  goodsPriceStart?: number;
  goodsPriceEnd?: number;
  orderCreatedAt?: string[];
  categoryBrandModel: MultipleCascaderItemType[];
  loadOptionMap(): void;
};

export default function (): SearchType {
  const listQueryDefault: ListQueryType = {
    data_type: '',
    order_created_at: `${dayjs()
      .month(dayjs().month() - 2)
      .startOf('month')
      .format('YYYY-MM-DD')},${dayjs()
      .month(dayjs().month() - 2)
      .endOf('month')
      .format('YYYY-MM-DD')}`,
    overdue_node: 0,
  };
  const listQuery: ListQueryType = deepClone(listQueryDefault);
  const optionMap = {
    overdue_type: [],
    is_lock: [],
    goods_value: [],
    advance_amount: [],
    risk_type: [],
    risk_comprehensive: [],
    goods_types: [],
    model: [],
    brand: [],
    method: [],
    risk_qualitative: [],
    overdue_node: [],
  };

  const buyAgeStart = computed({
    get() {
      return (search.listQuery.buy_age || [])[0] as number;
    },
    set(value: number) {
      if (search.listQuery.buy_age) {
        search.listQuery.buy_age[0] = value;
      } else {
        search.listQuery.buy_age = [value, undefined];
      }
    },
  });
  const buyAgeEnd = computed({
    get() {
      return (search.listQuery.buy_age || [])[1];
    },
    set(value) {
      if (search.listQuery.buy_age) {
        search.listQuery.buy_age[1] = value;
      } else {
        search.listQuery.buy_age = [undefined, value];
      }
    },
  });
  const goodsPriceStart = computed({
    get() {
      return (search.listQuery.goods_price || [])[0];
    },
    set(value) {
      if (search.listQuery.goods_price) {
        search.listQuery.goods_price[0] = value;
      } else {
        search.listQuery.goods_price = [value, undefined];
      }
    },
  });
  const goodsPriceEnd = computed({
    get() {
      return (search.listQuery.goods_price || [])[1];
    },
    set(value) {
      if (search.listQuery.goods_price) {
        search.listQuery.goods_price[1] = value;
      } else {
        search.listQuery.goods_price = [undefined, value];
      }
    },
  });
  const orderCreatedAt = computed({
    get() {
      return search.listQuery.order_created_at.split(',');
    },
    set(value) {
      search.listQuery.order_created_at = value.join(',');
    },
  });
  // 类目/品牌/型号
  const categoryBrandModel: MultipleCascaderItemType[] = [];

  const search: SearchType = reactive({
    listQueryDefault,
    listQuery,
    isExpandSearch: true,
    isShowExport: false,
    optionMapLoading: false,
    optionMap,
    buyAgeStart,
    buyAgeEnd,
    goodsPriceStart,
    goodsPriceEnd,
    orderCreatedAt,
    categoryBrandModel,
    loadOptionMap,
  });

  // 监听类目/品牌/型号切换
  watch(
    () => search.categoryBrandModel,
    list => {
      const cascade_category: number[] = [];
      const cascade_brand: number[] = [];
      const cascade_spu_model: number[] = [];
      list.forEach(item => {
        new Map([
          [0, cascade_category],
          [1, cascade_brand],
          [2, cascade_spu_model],
        ])
          .get(item.level)
          ?.push(item.value);
      });
      Object.assign(listQuery, {
        cascade_category,
        cascade_brand,
        cascade_spu_model,
      });
    },
  );

  function loadOptionMap() {
    search.optionMapLoading = true;
    getSearchMap()
      .then(res => {
        search.optionMap = res.data;
      })
      .finally(() => {
        search.optionMapLoading = false;
      });
  }

  return search;
}
