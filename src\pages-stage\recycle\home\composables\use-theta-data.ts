import { ref, toRaw, onMounted, onBeforeUnmount } from 'vue';
import { debounce } from '@/utils/base';
import { loadG2 } from '@/utils/static-load';
import { getPieDetail } from '../service';

type TParams = {
  element: string;
  url: string;
  totalText: string;
  totalField: string;
};

export default function <T>(params: TParams) {
  const { element, url, totalText, totalField } = params;

  const itemTitleStyle = {
    color: 'rgba(6,21,51,0.65)',
  };
  const itemValueStyle = {
    'font-size': '16px',
    'line-height': '24px',
  };

  const typeOptions = [
    { label: '按设备型号展示图表', value: 'device_model_list' },
    { label: '按成新度展示图表', value: 'device_ratio_pass_list' },
  ];

  const typeValue = ref<string>('device_model_list');

  const chartInfo = ref<Partial<T>>({});

  const loading = ref<boolean>(false);

  const isEmpty = ref<boolean>(false);

  let chart: any = null;

  const formatChartData = (data = []) => {
    const fieldMap = {
      model_name: 'name',
      device_ratio_pass: 'name',
      device_ratio_pass_list: 'childList',
      model_list: 'childList',
    };
    const fieldKeys = Object.keys(fieldMap);

    const list = data.map(item => {
      for (const key in item) {
        if (fieldKeys.includes(key)) {
          item[fieldMap[key]] = item[key];
          if (Array.isArray(item[key])) {
            item[fieldMap[key]] = formatChartData(item[key]);
          }
        }
      }
      return item;
    });
    return list;
  };

  const createCharts = async () => {
    chart?.destroy();
    loading.value = true;
    const { data } = await getPieDetail(url);
    chartInfo.value = data;
    loading.value = false;
    // 若为空数据，则跳过可视图渲染；
    const chartData = toRaw(JSON.parse(JSON.stringify(chartInfo.value[typeValue.value] || [])));
    isEmpty.value = !chartData?.length;
    if (isEmpty.value) return;

    const container = document.getElementById(element) as HTMLDivElement;
    container.innerHTML = '';
    await loadG2();
    if (window.G2) {
      chart = new G2.Chart({
        container: element,
        autoFit: true,
        height: 320,
      });
      render();
    }
  };

  const render = () => {
    const data = toRaw(JSON.parse(JSON.stringify(chartInfo.value[typeValue.value] || [])));
    const chartData = formatChartData(data);
    chart.data(chartData);
    chart.coordinate('theta', {
      radius: 0.85,
      innerRadius: 0.5,
    });

    chart.legend({
      flipPage: false,
    });

    chart.tooltip({
      showTitle: true,
      domStyles: {
        'g2-tooltip-title': {
          'font-size': '14px',
          'font-weight': 'bold',
          'color': 'rgba(6,21,51,0.85)',
          'line-height': '20px',
        },
      },
      title: (title, datum) => {
        return `${datum['name']}\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0${datum['num']}台`;
      },
      customItems: items => {
        const { childList = [], color } = items[0];
        return childList.map(item => {
          return {
            name: item.name,
            value: `${item.num}台`,
            color,
          };
        });
      },
    });
    chart.axis(false); // 关闭坐标轴
    chart
      .interval()
      .adjust('stack')
      .position('num')
      .color('name')
      .label('num', num => {
        return {
          content: data => {
            return `${data.name}: ${num}台`;
          },
        };
      })
      .tooltip('name*num*childList', (name, num, childList) => ({ name, num, childList }))
      .style({
        lineWidth: 1,
        stroke: '#fff',
      });

    chart
      .annotation()
      .text({
        position: ['50%', '50%'],
        content: totalText,
        style: {
          fontSize: 14,
          fill: '#061533',
          fontWeight: 400,
          textBaseline: 'bottom',
          textAlign: 'center',
        },
        offsetY: -12,
      })
      .text({
        position: ['50%', '60%'],
        content: `${Number(chartInfo.value[totalField]) ? chartInfo.value[totalField] + '元' : '暂无数据'}`,
        style: {
          fontSize: 14,
          fill: '#061533',
          fontWeight: 400,
          textBaseline: 'bottom',
          textAlign: 'center',
        },
        offsetY: -12,
      });

    chart.interaction('element-active');

    chart.render();
  };

  onMounted(() => {
    createCharts();
    window.addEventListener('resize', debounce(createCharts, 500));
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', debounce(createCharts, 500));
  });

  return {
    itemTitleStyle,
    itemValueStyle,
    typeOptions,
    typeValue,
    chartInfo,
    createCharts,
    loading,
    isEmpty,
  };
}
