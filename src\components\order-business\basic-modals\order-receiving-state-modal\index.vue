<template>
  <a-modal
    v-model:visible="bindVisible"
    class="receive-modal"
    title="请选择您不接单的状态"
    :width="640"
    @ok="receiveSubmit()"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <div class="flex-wrap flex-vertical flex-gap-16">
        <div
          v-if="isWhite"
          class="tips flex-wrap flex-vertical flex-gap-8"
        >
          <div class="flex-wrap flex-gap-4">
            1.您当前剩余关单次数为
            <span class="count">{{ closeNum }}</span>
            次，次数完结后，关单将列入考核
          </div>
          <div class="flex-wrap flex-gap-4">
            2.只要手动“开启”接单，都会关联您已配置的营业时间进行自动开启和关闭
          </div>
          <div class="flex-wrap flex-gap-4">
            3.夜间时段可自由开启/关闭接单按钮，不列入次数考核
          </div>
        </div>

        <a-radio-group v-model:value="noOrderType">
          <a-radio
            style="margin-bottom: 8px"
            :value="1"
          >
            当天不接单
            <span style="color: rgba(6, 21, 51, 0.45)">(仅当天关闭接单，次日的营业时间将为您自动开启接单。)</span>
          </a-radio>
          <a-radio :value="2">
            不接单
            <span style="color: rgba(6, 21, 51, 0.45)">(一直处于不接单状态，手动开启后才为您重新关联营业时间自动开关。)</span>
          </a-radio>
        </a-radio-group>

        <div
          v-if="!isWhite"
          class="tips"
        >
          <InfoCircleOutlined style="margin-right: 8px" />
          提示：只要手动”开启“接单，都会关联您已配置的营业时间进行自动开启和关闭
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';

import { getServerResidue, updateReceive } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'resetOptions'): void;
}>();
const bindVisible = useVModel(props, 'visible', emits);
const loading = ref(false);

const noOrderType = ref<number>(1);
const closeNum = ref(0);
const isWhite = ref(false);

/**
 * 打开关单弹窗
 */
function loadData() {
  loading.value = true;
  getServerResidue()
    .then(({ data }) => {
      closeNum.value = data?.close_num || 0;
      isWhite.value = data?.white_server || false;
      bindVisible.value = true;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 提交接单状态修改 */
function receiveSubmit() {
  loading.value = true;
  updateReceive({
    is_dispatch: !noOrderType.value,
    no_order_type: noOrderType.value,
  })
    .then(res => {
      bindVisible.value = false;
      message.success(res.message);
      emits('resetOptions');
    })
    .finally(() => {
      loading.value = false;
    });
}

watch(
  () => props.visible,
  val => {
    if (val) {
      loadData();
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="less">
.receive-modal {
  .tips {
    padding: 12px 16px;
    color: rgba(6, 21, 51, 0.65);
    background: #f0f7ff;
    border-radius: 4px;

    .count {
      color: #f33848;
    }
  }
}
</style>
