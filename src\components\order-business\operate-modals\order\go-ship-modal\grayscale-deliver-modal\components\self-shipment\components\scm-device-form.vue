<template>
  <a-form
    ref="formRef"
    :label-col="{ style: { width: '180px' } }"
    :model="deviceFormState"
    :wrapper-col="{ style: { flex: '0 0 365px' } }"
  >
    <a-form-item
      label="设备编码"
      name="sku"
      :rules="[{ required: true, message: '请选择SKU', trigger: 'change' }]"
    >
      <a-cascader
        v-model:value="deviceFormState.sku"
        change-on-select
        :field-names="{
          label: 'name',
          value: 'id',
          children: 'child',
        }"
        :load-data="loadData"
        :max-tag-count="2"
        :options="skuOptions"
        placeholder="请选择SKU"
      />
    </a-form-item>
    <a-form-item
      class="form-warehouse"
      label=""
      name="warehouse_id"
      :rules="[{ required: true, message: '请选择仓库', trigger: 'change' }]"
      :wrapper-col="{ style: { marginLeft: '180px', flex: '0 0 365px' } }"
    >
      <div class="warehouse-box">
        <a-select
          v-model:value="deviceFormState.warehouse_id"
          allow-clear
          :field-names="{
            label: 'warehouse_name',
            value: 'id',
          }"
          :options="warehouseList"
          placeholder="全部仓库"
          show-search
        />
        <a-button
          type="primary"
          @click="handleSelect"
        >
          查询库存
        </a-button>
      </div>
      <div class="sale-text">
        可销售库存：{{ canSaleValue }}
      </div>
    </a-form-item>
    <a-form-item
      label=""
      name="device_codes"
      :rules="[{ required: true, message: '请输入设备编码', trigger: 'blur' }]"
      :wrapper-col="{ style: { marginLeft: '180px', flex: '0 0 365px' } }"
    >
      <MultipleInput
        ref="multipleInput"
        v-model:value="deviceFormState.device_codes"
        placeholder="支持输入多个设备编码，用空格/英文逗号隔开"
        style="width: 100%"
      />
      <div class="infer">
        绑码后SCM系统库存将被该订单占用
      </div>
    </a-form-item>
  </a-form>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import type { CascaderProps, FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';

import MultipleInput from '@/components/multiple-input/multiple-input.vue';

import { getSkuApi, queryInventoryApi } from '../../../../services';

const props = defineProps({
  skuOptions: {
    type: Array,
    default: () => [],
  },
  warehouseList: {
    type: Array,
    default: () => [],
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['handleChangeCode']);

const deviceFormState = ref({
  sku: [],
  warehouse_id: undefined,
  device_codes: '',
});

const canSaleValue = ref<number | string>('-'); //可销售数量
const formRef = ref<FormInstance>();

const loadData: CascaderProps['loadData'] = async (selectedOptions: any) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  if (selectedOptions.length === 2) {
    targetOption.child.forEach(item => {
      item.isLeaf = false;
    });
  }
  if (selectedOptions.length === 3) {
    const [category_id, brand_id] = deviceFormState.value.sku;
    const { data } = await getSkuApi({
      order_id: props.orderId,
      category_id,
      brand_id,
      model_id: targetOption.id,
    });
    if (data && data.length) {
      targetOption.child = data.map(item => ({ id: item.sku_id, name: item.sku_info }));
    } else {
      targetOption.isLeaf = true;
    }
  }
  targetOption.loading = false;
};

const handleSelect = async () => {
  const { sku, warehouse_id } = deviceFormState.value;
  if (sku.length !== 4) {
    message.warning('未找到该型号下满足发货设备用途的可销售设备');
    return;
  }
  const sku_id = sku[3];
  const { data } = await queryInventoryApi({
    order_id: props.orderId,
    sku_id,
    warehouse_id,
  });
  canSaleValue.value = data.device_num;
  deviceFormState.value.device_codes = data.device_codes.join(',');
};

const handleResetFields = () => {
  deviceFormState.value = {
    sku: [],
    warehouse_id: undefined,
    device_codes: '',
  };
  canSaleValue.value = '-';
};

// 定义校验方法
const validate = async () => {
  if (!formRef.value) return Promise.reject('表单实例未初始化');
  await formRef.value.validate();
  return Promise.resolve();
};

watch(
  () => deviceFormState.value.device_codes,
  val => {
    emits('handleChangeCode', val);
  },
);

defineExpose({
  handleResetFields,
  validate,
});
</script>
<style lang="less" scoped>
.form-warehouse {
  margin-bottom: 0;
}
.warehouse-box {
  display: flex;
  gap: 16px;
  align-items: center;
}

.sale-text {
  color: #3777ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
.infer {
  color: #ff4d4f;
}
.infer::before {
  color: #ff4d4f;
  content: '*';
}
</style>
