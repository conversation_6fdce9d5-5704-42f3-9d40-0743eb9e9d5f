function getKLF(key: string, label: string) {
  return { key, label };
}

export const formGroup = [
  getKLF('investment_type', '招商类型'),
  getKLF('work_province', '办公地点'),
  {
    ...getKLF('office', '办公室'),
    childrenDisplay: form => form['office'] === '11',
    children: [
      getK<PERSON>('office_position', '办公室位置'),
      getK<PERSON>('office_env', '办公环境'),
      getKLF('office_area', '办公面积'),
      getKLF('office_business', '办公室业务'),
      getKLF('office_inventory_num', '设备库存数量'),
      getKLF('office_inventory_cate', '设备库存种类'),
      getK<PERSON>('office_stash', '仓库情况'),
    ],
  },
  {
    ...getKLF('office_team', '办公室团队'),
    childrenDisplay: form => form['office_team'] === '11',
    children: [getK<PERSON>('office_person_number', '办公室人员数量'), getKLF('office_person_make', '办公室人员构成情况')],
  },
  {
    key: 'offline_stores',
    label: '线下门店',
    childrenDisplay: form => form['offline_stores'] === '11',
    children: [
      getKLF('stores_position', '门店位置'),
      getKLF('stores_city_position', '门店在城市位置'),
      getKLF('stores_signboard', '门店招牌'),
      getKLF('stores_env', '门店环境'),
      getKLF('stores_area', '门店面积'),
      getKLF('stores_business', '门店业务'),
      getKLF('stores_inventory_num', '门店库存数量'),
      getKLF('stores_inventory_cate', '门店库存种类'),
      getKLF('stores_stash', '门店仓库情况'),
      getKLF('stores_person_number', '门店人员数量'),
      getKLF('stores_person_make', '门店人员构成情况'),
      getKLF('stores_main_customer', '门店主要客群'),
      getKLF('stores_customer_flow', '门店客流量/日'),
      getKLF('stores_nature', '门店性质'),
    ],
  },
  { key: 'source_funds', label: '资金来源' },
  { key: 'bad_debt', label: '坏账可承受范围' },
  { key: 'operation_mode', label: '运营模式' },
  { key: 'industry_experience', label: '租赁行业经验' },
  { key: 'year_invest_amount', label: '年投资金' },
  { key: 'investment_amount', label: '预估加投资金' },
  { key: 'year_business_turnover', label: '年营业额' },
  { key: 'cash_conv', label: '资金周转周期' },
  { key: 'third_party_risk', label: '第三方风控能力' },
  { key: 'bad_debt_method', label: '现有坏账处理方式' },
  { key: 'open_store_other', label: '其他平台开店' },
  { key: 'drainage_method', label: '引流方式' },
  { key: 'lease_bkg', label: '是否有租赁背景' },
  { key: 'daily_order_limit', label: '日承接订单上限' },
  {
    key: 'store_project',
    label: '是否已有店铺入驻项目',
    childrenDisplay: form => form['store_project'] === '10',
    children: [
      { key: 'same_legal', label: '是否同一个法人' },
      { key: 'same_license', label: '是否用一个营业执照' },
      { key: 'same_team', label: '是否同一个运营团队' },
    ],
  },
  { key: 'own_supply_chain', label: '自有供应链' },
  getKLF('accept_untaxed_equipment', '是否能接受未税设备'),
  getKLF('be_on_duty', '是否周一到周日都有人值班'),
  getKLF('model_accept_intention', '机型接单倾向'),
  getKLF('rest_day_duty', '周六日值班情况'),
  getKLF('rest_day_work', '节假日上班情况'),
  {
    ...getKLF('accept_no_service_equipment', '是否接受不带租赁服务设备'),
    childrenDisplay: form => form['accept_no_service_equipment'] === '11',
    children: [getKLF('standard_risk_rule_other', '具体的风控规则和描述')],
  },
  getKLF('customer_service_system', '有无客户服务制度'),
  getKLF('risk_pass_order_service_equipment', '风控通过订单是否接受不带租赁服务的设备'),
  {
    ...getKLF('standard_risk_rule', '有无标准版的风控规则'),
    childrenDisplay: form => form['standard_risk_rule'] === '10',
    children: [getKLF('standard_risk_rule_other', '具体的风控规则和描述')],
  },
  getKLF('third_risk_tool', '有无第三方风控工具'),
  getKLF('supply_chain_channel', '供应链渠道'),
  getKLF('business_information_money', '注册资金'),
  { key: 'business_information_address', label: '注册地址' },
];

export const renderMap = {
  work_province: (value, record) => {
    const { work_city, work_county } = record;
    if (value && work_city && work_county) return [value, work_city, work_county].join('-');
    return '-';
  },
  year_invest_amount: value => (value ? `${value}万` : '-'),
  investment_amount: value => (value ? `${value}万` : '-'),
  year_business_turnover: value => (value ? `${value}万` : '-'),
};
