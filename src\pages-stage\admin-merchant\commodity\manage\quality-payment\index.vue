<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px 24px' }"
    title="待付款商品"
  >
    <!-- 搜索表单 -->
    <search-form
      ref="searchFormRef"
      :form-item-list="formItemArr"
      @search="search"
    />
    <div class="content">
      <a-tabs
        v-model:activeKey="activeTab"
        :animated="false"
        @change="tabChange"
      >
        <a-tab-pane
          v-for="tab in tabs"
          :key="tab.value"
          :tab="tab.label"
        />
        <template
          v-if="type === 'admin'"
          #tabBarExtraContent
        >
          <a-button
            style="margin: 15px 40px 0 0"
            type="primary"
            @click="showPriceModal"
          >
            价格设置
          </a-button>
        </template>
      </a-tabs>
      <a-table
        class="table"
        :columns="cols"
        :data-source="dataSource"
        :loading="listLoading"
        :pagination="false"
        row-key="id"
        :scroll="{ x: '100%' }"
        :sticky="true"
      >
        <template #operations="{ record }">
          <span
            v-for="action in actions"
            :key="action.title"
          >
            <a
              class="action"
              @click="actionHandler(action.cb, record)"
            >{{ action.title }}</a>
          </span>
        </template>
      </a-table>
      <pagination
        ref="myPaginationRef"
        :create-page-data="createPageData"
      />
    </div>
    <!-- 支付弹窗 -->
    <pay-modal
      v-model:visible="payModalData.visible"
      :data="payModalData"
      @get-list="createPageData(true)"
    />
    <!-- 价格设置弹窗 -->
    <set-price-modal
      v-model:visible="setPriceModalData.visible"
      :batch-select="setPriceModalData.batchSelect"
      :current-batch="setPriceModalData.currentBatch"
      @success="createPageData"
    />
  </layout-shops-page>
</template>

<script lang="ts">
import { defineComponent, onMounted,Ref, ref } from 'vue';

import Pagination from '@/components/pagination/index.vue';

import PayModal from './components/pay-modal.vue';
import SearchForm, { IFormItemList } from './components/search-form.vue';
import setPriceModal from './components/set-price-modal.vue';
import useTable from './composables/use-table';
import { getListApi } from './services';
interface ITab {
  label: string;
  value: any;
}
const configData = {
  //商家
  merchant: {
    breadcrumb: '待付款',
    tabs: [
      { label: '待付款', value: 0 },
      { label: '已付款', value: 1 },
    ],
    path: '/quality-spu-batch-pay/index-list',
  },
  //运营
  admin: {
    breadcrumb: '待收款',
    tabs: [
      { label: '待收款', value: 0 },
      { label: '已收款', value: 1 },
    ],
    path: '/super/quality-spu-batch-pay/index',
  },
};
export default defineComponent({
  components: { SearchForm, Pagination, PayModal, setPriceModal },
  props: {
    //merchant：商家后台; admin：运营后台
    type: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    // eslint-disable-next-line vue/no-setup-props-destructure
    const type = props.type;
    //data
    let breadcrumb: string[] = configData[type].breadcrumb;
    let tabs: ITab[] = configData[type].tabs;
    let activeTab: Ref<number> = ref(0);
    const searchFormRef = ref();
    //分页组件Ref
    const myPaginationRef = ref();

    const formItemArr: Ref<IFormItemList> = ref<IFormItemList>([]);
    //hooks
    const {
      cols,
      actions,
      dataSource,
      listLoading,
      payModalData,
      setPriceModalData,
      actionHandler,
      showPriceModal,
    } = useTable(type, activeTab, createPageData);

    //获取首屏数据
    async function createPageData(isDeleted?: boolean) {
      listLoading.value = true;
      const path = configData[type].path;
      try {
        const {
          data: { pageInfo, list },
        } = await getListApi(path, {
          is_pay: activeTab.value, //是否已付款（0否1是）
          ...searchFormRef.value.formState,
          ...myPaginationRef.value.handlePageBehind(isDeleted),
        });
        myPaginationRef.value.handlePageFront(pageInfo);
        dataSource.value = list;
      } finally {
        listLoading.value = false;
      }
    }

    //methods
    function tabChange() {
      dataSource.value = [];
      myPaginationRef.value.setCurrent(1);
      createPageData();
    }

    function initSearchForm() {
      const formItems: IFormItemList = [
        {
          type: 'input',
          name: 'sign',
          label: '检测批次',
          placeholder: '请输入',
          value: '',
        },
      ];
      if (type === 'admin') {
        formItems.push({
          type: 'input',
          name: 'server_name',
          label: '商家名称',
          placeholder: '请输入',
          value: '',
        });
      }
      formItemArr.value = formItems;
    }
    initSearchForm();

    function search() {
      myPaginationRef.value.setCurrent(1);
      createPageData();
    }
    onMounted(() => {
      createPageData();
    });
    return {
      breadcrumb,
      tabs,
      activeTab,
      formItemArr,
      cols,
      actions,
      dataSource,
      listLoading,
      myPaginationRef,
      searchFormRef,
      payModalData,
      setPriceModalData,
      //func
      createPageData,
      tabChange,
      actionHandler,
      showPriceModal,
      search,
    };
  },
});
</script>
<style lang="less" scoped>
.action {
  padding: 0 12px;
}
</style>
