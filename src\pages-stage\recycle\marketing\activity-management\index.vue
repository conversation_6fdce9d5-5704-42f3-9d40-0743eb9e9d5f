<template>
  <layout-admin-page
    :navs="['趣回收', '营销管理', '活动管理']"
    title="活动管理"
  >
    <div class="main">
      <a-table
        class="bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: '1118px' }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-switch
              :checked="record.status == 1"
              checked-children="启用"
              un-checked-children="停用"
              @change="changeStatus($event, record)"
            />
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { useTable } from '@/hook/component/use-table';
import { columns } from './config';
import { IDataInfo } from './data';
import { changeItemStatus } from './service';

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  url: '/super/recycle/recycle-activity/list',
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatHandle: (res: { data: { list: IDataInfo[] } }) => {
    const data = res.data || {};
    const list = data.list || [];
    return list;
  },
  pagination: {
    showTotal: (): string => {
      let totalPages = Math.ceil((page.total ?? 0) / (page.pageSize ?? 0));
      return `共 ${page.total} 条记录   第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const changeStatus = async (e: any, item: IDataInfo) => {
  item.status = !e ? 2 : 1;
  const params = {
    id: item.id,
    status: item.status,
  };
  await changeItemStatus(params);
  getTableList();
};

getTableList();
</script>

<style lang="less" scoped>
.main {
  padding: 0 24px;
  .form-item-block {
    margin-right: 24px;
  }
  .form-item {
    display: inline-flex;
    flex-wrap: wrap;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    &.time-text {
      color: rgba(0, 0, 0, 0.65);
    }
  }
  .handle-btn {
    margin-right: 16px;
    padding: 0;
    &:nth-last-child(1) {
      margin-right: 0;
    }
    &.primary {
      color: #3777ff;
    }
  }
}
</style>
