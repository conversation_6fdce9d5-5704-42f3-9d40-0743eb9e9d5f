import { renderTableHeaderTooltip } from '@/pages-stage/stock/manage/device/config';

import type { ColumnProps } from 'ant-design-vue/lib/table/interface';

export interface IFilterItem {
  title: string;
  field: IFilterKey;
  type: 'input' | 'select' | 'map-select' | 'none';
  none: boolean;
}
export const filterConfig = [
  {
    title: '批次',
    field: 'batch',
    type: 'input',
    none: false,
  },
  {
    title: '设备号',
    field: 'device_id',
    type: 'input',
    none: false,
  },
  {
    title: 'tab栏',
    field: 'type',
    type: 'none',
    none: true,
  },
] as const;

export type IFilterKey = typeof filterConfig[number]['field'];
export type IFilter = {
  [key in IFilterKey]?: string | number;
};

export enum EFilterType {
  '待发布' = 1,
  '已发布',
}

export const dataTableColumns: ColumnProps[] = [
  {
    title: '设备信息',
    width: '210px',
    dataIndex: 'imei',
    slots: { customRender: 'text' },
  },
  {
    title: '型号',
    width: '150px',
    dataIndex: 'model_name',
    slots: { customRender: 'text' },
  },
  {
    title: '颜色',
    width: '100px',
    dataIndex: 'color',
    slots: { customRender: 'text' },
  },
  {
    title: '内存',
    width: '100px',
    dataIndex: 'memory',
    slots: { customRender: 'text' },
  },
  {
    title: '成新度',
    width: '100px',
    dataIndex: 'condition',
    slots: { customRender: 'text' },
  },
  {
    title: '版本',
    width: '100px',
    dataIndex: 'version',
    slots: { customRender: 'text' },
  },
  {
    title: '批次',
    width: '140px',
    dataIndex: 'batch_no',
    slots: { customRender: 'text' },
  },
  {
    title: '创建时间',
    width: '180px',
    dataIndex: 'created_at',
    slots: { customRender: 'text' },
  },
  {
    title: '设备状态',
    width: '120px',
    dataIndex: 'release_status',
    slots: { customRender: 'text' },
  },
  {
    title: renderTableHeaderTooltip('可销售库存', '可用于订单发货的仓库库存'),
    width: '150px',
    dataIndex: 'sell_stock',
    filterDropdownVisible: false,
    slots: { customRender: 'text' },
  },
  {
    title: renderTableHeaderTooltip('仓库库存', '真实在库的库存'),
    width: '150px',
    dataIndex: 'real_stock',
    filterDropdownVisible: false,
    slots: { customRender: 'text' },
  },
  {
    title: '操作',
    width: '120px',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
