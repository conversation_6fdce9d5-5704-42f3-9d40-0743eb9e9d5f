<template>
  <a-input
    v-model:value="modelValue"
    allow-clear
    :disabled="disabled"
    placeholder="请输入"
    v-bind="$attrs"
  >
    <template #addonAfter>
      <div
        style="cursor: pointer"
        @click="batchHandler"
      >
        批量
      </div>
    </template>
  </a-input>
  <a-modal
    v-model:visible="visible"
    :footer="disabled ? null : undefined"
    :title="`批量输入${label}`"
    width="450px"
    @ok="confirm"
  >
    <a-form>
      <a-form-item :label="label">
        <a-textarea
          v-model:value="content"
          :disabled="disabled"
          :placeholder="`多个${label}需要隔行或英文逗号分割`"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

defineOptions({ inheritAttrs: false });

const props = withDefaults(
  defineProps<{
    value?: string;
    label?: string;
    disabled?: boolean;
  }>(),
  {
    label: '',
    value: undefined,
    disabled: false,
  },
);

const emit = defineEmits<{
  (e: 'update:value', value?: string): void;
}>();

const modelValue = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit('update:value', value.replace(/[\s，,]+/g, ','));
  },
});

const visible = ref(false);
const content = ref('');

function batchHandler() {
  visible.value = true;
  content.value =
    modelValue.value
      ?.split(',')
      .map(item => item.trim())
      .filter(Boolean)
      .join('\n') || '';
}

function confirm() {
  modelValue.value = content.value
    .split('\n')
    .map(item => item.trim())
    .filter(Boolean)
    .join(',');
  visible.value = false;
}
</script>
