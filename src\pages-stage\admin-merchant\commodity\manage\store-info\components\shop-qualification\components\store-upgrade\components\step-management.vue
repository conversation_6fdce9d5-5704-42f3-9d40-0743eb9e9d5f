<template>
  <section class="step-management-container">
    <div class="alert">
      <span>您当前店铺类型为</span>
      <Tag
        ghost
        tag="平台型商家"
        type="primary"
      >
        <template #prefix>
          <i class="icon-platform" />
        </template>
      </Tag>
      <span>将升级为</span>
      <Tag
        ghost
        tag="码商型商家"
        type="primary"
      >
        <template #prefix>
          <i class="icon-mashang" />
        </template>
      </Tag>
      <span>请按照以下流程完成店铺升级，耗时约&nbsp;<span class="text-link">1~3</span>&nbsp;天</span>
    </div>

    <a-space :size="8">
      <SubmitStepCard
        :shared-data="sharedData"
        :update-shared-data="updateSharedData"
      />
      <FinishStepCard
        :shared-data="sharedData"
        :update-shared-data="updateSharedData"
      />
    </a-space>

    <div
      v-if="sharedData.audit_status === null"
      class="footer"
    >
      <a-button
        ghost
        size="large"
        type="primary"
        @click="handleCancelUpgrade"
      >
        取消升级
      </a-button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Modal } from 'ant-design-vue';

import { useSharedData } from '../composables/use-shared-data';
import FinishStepCard from './finish-step-card.vue';
import SubmitStepCard from './submit-step-card.vue';
import Tag from './tag.vue';

const { sharedData, updateSharedData } = useSharedData();

function handleCancelUpgrade() {
  Modal.confirm({
    title: '确认取消店铺升级',
    content: '取消后，已有流程将会结束，再次变更需要重新进行初始流程',
    okText: '取消升级',
    cancelText: '返回',
    onOk() {
      updateSharedData(null);
    },
  });
}
</script>

<style lang="less" scoped>
.step-management-container {
  height: calc(100vh - 200px);
}

.alert {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 24px;
  padding: 14px 16px;
  background: linear-gradient(90deg, #6b9bff1f 0%, #3471f30d 100%);
  border-radius: 4px;
}

.icon-platform {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/12/m/4pggoZ1ApudQVbPC0EFv.png') no-repeat 100% / cover;
}

.icon-mashang {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/12/m/l40bR6ysybfDBrhLVD6B.png') no-repeat 100% / cover;
}

.footer {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 16px;
  background-color: #fff;
  box-shadow: 8px 0 8px 0 rgba(0, 0, 0, 0.15);
}
</style>
