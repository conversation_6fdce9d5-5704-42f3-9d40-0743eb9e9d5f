<!-- 苹果订单取消弹窗 -->
<template>
  <a-modal
    v-model:visible="bindVisible"
    :body-style="{
      maxHeight: '600px',
      overflow: 'auto',
    }"
    class="agree-cancel-modal"
    :confirm-loading="submitLoading"
    destroy-on-close
    :ok-text="setpMap[confirmSetp]"
    :title="modalTitle"
    @cancel="cancelHandle"
    @ok="confirmHandle"
  >
    <a-spin :spinning="loading">
      <template v-if="confirmSetp === 1">
        <RForm
          ref="formRef"
          v-model:value="formState"
          :form-group="formGroup"
          :origin-props="{ layout: 'vertical' }"
        />
      </template>
      <template v-else>
        <LargeTicketTemplate
          ref="largeTicketTemplateRef"
          :order-id="orderId"
          :template-data="templateData"
          @settemplate-data="settemplateData"
        />
      </template>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import type { RFormInstance } from 'rrz-web-design';

import { useVModel } from '@/hook';
import type { TFieldItem, TTemplate } from '@/pages-stage/user-ticket-v2/components/large-ticket-template/data';
import LargeTicketTemplate from '@/pages-stage/user-ticket-v2/components/large-ticket-template/index.vue';

import { cateFormGroup } from './config';
import { createWorkOrder, getCateTempDetail, getSuperCateTempDetail, superCreateWorkOrder } from './service';

interface IExtraData {
  rental_has_pay: number | string;
  rental_has_refund: number | string;
  deposit_has_pay: number | string;
  deposit_has_refund: number | string;
  apply_cancel_reason: string;
  sub_cancel_reason_text: string;
}
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<Partial<IExtraData>>,
    default: () => ({}),
  },
});
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}>();

const route = useRoute();
const formGroup = cateFormGroup(route);
const formRef = ref<RFormInstance | null>(null);
const bindVisible = useVModel(props, 'visible', emit);
const submitLoading = ref(false);
const loading = ref(false);
const setpMap = {
  1: '下一步',
  2: '提交',
};
const modalTitle = ref('提交工单');
const confirmSetp = ref(1);
const formState = reactive<{
  cateSelect: string[];
}>({
  cateSelect: [],
});

const templateData = ref<TTemplate>({
  common_data: [],
  operate_data: [],
  link_data: [],
});

const isSuper = useRoute().query.role === 'super';

const largeTicketTemplateRef = ref<any>();

function settemplateData(info: TFieldItem[]) {
  templateData.value.common_data = info;
}

async function confirmHandle() {
  if (confirmSetp.value === 1) {
    const formInstance = formRef.value?.getFormRef();
    await formInstance?.validate();
    const length = formState.cateSelect.length;
    const tempCate = formState.cateSelect[length - 1]?.split('-');
    loading.value = true;
    const { data } = await (isSuper ? getSuperCateTempDetail : getCateTempDetail)({ id: tempCate[1] }).finally(() => {
      loading.value = false;
    });
    templateData.value = data.json_data;
    modalTitle.value = tempCate[2];
    confirmSetp.value = 2;
  } else {
    await largeTicketTemplateRef.value?.setData();
    submit();
  }
}

function cancelHandle() {
  modalTitle.value = '提交工单';
  confirmSetp.value = 1;
  formState.cateSelect = [];
  templateData.value = {
    common_data: [],
    operate_data: [],
    link_data: [],
  };
  bindVisible.value = false;
}

async function submit() {
  submitLoading.value = true;
  const length = formState.cateSelect.length;
  const tempCate = formState.cateSelect[length - 1]?.split('-');
  const params = {
    order_id: props.orderId,
    new_type_id: tempCate[0],
    template_id: tempCate[1],
    json_data: JSON.stringify(templateData.value),
  };
  await (isSuper ? superCreateWorkOrder : createWorkOrder)(params).finally(() => {
    submitLoading.value = false;
  });
  cancelHandle();
  emit('success');
}
</script>

<style lang="less" scoped>
.setp-content {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .setp-content-item {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .title {
      display: flex;
      gap: 8px;
      align-items: center;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }

    .title::before {
      width: 4px;
      height: 16px;
      background: #3777ff;
      border-radius: 2px 2px 2px 2px;
      content: '';
    }
  }
}
</style>
