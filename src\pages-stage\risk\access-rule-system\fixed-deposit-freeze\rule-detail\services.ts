import { GET, POST } from '@/services/api';

export function apiGetRuleDetailData(params: { rule_id: string }) {
  return GET('/super/fixed-deposit-config/get-pre-rule-data', params);
}

export function apiSaveRuleDetailData(params: { type: 'preview' | 'formal'; rule_id: string; rule_preview?: string }) {
  return POST('/super/fixed-deposit-config/save-rule-data', params);
}

export function apiResetRuleDetailData(params: { rule_id: string }) {
  return POST('/super/fixed-deposit-config/resetting', params);
}
