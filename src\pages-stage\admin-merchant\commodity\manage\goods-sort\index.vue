<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px 24px' }"
    :navs="['店铺管理', '商品调序']"
    title="商品调序"
  >
    <template #title-suffix>
      <div class="suffix">
        <span>可拖动排序，所有修改需要保存才生效</span>

        <a-tooltip>
          <template #title>
            商家可调整小程序店铺内，导航条的类目顺序
          </template>
          <question-circle-outlined style="margin-left: 4px" />
        </a-tooltip>
      </div>
    </template>
    <template #extra>
      <div class="extra">
        <span>上次修改时间： {{ updatedAt }}</span>
        <a-button
          :loading="saveLoading"
          style="margin: 0 10px 0 8px"
          type="primary"
          @click="onSave"
        >
          <template #icon>
            <SaveOutlined />
          </template>
          保存修改
        </a-button>
      </div>
    </template>
    <a-alert
      message="商家可调整小程序店铺内导航条的类目顺序。"
      show-icon
      type="info"
    />
    <div class="list mt-24">
      <a-spin :spinning="spinning">
        <div class="mt-6">
          <draggable
            animation="300"
            chosen-class="chosenClass"
            ghost-class="ghost"
            item-key="id"
            :list="categoryList"
          >
            <template #item="{ element }">
              <div class="item">
                <a-space :size="4">
                  <DragOutlined style="font-size: 16px" />
                  <div class="text">
                    {{ element.name }}
                  </div>
                </a-space>
                <a-space :size="8">
                  <edit-sort-drawer
                    :id="element.id"
                    :code="element.code"
                  />
                </a-space>
              </div>
            </template>
          </draggable>
        </div>
      </a-spin>
    </div>
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { QuestionCircleOutlined, SaveOutlined, DragOutlined } from '@ant-design/icons-vue';
import { ref, onMounted } from 'vue';
import { getCategory, saveCategory } from './services';
import { message } from 'ant-design-vue';
import EditSortDrawer from './components/edit-sort-drawer.vue';
import draggable from 'vuedraggable';
import { CategoryItem } from './data';
const spinning = ref(false);
const updatedAt = ref('');
const categoryList = ref<CategoryItem[]>([]);
// 获取分类数据
const getCategoryData = async () => {
  spinning.value = true;
  const res = await getCategory();
  categoryList.value = res.data.list;
  updatedAt.value = res.data.updated_at;
  spinning.value = false;
};

onMounted(() => {
  getCategoryData();
});

// 保存
const saveLoading = ref(false);
const onSave = async () => {
  saveLoading.value = true;
  try {
    await saveCategory({
      category_data: JSON.stringify(categoryList.value),
    });
    message.success('保存成功');
    saveLoading.value = false;
  } catch (error) {
    saveLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.list {
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 -6px 12px;
    padding: 16px 24px;
    color: rgba(6, 21, 51, 0.65);
    background: #f5f7fa;
    border-radius: 4px;
    cursor: move;
    .text {
      font-weight: 400;
      font-size: 14px;
    }
  }
}
.mt-24 {
  margin-top: 24px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-6 {
  margin-top: 6px;
}

.suffix {
  display: flex;
  align-items: center;
  margin-left: 12px;
  padding-top: 6px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.extra {
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

:deep(.ant-alert-message) {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
}
</style>
