<template>
  <div class="sku-tag-wrapper">
    <span :class="{ 'grey-color': !item.recycle_num }">
      {{ item.recycle_num ? `${item.recycle_num}台` : '暂无数据' }}
    </span>
    <span class="sku-tag-wrapper__item">{{ item.sku }}</span>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { IGoodsSkuInfo } from '../../data.d';

defineProps({
  item: {
    type: Object as PropType<IGoodsSkuInfo>,
    default: () => ({}),
  },
});
</script>

<style lang="less" setup>
.sku-tag-wrapper {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  &__item {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    padding: 0 8px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    background: #fff;
    border: 1px solid rgba(6, 21, 51, 0.15);
    border-radius: 4px;
  }
}
.grey-color {
  color: rgba(6, 21, 51, 0.25);
}
</style>
