import { ref } from 'vue';
import { IFormSearch, IFormAdd } from './data';
//表单
export const formState = ref<IFormSearch>({
  business_type: null,
});

//添加联系人表单
export const addFormState = ref<IFormAdd>({
  name: '',
  contact: '',
  code: '',
});

export const columns = [
  {
    title: '消息类型',
    dataIndex: 'business_type',
    key: 'business_type',
    width: 200,
  },
  {
    title: '消息类别',
    dataIndex: 'message_type',
    key: 'message_type',
    width: 200,
  },
  {
    title: '消息标题',
    dataIndex: 'title',
    width: 200,
  },
  {
    title: '消息描述',
    dataIndex: 'content',
    width: 320,
  },
  {
    title: '短信',
    dataIndex: 'sms_status',
    key: 'sms_status',
    width: 112,
  },
  {
    title: '微信公众号',
    dataIndex: 'weixin_status',
    key: 'weixin_status',
    width: 112,
  },
];

export const smsColumns = [
  {
    title: '手机号码',
    dataIndex: 'contact',
    key: 'contact',
    width: 110,
  },
  {
    title: '用户昵称',
    dataIndex: 'name',
    key: 'name',
    width: 110,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 50,
  },
];

export const weixinColumns = [
  {
    title: '微信账号',
    dataIndex: 'contact',
    key: 'contact',
    width: 110,
  },
  {
    title: '微信昵称',
    dataIndex: 'name',
    key: 'name',
    width: 110,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 50,
  },
];
