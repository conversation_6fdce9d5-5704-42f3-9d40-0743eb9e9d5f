import { createGlobalState } from '@/hook';

/** 设置懒加载数据重置函数，在订单列表中的分组数据需要刷新时，调用此函数中的重置 */
export const resetLazyLoadData = createGlobalState(() => {
  const resetFnList = new Map<string, () => void>();

  function setResetFn(key: string, fn: () => void) {
    resetFnList.set(key, fn);
  }

  function deleteResetFn(key: string) {
    resetFnList.delete(key);
  }

  function reset() {
    resetFnList.forEach(fn => fn());
  }

  return {
    setResetFn,
    deleteResetFn,
    reset,
  };
});
