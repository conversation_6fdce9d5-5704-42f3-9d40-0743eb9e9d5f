<template>
  <div class="relative-box">
    <RelationChoose v-model:value="rulesInfo.relation" />
    <div class="rules-list-box right-box">
      <div
        v-for="(item, index) in rulesInfo.rules"
        :key="item._id"
        class="rules-list"
      >
        <div
          v-if="isLogic(item)"
          class="relative-box"
        >
          <RelationChoose v-model:value="item.relation" />
          <div
            v-for="(child, childIndex) in item.rules"
            :key="childIndex"
            class="rules-item right-box"
          >
            <template v-if="isLogic(child) && child.rules.length > 0">
              <RelationContent
                v-model:dataInfo="item.rules[childIndex]"
                :level="level"
                @add-child-rules="addChildRules"
                @add-rules="addRules"
                @del-rules="delRules"
              >
                <template #rulesItemLevel="{ rulesItemInfo }">
                  <slot
                    name="rulesItemLevel"
                    :rules-item-info="rulesItemInfo"
                  />
                </template>
              </RelationContent>
            </template>
            <template v-else>
              <slot
                name="rulesItemLevel"
                :rules-item-info="child"
              />
              <RulesHandle
                :current-item-info="child"
                :is-last="getLastCondition(childIndex, item.rules)"
                :level="level"
                @add="addRules(item)"
                @add-child-rules="addChildRules(child)"
                @del="delRules(item, childIndex)"
              />
            </template>
          </div>
        </div>
        <div
          v-else
          class="rules-item"
        >
          <slot
            name="rulesItemLevel"
            :rules-item-info="item"
          />
          <RulesHandle
            v-if="!isLogic(item)"
            :current-item-info="item"
            :is-last="getLastCondition(index, rulesInfo.rules)"
            :level="level"
            @add="addRules(rulesInfo)"
            @add-child-rules="addChildRules(item)"
            @del="delRules(rulesInfo, index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { TRulesInfo } from '../data.d';
import RelationChoose from './relation-choose.vue';
import RulesHandle from './rules-handle.vue';
const props = defineProps<{
  dataInfo: TRulesInfo;
  level: number;
}>();

const emits = defineEmits(['update:dataInfo', 'delRules', 'addRules', 'addChildRules']);

const rulesInfo = computed({
  get: () => {
    return props.dataInfo;
  },
  set: val => {
    emits('update:dataInfo', val);
  },
});

function addRules(item: TRulesInfo) {
  emits('addRules', item);
}

function addChildRules(item: TRulesInfo) {
  emits('addChildRules', item);
}

function delRules(item: TRulesInfo, index: number) {
  emits('delRules', item, index);
}

function getLastCondition(index: number, rules: TRulesInfo[]) {
  let flag = index === rules.length - 1;
  if (!flag && isLogic(rules[rules.length - 1])) {
    flag = index === rules.length - 2;
  }
  return flag;
}

function isLogic(val: TRulesInfo) {
  return val.relationship === 'logic';
}
</script>

<style lang="less" scoped>
.relative-box {
  position: relative;
  width: 100%;
}
.rules-list-box {
  flex: 1;
}
.rules-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.right-box {
  margin-left: 32px;
}
</style>
