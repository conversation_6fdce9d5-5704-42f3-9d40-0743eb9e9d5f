import { POST, GET } from '@/services/api';
export function getListApi(path: string, params: any) {
  return GET(path, params);
}
//批次下拉框
export function getBatchSelect() {
  return GET('/super/quality-spu-batch-pay/batch-select');
}
//设置价格
export function postSetPrice(params: any) {
  return POST('/super/quality-spu-batch-pay/set-price', params);
}
//付款二维码
export function getPayQrcode(params: any) {
  return POST('/quality-spu-batch-pay/do-pay', params);
}
//获取付款状态
export function getPayStatus(params: any) {
  return GET('/quality-spu-batch-pay/get-pay-status', params);
}
