import { GET, POST } from '@/services/api';
const hostType = 'Golang';
//创建商品资质
export function createCredentialGoods(data: any) {
  return POST('/server/ServerCredentialGoods/create', data, { hostType });
}
//修改商品资质
export function updateCredentialGoods(data: any) {
  return POST('/server/ServerCredentialGoods/update', data, { hostType });
}
//获取商品资质详情
export function getGoodsInfoDetailApi(params: any) {
  return GET('/server/ServerCredentialGoods', params, { hostType });
}
