<template>
  <a-form
    ref="formRef"
    class="form-box"
    :model="dataSource"
  >
    <a-table
      :columns="computedColumns"
      :data-source="dataSource"
      :pagination="false"
      :scroll="{ x: tableScrollXWidth }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'stock_text'">
          <span :class="['stock-block', stockColorMap[record['stock_status']] || '']">
            {{ record[column.dataIndex] }}
          </span>
        </template>
        <template v-if="column.dataIndex === 'now_stock_text'">
          <span
            v-if="record.hasOwnProperty(column.dataIndex)"
            :class="['stock-block', stockColorMap[record['now_stock_status']] || '']"
          >
            {{ record[column.dataIndex] }}
          </span>
        </template>
        <template v-else-if="column.dataIndex === 'goods_id'">
          {{ goodsId }}
        </template>
        <template v-else-if="column.dataIndex === 'symbol'">
          =
        </template>
        <!-- 下拉选择配置项进行整合处理 -->
        <template v-if="keyToSelectConfigMap[column.dataIndex]">
          <a-form-item
            :name="column.dataIndex"
            :rules="[{ required: true, message: '请完善表单', validator: _rule => formValidator(_rule, index) }]"
          >
            <a-select
              v-model:value="dataSource[index][column.dataIndex]"
              :disabled="!isEdit || disabledHandleKeys.includes(column.dataIndex) || index !== 0 || isUseTactics"
              :options="computedOptions(index, keyToSelectConfigMap[column.dataIndex].optionsKey, column.dataIndex)"
              placeholder="请选择"
              :style="{ ...keyToSelectConfigMap[column.dataIndex].style }"
              @blur="onBlur"
              @change="
                value =>
                  onChange({
                    index,
                    key: column.dataIndex,
                    val: value,
                  })
              "
            />
          </a-form-item>
        </template>
        <template v-if="column.dataIndex === 'sku_base_price'">
          <a-form-item
            :name="column.dataIndex"
            :rules="[{ required: true, message: '请完善表单', validator: _rule => formValidator(_rule, index) }]"
          >
            <a-input-number
              v-show="!isUseTactics"
              v-model:value="dataSource[index][column.dataIndex]"
              :controls="false"
              :disabled="!isEdit || index !== 0"
              :max="99999"
              :min="0"
              placeholder="请输入"
              :precision="0"
              style="width: 130px"
              @blur="onBlur"
              @change="
                value =>
                  onChange({
                    index,
                    key: column.dataIndex,
                    val: value,
                  })
              "
            />
            <span v-show="isUseTactics">{{ getTactics(record?.min_evaluate_price, record?.max_evaluate_price) }}</span>
          </a-form-item>
        </template>
      </template>
    </a-table>
  </a-form>
</template>

<script lang="ts" setup>
import { ref, watch, computed, inject, PropType, Ref } from 'vue';
import { message } from 'ant-design-vue';
import { keyToSelectConfigMap } from '../../config';
import { IComputedTableDataSource, ITableSelectManageMap } from '../../data.d';
import useBaseTable from '../../composables/use-base-table';

const isEdit = inject('isEdit') as Ref<boolean>;
const isNowVersion = inject('isNowVersion') as Ref<boolean>;

const props = defineProps({
  goodsId: {
    type: String,
    default: '',
  },
  optionsManage: {
    type: Object as PropType<ITableSelectManageMap>,
    default: () => {
      return {
        base1_list: [],
        base2_list: [],
        price_type: [],
        change_price_type: [],
        arithmetic_list: [],
      };
    },
  },
  tableData: {
    type: Array as PropType<IComputedTableDataSource[]>,
    default: () => [],
  },
  // 是否不使用手动调价
  isUseTactics: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:dataSource', 'validatorTable']);

// 需要默认禁用控件的字段
const disabledHandleKeys = ['base1', 'base2', 'price_type', 'change_price_type'];

const formRef = ref<any>(null);

const { stockColorMap, dataSource, computedColumns, computedOptions, filterFullData } = useBaseTable(
  props,
  isNowVersion,
  isEdit,
);

// 表格x滚动的视口宽度
const tableScrollXWidth = computed(() => {
  return isEdit.value ? 1449 : isNowVersion.value ? 1584 : 1449;
});

async function formValidator(_rule: any, index: number) {
  const { field } = _rule;
  // 1、不为第一条时，无需提示必填，因为他们已经是禁用状态了
  // 2、只对【关系、数值】这2列进行数据校验
  if (index !== 0 || !['arithmetic_type', 'sku_base_price'].includes(field)) return Promise.resolve();
  const item: IComputedTableDataSource = dataSource.value[0];
  // 数值需要判断是否采取二分法方案，才进行校验
  if (field === 'sku_base_price' && props.isUseTactics) {
    return Promise.resolve();
  } else {
    return !item.hasOwnProperty(field) || [null, ''].includes(item[field])
      ? Promise.reject(_rule.message)
      : Promise.resolve();
  }
}

// 第一项的【关系、数值】调整后，下面的数据均跟着同步；
function onChange(params: { index: number; key: string; val: any }) {
  const { index, key, val } = params;
  if (index !== 0) return;
  dataSource.value.forEach((curItem: IComputedTableDataSource, curIndex: number) => {
    if (curIndex !== 0) {
      curItem[key] = val;
    }
  });
  updateModal();
}

function updateModal() {
  emits('update:dataSource', dataSource.value);
}

function onBlur() {
  emits('validatorTable', filterFullData());
}

function tableValidator() {
  return formRef.value
    ?.validate()
    .then(() => {
      return Promise.resolve();
    })
    .catch(() => {
      message.error('请完善表单');
      return Promise.reject();
    });
}

const getTactics = computed(() => (min_evaluate_price: string | number, max_evaluate_price: string | number) => {
  return min_evaluate_price !== undefined && max_evaluate_price !== undefined
    ? `${min_evaluate_price}-${max_evaluate_price}`
    : '';
});

watch(
  () => props.tableData,
  val => {
    dataSource.value = val || [];
  },
  { immediate: true },
);

defineExpose({
  validator: tableValidator,
});
</script>

<style lang="less" scoped>
.form-box {
  :deep(.ant-form-item) {
    margin-bottom: 0;
  }
}
.stock-block {
  position: relative;
  padding-left: 16px;
  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #00c8be;
    border-radius: 50%;
    transform: translateY(-50%);
    content: '';
  }
  &.grey {
    &::before {
      background-color: rgba(6, 21, 51, 0.25);
    }
  }
  &.danger {
    &::before {
      background-color: #ff4a57;
    }
  }
}
</style>
