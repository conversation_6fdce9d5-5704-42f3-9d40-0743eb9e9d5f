<template>
  <a-modal
    :footer="null"
    title="修改记录"
    :visible="visible"
    :width="800"
    @cancel="close"
  >
    <a-table
      class="bottom-fix-table"
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="false"
    />
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useModal } from '@/hook/component/use-modal';
import { getModifyLog } from '../service';

const dataSource = ref<{ content: string; created_by: string; created_at: string }[]>([]);

const { visible, loading, open, close } = useModal(
  () => {
    return;
  },
  {
    afterClose: () => {
      dataSource.value = [];
    },
  },
);

const onShow = async (id: number) => {
  open();
  loading.value = true;
  const params = {
    coupon_market_id: id,
  };
  try {
    const res = await getModifyLog(params);
    const data = res.data || {};
    const list = data.list || [];
    dataSource.value = list;
  } finally {
    loading.value = false;
  }
};

const columns = [
  {
    title: '操作内容',
    dataIndex: 'content',
    width: 432,
  },
  {
    title: '操作人',
    dataIndex: 'created_by',
    width: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'created_at',
    width: 220,
  },
];

defineExpose({
  onShow,
});
</script>
