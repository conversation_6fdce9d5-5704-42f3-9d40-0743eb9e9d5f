export interface IFeedbackCollectionList {
  id: string;
  phone: string;
  content: string;
  created_at: string;
}
export interface IFeedBackSearch {
  terminal: string;
  time: string[];
  created_at_gt: string;
  created_at_lt: string;
}

export interface IModalState {
  id: string;
  user_id: string;
  phone: string;
  images: string[];
  content: string;
  created_at: string;
  username: string;
}

interface IDispatchModal<T> {
  action: T;
  params?: {
    id?: string;
    [key: string]: any;
  };
}
