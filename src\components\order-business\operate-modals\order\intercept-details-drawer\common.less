.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 12px;

  .right-record {
    color: #3777ff;
    cursor: pointer;
  }
}

.title {
  position: relative;
  padding-left: 12px;
  font-weight: 600;
  font-size: 14px;

  &::before {
    position: absolute;
    top: 4px;
    left: 0;
    width: 4px;
    height: 14px;
    background-color: #3777ff;
    border-radius: 2px;
    content: '';
  }
}

.info {
  padding-bottom: 16px;
  font-weight: 400;

  div {
    display: inline-block;
  }

  .label {
    width: 68px;
    padding-right: 4px;
    color: rgba(6, 21, 51, 0.45);
    text-align: right;
  }

  .value {
    color: rgba(6, 21, 51, 0.85);
  }

  &:last-child {
    padding-bottom: 0;
  }
}

.images-wrap {
  display: flex !important;
  flex: 1;
  flex-wrap: wrap;

  .cover-image {
    margin-bottom: 8px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px 4px 4px 4px;

    &:nth-child(3n - 1) {
      margin-right: 8px;
      margin-left: 8px;
    }
  }
}
