<template>
  <a-drawer
    v-model:visible="visible"
    :body-style="{ paddingBottom: '80px' }"
    :closable="false"
    placement="right"
    width="480"
  >
    <template #title>
      <div style="display: flex; align-items: center">
        <span>我的支付宝</span>
        <i
          class="icon-close"
          @click="onClose"
        />
      </div>
    </template>
    <div class="bank-list">
      <a-spin :spinning="spinning">
        <div
          v-for="(item, index) in bankList"
          :key="index"
          class="item"
        >
          <i
            class="del-icon-bank"
            @click="delBank(item.id as string)"
          />
          <span class="time-box"> {{ item.created_at }} </span>
          <div class="item-content">
            <div class="titel">
              支付宝户名
            </div>
            <div class="label">
              {{ item.alipay_payuser }}
            </div>
          </div>
          <div class="item-content">
            <div class="titel">
              支付宝账号
            </div>
            <div class="label">
              {{ item.alipay_account }}
            </div>
          </div>
        </div>

        <div
          v-if="bankList.length < 5"
          class="add-item"
          @click="handleAddBankCard"
        >
          <div class="icon" />
          <div class="text">
            添加支付宝
          </div>
        </div>
      </a-spin>
    </div>

    <div :style="footerStyle">
      <a-button
        style="margin-right: 8px"
        @click="onClose"
      >
        取 消
      </a-button>
      <a-button
        type="primary"
        @click="onClose"
      >
        确 定
      </a-button>
    </div>
    <AddAlipayModal
      v-model:visible="addBankModalVisible"
      @ok="getBankData"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message, Modal } from 'ant-design-vue';

import { useVModel } from '@/hook';

import type { IBankList, IFooterStyle } from '../../data';
import { deleteAlipayAccount, getAlipayAccountList } from '../../services';
import AddAlipayModal from '../add-alipay-modal/index.vue';

const emits = defineEmits(['update:visible']);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const footerStyle: IFooterStyle = {
  position: 'absolute',
  right: 0,
  bottom: 0,
  width: '100%',
  borderTop: '1px solid #e9e9e9',
  padding: '10px 16px',
  background: '#fff',
  textAlign: 'right',
  zIndex: 1,
};

const visible = useVModel(props, 'visible', emits);

function onClose() {
  visible.value = false;
  bankList.value = [];
}

const addBankModalVisible = ref(false);
function handleAddBankCard() {
  addBankModalVisible.value = true;
}

const spinning = ref(false);
const bankList = ref<IBankList[]>([]);
async function getBankData() {
  spinning.value = true;
  const res = await getAlipayAccountList().finally(() => {
    spinning.value = false;
  });
  bankList.value = res.data;
}

async function delBank(id: number) {
  Modal.confirm({
    title: '请确认是否删除该支付宝账号？',
    onOk: async () => {
      await deleteAlipayAccount(id);
      message.success('操作成功！');
      getBankData();
    },
  });
}

defineExpose({ getBankData });
</script>

<style scoped lang="less">
.icon-close {
  width: 16px;
  height: 16px;
  margin-left: auto;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2312/04/m/GLo64CqXUDrn4UooaphM.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

.bank-list {
  display: flex;
  flex-direction: column;
}

.item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 432px;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2312/04/m/uwPiwo8mR0D7M14fqpQH.png') no-repeat;
  background-size: 100% 100%;

  .del-icon-bank {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 16px;
    height: 16px;
    background: url('https://img1.rrzuji.cn/uploads/scheme/2312/04/m/1fBlGL3CF2YtgGtlFq52.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }

  .time-box {
    display: flex;
    align-items: center;
    height: 22px;
    margin-bottom: 16px;
    // width: 142px;
    padding: 0 8px;
    color: #3777ff;
    font-weight: 400;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
  }

  .item-content {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    line-height: 22px;

    .titel {
      margin-right: 12px;
      color: rgba(255, 255, 255, 0.65);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }

    .label {
      color: #fff;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }
}

.add-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 432px;
  height: 190px;
  border: 1px dotted #b3d2ff;
  border-radius: 7px;
  cursor: pointer;

  .icon {
    width: 32px;
    height: 32px;
    margin-bottom: 16px;
    background: url('https://img1.rrzuji.cn/uploads/scheme/2312/04/m/AC2DtrgAbwDQuLMrkK6g.png') no-repeat;
    background-size: 100% 100%;
  }

  .text {
    color: #3777ff;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
