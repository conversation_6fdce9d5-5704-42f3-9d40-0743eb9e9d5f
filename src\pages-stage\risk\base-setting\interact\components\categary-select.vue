<template>
  <a-modal
    v-model:visible="addModalVisible"
    :confirm-loading="addModalLoading"
    :title="options.type === MODEL ? '新增型号规则' : '新增类目规则'"
    @cancel="addItemModalClose"
    @ok="addItemHandle"
  >
    <a-tree-select
      v-model:value="addSelectValue"
      allow-clear
      dropdown-class-name="category-select-content"
      :dropdown-style="{ height: '300px' }"
      placeholder="请选择"
      :show-checked-strategy="SHOW_PARENT"
      style="width: 100%"
      tree-checkable
      :tree-data="addSelectOptions"
      tree-node-filter-prop="title"
    />
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, Ref, PropType, watch } from 'vue';
import { TreeSelect, message } from 'ant-design-vue';
import { getCategoryList, addRuleItem, ruleItemFetch } from '../service';
import { selectType } from '../config';

interface stateData {
  addModalVisible: boolean;
  addModalLoading: boolean;
  addSelectValue: string[];
  addSelectOptions: any[];
}
interface propsOption {
  visible: boolean;
  type: selectType;
  addItemId: string;
  tags: string[];
  config: any;
}
interface componentSetupData {
  MODEL: selectType;
  SHOW_PARENT: string;
  addModalVisible: Ref<boolean>;
  addModalLoading: Ref<boolean>;
  addSelectValue: Ref<string[]>;
  addSelectOptions: Ref<any[]>;
  addItemModalClose: () => void;
  addItemHandle: () => void;
}

const SHOW_PARENT = TreeSelect.SHOW_PARENT;
const MODEL = selectType.MODEL;

function formatTreeData(data: Array<any>, prefix: string, first?: boolean): any[] {
  const result: any[] = [];
  data.forEach((item: any, index: number) => {
    const copyItem: any = {
      title: item.name,
      value: first ? `f${index}` : prefix + item.id,
      key: first ? `f${index}` : prefix + item.id,
      disableCheckbox: first,
      children: [],
      searchKey: '',
    };
    copyItem.searchKey = item.name;
    if (item.child && item.child.length) {
      copyItem.children = formatTreeData(item.child, first ? '' : copyItem.value + '-', false);
    }
    // 有一些奇奇怪怪的测试数据 id 都等于 0, 过滤一下
    if (item.id !== '0') {
      result.push(copyItem);
    }
  });
  return result;
}

function formatDelNoChildren(data: Array<any>, currentLevel: number, targetLevel: number): any[] {
  const result: any[] = [];
  data.forEach((item: any) => {
    item.disableCheckbox = true;
    if (item.children && item.children.length && currentLevel < targetLevel) {
      // 有children且没有触底
      item.children = formatDelNoChildren(item.children, currentLevel + 1, targetLevel);
    }
    if (item.children.length) {
      result.push({ ...item });
    }
  });
  return result;
}

export default defineComponent({
  props: {
    options: {
      type: Object as PropType<propsOption>,
      default: () => {
        return {
          visible: false,
          type: selectType.CATEGORY,
          addItemId: '',
        };
      },
    },
  },
  emits: ['success'],
  setup(props, { emit }): componentSetupData {
    const state = reactive<stateData>({
      addModalVisible: false,
      addModalLoading: false,
      addSelectValue: [],
      addSelectOptions: [],
    });

    function addItemModalClose() {
      emit('success', { type: 'close' });
    }
    function addItemHandle() {
      if (!state.addSelectValue.length) {
        message.warning('请选择分类！');
        return;
      }
      const { type, addItemId } = props.options;
      const params: any = {};
      params.version = '0';
      if (type === selectType.MODEL) {
        const modelIds: any[] = [];
        state.addSelectValue.forEach((item: any) => {
          const itemIds: any[] = item.split('-');
          if (itemIds.length === 3) {
            modelIds.push(itemIds[2]);
          }
        });
        if (!modelIds.length) {
          message.warning('请选择型号！');
          return;
        }
        params.model_ids = [...modelIds];

        // console.log(modelIds, addItemId);
      } else {
        params.category_ids = [...state.addSelectValue];
      }
      state.addModalLoading = true;
      let addHandle: any = null;
      if (type == '2') {
        // 添加类目
        addHandle = addRuleItem;
      } else if (type == '3') {
        // 添加型号
        addHandle = addRuleItem;
      }
      // addItemId
      if (addItemId && type == '3') {
        // 新增规则类目或型号标签
        addHandle = ruleItemFetch;

        params.rule_id = addItemId;
      } else if (addItemId && type == '2') {
        addHandle = ruleItemFetch;
        params.rule_id = addItemId;
        // console.log(params);
      }
      state.addModalLoading = false;

      addHandle(params)
        .then(() => {
          emit('success', { type: 'refresh' });
          state.addModalVisible = false;
        })
        .finally(() => {
          state.addModalLoading = false;
        });
    }

    async function getSelectOptions() {
      const res = await getCategoryList();
      let categorys = formatTreeData(res.data, '', true);
      if (props.options.type === selectType.MODEL) {
        // 过滤出四级类目
        categorys = formatDelNoChildren(categorys, 0, 2);
      } else {
        // 过滤出二级类目
        categorys = categorys.map((item: any) => {
          const copyItem = { ...item };
          copyItem.children = copyItem.children.map((child: any) => ({ ...child, children: [] }));
          return copyItem;
        });
      }
      state.addSelectOptions = categorys;
    }
    watch(
      () => props.options.visible,
      val => {
        state.addModalVisible = val;
        if (val) {
          state.addSelectValue = [];
          getSelectOptions();
        }
      },
    );

    return {
      MODEL,
      SHOW_PARENT,
      ...toRefs(state),
      addItemModalClose,
      addItemHandle,
    };
  },
});
</script>
