export const columns = [
  {
    title: '收款账户',
    key: 'pay_account',
    dataIndex: 'pay_account',
    width: 160,
  },
  {
    title: '收款方式',
    key: 'pay_type',
    dataIndex: 'pay_type',
    width: 160,
  },
  {
    title: '收款金额',
    key: 'amount',
    dataIndex: 'amount',
    width: 160,
  },

  {
    title: '交易号/票据号',
    key: 'payment_flow_sn',
    dataIndex: 'payment_flow_sn',
    width: 280,
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
    width: 280,
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 60,
    fixed: 'right',
  },
];

export const sourceColumns = [
  {
    title: '源单日期',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 120,
    fixed: 'left',
  },
  {
    title: '源单类型',
    key: 'source_order_type_text',
    dataIndex: 'source_order_type_text',
    width: 120,
  },
  {
    title: '源单编号',
    key: 'source_order_id',
    dataIndex: 'source_order_id',
    width: 180,
  },
  {
    title: '源单结算日期',
    key: 'pay_created_at',
    dataIndex: 'pay_created_at',
    width: 120,
  },
  {
    title: '源单币别',
    key: 'coin_type_text',
    dataIndex: 'coin_type_text',
    width: 88,
  },
  {
    title: '源单金额',
    key: 'total_price',
    dataIndex: 'total_price',
    width: 120,
  },

  {
    title: '源单已核销金额',
    key: 'finish_pay_price',
    dataIndex: 'finish_pay_price',
    width: 130,
    fixed: 'right',
  },
  {
    title: '源单未核销金额',
    key: 'wait_pay_price',
    dataIndex: 'wait_pay_price',
    width: 130,
    fixed: 'right',
  },
  {
    title: '本次核销金额',
    key: 'pay_price',
    dataIndex: 'pay_price',
    width: 130,
    fixed: 'right',
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 60,
    fixed: 'right',
  },
];
export const sourceSelectColumns = [
  {
    title: '单据日期',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 120,
    fixed: 'left',
  },
  {
    title: '单据编号',
    key: 'source_order_id',
    dataIndex: 'source_order_id',
    width: 200,
    fixed: 'left',
  },
  {
    title: '客户',
    key: 'server_name',
    dataIndex: 'server_name',
    width: 120,
  },
  {
    title: '收款状态',
    key: 'order_pay_status_text',
    dataIndex: 'order_pay_status_text',
    width: 120,
  },
  {
    title: '币别',
    key: 'coin_type_text',
    dataIndex: 'coin_type_text',
    width: 88,
  },
  {
    title: '本次应收账款',
    key: 'total_price',
    dataIndex: 'total_price',
    width: 130,
  },

  {
    title: '已收账款',
    key: 'finish_pay_price',
    dataIndex: 'finish_pay_price',
    width: 130,
  },
  {
    title: '未收账款',
    key: 'wait_pay_price',
    dataIndex: 'wait_pay_price',
    width: 130,
  },
];
