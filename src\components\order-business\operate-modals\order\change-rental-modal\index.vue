<template>
  <a-modal
    v-model:visible="bindVisible"
    title="修改租金"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="总租金"
          name="rentalMoney"
        >
          <a-input-number
            v-model:value="formData.rentalMoney"
            :precision="2"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item
          label="需付租金"
          name="rentalNeedPay"
        >
          <a-input-number
            v-model:value="formData.rentalNeedPay"
            :precision="2"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { PropType, ref, watch } from 'vue';
import { FormInstance, message } from 'ant-design-vue';
import { useVModel } from '@/hook';
import { fetchGateway } from '@/components/order-business/basic-modals/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

interface IExtraData {
  rental_money?: string | number;
  rental_need_pay?: string | number;
}

interface IFormData {
  rentalMoney?: number;
  rentalNeedPay?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const formData = ref<IFormData>({});
const bindVisible = useVModel(props, 'visible', emit);

const rules = {
  rentalMoney: [{ required: true, message: '总租金不能为空', trigger: 'blur' }],
  rentalNeedPay: [{ required: true, message: '需付租金不能为空', trigger: 'blur' }],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      const { rental_need_pay, rental_money } = props.extraData;
      // 初始化表单
      formData.value = {
        rentalMoney: Number(rental_money) || 0,
        rentalNeedPay: Number(rental_need_pay) || 0,
      };
    }
  },
  { immediate: true },
);

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      rentalMoney: formData.value.rentalMoney,
      rentalNeedPay: formData.value.rentalNeedPay,
      orderId: props.orderId,
      method: 'v2.order.change.rental.data',
    };
    fetchGateway(params)
      .then(() => {
        message.success('操作成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data', 'all_remark']);
      })
      .finally(() => (loading.value = false));
  });
}
</script>
