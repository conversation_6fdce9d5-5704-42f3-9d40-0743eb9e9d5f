import { GET, POST } from '@/services/api';

/**
 * 获取订单配置分组
 */
export function getOrderGroupList(params: { page_no: string; page_size: string }) {
  return GET('/order/V2OrderServerGroup/query', params, { hostType: 'Golang' });
}

/**
 * 添加订单配置分组
 */
export function ApiAddGroupItem(data: { name: string }) {
  return POST('/order/V2OrderServerGroup/create', data, { hostType: 'Golang' });
}

/**
 * 删除订单配置分组
 */
export function ApiDelGroupItem(data: { id: string }) {
  return POST('/order/V2OrderServerGroup/delete', data, { hostType: 'Golang' });
}

/**
 * @Description 编辑订单组名称
 * @returns {*}
 */
export function ApiChangeGroupName(payload: any) {
  return POST('/order/V2OrderServerGroup/update', payload, { hostType: 'Golang' });
}
