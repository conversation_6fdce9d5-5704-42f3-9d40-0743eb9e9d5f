<template>
  <div class="shipment-search-page">
    <section class="search-box">
      <a-form
        class="list-query"
        :model="listQuery"
      >
        <a-row
          :gutter="[24, 16]"
          style="width: 100%"
        >
          <a-col
            :lg="10"
            :md="12"
            :xl="8"
          >
            <a-form-item
              label="接单时间"
              style="margin-bottom: 16px"
            >
              <a-range-picker
                v-model:value="orderCreatedAt"
                :allow-clear="false"
                :placeholder="['填入', '填入']"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col
            :lg="8"
            :md="12"
            style="margin-bottom: 16px"
            :xl="6"
          >
            <a-button
              class="operator-button"
              type="primary"
              @click="loadList"
            >
              搜 索
            </a-button>
            <a-button
              class="operator-button"
              @click="resetList"
            >
              重 置
            </a-button>

            <span class="more-link">更多筛选条件</span>
            <down-outlined class="icon" />
          </a-col>
        </a-row>

        <div class="more-screen">
          <a-row
            class="screen-one"
            @mouseenter="maskOneVisible = true"
            @mouseleave="maskOneVisible = false"
          >
            <div
              v-show="maskOneVisible"
              class="screen-mask"
            >
              <span class="normal-text">
                该模块为
                <span class="zulin-text"> 租赁数智进阶版 </span>
                数据体验，当前内测中，是否参与体验？</span>

              <a-button
                style="margin-left: 8px"
                type="primary"
                @click="toExperience(1)"
              >
                去体验
              </a-button>
            </div>
            <a-col :sm="8">
              <a-form-item
                label="考核维度"
                style="margin-right: 24px"
              >
                <a-select
                  v-model:value="inspectionDimension"
                  :allow-clear="true"
                  :options="optionMap.inspection_dimension"
                  placeholder="请选择"
                >
                  <template
                    v-if="optionMapLoading"
                    #notFoundContent
                  >
                    <a-spin size="small" />
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :sm="8">
              <a-form-item
                label="风控定性"
                style="margin-right: 24px"
              >
                <a-select
                  v-model:value="listQuery.risk_qualitative"
                  :allow-clear="true"
                  mode="multiple"
                  :options="optionMap.risk_qualitative"
                  placeholder="请选择"
                >
                  <template
                    v-if="optionMapLoading"
                    #notFoundContent
                  >
                    <a-spin size="small" />
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :sm="8">
              <a-form-item label="类目/品牌/型号">
                <multiple-cascader
                  v-model="categoryBrandModel"
                  :loading="optionMapLoading"
                  :options="optionMap.goods_types"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row
            align="center"
            class="screen-two"
            @mouseenter="maskTwoVisible = true"
            @mouseleave="maskTwoVisible = false"
          >
            <div
              v-show="maskTwoVisible"
              class="screen-mask"
            >
              <span class="normal-text">
                该模块为
                <span class="zulin-text"> 租赁数智进阶版 </span>
                数据体验，当前内测中，是否参与体验？</span>

              <a-button
                style="margin-left: 8px"
                type="primary"
                @click="toExperience(2)"
              >
                去体验
              </a-button>
            </div>
            <a-col>
              <a-button type="primary">
                <template #icon>
                  <export-outlined class="export-icon" />
                </template>
                导出订单明细
              </a-button>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </section>

    <section class="statistics-data">
      <a-spin :spinning="statisticsLoading">
        <a-row :gutter="24">
          <a-col
            :lg="12"
            :md="24"
            :xl="6"
            :xxl="6"
          >
            <card
              description="订单状态为待风控、待发货、待收货、待归还、归还中、已归还、交易完成和订单状态从待风控、待发货、待收货、待归还、归还中、已归还、交易完成变成订单关闭的数据"
              :remain="0"
              :time="timeScoped"
              :title="`可发货订单数(${assessment})`"
              :value="statisticsData.server_can_delivery_sign"
            />
          </a-col>
          <a-col
            :lg="12"
            :md="24"
            :xl="6"
            :xxl="6"
          >
            <card
              description="「可发货订单总数」里，当前订单状态为待收货、待归还、归还中、已归还、交易完成的数据"
              :remain="0"
              :time="timeScoped"
              :title="`已发货订单数(${assessment})`"
              :value="statisticsData.server_is_delivery_sign"
            />
          </a-col>
          <a-col
            :lg="12"
            :md="24"
            :xl="6"
            :xxl="6"
          >
            <card
              description="可发货订单中已发货订单在可发货订单中的占比"
              :time="timeScoped"
              :title="`发货率(${assessment})`"
              unit="%"
              :value="statisticsData.delivery_rate && accMul(statisticsData.delivery_rate, 100)"
            />
          </a-col>
          <a-col
            :lg="12"
            :md="24"
            :xl="6"
            :xxl="6"
          >
            <card
              description="(已发货数+已经扣款未发货订单数）/可发货订单总数"
              :time="timeScoped"
              :title="`较大可能发货率(${assessment})`"
              unit="%"
              :value="statisticsData.desire_delivery_rate && accMul(statisticsData.desire_delivery_rate, 100)"
            />
          </a-col>
        </a-row>
      </a-spin>
    </section>

    <section class="table-list">
      <div class="title">
        发货明细
      </div>
      <div class="time">
        {{ timeScoped }}
      </div>
      <a-table
        class="table"
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :pagination="{
          total,
          current: listQuery.page,
          pageSize: listQuery.page_size,
        }"
        @change="listChange"
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'date'">
            {{ `${String(text).slice(0, 4)}-${String(text).slice(4, 6)}-${String(text).slice(6, 8)}` }}
          </template>
          <template v-if="column.dataIndex === 'delivery_rate'">
            {{ text && accMul(text, 100) }}%
          </template>
        </template>
      </a-table>
    </section>

    <open-remain-modal
      v-model:visible="openRemainVisible"
      :server-type="serverType"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import type { TableColumnType } from 'ant-design-vue';
import { DownOutlined, ExportOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import Card from '@/components/card-shuzhi/index.vue';
import type { MultipleCascaderItemType } from '@/components/multiple-cascader/data';
import MultipleCascader from '@/components/multiple-cascader/index.vue';
import { deepClone } from '@/utils/base';
import { accMul } from '@/utils/number';

import OpenRemainModal from '../../components/open-remain-modal/index.vue';
import type { ListQueryType, OptionMapType } from '../data';
import { getSearchMap, getShipmentList, getStatisticsData, loginShuzhi } from '../service';

const listQueryDefault = {
  order_created_date: {
    time_params: `${dayjs().subtract(9, 'days').format('YYYY-MM-DD')},${dayjs()
      .subtract(3, 'days')
      .format('YYYY-MM-DD')},day`,
    time_relation: 'absolute_date',
  },
  inspection_dimension: [1],
  page: 1,
  page_size: 10,
};

const emit = defineEmits(['getInspectionDimension']);

const listQuery = ref<ListQueryType>(deepClone(listQueryDefault));
const orderCreatedAt = computed({
  get() {
    return listQuery.value.order_created_date.time_params.split(',');
  },
  set(value) {
    listQuery.value.order_created_date.time_params = value ? value.join(',') + ',day' : '';
  },
});
const statisticsData = ref<{
  server_can_delivery_sign?: number;
  server_is_delivery_sign?: number;
  delivery_rate?: number;
  desire_server_is_delivery_sign?: number;
  desire_delivery_rate?: number;
}>({});
const statisticsLoading = ref(false);
const listLoading = ref(false);
const timeScoped = ref('');
const columns: TableColumnType[] = [
  {
    title: '时间',
    dataIndex: 'date',
    key: 'date',
  },
  {
    title: '发货率',
    dataIndex: 'delivery_rate',
    key: 'delivery_rate',
  },
  {
    title: '可发货数',
    dataIndex: 'server_can_delivery_sign',
    key: 'server_can_delivery_sign',
  },
  {
    title: '已发货数',
    dataIndex: 'server_is_delivery_sign',
    key: 'server_is_delivery_sign',
  },
];

const list = ref([]);
const total = ref(0);
const optionMapLoading = ref(false);
const optionMap = ref<OptionMapType>({});
// 考核维度
const assessment = ref<string>('所有订单');
// 绑定考核维度值
const inspectionDimension = computed({
  get() {
    return listQuery.value.inspection_dimension[0];
  },
  set(value) {
    listQuery.value.inspection_dimension = [value];
  },
});
// 类目/品牌/型号
const categoryBrandModel = ref<MultipleCascaderItemType[]>([]);

// 监听类目/品牌/型号切换
watch(
  () => categoryBrandModel.value,
  list => {
    const category: number[] = [];
    const brand: number[] = [];
    const model: number[] = [];
    list.forEach(item => {
      new Map([
        [0, category],
        [1, brand],
        [2, model],
      ])
        .get(item.level)
        ?.push(item.value);
    });
    Object.assign(listQuery.value, {
      category,
      brand,
      model,
    });
  },
);

function loadList() {
  statisticsLoading.value = true;
  getStatisticsData(listQuery.value)
    .then(res => {
      statisticsData.value = res.data;
      timeScoped.value = getTimeScoped();
      if (optionMap.value.inspection_dimension) {
        const item = optionMap.value.inspection_dimension.find(item => item.value === inspectionDimension.value);
        assessment.value = item ? item.label : '所有订单';
      }
    })
    .finally(() => (statisticsLoading.value = false));
  listLoading.value = true;
  getShipmentList(listQuery.value)
    .then(res => {
      list.value = res.data.list || [];
      total.value = res.data.page_info.count;
    })
    .finally(() => (listLoading.value = false));
}

function resetList() {
  listQuery.value = deepClone(listQueryDefault);
  categoryBrandModel.value = [];
  loadList();
}

function getTimeScoped() {
  const arr = listQuery.value.order_created_date.time_params.split(',');
  return `${arr[0]}~${arr[1]} 更新于 ${dayjs().format('YYYY-MM-DD hh:mm:ss')}`;
}

/**
 * 列表变化
 */
function listChange(e: { pageSize: number; current: number }) {
  listQuery.value.page = e.current;
  listQuery.value.page_size = e.pageSize;
  loadList();
}

const maskOneVisible = ref<boolean>(false);
const maskTwoVisible = ref<boolean>(false);
const openRemainVisible = ref<boolean>(false);
const serverType = ref<string>('serverStaff');

const shuzhiLink = ((referrer: string) => {
  if (import.meta.env.DEV || /dev\d*\-/.test(referrer)) {
    return 'dev1-shuzhi.rrzuji.com';
  } else if (/test\d*\-/.test(referrer)) {
    return 'test1-shuzhi.rrzuji.com';
  } else {
    return 'shuzhi.rrzu.com';
  }
})(document.referrer);

const toExperience = (type: number) => {
  if (type === 1) {
    window.sensors.track({ key: '$ScreenExperienceClick' });
  } else {
    window.sensors.track({ key: '$ExportExperienceClick' });
  }
  console.log(type);
  loginShuzhi().then(res => {
    if (res.data.account_id > 0) {
      window.open('https://' + shuzhiLink + '/statistics/shipment-data-board?token=' + res.data.token);
      window.sensors.track({ key: '$MerchantLoginShuzhi' });
    } else {
      serverType.value = res.data.server_role;
      openRemainVisible.value = true;
    }
  });
};

onMounted(() => {
  optionMapLoading.value = true;
  getSearchMap()
    .then(res => {
      optionMap.value = res.data;
      emit('getInspectionDimension', res.data.inspection_dimension);
    })
    .finally(() => {
      optionMapLoading.value = false;
    });
  loadList();
});
</script>

<style scoped lang="less">
@import '@/style/variables.less';
.shipment {
  padding-bottom: 10px;
}

.search-box {
  padding: 24px 24px 8px;
  background-color: #fff;
  .operator-button + .operator-button {
    margin-left: 8px;
  }

  .more-link {
    margin-left: 16px;
    color: #3777ff;
    cursor: pointer;
  }
}
.icon {
  margin-left: 8px;
  color: #3777ff;
}

.statistics-data {
  padding: 12px 24px;
  :deep(.ant-col) {
    margin-top: 12px;
    margin-bottom: 12px;
  }
}

.table-list {
  margin: 0 24px 24px;
  padding: 20px 24px;
  background-color: #fff;
  .title {
    color: rgb(103, 113, 130);
    font-weight: bold;
  }
  .time {
    color: rgb(177, 181, 191);
  }
  .table {
    margin-top: 20px;
  }
}

.more-screen {
  position: relative;
  display: flex;

  margin-bottom: 24px;
  padding: 0 24px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .screen-one {
    flex: 3;
    padding-top: 24px;
  }

  .screen-two {
    flex: 1;
    margin-left: 12px;
    padding-top: 24px;
    text-align: center;
  }

  .screen-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: rgba(6, 21, 51, 0.65);

    .normal-text {
      color: #fff;
    }
    .zulin-text {
      color: #52c41a;
    }
  }
}
</style>
