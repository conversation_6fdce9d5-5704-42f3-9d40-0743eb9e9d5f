<template>
  <a-range-picker
    class="block"
    :disabled-date="disabledDate"
    :value="hackVal || value"
    @calendar-change="onCalendarChange"
    @change="onRangeChange"
    @open-change="onOpenChange"
  />
</template>

<script lang="ts" setup>
import dayjs, { Dayjs } from 'dayjs';
import { ref } from 'vue';

type DateRange = [Dayjs, Dayjs];

defineProps<{
  value: DateRange;
}>();

const emit = defineEmits(['pick', 'update:value']);

const dateArr = ref<DateRange>();
const hackVal = ref<DateRange>();
const disabledDate = (current: Dayjs) => {
  const dayAfterToday = current && current > dayjs().endOf('day');
  if (!dateArr.value || (dateArr.value as any).length === 0) {
    return dayAfterToday;
  }
  const tooLate = dateArr.value[0] && current.diff(dateArr.value[0], 'days') > 7;
  const tooEarly = dateArr.value[1] && dateArr.value[1].diff(current, 'days') > 7;
  return dayAfterToday || tooEarly || tooLate;
};
const onCalendarChange = (val: DateRange) => {
  dateArr.value = val;
};
const onOpenChange = (open: boolean) => {
  if (open) {
    dateArr.value = [] as any;
    hackVal.value = [] as any;
  } else {
    hackVal.value = undefined;
  }
};
// 图表按日期筛选
const onRangeChange = (timeArr: DateRange) => {
  emit('update:value', timeArr);
  if (timeArr) {
    emit('pick', timeArr);
  }
};
</script>
