<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px' }"
    title="店铺信息"
    :top-style="{ padding: '24px 24px 12px 24px' }"
  >
    <a-tabs
      v-model:activeKey="activeKey"
      destroy-inactive-tab-pane
    >
      <a-tab-pane
        :key="ETabKeys.CertificationInfo"
        tab="认证资料"
      >
        <CertificationInfo @update:value="updateServerId" />
      </a-tab-pane>
      <a-tab-pane
        :key="ETabKeys.InformationBank"
        tab="运营信息"
      >
        <InformationBank />
      </a-tab-pane>
      <a-tab-pane
        :key="ETabKeys.Setting"
        tab="店铺设置"
      >
        <Setting :server-id="serverId" />
      </a-tab-pane>
      <a-tab-pane
        :key="ETabKeys.ShopQualification"
        tab="店铺资质"
      >
        <ShopQualification :current="qualificationCurrent" />
      </a-tab-pane>
      <a-tab-pane
        :key="ETabKeys.SupplementInfo"
        tab="商家经营档案"
      >
        <SupplementInfo />
      </a-tab-pane>
    </a-tabs>
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import CertificationInfo from './components/certification-info/index.vue';
import InformationBank from './components/information-bank/index.vue';
import Setting from './components/setting/index.vue';
import { backTypeMap } from './components/shop-qualification/config';
import { EOptions } from './components/shop-qualification/data.d';
import ShopQualification from './components/shop-qualification/index.vue';
import SupplementInfo from './components/supplement-info/index.vue';

/** 店铺信息 */
enum ETabKeys {
  /** 认证资料 */
  CertificationInfo = 'certificationInfo',
  /** 运营信息 */
  InformationBank = 'informationBank',
  /** 店铺设置 */
  Setting = 'setting',
  /** 店铺资质 */
  ShopQualification = 'shopQualification',
  /** 商家经营档案 */
  SupplementInfo = 'supplementInfo',
}

const route = useRoute();
const tabKey = route.query.tabKey || ETabKeys.CertificationInfo;
const activeKey = ref(tabKey);

const serverId = ref('');
const updateServerId = (server_id: string) => {
  serverId.value = server_id;
};

const qualificationCurrent = ref<EOptions | null>(null);
onMounted(() => {
  const type = window.sessionStorage.getItem('backQualificationType');
  if (type) {
    window.sessionStorage.removeItem('backQualificationType');
    activeKey.value = ETabKeys.ShopQualification;
    qualificationCurrent.value = backTypeMap[Number(type) as keyof typeof backTypeMap];
  }
});
</script>

<style scoped lang="less">
:deep(.ant-tabs-tab) {
  font-size: 16px;
}
</style>
