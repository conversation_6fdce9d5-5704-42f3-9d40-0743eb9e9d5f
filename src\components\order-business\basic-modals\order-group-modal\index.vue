<template>
  <a-modal
    v-model:visible="bindVisible"
    class="order-config-modal"
    title="分组管理"
    :width="700"
  >
    <a-spin :spinning="loading">
      <a-form
        layout="inline"
        :model="orderConfigForm"
        style="margin-bottom: 24px"
      >
        <a-form-item
          label="姓名"
          name="name"
        >
          <a-input
            v-model:value.trim="orderConfigForm.name"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item>
          <a-button
            type="primary"
            @click="addNewGroup"
          >
            新增
          </a-button>
        </a-form-item>
      </a-form>

      <a-table
        :columns="columns"
        :data-source="list"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'id'">
            {{ index + 1 }}
          </template>
          <template v-else-if="column.key === 'name'">
            <div
              v-if="inputStore[index]?.show === true"
              class="table-input"
            >
              <a-input
                v-model:value="inputStore[index].value"
                allow-clear
                :maxlength="8"
                placeholder="请输入"
                show-count
              />
              <div class="op-icons">
                <CheckOutlined
                  class="icon-check"
                  @click="changeName(record, index)"
                />
                <CloseOutlined
                  class="icon-close"
                  @click="toggleInput(index)"
                />
              </div>
            </div>
            <a-space v-else>
              <div>{{ record.name }}</div>
              <FormOutlined
                class="icon-edit"
                @click="toggleInput(index, record.name)"
              />
            </a-space>
          </template>
          <template v-else-if="column.key === 'op'">
            <a-button
              danger
              style="padding: 0"
              type="link"
              @click="delNew(record.id)"
            >
              删除
            </a-button>
          </template>
        </template>
      </a-table>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { createVNode, reactive, ref, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { CheckOutlined, CloseOutlined, ExclamationCircleOutlined, FormOutlined } from '@ant-design/icons-vue';

import { resetLazyLoadData } from '@/components/order-business/feature-merchant';
import { useVModel } from '@/hook';

import { columns } from './config';
import { OrderGroupListType } from './data';
import { ApiAddGroupItem, ApiChangeGroupName, ApiDelGroupItem, getOrderGroupList } from './services';

interface INameInput {
  show: boolean;
  value?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'changeGroup'): void;
}>();

const orderConfigForm = reactive({
  name: '',
});
const list = ref<OrderGroupListType[]>([]);
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
});

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const inputStore = ref<INameInput[]>([]);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      inputStore.value = [];
      loadData();
    }
  },
  {
    immediate: true,
  },
);

/** 加载数据 */
function loadData() {
  loading.value = true;
  getOrderGroupList({
    page_no: String(pagination.value.current),
    page_size: String(pagination.value.pageSize),
  })
    .then(res => {
      list.value = res.data || [];
      pagination.value.total = res.meta.pagination.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 添加 */
function addNewGroup() {
  loading.value = true;
  ApiAddGroupItem({ name: orderConfigForm.name })
    .then(() => {
      message.success('添加成功');
      loadData();
      emit('changeGroup');
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 删除 */
function delNew(id: string) {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: '该分组下目前可能有订单被关联，确定删除分组吗？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      loading.value = true;
      ApiDelGroupItem({ id })
        .then(() => {
          message.success('删除成功');
          loadData();
          emit('changeGroup');
        })
        .finally(() => {
          loading.value = false;
        });
    },
  });
}

function toggleInput(index: number, defaultValue?: string) {
  const input = inputStore.value[index];
  if (!input) {
    inputStore.value[index] = {
      show: true,
      value: defaultValue,
    };
  } else {
    input.show = !input.show;
    if (input.show) {
      input.value = defaultValue;
    }
  }
}

const { reset } = resetLazyLoadData();

/** 修改 */
function changeName(record: OrderGroupListType, index: number) {
  const value = inputStore.value[index].value;
  if (!value) {
    message.warning('分组名称是必填的');
  } else {
    record.name = value;
    loading.value = true;
    ApiChangeGroupName({ id: record.id, name: value })
      .then(() => {
        toggleInput(index);
        message.success('修改成功');
        loadData();
        reset();
      })
      .finally(() => {
        loading.value = false;
      });
  }
}

/** 分页切换 */
function handleTableChange(pag) {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  loadData();
}
</script>

<style lang="less" scoped>
.table-input {
  display: flex;
  gap: 4px;
}

.op-icons {
  display: flex;
  flex-direction: column;
}

.icon-edit {
  cursor: pointer;
}

.icon-check {
  color: #3777ff;
  cursor: pointer;
}

.icon-close {
  color: #f64356;
  cursor: pointer;
}
</style>
