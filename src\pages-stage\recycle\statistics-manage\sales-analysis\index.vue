<template>
  <layout-admin-page
    :navs="['趣回收', '经营管理', '回收销售分析表']"
    title="回收销售分析表"
  >
    <div class="container">
      <SearchGroup
        v-model:value="searchInfo"
        @confirm="getTableList"
      />
      <a-table
        class="table-box bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: 2028 }"
        @change="tableChange"
      />
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { reactive, onMounted } from 'vue';
import { useTable } from '@/hook/component/use-table';
import { columns } from './config';
import { ISearchInfo, IDataSource } from './data.d';
import SearchGroup from './components/search-group.vue';

const searchInfo = reactive<ISearchInfo>({
  source_type: '0',
  category_brand_model: [[]],
  sku_id: '',
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable<IDataSource, any>({
  url: '/super/recycle/data-count/sale-list',
  searchForm: searchInfo,
  method: 'GET',
  totalKey: 'data.pageData.count',
  formatSearchValue: (value: ISearchInfo) => {
    const params = {
      ...value,
    };
    delete (params as any).category_brand_model;
    const [category_id, brand_id, model_id] = formatCategoryPrams(value.category_brand_model);
    return {
      ...params,
      category_id,
      brand_id,
      model_id,
    };
  },
  formatHandle: res => res.data.listData,
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const formatCategoryPrams = (data: number[][]) => {
  const category_id: number[] = [],
    brand_id: number[] = [],
    model_id: number[] = [];
  if (data.length > 0) {
    data.forEach(item => {
      const [c, b, m] = item;
      if (item.length === 1) {
        category_id.push(c);
      } else if (item.length === 2) {
        category_id.push(c);
        brand_id.push(b);
      } else if (item.length === 3) {
        category_id.push(c);
        brand_id.push(b);
        model_id.push(m);
      }
    });
  }
  return [[...new Set(category_id)], [...new Set(brand_id)], [...new Set(model_id)]].map(i => i.join(','));
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
.container {
  padding: 2px 24px 26px 24px;
}
.table-box {
  margin-top: 24px;
}
</style>
