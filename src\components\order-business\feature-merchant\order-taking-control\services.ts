import { GET, POST } from '@/services/api';

/** 修改接单状态 */
export function updateReceive(data: any) {
  return POST('/server/update-receive-status', data);
}

/** 获取接单状态数据 */
export function fetchReceiveInfo() {
  return GET('/server/storeStatus', {}, { hostType: 'Golang' });
}

/**
 * @description: 查询是否有权限关单
 * @return {*}
 */
export function hasPermissionCloseDispatch() {
  return GET('/server/check-open-order-dispatch');
}

export function getBusinessCalendar() {
  return GET('/distributor/business-calendar');
}

export function getHolidayCalendar() {
  return GET('/distributor/holiday-calendar');
}
