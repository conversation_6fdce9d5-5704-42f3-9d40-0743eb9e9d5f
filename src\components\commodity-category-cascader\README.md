# CommodityCategoryCascader 商品分类级联选择

基于 [a-cascader](https://3x.antdv.com/components/cascader-cn#API) 组件封装，在保持原有属性的基础上，针对商品类目进行封装，使其具备对商品分类的单选、多选、分级别获取、全级别获取、远程搜索、禁用项显示控制等。当前未全局引入，需手动引入后使用。

### 属性

除已有属性外，其他属性已继承自 [a-cascader](https://3x.antdv.com/components/cascader-cn#API) ，更多可查看对应文档获取

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value(v-model) | 指定选中项 | number[]\|number\[]\[] | -- |
| disabledType | 分类字典页面中可设置选择项的隐藏过滤，这里配置'show'可显示但禁用选项，配置'enable'可显示且选项可选 | 'show'\|'enable' | -- |
| level | 获取到最后一级的分类级别：1 一级\| 2 二级\| 3 品牌\| 4 型号 | 1 \| 2 \| 3 \| 4 | 4 |
| scene | 不同的场景会决定分类的排序，1 平台商品 2 商家商品 3 平台订单 4 商家订单 | EScene（1 \|2 \|3 \|4） | 1 或 2(根据用户 role 判断) |
| filterPidLevel | 对第几层的父 id 进行筛选（通过 pid 属性获取分类），需要获取莫个分类下子级选择项时使用 | 1 \|2 \|3 | -- |
| pid | 使用完整的父级层 id 数组（完整的父级 id 查询速度更快），配合 filterPidLevel 属性使用，获取对应 id 下的子级选择项 | number[] | -- |
| asyncSearch | 是否使用异步远程搜索，无法与 filterPidLevel 同时使用，推荐在需要获取 4 级的分类数据时使用，避免一次性获取过多分类数据导致加载过慢 | boolean | false |
| isMustChild | 使绑定的数据必须包含子级（如 level 是 3，而返回数据层级少于 3 时，在无不设置时 changeOnSelect 时将无法被选择） | boolean | false |
| changeOnSelect | 当此项为 true 时，每级菜单选项值都可选，与 a-cascader 不同的是，多选也可生效 | boolean | false |
| defineOptions | 定义选项，传入函数，可用于获取选项，return 的选择将控制最终显示的选择项内容 | (options)=>options | -- |



### 事件

均已继承自 [a-cascader](https://3x.antdv.com/components/cascader-cn#API) ，更多可查看对应文档获取

| 事件名称 | 说明         | 回调参数        |
| -------- | ------------ | --------------- |
| change   | 手动更改选择 | (value,options) |



### 获取选择项数据方式

#### useCommodityCategoryTree函数获取

通过调用useCommodityCategoryTree函数获取数据，需要手动配置与组件等同的参数来获取数据，其优点是与组件不会有关联，可在项目任何地方调用，且不会重复请求数据。

```vue
<template>
	<CommodityCategoryCascader
  	v-model:value="categoryIds"
  	change-on-select
  	:level="3"
  	placeholder="请选择类目"
	/>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import { useCommodityCategoryTree } from '@/composables/commodity-category';

const categoryIds = ref<number[]>([])

const { categoryTree: options } = useCommodityCategoryTree({
  level: 3,
});
</script>
```

#### ref获取

通过定义组件ref获取暴露的选择项

```vue
<template>
	<CommodityCategoryCascader
    ref="cascaderRef"
  	v-model:value="categoryIds"
  	change-on-select
  	:level="3"
    :scene="EScene.PlatformOrder"
  	placeholder="请选择类目"
	/>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import type { IOption } from '@/composables/commodity-category/data';
import { EScene } from '@/utils/enums/commodity';

const categoryIds = ref<number[]>([]);

const cascaderRef = ref();
const options = computed<IOption[]>(() => {
  return cascaderRef.value?.options;
});
</script>
```

#### defineOptions参数内获取

defineOptions在选择项发生变化时执行，便可通过执行过程时进行赋值选择项，注意defineOptions必须要有return返回

```vue
<template>
	<CommodityCategoryCascader
  	v-model:value="categoryIds"
  	change-on-select
    :defineOptions="defineOptions"
  	:level="3"
    :scene="EScene.PlatformOrder"
  	placeholder="请选择类目"
	/>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import type { IOption } from '@/composables/commodity-category/data';
import { EScene } from '@/utils/enums/commodity';

const categoryIds = ref<number[]>([]);
const options = ref<IOption[]>([]);

function defineOptions(options: IOption[]) {
  options.value = options;
  return options;
}
</script>
```



### 在form-create中使用

项目公共组件form-create中配置时，fragmentKey配置'renderCustom'之后，component配置使用该组件即可，组件的相关属性同样在elProps中配置生效。

```ts
import { FormGroupItem } from '@/components/form-create/src/typing';
import CommodityCategoryCascader from '@/components/commodity-category-cascader';

export const operationRecordSearchFormGroup: FormGroupItem[] = [
  {
    key: 'spu_id',
    fragmentKey: 'renderInput',
    originProps: { label: '商品ID', name: 'spu_id' },
    elProps: { placeholder: '请输入', allowClear: true },
  },
  {
    key: 'category_id',
    fragmentKey: 'renderCustom',
    component: CommodityCategoryCascader,
    originProps: { label: '商品分类', name: 'category_id' },
    elProps: { level: 3 },
  },
}
```



### 应用

#### 获取到X级的分类选择

通过level配置，默认会取第4级

```vue
<!-- 3级的商品分类选择 -->
<CommodityCategoryCascader
  v-model:value="formData.categoryIds"
  :level="3"
  placeholder="请选择类目"
/>
```

#### 获取第X级id为Y的子级分类选择

配置filterpPidLevel与pid来决定只显示莫个分类的子级选择项


```vue
<!-- 第2级id为34的子级分类选择 -->
<CommodityCategoryCascader
  v-model:value="category"
  :filter-pid-level="2"
  :pid="[2,34]"
  placeholder="请选择商品型号"
  style="width: 320px"
/>
<!-- 第1级id为1，子级到3级(品牌)的分类选择 -->
<CommodityCategoryCascader
  v-model:value="category"
  :filter-pid-level="1"
  :pid="[1]"
  :level="3"
  placeholder="请选择商品品牌"
  style="width: 320px"
/>
```

#### 数据是否必须有子级

通过 isMustChild 配置，数据是否必须包含子级，如 level 是 3，而返回数据层级少于 3 且不设置时 changeOnSelect 时无法被选择

```vue
<!-- 设置显示的选择项都必须要有第3级的分类 -->
<CommodityCategoryCascader
  v-model:value="formData.categoryIds"
  :level="3"
	is-must-child
  placeholder="请选择品牌"
/>
```

#### 每级菜单选项值都可选

通过changeOnSelect配置， 原 [a-cascader](https://3x.antdv.com/components/cascader-cn#API) 基础上修改，区别是多选也会生效，因此如果你需要在多选时也让每个选择都可选时，需要配置该属性，否则选择时只有点击最后一级选项才可选择。

```vue
<!-- 单选时选择项的每一项都可被选择 -->
<CommodityCategoryCascader
  v-model:value="formData.categoryIds"
  :level="3"
	change-on-select
  placeholder="请选择类目"
/>
<!-- 多选时选择项的每一项都可被选择 -->
<CommodityCategoryCascader
  v-model:value="formData.categoryIds"
  :level="3"
	multiple
	change-on-select
  placeholder="请选择类目"
/>
```

#### 异步远程搜索

配置asyncSearch启用，通常在获取到4级的数据时由于数量过大导致请求缓慢时推荐开启，选择项便会通过多次请求的方式，只获取需要的分类选择项数据，其他情况不推荐开启，且不能与filterPidLevel同时使用。

```vue
<!-- 可远程搜索选择有4级商品分类的选择项 -->
<CommodityCategoryCascader
  v-model:value="formData.categoryIds"
  async-search
  change-on-select
  placeholder="请选择类目"
/>
```

#### 禁用选项显示控制

在分类字典页面中可设置选择项的隐藏不可选，组件内获取到数据默认会对这些选项过滤掉，这里配置通过配置disabledType控制这些选择项的显示方式，'show'是可显示但禁用选项，'enable'是可显示并且选项可选。

```vue
<!-- 显示禁用选项 -->
<CommodityCategoryCascader
  v-model:value="category"
	disabled-type="show"
  :level="2"
/>
<!-- 启用禁用选项 -->
<CommodityCategoryCascader
   v-model:value="category"
   disabled-type="enable"
   :level="3"
/>
```

#### 商品分类场景

不同的场景会决定分类的排序，如平台商品时会根据平台内分类对应的商品数量进行排序，商家订单则会跟根据当前商家账号下分类对应的订单数量进行排序，默认根据当前账号role的决定为平台商品或者商家商品，使用时可根据应用的页面业务场景进行配置。

```vue
<!-- 商家订单 -->
<CommodityCategoryCascader
  v-model:value="category"
	:scene="EScene.StoreOrder"
  :level="2"
/>
<!-- 平台订单 -->
<CommodityCategoryCascader
   v-model:value="category"
   :scene="EScene.PlatformOrder"
   :level="3"
/>
```

