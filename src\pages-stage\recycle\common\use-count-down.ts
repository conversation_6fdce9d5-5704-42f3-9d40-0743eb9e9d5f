import { ref, onBeforeUnmount } from 'vue';

interface CountDownConfig {
  seconds: number;
  format?: string;
  callback?: () => void;
}

export default function (config: CountDownConfig) {
  // 倒计时文本
  const text = ref('');
  const [formatMap, valueMap] = [{}, {}];
  let timer: NodeJS.Timeout | undefined = undefined;
  let { seconds = 0 } = config;
  const { callback, format = 'DD:hh:mm' } = config;

  const timeTick = () => {
    text.value = '';
    seconds -= 1; // 剩余的秒数
    if (seconds <= 0) {
      timer && clearInterval(timer);
      text.value = '--';
      typeof callback === 'function' && callback();
      return;
    }
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    valueMap['ss'] = seconds % 60; // 剩余的秒数
    valueMap['mm'] = minutes % 60; // 剩余的分钟数
    valueMap['hh'] = hours % 24; // 剩余的小时数
    valueMap['DD'] = days; // 剩余的天
    formatMap['ss'] = `${seconds % 60}秒`;
    formatMap['mm'] = `${minutes % 60}分`;
    formatMap['hh'] = `${hours % 24}时`;
    formatMap['DD'] = `${days}天`;
    for (let i = 0; i < format.split(':').length; i++) {
      const key = format.split(':')[i];
      text.value += valueMap[key] > 0 ? formatMap[key] : '';
    }
  };

  timeTick();

  timer = setInterval(timeTick, 1000);

  onBeforeUnmount(() => {
    timer && clearInterval(timer);
  });

  return {
    text,
    timer,
  };
}
