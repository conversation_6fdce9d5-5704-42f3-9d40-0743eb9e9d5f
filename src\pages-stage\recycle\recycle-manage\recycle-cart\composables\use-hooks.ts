import { ref, computed } from 'vue';
import { Modal } from 'ant-design-vue';
import { IDataInfo } from '../data';
import { getCarList, delCarItem, filterImeiHasOrderAndReport } from '../service';

export const useHooks = () => {
  const formState = ref<any>({});

  const dataSource = ref<IDataInfo[]>([]);

  const loading = ref<boolean>(false);

  const selectedRowKeys = ref<number[]>([]);

  const imeiErrorList = ref<number[]>([]);

  // 记录未失效的数据;
  const validDataSource = computed(() => {
    const data = dataSource.value.filter((item: IDataInfo) => !item.is_invalid);
    return data;
  });

  const indeterminate = computed(() => {
    return !!(selectedRowKeys.value.length && selectedRowKeys.value.length != validDataSource.value.length);
  });

  const isSelectAll = computed(() => {
    return !!(validDataSource.value.length && validDataSource.value.length === selectedRowKeys.value.length);
  });
  const selectItems = computed(() => {
    const list = validDataSource.value.filter((item: IDataInfo) => selectedRowKeys.value.includes(item.carId));
    return list;
  });
  const onSelectChange = (selecyKeys: number[]) => {
    selectedRowKeys.value = selecyKeys;
    calcTotalPrice();
  };
  const onSelectAll = () => {
    const selectKeys: number[] = (validDataSource.value as IDataInfo[]).map(item => item.carId);
    onSelectChange(isSelectAll.value ? [] : selectKeys);
  };

  const getCheckboxProps = (record: IDataInfo) => ({
    disabled: !!record.is_invalid,
  });

  const onDel = async (carId: string) => {
    const params = { ids: carId };
    await delCarItem(params);
    listInit(() => {
      const idList = validDataSource.value.map((item: IDataInfo) => item.carId);
      selectedRowKeys.value = selectedRowKeys.value.filter((key: number) => {
        return idList.includes(key);
      });
      calcTotalPrice();
    });
  };
  const onDelSelectAll = () => {
    const idsStr = selectedRowKeys.value.join(',');
    onDel(idsStr);
  };

  const rowClassName = (record: IDataInfo) => {
    if (imeiErrorList.value.includes(record.imei)) return 'isError';
  };

  const totalPrice = ref<number>(0);
  const calcTotalPrice = () => {
    const total = selectItems.value
      .reduce((pre, cur: IDataInfo) => {
        return pre + cur.evaluatePrice;
      }, 0)
      .toFixed(2);
    totalPrice.value = Number(total);
  };

  const listInit = async (cb?: any) => {
    loading.value = true;
    try {
      const res = await getCarList();
      const data = res.data || {};
      const list = data.list || [];
      list.forEach((item: IDataInfo) => {
        item.sku_info = {
          model: item.model,
          color: item.color,
          memory: item.memory,
          version: item.version,
          condition: item.condition,
          condition_tran: item.condition_tran,
          imei: item.imei,
          is_replace_device: item.is_replace_device,
        };
        item.evaluatePrice = Number(item.evaluatePrice);
        item.imei = Number(item.imei);
      });
      dataSource.value = list;
      cb && cb();
    } finally {
      loading.value = false;
    }
  };
  // 校验提交的imei码是否符合生成订单
  const validatorImei = async () => {
    const imeiList = selectItems.value.map((item: IDataInfo) => item.imei);
    const res = await filterImeiHasOrderAndReport({ imeiArr: imeiList });
    const data = res.data || {};
    let { error, success } = data;
    if (error && error.length) {
      error = error.map((val: string) => Number(val));
      success = success.map((val: string) => Number(val));
      const imeiErrorData = dataSource.value.filter((item: IDataInfo) => error.includes(item.imei));
      const list = [...new Set([...imeiErrorData, ...dataSource.value])];
      dataSource.value = list;
      imeiErrorList.value = error;
      selectedRowKeys.value = selectItems.value
        .filter((item: IDataInfo) => success.includes(item.imei))
        .map((item: IDataInfo) => item.carId);
      calcTotalPrice();
      Modal.warning({
        title: '部分设备IMEI码错误',
        content: '您的设备IMEI码已被占用，请重新输入。',
      });
      return false;
    }
    return true;
  };

  listInit();

  return {
    formState,
    dataSource,
    loading,

    selectItems,
    selectedRowKeys,
    indeterminate,
    isSelectAll,
    onSelectChange,
    getCheckboxProps,
    onSelectAll,

    onDel,
    onDelSelectAll,
    rowClassName,

    totalPrice,

    listInit,

    validatorImei,
  };
};
