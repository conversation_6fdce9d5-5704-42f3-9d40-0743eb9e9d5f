<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    ok-text="提交"
    title="顺丰取件"
    :visible="visible"
    width="560px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form
      ref="formRef"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item label="订单号">
        <div class="order-ids-block">
          <div class="ids-list">
            <span
              v-for="(ite, index) in isExtend ? orderId : orderId.slice(0, 4)"
              :key="index"
            >{{ ite }},</span>
          </div>
          <div
            v-if="orderId.length > 4"
            class="extend-btn"
            @click="isExtend = !isExtend"
          >
            {{ isExtend ? '收起' : '展开' }}
          </div>
        </div>
      </a-form-item>
      <a-form-item
        label="上门取件时间"
        name="times"
        :rules="[{ required: true, message: '请选择上门取件时间!' }]"
      >
        <a-cascader
          v-model:value="formState.times"
          :options="options"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item
        label="取件地址"
        name="sf_address"
        :rules="[{ required: true, message: '请选择取件地址!' }]"
      >
        <a-select
          v-model:value="formState.sf_address"
          :options="addressOption"
          placeholder="请选择取件地址"
        />
      </a-form-item>
      <a-form-item
        label="收件地址"
        name="receive_address"
        :rules="addressRule"
      >
        <a-form-item-rest>
          <div class="address-box">
            <a-select
              v-model:value="provinceValue"
              :filter-option="filterOption"
              :options="provinceOptions"
              placeholder="请选择省份"
              show-search
              @change="
                () => {
                  formState.is_change_receive = 1;
                  cityValue = null;
                  areaValue = null;
                }
              "
            />
            <a-select
              v-model:value="cityValue"
              :filter-option="filterOption"
              :options="cityOptions"
              placeholder="请选择城市"
              show-search
              @change="
                () => {
                  formState.is_change_receive = 1;
                  areaValue = null;
                }
              "
            />
            <a-select
              v-model:value="areaValue"
              :filter-option="filterOption"
              :options="areaOptions"
              placeholder="请选择市区"
              show-search
              @change="() => (formState.is_change_receive = 1)"
            />
          </div>
          <a-input
            v-model:value="addressValue"
            placeholder="请输入详细地址"
            style="margin-top: 8px"
            @change="formState.is_change_receive = 1"
          />
        </a-form-item-rest>
      </a-form-item>
      <a-form-item
        label="收件人姓名"
        name="name"
        :rules="[{ required: true, message: '请填写收件人姓名' }]"
      >
        <a-input
          v-model:value="formState.name"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="收件人手机"
        name="phone"
        :rules="[{ required: true, message: '请填写收件人手机号', validator: validatePass }]"
      >
        <a-input
          v-model:value="formState.phone"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="已选服务"
        name="serve"
      >
        <a-checkbox-group
          v-model:value="formState.serve"
          class="serve-group"
        >
          <div>
            <a-checkbox
              name="type"
              value="is_insure"
            >
              保价
            </a-checkbox>
            <a
              class="sf-link"
              :href="origin + '/img/sf_service_insure.png'"
              target="_blank"
            >保价规则</a>
          </div>
          <div>
            <a-checkbox
              name="type"
              value="is_packed"
            >
              包装
            </a-checkbox>
            <a
              class="sf-link"
              href="https://www.sf-express.com/cn/sc/express/value_added_service/installation_services/"
              target="_blank"
            >包装费用说明</a>
          </div>
        </a-checkbox-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { message, FormInstance } from 'ant-design-vue';
import moment from 'moment';
import { globalConfig } from '@/config/global-config';
import { sfParams, batchSfExpress, getAddressOptions } from '../service';
import { useAddress } from '@/pages-stage/recycle/order/self-order-list/composables/use-address';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: Array,
    default: [],
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const route = useRoute();

const submitLoading = ref(false);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref<any>({
  times: [],
  sf_address: null, // 取件地址
  serve: ['is_sign'], // 选择的服务
  name: null,
  phone: null,
  is_change_receive: 0,
});

// 域名
const origin = ref<any>('');
//展开收起
const isExtend = ref(false);
const options = createTimeOptions();

const addressOption = ref([]);
const onCancel = () => emits('update:visible', false);

const {
  provinceValue,
  cityValue,
  areaValue,
  addressValue,

  provinceOptions,
  cityOptions,
  areaOptions,

  filterOption,
} = useAddress();

const addressRule = {
  required: true,
  trigger: 'change',
  validator: () => {
    if (!provinceValue.value || !cityValue.value || !areaValue.value || !addressValue.value) {
      return Promise.reject('请完善收件地址');
    }
    return Promise.resolve();
  },
};
const validatePass = () => {
  const phoneRegExp = /^1[3456789]\d{9}$/;
  if (!phoneRegExp.test(formState.value.phone)) {
    return Promise.reject('请完善收件地址');
  }
  return Promise.resolve();
};
const onOk = () => {
  formRef.value?.validate().then(() => {
    const params: sfParams = {
      order_id: props.orderId.join(','),
      is_insure: formState.value.serve.includes('is_insure') ? 1 : 0,
      is_packed: formState.value.serve.includes('is_packed') ? 1 : 0,
      sf_address: formState.value.sf_address,
      send_start_time: `${formState.value.times[0]} ${formState.value.times[1]}`,
      is_change_receive: formState.value.is_change_receive,
      receive_address: JSON.stringify({
        name: formState.value.name,
        phone: formState.value.phone,
        province_name: provinceValue.value,
        city_name: cityValue.value,
        area_name: areaValue.value,
        addr_detail: addressValue.value,
      }),
    };
    submitLoading.value = true;
    batchSfExpress(params)
      .then(() => {
        onCancel();
        message.success('预约成功');
        emits('onLoadList');
      })
      .finally(() => (submitLoading.value = false));
  });
};

onMounted(() => {
  origin.value = route.query.origin || globalConfig.baseUrl;
  getAddressOptions().then(({ data }) => {
    const { sf_send_address_list } = data || {};
    addressOption.value = (sf_send_address_list || []).reduce((acc: any[], curr: string, index: number) => {
      acc.push({
        lable: index,
        value: curr,
      });
      return acc;
    }, []);
  });
});

function createTimeOptions(start = 8, end = 19) {
  // 生成配置信息
  let time: any[] = [];
  for (let i = start; i < end; i++) {
    time.push({
      id: i,
      value: `${i < 10 ? '0' + i : i}:00:00`,
      label: `${i < 10 ? '0' + i : i}:00-${i + 1 < 10 ? '0' + (i + 1) : i + 1}:00`,
    });
  }
  const options = ['今天', '明天', '后天'].map((item, index) => {
    return {
      value: moment().add(index, 'days').format('YYYY-MM-DD'),
      label: item,
      children: time,
    };
  });
  const todayHour = moment().hour();
  // 当前时间是否大于最大时间 ？ 移除今天 : 过滤今天时间
  todayHour >= end ? options.splice(0, 1) : (options[0].children = time.filter(item => item.id > todayHour));
  return options;
}
</script>
<style lang="less" scoped>
.serve-group {
  display: flex;
  flex-direction: column;
}

.sf-link {
  margin-left: 8px;
  color: #22d4c5;
  text-decoration: underline;
  cursor: pointer;
}

.address-box {
  display: flex;
}
.order-ids-block {
  display: flex;
  align-items: flex-end;
  .ids-list {
    display: flex;
    flex-wrap: wrap;
    width: 340px;
    span {
      margin-left: 5px;
    }
  }
  .extend-btn {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    height: 100%;
    color: #00c8be;
    cursor: pointer;
  }
}
</style>
