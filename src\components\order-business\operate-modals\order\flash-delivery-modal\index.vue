<template>
  <a-modal
    title="预约闪送"
    :visible="visible"
    width="600px"
    @cancel="visible=false"
  >
    <template #footer>
      <a-button
        :disabled="!isFlashSupported"
        :loading="loading"
        type="primary"
        @click="handleNextStep"
      >
        下一步
      </a-button>
    </template>
    <a-form
      class="flash-delivery-form"
      layout="vertical"
    >
      <a-spin :spinning="receiptShow">
        <a-form-item>
          <template #label>
            <h3>收件信息：</h3>
          </template>
          <div class="form-info-group">
            <div class="form-info-item">
              <span class="info-label">收件地址：</span>
              <span>{{ receiptInfo.desensitized_address }}</span>
            </div>
            <div style="display: flex;">
              <div
                class="form-info-item"
                style="padding-right: 20px;"
              >
                <span class="info-label">收件人：</span>
                <span>{{ receiptInfo.desensitized_name }}</span>
              </div>
              <div class="form-info-item">
                <span class="info-label">联系电话：</span>
                <span>{{ receiptInfo.desensitized_phone }}</span>
              </div>
            </div>
          </div>
        </a-form-item>
      </a-spin>

      <a-form-item>
        <template #label>
          <h3>预约信息：</h3>
        </template>
        <a-form-item
          class="order-info-item"
          label="取货地址："
          name="pickupInfo"
        >
          <a-space>
            <a-select
              v-model:value="serverStoreId"
              allow-clear
              :filter-option="filterOption"
              :options="serverStoreList"
              placeholder="请选择"
              show-search
              style="width: 380px;"
              @change="handleChangeStore"
            />
          </a-space>
        </a-form-item>
        <a-form-item
          class="order-info-item"
          label="上门取件时间："
          name="pickupTime"
        >
          <a-date-picker
            v-model:value="appointment_date"
            :disabled-date="disabledDate"
            :disabled-time="disabledTime"
            format="YYYY-MM-DD HH:mm"
            placeholder="请选择"
            :show-now="true"
            :show-time="{ format: 'HH:mm' }"
            @change="handleChangeDate"
          />
        </a-form-item>
      </a-form-item>

      <a-form-item>
        <div class="tips">
          提示：选择【闪送】服务类型时，点击【确定】后，平台会弹出即为你预测闪送费用及闪送预计时间，再次点击【发起闪送】才会为你呼叫骑手。【预测有效期为30分钟，超时需要重新走一遍流程】
        </div>
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 闪送确认弹窗 -->
  <FlashDeliveryConfirmModal
    :api-result="apiResult"
    :order-info="confirmOrderInfo"
    :visible="confirmModalVisible"
    @confirm="handleConfirmFlashDelivery"
    @update:visible="handleConfirmModalClose"
  />
</template>

<script setup lang="ts">
import {  computed,reactive,ref, watch  } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVModel } from '@/hook';

import FlashDeliveryConfirmModal from '../flash-delivery-confirm-modal/index.vue';
import { useFlashDeliveryList } from './config';
import { flashDeliveryOrderPlace,flashDeliveryPreOrder,getServerStoreList } from './services';

const loading = ref<boolean>(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  serverId: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['update:visible', 'refresh']);
const visible = useVModel(props, 'visible', emit);
const receiptShow = ref<boolean>(true);
const apiResult = ref(null);

const {receiptInfo,handleGetAddress} = useFlashDeliveryList(props.orderId);


// 控制确认弹窗的显示与隐藏
const confirmModalVisible = ref(false);
const confirmOrderInfo = ref({});

const appointment_date = ref<dayjs | string>(dayjs());
const fetchFlashDeliveryInfo =async () => {
  loading.value = true;

  let date;
  if (isImmediateDelivery.value) {
    date = '';
  }else{
    date = appointment_date.value && dayjs(appointment_date.value).format('YYYY-MM-DD HH:mm');
  }

  const params = {
    order_id: Number(props.orderId),
    channel_code: 'ZL',// 租赁业务客户端专用，客户端用ZLKHD，非客户端用ZL
    ...(date && { appointment_date:date }),
    ...(serverStoreId.value && { server_store_id: serverStoreId.value })
  }

  await flashDeliveryPreOrder(params).then(res => {
    Object.assign(confirmOrderInfo.value,res.data);
    confirmModalVisible.value = true;
  }).catch(()=>{
    confirmModalVisible.value = false;
  }).finally(()=>{
    loading.value = false;
    emit('refresh', props.orderId);
  })
}

const getContact = (array) => {

    if (!Array.isArray(array)) {
      return array || '';
    }

    // 查找默认联系人 (is_default === 1)
    const defaultContact = array.find(item => item?.is_default === 1);

    if (defaultContact) {
      return (defaultContact?.contact_name + defaultContact?.contact_phone) || '';
    }

    // 单个联系人 || 多个联系人
    return (array[0]?.contact_name + array[0]?.contact_phone) || '';
}

const serverStoreId = ref(null);
const serverStoreList = reactive([]);
const getPickupInfo = async (server_id) => {
  // 公开门店筛选，1 公开 2 非公开
  const res = await getServerStoreList({server_id,open:1,order_id:props.orderId});
  serverStoreList.length = 0;
  if (res.data?.length) {
    res.data.forEach(ele => {
      const defaultContact = getContact(ele.store_contact);
      const label = ele.store_address + (ele?.store_address_desc || '') + defaultContact;
      serverStoreList.push({
        ...ele,
        label,
        value: ele.id
      });
    });
  }
}
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 判断一下service_certification_list和service_certification_open_list字段的信息，都有1说明能用闪送
const isSupportedFlash = computed(()=>{
  return (option)=>{
    const {service_certification_list,service_certification_open_list} = option;
    const isList = service_certification_list.some(item => item === 1);
    const isOpenList = service_certification_open_list.some(item => item === 1);
    return isList && isOpenList;
  };
})

const isFlashSupported = ref(true);
const handleChangeStore = (val,option)=>{
  isFlashSupported.value = option ? isSupportedFlash.value(option) : true;
  if(!isFlashSupported.value){
    message.warning('当前门店不支持闪送');
  }
}

watch(
  () => props.visible,
  async (value) => {
    if (value) {
      receiptShow.value = true;
      serverStoreId.value = null;
      await handleGetAddress(props.orderId).then((res)=>{
        receiptShow.value = !res;
      });
      await getPickupInfo(props.serverId);
    }
  },
  { immediate: true }
);

watch(
  () => confirmModalVisible.value,
  (val) => {
    if (!val) {
      visible.value = false;
      emit('refresh', props.orderId);
    }
  }
);


const disabledDate = (current: Dayjs) => {
  return current && current < dayjs().startOf('minute');
};

const isImmediateDelivery = ref(false);
const handleChangeDate = (val) => {
  // 新增时间判断逻辑
  const oneHourLater = dayjs().add(1, 'hour');
  if (val && dayjs(val).isBefore(oneHourLater)) {
    isImmediateDelivery.value = true;
  }
}
handleChangeDate(appointment_date.value);


// 处理下一步按钮点击
const handleNextStep = async () => {
  await fetchFlashDeliveryInfo();
  visible.value = false;
};

// 处理确认弹窗的关闭
const handleConfirmModalClose = () => {
  confirmModalVisible.value = false;
};

// 处理确认闪送
const handleConfirmFlashDelivery =async (orderInfo) => {
  const {order_no} = orderInfo?.iss_order;
  await flashDeliveryOrderPlace({
    order_no: String(order_no)
  })
  apiResult.value = { success: true};
  confirmModalVisible.value = false;
  visible.value = false;
  emit('refresh', props.orderId);
};
</script>

<style scoped lang="scss">
.flash-delivery-form {
  padding: 10px;
  color: #333;
}

.form-info-group {
  padding:0 15px 15px 15px;
}

.order-info-item {
  flex-direction: row;
  color: #666;
  :deep(.ant-form-item-label) {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 120px;
    padding-bottom: 0;
  }
}

.form-info-item {
  margin-bottom: 14px;
}

.info-label {
  display: inline-block;
  width: 100px;
}

.tips {
  padding: 10px;
  color: #333;
  font-size: 14px;
  line-height: 30px;
}

</style>
