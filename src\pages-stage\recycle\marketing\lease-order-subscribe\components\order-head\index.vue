<template>
  <div class="order-head">
    <div class="data-item">
      订单号：{{ orderId }}
    </div>
    <div class="data-item">
      创建日期：{{ commonHead.create_date }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

defineProps({
  // 订单ID
  orderId: {
    type: String,
    required: true,
  },
  // 基本信息
  commonHead: {
    type: Object as PropType<{ create_date?: string }>,
    required: true,
  },
});
</script>

<style lang="less" scoped>
.order-head {
  display: flex;
  padding: 16px;
  background: #f9f9fb;

  .data-item {
    margin-right: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;

    &:nth-last-child(1) {
      margin: 0;
    }
  }
}
</style>
