<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :destroy-on-close="true"
    :footer="null"
    title="备注记录"
    :visible="visible"
    width="800px"
    @cancel="onCancel"
  >
    <div class="table-block">
      <a-table
        class="list-table"
        :columns="columns"
        :data-source="tableList"
        :loading="isLoading"
        :pagination="false"
        :scroll="{ y: 500 }"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { remarkList } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
});
const columns = [
  {
    title: '备注内容',
    dataIndex: 'remark',
    width: 470,
  },
  {
    title: '操作人',
    dataIndex: 'username',
    width: 100,
  },
  {
    title: '备注时间',
    dataIndex: 'created_at',
  },
];

const isLoading = ref(false);
const tableList = ref([]);

const emits = defineEmits(['update:visible', 'onLoadList']);

const onCancel = () => emits('update:visible', false);

onMounted(() => {
  isLoading.value = true;
  remarkList(props.id)
    .then(({ data }) => {
      tableList.value = data;
    })
    .finally(() => (isLoading.value = false));
});
</script>
<style lang="less" scoped>
.table-block {
  height: 600px;
}
</style>
