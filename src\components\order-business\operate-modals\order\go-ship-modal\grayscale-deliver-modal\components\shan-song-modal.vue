<template>
  <a-modal
    v-model:visible="bindVisible"
    :closable="false"
    :mask-closable="false"
    title="闪送预发起单"
    :width="424"
    z-index="10001"
  >
    <a-alert
      show-icon
      type="warning"
    >
      <template #message>
        预发起单超时时间为【{{ timeoutText }}】，<span style="color: #ff4d4f">如未在超时时间前【发起闪送】，当前预发起单将失效</span>，需要重新通过【去发货】生成新的预发起单
      </template>
    </a-alert>
    <div class="info-wrap info-label-width">
      <div class="info-item">
        <div class="label">
          总距离：
        </div>
        <div class="value">
          {{ info.order_calculate_resp.total_distance }}
        </div>
      </div>
      <div class="info-item">
        <div class="label">
          总重量：
        </div>
        <div class="value">
          {{ info.order_calculate_resp.total_weight }}
        </div>
      </div>
      <div class="info-item">
        <div class="label">
          物流单号：
        </div>
        <div class="value">
          {{ info.iss_order.iss_order_no }}
        </div>
      </div>
      <div class="info-item">
        <div class="label">
          预计运费：
        </div>
        <div class="value">
          {{ info.total_fee_after_save }}
        </div>
      </div>
      <div class="info-item">
        <div class="label">
          预计接单时长：
        </div>
        <div class="value">
          {{ formatSecondsToTime(info.query_addr_eta_resp.estimate_grab_second) }}
        </div>
      </div>
      <div class="info-item">
        <div class="label">
          预计取件时长：
        </div>
        <div class="value">
          {{ formatSecondsToTime(info.query_addr_eta_resp.estimate_pick_up_second) }}
        </div>
      </div>
      <div class="info-item">
        <div class="label">
          预计完单时长：
        </div>
        <div class="value">
          {{ formatSecondsToTime(info.query_addr_eta_resp.estimate_receive_second) }}
        </div>
      </div>
      <div class="info-item">
        <div class="label">
          附近闪送员数量：
        </div>
        <div class="value">
          {{ info.query_addr_eta_resp.courier_count }}
        </div>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleCancel">
        取消发起
      </a-button>
      <a-button
        type="primary"
        @click="sendShanSong"
      >
        发起闪送
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { formatSecondsToTime } from '@/utils/time';

import { apiAdminIssOrder, apiAdminIssOrderPlace, apiServerIssOrder, apiServerIssOrderPlace } from '../../services';

const route = useRoute();
const isSuper = route.query.role === 'super';

const bindVisible = ref(false);

const emits = defineEmits(['success', 'cancel']);

function handleCancel() {
  bindVisible.value = false;
  emits('cancel');
}
async function sendShanSong() {
  // 提交订单
  const apiIssOrderPlace = isSuper ? apiAdminIssOrderPlace : apiServerIssOrderPlace;
  await apiIssOrderPlace({ order_no: String(info.value.iss_order.order_no) });
  bindVisible.value = false;
  message.success('操作成功');
  emits('success');
}

const info = ref({ query_addr_eta_resp: {}, order_calculate_resp: {}, iss_order: {} });
const timeoutText = ref('');

// 将秒级时间戳转换为 dayjs 对象，并加上 30 分钟（1800 秒）
function formatTimeScoped(time: number) {
  timeoutText.value = dayjs.unix(time).add(30, 'minute').format('YYYY-MM-DD HH:mm:ss');
}

async function sendOrder(orderId: number, serverId: number, formData: any) {
  // 预下单
  const apiIssOrder = isSuper ? apiAdminIssOrder : apiServerIssOrder;
  const { data } = await apiIssOrder({
    order_id: orderId,
    server_store_id: formData.server_store_id,
    store_contact_id: formData.store_contact_id,
    appointment_date: formData.ss_send_start_time,
    channel_code: 'ZL',
  });
  info.value = data;
  formatTimeScoped(info.value.iss_order.created_at);
  bindVisible.value = true;
}
defineExpose({
  sendOrder,
});
</script>

<style lang="less" scoped>
.info-wrap {
  .info-item {
    display: flex;
    margin-top: 16px;
    .label {
      width: 112px;
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: right;
    }

    .value {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }

    :deep(.ant-image-img) {
      width: 98px !important;
      height: 98px !important;
      border-radius: 8px;
    }
  }
}
</style>
