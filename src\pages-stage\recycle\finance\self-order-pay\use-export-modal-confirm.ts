import { Modal, message } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
//requestfn:请求方倒入方法
//url:跳转我的导出中心 ${route.query.origin}/super/async-export/index
//confirm1Pramas对话框配置
const useExportModalConfirm = (requestfn: () => any, url: string, confirm1Pramas?: any) => {
  const defaultparma = {
    title: '账单数据导出',
    icon: createVNode(ExclamationCircleOutlined),
    content: '请确认是否导出账单数据？',
    okText: '确认',
    cancelText: '取消',
  };
  const confirmPramas = { ...defaultparma, ...confirm1Pramas };
  const fn = () => {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await requestfn();
        resolve(res);
      } catch (error: any) {
        if (error.indexOf('timeout of') !== -1) {
          message.error('请求超时，请重试。');
        }
        reject(error);
      }
    });
  };
  const down = () =>
    Modal.confirm({
      ...confirmPramas,
      onOk: () => {
        return fn().then((res: any) => {
          if (res.status == 0) {
            Modal.confirm({
              title: '提示',
              content: '已在后台异步导出，需要跳转到我的导出查看吗？',
              onOk: () => {
                // 统一跳转到这个页面去排队导出
                window.open(url);
                // ${route.query.origin}/super/async-export/index
              },
            });
          }
          return res;
        });
      },
    });

  return { down };
};

export default useExportModalConfirm;
