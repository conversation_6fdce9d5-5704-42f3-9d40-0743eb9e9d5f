<template>
  <a-modal
    v-model:visible="visible"
    :mask-closable="false"
    title="检测发货"
    @ok="confirm"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="下发仓库"
          name="warehouse_id"
        >
          <a-select
            v-model:value="formData.warehouse_id"
            :options="options"
            placeholder="请选择"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';

import { useModal } from '@/hook/component/use-modal';

import { fetchChangeOrderMark, getOrderWarehouse, getWarehouseOptions } from '../services';

const props = defineProps<{
  orderId: string;
}>();

const emit = defineEmits<{
  (e: 'success', value: string): void;
}>();

const formRef = ref<FormInstance>();

const { loading, visible, confirm } = useModal(async () => {
  // 确认提交
  await formRef.value?.validate();
  const res = await fetchChangeOrderMark({
    orderId: props.orderId,
    type: 'mark',
    markType: 299,
    value: 1,
    warehouse_id: formData.value.warehouse_id,
    is_lock: 2,
  });
  message.success(res.data.msg);
  emit('success', props.orderId);
});

const formData = ref<{ warehouse_id?: string }>({});
const options = ref<{ label: string; value: string }[]>([]);
const rules = {
  warehouse_id: [{ required: true, message: '请选择下发仓库', trigger: 'blur' }],
};

/** 打开弹窗 */
function openModal() {
  visible.value = true;
  setWarehouseId();
  getOptions();
}

/** 设置选择仓库 */
function setWarehouseId() {
  loading.value = true;
  getOrderWarehouse({ order_id: props.orderId })
    .then(res => {
      formData.value.warehouse_id = res.data?.warehouse_id || undefined;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 获取仓库下拉选择 */
function getOptions() {
  if (!options.value.length) {
    getWarehouseOptions().then(res => {
      options.value = res.data;
    });
  }
}

defineExpose({ openModal });
</script>
