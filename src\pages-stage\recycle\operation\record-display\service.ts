import { GET, POST } from '@/services/api';
import { IModalState, ITransactionRecordDetail } from './data';

type TModal = { id: string };

/** 删除接口 */
export const deleteAction = <T extends TModal>(params: T) => {
  return GET<T, []>('/super/recycle/order-record/del', params);
};

/** 创建接口 */
export const createModalContent = <T extends IModalState>(data: T) => {
  return POST<T, []>('/super/recycle/order-record/create', data);
};

/** 详情接口 */
export const getModalContent = <T extends TModal>(params: T) => {
  return GET<T, ITransactionRecordDetail>('/super/recycle/order-record/detail', params);
};

/** 创建时获取订单信息 */
export const getOrderInfo = <T extends { recycle_order_id: string }>(params: T) => {
  return GET<T, ITransactionRecordDetail>('/super/recycle/order-record/get-order', params);
};

/** 切换状态 */
export const toggleRecordStatus = <T extends TModal>(data: T) => {
  return POST<T, []>('/super/recycle/order-record/use-or-stop', data);
};

/** 切换弹窗中订单评价状态 */
export const toggleRecordModalOrderStatus = <T extends TModal & { show_comment: string }>(data: T) => {
  return POST<T, []>('/super/recycle/order-record/modify', data);
};
