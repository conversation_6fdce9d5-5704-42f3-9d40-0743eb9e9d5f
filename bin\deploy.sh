#!/bin/sh

# 判断如果是test分支，执行webhook
get_branch=`git symbolic-ref --short -q HEAD`
user=`git config user.name`
go_dev="$1"
if [ -z "$1" ]; then go_dev="1"; fi;
read -p "🦊请输入需要部署的开发服编号，(例如输入：2。将部署到 test2-admin-vue.rrzuji.com。默认编号为1。):" test
if [ -z "$test" ]; then test="1"; fi;
echo "👏云效仓库'$get_branch'分支将自动打包部署到开发服['$test']"
curl --header "Content-Type: application/json" --request POST --data '{ "branch": "'$get_branch'", "test": "'$test'", "user": "'$user'", "GO_DEV": "'$go_dev'" }' http://flow-openapi.aliyun.com/pipeline/webhook/8m3YTregBdoIbKXSCaa0
echo '...'
echo 👏请稍后打开开发服页面查看，访问地址：https://dev${test}-admin.rruzji.com
cur_dateTime="`date +%m月%d日_%H点%M分`"
echo 当前时间 $cur_dateTime ，打包生效约耗时3-5分钟.

# 流水线shell命令
# get_branch=`git symbolic-ref --short -q HEAD`
# if [ "$get_branch"x != "$branch"x ]
# then
#     git config remote.origin.fetch "+refs/heads/*:refs/remotes/origin/*"
#     git fetch origin $branch
#     git checkout -b $branch origin/$branch
# else
#     echo 'next'
# fi

# if [ "$get_branch" == "test" ]; then
#   curl --header "Content-Type: application/json" --request POST --data '{}' http://flow-openapi.aliyun.com/pipeline/webhook/bLgskkwg86nY3K8NHd8M
#   echo '...'
#   echo 👏云效仓库test分支将自动打包部署到测试服 https://test-admin-vue.rrzuji.com ，请稍后打开查看...
#   cur_dateTime="`date +%m月%d日_%H点%M分`"
#   echo 当前时间 $cur_dateTime ，打包生效约耗时3分钟.
# elif [ "$get_branch" == "develop" ]; then
#   read -p "🦊是否需要执行云效流水线操作，云效develop分支将自动打包，打包后合并master分支(y/n):" choice
#     if [ "$choice" == "y" ] || [ "$choice" == "Y" ]
#       then
#         read -p "🐼请输入发布版本号，格式「主版本号.次版本号.修订号」。(如果不需要版本号，请按回车键或输入0):" USERINPUT
#         if [ -z "$USERINPUT" ]; then USERINPUT="0"; fi;
#         curl --header "Content-Type: application/json" --request POST --data '{ "VERSION": "'$USERINPUT'" }' http://flow-openapi.aliyun.com/pipeline/webhook/w33AcPTR9EeTkgd9LH1i
#         echo '...'
#         echo 👏云效仓库develop分支将自动打包，打包后合并master分支
#         cur_dateTime="`date +%m月%d日_%H点%M分`"
#         echo 当前时间 $cur_dateTime ，打包生效约耗时3分钟.
#     fi;
# elif [ "$get_branch" == "master" ]; then
#   echo 😅master分支不做操作，退出
# else
#   echo "👏云效仓库'$get_branch'分支将自动打包部署到测试服"
#   read -p "🦊请输入需要部署的测试服编号，(例如输入：2。将部署到 admin-vue2.rruzji.net。默认编号为1。):" test
#   if [ -z "$test" ]; then test="1"; fi;
#   curl --header "Content-Type: application/json" --request POST --data '{ "branch": "'$get_branch'", "test": "'$test'" }' http://flow-openapi.aliyun.com/pipeline/webhook/9tKVMEmxYAs6fesFuBuT
#   echo '...'
#   echo 👏云效仓库${get_branch}分支将自动打包部署到测试服 admin-vue${test}.rruzji.net，请稍后打开测试服页面查看...
#   cur_dateTime="`date +%m月%d日_%H点%M分`"
#   echo 当前时间 $cur_dateTime ，打包生效约耗时3分钟.
# fi;


# 云效部署
# # 12-16号无效
# # export TEST=$(echo $test | base64 -d)

# # 先解压到临时目录,任何一个非项目根目录，都行
# rm -rf /alidata/www/admin-vue/10/src/*
# tar zxvf /alidata/www/dist.tgz -C /alidata/www/admin-vue/10/src

# # 找出.testconfig文件
# for file in $(ls /alidata/www/admin-vue/10/src)
# do
#     if [ "${file##*.}" = "testconfig" ]; then
#         export TEST=${file%%.*}
#     fi
# done

# # 部署到测试环境
# rm -rf /alidata/www/admin-vue/${TEST}/dist/*
# tar zxvf /alidata/www/dist.tgz -C /alidata/www/admin-vue/${TEST}/dist

# # 删除打包压缩包
# rm -rf /alidata/www/dist.tgz
