<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px 24px' }"
    title="导航条设置"
  >
    <template #extra>
      <a-button
        style="margin-right: 10px"
        @click="updateNavInfo"
      >
        <template #icon>
          <SaveOutlined />
        </template>
        保存修改
      </a-button>
      <a-button
        type="primary"
        @click="toPushPage"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        新建页面
      </a-button>
    </template>
    <div class="name">
      <div class="label">
        店铺名字
      </div>
      <a-input
        v-model:value="TblShopNav.shop_name"
        class="mt-8"
        placeholder="请输入"
        type="text"
      />
    </div>
    <div class="list mt-24">
      <div class="label">
        拖动可重新排序类目
      </div>
      <a-spin :spinning="spinning">
        <div class="mt-6">
          <drag
            active-background="rgba(55, 119, 255, 0.2)"
            direction="vertical"
            item-width="auto"
            :items="items"
            target-active-background="#3777ff"
            @on-sort="update"
          >
            <template #item="{ item }">
              <div class="item">
                <a-space :size="4">
                  <DragOutlined style="font-size: 16px" />
                  <div class="text">
                    {{ item.name }}
                  </div>
                </a-space>
                <a-space :size="8">
                  {{ item.tip }}<a-checkbox
                    v-model:checked="item.is_check"
                    @change="update"
                  />
                </a-space>
              </div>
            </template>
          </drag>
        </div>
      </a-spin>
    </div>
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { DragOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons-vue';

import { fetchNavInfo, saveNavInfo } from './service';

interface ItemsType {
  id: number;
  is_check: boolean;
  name: string;
  tip: string;
  type?: string;
}

const items = ref<ItemsType[]>([]);
const spinning = ref<boolean>(false);

const getNavInfo = async () => {
  spinning.value = true;
  const res = await fetchNavInfo();
  items.value = res.data.items;
  TblShopNav.shop_name = res.data.shop_name;
  spinning.value = false;
};
getNavInfo();

const updateNavInfo = async () => {
  await update();
  const res = await saveNavInfo({
    TblShopNav,
  });
  message.success(res.message);
  spinning.value = true;
  await getNavInfo();
  TblShopNav.shop_name = '';
};

const TblShopNav = reactive<{
  data_config: string;
  shop_name: string;
}>({
  data_config: '',
  shop_name: '',
});

const update = async () => {
  TblShopNav.data_config = JSON.stringify(
    items.value
      .filter(item => item.is_check)
      .map(item => ({
        name: item.name,
        id: item.id,
        type: item.type,
      })),
  );
};

const { query } = useRoute();
const { origin } = query;

const toPushPage = () => {
  const url = `${origin}/shop/page/create`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};
</script>

<style lang="less" scoped>
.list {
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 -6px;
    padding: 16px 24px;
    color: rgba(6, 21, 51, 0.65);
    background: #f5f7fa;
    border-radius: 4px;

    .text {
      font-weight: 400;
      font-size: 14px;
    }
  }
}
.mt-24 {
  margin-top: 24px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-6 {
  margin-top: 6px;
}
.label {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 14px;
}
</style>
