export interface IBindContactList {
  config_contact_id: string,
  name: string,
  contact: string,
}

export interface IBindListItem{
  id: string;
  template_code: string;
  business_type: string;
  message_type: number[];
  title: string;
  content: string;
  sms_code: string;
  weixin_code: string;
  status: string;
  business_type_trans: string;
  message_type_trans: Messagetypetran[];
}
interface Messagetypetran {
  label: string;
  value: number;
}


