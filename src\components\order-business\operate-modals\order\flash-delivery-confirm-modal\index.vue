<template>
  <a-modal
    title="闪送预发起单"
    :visible="visible"
    width="620px"
    @cancel="visible=false"
  >
    <a-form
      class="flash-delivery-form"
    >
      <div class="tips-container">
        <div class="tips-title">
          提示：
        </div>
        <div class="tips-content">
          预发起单超时时间为【{{ dayjs.unix(orderInfo.iss_order?.created_at).add(30, 'minute').format('YYYY-MM-DD HH:mm:ss') }}】，如未在超时时间前【发起闪送】，当前预发起单将失效，需要重新通过【闪送】生成新的预发起单
        </div>
      </div>
      <a-form-item
        label="总距离："
        name="totalDistance"
      >
        <span>{{ orderInfo.order_calculate_resp?.total_distance }}</span>
      </a-form-item>

      <a-form-item
        label="总重量："
        name="totalWeight"
      >
        <span>{{ orderInfo.order_calculate_resp?.total_weight }}</span>
      </a-form-item>

      <a-form-item
        label="闪送单号："
        name="flashDeliveryNo"
      >
        <span>{{ orderInfo.iss_order?.iss_order_no }}</span>
      </a-form-item>

      <a-form-item
        label="预计运费："
        name="estimatedFreight"
      >
        <span>{{ orderInfo.order_calculate_resp?.total_fee_after_save }}</span>
      </a-form-item>
      <a-form-item
        label="预计接单时长："
        name="estimatedOrderTime"
      >
        <span>{{ orderInfo.query_addr_eta_resp?.estimate_grab_second }}</span>
      </a-form-item>
      <a-form-item
        label="预计取件时长："
        name="estimatedPickupTime"
      >
        <span>{{ orderInfo.query_addr_eta_resp?.estimate_pick_up_second }}</span>
      </a-form-item>
      <a-form-item
        label="预计完单时长："
        name="estimatedFinishTime"
      >
        <span>{{ orderInfo.query_addr_eta_resp?.estimate_receive_second }}</span>
      </a-form-item>

      <a-form-item
        label="附近闪送员数量："
        name="nearbyCouriers"
      >
        <span>{{ orderInfo.query_addr_eta_resp?.courier_count }}</span>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="handleCancel">
        取消发起
      </a-button>
      <a-button
        :loading="loading"
        type="primary"
        @click="confirmFlashDelivery"
      >
        发起闪送
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref,watch } from 'vue';
import dayjs from 'dayjs';

import { useVModel } from '@/hook';

import { apiAdminAbortOrder } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderInfo: {
    type: Object,
    default: () => ({}),
  },
  apiResult: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:visible', 'confirm']);
const visible = useVModel(props, 'visible', emit);

const loading = ref(false);
watch(
  () => props.apiResult,
  (value) => {
    if (value?.success) {
      loading.value = false;
    }
  },
  { immediate: true }
);

const handleCancel = async () => {
  visible.value = false;
  const {iss_order} = props.orderInfo;
  const params = { channel_code: iss_order?.channel_code, iss_order_no: iss_order?.iss_order_no }
  await apiAdminAbortOrder(params)
  emit('update:visible', false);
};

// 确认发起闪送
const confirmFlashDelivery = () => {
  loading.value = true;
  emit('confirm', props.orderInfo);
};
</script>

<style scoped lang="scss">
.flash-delivery-form {
  padding: 10px;
}

.tips-container {
  margin-bottom: 20px;
  padding: 10px;
}

.tips-title {
  margin-bottom: 5px;
  font-weight: bold;
}

.tips-content {
  color: #333;
  font-size: 14px;
  text-indent: 2em;
}
:deep(.ant-form-item) {
  margin-bottom: 0;
}
:deep(.ant-form-item-label) {
  width: 120px;
  margin-bottom: 0;
  text-align: left;
}


.price {
  color: #f5222d;
  font-weight: bold;
}
</style>
