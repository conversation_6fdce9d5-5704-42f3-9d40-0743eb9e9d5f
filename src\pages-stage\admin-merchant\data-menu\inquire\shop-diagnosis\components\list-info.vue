<template>
  <div
    :class="isLarge ? `list-info-large ${infoClass} list-info` : `list-info-hover ${infoClass} list-info`"
    :style="{ backgroundColor: isLarge ? dataObj.notify_type[item.notifyType].backgroundColor : 'transparent' }"
  >
    <div class="list-info-left ellipsis">
      <a-tag
        class="tag"
        :style="{
          color: dataObj.notify_type[item.notifyType].color,
          borderColor: dataObj.notify_type[item.notifyType].color,
          backgroundColor: dataObj.notify_type[item.notifyType].backgroundColor,
        }"
      >
        {{ dataObj.notify_type[item.notifyType].label }}
      </a-tag>
      <div :class="[1, 2, 3, 12].includes(item.indicator) ? 'font-black-65' : 'font-black-85'">
        {{ dataObj.indicator[item.indicator] }}<span v-if="colon">：</span>
      </div>
      <div v-if="item.value">
        <template v-if="item.indicator === 1">
          {{ item.value[0].is_open_dispatch ? '开启' : '关闭' }}
        </template>
        <template v-if="item.indicator === 2">
          {{ item.value[0].today_schedule_plan }}
        </template>
        <template v-if="item.indicator === 3">
          {{ item.value[0].today_order_num }}/{{ item.value[0].today_order_num_limit }}
        </template>
        <template v-if="item.indicator === 12">
          客服手机号（{{ filterPhone(item.value[0].phone) }}）未备案
        </template>
      </div>
      <template
        v-if="[1, 2].includes(item.indicator) && item.abnormal_affect_analysis?.length && item.notifyType !== 3"
      >
        <div
          v-for="(penalty, penaltyIndex) in item.abnormal_affect_analysis[0]!.penalty_contents"
          :key="penaltyIndex"
          class="status-info"
        >
          <InfoCircleOutlined style="margin-right: 6px" />{{ penalty.penalty }}
        </div>
      </template>

      <template v-if="[3].includes(item.indicator) && item.notifyType !== 3">
        <div class="status-info">
          <InfoCircleOutlined style="margin-right: 6px" />接单日上限较低，为了避免错失流量，建议商家提高接单日上限。
        </div>
      </template>

      <slot name="advice" />
      <a-tooltip placement="topLeft">
        <template #title>
          平台建议：{{ adviceMap[item.indicator] }}
        </template>
        <span
          v-if="item.notifyType !== 3 && adviceMap[item.indicator]"
          class="yellow-color ellipsis"
        >&nbsp;平台建议：{{ adviceMap[item.indicator] }}</span>
      </a-tooltip>
    </div>
    <slot name="right" />
  </div>

  <slot />
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';

import { adviceMap } from '../config';
import { IDataObj, IModuleLinesItem } from '../data';

defineProps({
  item: {
    type: Object as PropType<IModuleLinesItem>,
    required: true,
  },
  isLarge: {
    type: Boolean,
    default: false,
  },
  colon: {
    type: Boolean,
    default: true,
  },
  dataObj: {
    type: Object as PropType<IDataObj>,
    required: true,
  },
  infoClass: {
    type: String,
    default: '',
  },
});

function filterPhone(value: string) {
  return value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

.font-black-65 {
  color: @font-black-65;
}

.font-black-85 {
  color: @font-black-85;
}

.yellow-color {
  color: #fa9a14;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.list-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  border-radius: 4px;

  &-left {
    display: flex;
    align-items: center;

    .tag {
      width: 52px;
      margin-right: 8px;
      font-size: 12px;
      text-align: center;
    }

    .status-info {
      margin-left: 16px;
      padding: 2px 8px;
      color: #f5222d;
      font-size: 12px;
      background-color: #fff1f0;
      border-radius: 4px;
    }
  }
}

.list-info-large {
  padding: 8px 16px;
}

.list-info-hover {
  &:hover {
    background-color: #f8fbff !important;
  }
}
</style>
