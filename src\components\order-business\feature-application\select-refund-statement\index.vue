<!-- Referenced: 申请退款 | 订单退款 | 取消订单 | 工单申请 -->
<template>
  <div class="select-refund-statement">
    <div class="title">
      {{ title }}
    </div>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="false"
      row-key="id"
      :row-selection="hasExternalData ? undefined : rowSelection"
      :scroll="{ x: 1450 }"
    >
      <template #headerCell="{ column }">
        <template v-if="column.tooltip">
          {{ column.title }}
          <a-tooltip :title="column.tooltip">
            <QuestionCircleOutlined />
          </a-tooltip>
        </template>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.discountKey">
          <div class="text-align-center">
            <div>{{ normalizeMoney(record[column.dataIndex]) }}</div>
            <div class="discount-text">
              （含优惠：{{ normalizeMoney(record[column.discountKey]) }}）
            </div>
          </div>
        </template>
      </template>
    </a-table>

    <div
      v-if="!externalData"
      class="result"
    >
      当前选择流水最大支持退款￥{{ normalizeMoney(totalSurplusHasRefund) }}
    </div>

    <template v-if="externalData">
      <div class="refund-money">
        申请退款租金: ￥{{ externalData.refund_money }}
      </div>
    </template>
    <template v-else>
      <div class="label">
        申请退款租金
      </div>
      <a-input-number
        v-model:value="formState.refund_rental"
        :disabled="props.inputDisabled"
        :max="props.maxApplyRefundMoney || totalSurplusHasRefund"
        :min="0"
        placeholder="请输入申请退款租金"
        :precision="2"
        style="width: 100%"
      />
      <span
        v-if="totalMinRefundMoney !== undefined"
        class="input-tip"
      >
        本次申请的退款建议最小值：￥{{
          normalizeMoney(totalMinRefundMoney)
        }}。电动车类目订单，超过两周的租期，实行按日计费原则，请申请退款时退款金额不能低于最小值，否则会造成申请失败
      </span>
    </template>

    <div class="description">
      <div>
        当前输入退款租金含优惠金额￥{{ normalizeMoney(totalDiscount) }}，用户实际到账￥{{ normalizeMoney(totalActual) }}
      </div>
      <div>执行退款顺序按勾选的流水号自上而下执行；</div>
      <div>退款需要财务审核，大概1~3个工作日处理完成（客服无需在有道备注）</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message, TableProps } from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

import { columns } from './config';
import { ERefundType, IExternalData, IRefundData, IRefundItem } from './data.d';
import { applyRefund, applyRefundServer, getRefundList, getRefundListServer } from './services';

const props = withDefaults(
  defineProps<{
    visibleDependency?: boolean | null;
    orderId?: string | null;
    type: ERefundType;
    externalData?: IExternalData | null;
    maxApplyRefundMoney: number; // 申请的最大可退金额
    inputDisabled: false; // 是否禁止输入退款金额
  }>(),
  {
    visibleDependency: null,
    orderId: null,
    externalData: null,
  },
);

const title = computed(() => (props.type === ERefundType.WorkOrderApplication ? '已选择退款流水' : '选择退款流水'));

const route = useRoute();
const isSuper = route.query.role === 'super';

/** 是否处在弹窗中，控制数据更新 */
const inModal = props.visibleDependency !== null;
/** 若处在工单详情页，所有数据由外部传入（工单详情接口中携带）, 若该属性不为空应优先使用并调整逻辑 */
const hasExternalData = !!props.externalData;

const loading = ref(false);
const dataSource = ref<IRefundItem[]>(props.externalData?.account_pay_log_refund_list || []);

/** 校验接口参数，根据已选的流水和申请退款租金，所计算得到的记录 */
const refundData = ref<IRefundData[]>([]);
/** 申请退款租金 */
const formState = reactive<{
  refund_rental: number | undefined;
}>({
  refund_rental: undefined,
});

const selectedRows = ref<IRefundItem[]>([]);
const selectedRowKeys = ref<string[]>([]);

const rowSelection: TableProps<IRefundItem>['rowSelection'] = {
  selectedRowKeys: selectedRowKeys as any,
  getCheckboxProps: record => {
    // 没有剩余可退款金额
    const noAmount = Number(record.surplus_has_refund) === 0;
    // 正常申请退款仅处理除【押金】类型外的其他流水
    // const noDepositAllowed = props.type === ERefundType.ApplyForRefund && Number(record.money_type) === 9;

    return {
      disabled: noAmount,
    };
  },
  onChange: (_selectedRowKeys, _selectedRows) => {
    selectedRowKeys.value = _selectedRowKeys as string[];
    selectedRows.value = _selectedRows.sort((a, b) => Number(a._index) - Number(b._index));
    if (props.inputDisabled) {
      // 如果配置了输入框不可用，则需要自动填充金额
      nextTick(() => {
        formState.refund_rental =
          totalSurplusHasRefund.value > props.maxApplyRefundMoney
            ? props.maxApplyRefundMoney
            : totalSurplusHasRefund.value;
      });
    } else {
      formState.refund_rental = undefined;
    }
  },
};

/** 已选中的剩余可退金额的总计 */
const totalSurplusHasRefund = computed(() => totalOf(selectedRows.value, 'surplus_has_refund'));
const totalMinRefundMoney = computed(() => dataSource.value[0]?.total_min_refund_money);
/** 退款租金含优惠金额的总计 */
const totalDiscount = computed(
  () => props.externalData?.refund_discount_money ?? totalOf(refundData.value, 'refund_discount_money'),
);
/** 用户实际到账的总计 */
const totalActual = computed(
  () => props.externalData?.actual_refund_money ?? totalOf(refundData.value, 'refund_money'),
);

function shrinkPrecision(num: number) {
  return Number(num.toFixed(2));
}

function totalOf<T>(array: T[], key: keyof T) {
  return array.reduce((a, c) => shrinkPrecision(a + Number(c[key])), 0);
}

function normalizeMoney(money?: number | string) {
  return money ? Number(money).toFixed(2) : '0.00';
}

/**
 * 同步“当前输入退款租金含优惠金额【变量1】，用户实际到账【变量2】”，
 * 执行退款顺序按勾选的流水号自上而下执行，
 * 根据输入的【申请退款租金】按照退款顺序计算，当流水剩余可退金额含优惠时，需要分两步计算，先退支付金额，再退优惠部分，
 * 以此类推直到每条流水的剩余可退金额都退满；汇总得到【变量1】，
 * 申请退款租金-【变量1】=【变量2】。
 */
function syncRefundData(refundRental: number | undefined) {
  if (!refundRental) {
    refundData.value = [];
    return;
  }

  const newRefundData: IRefundData[] = [];

  for (let i = 0; i < selectedRows.value.length; i++) {
    const row = selectedRows.value[i];

    const total = Number(row.surplus_has_refund);
    const discount = Number(row.surplus_has_refund_discount);
    const actual = shrinkPrecision(total - discount);

    if (refundRental >= total) {
      // 如果当前金额足以全部退款，则全部退款
      newRefundData.push({
        account_pay_log_id: row.id,
        refund_money: actual,
        refund_discount_money: discount,
      });
      refundRental = shrinkPrecision(refundRental - total);
    } else if (refundRental > 0) {
      // 如果当前金额不足以全部退款，则优先退款实际金额，若还可退回优惠金额，则退款优惠金额
      const partOfDiscount = Math.max(shrinkPrecision(refundRental - actual), 0);
      newRefundData.push({
        account_pay_log_id: row.id,
        refund_money: partOfDiscount ? actual : refundRental,
        refund_discount_money: partOfDiscount,
      });
      refundRental = 0;
    } else {
      // 退款金额不足，跳出循环
      break;
    }
  }

  refundData.value = newRefundData;
}

function reset() {
  selectedRowKeys.value = [];
  selectedRows.value = [];
  formState.refund_rental = undefined;
}

async function fetchData() {
  if (!hasExternalData) {
    try {
      loading.value = true;
      const api = isSuper ? getRefundList : getRefundListServer;
      const { data } = await api({ order_id: props.orderId!, type: props.type });
      setupData(data);
      dataSource.value = data;
    } finally {
      loading.value = false;
    }
  }
}

function setupData(data: IRefundItem[]) {
  // 确保 selectedRows 以正确的顺序被 syncRefundData 使用
  data.forEach((item: IRefundItem, index: number) => (item._index = index));

  // 取消订单场景，需要自动选择所有支付流水号，并且自动填充选择流水下的最大支持退款金额自动填充在【申请退款租金】输入框
  if (props.type === ERefundType.CancelOrder) {
    let hasPreSelected = false;

    data.forEach((item: IRefundItem) => {
      if (Number(item.surplus_has_refund) > 0) {
        selectedRowKeys.value.push(item.id);
        selectedRows.value.push(item);

        hasPreSelected = true;
      }
    });
    if (hasPreSelected) {
      formState.refund_rental = totalSurplusHasRefund.value;
    }
  }
}

/**
 * 外部使用该组件后，提交接口需额外携带 refund_rental、account_pay_log_refund_apply_id 参数，
 * 此方法封装校验和获取逻辑
 */
async function requestParams(notify = true) {
  if (!refundData.value.length) {
    notify && message.warn('请先选择退款流水并填写申请退款租金');
    return null;
  }
  const params = { order_id: props.orderId, type: props.type, refund_data: refundData.value };
  const api = isSuper ? applyRefund : applyRefundServer;

  const { data } = await api(params);
  const { account_pay_log_refund_apply_id } = data;

  return {
    refund_rental: formState.refund_rental,
    account_pay_log_refund_apply_id,
  };
}

function getRefundData(notify = true) {
  if (!refundData.value.length) {
    notify && message.warn('请先选择退款流水并填写申请退款租金');
    return null;
  }
  return {
    refund_data: refundData.value,
    refund_rental: formState.refund_rental,
  };
}

watch(() => formState.refund_rental, syncRefundData);

onMounted(() => {
  fetchData();

  if (inModal) {
    watch(
      () => props.visibleDependency,
      newVal => (newVal && fetchData()) || reset(),
    );
  }
});

defineExpose({
  requestParams,
  getRefundData,
});
</script>

<style lang="less" scoped>
.title {
  margin-bottom: 16px;
  color: #061533d9;
}

.result {
  margin: 16px 0 24px;
  color: #faad14;
}

.refund-money {
  margin-top: 8px;
}

.label {
  margin-bottom: 8px;
}

.description {
  margin: 8px 0;
  color: #********;
}

.text-align-center {
  text-align: center;
}

.discount-text {
  color: #808080;
}

.input-tip {
  color: #3777ff;
}
</style>
