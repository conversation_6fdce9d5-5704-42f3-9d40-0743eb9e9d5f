import type { FormGroupItem } from 'rrz-web-design';

import uploadToOss from '@/utils/oss-upload';

import { EHandleType } from '../store-info/components/shop-qualification/data.d';

export const formGroup: FormGroupItem[] = [
  {
    key: 'server_credential_industry_image',
    fragmentKey: 'renderUpload',
    label: '资质图片',
    elProps: ([disabled]: any[]) => {
      return {
        disabled,
        template: 'pictureCard',
        removeConfirm: true,
        uploadApi: uploadToOss,
        accept: '.jpg,.jpeg,.png',
        hideEntry: 1,
        maxCount: 1,
        showText: '上传',
        valueType: 'file',
      };
    },
    originProps: {
      extra: '支持添加jpg、jpeg、png 格式，上限1张',
      rules: { required: true, message: '请上传资质图片', trigger: 'change' },
    },
  },
  {
    key: 'time',
    label: '许可证有效期',
    fragmentKey: 'renderInput',
  },
];

export const handleTypeMap = {
  [EHandleType.ADD]: '添加行业资质',
  [EHandleType.EDIT]: '编辑行业资质',
  [EHandleType.DETAIL]: '行业资质详情',
};
