<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    title="顺丰取件"
    :visible="visible"
    width="560px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form
      ref="formRef"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item label="订单号">
        {{ orderId }}
      </a-form-item>
      <a-form-item
        label="上门取件时间"
        name="times"
        :rules="[{ required: true, message: '请选择上门取件时间!' }]"
      >
        <a-cascader
          v-model:value="formState.times"
          :options="options"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item
        label="取件地址"
        name="sf_address"
        :rules="[{ required: true, message: '请选择取件地址!' }]"
      >
        <a-select
          v-model:value="formState.sf_address"
          :options="addressOption"
          placeholder="请选择取件地址"
        />
      </a-form-item>
      <a-form-item
        label="收件地址"
        name="receive_address"
        :rules="addressRule"
      >
        <a-form-item-rest>
          <div class="address-box">
            <a-select
              v-model:value="provinceValue"
              :filter-option="filterOption"
              :options="provinceOptions"
              placeholder="请选择省份"
              show-search
              @change="
                () => {
                  formState.is_change_receive = 1;
                  cityValue = null;
                  areaValue = null;
                }
              "
            />
            <a-select
              v-model:value="cityValue"
              :filter-option="filterOption"
              :options="cityOptions"
              placeholder="请选择城市"
              show-search
              @change="
                () => {
                  formState.is_change_receive = 1;
                  areaValue = null;
                }
              "
            />
            <a-select
              v-model:value="areaValue"
              :filter-option="filterOption"
              :options="areaOptions"
              placeholder="请选择市区"
              show-search
              @change="() => (formState.is_change_receive = 1)"
            />
          </div>
          <a-input
            v-model:value="addressValue"
            placeholder="请输入详细地址"
            style="margin-top: 8px"
            @change="formState.is_change_receive = 1"
          />
        </a-form-item-rest>
      </a-form-item>
      <a-form-item
        label="已选服务"
        name="serve"
      >
        <a-checkbox-group
          v-model:value="formState.serve"
          class="serve-group"
        >
          <div>
            <a-checkbox
              name="type"
              value="is_insure"
            >
              保价
            </a-checkbox>
            <a
              class="sf-link"
              :href="origin + '/img/sf_service_insure.png'"
              target="_blank"
            >保价规则</a>
          </div>
          <div>
            <a-checkbox
              name="type"
              value="is_packed"
            >
              包装
            </a-checkbox>
            <a
              class="sf-link"
              href="https://www.sf-express.com/cn/sc/express/value_added_service/installation_services/"
              target="_blank"
            >包装费用说明</a>
          </div>
        </a-checkbox-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { FormInstance, message } from 'ant-design-vue';
import moment from 'moment';

import { globalConfig } from '@/config/global-config';

import { useAddress } from '../composables/use-address';
import { getAddressOptions, sfExpress, sfParams } from '../service';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const route = useRoute();

const submitLoading = ref(false);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref<any>({
  times: [],
  sf_address: null, // 取件地址
  serve: ['is_sign'], // 选择的服务
  user_info: {
    // 用户联系方式
    name: null,
    phone: null,
  },
  // 触发修改地址则修改为1；
  is_change_receive: 0,
});

// 域名
const origin = ref<any>('');

const options = createTimeOptions();

const addressOption = ref([]);
const onCancel = () => emits('update:visible', false);

const {
  provinceValue,
  cityValue,
  areaValue,
  addressValue,

  provinceOptions,
  cityOptions,
  areaOptions,

  filterOption,
} = useAddress();

const addressRule = {
  required: true,
  trigger: 'change',
  validator: () => {
    if (!provinceValue.value || !cityValue.value || !areaValue.value || !addressValue.value) {
      return Promise.reject('请完善收件地址');
    }
    return Promise.resolve();
  },
};

const onOk = () => {
  formRef.value?.validate().then(() => {
    const params: sfParams = {
      order_id: props.orderId,
      is_insure: formState.value.serve.includes('is_insure') ? 1 : 0,
      is_packed: formState.value.serve.includes('is_packed') ? 1 : 0,
      sf_address: formState.value.sf_address,
      send_start_time: `${formState.value.times[0]} ${formState.value.times[1]}`,
      is_change_receive: formState.value.is_change_receive,
      receive_address: JSON.stringify({
        ...formState.value.user_info,
        province_name: provinceValue.value,
        city_name: cityValue.value,
        area_name: areaValue.value,
        addr_detail: addressValue.value,
      }),
    };
    submitLoading.value = true;
    sfExpress(params)
      .then(() => {
        onCancel();
        message.success('预约成功');
        emits('onLoadList');
      })
      .finally(() => (submitLoading.value = false));
  });
};

onMounted(() => {
  origin.value = route.query.origin || globalConfig.baseUrl;
  getAddressOptions({ order_id: props.orderId }).then(({ data }) => {
    const { sf_send_address_list, customer_address } = data || {};
    addressOption.value = (sf_send_address_list || []).reduce((acc: any[], curr: string, index: number) => {
      acc.push({
        lable: index,
        value: curr,
      });
      return acc;
    }, []);
    // 收件地址回显；
    if (customer_address) {
      formState.value.user_info.name = customer_address.name;
      formState.value.user_info.phone = customer_address.phone;
      provinceValue.value = customer_address.province_name;
      cityValue.value = customer_address.city_name;
      areaValue.value = customer_address.area_name;
      addressValue.value = customer_address.addr_detail;
    }
  });
});

function createTimeOptions(start = 8, end = 19) {
  // 生成配置信息
  let time: any[] = [];
  for (let i = start; i < end; i++) {
    time.push({
      id: i,
      value: `${i < 10 ? '0' + i : i}:00:00`,
      label: `${i < 10 ? '0' + i : i}:00-${i + 1 < 10 ? '0' + (i + 1) : i + 1}:00`,
    });
  }
  const options = ['今天', '明天', '后天'].map((item, index) => {
    return {
      value: moment().add(index, 'days').format('YYYY-MM-DD'),
      label: item,
      children: time,
    };
  });
  const todayHour = moment().hour();
  // 当前时间是否大于最大时间 ？ 移除今天 : 过滤今天时间
  todayHour >= end ? options.splice(0, 1) : (options[0].children = time.filter(item => item.id > todayHour));
  return options;
}
</script>
<style lang="less" scoped>
.serve-group {
  display: flex;
  flex-direction: column;
}

.sf-link {
  margin-left: 8px;
  color: #22d4c5;
  text-decoration: underline;
  cursor: pointer;
}

.address-box {
  display: flex;
}
</style>
