<template>
  <div class="calendar-container">
    <div class="wrapper">
      <div class="flex-wrap flex-y-center flex-x-justify">
        <!--  运营周历标题  -->
        <div class="title flex-wrap flex-y-center flex-gap-16">
          <img
            class="calendar-icon"
            src="https://img1.rrzuji.cn/uploads/scheme/2409/07/m/fEl9xL5S9FLxYGfXj2BJ.png"
          >
          <div class="flex-wrap flex-y-center flex-gap-8">
            <span>营业周历</span>
            <a-tooltip
              v-model:visible="tipsVisible"
              title="你可点击此处查看营业周历指引"
            >
              <QuestionCircleOutlined
                class="question-icon"
                @click="toggleCalendarModal"
              />
            </a-tooltip>
          </div>
        </div>
        <!-- 接单状态控制 -->
        <div class="flex-end flex-wrap flex-y-center flex-gap-16">
          <div
            class="text-link flex-wrap flex-y-center flex-gap-4"
            @click="openParentPage"
          >
            <SettingOutlined style="transform: rotate(-30deg)" />
            <span>接单高级设置</span>
          </div>

          <div class="flex-wrap flex-y-center flex-gap-4">
            <span>接单状态</span>
            <a-tooltip placement="bottom">
              <template #title>
                <span> {{ tooltipText }} </span>
              </template>
              <InfoCircleOutlined style="color: rgba(6, 21, 51, 0.45)" />
            </a-tooltip>
            ：
            <a-switch
              :checked="receiveStatus?.is_dispatch"
              checked-children="开启"
              un-checked-children="关闭"
              @change="handleReceiveChange"
            />
          </div>
        </div>
      </div>

      <div class="switch-area flex-wrap flex-y-center">
        <!--  运营周历  -->
        <a-radio-group v-model:value="selected">
          <a-radio
            v-for="(item, index) in week"
            :key="index"
            :class="[
              'date-item',
              {
                current: calendar.current_week === item.value,
                business: calendar[item.value]?.type === 1,
                platform: calendar[item.value]?.type === 3,
              },
            ]"
            :value="item.value"
          >
            {{ item.label }}
          </a-radio>
        </a-radio-group>
        <!--  营业时间  -->
        <div class="context flex-wrap flex-y-center">
          <div
            v-if="selectedDay?.type === 3"
            class="platform"
          >
            【平台配置】
          </div>
          <span>{{ currentContext }}</span>
          <span
            v-if="showReleaseBtn"
            class="text-link underlined"
            :style="{
              color: applyTextData[calendar.relieve_status]?.color,
              marginLeft: '8px',
            }"
            @click="openApplyModal"
          >
            {{ applyTextData[calendar.relieve_status]?.text }}
          </span>
        </div>
      </div>

      <BusinessCalendarModal
        v-model:visible="visible"
        :calendar="calendar"
        @show-tips="toggleTipsVisible"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, type PropType, ref } from 'vue';
import { InfoCircleOutlined, QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons-vue';

import { basicModal } from '@/components/order-business/basic-modals';
import { useUnityModal } from '@/components/unity-modal';
import { useBoolean } from '@/hook';

import { useReceiveInfo } from '../composables/use-receive-info';
import type { ICalendar } from '../data';
import BusinessCalendarModal from './business-calendar-modal.vue';

const week = [
  { label: '周一', value: 'Monday' },
  { label: '周二', value: 'Tuesday' },
  { label: '周三', value: 'Wednesday' },
  { label: '周四', value: 'Thursday' },
  { label: '周五', value: 'Friday' },
  { label: '周六', value: 'Saturday' },
  { label: '周日', value: 'Sunday' },
];

const props = defineProps({
  calendar: {
    type: Object as PropType<ICalendar>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

const { onToggleComponent } = useUnityModal();

const {
  receiveStatus,
  applyTextData,
  tooltipText,
  getReceiveData,
  openParentPage,
  handleReceiveChange,
} = useReceiveInfo();

/** 当天营业情况 */
const selected = ref();
/** 选择的运营日信息 */
const selectedDay = computed(() => props.calendar[selected.value]);
/** 选择的运营日运营时间以及单量上限  */
const currentContext = computed(() => {
  let { context } = selectedDay.value || {};
  const isCurrent = props.calendar.current_week === selected.value;
  if ([1, 3].includes(selectedDay.value?.type)) {
    context += ' | 日订单上限：';
    isCurrent && (context += `${props.calendar.day_num || 0} / `);
    context += `${selectedDay.value?.limit}单`;
  }
  return context;
});
/** 是否可解除平台配置申请 */
const showReleaseBtn = computed(() => {
  const { order_auth, relieve_status } = props.calendar;
  return selectedDay.value?.type === 3 && order_auth === '2' && relieve_status < 3;
});

/** 打开解除平台配置申请 */
function openApplyModal() {
  onToggleComponent(basicModal.platformConfigApply, {
    businessId: props.calendar.business_id,
    onResetOptions: () => emit('refresh'),
  });
}

/** 营业周历弹窗 */
const { value: visible, toggle: toggleCalendarModal } = useBoolean();
const { value: tipsVisible, toggle: toggleTipsVisible } = useBoolean();

onMounted(() => {
  selected.value = props.calendar.current_week;
  getReceiveData();
});
</script>

<style lang="less" scoped>
.holiday-tag {
  padding: 1px 8px;
  color: #faad14;
  background: #fffbe6;
  border: none;
}

.flex-end {
  margin-left: auto;
}

.calendar-container {
  padding: 3px;
  background-color: #fff;
  border: 1px solid rgba(73, 131, 255, 0.5);
  border-radius: 6px 6px 6px 6px;

  .wrapper {
    padding: 12px 16px 26px;
    background: #f0f9ff url(https://img1.rrzuji.cn/uploads/scheme/2409/07/m/SJGRkYnF462XEjdrIgjq.png) no-repeat 0 -30px;
    background-size: 100px 100px;
    border: 1px solid rgba(164, 193, 255, 0.5);
    border-radius: 4px 4px 4px 4px;
  }

  .title {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;

    .calendar-icon {
      width: 32px;
      height: 32px;
    }

    .question-icon {
      color: rgba(6, 21, 51, 0.45);
      font-size: 14px;
    }
  }

  .switch-area {
    margin-top: 12px;

    .date-item {
      padding: 2px 4px;
      background: #f0f9ff;
      border: 1px solid rgba(6, 21, 51, 0.15);
      border-radius: 4px 4px 4px 4px;
      transition: all 0.3s;

      &:hover {
        background: var(--ant-primary-color-outline);
      }

      :deep(.ant-radio) {
        display: none;
      }

      &::after {
        position: absolute;
        bottom: -13px;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(6, 21, 51, 0.25);
        border-radius: 50%;
        transform: translateX(-50%);
        content: '';
      }

      // 当天
      &.current {
        color: var(--ant-primary-color);
        border-color: var(--ant-primary-color);
      }

      // 有营业任务
      &.business::after {
        background: var(--ant-primary-color);
      }

      // 平台配置
      &.platform::after {
        background: var(--ant-warning-color);
      }

      // 选中
      &.ant-radio-wrapper-checked {
        color: #fff;
        background: var(--ant-primary-color);
        border-color: var(--ant-primary-color);
      }
    }

    .context {
      margin-left: 8px;
      color: rgba(6, 21, 51, 0.65);

      .platform {
        color: var(--ant-warning-color);
      }
    }
  }
}
</style>
