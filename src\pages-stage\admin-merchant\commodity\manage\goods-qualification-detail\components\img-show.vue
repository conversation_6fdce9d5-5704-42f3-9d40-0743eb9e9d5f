<template>
  <a-upload
    :file-list="fileList"
    list-type="picture-card"
    :show-upload-list="{
      showPreviewIcon: true,
      showDownloadIcon: true,
      showRemoveIcon: false,
    }"
  >
    <template #downloadIcon="{ file }">
      <DownloadOutlined @click.stop="handleDownload(file)" />
    </template>
  </a-upload>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { DownloadOutlined } from '@ant-design/icons-vue';

import { downloadFile } from '@/utils/file-export';

import { TImage } from '../data.d';
const props = defineProps<{
  imgs: any[];
  imgKey?: string;
}>();
const fileList = ref<TImage[]>([]);

async function handleDownload(item: any) {
  const { url, name } = item;
  const res = await fetch(url);
  const blob = await res.blob();
  downloadFile(blob, name);
}

watch(
  () => [props.imgs, props.imgKey],
  () => {
    if (props.imgs && props.imgs?.length > 0) {
      const key = props.imgKey;
      const haveKey =
        key && props.imgs.every((item: any) => typeof item !== 'string' && Object.keys(item).includes(key));
      const isStringArr = props.imgs.every((item: any) => typeof item === 'string');
      if (isStringArr || haveKey) {
        fileList.value = props.imgs.map((item: Record<string, any>, index: number) => {
          return {
            uid: index,
            status: 'done',
            name: `file${index}.png`,
            url: haveKey ? item[key] : item,
          };
        });
      } else {
        console.warn('无效的imgKey或无效的imgs');
      }
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
</script>
