import { ref, onMounted, onBeforeUnmount } from 'vue';
import { loadG2 } from '@/utils/static-load';
import { debounce } from '@/utils/base';
import { getPillarDetail } from '../service';

type TParams = {
  element: string;
  url: string;
  searchForm?: any;
};

export default function (params: TParams) {
  const { element, url, searchForm } = params;
  const chartInfo = ref({});
  const loading = ref<boolean>(false);
  const isEmpty = ref<boolean>(false);
  let searchParams = {};

  let chart: any = null;

  const createCharts = async () => {
    chart?.destroy();
    loading.value = true;
    if (searchForm && searchForm.date) {
      searchParams = { ...searchForm };
      searchParams['start_at'] = searchForm.date[0];
      searchParams['end_at'] = searchForm.date[1];
      delete searchParams.date;
    }

    const { data } = await getPillarDetail(url, searchParams);
    chartInfo.value = data;
    loading.value = false;
    // 若为空数据，则跳过可视图渲染；
    isEmpty.value = (data.count_list || []).every((item: { name: string; num: number }) => !item.num);
    if (isEmpty.value) return;

    const container = document.getElementById(element) as HTMLDivElement;
    container.innerHTML = '';
    await loadG2();
    if (window.G2) {
      chart = new G2.Chart({
        container: element,
        autoFit: true,
        height: 300,
      });
      render();
    }
  };

  const render = () => {
    chart.data(chartInfo.value.count_list || []);
    chart.scale('num', {
      alias: '金额(元)',
      nice: true,
    });
    chart.axis('name', {
      tickLine: null,
    });

    chart.tooltip({
      showMarkers: true,
      title: (title, datum) => {
        return `全部${datum['name']}`;
      },
    });

    chart.interval().position('name*num');

    chart.render();
  };

  onMounted(() => {
    createCharts();
    window.addEventListener('resize', debounce(createCharts, 500));
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', debounce(createCharts, 500));
  });

  return {
    chartInfo,
    loading,
    isEmpty,
    createCharts,
  };
}
