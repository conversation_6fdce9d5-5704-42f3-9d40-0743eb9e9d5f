<template>
  <ExpandBox
    placement="top"
    :target-range="formTargetHeight || RESIZE_TARGET_HEIGHT"
  >
    <template #content>
      <div
        v-for="(group, index) in formListGroup"
        :key="index"
        style="display: flex"
      >
        <div
          v-if="group.title"
          class="group-title"
        >
          {{ group.title }}
        </div>
        <RForm
          ref="formRef"
          v-model:value="formDataValue"
          :form-dependencies="formDependencies"
          :form-group="group.items || []"
          gird
          :grid-config="gridConfig"
          :origin-props="{ layout: 'horizontal', ...formOriginProps }"
          style="flex: 1"
          @keydown.enter="handleKeyEnter"
        >
          <!-- 继承插槽 -->
          <template
            v-for="(_index, name) in slots"
            #[name]="slotData"
          >
            <slot
              :name="name"
              v-bind="slotData || {}"
            />
          </template>
        </RForm>
      </div>
    </template>
    <template #custom="{ hasExtendMore, isOutRange, toggleExtendMore }">
      <!-- 其他功能插槽（left） -->
      <slot name="left" />
      <!-- 展开/收起按钮 -->
      <div
        v-if="isOutRange"
        class="more-filtering"
        :class="hasExtendMore ? 'active' : 'default'"
        @click="toggleExtendMore"
      >
        <span>更多筛选</span>
        <img
          alt="更多图标"
          class="icon"
          src="https://img1.rrzuji.cn/uploads/scheme/2301/09/o/AhlqYzpmw9nw41GSTW2X.png"
        >
      </div>
      <!-- 其他功能插槽（right） -->
      <slot name="right" />
    </template>
  </ExpandBox>
</template>

<script setup lang="ts">
import { ref, useSlots } from 'vue';
import { useVModel } from '@vueuse/core';
import { RForm } from 'rrz-web-design';

import ExpandBox from '@/components/expand-box';

import { IFormListGroup, TObject } from '../data';

const props = defineProps<{
  formListGroup?: IFormListGroup[];
  formData?: TObject;
  formDependencies?: Record<string, any[]>;
  formOriginProps?: Record<string, any>;
  formTargetHeight?: number;
}>();
const emit = defineEmits<{
  (e: 'update:formData', value: TObject[]): void;
  (e: 'keyEnter'): void;
}>();

// 继承插槽
const slots = useSlots();
/** 需要控制展开收起的高度（两行搜索列） */
const RESIZE_TARGET_HEIGHT = 2 * 56;

const gridConfig = {
  colProps: {
    md: 8,
    lg: 8,
    xl: 6,
    xxl: 4,
  },
  rowProps: {
    gutter: {
      md: 8,
      lg: 8,
      xl: 16,
    },
  },
};

const formRef = ref([]);
const formDataValue = useVModel(props, 'formData', emit);

/** 检查搜索数据 */
async function validate() {
  for (let i = 0; i < formRef.value.length; i++) {
    await formRef.value[i]?.getFormRef().validate();
  }
}

/** 重置搜索条件 */
function resetFields() {
  formRef.value.forEach(form => {
    form?.getFormRef().resetFields();
  });
}

/** 回车搜索 */
function handleKeyEnter() {
  emit('keyEnter');
}

defineExpose({
  validate,
  resetFields,
});
</script>

<style lang="less" scoped>
.group-title {
  width: 80px;
  padding-top: 6px;
  padding-right: 8px;
  color: rgba(6, 21, 51, 0.45);
}

.more-filtering {
  display: inline-block;
  margin: auto 10px;
  color: #3777ff;
  cursor: pointer;
  user-select: none;

  .icon {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    transition-duration: 0.3s;
  }
}

.more-filtering.default {
  .icon {
    transform: translateY(2px);
  }
}

.more-filtering.active {
  .icon {
    transform: rotateZ(180deg) translateY(0);
  }
}
</style>
