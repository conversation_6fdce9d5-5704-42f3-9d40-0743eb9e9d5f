<template>
  <a-drawer
    title="操作日志"
    :visible="props.visible"
    :width="850"
    @close="emits('update:visible', false)"
  >
    <search-table
      ref="searchTableRef"
      class="log-modal"
      :search-api="apiGetRuleLog"
      :search-config="searchFormConfig"
      :tb-col="tbCol"
      :tb-props="{
        scroll: { x: 1000 },
      }"
    />
  </a-drawer>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import dayjs from 'dayjs';

import { apiGetRuleLog } from '../../services';
import { ISearchConfigItem } from '@/components/search-table/src/data';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits<{
  (event: 'update:visible', value: boolean): void;
}>();
const searchTableRef = ref();
const tbCol = [
  {
    title: '规则ID',
    dataIndex: 'rule_id',
    key: 'rule_id',
    width: 64,
  },
  {
    title: '渠道名称',
    dataIndex: 'name',
    key: 'name',
    width: 100,
  },
  {
    title: '渠道绑定',
    dataIndex: 'method',
    key: 'method',
    width: 100,
  },
  {
    title: '修改人',
    dataIndex: 'created_by',
    key: 'created_by',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 55,
  },
  {
    title: '修改时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 100,
  },
  {
    title: '修改动作',
    dataIndex: 'operate_type',
    key: 'operate_type',
    width: 55,
    fixed: 'right',
  },
];
const searchFormConfig = ref<ISearchConfigItem[]>([
  {
    label: '时间',
    key: 'created_at',
    renderType: 'datePicker',
    default: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  },
]);

watch(
  () => props.visible,
  (val: boolean) => {
    val && searchTableRef.value?.searchFormData();
  },
);
</script>

<style lang="less">
.log-modal .ant-form {
  grid-template-columns: repeat(3, 1fr);
  .ant-picker {
    width: 200px;
  }
}
</style>
