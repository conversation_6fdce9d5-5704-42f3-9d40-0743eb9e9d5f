<template>
  <div :class="['status-box', classMap[type]] ">
    <div class="status-text">
      <div class="flex-wrap">
        <img
          alt=""
          class="img-icon"
          :src="imgMap[type]"
        >
        <div class="flex-wrap flex-x-center flex-vertical">
          <span>{{ statusMsgMap[type] }}</span>
          <div
            v-if="type === EInterviewStatus.ERROR"
            class="reason"
          >
            <span>审核原因：</span>{{ errorMsg }}
          </div>
        </div>
      </div>
    </div>
    <slot name="operate" />
  </div>
</template>

<script setup lang="ts">
import { EInterviewStatus } from '../data.d';

withDefaults(
  defineProps<{
    type: EInterviewStatus;
    showButton?: boolean;
    errorMsg?: string;
  }>(),
  {
    type: EInterviewStatus.WAINTING,
    showButton: false,
  },
);

const imgMap = {
  [EInterviewStatus.ERROR]: 'https://img1.rrzuji.cn/uploads/scheme/2501/21/m/us9dLR965dqHPJNLsw1J.png',
  [EInterviewStatus.SUCCESS]: 'https://img1.rrzuji.cn/uploads/scheme/2501/21/m/UJoSWLwWZObV0RceEr1a.png',
  [EInterviewStatus.WAINTING]: 'https://img1.rrzuji.cn/uploads/scheme/2501/21/m/fhY1eOOgCMuC7zfhtHat.png',
};

const statusMsgMap = {
  [EInterviewStatus.ERROR]: '您的面审未通过，需要完善相关面审信息',
  [EInterviewStatus.SUCCESS]: '您的面审已通过',
  [EInterviewStatus.WAINTING]: '您的面审补充信息审核中，预计耗时1~3天',
}

const classMap = {
  [EInterviewStatus.ERROR]: 'error',
  [EInterviewStatus.SUCCESS]: 'success',
  [EInterviewStatus.WAINTING]: 'wainting',
}
</script>

<style scoped lang="less">
.status-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 72px;
  padding: 16px 24px;
  background: #fff5f5;
  border: 1px solid #ffc0c4;
  border-radius: 8px 8px 8px 8px;
  span {
    padding-left: 16px;
    color: rgba(6,21,51,0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
  .reason {
    span {
      color: rgba(6,21,51,0.65);
      font-weight: 400;
      font-size: 14px;
    }
    color: #FF4D4F;
    font-size: 14px;
  }
}
.img-icon {
  width: 40px;
  height: 40px;
}
.error {
  background: #FFF5F5;
  border: 1px solid #FFC0C4;
}
.success {
  background: rgba(228,252,213,0.5);
  border: 1px solid #C5F4A8;
}
.wainting {
  background: #F3F9FF;
  border: 1px solid #DBEBFF;
}

</style>
