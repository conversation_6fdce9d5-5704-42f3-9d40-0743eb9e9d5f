<template>
  <a-form
    ref="formRef"
    class="search-flex"
    layout="inline"
    :model="formInfo"
  >
    <a-form-item
      label="品类"
      name="category_brand_model"
    >
      <a-cascader
        v-model:value="formInfo.category_brand_model"
        allow-clear
        :field-names="{ label: 'title', value: 'id' }"
        :max-tag-count="2"
        multiple
        :options="breedList"
        placeholder="请选择"
        style="width: 212px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="入库日期"
      name="dateValue"
    >
      <a-range-picker
        v-model:value="formInfo.dateValue"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </a-form-item>
    <a-form-item>
      <div class="btn-flex">
        <a-button
          style="margin-right: 8px"
          type="primary"
          @click="emits('confirm', 'search')"
        >
          查询
        </a-button>
        <a-button
          style="margin-right: 15px"
          @click="onReset"
        >
          重置
        </a-button>
      </div>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { ref, watch, PropType } from 'vue';
import { ISearchInfo } from '../data.d';
import { getBreedList } from '../service';

const props = defineProps({
  value: {
    type: Object as PropType<ISearchInfo>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:value', 'confirm']);

const formRef = ref<HTMLElement | any>(null);

const formInfo = ref<Partial<ISearchInfo>>({});

const onEmit = () => {
  emits('update:value', formInfo.value);
};

const onReset = () => {
  formRef.value?.resetFields();
  onEmit();
  emits('confirm', 'search');
};

const breedList = ref();

const getBreed = () => {
  getBreedList({}).then(res => {
    breedList.value = res.data.spuRelation?.children || [];
  });
};
getBreed();

watch(
  () => props.value,
  newValue => {
    formInfo.value = newValue || {};
  },
  {
    immediate: true,
  },
);
</script>
<style lang="less" scoped>
.search-flex {
  display: flex;
  gap: 12px;
}

.btn-flex {
  display: flex;
  align-items: center;
}
</style>
