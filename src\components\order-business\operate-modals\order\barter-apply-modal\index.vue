<template>
  <a-modal
    class="barter-modal"
    :destroy-on-close="true"
    title="提交换货申请单"
    :visible="visible"
    width="660px"
    @cancel="onCancel"
  >
    <template v-if="!isSuccess">
      <!-- 应发货品 -->
      <div class="delivery-card">
        <div class="delivery-card-header">
          <span style="padding-right: 4px">应发货品:</span>
          <span class="delivery-message">{{ barterInfo.sku_info }}</span>
        </div>
        <div class="delivery-hint">
          <div style="display: flex; align-items: center; padding-right: 16px">
            <img
              alt=""
              class="delivery-hint-icon"
              src="https://img1.rrzuji.cn/uploads/scheme/2305/27/m/twhg8oEuYUFwkPokvzRm.png"
            >
            <span>若与订单信息不一致，可申请换货说明原因</span>
          </div>
          <div style="display: flex; align-items: center">
            <img
              alt=""
              class="delivery-hint-icon"
              src="https://img1.rrzuji.cn/uploads/scheme/2305/27/m/HNkug7nKudQeVmH9iXKK.png"
            >
            <span>若无当前库存，可与用户协商换货</span>
          </div>
        </div>
      </div>
      <!-- 选择换货原因 -->
      <div
        class="form-item"
        style="padding-bottom: 24px"
      >
        <div class="label">
          选择换货原因
        </div>
        <div style="display: flex">
          <a-select
            v-model:value="formState.type"
            style="margin-right: 8px"
          >
            <a-select-option :value="0">
              用户原因
            </a-select-option>
            <a-select-option :value="1">
              商家原因
            </a-select-option>
            <a-select-option :value="2">
              平台原因
            </a-select-option>
          </a-select>
          <a-input
            v-model:value="formState.cause"
            :maxlength="10"
            placeholder="具体原因说明"
            show-count
            style="flex: 1"
          />
        </div>
      </div>
      <!-- 选择换货数量 -->
      <div
        class="form-item"
        style="padding-bottom: 24px"
      >
        <div class="label">
          选择换货数量
        </div>
        <div style="display: flex; align-items: center">
          <a-input-number
            disabled
            style="width: 88px"
            :value="barterInfo.item_num"
          />
          <span style="padding-left: 8px; color: rgba(6, 21, 51, 0.65)">当前仅支持与订单数量一致</span>
        </div>
      </div>
      <!-- 选择更换货品为 -->
      <div style="padding-left: 6px">
        <div class="select-label">
          选择更换货品为:
        </div>
        <div style="display: flex; flex-wrap: wrap; padding-top: 8px">
          <!-- 型号 -->
          <a-select
            v-model:value="classifyOptions.modal"
            :options="classifyOptions.modalOptions"
            placeholder="请选择型号"
            style="width: 190px"
            @change="onChangeModal"
          />
          <!-- sku -->
          <template
            v-for="(item, index) in classifyOptions.attrList"
            :key="item.id"
          >
            <a-select
              v-model:value="item.value"
              :options="item.options"
              :placeholder="`请选择${item.name}`"
              :style="{
                width: '190px',
                marginTop: `${index > 1 ? '8px' : 0}`,
                marginLeft: `${index === 2 || index === 5 ? 0 : '8px'}`,
              }"
            />
          </template>
        </div>
      </div>
      <div class="search-flex">
        <a-button
          :disabled="searchStockDisabled"
          :loading="searchStockStatus"
          style="margin-right: 12px"
          type="primary"
          @click="onSearchStock"
        >
          确认并查询库存
        </a-button>
        <a-tag :color="isQueryStock ? 'green' : 'red'">
          {{ isQueryStock ? '已查询' : '未查询' }}
        </a-tag>
      </div>
      <!-- 设备情况 -->
      <div
        v-if="barterInfo.device_auth"
        class="inventory"
      >
        <div class="inventory-label">
          设备情况:
        </div>

        <a-radio-group
          v-model:value="isNew"
          class="radio-group"
          :options="[
            { label: '全新', value: false },
            { label: '全新（仅激活）', value: true },
          ]"
          @change="onChangeIsNew"
        />
      </div>
      <!-- 送检选项 -->
      <div
        v-if="!!barterInfo.is_checked"
        class="inventory"
      >
        <div class="inventory-label">
          是否送检:
        </div>
        <a-checkbox-group
          v-model:value="is_checked"
          class="checkbox-group"
          :options="[{ label: '送检', value: 1 }]"
          @change="handleCheckedChange"
        />
      </div>
      <!-- 自有仓库库存情况 -->
      <div class="inventory">
        <div class="inventory-label">
          自有仓库库存情况:
        </div>
        <div class="inventory-tag">
          暂无数据
        </div>
      </div>
      <!-- 新网商家才有的票据选择 -->
      <template v-if="barterInfo?.is_new_online_server && (!is_checked.length || is_checked.includes(0))">
        <a-radio-group
          v-model:value="invoice"
          style="margin-top: 16px"
          @change="onSearchStock"
        >
          <a-radio-button :value="2">
            有票
          </a-radio-button>
          <a-radio-button
            :disabled="barterInfo.is_new"
            :value="3"
          >
            无票
          </a-radio-button>
        </a-radio-group>
      </template>
      <!-- 平台仓库库存情况 -->
      <div class="inventory">
        <div class="inventory-label">
          平台仓库库存情况:
        </div>
        <a-radio-group
          v-model:value="regulation"
          class="radio-group"
          :options="[
            {
              label: '租赁服务设备',
              value: true,
              disabled: regulatoryDeviceStatusTag.disabled,
            },
            {
              label: '非租赁服务设备',
              value: false,
              disabled: nonRegulatoryDeviceStatusTag.disabled || isNew,
            },
          ]"
          @change="onChangeInventory"
        />

        <div class="status-wrap">
          <div
            class="status-tag"
            style="margin-top: 6px"
            :style="regulatoryDeviceStatusTag.style"
          >
            <sync-outlined :spin="searchStockStatus" />
            {{ regulatoryDeviceStatusTag.txt }}
          </div>
          <div
            class="status-tag"
            style="margin-top: 32px"
            :style="nonRegulatoryDeviceStatusTag.style"
          >
            <sync-outlined :spin="searchStockStatus" />
            {{ nonRegulatoryDeviceStatusTag.txt }}
          </div>
        </div>
      </div>
      <!-- 当前分配的发货仓库 -->
      <div
        v-show="stockStatus"
        class="inventory"
        style="padding-bottom: 8px"
      >
        <div class="inventory-label">
          当前分配的发货仓库:
        </div>
        <div class="inventory-tag">
          {{ WAREHOUSE_MAP[warehouseInfo.warehouse_type] || '商家仓' }}
        </div>
      </div>
    </template>

    <template v-else>
      <div class="success-content">
        <img
          alt=""
          src="https://img1.rrzuji.cn/uploads/scheme/2306/01/m/o3L5iuOgOHkdvOL6A1Zt.png"
        >
        <div class="success-content-title">
          申请成功
        </div>
      </div>
    </template>

    <template #footer>
      <template v-if="!isSuccess">
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button
          :disabled="!isQueryStock || issueDisabled"
          :loading="submitLoading"
          type="primary"
          @click="onSubmitBarter"
        >
          提交申请
        </a-button>
      </template>
      <a-button
        v-else
        type="primary"
        @click="onCancel"
      >
        知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SyncOutlined } from '@ant-design/icons-vue';

import isSufficientWarehouse from '@/utils/is-sufficient-warehouse';

import { INIT_CLASSIFY_OPTIONS, INIT_WAREHOUSE_INFO, WAREHOUSE_MAP } from './config';
import {
  getModal,
  getSendWarehouseInfo,
  getSkuInfo,
  searchSkuId,
  skuSearchBySkuId,
  submitGoodsBarter,
  subwarehouseService,
} from './services';

type BarterInfo = {
  category_id: string;
  brand_id: string;
  pdm_brand_id?: string;
  pdm_category_id?: string;
  pdm_model_id: string;
  is_checked?: number;
  replace_auth: boolean;
  order_status: string;
  binding_code: boolean;
  sku_id: string;
  sku_info: string;
  item_num: string;
  device_auth: boolean;
  device_special_tag: number;
};

type WarehouseInformation = {
  sku_id: string;
  warehouse_id: number;
  warehouse_type: number;
  inventoryStatus: boolean;
} | null;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
  warehouseId: {
    type: String,
    required: true,
  },
  isChecked: {
    type: Number,
    required: true,
  },
  serverId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'refresh']);

const barterInfo = ref<Partial<BarterInfo>>({});
let classifyOptions = ref<any>({ ...INIT_CLASSIFY_OPTIONS });
const invoice = ref(2);
// 仓库信息
const warehouseInfo = ref({
  ...INIT_WAREHOUSE_INFO,
});

const formState = ref({
  type: 1,
  cause: '',
  sku_id: '',
});

const isSuccess = ref(false);

// 是否支持监管
const regulation = ref(true);
// 是否是全新（仅激活）设备
const isNew = ref(false);
// 是否送检
const is_checked = ref([]);

// 下发按钮置灰状态
const issueDisabled = ref(true);

// 查库存按钮置灰状态
const searchStockDisabled = ref(true);

// 查询库存情况加载状态
const searchStockStatus = ref(false);

// 下发仓库提交状态
const submitLoading = ref(false);

// 库存情况
const stockStatus = ref<0 | 1 | 2>(0);

// 监管机和非监管机的分仓相关信息
const warehouseInformation = ref<{
  regulatoryDeviceInformation: WarehouseInformation;
  nonRegulatoryDeviceInformation: WarehouseInformation;
}>({
  regulatoryDeviceInformation: null,
  nonRegulatoryDeviceInformation: null,
});

const regulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.regulatoryDeviceInformation?.inventoryStatus;
  const isNumber = typeof inventoryStatus === 'boolean';
  if (isNumber) {
    return {
      txt: inventoryStatus ? '库存充足' : '缺货',
      style: inventoryStatus
        ? {
            color: '#52c41a',
            backgroundColor: '#f0ffe1',
            border: '1px solid #52C41A',
          }
        : {
            color: '#ff4d4f',
            backgroundColor: '#fff1f0',
            border: '1px solid #FFA39E',
          },
      disabled: !inventoryStatus,
    };
  }
  return {
    txt: '暂无数据',
    style: {
      color: 'rgba(6, 21, 51, 0.65)',
      backgroundColor: '#0615330a',
      border: '1px solid #dadce1',
    },
    disabled: !inventoryStatus,
  };
});

const nonRegulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.nonRegulatoryDeviceInformation?.inventoryStatus;
  const isNumber = typeof inventoryStatus === 'boolean';

  if (isNumber) {
    return {
      txt: inventoryStatus ? '库存充足' : '缺货',
      style: inventoryStatus
        ? {
            color: '#52c41a',
            backgroundColor: '#f0ffe1',
            border: '1px solid #52C41A',
          }
        : {
            color: '#ff4d4f',
            backgroundColor: '#fff1f0',
            border: '1px solid #FFA39E',
          },
      disabled: !inventoryStatus,
    };
  }

  return {
    txt: '暂无数据',
    style: {
      color: 'rgba(6, 21, 51, 0.65)',
      backgroundColor: '#0615330a',
      border: '1px solid #dadce1',
    },
    disabled: !inventoryStatus,
  };
});

// 更改货品sku后，是否有重新查询库存
const isQueryStock = ref(false);
const checkStockCallback = (type: 'attrChange' | 'btnSubmit') => {
  isQueryStock.value = type === 'btnSubmit';
};
// 切换租赁服务时触发
const onChangeInventory = (e: any) => {
  const value = e.target?.value as boolean;
  const { regulatoryDeviceInformation, nonRegulatoryDeviceInformation } = warehouseInformation.value;
  if (value) {
    warehouseInfo.value = {
      warehouse_id: regulatoryDeviceInformation?.warehouse_id as number,
      warehouse_type: regulatoryDeviceInformation?.warehouse_type as number,
    };
  } else {
    warehouseInfo.value = {
      warehouse_id: nonRegulatoryDeviceInformation?.warehouse_id as number,
      warehouse_type: nonRegulatoryDeviceInformation?.warehouse_type as number,
    };
  }
};

const getModelOptions = () => {
  const { pdm_category_id: category_id, pdm_brand_id: brand_id } = barterInfo.value;
  getModal({
    category_id,
    brand_id,
  }).then(({ data }: any) => {
    const { spuRelation } = data;
    const modalOptions =
      spuRelation.children
        .find((item: any) => item.id == category_id)
        ?.children.find((item: any) => item.id == brand_id)
        ?.children.map((item: any) => ({
          label: item.title,
          value: item.id,
        })) || [];
    classifyOptions.value.modalOptions = modalOptions;
  });
};

const getSkuOptions = () => {
  const { pdm_category_id: category_id, pdm_brand_id: brand_id } = barterInfo.value;
  return getSkuInfo({
    category_id,
    brand_id,
    model_id: classifyOptions.value.modal,
  }).then(({ data }: any) => {
    const { attr_list } = data;
    classifyOptions.value.attrList = attr_list?.map((item: any) => {
      return {
        id: item.id,
        name: item.name,
        value: attrValueMap.get(item.id) || null,
        options: item.attr_items.map((sku: any) => ({ label: sku.name, value: sku.id })),
      };
    });
  });
};

function handleCheckedChange(val) {
  invoice.value = val.length ? 0 : 2;
  onSearchStock();
}

// 监听属性值变化，只有在所有的属性值都有选择的时候才能移除查询库存按钮、提交按钮的置灰状态
watch(
  () => classifyOptions.value,
  newVal => {
    if (newVal.modal) {
      const status = newVal.attrList.every((item: any) => item.value && item.value !== null);
      searchStockDisabled.value = !status;
    } else {
      issueDisabled.value = true;
      searchStockDisabled.value = true;
    }
    checkStockCallback && checkStockCallback('attrChange');
  },
  {
    deep: true,
  },
);

const onCancel = () => {
  onResetState();
  emits('update:visible', false);
};

// 对部分状态进行重置
const onResetState = () => {
  formState.value = {
    type: 1,
    cause: '',
    sku_id: '',
  };
  classifyOptions.value = { ...INIT_CLASSIFY_OPTIONS };
  warehouseInfo.value = {
    ...INIT_WAREHOUSE_INFO,
  };
  regulation.value = true;
  isNew.value = false;
  stockStatus.value = 0;
  issueDisabled.value = true;
  isSuccess.value = false;
};

const attrValueMap = new Map();
// 型号发生变化时触发
const onChangeModal = () => {
  attrValueMap.clear();
  getSkuOptions();
};
const onChangeIsNew = () => {
  onSearchStock();
};

const onSearchStock = () => {
  if (!classifyOptions.value.modal) {
    message.warning('请先选择型号!');
    return;
  }

  const params: any = {
    model_id: classifyOptions.value.modal,
    brand_id: Number(barterInfo.value.pdm_brand_id),
    attribute: [],
  };
  const attribute_list = classifyOptions.value.attrList.reduce(
    (acc: any, curr: any) => {
      if (curr.value) {
        acc.attribute_list.push(curr.value);
      }
      return acc;
    },
    { attribute_list: [] },
  );
  params.attribute.push(attribute_list);

  searchStockStatus.value = true;
  // 1. 根据所选的sku信息查找出对应的skuid
  searchSkuId({ ...params })
    .then(({ data }: any) => {
      const { sku_id } = data.sku_list[0];
      formState.value.sku_id = sku_id;
      if (!sku_id) {
        message.warning('未找到当前商品的sku_id');
        // 下发按钮置灰并且将库存状态修改为暂无数据
        searchStockStatus.value = false;
        stockStatus.value = 0;
        issueDisabled.value = true;
        return;
      }

      // 2.查询分仓服务
      selectOptimalWarehouseService(sku_id);
      checkStockCallback && checkStockCallback('btnSubmit');
    })
    .finally(() => (searchStockStatus.value = false));
};

// stockStatus字典翻译
const handleTranslate = (status: 0 | 1 | 2) => ['暂无数据', '缺货', '充足'][status];

/*
  查询分仓服务,择优选择
  1. 「租赁服务设备」、「非租赁服务设备」(本质就是监管机和非监管机)，哪个有货就有些命中对应的那个
  2. 如果双方都有货，那么优先选择「租赁服务设备」
  3. 没货的需要置灰不允许切换
*/
async function selectOptimalWarehouseService(sku_id: string) {
  searchStockStatus.value = true;
  const { item_num, device_special_tag, is_new_online_server } = barterInfo.value;
  const parmas = {
    sku_id,
    item_num: Number(item_num),
    order_id: props.orderId,
    expected_warehouse_id: Number(props.warehouseId) || 0, // 期望的仓库id ~分仓策略尽量在本仓分
    is_activate_only: isNew.value,
    is_checked: is_checked.value.includes(1),
    server_id: Number(props.serverId),
    device_special_tag,
  };
  if (is_new_online_server) {
    parmas.invoice = invoice.value;
  }

  try {
    // 监管机
    const { data: regulatoryDeviceInformation } = await subwarehouseService({
      ...parmas,
      is_lock_machine: true,
    });
    // 非监管机
    const { data: nonRegulatoryDeviceInformation } = await subwarehouseService({
      ...parmas,
      is_lock_machine: false,
    });

    const {
      warehouse_id: regulatoryWarehouseId,
      warehouse_type: regulatoryWarehouseType,
    } = regulatoryDeviceInformation;
    const {
      warehouse_id: nonRegulatoryWarehouseId,
      warehouse_type: nonRegulatoryWarehouseType,
    } = nonRegulatoryDeviceInformation;

    // 监管机库存状态
    const regulatoryInventoryStatus = isSufficientWarehouse({
      warehouse_type: regulatoryWarehouseType,
      warehouse_id: regulatoryWarehouseId,
      regulation: true,
    });

    // 非监管机库存状态
    const nonRegulatoryInventoryStatus = isSufficientWarehouse({
      warehouse_type: nonRegulatoryWarehouseType,
      warehouse_id: nonRegulatoryWarehouseId,
      regulation: false,
    });

    warehouseInformation.value = {
      regulatoryDeviceInformation: {
        ...regulatoryDeviceInformation,
        inventoryStatus: regulatoryInventoryStatus,
      },
      nonRegulatoryDeviceInformation: {
        ...nonRegulatoryDeviceInformation,
        inventoryStatus: nonRegulatoryInventoryStatus,
      },
    };

    // 如果监管机有货
    if (regulatoryInventoryStatus) {
      stockStatus.value = 2; // 充足状态
      issueDisabled.value = false;
      warehouseInfo.value = {
        warehouse_id: regulatoryWarehouseId,
        warehouse_type: regulatoryWarehouseType,
      };
      regulation.value = true;
      return;
    }

    // 如果监管机无货并且非监管机有货
    if (!regulatoryInventoryStatus && nonRegulatoryInventoryStatus) {
      stockStatus.value = 2; // 充足状态
      issueDisabled.value = false;
      warehouseInfo.value = {
        warehouse_id: nonRegulatoryWarehouseId,
        warehouse_type: nonRegulatoryWarehouseType,
      };
      regulation.value = false;
      return;
    }

    // 如果都没货
    if (!regulatoryInventoryStatus && !nonRegulatoryInventoryStatus) {
      stockStatus.value = 1; // 缺货状态
      issueDisabled.value = true;
      regulation.value = true;
      return;
    }
  } finally {
    searchStockStatus.value = false;
  }
}

const onSubmitBarter = () => {
  if (!formState.value.cause) {
    message.warning('请填写具体原因');
    return;
  }

  Modal.confirm({
    title: '请确认操作',
    content: '您确认要执行换货申请吗?',
    onOk() {
      const { type: reasonType, cause: remark, sku_id } = formState.value;
      const SUBMIT_STATUS_MAP = {
        暂无数据: 3,
        缺货: 2,
        充足: 1,
      };

      const params = {
        order_id: props.orderId,
        old_sku_id: barterInfo.value.sku_id,
        new_sku_id: sku_id,
        reason: ['用户原因', '商家原因', '平台原因'][reasonType] || '未知',
        remark,
        is_support: regulation.value ? 1 : 0,
        activate_only: isNew.value ? 1 : 0,
        stock_status: SUBMIT_STATUS_MAP[handleTranslate(stockStatus.value)],
        ...warehouseInfo.value,
        is_checked: is_checked.value.includes(1) ? 1 : 0,
      };
      if (barterInfo.value.is_new_online_server) {
        params.invoice = invoice.value;
      }

      submitLoading.value = true;
      submitGoodsBarter(params)
        .then(() => {
          isSuccess.value = true;
          emits('refresh', props.orderId);
        })
        .finally(() => {
          submitLoading.value = false;
        });
    },
  });
};

const handleGetAttrValue = async (sku_id: string) => {
  const { pdm_model_id } = barterInfo.value;
  const {
    data: { model_id, sku_val_arr },
  } = await skuSearchBySkuId({ sku_id });
  classifyOptions.value.modal = model_id || pdm_model_id ? Number(pdm_model_id) : undefined;
  sku_val_arr?.forEach((it: { attr_val_id: number; attr_id: number }) => {
    attrValueMap.set(it.attr_id, it.attr_val_id);
  });
  await getSkuOptions();
  onSearchStock();
};
watch(
  () => props.visible,
  async visible => {
    is_checked.value = [props.isChecked];

    if (visible && props.orderId) {
      const { data } = await getSendWarehouseInfo(props.orderId);
      const { sku_id } = data;
      barterInfo.value = data;
      await handleGetAttrValue(sku_id);
      getModelOptions();
      is_checked.value = [props.isChecked];
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
.delivery-card {
  position: relative;
  margin-bottom: 24px;
  padding: 24px;
  color: rgba(6, 21, 51, 0.65);
  background-color: #f0f7ff;
  border-radius: 4px;
}

.delivery-card-header {
  font-size: 16px;
}

.delivery-message {
  color: #3777ff;
  font-weight: 500;
}

.delivery-hint {
  display: flex;
  padding-top: 16px;
}

.delivery-hint-icon {
  width: 16px;
  height: 16px;
}

.form-item {
  padding-left: 6px;

  .label {
    position: relative;
    padding-bottom: 8px;
    color: rgba(6, 21, 51, 0.85);
  }

  .label::before {
    position: absolute;
    top: 0;
    left: -8px;
    color: #ff4d4f;
    content: '*';
  }
}

.inventory {
  display: flex;
  align-items: center;
  padding: 24px 0 0 8px;
  color: rgba(6, 21, 51, 0.85);
}

.inventory-tag {
  margin: 0 8px;
  padding: 2px 6px;
  color: rgba(6, 21, 51, 0.65);
  font-size: 12px;
  background-color: rgba(6, 21, 51, 0.04);
  border: 1px solid #dadce1;
  border-radius: 4px;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px 0;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 16px;
}

.success-content-title {
  padding: 8px 0;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 20px;
}

.select-label {
  position: relative;
  padding: 0;
  color: rgba(6, 21, 51, 0.85);
}

.select-label::before {
  position: absolute;
  top: 0;
  left: -8px;
  color: #ff4d4f;
  content: '*';
}

.checkbox-group,
.radio-group {
  position: relative;
  display: flex;
  flex-flow: column wrap;
  gap: 24px;
  padding-left: 8px;

  :deep(.ant-radio-wrapper),
  :deep(.ant-checkbox-wrapper) {
    position: relative;
    gap: 8px;

    & > span.ant-radio + *,
    & > span.ant-checkbox + * {
      box-sizing: border-box;
      min-width: 130px;
      padding: 5px 0;
      color: rgba(6, 21, 51, 0.85);
      text-align: center;
      background-color: #f5f7fa;
      border: 1px solid #f5f7fa;
      border-radius: 4px;
    }

    &:not(:has(.ant-radio-disabled)) > span.ant-radio + *:hover,
    &:not(:has(.ant-checkbox-disabled)) > span.ant-checkbox + *:hover {
      border-color: var(--ant-primary-color);
      transition: border-color 0.3s linear;
    }

    &:not(:first-child)::before {
      position: absolute;
      top: -29px;
      left: 8px;
      width: 1px;
      height: 34px;
      background-color: rgba(6, 21, 51, 0.06);
      content: '';
    }

    &.ant-radio-wrapper-checked,
    &.ant-checkbox-wrapper-checked {
      & > span.ant-radio + *,
      & > span.ant-checkbox + * {
        color: var(--ant-primary-color);
        background-color: var(--ant-primary-1);
        border-color: var(--ant-primary-color);
        transition: border-color 0.3s linear;
      }

      &::before {
        background-color: var(--ant-primary-color);
      }
    }

    &.ant-radio-wrapper-disabled > .ant-radio-disabled + span,
    &.ant-checkbox-wrapper-disabled > .ant-checkbox-disabled + span {
      color: rgba(0, 0, 0, 0.25);
      background-color: #f5f7fa;
      border-color: #f5f7fa;
      cursor: not-allowed;
    }
  }
}

.status-wrap {
  margin-left: 8px;

  .status-tag {
    padding: 2px 6px;
    font-size: 12px;
    border-radius: 4px;
  }
}
.search-flex {
  display: flex;
  align-items: center;
  margin-top: 18px;
}
</style>
