import { GET, IAxiosResponse, POST } from '@/services/api';
import { ISelectOption } from '../add-valuation-template/data';

// 获取table数据
export function getTableList(params: any) {
  return POST('/super/recycle/purchased-goods/list', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

export function getPurchasedGoodList(params: any) {
  return GET('/super/recycle/base-goods/search-list', params);
}

// 查看回收商品详情
export function searchPurchasedGood(id: string) {
  return GET(`/super/recycle/purchased-goods/detail?id=${id}`);
}

// 修改回收商品
export function updatePurchasedGood(body: any) {
  return POST('/super/recycle/purchased-goods/update', body, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 创建回收商品
export function createPurchasedGood(body: any) {
  return POST('/super/recycle/purchased-goods/create', body, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 删除回收商品
export function deletePurchasedGood(id: string) {
  return GET(`/super/recycle/purchased-goods/delete?id=${id}`);
}

// 商品上架或者暂停回收
export function changePurchasedGoodStatus(id: string, type: 'open' | 'close') {
  return GET(`/super/recycle/purchased-goods/switch-status?id=${id}&type=${type}`);
}

/**
 * 获取估价模板列表（下拉框）
 * @returns {any}
 */
export function queryTempalteNameList() {
  return GET('/super/recycle/evaluate-template/name-filter');
}

/**
 * 查询估价模板详情
 * @param {any} id:string|number
 * @returns {any}
 */
export const queryTemplateLogDetail = (id: string | number) => {
  return GET('/super/recycle/evaluate-template-log/detail', { id });
};

/**
 * 根据type获取评估项下拉框列表
 * @param {any} type:string|number
 * @returns {any}
 */
export const queryOptionList = (type: string | number): Promise<IAxiosResponse<ISelectOption[]>> => {
  return GET('/super/recycle/evaluate-option/option-filter', { type });
};

/**
 * 价格趋势下拉框数据
 * @returns {any}
 */
export const queryChartDropdownList = (spu_id?: number) => {
  return GET('/super/recycle/evaluate-market/title-filter', { spu_id });
};

/**
 * 获取图表详情
 * @param {any} id:number
 * @returns {any}
 */
export const queryChartDetail = (params: Record<string, any>) => {
  return GET('/super/recycle/evaluate-market/chart-detail', params);
};
