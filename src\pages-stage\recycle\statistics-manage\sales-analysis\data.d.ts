export interface ISearchInfo {
  source_type: string;
  category_brand_model: number[][];
  sku_id: string;
}

export interface IDataSource {
  sku_id: string;
  category_name: string;
  brand_name: string;
  model_name: string;
  sku_info: string;
  sale_num: number;
  sale_price: number;
  average_sale_price: number;
  differences_price: number;
  sale_purchased_price: number;
  sale_win_price: number;
  gross_profit: number;
  stock: string;
  stock_purchased_price: number;
  average_stock_purchased_price: number;
}
