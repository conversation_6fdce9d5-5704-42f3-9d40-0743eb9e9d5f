<!--  -->
<template>
  <a-modal
    :visible="visible"
    @cancel="close"
    @ok="close"
  >
    <template #title>
      <div class="title">
        付款二维码
        <span
          v-if="state.isPay"
          class="right-tip"
        >已付款商品审核通过后商品即可上架</span>
        <span
          v-else
          style="color: #ff4a57; font-size: 14px"
        >请在60秒内完成付款！</span>
      </div>
    </template>
    <!-- 展示支付二维码 或者 支付超时失败-->
    <div v-if="!state.isPay">
      <template v-if="state.isPayFailed">
        <div class="pay-failed">
          <img
            src="https://img1.rrzuji.cn/uploads/scheme/2201/12/m/1JoWHkzqAMp1QP4omm8b.png"
            style="width: 160px; height: 160px"
          >
          <div class="failed">
            支付失败
          </div>
          <div class="tip">
            {{ state.countDown }}秒后自动返回
          </div>
        </div>
      </template>
      <template v-else>
        <div class="wrap">
          <img
            :src="data.qrCode"
            style="width: 160px; height: 160px"
          >
          <div class="scan">
            打开支付宝/微信扫一扫
          </div>
          <div class="money">
            支付金额：<span style="color: rgba(255, 74, 87, 1)">{{ data.money }}</span> 元
          </div>
          <div class="tip">
            未付款的商品不支持上架，为避免影响上架时间，请及时付款
          </div>
          <a-button
            style="width: 160px; margin-top: 10px"
            type="primary"
            @click="showPayResult"
          >
            完成支付
          </a-button>
        </div>
      </template>
    </div>
    <!-- 展示支付成功 -->
    <div
      v-if="state.isPay"
      class="pay-success"
    >
      <img
        src="https://img1.rrzuji.cn/uploads/scheme/2112/08/o/7zvaHeRFVQrWbs3pEnR6.png"
        style="width: 160px; height: 160px"
      >
      <div class="success">
        支付成功
      </div>
      <div class="tip">
        成功支付{{ data.money }}元，{{ state.countDown }}秒后自动返回
      </div>
    </div>
    <template #footer>
      <a-button
        type="primary"
        @click="close"
      >
        关闭
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, watch, Ref, ref, onUnmounted, nextTick } from 'vue';
import { getPayStatus } from '../services';
export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      required: true,
    },
  },
  emits: ['update:visible', 'getList'],
  setup(props, { emit }) {
    const state: Ref<any> = ref({
      isPay: false,
      isPayFailed: false,
      //查询支付状态定时器
      queryPayTimer: null,
      //每隔3s轮询，轮询20次。
      queryPayCount: 20,
      //倒计时
      countDownTimer: null,
      countDown: 3,
    });
    watch(
      () => props.visible,
      visible => {
        if (!visible) return;
        queryPayStatus();
      },
    );
    //轮询支付状态
    function queryPayStatus() {
      if (--state.value.queryPayCount < 0) return;
      state.value.queryPayTimer = setTimeout(async () => {
        const isSuccess: boolean = await isPay();
        if (!isSuccess) {
          queryPayStatus();
        }
      }, 3000);
    }
    async function isPay(): Promise<boolean> {
      const {
        data: { is_pay },
      } = await getPayStatus({ id: props.data.id });
      if (is_pay) {
        state.value.isPay = true;
        await nextTick();
        beginCountDown();
      }
      return !!is_pay;
    }
    async function showPayResult() {
      clearTimers();
      const isSuccess: boolean = await isPay();
      state.value.isPayFailed = !isSuccess;
      if (state.value.isPayFailed) {
        await nextTick();
        beginCountDown();
      }
    }
    //3s倒计时
    function beginCountDown() {
      state.value.countDownTimer = setInterval(() => {
        console.log('倒计时：' + state.value.countDown);
        if (state.value.countDown > 1) {
          state.value.countDown--;
        } else {
          close();
        }
      }, 1000);
    }
    //关闭
    function close() {
      clearTimers();
      emit('update:visible', false);
      emit('getList');
      state.value.isPay = false;
      state.value.isPayFailed = false;
      state.value.countDown = 3;
      state.value.queryPayCount = 20;
    }
    //清除定时器
    function clearTimers() {
      if (state.value.queryPayTimer) {
        clearTimeout(state.value.queryPayTimer);
        state.value.queryPayTimer = null;
      }
      if (state.value.countDownTimer) {
        clearInterval(state.value.countDownTimer);
        state.value.countDownTimer = null;
      }
    }
    onUnmounted(() => {
      clearTimers();
    });
    return { state, close, showPayResult };
  },
});
</script>
<style scoped lang="less">
.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 16px;
  .right-tip {
    color: rgba(0, 0, 0, 0.45);
    font-weight: 400;
    font-size: 14px;
  }
}
.wrap {
  text-align: center;
  .scan {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    color: rgba(24, 144, 255, 1);
    &::before {
      display: inline-block;
      width: 24px;
      height: 24px;
      margin-right: 8px;
      background: url(https://img1.rrzuji.cn/uploads/scheme/2112/08/o/tk3OyyaRwvp2FsiYOl7T.png) no-repeat;
      content: '';
    }
  }
  .money {
    margin: 28px 0 8px 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }
  .tip {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }
}
.pay-success,
.pay-failed {
  text-align: center;
  .success,
  .failed {
    margin: 16px 0 8px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 24px;
  }
  .tip {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
  }
}
</style>
