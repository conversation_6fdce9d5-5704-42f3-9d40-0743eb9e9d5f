<template>
  <div class="data-statistics-board">
    <div class="item-title">
      {{ info.title }}
      <a-tooltip
        v-if="info.tips"
        placement="rightTop"
      >
        <template #title>
          <div
            v-for="(item, index) in info.tips"
            :key="index"
          >
            {{ item }}
          </div>
        </template>
        <InfoCircleOutlined style="margin-left: 8px; color: rgba(6, 21, 51, 0.45); font-size: 16px" />
      </a-tooltip>
    </div>
    <div class="item-content">
      <div class="item-block">
        <div class="item-block__title">
          应付金额（元）
        </div>
        <div class="item-block__value">
          <a-statistic :value="info.wait_pay_num" />
        </div>
      </div>
      <div class="item-block">
        <div class="item-block__title">
          已付金额（元）
        </div>
        <div class="item-block__value">
          <a-statistic :value="info.finish_pay_num" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { IDataStatistics } from '../data.d';
defineProps({
  info: {
    type: Object as PropType<IDataStatistics>,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.data-statistics-board {
  padding: 16px;
  background: #f9f9fb;
  border-radius: 8px;
  .item-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  .item-content {
    display: flex;
    .item-block {
      flex: 1;
      &__title {
        color: rgba(6, 21, 51, 0.45);
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
      }
      &__value {
        margin-top: 4px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
      }
    }
  }
}
</style>
