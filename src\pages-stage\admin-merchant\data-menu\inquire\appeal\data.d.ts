export type SearchConditionType = {
  order_id?: string;
  appeal_type: number;
  appeal_desc: string;
};

export type TableCacheItem = Partial<{
  order_id: number;
  file_evidence: FileItemType[];
  desc: string;
  appeal_order_reasons: {
    appeal_order_type: number;
    appeal_desc: string;
  }[];
  isEvidence: boolean;
  appeal_type: string | number;
}>;

export type RecordItem = Partial<{
  order_id: number;
  sent_pay_num: number;
  sent_at: number;
  server_receive_at: number;
}>;

export type RecordDataType = Partial<{
  appeal_type: number;
  appeal_desc: string;
  status: number;
  audit_desc: string;
  deal_num: number;
  pass_num: number;
}>;

export type FileItemType = {
  name: string;
  url: string;
  path: string;
  uid: number;
};
