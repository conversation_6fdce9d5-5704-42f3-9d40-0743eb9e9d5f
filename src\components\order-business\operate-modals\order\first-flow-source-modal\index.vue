<template>
  <a-modal
    v-model:visible="bindVisible"
    class="order-flow-modal"
    title="订单首次流量来源"
  >
    {{ content }}
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

import { useVModel } from '@/hook';

import { getFirstFlow } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);
const bindVisible = useVModel(props, 'visible', emit);

const content = ref('');
const loading = ref(false);

watch(
  () => props.visible,
  value => {
    if (value) {
      loadData();
    }
  },
  {
    immediate: true,
  },
);

function loadData() {
  loading.value = true;
  getFirstFlow({
    order_id: props.orderId,
  })
    .then(res => {
      content.value = res.message || '';
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
