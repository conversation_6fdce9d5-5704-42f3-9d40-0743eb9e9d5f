<template>
  <a-form
    layout="vertical"
    :model="bindFormData"
  >
    <!-- 选择换货原因 -->
    <a-form-item
      label="选择换货原因"
      required
    >
      <div class="flex-wrap">
        <a-select
          v-model:value="bindFormData.type"
          :options="[
            { label: '用户原因', value: 0 },
            { label: '商家原因', value: 1 },
            { label: '平台原因', value: 2 },
          ]"
          style="width: 100px; margin-right: 8px"
        />
        <a-input
          v-model:value="bindFormData.cause"
          :maxlength="10"
          placeholder="具体原因说明"
          show-count
          style="flex: 1"
        />
      </div>
    </a-form-item>
    <!-- 选择换货数量 -->
    <a-form-item
      label="选择换货数量"
      required
    >
      <div class="flex-wrap flex-y-center">
        <a-input-number
          disabled
          style="width: 88px"
          :value="Number(initData.item_num)"
        />
        <span style="padding-left: 8px; color: rgba(6, 21, 51, 1)">当前仅支持与订单数量一致</span>
      </div>
    </a-form-item>
    <!-- 选择更换货品为 -->
    <a-form-item
      label="选择更换货品"
      required
    >
      <div
        class="flex-wrap-line"
        style="padding-top: 8px"
      >
        <!-- 型号 -->
        <a-select
          v-model:value="bindFormData.modalId"
          :disabled="!isSuper"
          :options="modalOptions"
          placeholder="请选择型号"
          style="width: 190px"
          @change="onChangeModal"
        />
        <!-- sku -->
        <a-select
          v-for="(item, index) in attrList"
          :key="item.id"
          v-model:value="item.value"
          :options="item.options"
          :placeholder="`请选择${item.name}`"
          :style="{
            width: '190px',
            marginTop: `${index > 1 ? '8px' : 0}`,
            marginLeft: `${[2, 5].includes(index) ? 0 : '8px'}`,
          }"
        />
      </div>
    </a-form-item>

    <a-form-item>
      <a-button
        :disabled="searchStockDisabled"
        :loading="searchStockLoading"
        style="margin-right: 12px"
        type="primary"
        @click="handleSearchStock"
      >
        确认并查询库存
      </a-button>
      <a-tag :color="isQueryStock ? 'green' : 'red'">
        {{ isQueryStock ? '已查询' : '未查询' }}
      </a-tag>
    </a-form-item>
    <!-- 设备情况  -->
    <a-tabs
      v-if="initData.device_auth"
      v-model:activeKey="bindFormData.activate_only"
      class="clear-tab"
      @change="handleSearchStock"
    >
      <a-tab-pane
        :key="false"
        tab="全新"
      />
      <a-tab-pane :key="true">
        <template #tab>
          <div class="flex-wrap flex-con flex-y-center">
            全新（仅激活）
            <img
              src="https://img1.rrzuji.cn/uploads/scheme/2412/07/m/VfiQCzPbFIAZ3gvqHGMo.png"
              style="width: 16px"
            >
          </div>
        </template>
      </a-tab-pane>
    </a-tabs>
    <!-- 新网商家才有的票据选择 -->
    <template v-if="initData.is_new_online_server && !bindFormData.isChecked">
      <a-radio-group
        v-model:value="bindInvoice"
        style="margin-bottom: 16px"
        @change="handleSearchStock"
      >
        <a-radio-button :value="2">
          有票
        </a-radio-button>
        <a-radio-button
          :disabled="initData.is_new"
          :value="3"
        >
          无票
        </a-radio-button>
      </a-radio-group>
    </template>

    <div class="flex-con flex-wrap">
      <a-radio-group
        v-model:value="bindFormData.regulation"
        class="radio-group"
        :options="[
          { label: '租赁服务设备', value: true, disabled: isForbid },
          {
            label: '非租赁服务设备',
            value: false,
            disabled: bindFormData.activate_only,
          },
        ]"
        @change="handleSupportChange"
      />

      <!--   预估价格   -->
      <div
        class="status-wrap"
        style="margin-right: 16px"
      >
        <div
          v-for="(item, key) in warehouseInformation"
          :key="key"
          class="status-item alert-text flex-wrap flex-y-center flex-gap-4"
        >
          <template v-if="item.showPrice && item.inventoryStatus">
            <span>预估价：￥{{ item?.totalPrice }}</span>
            <a-popover>
              <template #content>
                <div class="flex-wrap flex-vertical flex-gap-8">
                  <div class="flex-wrap flex-y-center flex-gap-8">
                    <InfoCircleOutlined />
                    <span>实际价格按设备下发日期的价格为准</span>
                  </div>
                  <div class="flex-wrap flex-y-center flex-gap-8 alert-text">
                    <div>预估价<br>({{ item?.totalPrice }})</div>
                    <div>=</div>
                    <a-space>
                      <template #split>
                        <span>+</span>
                      </template>
                      <template v-if="item.deviceMoney !== false">
                        <div>设备价<br>({{ item.deviceMoney }})</div>
                      </template>
                      <template v-if="item.inspectMoney !== false && !item.isNew">
                        <div>检测价<br>({{ item.inspectMoney }})</div>
                      </template>
                      <template v-if="item.sendMoney !== false">
                        <div>发货费<br>({{ item.sendMoney }})</div>
                      </template>
                      <template v-if="item.accessoryMoney !== false">
                        <div>配件费<br>({{ item.accessoryMoney }}{{ item.isNew ? `，线下结算` : '' }})</div>
                      </template>
                      <template v-if="item.lockMoney !== false">
                        <div>租赁服务费<br>({{ item.lockMoney }})</div>
                      </template>
                    </a-space>
                  </div>
                </div>
              </template>
              <InfoCircleOutlined
                class="cursor"
                style="font-size: 12px"
              />
            </a-popover>
          </template>
          <template v-else>
            <span>预估价：暂无</span>
          </template>
        </div>
      </div>

      <!--   库存情况   -->
      <div class="status-wrap">
        <div class="status-tag">
          <a-tag :color="regulatoryDeviceStatusTag.color">
            <SyncOutlined :spin="searchStockLoading" />
            {{ regulatoryDeviceStatusTag.txt }}
          </a-tag>
        </div>

        <div class="status-tag">
          <a-tag :color="nonRegulatoryDeviceStatusTag.color">
            <SyncOutlined :spin="searchStockLoading" />
            {{ nonRegulatoryDeviceStatusTag.txt }}
          </a-tag>
        </div>
      </div>
    </div>
    <!-- 是否送检 -->
    <div
      v-if="initData.is_checked"
      class="inspect-checkbox"
    >
      <a-checkbox
        v-model:checked="bindFormData.isChecked"
        @change="handleCheckedChange"
      >
        送检设备（选择后无需采购）
      </a-checkbox>
    </div>
    <!--   换货建议  -->
    <ReplacementProposalTable
      v-if="
        bindFormData.stockStatus === EStockStatus.NotEnough && (exchangeTableData.length || upgradeTableData.length)
      "
      :checked="bindFormData.isChecked"
      :exchange-table-data="exchangeTableData"
      :is-support="bindFormData.regulation"
      :is-upgrade="initData?.storage_memory_up_model"
      style="margin-top: 10px"
      :upgrade-table-data="upgradeTableData"
      @affirm-barter="(sku_id: string) => emit('affirmBarter', {c_type: 2 , new_sku_id: sku_id, type: 'exchange' })"
      @upgrade-barter="(sku_id: string) => emit('affirmBarter', {c_type: 21 , new_sku_id: sku_id, type: 'upgrade' })"
    />

    <!-- 采购余额展示 -->
    <AccountPriceShow
      :account-amount="accountAmount"
      :loading="accountState.loading"
      :show-select-controls="showSelectControls"
      @get-price-info="getPriceInfo"
      @go-recharge-route="goRechargeRoute"
    />
  </a-form>
</template>

<script lang="ts" setup>
import { computed, ref, toRef, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined, SyncOutlined } from '@ant-design/icons-vue';

import ReplacementProposalTable from '@/components/order-business/operate-modals/order/go-ship-modal/grayscale-deliver-modal/components/platform-shipment/components/replacement-proposal-table.vue';
import useRepTable from '@/components/order-business/operate-modals/order/go-ship-modal/grayscale-deliver-modal/components/platform-shipment/composables/use-rep-table';

import { IAcountAmount } from '../../go-ship-modal/data';
import AccountPriceShow from '../../go-ship-modal/grayscale-deliver-modal/components/platform-shipment/components/account-price-show.vue';
import { useAdviceParams } from '../../go-ship-modal/grayscale-deliver-modal/components/platform-shipment/composables/use-exchange-advice';
import { EStockStatus, useCheckStock } from '../composables/use-check-stock';
import { IAttrItem, IFormData, IInitData } from '../data';
import { getModal, getSkuInfo, skuSearchBySkuId } from '../services';
const props = defineProps<{
  initData: IInitData;
  formData: IFormData;
  invoice: number;
}>();

const emit = defineEmits<{
  (event: 'update:formData', data: IFormData): void;
  (event: 'update:invoice', data: number): void;
  (event: 'closeModal'): void;
  (event: 'affirmBarter', data: { c_type: 1 | 2 | 21; new_sku_id: string; type: 'exchange' | 'upgrade' }): void;
}>();

const route = useRoute();

const isSuper = route.query.role === 'super';

const { exchangeTableData, upgradeTableData, getTableData } = useRepTable();

const modalOptions = ref<{ label: string; value: number }[]>([]);
/** 绑定的表单信息 */
const bindFormData = computed({
  get: () => props.formData,
  set: val => emit('update:formData', val),
});
/** 绑定的票据 */
const bindInvoice = computed({
  get: () => props.invoice,
  set: val => emit('update:invoice', val),
});
/** sku列表选择信息 */
const attrList = ref<IAttrItem[]>([]);
// 更改货品sku后，是否有重新查询库存
const isQueryStock = ref(false);

// 初始化分类选择
async function getModelOptions() {
  const { pdm_category_id, pdm_brand_id } = props.initData;
  await getModal({
    category_id: pdm_category_id,
    brand_id: pdm_brand_id,
  }).then(res => {
    const { spuRelation } = res.data;
    modalOptions.value =
      spuRelation.children
        .find((item: any) => item.id === Number(pdm_category_id))
        ?.children.find((item: any) => item.id === Number(pdm_brand_id))
        ?.children.map((item: any) => ({
          label: item.title,
          value: item.id,
        })) || [];
  });
}

const attrValueMap = new Map();

async function handleGetAttrValue(sku_id: string) {
  const { pdm_model_id } = props.initData;
  const {
    data: { model_id, sku_val_arr },
  } = await skuSearchBySkuId({ sku_id });
  bindFormData.value.modalId = model_id || pdm_model_id ? Number(pdm_model_id) : undefined;
  sku_val_arr?.forEach((it: { attr_val_id: number; attr_id: number }) => {
    attrValueMap.set(it.attr_id, it.attr_val_id);
  });
  await getSkuOptions();
}

function handleSupportChange(ev: any) {
  const { value: currVal } = ev.target;
  const currKey = currVal ? 'regulatoryDeviceInformation' : 'nonRegulatoryDeviceInformation';
  const currInventoryStatus = warehouseInformation.value[currKey].inventoryStatus;
  if (currInventoryStatus) {
    bindFormData.value.stockStatus = EStockStatus.Enough; // 充足状态
  } else {
    bindFormData.value.stockStatus = EStockStatus.NotEnough; // 缺货状态
    // 请求换货推荐
    const skuId = bindFormData.value.skuId;
    if (!skuId) return;
    const params = handleWarehouseBaseParams(skuId);

    const exchangeParams = useAdviceParams(params, currKey === 'regulatoryDeviceInformation');
    const upgradeParams = useAdviceParams(params, currKey === 'regulatoryDeviceInformation', {
      get_other_sku_type: 2,
      skip_server_config: props.initData?.storage_memory_up_auth ? undefined : 1,
    });
    getTableData(exchangeParams, upgradeParams, props.initData?.storage_memory_up_model);
  }
}

/** 获取sku列表选择信息 */
async function getSkuOptions() {
  const { pdm_category_id, pdm_brand_id } = props.initData;
  const response = await getSkuInfo({
    category_id: Number(pdm_category_id),
    brand_id: Number(pdm_brand_id),
    model_id: bindFormData.value.modalId!,
  });
  attrList.value =
    response.data?.attr_list?.map((item: any) => {
      const { id, name, attr_items } = item;
      return {
        id,
        name,
        value: attrValueMap.get(id) || null,
        options: attr_items.map((sku: any) => ({ label: sku.name, value: sku.id })),
      };
    }) || [];
}

function onChangeModal() {
  attrValueMap.clear();
  getSkuOptions();
}

function checkStockCallback(type: 'attrChange' | 'btnSubmit') {
  isQueryStock.value = type === 'btnSubmit';
}

const {
  isForbid,
  handleSearchStock,
  searchStockDisabled,
  searchStockLoading,
  warehouseInformation,
  accountState,
  getPriceInfo,
  handleWarehouseBaseParams,
} = useCheckStock({
  formData: bindFormData,
  attrList: attrList,
  initData: toRef(() => props.initData),
  checkStockCallback,
  getTableData,
  is_grayscale: true, // 灰度版本的
  invoice: bindInvoice,
});

const regulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.regulatoryDeviceInformation?.inventoryStatus;
  return getRenderData(inventoryStatus);
});

const nonRegulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.nonRegulatoryDeviceInformation?.inventoryStatus;
  return getRenderData(inventoryStatus);
});

const showSelectControls = computed(() => {
  return !!(props.initData.sku_id && bindFormData.value.skuId);
});

// 当前项目余额
const accountAmount = computed<IAcountAmount>(() => {
  const { regulation } = bindFormData.value;
  return {
    is_one_pay_account: accountState.value.is_one_pay_account,
    ...warehouseInformation.value?.[regulation ? 'regulatoryDeviceInformation' : 'nonRegulatoryDeviceInformation'],
  };
});

function getRenderData(inventoryStatus?: boolean) {
  if (typeof inventoryStatus === 'boolean') {
    return {
      txt: inventoryStatus ? '库存充足' : '缺货',
      color: inventoryStatus ? 'success' : 'error',
      disabled: !inventoryStatus,
    };
  }

  return {
    txt: '暂无数据',
    color: 'default',
    disabled: !inventoryStatus,
  };
}

function validate() {
  return new Promise<void>((resolve, reject) => {
    if (!bindFormData.value.cause) {
      message.warning('请填写具体原因');
      return reject('请填写具体原因');
    }
    if (searchStockDisabled.value) {
      message.warning('请选择更换货品');
      return reject('请选择更换货品');
    }
    if (!bindFormData.value.skuId) {
      message.warning('请确认并查询库存');
      return reject('请确认并查询库存');
    }
    resolve();
  });
}

//回显sku选项信息
async function handleEcho() {
  await getModelOptions();
  if (props.initData?.sku_id) {
    await handleGetAttrValue(props.initData.sku_id);
    handleSearchStock();
  }
}

function buildQueryString(params: Record<string, unknown>) {
  return Object.entries(params)
    .filter(([, value]) => value !== undefined) // 过滤掉无效值
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value as string)}`)
    .join('&');
}

async function goRechargeRoute(account_type: string, account_info: any) {
  const { account_id, type, need_recharge, need_recharge_amount } = account_info;
  const { warehouse_id, warehouse_type, is_one_pay_account } = accountAmount.value;
  const params = {
    action: 'goAllRecharge',
    type: type === 'plat' ? 'platform' : 'thirdParty',
    id: account_id,
  };

  if (account_type === 'notPlat' && is_one_pay_account) {
    const { orderId: order_id, sku_id: origin_sku_id } = props.initData;
    const { regulation, activate_only, skuId } = bindFormData.value;

    await sendServerRecharge({
      v3_order_id: order_id,
      sku_id: skuId || origin_sku_id,
      warehouse_id,
      warehouse_type,
      is_activate_only: activate_only ? 1 : 0,
      is_lock: regulation ? 1 : 0,
    });
  }
  if (account_type === 'plat' && need_recharge && need_recharge_amount && is_one_pay_account) {
    params.acc_amount = need_recharge_amount;
  }
  const devUrl = `${window.location.origin}/merchant/dropship/manage/purchasing-management`;
  const testUrl = `${route.query.origin}/supply-chain-account/index`;
  const finalUrl =
    process.env.NODE_ENV === 'development'
      ? `${devUrl}?${buildQueryString(params)}`
      : `${testUrl}?${buildQueryString(params)}`;
  window.open(finalUrl, '_blank');
}

function handleCheckedChange(e) {
  const val = e.target.checked;
  bindInvoice.value = val ? 0 : 2;
  handleSearchStock();
}

watch(
  () => props.initData?.sku_id,
  () => {
    handleEcho();
  },
  { immediate: true },
);

defineExpose({
  validate,
  isQueryStock,
});
</script>

<style lang="less" scoped>
.inventory {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: rgb(6, 21, 51);

  :deep(.ant-form-item-label) {
    padding: 0;
  }
}

.clear-tab {
  :deep(.ant-tabs-nav) {
    margin-bottom: 24px;
  }
  :deep(.ant-tabs-tab) {
    padding-top: 0;
  }
}

.radio-group {
  position: relative;
  display: flex;
  flex-flow: column wrap;
  gap: 24px;
  padding-left: 8px;

  :deep(.ant-radio-wrapper),
  :deep(.ant-checkbox-wrapper) {
    position: relative;
    gap: 8px;

    & > span.ant-radio + *,
    & > span.ant-checkbox + * {
      box-sizing: border-box;
      min-width: 130px;
      padding: 5px 0;
      color: rgba(6, 21, 51, 0.85);
      text-align: center;
      background-color: #f5f7fa;
      border: 1px solid #f5f7fa;
      border-radius: 4px;
    }

    &:not(:has(.ant-radio-disabled)) > span.ant-radio + *:hover,
    &:not(:has(.ant-checkbox-disabled)) > span.ant-checkbox + *:hover {
      border-color: var(--ant-primary-color);
      transition: border-color 0.3s linear;
    }

    &:not(:first-child)::before {
      position: absolute;
      top: -29px;
      left: 8px;
      width: 1px;
      height: 34px;
      background-color: rgba(6, 21, 51, 0.06);
      content: '';
    }

    &.ant-radio-wrapper-checked,
    &.ant-checkbox-wrapper-checked {
      & > span.ant-radio + *,
      & > span.ant-checkbox + * {
        color: var(--ant-primary-color);
        background-color: var(--ant-primary-1);
        border-color: var(--ant-primary-color);
        transition: border-color 0.3s linear;
      }

      &::before {
        background-color: var(--ant-primary-color);
      }
    }

    &.ant-radio-wrapper-disabled > .ant-radio-disabled + span,
    &.ant-checkbox-wrapper-disabled > .ant-checkbox-disabled + span {
      color: rgba(0, 0, 0, 0.25);
      background-color: #f5f7fa;
      border-color: #f5f7fa;
      cursor: not-allowed;
    }
  }
}

.status-wrap {
  display: flex;
  flex-flow: column wrap;
  gap: 24px;

  .status-item,
  .status-tag {
    height: 34px;
    line-height: 34px;
  }
}

.inspect-checkbox {
  padding: 16px 0 0 8px;
}

.alert-text {
  color: var(--ant-error-color);
}

.action-icon {
  color: var(--ant-primary-color);
  cursor: pointer;
}

.line-btn {
  color: var(--ant-primary-color);
  border-color: var(--ant-primary-color);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:hover,
  &:active,
  &:focus,
  &:visited {
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    background: var(--ant-primary-color);
    border-color: var(--ant-primary-color);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }
}

.cursor {
  cursor: pointer;
}

.fw-500 {
  font-weight: 500;
}

.bold {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
}
</style>
