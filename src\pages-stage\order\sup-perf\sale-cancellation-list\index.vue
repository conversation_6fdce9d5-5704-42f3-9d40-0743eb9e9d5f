<template>
  <layout-admin-page v-bind="pageLayoutConfig">
    <RTable
      ref="salesPerformanceTargetRef"
      v-bind="salesCancellationTableConfig"
      :columns="columns"
    >
      <template #toolBar>
        <a-button
          type="primary"
          @click="handleExport"
        >
          导出 <export-outlined />
        </a-button>
      </template>
      <template #tableBodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'order_id'">
          <a-button
            style="color: #3777ff"
            type="link"
            @click="goSalesOrderList(record)"
          >
            {{ record.order_id }}
          </a-button>
        </template>
      </template>
    </RTable>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { ExportOutlined } from '@ant-design/icons-vue';
import { RTable } from 'rrz-web-design';

import { useTopWindow } from '@/hook/common/use-top-window';
import { asyncEduce } from '@/utils/educe';

import { getPageBaseConfig } from './config';
import { apiGetSaleCancellationPermissionList } from './services';

const { pageLayoutConfig, salesCancellationTableConfig, columns } = getPageBaseConfig();
const salesPerformanceTargetRef = ref<InstanceType<typeof RTable>>();
const { navigate } = useTopWindow();
const route = useRoute();
const goSalesOrderList = (record: Record<string, any>) => {
  const query = JSON.stringify({
    order_id: record.order_id,
  });
  navigate('blank', `/super/sales-order/index?query=${query}`);
};
const handleExport = async () => {
  asyncEduce(
    '/super/sales-perform-leader/export-refund-order',
    salesPerformanceTargetRef.value?.searchForm,
    route.query.origin as string,
  );
};

const getPermissionInfo = async () => {
  const { data } = await apiGetSaleCancellationPermissionList();
  if (!data.is_white && !data.is_director) {
    columns.value = columns.value.filter(item => item.dataIndex !== 'department_id');
  }
};
getPermissionInfo();
</script>
