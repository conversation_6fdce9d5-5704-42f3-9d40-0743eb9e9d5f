import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';

import { basicModal } from '@/components/order-business/basic-modals';
import { useUnityModal } from '@/components/unity-modal';

import type { IReceiveStatus } from '../data';
import { fetchReceiveInfo, hasPermissionCloseDispatch, updateReceive } from '../services';

/** 订单接单控制hook */
export function useReceiveInfo() {
  const route = useRoute();
  const domain = route.query.origin || window.location.origin;
  const { onToggleComponent } = useUnityModal();

  /** 接单状态信息 */
  const receiveStatus = ref<IReceiveStatus>();

  /** 获取接单状态数据 */
  function getReceiveData() {
    fetchReceiveInfo().then(res => {
      receiveStatus.value = res?.data?.receive_status;
    });
  }

  /**  */
  const applyTextData = {
    0: { text: '申请解除', color: '#3777ff' },
    1: { text: '申请解除中', color: '#fa8c16' },
    2: { text: '申请解除失败', color: '#f5222d' },
  };

  /** 订单状态修改提示 */
  const tooltipText = computed(() => {
    let tooltipText: string;
    const { dispatch_info, is_dispatch } = receiveStatus.value || {};
    if (!dispatch_info?.is_dispatch || (dispatch_info?.no_order_type === 2 && !is_dispatch)) {
      tooltipText = '接单模式，开启即指能接受订单流入，非接单时段请自觉关闭或通过”接单高级设置“进行自动化关闭。';
    } else {
      tooltipText = is_dispatch
        ? `距离下次关闭时间为：${dispatch_info?.next_end}`
        : `距离下次开启时间为：${dispatch_info?.next_start}`;
    }
    return tooltipText;
  });

  /** 接单高级设置 */
  function openParentPage() {
    window.parent.postMessage(
      {
        action: 'jump',
        jump_url: domain + '/distributor/auto-setting',
      },
      '*',
    );
  }

  /** 更改接单状态 */
  function handleReceiveChange(value: boolean) {
    if (value) {
      hasPermissionCloseDispatch().then(({ data }) => {
        if (data.allow) {
          updateReceive({ is_dispatch: true }).then(res => {
            message.success(res.message);
            getReceiveData();
          });
          return;
        }
        Modal.confirm({
          title: '温馨提示',
          okText: '前往设置',
          cancelText: '关闭',
          content:
            '您的店铺未自行设置营业班次，不可自行开启接单按钮，为保证您的店铺正常派单，请前往“接单高级设置”，设置营业班次。（此功能仅针对活动商家，若您非活动商家，可忽略）',
          onOk() {
            window.parent.postMessage({ action: 'jump', jump_url: '/distributor/auto-setting' }, '*');
          },
        });
      });
    } else {
      onToggleComponent(basicModal.orderReceivingState, {
        onResetOptions: getReceiveData,
      });
    }
  }

  return {
    receiveStatus,
    applyTextData,
    tooltipText,
    openParentPage,
    getReceiveData,
    handleReceiveChange,
  };
}
