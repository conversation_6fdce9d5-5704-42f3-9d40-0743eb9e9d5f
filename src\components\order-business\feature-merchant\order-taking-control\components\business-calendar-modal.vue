<template>
  <a-modal
    v-model:visible="bindVisible"
    :body-style="{ maxHeight: '800px', minHeight: '400px', overflow: 'auto' }"
    :footer="autoOpen ? undefined : false"
    :mask-closable="false"
    title="营业周历"
    width="709px"
    @cancel="handleClose"
  >
    <div class="flex-wrap flex-vertical flex-gap-24">
      <a-tabs
        v-show="!autoOpen"
        v-model:activeKey="activeKey"
        :tab-bar-style="{ margin: '-12px 0 0' }"
      >
        <a-tab-pane
          key="calendar"
          tab="营业周历"
        />
        <a-tab-pane
          key="holiday"
          tab="放假安排"
        />
        <a-tab-pane
          key="information"
          tab="功能使用说明"
        />
      </a-tabs>

      <!-- 营业周历 -->
      <template v-if="activeKey === 'calendar'">
        <a-alert type="error">
          <template #message>
            <div style="color: rgba(6, 21, 51, 0.65)">
              <span>系统根据营业班次为派单商家派发订单，为避免平台订单浪费，商家需在营业班次响应处理订单，若未正常处理订单，系统将按照</span>
              <a
                class="text-link"
                href="https://admin-vue.rrzu.com/admin-merchant/university?type=rules&origin=https%3A%2F%2Fadmin.rrzu.com&id=686"
                target="_blank"
              >《商家分班制规范2.0》</a>
              <span>进行处罚。</span>
            </div>
          </template>
        </a-alert>

        <div v-if="calendar.date_config">
          <span>您当前参与平台流量扶持活动，积极响应处理订单，可获得更多订单派发哦~</span>
          <span style="color: var(--ant-error-color)">（{{ calendar.date_config }}）</span>
        </div>

        <div class="flex-wrap flex-vertical flex-gap-16">
          <div
            v-for="(day, index) in week"
            :key="index"
            :class="[
              'date-item flex-wrap flex-y-center flex-gap-24',
              {
                business: calendar[day.value]?.type === 1,
                holiday: calendar[day.value]?.type === 2,
                platform: calendar[day.value]?.type === 3,
              },
            ]"
          >
            <div class="label">
              {{ day.label }}
            </div>
            <div class="content flex-con">
              <span
                v-if="calendar[day.value]?.type === 3"
                style="color: var(--ant-warning-color)"
              >【平台配置】</span>
              <span>{{ calendar[day.value]?.context }}</span>
              <span v-if="[1, 3].includes(calendar[day.value]?.type)">
                {{ ` | 日订单上限：${calendar[day.value]?.limit}` }}
              </span>
            </div>
          </div>
        </div>
      </template>
      <!-- 放假安排 -->
      <template v-else-if="activeKey === 'holiday'">
        <div
          v-if="holiday?.length"
          class="flex-wrap flex-vertical flex-gap-16"
        >
          <div>放假任务设置</div>
          <div
            v-for="(task, index) in holiday"
            :key="index"
            class="date-item normal flex-wrap flex-gap-24"
          >
            <div class="label flex-wrap flex-x-center flex-y-center">
              <div>
                <span>{{ task.title }}</span>
                <span style="white-space: nowrap">{{ `（ID: ${task.id}）` }}</span>
              </div>
            </div>
            <div class="content flex-con flex-wrap flex-y-center">
              <div>
                <span>{{ `${task.start_day}~${task.end_day}` }}</span>
                <span
                  v-if="task.platformConfig"
                  style="color: var(--ant-error-color)"
                >
                  {{ `（${task.platformConfig.join('、')}为平台配置时段，需正常营业）` }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          目前未设置放假任务
        </div>
      </template>
      <!-- 功能使用说明 -->
      <template v-else>
        <div class="infos-item flex-wrap flex-vertical flex-y-start">
          <div class="title">
            一、查看当天营业班次
          </div>
          <a-image src="https://img1.rrzuji.cn/uploads/scheme/2409/13/m/wLyeTSJ7SKOHynexS0vI.png" />
          <ul>
            <li>默认选择当天营业星期，右侧显示营业班次，日接单上限与已接订单量。</li>
          </ul>
        </div>

        <div class="infos-item flex-wrap flex-vertical flex-y-start">
          <div class="title">
            二、查看其他营业班次
          </div>
          <a-image src="https://img1.rrzuji.cn/uploads/scheme/2409/13/m/EBKqeHk8IymMN2kyagqW.png" />
          <ul>
            <li>点击不同星期可切换查看营业情况；</li>
            <li>“蓝色字体+蓝色边框”为当天营业星期；</li>
            <li>蓝点表示商家已设置营业班次，灰点表示放假或未设置营业班次，黄点表示平台配置营业班次。</li>
          </ul>
        </div>

        <div class="infos-item flex-wrap flex-vertical flex-y-start">
          <div class="title">
            三、修改/设置营业班次
          </div>
          <a-image
            src="https://img1.rrzuji.cn/uploads/scheme/2409/08/m/AJoNbeUdz8HpO3APl4RV.jpg"
            style="width: 136px"
          />
          <ul>
            <li>点击“接单高级设置”，可跳转至班次配置页面，修改营业班次。</li>
          </ul>
        </div>

        <div class="infos-item flex-wrap flex-vertical flex-y-start">
          <div class="title">
            四、开启/关闭接单按钮
          </div>
          <a-image
            src="https://img1.rrzuji.cn/uploads/scheme/2409/08/m/9liFAcclTFT54Xd5mVCE.jpg"
            style="width: 177px"
          />
          <ul>
            <li>
              系统根据营业班次自动开启/关闭接单按钮，非营业班次（夜间时段除外）不可自行打开接单按钮，营业班次时段特殊原因下可自行关闭接单按钮。
            </li>
          </ul>
        </div>
      </template>
    </div>

    <template #footer>
      <div class="flex-wrap flex-x-center">
        <a-popconfirm
          v-if="!neverMind"
          @confirm="neverMind = true"
        >
          <template #title>
            <div style="width: 260px">
              若您选择“不再提示”，后续存在班次调整时，将不再为你推送营业周历，你需自行查看，是否确认关闭推送？
            </div>
          </template>
          <a-checkbox :checked="neverMind">
            不再提示
          </a-checkbox>
        </a-popconfirm>
        <a-checkbox
          v-else
          v-model:checked="neverMind"
        >
          不再提示
        </a-checkbox>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { onMounted, type PropType, ref } from 'vue';

import { useVModel } from '@/hook';
import { getCookie, setCookie } from '@/utils/cookies';

import type { ICalendar, IHoliday } from '../data';
import { getHolidayCalendar } from '../services';

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'showTips'): void;
}>();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  calendar: {
    type: Object as PropType<ICalendar>,
    default: () => ({}),
  },
});

const storeKey = 'ignore_business_calendar';
const countKey = 'business_calendar_tips_count';
const popupEveryMondayKey = 'business_calendar_every_week';

const week = [
  { label: '周一', value: 'Monday' },
  { label: '周二', value: 'Tuesday' },
  { label: '周三', value: 'Wednesday' },
  { label: '周四', value: 'Thursday' },
  { label: '周五', value: 'Friday' },
  { label: '周六', value: 'Saturday' },
  { label: '周日', value: 'Sunday' },
];

const bindVisible = useVModel(props, 'visible', emits);
const activeKey = ref('calendar');
const neverMind = ref(false);
const autoOpen = ref(false);
const holiday = ref<IHoliday[]>([]);

function getHoliday() {
  getHolidayCalendar().then(res => {
    holiday.value = res.data || [];
  });
}

function handleClose() {
  bindVisible.value = false;
  const bindCountKey = `${countKey}_${props.calendar.server_id}`;
  const bindStoreKey = `${storeKey}_${props.calendar.server_id}`;
  // 弹窗指引上限3次，之后不再提示
  if (autoOpen.value && Number(localStorage.getItem(bindCountKey)) < 3) {
    emits('showTips');
    localStorage.setItem(bindCountKey, String(Number(localStorage.getItem(bindCountKey)) + 1));
  }
  if (neverMind.value) localStorage.setItem(bindStoreKey, '1');
  autoOpen.value = false;
}

function handleAutoOpen() {
  autoOpen.value = true;
  bindVisible.value = true;
}

onMounted(() => {
  getHoliday(); // 初始化放假数据
  const { current_week, has_change, show_pop, server_id } = props.calendar;
  const bindStoreKey = `${storeKey}_${server_id}`;
  const bindPopupKey = `${popupEveryMondayKey}_${server_id}`;
  // 勾选不再提示 或者 非符合弹窗条件商家
  if (localStorage.getItem(bindStoreKey) || !show_pop) return;
  // 每周一首次进入弹窗一次
  if (current_week === 'Monday' && !getCookie(bindPopupKey)) {
    handleAutoOpen();
    setCookie(bindPopupKey, '1', {
      expires: new Date(Date.now() + 24 * 3600000),
    });
    return;
  }
  // 修改过营业班次，弹窗一次
  if (has_change) handleAutoOpen();
});
</script>

<style lang="less" scoped>
.date-item {
  .label {
    position: relative;
    flex: none;
    min-width: 100px;
    padding: 7px 16px;
    color: rgba(6, 21, 51, 0.85);
    text-align: center;
    background: rgba(55, 119, 255, 0.04);
    border: 1px solid rgba(55, 119, 255, 0.15);
    border-radius: 4px 4px 4px 4px;
  }

  .content {
    padding: 7px 16px;
    color: rgba(6, 21, 51, 0.45);
    background: rgba(55, 119, 255, 0.04);
    border-radius: 4px 4px 4px 4px;
  }

  &.business,
  &.platform,
  &.holiday {
    .label::after {
      position: absolute;
      top: -1px;
      right: -1px;
      width: 27px;
      height: 27px;
      padding-right: 2px;
      color: #fff;
      font-size: 12px;
      line-height: 16px;
      text-align: right;
      background: #dadce0;
      border-radius: 0 4px 0 0;
      content: '';
    }
  }

  &.normal {
    .label {
      width: 165px;
    }

    .content {
      color: rgba(6, 21, 51, 0.65);
    }
  }

  &.business,
  &.platform {
    .content {
      color: rgba(6, 21, 51, 0.65);
    }

    .label::after {
      background: linear-gradient(
        45deg,
        transparent 0%,
        transparent 50%,
        var(--ant-primary-6) 50%,
        var(--ant-primary-6) 100%
      );
      content: '班';
    }
  }

  &.holiday {
    .label::after {
      background: linear-gradient(45deg, transparent 0%, transparent 50%, #dadce0 50%, #dadce0 100%);
      content: '休';
    }
  }
}

.infos-item {
  gap: 12px;

  .title {
    color: rgba(6, 21, 51, 0.85);
    line-height: 16px;
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: inside;
  }
}
</style>
