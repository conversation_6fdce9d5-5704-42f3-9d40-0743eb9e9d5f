<template>
  <a-drawer
    v-model:visible="bindVisible"
    class="complaint-modal"
    :footer-style="{ textAlign: 'right' }"
    title="起租日申诉"
    :width="720"
  >
    <a-spin :spinning="loading">
      <div class="modal-content">
        <div class="info">
          <div class="base-data">
            <div class="sub-title">
              订单商品信息
            </div>
            <div class="good-info">
              <div
                v-for="item in goodItem"
                :key="item.key"
                class="row"
              >
                <div class="row-label">
                  {{ item.label }}：
                </div>
                <div class="row-value">
                  {{ complaintInfo.base_info?.[item.key] }}
                </div>
              </div>
            </div>
            <div class="normal-info">
              <div
                v-for="item in normalItem"
                :key="item.key"
                class="row"
              >
                <div class="row-label">
                  {{ item.label }}：
                </div>

                <div
                  v-if="item.key === 'total_rental_money'"
                  class="row-value"
                >
                  {{ complaintInfo[item.key] }}
                  <a-tag style="border: none">
                    日租金：{{ complaintInfo['rental_money'] }}元/天
                  </a-tag>
                </div>

                <div
                  v-else
                  class="row-value"
                >
                  {{ complaintInfo[item.key] || '--' }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-data">
            <div class="sub-title">
              起租日申诉
            </div>

            <a-form
              ref="formRef"
              class="info-form"
              :model="formData"
            >
              <div class="info-form-content">
                <a-form-item
                  label="新起租日"
                  name="new_date_start"
                >
                  <a-date-picker
                    v-model:value="formData.new_date_start"
                    :allow-clear="false"
                    :disabled-date="disabledDate"
                    value-format="YYYY-MM-DD"
                    @change="changeEnd"
                  />
                </a-form-item>
                <a-form-item label="新完租日">
                  {{ complaintInfo.time && complaintInfo.new_data_end }}
                </a-form-item>

                <a-form-item label="订单总租期">
                  {{ complaintInfo.time && complaintInfo.time.tenancy_text }}
                </a-form-item>
              </div>

              <div class="sub-title">
                问题描述
              </div>
              <div class="info-form-content">
                <a-form-item
                  label="我要反馈"
                  name="feedback"
                  required
                >
                  <a-textarea
                    v-model:value="formData.feedback"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                    :maxlength="300"
                    placeholder="请输入起租日申诉内容"
                    show-count
                  />
                </a-form-item>

                <a-form-item
                  label="上传凭证"
                  required
                >
                  <UploadComplaint
                    v-model:proof-audio="formData.proof_audio"
                    v-model:proof-img="formData.proof_img"
                  />
                </a-form-item>
              </div>
            </a-form>
          </div>
        </div>
      </div>
    </a-spin>

    <template #footer>
      <a-button
        style="margin-right: 8px"
        @click="emit('update:visible', false)"
      >
        取消
      </a-button>
      <a-button
        type="primary"
        @click="submit"
      >
        提交
      </a-button>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { type FormInstance, message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useVModel } from '@/hook';
import type { ComplaintInfoType } from '../data';
import { goodItem, normalItem } from '../config';
import { getOrderDetail, createWork } from '../services';
import UploadComplaint from './upload-complaint.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emit);

const loading = ref(false);
const formData = ref({
  proof_img: [],
  proof_audio: [],
  new_date_start: '',
  feedback: '',
});

// =======================  下单数据  =========================
/** 订单下单信息 */
const complaintInfo = ref<ComplaintInfoType>({});

// 打开弹窗时初始化数据
watch(
  () => props.visible,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

const honourMap = {
  '0': '距离到期大于7天',
  '-1': '距离到期0-7天',
  '1': '逾期0-7天',
  '7': '逾期7-35天',
  '35': '逾期35-65天',
  '65': '逾期65天以上',
};

/** 获取订单下单信息 */
function loadData() {
  loading.value = true;
  getOrderDetail({ order_id: props.orderId })
    .then(res => {
      formData.value.new_date_start = res.data.time?.start;
      complaintInfo.value = {
        ...res.data,
        time_date: res.data.time?.start + '~' + res.data.time?.end,
        new_data_end: res.data.time?.end,
        rental_type_text: res.data.rental_type == '0' ? '短租' : '长租',
        honour_type_text: honourMap[res.data.overdue],
        work_type_text: '起诉日申诉',
        deposit_amount: res.data.deposit?.amount,
        type: 8,
        new_data_end: res.data.time?.end,
      };
    })
    .catch((e) => {
      console.error(e);
      bindVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 更新结束租期 */
function changeEnd(date: string) {
  const oldStart = dayjs(complaintInfo.value.time.start);
  const oldEnd = complaintInfo.value.time.end;
  const newStart = dayjs(date);
  const startSub = oldStart.diff(newStart, 'days');
  const isAfter = oldStart.isAfter(newStart);
  let newTime: string;
  // 如果新的在旧的之前
  if (isAfter) {
    newTime = dayjs(oldEnd).subtract(startSub, 'days').format('YYYY-MM-DD');
  } else {
    newTime = dayjs(oldEnd).add(startSub, 'days').format('YYYY-MM-DD');
  }
  complaintInfo.value.new_data_end = newTime;
}

/** 限制当天之前的日期不可选 */
function disabledDate(current?: Dayjs) {
  return current && current >= dayjs(complaintInfo.value.time.start).endOf('days');
}

// ========================  提交  =========================
const formRef = ref<FormInstance>();

function submit() {
  if (!formData.value.proof_img?.length && !formData.value.proof_audio?.length) {
    return message.error('请上传凭证');
  }
  formRef.value.validate().then(() => {
    const postParams = {
      channel: 1,
      proof_img: formData.value.proof_img.join(','),
      proof_audio: formData.value.proof_audio.join(','),
      data: { new_date_start: formData.value.new_date_start },
      type: 8,
      order_id: props.orderId,
      feedback: formData.value.feedback,
    };
    loading.value = true;
    createWork(postParams)
      .then(res => {
        message.success(res.message);
        bindVisible.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  });
}
</script>

<style scoped lang="less">
.complaint-modal {
  .sub-title {
    margin-bottom: 12px;
    padding-left: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: bold;

    &::before {
      position: relative;
      top: 2px;
      right: 5px;
      display: inline-block;
      width: 4px;
      height: 14px;
      background: #3777ff;
      border-radius: 2px;
      content: '';
    }
  }

  .row {
    display: flex;
    align-items: center;
    margin-top: 8px;

    &-label {
      flex-basis: 84px;
      color: rgba(6, 21, 51, 0.45);
    }

    &-value {
      flex: 1;
      color: rgba(6, 21, 51, 0.85);
    }
  }

  .info {
    .base-data {
      .good-info {
        padding: 0 16px;
      }

      .normal-info {
        padding: 0 16px;
      }
    }

    .form-data {
      margin-top: 24px;

      .info-form {
        &-content {
          padding: 0 16px;
        }

        .err_msg {
          margin-top: 10px;
          color: #ff4d4f;
        }

        .right-row {
          display: block;
          width: 16px;
          height: 16px;
          margin: 0 8px;
          background: url('https://img1.rrzuji.cn/uploads/scheme/2212/01/m/L2ZjcybULbgHg1PRUOhl.png') no-repeat;
          background-size: 100% 100%;
        }

        .buyout {
          margin-bottom: 12px;
        }

        .buyout-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 8px;
          padding: 12px 16px;
          background-color: rgb(245, 247, 250);
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
