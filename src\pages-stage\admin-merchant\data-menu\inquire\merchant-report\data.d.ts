export interface ITableItemType {
  title: string;
  label: string[];
  reachValue: (string | number)[];
  realityValue: (string | number)[];
  is_pass: boolean[];
}

export interface IPenaltyResult {
  is_invalid: boolean;
  children?: IPenaltyResultChild[];
  resultChild?: IPenaltyResultChild;
}

export interface IPenaltyResultChild {
  date_range_str: string;
  invalid_time_str: string;
  is_invalid: boolean;
  name: string;
  penalty_content: string;
}
