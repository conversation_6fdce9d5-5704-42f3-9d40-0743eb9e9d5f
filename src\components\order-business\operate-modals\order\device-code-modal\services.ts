import { GET, POST } from '@/services/api';

function getPrefix() {
  return window.location.href.includes('super') ? '/super' : '';
}

export function apiGetCommodityList(data: Record<string, any>) {
  return POST(getPrefix() + '/device-code/upload-item-picture', data);
}

/**
 * @description: 获取订单序列号详情
 * @param {any} params
 * @return {*}
 */
export function getOrderCode(data: any, prefix = '') {
  return GET(prefix + '/device-code/order-code', data);
}

/**
 * @description: 获取模板列表
 * @param {any} params
 * @return {*}
 */
export function getModalList(data: any, prefix = '') {
  return GET(prefix + '/device-code/template-list', data);
}

/**
 * @description: 获取模板详情
 * @param {any} params
 * @return {*}
 */
export function getModalDetail(data: any, prefix = '') {
  return GET(prefix + '/device-code/template-info', data);
}

/**
 * @description 保存模板
 * @returns Promise<AxiosResponse<any>>
 */
export function saveModal(params: any, prefix = '') {
  return POST(prefix + '/device-code/template-save', params);
}

/**
 * @description:订单绑定序列号
 * @param {any} data
 * @return {*}
 */
export function serialBind(data: any, prefix = '') {
  return POST(prefix + '/device-code/save-bind', data);
}

export function getDeviceCodeDetail(params: { order_id: string }) {
  return GET('/device-code/order-detail', params);
}

export function saveItemList(data: { order_id: string; items: Record<string, any>[] }) {
  return POST('/device-code/save-item-list', data);
}
