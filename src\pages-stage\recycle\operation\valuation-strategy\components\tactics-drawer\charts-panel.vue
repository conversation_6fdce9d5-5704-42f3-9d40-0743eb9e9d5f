<template>
  <transition name="slide">
    <div
      v-show="visible"
      class="charts-conatiner"
    >
      <HeaderBlock :title="`价格趋势图表（${skuInfoList.length}个）`" />
      <div
        v-if="skuInfoList.length"
        class="charts-render-box"
      >
        <ChartsItem
          v-for="(item, index) in skuInfoList"
          :key="Math.ceil(Math.random() * 10e5) + index"
          :base-request-params="{ ...initParams, memory: item.memory }"
          :color-options="item.colorList"
          :label="`${goodsName} ${item.memory}`"
        />
      </div>
      <a-empty
        v-else
        :image="simpleImage"
        style="height: 300px; padding-top: 100px"
      />
    </div>
  </transition>
</template>

<script lang="ts" setup>
import { ref, watch, PropType } from 'vue';
import { Empty } from 'ant-design-vue';
import { getSkuInfo } from '../../service';
import { IOpenDrawerParams, IChartSkuInfo } from '../../data.d';
import HeaderBlock from './header-block.vue';
import ChartsItem from './charts-item.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: () => false,
  },
  goodsName: {
    type: String,
    default: '',
  },
  // 初始化请求参数
  initParams: {
    type: Object as PropType<Omit<IOpenDrawerParams, 'goods_name'>>,
    default: () => ({}),
  },
});

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const skuInfoList = ref<IChartSkuInfo[]>([]);

async function init() {
  const res = await getSkuInfo(props.initParams);
  const data = res.data || [];
  const options = data.map((item: { memory: string; colour_list: string[] }) => {
    const { memory, colour_list } = item;
    const colorOptions = colour_list.map((colorItem: string) => {
      return {
        label: colorItem,
        value: colorItem,
      };
    });
    return {
      memory,
      colorList: colorOptions,
    };
  });
  skuInfoList.value = options;
}

watch(() => props.initParams, init, { immediate: true });
</script>

<style scoped lang="less">
.charts-conatiner {
  box-sizing: border-box;
  width: 422px;
  height: calc(100vh - 55px);
  padding: 24px 0 0 24px;
}

.charts-render-box {
  display: grid;
  box-sizing: border-box;
  height: calc(100% - 62px - 10px);
  margin-top: 16px;
  padding: 5px 0 5px 5px;
  overflow-y: scroll;
}

// 图表渐显动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
// 估价面板伸缩动画
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-enter-from,
.slide-leave-to {
  width: 0 !important;
  opacity: 0;
}
</style>
