import { reactive } from 'vue';
import { decryptSensitiveInfo } from '@/utils/sensitive-info';
import type {  IReceiptInfo } from '@/components/order-business/typing';

export const useFlashDeliveryList = () => {
  const receiptInfo = reactive<IReceiptInfo>({});

    /** 获取收货人姓名 */
  function getNameValue(order_id) {
    return decryptSensitiveInfo({ order_id, sign: 256, type: 2 }).then(({data})  => {
      const { receipt_info } = data
      if (!receipt_info) return;
      receiptInfo.desensitized_name = receipt_info.desensitized_name;
    });
  }

  /** 获取收货人手机号 */
  function getReceivingPhoneValue(order_id) {
      return decryptSensitiveInfo({ order_id, sign: 64, type: 1 }).then(({data}) => {
        const { receipt_info } = data
        if (!receipt_info) return;
        receiptInfo.desensitized_phone = receipt_info.desensitized_phone;
    });
  }

  /** 获取收货地址 */
  function getAddressData(order_id) {
    return decryptSensitiveInfo({ order_id, sign: 512, type: 3 }).then(res => {
      if (res?.data?.receipt_info) {
        const { desensitized_address, desensitized_address_code } = res.data.receipt_info;
        receiptInfo.desensitized_address = desensitized_address;
        receiptInfo.desensitized_address_code = desensitized_address_code;
      }
    });
  }

  function clearObjectValues(obj) {
    if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
      throw new Error('请传入有效的非数组对象');
    }

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        obj[key] = null;
      }
    }
    return obj;
  }

  function  handleGetAddress(order_id) {
    clearObjectValues(receiptInfo);
    return Promise.all([getNameValue(order_id), getReceivingPhoneValue(order_id), getAddressData(order_id)]).then(()=>{
      return true;
    });
  }


  return{
    receiptInfo,
    handleGetAddress,
    clearObjectValues
  }
}
