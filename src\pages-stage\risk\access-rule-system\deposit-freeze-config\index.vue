<template>
  <layout-admin-page
    :navs="['风控', '风控准入设置', '自建押金冻结配置']"
    title="自建押金冻结配置"
    top-fixed
  >
    <!-- 标题后缀 -->
    <template #title-suffix>
      <a-radio-group
        v-model:value="versionValue"
        style="margin-left: 24px"
      >
        <a-radio-button :value="UVersionValue.preview">
          预览版
        </a-radio-button>
        <a-radio-button :value="UVersionValue.online">
          线上版
        </a-radio-button>
      </a-radio-group>
    </template>

    <!-- 页面功能按钮，标题右侧 -->
    <template #extra>
      <Transition
        mode="out-in"
        name="fade"
      >
        <div
          v-if="versionValue === UVersionValue.preview"
          class="header-btns"
        >
          <a-dropdown>
            <a-button>更多 <down-outlined /></a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="addRuleItem">
                  新增规则
                </a-menu-item>
                <a-menu-item @click="openUpdateLogModal">
                  修改日志
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

          <a-button @click="resetRuleList">
            重置
          </a-button>
          <a-button
            type="primary"
            @click="releaseRuleList"
          >
            立即生效
          </a-button>
        </div>
        <div
          v-else
          class="flex-wrap flex-y-center"
        >
          <a-tooltip
            placement="left"
            :title="
              onlineSwitch
                ? '已开启状态表示押金冻结配置正在生效中，用户下单后会进行押金评估，前端页面展示押金评估页面；'
                : '已关闭状态表示押金冻结配置未生效，用户下单后不会进行押金评估，直接免押下单，前端页面不展示押金评估页面；'
            "
          >
            <question-circle-outlined style="margin-right: 8px" />
          </a-tooltip>
          押金冻结开关：
          <a-switch
            :checked="onlineSwitch"
            @change="changeOnlineSwitch"
          />
        </div>
      </Transition>
    </template>

    <!-- 页面主体内容，slot=default一般不需要指定template -->
    <template #default>
      <div class="content">
        <a-spin
          :spinning="tbLoading"
          tip="加载中..."
        >
          <a-table
            v-for="item in tbDataSource"
            :key="item"
            bordered
            class="content-item"
            :columns="tbCol"
            :data-source="item"
            :pagination="false"
            :scroll="{ x: 1280 }"
          >
            <template #headerCell="{ column, title }">
              <template v-if="column.key === 'type'">
                <DeleteOutlined
                  v-if="versionValue === UVersionValue.preview"
                  class="del-icon-btn"
                  @click="delConfigItem(item)"
                />{{ title }}
              </template>
            </template>
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.key === 'type'">
                <div class="category-box">
                  <div class="category-box-item">
                    <a-tag color="#00C8BE">
                      类目
                    </a-tag>:
                    <template
                      v-for="tag in text.category"
                      :key="tag.id"
                    >
                      <a-tag>
                        {{ tag.name }}
                        <close-outlined
                          v-if="versionValue === UVersionValue.preview"
                          @click="delRuleItem(record.id, tag.id, '1')"
                        />
                      </a-tag>
                    </template>
                    <a-tag
                      v-if="versionValue === UVersionValue.preview"
                      style="background: #fff; border-style: dashed; cursor: pointer"
                      @click="openCategoryModal(record.id, text.category, selectedKeys.category)"
                    >
                      <plus-outlined />
                      添加类目
                    </a-tag>
                  </div>
                  <a-divider />
                  <div class="category-box-item">
                    <a-tag color="#3777FF">
                      型号
                    </a-tag>:
                    <template
                      v-for="tag in text.model"
                      :key="tag.id"
                    >
                      <a-tag>
                        {{ tag.name }}
                        <close-outlined
                          v-if="versionValue === UVersionValue.preview"
                          @click="delRuleItem(record.id, tag.id, '2')"
                        />
                      </a-tag>
                    </template>
                    <a-tag
                      v-if="versionValue === UVersionValue.preview"
                      style="background: #fff; border-style: dashed; cursor: pointer"
                      @click="openModelModal(record.id, text.model, selectedKeys.model)"
                    >
                      <plus-outlined />
                      添加型号
                    </a-tag>
                  </div>
                </div>
              </template>
              <template v-else-if="column.key === 'deposit'">
                {{ text.name }}
              </template>
              <template v-else-if="column.key === 'condition'">
                {{ text.name }}
              </template>
              <template v-else-if="column.renderType === 'inputNumber'">
                <template v-if="versionValue === UVersionValue.preview">
                  <a-input-number
                    v-model:value="record.user_risk_levels[column.dataIndex]"
                    addon-after="%"
                    :bordered="false"
                    :controls="false"
                    :max="100"
                    :min="0"
                    @blur="changeUserRiskLevel(record, column.dataIndex)"
                  />
                </template>
                <template v-else>
                  {{ record.user_risk_levels[column.dataIndex] }}%
                </template>
              </template>
            </template>
          </a-table>
          <!-- 兜底规则 -->
          <a-table
            v-for="item in tbDefaultData"
            :key="item"
            bordered
            class="content-item"
            :columns="tbCol"
            :data-source="item"
            :pagination="false"
            :scroll="{ x: 1280 }"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.key === 'type'">
                <div class="category-box">
                  兜底规则
                </div>
              </template>
              <template v-else-if="column.key === 'deposit'">
                {{ text.name }}
              </template>
              <template v-else-if="column.key === 'condition'">
                {{ text.name }}
              </template>
              <template v-else-if="column.renderType === 'inputNumber'">
                <template v-if="versionValue === UVersionValue.preview">
                  <a-input-number
                    v-model:value="record.user_risk_levels[column.dataIndex]"
                    addon-after="%"
                    :bordered="false"
                    :controls="false"
                    :max="100"
                    :min="0"
                    @blur="changeUserRiskLevel(record, column.dataIndex)"
                  />
                </template>
                <template v-else>
                  {{ record.user_risk_levels[column.dataIndex] }}%
                </template>
              </template>
            </template>
          </a-table>
        </a-spin>
      </div>
    </template>
  </layout-admin-page>
  <a-modal
    v-model:visible="categoryVisible"
    :confirm-loading="modalLoading"
    :title="modalTitle"
    @ok="addCategoryRule(getDepositFreezeConfig)"
  >
    <a-tree-select
      v-model:value="categoryIds"
      allow-clear
      placeholder="请选择需要添加的类目"
      show-search
      style="width: 100%"
      tree-checkable
      :tree-data="menuTreeOptions"
      tree-node-filter-prop="title"
      @change="changeCategoryIds"
    >
      <template #tagRender="{ label, closable, onClose, value }">
        <a-tag
          v-if="mapIds.includes(value)"
          style="margin: 4px"
        >
          {{ label }}&nbsp;&nbsp;
        </a-tag>
        <a-tag
          v-else
          :closable="closable"
          style="margin: 4px"
          @close="onClose"
        >
          {{ label }}&nbsp;&nbsp;
        </a-tag>
      </template>
    </a-tree-select>
  </a-modal>
  <a-modal
    v-model:visible="modelVisible"
    :confirm-loading="modalLoading"
    :title="modalTitle"
    @ok="addModelRule(getDepositFreezeConfig)"
  >
    <a-tree-select
      v-model:value="modelIds"
      allow-clear
      placeholder="请选择需要添加的型号"
      show-search
      style="width: 100%"
      tree-checkable
      :tree-data="menuTreeOptions"
      tree-node-filter-prop="title"
      @change="changeModelIds"
    >
      <template #tagRender="{ label }">
        <a-tag style="margin: 4px">
          {{ label }}&nbsp;&nbsp;
        </a-tag>
      </template>
    </a-tree-select>
  </a-modal>

  <a-back-top />
  <UpdateLog v-model:value="updateLogVisible" />
</template>
<script setup lang="ts">
import { ref, onMounted, reactive, watch } from 'vue';
import { PlusOutlined, DownOutlined, CloseOutlined } from '@ant-design/icons-vue';

import { tbCol } from './config';
import { useOptions } from './composables/use-options';
import { useModal } from './composables/use-modal';
import {
  apiAddRuleItem,
  apiDelRuleItem,
  apiEditFreezeRule,
  apiGetDepositFreezeConfig,
  apiReleaseRuleList,
  apiResetRuleList,
  apiDelRuleConfigItem,
  apiChangeRuleStatus,
} from './services';
import { deepClone } from '@/utils/base';
import { message, Modal } from 'ant-design-vue';
import { DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';

import UpdateLog from './components/update-log/index.vue';
import { IRuleHashList, ITbDataItem, ITbDataRes } from './data';

const { taDataItem, getFreezeRuleOptions } = useOptions();
enum UVersionValue {
  'preview' = '0',
  'online' = '1',
}
const onlineSwitch = ref(false);
function changeOnlineSwitch() {
  Modal.confirm({
    title: onlineSwitch.value ? '关闭后，押金冻结将不生效，是否确认关闭？' : '开启后，押金冻结立即生效，是否确认开启？',
    onOk() {
      return apiChangeRuleStatus({
        status: onlineSwitch.value ? 0 : 1,
      }).then(() => {
        message.success('操作成功');
        onlineSwitch.value = !onlineSwitch.value;
      });
    },
  });
}

const tbDataSource = ref<Array<ITbDataItem[]>>([]);
const tbLoading = ref(false);
const tbDefaultData = ref<Array<ITbDataItem[]>>([]);
const tbDefaultLoading = ref(false);
const versionValue = ref(UVersionValue.preview);
const ruleHashList = reactive<IRuleHashList>({});
const selectedKeys = reactive({
  category: [],
  model: [],
});
watch(
  () => versionValue.value,
  () => getDepositFreezeConfig(),
);
function getTbDataItem(item: ITbDataRes, data: ITbDataItem): ITbDataItem[] {
  const arr: ITbDataItem[] = [];
  const { id, category_ids, model_ids, tmp_config } = item;
  const { condition, deposit } = data;
  const statusKey = `${id}-${deposit.id}-${condition.id}`;
  const obj: ITbDataItem = {
    id,
    condition,
    deposit,
    type: {
      category: category_ids,
      model: model_ids,
    },
    user_risk_levels: tmp_config.user_risk_levels[statusKey],
  };
  arr.push(obj);
  return arr;
}

function handleRuleList(ruleList: ITbDataRes[]): Array<ITbDataItem[]> {
  const tbData: Array<ITbDataItem[]> = [];
  ruleList.forEach(dataItem => {
    let arr: ITbDataItem[] = [];
    const data: ITbDataItem[] = deepClone(taDataItem.value);
    ruleHashList[dataItem.id] = deepClone(dataItem);
    data.forEach(item => {
      arr.push(...getTbDataItem(dataItem, item));
    });
    tbData.push(arr);
  });
  return tbData;
}
function getDepositFreezeConfig() {
  tbLoading.value = true;
  tbDefaultLoading.value = true;
  apiGetDepositFreezeConfig({
    status: versionValue.value,
  })
    .then(res => {
      const { default_rule, rule, category_ids, model_ids } = res.data;
      selectedKeys.category = category_ids;
      selectedKeys.model = model_ids;
      onlineSwitch.value = Boolean(res.data.status);
      tbDataSource.value = handleRuleList(rule);
      tbDefaultData.value = handleRuleList([default_rule]);
    })
    .finally(() => {
      tbLoading.value = false;
      tbDefaultLoading.value = false;
    });
}

onMounted(async () => {
  await getFreezeRuleOptions();
  getDepositFreezeConfig();
});

function changeUserRiskLevel(item: ITbDataItem, colKey: string) {
  const value = item.user_risk_levels?.[colKey];
  const key = `${item.id}-${item.deposit.id}-${item.condition.id}`;
  if (value === ruleHashList[item.id].tmp_config.user_risk_levels[key][colKey]) {
    return;
  }
  ruleHashList[item.id].tmp_config.user_risk_levels[key][colKey] = value;
  editFreezeRule(item);
}

function editFreezeRule(item: any) {
  apiEditFreezeRule({
    rule_id: item.id,
    config: JSON.stringify(ruleHashList[item.id].tmp_config),
  })
    .then(() => {
      message.success({
        content: '操作成功',
        key: 'editFreezeRule',
      });
    })
    .catch(() => {
      getDepositFreezeConfig();
    });
}
// useMoreBtn
function addRuleItem() {
  apiAddRuleItem().then(() => {
    getDepositFreezeConfig();
  });
}
function delRuleItem(ruleId: any, id: number, type: '1' | '2') {
  Modal.confirm({
    title: '确认删除吗？',
    onOk() {
      const map_id = [id];
      const rule_id = ruleId;
      apiDelRuleItem({
        type,
        map_id,
        rule_id,
      }).then(() => {
        message.success('操作成功');
        getDepositFreezeConfig();
      });
    },
  });
}
function resetRuleList() {
  Modal.confirm({
    title: '确定将 【线上版】配置同步到 【预览版】 配置吗？',
    onOk() {
      return apiResetRuleList().then(() => {
        message.success('操作成功');
        getDepositFreezeConfig();
      });
    },
  });
}
function releaseRuleList() {
  Modal.confirm({
    title: '确定将【预览版】配置同步至【线上版】吗？',
    onOk() {
      return apiReleaseRuleList().then(() => {
        message.success('操作成功');
      });
    },
  });
}
const { modalTitle, modalLoading, menuTreeOptions, mapIds, useCategoryModal, useModelModal } = useModal();
const { categoryVisible, categoryIds, openCategoryModal, addCategoryRule, changeCategoryIds } = useCategoryModal();
const { modelVisible, modelIds, openModelModal, addModelRule, changeModelIds } = useModelModal();

const updateLogVisible = ref(false);
function openUpdateLogModal() {
  updateLogVisible.value = true;
}
function delConfigItem(item: ITbDataItem[]) {
  Modal.confirm({
    title: '确认删除吗？',
    onOk() {
      const id = item[0].id;
      return apiDelRuleConfigItem({
        rule_id: id,
      }).then(() => {
        message.success('操作成功');
        getDepositFreezeConfig();
      });
    },
  });
}
</script>
<style lang="less" scoped>
.header-btns {
  .ant-btn {
    margin-left: 8px;
  }
}
.content {
  padding: 0 24px;
  background-color: #fff;
  .content-item {
    margin-top: 24px;
  }
  .category-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .category-box-item {
      min-height: 124px;
      .box-item-title {
        margin-bottom: 8px;
        padding: 8px 0;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        line-height: 22px;
        text-align: center;
        background: #f0f7ff;
        border-radius: 4px;
      }
      .ant-tag {
        margin: 4px;
      }
    }
  }
  .ant-pagination {
    margin-top: 24px;
    text-align: right;
  }
}
.add-tree {
  :deep(.ant-select-selector) {
    max-height: 35vh;
    overflow: scroll;
  }
}
:deep(.ant-table-cell) {
  &:has(.ant-input-number) {
    padding: 0;
  }

  .ant-input-number {
    width: 100%;
    height: 55px;
    margin: 0;
    .ant-input-number-input-wrap,
    .ant-input-number-input {
      height: 100%;
    }
  }
  .ant-input-number-group-addon {
    // 透明背景色
    background: rgba(0, 0, 0, 0);
    border: none;
    border-radius: 0;
  }
}
.del-icon-btn {
  margin-right: 8px;
  cursor: pointer;
  &:hover {
    color: #f5222d;
  }
}
</style>
