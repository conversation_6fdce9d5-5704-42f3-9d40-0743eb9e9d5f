<template>
  <layout-admin-page
    :navs="['趣回收', '库存管理', '入库单列表']"
    title="入库单列表"
  >
    <div class="container">
      <SearchGroup
        v-model:value="searchInfo"
        @confirm="getTableList"
      />
      <a-table
        class="table-box bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: 1400 }"
        @change="tableChange"
      />
    </div>
  </layout-admin-page>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { compact } from 'lodash';
import { useTable } from '@/hook/component/use-table';
import { getWarehouseList } from './service';
import { columns } from './config';
import { ISearchInfo } from './data';
import SearchGroup from './components/search-group.vue';

const route = useRoute();

const searchInfo = ref<ISearchInfo>({
  category_brand_model: [],
  dateValue: [],
  stock_count_id: '',
  stock_type: '',
  data_type: '',
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  api: getWarehouseList,
  searchForm: searchInfo.value,
  totalKey: 'data.pageData.count',
  formatHandle: (res: any) => {
    return res.data.listData;
  },
  formatSearchValue: (params: any) => {
    const requestParams = { ...params };
    delete requestParams.dateValue;
    delete requestParams.category_brand_model;
    if (params.dateValue.length) {
      requestParams.start_at = params.dateValue[0];
      requestParams.end_at = params.dateValue[1];
    }
    const [category_id, brand_id, model_id] = formatCategoryPrams(params.category_brand_model);
    return {
      ...requestParams,
      category_id,
      brand_id,
      model_id,
    };
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const formatCategoryPrams = (data: number[][]) => {
  const category_id: number[] = [],
    brand_id: number[] = [],
    model_id: number[] = [];
  if (data.length > 0) {
    data.forEach(item => {
      const [c, b, m] = item;
      if (item.length === 1) {
        category_id.push(c);
      } else if (item.length === 2) {
        category_id.push(c);
        brand_id.push(b);
      } else if (item.length === 3) {
        category_id.push(c);
        brand_id.push(b);
        model_id.push(m);
      }
    });
  }
  return [[...new Set(category_id)], [...new Set(brand_id)], [...new Set(model_id)]].map(i => i.join(','));
};

// 获取url参数进行默认查询
const getUrlParams = () => {
  const query = route.query || {};
  const keysArr = Object.keys(query).filter(key => !['role', 'origin'].includes(key));
  if (keysArr.length) {
    const brandKeys = ['category_id', 'brand_id', 'model_id'];
    const brandValues = compact(brandKeys.map(key => Number(query[key])));
    if (brandValues.length) {
      searchInfo.value.category_brand_model.push(brandValues);
    }

    keysArr.forEach(key => {
      const value = query[key];
      if (searchInfo.value.hasOwnProperty(key)) {
        searchInfo.value[key] = value;
      }
    });
  }
};

onMounted(() => {
  getUrlParams();
  getTableList();
});
</script>
<style lang="less" scoped>
.container {
  padding: 2px 24px 26px 24px;
}
.table-box {
  margin-top: 24px;
}
</style>
