import { GET, POST } from '@/services/api';

export const fetchAll = (params: any): Promise<any> => {
  return GET('/admin/index-inf', params);
};

export const addUserInfo = (data: {
  User: {
    username: string;
    phone: string;
    id: any;
  };
}): Promise<any> => {
  return POST('/admin/create', data);
};

export const fetchOne = (id: string): Promise<any> => {
  return GET(`/admin/update/${id}`);
};

export const updateUserInfo = (data: {
  User: {
    username: string;
    phone: string;
    id: any;
  };
  id: any;
}): Promise<any> => {
  return POST('/admin/update', data);
};

export const deleteUserInfo = (data: any): Promise<any> => {
  return POST('/admin/delete', data);
};

export const fetchTreeData = (params: any): Promise<any> => {
  return GET('/admin/auth-inf', params);
};

export const updateTreeData = (data: any): Promise<any> => {
  return POST('/admin/auth-inf', data);
};
export const getUserInfo = (): Promise<any> => {
  return GET('/account/get-user-info');
};
