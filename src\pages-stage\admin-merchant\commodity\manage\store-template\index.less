.page {
  background-color: #fff;
  .page-header {
    padding: 24px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    border-bottom: 1px solid rgba(6, 21, 51, 0.06);
  }
  .page-content {
    display: flex;
    .left {
      min-width: 60%;
      height: calc(100vh - 142px);
      padding: 24px;
      :deep(.ant-alert-info) {
        background-color: var(--ant-primary-1);
        border: none;
      }
      :deep(.ant-alert-icon) {
        color: var(--ant-primary-6);
      }
      :deep(.ant-alert-message) {
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: var(--ant-heading-1-size);
      }
      .description {
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
      .colors {
        position: relative;
        .item {
          width: 64px;
          height: 32px;
          border-radius: 4px;
          cursor: pointer;
        }
        .check {
          position: absolute;
          top: 0;
          left: 0;
          font-size: 16px;
        }
      }
      .color-select {
        display: flex;
        align-items: center;
        .color-box {
          width: 260px;
        }
      }
      .preview-button {
        display: flex;
        padding: 8px 16px;
        color: var(--ant-primary-color);
        font-size: 14px;
        background-color: var(--ant-primary-1);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3 linear;
        &:hover {
          background-color: var(--ant-primary-2);
        }
      }
      .preview {
        &:deep(.ant-btn) {
          color: var(--ant-primary-1);
        }
      }
    }
    .right {
      flex: 1;
      padding: 24px;
      border-left: 1px solid rgba(6, 21, 51, 0.06);
      .title {
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 14px;
      }
      .page-box {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -4px;
        .button-wrapper {
          flex: 1;
          min-width: 50%;
          padding: 4px;
          :deep(.ant-btn) {
            width: 100%;
          }
          .button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 6px 0;
            overflow: hidden;
            color: rgba(6, 21, 51, 0.65);
            white-space: nowrap;
            background: #f5f7fa;
            border: 1px solid transparent;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s linear;
            &:hover {
              color: var(--ant-primary-color);
              border-color: var(--ant-primary-color);
            }
          }
        }
      }
    }
  }
}
.mt-24 {
  margin-top: 24px;
}
.mt-16 {
  margin-top: 16px;
}
.mb-8 {
  height: 32px;
  margin-bottom: 8px;
}
.h3 {
  margin-bottom: 8px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 14px;
}
