<template>
  <layout-shops-page
    :content-style="{ padding: '24px' }"
    title="商品资质编辑"
    :top-style="{ padding: '24px', borderBottom: '1px solid #DADCE0' }"
  >
    <template #title>
      <div
        class="title"
        @click="goBack(EQualificationFileType.GOODS, -1)"
      >
        <ArrowLeftOutlined style="margin-right: 16px" />
        {{ detailId ? '编辑商品资质' : '新增商品资质' }}
      </div>
    </template>
    <div
      v-if="detailId && formState?.reject_reason"
      class="alert-wrapper"
    >
      <h4>审核驳回原因</h4>
      <p>
        {{ formState?.reject_reason }}
      </p>
    </div>
    <header class="header-title">
      资质信息
    </header>
    <div class="form-box">
      <RForm
        ref="rFromRef"
        v-model:value="formState"
        :form-group="formGroup"
        :origin-props="{
          labelCol: { span: 3 },
        }"
      >
        <template #dict_id>
          <a-form-item
            label="资质类型"
            name="dict_id"
            :required="true"
          >
            <a-select
              v-model:value="formState.dict_id"
              :disabled="!!detailId"
              :options="typeOptions"
              placeholder="请选择"
              style="width: 100%"
              @change="onChangeDict"
            />
            <span
              v-if="formState?.prompt"
              class="tips"
            >{{ formState.prompt }}</span>
          </a-form-item>
        </template>
        <template #time>
          <div class="sceond-title">
            许可证有效期
          </div>
          <a-form-item
            label="开始日期"
            name="valid_start_at_label"
            :required="true"
          >
            <a-date-picker
              v-model:value="formState.valid_start_at_label"
              :disabled-date="current => startDisabledDate(current, formState.valid_end_at_label)"
              style="width: 60%"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
          <a-form-item
            label="截止日期"
            name="valid_end_at_label"
            :rules="[
              {
                required: !longTerm,
                message: '请选择截止日期',
              },
            ]"
          >
            <a-date-picker
              v-model:value="formState.valid_end_at_label"
              :disabled="!!longTerm"
              :disabled-date="current => endDisabledDate(current, formState.valid_start_at_label)"
              style="width: 60%"
              value-format="YYYY-MM-DD"
            />
            <a-form-item-rest>
              <a-checkbox
                v-model:checked="longTerm"
                class="checkbox-btn"
                @change="formState.valid_end_at_label = null"
              >
                长期
              </a-checkbox>
            </a-form-item-rest>
          </a-form-item>
        </template>
      </RForm>
    </div>
    <div class="bottom-box">
      <a-space>
        <a-button
          class="bottom-btn"
          @click="onCancel"
        >
          取消
        </a-button>
        <a-button
          class="bottom-btn"
          :loading="confirmLoading"
          type="primary"
          @click="onConfirm"
        >
          提交审核
        </a-button>
      </a-space>
    </div>
  </layout-shops-page>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { safeJsonStringify } from '@rrzu/utils';

import { getCredentialDictFiledList } from '@/pages-stage/commodity/dict/qualification/components/qualification-conifg/service';
import { EQualificationFileType, TQualificationFiled } from '@/pages-stage/commodity/dict/qualification/data.d';
import { deepClone } from '@/utils/base';

import UseCommonFun from '../industry-qualification-edit/composables/use-common-fun';
import { ECurrentDataType, EHandleType } from '../store-info/components/shop-qualification/data.d';
import { formGroup } from './config';
import { TFormState } from './data.d';
import { createCredentialGoods, getGoodsInfoDetailApi, updateCredentialGoods } from './service';
const { endDisabledDate, startDisabledDate, getFormatImgs, handleResponeImgs, goBack } = UseCommonFun();
const rFromRef = ref();
const route = useRoute();
const router = useRouter();
const detailId = computed(() => route.query?.id);
const isRelease = computed(() => route.query?.dataType === ECurrentDataType.RELEASE);
const typeOptions = ref([]);
const longTerm = ref(false);
const originalData = ref<TFormState>();
const formState = ref<TFormState>({
  dict_id: undefined,
  config_name: '',
  customize_name: '',
  valid_start_at_label: '',
  valid_end_at_label: '',
  reject_reason: '',
  image_list: [],
});

function onChangeDict(_: number, options: TQualificationFiled) {
  formState.value.prompt = options.config_prompt;
  formState.value.config_type = options.config_type;
}

async function getOptions() {
  const { data } = await getCredentialDictFiledList({ page_size: -1 });
  typeOptions.value = data.reduce(
    (acc: Array<{ label: string; value: number } & TQualificationFiled>, item: TQualificationFiled) => {
      if (item.config_type === EQualificationFileType.GOODS) {
        acc.push({
          label: item.secondary_category_name + '-' + item.config_name,
          value: item.id,
          ...item,
        });
      }
      return acc;
    },
    [],
  );
}

function onCancel() {
  router.back();
}

const confirmLoading = ref(false);
async function onConfirm() {
  if (safeJsonStringify(formState.value) === safeJsonStringify(originalData.value)) {
    message.info('数据未修改，请勿重复提交');
    return;
  }
  await rFromRef.value?.validate();
  try {
    confirmLoading.value = true;
    const { valid_end_at_label } = formState.value;
    const image_list = await getFormatImgs(formState.value.image_list);
    const queryData = {
      ...formState.value,
      id: Number(detailId.value) || undefined,
      valid_end_at_label: longTerm.value ? '-1' : valid_end_at_label,
      image_list,
    };
    const api = detailId.value ? updateCredentialGoods : createCredentialGoods;
    const { data } = await api(queryData);
    message.success('提交成功，即将跳转详情页', 1).then(() => {
      router.push({
        path: '/merchant/commodity/manage/goods-qualification-detail',
        query: {
          id: data.id,
          type: EHandleType.DETAIL,
          dataType: ECurrentDataType.RELEASE,
          go: '-2',
        },
      });
    });
  } finally {
    confirmLoading.value = false;
  }
}
async function getDetailInfo() {
  const { data } = await getGoodsInfoDetailApi({ id: detailId.value });
  const currentKey = isRelease.value ? 'release_goods_version' : 'use_goods_version';
  if (data[currentKey]) {
    const {
      dict_id,
      config_name,
      customize_name,
      valid_end_at_label,
      valid_end_at,
      server_credential_goods_image,
      valid_start_at_label,
      reject_reason,
    } = data[currentKey];

    const temp = {
      dict_id,
      config_name,
      customize_name,
      valid_start_at_label,
      reject_reason,
      valid_end_at_label: Number(valid_end_at) === -1 ? '' : valid_end_at_label,
      image_list: handleResponeImgs(server_credential_goods_image),
    };
    formState.value = temp;
    originalData.value = deepClone(temp);
    longTerm.value = Number(valid_end_at) === -1;
  }
}

onMounted(() => {
  getOptions();
  if (detailId.value) {
    getDetailInfo();
  }
});
</script>

<style scoped lang="less">
.title {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  text-align: left;
}
.alert-wrapper {
  width: 100%;
  margin-bottom: 24px;
  padding: 16px;
  word-wrap: break-word;
  background: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  h4 {
    margin-bottom: 2px;
    color: #ff4d4f;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
  p {
    margin-bottom: 2px;
  }
}
.header-title {
  margin-bottom: 20px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 16px;
}
.header-title::before {
  position: relative;
  top: 2px;
  right: 5px;
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #3777ff;
  border-radius: 20px;
  content: '';
}
.form-box {
  width: 900px;
  :deep(.ant-upload-list) {
    width: 600px;
  }
  .tips {
    color: #faad14;
    font-weight: 400;
    font-size: 12px;
    line-height: 22px;
  }
  .sceond-title {
    margin-bottom: 16px;
    padding-left: 2px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  .sceond-title::before {
    display: inline-block;
    margin-right: 2px;
    color: #ff4d4f;
    font-size: 14px;
    line-height: 1;
    content: '*';
  }
  .checkbox-btn {
    margin-left: 20px;
  }
}
.bottom-box {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  height: 72px;
  background-color: #fff;
  box-shadow: 0 -2px 20px 0 rgba(0, 0, 0, 0.05);
  .bottom-btn {
    height: 40px;
    padding: 8px 32px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
