import type { TableColumnType } from 'ant-design-vue';

import { reactive } from 'vue';

type BillingType = {
  columns: TableColumnType[];
};

export default function (): BillingType {

  const columns: TableColumnType[] = [
    {
      title: '基础运费',
      dataIndex: 'freight',
      key: 'freight',
    },
    {
      title: '保价',
      dataIndex: 'insure_fee',
      key: 'insure_fee',
    },
    {
      title: '包装',
      dataIndex: 'packed_fee',
      key: 'packed_fee',
    },
    {
      title: '纸质签回单',
      dataIndex: 'sign_paper_fee',
      key: 'sign_paper_fee',
    },
    {
      title: '优惠',
      dataIndex: 'platform_substract',
      key: 'platform_substract',
    },
    {
      title: '其他',
      dataIndex: 'other_fee',
      key: 'other_fee',
    },
    {
      title: '总计',
      dataIndex: 'total',
      key: 'total',
    },
  ];

  const billing = reactive<BillingType>({
    columns
  });

  return billing;
}
