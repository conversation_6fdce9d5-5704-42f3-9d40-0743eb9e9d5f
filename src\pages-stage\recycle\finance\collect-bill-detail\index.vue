<template>
  <layout-admin-page
    :navs="navsList"
    :title="navsList[navsList.length - 1]"
  >
    <template #title-prefix>
      <ArrowLeftOutlined
        style="margin-right: 16px; font-size: 16px"
        @click="router.go(-1)"
      />
    </template>
    <BaseInfo
      ref="baseInfoRef"
      v-model:formValue="baseinfo"
    />
    <CollectionInfo
      ref="collectionInfoRef"
      v-model:value="collectioninfo"
    />
    <SourceInfo
      ref="sourceInfoRef"
      v-model:value="sourceInfo"
      :is-first="isFirst"
      :server-id="baseinfo.server_id"
      @handle-total="handleTotal"
    />
    <Attachments v-model:imageValue="imageArr" />
  </layout-admin-page>
  <div class="footer-block">
    <div class="footer-title">
      收款核销总金额(元)：{{ totalnum || '-' }}
    </div>
    <div
      v-if="hasBtn"
      class="footer-content"
    >
      <a-space>
        <a-button
          :loading="saveloading"
          @click="handleSave(1)"
        >
          保存
        </a-button>
        <a-button
          :loading="applyloading"
          type="primary"
          @click="handleSave(2)"
        >
          审核
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick } from 'vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import BaseInfo from './components/base-info.vue';
import CollectionInfo from './components/collection-info.vue';
import SourceInfo from './components/source-info.vue';
import Attachments from './components/attachments.vue';
import {
  saveVerification,
  getVerificationDetail,
  editVerification,
  getServerOrderList,
  IVerification,
} from './service';

const router = useRouter();
const route = useRoute();
const navsList = ref(['回收', '财务管理', '收款单', '收款单详情']);
const baseinfo = ref({
  coin_type: 1,
  order_id: undefined,
  server_id: undefined,
  date: '',
  price: '',
  remark: '',
});
const baseInfoRef = ref();
const isFirst = ref(true);
const hasBtn = ref(true);
const saveloading = ref(false);
const applyloading = ref(false);
const collectioninfo = ref([
  {
    pay_account: 1,
    pay_type: 1,
    amount: '',
    payment_flow_sn: '',
    remark: '',
  },
  //空行合计
  {},
]);
const collectionInfoRef = ref();
const sourceInfo = ref([{}]);
const sourceInfoRef = ref();
const imageArr = ref([]);
const totalnum = ref(0);
const handleGetDetail = async () => {
  const { data: detail } = await getVerificationDetail({ order_id: route.query.id });
  const {
    coin_type,
    order_id,
    server_id,
    date,
    remark,
    wait_pay_price,
    images,
    pay_order_list,
    source_order_list,
    status,
  } = detail;
  if (status === '1') {
    hasBtn.value = false;
  }
  baseinfo.value = {
    coin_type: Number(coin_type),
    order_id,
    server_id,
    date,
    price: wait_pay_price,
    remark,
  };
  imageArr.value = images;
  collectioninfo.value = [
    ...pay_order_list.map((item: any) => ({
      pay_account: Number(item.pay_account),
      pay_type: Number(item.pay_type),
      amount: item.amount,
      payment_flow_sn: item.payment_flow_sn,
      remark: item.remark,
      id: item.id,
    })),
    //合计空行
    {},
  ];
  sourceInfo.value = [...source_order_list, {}];
  nextTick(() => {
    isFirst.value = false;
    collectionInfoRef.value.handleBlur();
    sourceInfoRef.value.handleBlur();
    sourceInfoRef.value.handleTableDom();
    totalnum.value = sourceInfoRef.value.totalAmountObj.pay_price;
  });
};
const init = async () => {
  if (route.query.type === 'create') {
    isFirst.value = false;
    navsList.value = ['回收', '财务管理', '收款单', '新增收款单'];
  }
  if (route.query.type === 'cllect') {
    isFirst.value = false;
    navsList.value = ['回收', '财务管理', '收款单', '新增收款单'];
    const params = window.localStorage.getItem('sale-bill-item') || '';
    const { ids, serverId } = JSON.parse(params);
    baseinfo.value.server_id = serverId;
    baseInfoRef.value.handlegetServerWaitPayMoney(serverId);
    const res = await getServerOrderList({
      source_order_id: ids,
      page: 1,
      page_size: ids.length,
      server_id: serverId,
    });
    sourceInfo.value = [...res.data.listData, {}];
  }
  if (route.query.id) {
    handleGetDetail();
  }
};
const collectionValidate = () => {
  collectioninfo.value.forEach((item: any) => {
    if (!String(item.amount)) {
      collectionInfoRef.value.handleError();
      throw message.error('请填写收款金额');
    }
  });
};
const sourceInfoValidate = () => {
  const len = sourceInfo.value.length;
  sourceInfo.value.forEach((item: any, index: number) => {
    if (index !== len - 1 && !item.pay_price && item.pay_price !== 0) {
      sourceInfoRef.value.handleError();
      throw message.error('请填写本次核销金额');
    }
  });
};
const handleSave = async (method: number) => {
  method === 1 ? (saveloading.value = true) : (applyloading.value = true);
  try {
    try {
      await baseInfoRef.value.baseForm.validate();
    } catch (err) {
      message.error('请填写基础信息');
    }
    collectionValidate();
    if (sourceInfo.value.length === 1) {
      baseInfoRef.value.scrollIntoDom();
      return message.error('请添加源单信息');
    }
    sourceInfoValidate();
    const params: IVerification = {
      method,
      server_id: baseinfo.value.server_id,
      coin_type: baseinfo.value.coin_type,
      remark: baseinfo.value.remark,
      date: baseinfo.value.date,
      images: imageArr.value,
      pay_order_list: collectioninfo.value.filter((_, ind) => ind !== collectioninfo.value.length - 1),
      source_order_list: sourceInfo.value
        .map((item: any) => ({
          source_order_type: item.source_order_type,
          source_order_id: item.source_order_id,
          pay_price: item.pay_price,
          id: item.id,
        }))
        .filter((_, ind) => ind !== sourceInfo.value.length - 1),
    };
    if (baseinfo.value.order_id) {
      params.order_id = baseinfo.value.order_id;
    }
    const fn = route.query.id ? editVerification : saveVerification;
    await fn(params);
    message.success(method === 1 ? '保存成功' : '审核成功');
    router.push('/recycle/finance/collect-bill');
  } finally {
    method === 1 ? (saveloading.value = false) : (applyloading.value = false);
  }
};
const handleTotal = (value: number) => {
  totalnum.value = value;
};

onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.search-form {
  margin-bottom: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
}
.list-mian {
  padding: 0 24px 24px;
  background-color: #fff;
}
.operation {
  color: #3777ff;
}
.footer-block {
  position: fixed;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 88px;
  background: #fff;
  box-shadow: 0 -2px 20px 0 rgba(0, 0, 0, 0.05);
  .footer-title {
    position: absolute;
    left: 20px;
    color: #00c8be;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-shadow: 0 -2px 20px rgba(0, 0, 0, 0.05);
  }
}
</style>
