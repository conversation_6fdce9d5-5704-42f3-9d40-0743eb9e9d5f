import { ColumnProps } from 'ant-design-vue/lib/table';

export const columns: ColumnProps = [
  {
    dataIndex: 'id',
    title: '序号',
    width: 60,
  },
  {
    dataIndex: 'type',
    title: '行类型',
    width: 76,
  },
  {
    dataIndex: 'category_name',
    title: '品类',
    width: 76,
  },
  {
    dataIndex: 'brand_name',
    title: '品牌',
    width: 76,
  },
  {
    dataIndex: 'model_name',
    title: '型号',
    width: 126,
  },
  {
    dataIndex: 'sku_info',
    title: 'sku信息',
    width: 182,
  },
  {
    dataIndex: 'sku_id',
    title: 'SKU ID',
    width: 126,
  },
  {
    dataIndex: 'warehouse_name',
    title: '仓位',
    width: 126,
  },
  {
    dataIndex: 'start_stock',
    title: '期初库存',
    width: 88,
  },
  {
    dataIndex: 'end_stock',
    title: '期末库存',
    width: 88,
  },
  {
    dataIndex: 'stock',
    title: '即时库存',
    width: 88,
  },
];
