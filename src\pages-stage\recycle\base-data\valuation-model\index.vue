<template>
  <layout-admin-page title="评估选项字典">
    <div class="container flex-wrap">
      <div class="tree-container">
        <a-tree
          v-model:expandedKeys="expandedKeys"
          v-model:selected-keys="activeKey"
          block-node
          :field-names="{ children: 'children', title: 'name', key: 'key' }"
          :load-data="onLoadTreeData"
          :tree-data="treeData"
          @select="onTreeSelect"
        />
      </div>

      <div class="card-container flex-con">
        <div class="card-list">
          <a-spin :spinning="loading">
            <div
              v-if="navList instanceof Array && navList.length"
              class="nav-bar flex-wrap flex-x-justify flex-y-center"
            >
              <div class="breadcrumb flex-wrap flex-y-center">
                <a-breadcrumb>
                  <template #separator>
                    <RightOutlined />
                  </template>
                  <a-breadcrumb-item
                    v-for="(item, index) in navList"
                    :key="index"
                  >
                    {{ item }}
                  </a-breadcrumb-item>
                </a-breadcrumb>
              </div>
              <a-button
                type="primary"
                @click="openModal(false)"
              >
                <template #icon>
                  <PlusOutlined />
                </template>
                新增评估属性
              </a-button>
            </div>
            <template v-if="optionList.length">
              <a-card
                v-for="(item, index) in optionList"
                :key="index"
                class="custom-card"
                hoverable
                :title="item.name"
              >
                <template #default>
                  <a-select
                    class="block"
                    :options="item.option_data"
                    :value="item.title_data.title"
                  />
                </template>
                <template #actions>
                  <form-outlined @click="() => openModal(true, item.id)" />
                  <a-switch
                    :checked="item.is_open"
                    checked-children="开"
                    :checked-value="1"
                    un-checked-children="关"
                    :un-checked-value="0"
                    @change="(checked: number | string) => switchOpen(item, checked)"
                  />
                </template>
              </a-card>
            </template>
            <a-empty v-else />
          </a-spin>
        </div>
      </div>
    </div>
  </layout-admin-page>
  <control-modal
    ref="controlRef"
    @ok="onOk"
  />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { TreeProps } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { EventDataNode } from 'ant-design-vue/lib/tree';
import { FormOutlined, PlusOutlined, RightOutlined } from '@ant-design/icons-vue';

import controlModal from './components/control-modal.vue';
import type { IOptionItem, ITreeItem } from './data';
import * as api from './service';

const treeData = ref<ITreeItem[]>([
  {
    name: '基础评估项',
    key: 'base',
    type: 1,
  },
  {
    name: '检测评估项',
    key: 'qa',
    type: 2,
  },
  {
    name: '功能评估项',
    key: 'func',
    type: 3,
  },
]);

const expandedKeys = ref();
const activeKey = ref();
const currentNode = ref<ITreeItem>();
const optionList = ref<IOptionItem[]>([]);
const loading = ref<boolean>(true);

// 当前评估项路径
const navList = computed<string[]>(() => {
  const keyArr = (activeKey.value[0] || '').split('-');
  const strArr = [];
  const parent = treeData.value.find(item => item.key === keyArr[0]);
  parent && strArr.push(parent.name);
  if (parent && keyArr.length > 1) {
    let children = (parent.children || []).find(item => item.id == keyArr[1]);
    children && strArr.push(children.name);
  }
  return strArr;
});

// 初始化
const init = () => {
  expandedKeys.value = ['base'];
  activeKey.value = ['base'];
  currentNode.value = treeData.value.find(item => item.key === activeKey.value[0]);
  loadOptionList();
};

// 选中树节点
const onTreeSelect = (
  keys: any,
  { selected, node: { dataRef } }: { selected: boolean; node: { dataRef: ITreeItem } },
) => {
  currentNode.value = dataRef;
  if (!selected) {
    activeKey.value = [dataRef.key];
  } else {
    loadOptionList();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
};

// 加载选项列表
const loadOptionList = () => {
  loading.value = true;
  const { type, id } = currentNode.value || {};
  api
    .queryEvaluateList({ type, id })
    .then(({ data }) => {
      optionList.value = (data || []).map(item => {
        item.option_data = item.option_data.map(({ title: label }, index) => ({ label, value: index }));
        return item;
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

// 加载树的子节点
const onLoadTreeData: TreeProps['loadData'] = ({ dataRef }) => {
  return new Promise(resolve => {
    if (dataRef?.children) {
      resolve();
      return;
    }
    api.queryEvaluateNameList({ type: dataRef?.type }).then(({ data }) => {
      data = data || [];
      if (dataRef) {
        dataRef.children = data.map((item: any) => {
          item.isLeaf = true;
          item.type = dataRef.type;
          item.key = dataRef.key + '-' + item.id;
          return item;
        });
      }
      treeData.value = [...treeData.value];
      resolve();
    });
  });
};

// 切换开关
const switchOpen = async (row: IOptionItem, is_open: number | string) => {
  const closeLoading = message.loading('切换中');
  try {
    await api.toggleEvaluateOptionOpen({ id: row.id, is_open });
    row.is_open = is_open;
  } catch (error) {
  } finally {
    closeLoading();
  }
};

// 弹窗控制
const controlRef = ref();
const openModal = (isEdit: boolean, id?: string) => {
  let title = isEdit ? '编辑' : '新增';
  title += navList.value[0];
  const { type } = currentNode.value || {};
  controlRef.value && controlRef.value.open(title, type, id);
};

init();

const onOk = (payload?: IOptionItem) => {
  loadOptionList();
  // 更新树子节点
  if (payload) {
    const treeNode = treeData.value.find(node => {
      return node.type === payload.type;
    });
    treeNode!.children = undefined;
    onLoadTreeData({ dataRef: treeNode } as EventDataNode);
  }
};
</script>

<style lang="less" scoped>
.container {
  // 覆盖预设主题色
  --ant-primary-2: rgba(0, 200, 190, 0.1);
  .block {
    width: 100%;
  }
  margin-top: -24px;
  .tree-container,
  .card-container {
    padding: 24px 32px;
  }
  .tree-container {
    min-width: 300px;
    min-height: calc(100vh - 150px);
    border-right: 1px solid rgba(6, 21, 51, 0.06);
    :deep(.ant-tree .ant-tree-node-content-wrapper) {
      padding: 3px 4px;
    }
  }
  .card-container {
    min-height: calc(100vh - 150px);
    :deep(.ant-empty) {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 100%;
    }
    .card-list {
      height: 100%;
      padding: 24px 0 24px 24px;
      background: #f5f7fa;
      border-radius: 4px;
      .nav-bar {
        padding: 0 24px 24px 0;
        .breadcrumb {
          height: 14px;
          padding-left: 10px;
          border-left: 2px solid var(--ant-primary-color);
          :deep(.ant-breadcrumb) {
            color: rgba(6, 21, 51, 0.85);
            font-weight: 500;
            & > span:nth-child(2) {
              color: var(--ant-primary-color);
            }
          }
        }
      }
      :deep(.ant-card-head) {
        font-size: 14px;
        border: none;
      }
      :deep(.ant-card-body) {
        padding-top: 0;
      }
      :deep(.ant-card-actions) {
        background: #f9f9fb;
        & > li:not(:last-child) {
          border-color: rgba(6, 21, 51, 0.15);
        }
      }
      .custom-card {
        display: inline-block;
        width: calc((100% - 72px) / 3);
        margin: 0 24px 24px 0;
        :deep(.ant-select-selection-placeholder) {
          color: rgba(6, 21, 51, 0.65);
        }
      }
    }
  }
}
</style>
