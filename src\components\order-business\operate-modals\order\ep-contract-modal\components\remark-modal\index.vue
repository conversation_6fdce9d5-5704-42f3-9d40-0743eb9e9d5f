<template>
  <a-modal
    v-model:visible="bindVisible"
    class="enterprise-modal height-limit"
    title="企业备注"
    :width="800"
  >
    <a-spin :spinning="loading">
      <div class="track-box">
        <div
          v-for="(item, index) in data"
          :key="index"
          :class="['track-item', index === 0 ? 'track-item-active' : '']"
        >
          <div class="step-dot" />
          <div class="text-content">
            <div class="title">
              {{ item.content }}
            </div>
            <div class="time">
              {{ item.created_at }}
            </div>
          </div>
        </div>
        <div class="step-line" />
      </div>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        关闭
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { getRemarkList } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  contractId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);
const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

type TrackItem = {
  content: string;
  created_at: string;
};

const data = ref<TrackItem[]>([]);
const loading = ref(false);

function loadData(contract_id: string) {
  loading.value = true;
  getRemarkList({ contract_id })
    .then(res => {
      data.value = res.data;
    })
    .catch(() => {
      bindVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

watch(
  bindVisible,
  value => {
    if (value) {
      loadData(props.contractId);
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
.track-box {
  position: relative;
  padding-left: 12px;

  .track-item {
    position: relative;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 22px;
      margin-bottom: 8px;
      color: rgba(6, 21, 51, 0.45);

      .title {
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 14px;
      }
    }

    .text-content {
      margin-bottom: 36px;
      padding: 12px;
      color: rgba(6, 21, 51, 0.65);
      background: #f5f7fa;
      border-radius: 4px;

      .title {
        &.active {
          color: #3777ff;
        }

        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
      }

      .time {
        margin-top: 12px;
        color: rgba(6, 21, 51, 0.25);
        font-weight: 400;
        font-size: 14px;
      }

      .info {
        margin-bottom: 8px;
        padding-bottom: 8px;
        color: rgba(6, 21, 51, 0.85);
        border-bottom: 1px solid rgba(6, 21, 51, 0.06);
      }
    }
  }

  .track-item-active {
    .text-content {
      .title {
        color: var(--ant-primary-color);
      }
    }

    .step-dot {
      background: var(--ant-primary-color);
    }
  }
}

.step-line {
  position: absolute;
  top: 6px;
  left: 0;
  z-index: 0;
  width: 2px;
  height: calc(100% - 6px);
  background: rgba(6, 21, 51, 0.06);
}

.step-dot {
  position: absolute;
  top: 18px;
  left: -16px;
  width: 10px;
  height: 10px;
  background: #c1c4cc;
  border-radius: 50%;

  &::after {
    position: relative;
    top: 2px;
    left: 2px;
    display: block;
    width: 6px;
    height: 6px;
    background-color: #fff;
    border-radius: 50%;
    content: '';
  }
}
</style>
