<template>
  <bill-detail-item title="退款信息">
    <a-table
      :columns="refundInfoColumns"
      :data-source="refundInfoList"
      :loading="loading"
      :pagination="false"
      :scroll="{ x: '100%' }"
      :sticky="true"
    >
      <template #bodyCell="{ column }">
        <template v-if="column.key === 'action'">
          <a-button
            class="link"
            type="link"
            @click="toAgain"
          >
            重试
          </a-button>
        </template>
      </template>
    </a-table>
  </bill-detail-item>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { IRefundInfoList } from '../../data';
import { refundInfoColumns } from '../../config';
import BillDetailItem from '../bill-detail-item/index.vue';
import { message } from 'ant-design-vue';

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  refundInfoList: {
    type: Array as PropType<IRefundInfoList[]>,
    default: () => [],
  },
});

const toAgain = () => {
  message.warn('功能正在维护中！');
};
</script>

<style lang="less" scoped>
.link {
  padding-left: 0;
  color: #3777ff;
}
</style>
