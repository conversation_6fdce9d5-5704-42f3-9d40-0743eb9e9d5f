import { ref } from 'vue';
export const columns = [
  {
    title: '通道名称',
    dataIndex: 'name',
    width: 110,
  },
  {
    title: '通道类型',
    dataIndex: 'message_type_trans',
    width: 110,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 110,
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    width: 110,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 110,
  },
];
//添加联系人表单
export const addFormState = ref({
  name: '',
  contact: '',
  code: '',
});
export const smsColumns = [
  {
    title: '手机号码',
    dataIndex: 'contact',
    key: 'contact',
    width: 110,
  },
  {
    title: '用户昵称',
    dataIndex: 'name',
    key: 'name',
    width: 110,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 50,
  },
];

export const weixinColumns = [
  {
    title: '微信账号',
    dataIndex: 'contact',
    key: 'contact',
    width: 110,
  },
  {
    title: '微信昵称',
    dataIndex: 'name',
    key: 'name',
    width: 110,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 50,
  },
];

export const phoneColumns = [
  {
    title: '手机号码',
    dataIndex: 'contact',
    key: 'contact',
    width: 110,
  },
  {
    title: '用户昵称',
    dataIndex: 'name',
    key: 'name',
    width: 110,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 50,
  },
];
