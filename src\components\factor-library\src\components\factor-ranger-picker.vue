<template>
  <a-range-picker
    v-model:value="dateValue"
    v-bind="attrs"
    :placeholder="datePlaceholder"
    value-format="X"
  />
</template>

<script setup lang="ts">
import { computed, useAttrs } from 'vue';

import { formatRangeTime } from '@/utils/time';

defineOptions({
  inheritAttrs: true,
});

const props = defineProps<{
  value?: string[];
  placeholder?: string | string[];
}>();

const emit = defineEmits<{
  (e: 'update:value', value?: number[]): void;
}>();

const dateValue = computed({
  get() {
    return props.value;
  },
  set(value?: string[]) {
    const rangeDate = formatRangeTime(value?.map(item => item * 1000));
    emit('update:value', rangeDate);
  },
});

const attrs = useAttrs();

const datePlaceholder = computed(() => {
  if (!props.placeholder) {
    return ['开始时间', '结束时间'];
  }
  if (typeof props.placeholder === 'string') {
    return [props.placeholder, props.placeholder];
  }
  return props.placeholder;
});
</script>
