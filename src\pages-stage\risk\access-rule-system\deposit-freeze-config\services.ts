import { GET, POST } from '@/services/api';

export function apiGetDepositFreezeConfig(params: { status: string }) {
  return GET('/super/tbl-deposit-freeze-rule/get-rules', params);
}

export function apiGetFreezeRuleOptions() {
  return GET('/super/tbl-deposit-freeze-rule/get-rule-config');
}

export function apiEditFreezeRule(params: { config: string; rule_id: string }) {
  return POST('/super/tbl-deposit-freeze-rule/update-rule', params);
}

export function apiReleaseRuleList() {
  return POST('/super/tbl-deposit-freeze-rule/take-effect');
}

export function apiResetRuleList() {
  return GET('/super/tbl-deposit-freeze-rule/reset-conf');
}
export function apiAddRuleItem() {
  return POST('/super/tbl-deposit-freeze-rule/add-rule');
}
export function apiGetDictionaryMenu() {
  return GET('/super/produceDict/category/get-menu');
}

export function apiGetAddRuleItem(params: { type: '1' | '2'; map_ids: string; rule_id: string }) {
  return POST('/super/tbl-deposit-freeze-rule/add-rule-item', params);
}

export function apiDelRuleItem(params: { type: '1' | '2'; map_id: number[]; rule_id: number }) {
  return POST('/super/tbl-deposit-freeze-rule/delete-map', params);
}

export function apiDelRuleConfigItem(params: { rule_id: string }) {
  return POST('/super/tbl-deposit-freeze-rule/delete-config', params);
}

export function apiChangeRuleStatus(params: { status: 0 | 1 }) {
  return GET('/super/tbl-deposit-freeze-rule/change-status', params);
}
