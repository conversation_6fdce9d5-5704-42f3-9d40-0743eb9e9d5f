<template>
  <a-modal
    :confirm-loading="submitLoading"
    destroy-on-close
    style="width: 680px"
    :title="title"
    :visible="visible"
    @cancel="onCancel"
    @ok="onSubmit"
  >
    <div class="header">
      <div class="title">
        应发货品 <span style="color: rgba(6, 21, 51, 0.65); font-weight: 500">{{ extraData.sku_info }}</span>
      </div>
      <div class="tag-wrap">
        <a-tag color="cyan">
          {{ extraData.is_support ? '租赁服务设备' : '非租赁服务设备' }}
        </a-tag>
        <a-tag
          v-if="extraData.is_checked"
          color="blue"
        >
          送检
        </a-tag>
        <a-tag
          v-if="extraData.activate_only"
          color="warning"
        >
          全新(仅激活)
        </a-tag>
      </div>
    </div>

    <div class="content">
      <a-form
        ref="formRef"
        layout="vertical"
        :model="formState"
      >
        <a-form-item
          label="拦截原因"
          name="intercept_reason"
          :rules="[{ required: true }]"
        >
          <a-select
            v-model:value="formState.intercept_reason"
            :options="currReasonOptions"
            placeholder="请选择拦截原因"
          />
        </a-form-item>
      </a-form>
      <component
        :is="currentComponent"
        ref="vassalCompRef"
        v-model:invoice="invoice"
        :addressee="{
          name: extraData.name,
          phone: extraData.phone,
          address: extraData.address,
        }"
        :current-key="currentKey"
        :order-id="orderId"
        :server-id="extraData.server_id"
        @submit="onSendSubmit"
      />
    </div>
    <template #footer>
      <a-button @click="onCancel">
        取消
      </a-button>
      <a-popconfirm v-if="inteceptVisible">
        <a-button
          type="primary"
          @click="onSendSubmit"
        >
          确定
        </a-button>
        <template #title>
          <a-spin :spinning="inteceptLoading">
            {{ interceptTitle }}
          </a-spin>
        </template>
        <template #okButton>
          <a-button
            :disabled="interceptDis"
            :loading="inteceptLoading"
            size="small"
            type="primary"
            @click="onSubmit"
          >
            确定
          </a-button>
        </template>
      </a-popconfirm>
      <a-button
        v-else
        type="primary"
        @click="onSubmit"
      >
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, PropType, ref } from 'vue';
import { Form } from 'ant-design-vue';
import { useCompRef } from 'rrz-web-design';

import type { TRefreshDataKey } from '@/components/order-business/typing';

import AddresseeCause from './components/addressee-cause.vue';
import BarterCause from './components/barter-cause.vue';
import RestsCause from './components/rests-cause.vue';
import UserCause from './components/user-cause.vue';
import WorkOrder from './components/work-order.vue';
import { getRole, reasonOptions } from './config';
import { createInterceptWorkOrder, limitSendApi, sendIntercept } from './services';

interface IExtraData {
  sku_info: string;
  is_checked: boolean;
  is_support: boolean;
  activate_only: boolean;
  name: string;
  phone: string;
  address: string;
  server_id: number;
  logistics_intercept_value?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const isSuper = getRole() === 'super';
const currentKey = computed(() => {
  return props.title === '物流拦截' ? 181 : 183;
});

const formRef = useCompRef(Form);
const vassalCompRef = ref();

const formState = ref({
  intercept_reason: null,
});

const invoice = ref(2);
const submitLoading = ref(false);

const currentComponent = computed(() => {
  const componentMap = {
    6: UserCause,
    4: RestsCause,
    5: AddresseeCause,
    7: BarterCause,
    8: WorkOrder,
  };
  return componentMap[formState.value.intercept_reason] || '';
});

const currReasonOptions = computed(() => {
  // if (props.extraData?.logistics_intercept_value === 1) {  // 后端写死，暂时无法通过后端字段值判断
  if (props.title === '物流拦截') {
    // 不显示「申请设备换货」
    return reasonOptions.filter(item => item.value !== 7);
  }
  return reasonOptions;
});

const limitNum = ref(0);
const noLimit = ref(true);
const inteceptLoading = ref(true);

const interceptDis = computed(() => {
  if (!noLimit.value) {
    return false;
  }
  return !limitNum.value;
});

const interceptTitle = computed(() => {
  if (!noLimit.value) {
    return '你确定要发起该拦截申请吗？';
  }
  return limitNum.value !== 0
    ? `你确定要发起该拦截申请吗？当前剩下${props.title}申请次数为“${limitNum.value}”次`
    : `当前剩下${props.title}申请次数为"0"次`;
});

//商家端物流拦截&发货拦截限制发起次数
const limitSend = async () => {
  const { orderId, title } = props;
  const { data } = await limitSendApi({
    work_type: title === '物流拦截' ? 1 : 2,
    order_id: orderId,
  });
  limitNum.value = data?.num || 0;
  noLimit.value = !!data?.no_limit;
};

const inteceptVisible = computed(() => {
  return !isSuper && [4, 6, 8].includes(formState.value.intercept_reason);
});

const onCancel = () => {
  emit('update:visible', false);
  formRef.value.resetFields();
};

const onSendSubmit = async () => {
  if (isSuper) {
    return onSubmit();
  } else {
    inteceptLoading.value = true;
    await limitSend();
    inteceptLoading.value = false;
  }
};

const onSubmit = async () => {
  await formRef.value.validate();
  //用户申请取消/其他：接入大工单
  if (formState.value.intercept_reason === 8) {
    sumbitLargeWorkOrder();
    return;
  }
  const data = await vassalCompRef.value.commit();
  const { orderId, title } = props;
  const params = { ...data, ...formState.value, order_id: orderId, work_type: title === '物流拦截' ? 1 : 2 };
  if (formState.value.intercept_reason === 7) {
    const isNewOnlineServer = vassalCompRef.value?.getIsNewOnlineServer();
    if (isNewOnlineServer) {
      params.invoice = invoice.value;
    }
  }
  submitLoading.value = true;
  sendIntercept(params)
    .then(() => {
      onCancel();
      emit('refresh', props.orderId, ['data', 'all_remark']);
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

async function sumbitLargeWorkOrder() {
  const data = await vassalCompRef.value.commit();
  submitLoading.value = true;
  await createInterceptWorkOrder(data).finally(() => {
    submitLoading.value = false;
  });
  onCancel();
  emit('refresh', props.orderId, ['data', 'all_remark']);
}
</script>

<style lang="less" scoped>
.header {
  margin-bottom: 24px;
  padding: 12px;
  color: rgba(6, 21, 51, 0.65);
  background: #f0f7ff;
  border-radius: 4px;

  .title {
    padding-bottom: 8px;
  }
}
</style>
