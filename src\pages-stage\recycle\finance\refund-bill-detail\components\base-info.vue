<template>
  <div class="base-info-mian">
    <div class="title-block">
      <div class="line" />
      <div class="title">
        基本信息
      </div>
    </div>
    <a-form
      ref="baseForm"
      class="form-mian"
      layout="vertical"
      :model="formValue"
    >
      <a-row
        :gutter="[24, 16]"
        style="width: 100%"
      >
        <a-col
          v-if="formValue.refund_order_id"
          :lg="10"
          :md="12"
          :xl="8"
        >
          <a-form-item
            label="单据编号"
            name="refund_order_id"
            :rules="[{ required: true, message: '请输入单据编号', trigger: ['blur', 'change'] }]"
          >
            <a-input
              v-model:value="formValue.refund_order_id"
              :disabled="!!formValue.refund_order_id"
            />
          </a-form-item>
        </a-col>
        <a-col
          :lg="10"
          :md="12"
          :xl="8"
        >
          <a-form-item
            label="客户"
            name="server_id"
            :rules="[{ required: true, message: '请选择客户', trigger: ['blur', 'change'] }]"
          >
            <a-select
              :filter-option="filterOption"
              :options="severOptions"
              placeholder="请选择客户"
              show-search
              :value="formValue.server_id"
              @change="handleChange"
            />
          </a-form-item>
        </a-col>

        <a-col
          :lg="10"
          :md="12"
          :xl="8"
        >
          <a-form-item
            label="单据日期"
            name="date"
            :rules="[{ required: true, message: '请选择单据日期', trigger: ['blur', 'change'] }]"
          >
            <a-date-picker
              v-model:value="formValue.date"
              placeholder="请选择日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row
        :gutter="[24, 16]"
        style="width: 100%"
      >
        <a-col
          :lg="10"
          :md="12"
          :xl="8"
        >
          <a-form-item
            label="币别"
            name="coin_type"
            :rules="[{ required: true, message: '请选择币别' }]"
          >
            <a-select
              v-model:value="formValue.coin_type"
              disabled
              :field-names="{ label: 'name', value: 'value' }"
              :options="coinTypeList"
            />
          </a-form-item>
        </a-col>
        <a-col
          :lg="10"
          :md="12"
          :xl="8"
        >
          <a-form-item
            label="应收款余额"
            name="price"
            required
          >
            <a-input
              v-model:value="formValue.price"
              disabled
              placeholder="选择客户后自动计算"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row
        :gutter="[24, 16]"
        style="width: 100%"
      >
        <a-col
          :lg="20"
          :md="24"
          :xl="16"
        >
          <a-form-item
            label="单据备注"
            name="remark"
            style="margin-bottom: 0"
          >
            <a-textarea
              v-model:value="formValue.remark"
              :maxlength="500"
              placeholder="请填写单据备注"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-divider />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useVModel } from '@/hook';
import { getServerList, getRefundWaitPay } from '../service';
import type { IServerOptionParams } from '../data';

const props = defineProps<{
  coinTypeList: {
    value: number;
    name: string;
  }[];
  formValue: {
    coin_type: number;
    refund_order_id?: string;
    server_id?: string;
    date: string;
    price: string;
    remark: string;
  };
}>();
const emit = defineEmits(['update:formValue']);

const formValue = useVModel(props, 'formValue', emit);

const baseForm = ref();

const severOptions = ref<IServerOptionParams[]>([]);
const handlgetServerList = async () => {
  const res = await getServerList();
  severOptions.value = res.data.map((item: any) => ({
    value: item.server_id,
    label: item.server_name,
  }));
};

//选中客户获取应收款余额
const handleRefundWaitPayMoney = async (server_id: number) => {
  const { data } = await getRefundWaitPay({ server_id });
  formValue.value.price = data.total_price;
};

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

//选择客户发起请求 确定应退款余额和源单数据
const handleChange = (value: number) => {
  formValue.value.server_id = value;
  handleRefundWaitPayMoney(value);
};
const scrollIntoDom = () => {
  const dom = document.querySelector('.base-info-mian');
  if (dom) {
    dom.scrollIntoView();
  }
};
onMounted(() => {
  handlgetServerList();
});
defineExpose({ baseForm, scrollIntoDom });
</script>

<style scoped lang="less">
.base-info-mian {
  padding: 24px 24px 0 24px;
  .title-block {
    display: flex;
    align-items: center;
    .line {
      width: 4px;
      height: 16px;
      background: #00c8be;
      border-radius: 2px;
    }
    .title {
      margin-left: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
    }
  }
  .form-mian {
    margin-top: 24px;
  }
}
</style>
