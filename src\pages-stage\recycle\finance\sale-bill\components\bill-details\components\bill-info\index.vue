<template>
  <bill-detail-item title="账单信息">
    <div class="bill-info">
      <div class="bill-list">
        <div class="list-item">
          <span class="label">订单编号</span>
          <span class="value">{{ saleBillDetail.rs_order_id }}</span>
        </div>
        <div class="list-item">
          <span class="label">账单类型</span>
          <span class="value">{{ findLabel('orderTypeOptions', saleBillDetail.source_type) }}</span>
        </div>
        <div class="list-item">
          <span class="label">账单编号</span>
          <span class="value">{{ saleBillDetail.rsp_order_id }}</span>
        </div>
        <div class="list-item">
          <span class="label">品类</span>
          <span class="value">{{ `${saleBillDetail.brand_name} ${saleBillDetail.model_name}` }} </span>
        </div>
        <div class="list-item">
          <span class="label">客户类型</span>
          <span class="value">{{ findLabel('customerTypeOtions', saleBillDetail.custom_type) }}</span>
        </div>
        <div class="list-item">
          <span class="label">客户ID/名称</span>
          <span class="value">{{ `${saleBillDetail.server_id} | ${saleBillDetail.server_name}` }}</span>
        </div>
        <div class="list-item">
          <span class="label">创建时间</span>
          <span class="value">{{ dayjs.unix(Number(saleBillDetail.created_at)).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
      </div>
      <div class="deliver-money">
        <div class="deliver-list">
          <div class="deliver-item">
            <span class="deliver-label deliver-value">账单金额</span>
            <span class="deliver-value color-85">￥{{ saleBillDetail.order_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">商品总价</span>
            <span class="deliver-value">￥{{ saleBillDetail.goods_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">运费</span>
            <span class="deliver-value">￥{{ saleBillDetail.other_money }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">检测费</span>
            <span class="deliver-value">￥{{ saleBillDetail.quality_check_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">锁机费</span>
            <span class="deliver-value">￥{{ saleBillDetail.lock_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">配件费</span>
            <span class="deliver-value">￥{{ saleBillDetail.accessory_price }}</span>
          </div>
          <div class="deliver-item">
            <span class="deliver-label">发货服务费</span>
            <span class="deliver-value">￥{{ saleBillDetail.delivery_service_price }}</span>
          </div>
        </div>
      </div>
    </div>
  </bill-detail-item>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import dayjs from 'dayjs';

import { findLabel } from '../../../../config';
import { ISaleBillDetail } from '../../data';
import BillDetailItem from '../bill-detail-item/index.vue';
defineProps({
  saleBillDetail: {
    type: Object as PropType<ISaleBillDetail>,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.bill-info {
  display: flex;
  align-items: center;
  .bill-list {
    width: 472px;
    height: 250px;
    padding: 12px;
    font-weight: 400;
    background: #f7f8f9;
    border-radius: 2px;
    .list-item {
      margin-bottom: 12px;
      &::nth-last-type {
        margin-bottom: 0;
      }
      .label {
        display: inline-block;
        min-width: 87px;
        color: rgba(6, 21, 51, 0.65);
        text-align: right;
        &::after {
          content: '：';
        }
      }
      .value.order {
        color: #3777ff;
      }
      .value {
        color: rgba(6, 21, 51, 0.85);
      }
    }
  }
  .deliver-money {
    position: relative;
    margin-left: 60px;
    &::before {
      position: absolute;
      top: 5px;
      width: 4px;
      height: 216px;
      background: #00c8be;
      border-radius: 2px;
      content: '';
    }
    .deliver-item {
      margin-bottom: 12px;
      padding-left: 12px;
      &::nth-last-type {
        margin-bottom: 0;
      }
      .deliver-label {
        color: rgba(6, 21, 51, 0.45);
        font-weight: 400;
        &::after {
          content: '：';
        }
      }
      .deliver-value {
        color: rgba(6, 21, 51, 0.65);
      }
      .color-85 {
        color: rgba(6, 21, 51, 0.85);
      }
    }
    .real-pay {
      color: #061533;
    }
  }
}
</style>
