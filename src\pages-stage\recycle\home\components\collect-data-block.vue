<template>
  <a-spin
    :spinning="loading"
    tip="Loading..."
  >
    <card-box
      hidden-handle
      title="今日收款"
    >
      <template #suffix>
        （总计：{{ chartInfo.count_num }}元）
      </template>
      <flex-container
        :columns-number="2"
        :gap="8"
      >
        <card-data-item
          title="待收金额"
          unit="元"
          :value="chartInfo.wait_pay_num"
        >
          <template #suffix>
            <a-tooltip placement="top">
              <template #title>
                <span>统计时间内，还未回款的所售全部设备的销售价格之和，精确到小数点后俩位，四舍五入。</span>
              </template>
              <InfoCircleOutlined />
            </a-tooltip>
          </template>
        </card-data-item>
        <card-data-item
          title="已收金额"
          unit="元"
          :value="chartInfo.finish_pay_num"
        >
          <template #suffix>
            <a-tooltip placement="top">
              <template #title>
                <span>统计时间内，已经回款的所售全部设备的销售价格之和，精确到小数点后俩位，四舍五入。</span>
              </template>
              <InfoCircleOutlined />
            </a-tooltip>
          </template>
        </card-data-item>
        <template #body>
          <div
            v-if="!isEmpty"
            id="collect_dataConatiner"
          />
          <EmptyBlock
            v-else
            class="empty-box"
          />
        </template>
      </flex-container>
    </card-box>
  </a-spin>
</template>

<script lang="ts" setup>
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { ICollectData } from '../data.d';
import usePillarData from '../composables/use-pillar-data';
import FlexContainer from './flex-container.vue';
import CardBox from './card-box.vue';
import CardDataItem from './card-data-item.vue';
import EmptyBlock from './empty-block.vue';

const { chartInfo, loading, isEmpty } = usePillarData<ICollectData>({
  element: 'collect_dataConatiner',
  url: '/super/recycle/data-count/sale-pay-data',
});
</script>

<style lang="less" scoped>
.containerCss() {
  width: 100%;
  height: 300px;
  margin-top: 40px;
}

#collect_dataConatiner {
  .containerCss();
}
.empty-box {
  .containerCss();
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
