## BMap 百度地图组件

### 使用方式

bmap 组件使用方式分以下三步： 1、加载百度地图 SDK 2、初始化地图容器 3、通过地图实例做绘制动作

#### 加载 SDK

bmap 组件实例暴露了 loadBMapScript 方法，用于初始化百度地图 SDK。需要注意的地方 loadBMapScript 方法是一个异步方法，开发者应该等待异步方法完成进入下一步动作。

```javascript
await bmapRef.value.loadBMapScript();
// ...后续逻辑

// 或
bmapRef.value.loadBMapScript().then(() => {
  // ...后续逻辑
});
```

#### 初始化地图容器

bmap 组件实例暴露了 initBMapContainer 方法，用户初始化地图容器，注意需要在第一步 SDK 加载完毕之后调用。返回 map 地图实例。

```javascript
await bmapRef.value.loadBMapScript();

const map = bmapRef.value.initBMapContainer();
// map为地图实例，可以在上面绘制节点
```

#### 内置的绘制动作

bmap 组件提供了快捷的绘制动作，目前支持根据经纬度坐标绘制轨迹 drawTrackByPoints

```javascript
await bmapRef.value.loadBMapScript();

bmapRef.value.initBMapContainer();

bmapRef.value.drawTrackByPoints([
  // points
]);
```
