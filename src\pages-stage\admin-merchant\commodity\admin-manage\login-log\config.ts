import { ColumnType } from 'ant-design-vue/lib/table';

export function phoneSeparated(phoneNumber: number) {
  const tel = phoneNumber.toString();
  return tel.substring(0, 3) + ' ' + tel.substring(3, 7) + ' ' + tel.substring(7, 11);
}

export const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'name',
    key: 'index',
    customRender: ({ index }: { index: number }) => `${index + 1}`,
    width: '9.7%',
  },
  {
    title: '手机',
    dataIndex: 'phone',
    key: 'phone',
    customRender: ({ record }) => phoneSeparated(record.user.phone),
    width: '17%',
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: '13%',
    customRender: ({ record }) => record.user.username,
  },
  { title: '登录ip', dataIndex: 'login_ip', key: 'login_ip', width: '17%' },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    customRender: ({ record }) =>
      record.user.role === '未知' ? '商家员工' : record.user.role === 'serverStaff' ? '未定义角色' : record.user.role,
    width: '17%',
  },
  {
    title: '登陆时间',
    dataIndex: 'login_time',
    key: 'login_time',
    width: '24%',
  },
  // {
  //   title: '操作',
  //   key: 'operation',
  // },
];
