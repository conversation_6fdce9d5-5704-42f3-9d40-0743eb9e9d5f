import { GET, POST } from '@/services/api';

import { IRefundItem, IRefundListParams } from './data';

/**
 * 流水退款列表
 */
export function getRefundList(params: IRefundListParams) {
  return GET<IRefundListParams, IRefundItem[]>('/super/finance/account-pay-log-refund/list', params);
}

/**
 * 流水退款列表（商家后台）
 */
export function getRefundListServer(params: IRefundListParams) {
  return GET<IRefundListParams, IRefundItem[]>('/finance/account-pay-log-refund/list', params);
}

/**
 * 流水退款申请
 */
export function applyRefund(params: any) {
  return POST('/super/finance/account-pay-log-refund/apply', params);
}

/**
 * 流水退款申请（商家后台）
 */
export function applyRefundServer(params: any) {
  return POST('/finance/account-pay-log-refund/apply', params);
}
