<!--
 * @Author: itnewdata <EMAIL>
 * @Date: 2023-05-29 20:23:18
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-05-30 00:02:26
 * @FilePath: \admin_vue\src\pages-stage\order\manage\order-list\components\list-tabs\components\customer-table\index.vue
 * @Description: 自定义表格
-->
<!-- 回收销售订单已引入此组件 -->
<template>
  <div class="customer-table">
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <table>
        <colgroup>
          <col
            v-for="column in columns"
            :key="column.key"
            :style="{ width: column.width }"
          >
        </colgroup>
        <thead :class="['table-thead', tableTheadClass.join(' ')]">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :style="{ textAlign: column.align }"
            >
              {{ column.title }}
            </th>
          </tr>
        </thead>
        <tbody>
          <template
            v-for="(record, index) in dataSource"
            :key="index"
          >
            <tr>
              <td :colspan="columns.length">
                <div class="spacing" />
              </td>
            </tr>
            <tr class="row-header">
              <td :colspan="columns.length">
                <slot
                  :index="index"
                  name="rowHeader"
                  :record="record"
                />
              </td>
            </tr>
            <tr>
              <td
                v-for="column in columns"
                :key="column.key"
                :class="['td-box', column?.className ?? '']"
              >
                <slot
                  :column="column"
                  :index="index"
                  name="bodyCell"
                  :record="record"
                  :text="record[column.key as string]"
                >
                  {{ record[column.key as string] || '' }}
                </slot>
              </td>
            </tr>
            <slot
              name="expandedRowRender"
              :record="record"
            />
          </template>
          <tr v-show="!dataSource.length">
            <td :colspan="columns.length">
              <a-empty
                :description="emptyTip"
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import type { TableColumnType } from 'ant-design-vue';
import { Empty } from 'ant-design-vue';

defineProps({
  dataSource: {
    type: Array as PropType<any[]>,
    default() {
      return [];
    },
  },
  columns: {
    type: Array as PropType<TableColumnType[]>,
    default() {
      return [];
    },
  },
  loading: {
    type: Boolean,
    default: false,
  },
  tableTheadClass: {
    type: Array,
    default: () => [],
  },
  emptyTip: {
    type: String,
    default: '暂无数据',
  },
});
</script>

<style scoped lang="less">
.customer-table {
  padding: 0 24px 54px;

  table {
    width: 100%;
    padding: 16px;
    table-layout: fixed;

    thead {
      th {
        position: relative;
        padding: 8px 16px;
        color: #061533d9;
        font-weight: bold;
        white-space: nowrap;
        text-align: left;
        overflow-wrap: break-word;
        background: #d9f7f5;
        border-bottom: 1px solid #f0f0f0;
        transition: background 0.3s ease;
      }
    }

    tbody {
      border-bottom: 1px solid #f0f0f0;

      tr.row-header {
        background-color: #f5f7fa;
      }

      tr {
        border-top: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        border-left: 1px solid #f0f0f0;
      }

      td {
        vertical-align: top;
      }

      td + td {
        border-left: 1px solid #f0f0f0;
      }
    }

    .empty-data {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.spacing {
  position: relative;
  width: calc(100% + 4px);
  height: 24px;
  margin-left: -2px;
  background-color: #fff;
}

// thead悬浮样式
.table-thead.ceiling {
  position: fixed;
  top: 0;
  z-index: 10;
}
</style>
