export interface IFormState {
  userName?: string | null;
  phone?: string | null;
  bookTime?: string[];
  payeeName?: string | null;
  payeeType?: number;
  province_name?: string | null;
  city_name?: string | null;
  area_name?: string | null;
  addr_detail?: string | null;
  return_name?: string | null;
  return_phone?: string | null;
  return_province_name?: string | null;
  return_city_name?: string | null;
  return_area_name?: string | null;
  return_addr_detail?: string | null;
  use_send_info?: number | null;
}

type TSkuInfo = { name: string };

type TForecastSkuInfo = {
  name: string;
  color: string;
  memory: string;
  version: string;
  new: string;
  imei: string;
};

export interface IDataInfo {
  carId: number;
  // 商品图片
  image: string;
  // 商品名称
  goodsName: string;
  // 估价
  evaluatePrice: number;
  // 型号
  model: string;
  // 颜色
  color: string;
  // 内存
  memory: string;
  // 版本
  version: string;
  // 成新度
  condition: string;
  // IMEI码
  imei: number;
  // 是否失效
  is_invalid: boolean;
  sku_info?: any;
  // 是否标识了imei错误
  imeiError?: boolean;
  // 1是代发设备 2是非代发设备
  is_replace_device: string;
}

export interface IArgeementForm {
  agreement_type: null | number;
}
