<template>
  <a-form
    ref="formRef"
    layout="vertical"
    :model="formState"
  >
    <a-form-item
      label="姓名"
      name="name"
      :rules="[{ required: true }]"
    >
      <a-input
        v-model:value="formState.name"
        placeholder="请输入"
      />
    </a-form-item>
    <a-form-item
      label="手机号"
      name="phone"
      :rules="[{ required: true }]"
    >
      <a-input
        v-model:value="formState.phone"
        placeholder="请输入"
      />
    </a-form-item>
    <a-form-item
      label="地址"
      name="address"
      :rules="[{ required: true }]"
    >
      <a-input
        v-model:value="formState.address"
        placeholder="请输入"
      />
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue';
import { Form } from 'ant-design-vue'
import { useCompRef } from 'rrz-web-design'

const props = defineProps<{
  addressee: {
    name: string
    phone: string
    address: string
  }
}>()

const formRef = useCompRef(Form)
const formState = ref({
  name: '',
  phone: '',
  address: '',
})

watchEffect(() => {
  formState.value = {
    name: props.addressee.name,
    phone: props.addressee.phone,
    address: props.addressee.address,
  }
})

const commit = () => {
  return new Promise(resolve => {
    formRef.value.validate().then(() => {
      const { name: before_name, phone: before_phone, address: before_address} = props.addressee;
      resolve({ ...formState.value, before_name, before_phone, before_address  })
    })
  })
}

defineExpose({
  commit,
})
</script>
