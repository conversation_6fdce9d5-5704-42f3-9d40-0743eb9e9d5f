<template>
  <a-spin :spinning="loading">
    <div class="generic-filter">
      <ExpandBox
        placement="top"
        :target-range="collapse ? RESIZE_TARGET_HEIGHT : 999"
      >
        <template #content>
          <RForm
            ref="refRForm"
            v-model:value="formState"
            :form-dependencies="formDependencies"
            :form-group="formGroup"
            :grid-config="gridConfig"
            @keydown.enter="handleSearch"
          />
        </template>

        <template #custom="{ hasExtendMore, isOutRange, toggleExtendMore }">
          <a-space>
            <!-- 按钮前（before） -->
            <slot
              name="before"
              :search-data="formState"
            />
            <!-- 搜索按钮 -->
            <a-button
              type="primary"
              @click="handleSearch"
            >
              查询
            </a-button>
            <!-- 重置按钮 -->
            <a-button @click="handleReset">
              重置
            </a-button>
            <!-- 按钮后（left） -->
            <slot
              name="after"
              :search-data="formState"
            />
            <!-- 展开/收起按钮 -->
            <div
              v-if="isOutRange"
              class="more-filtering"
              :class="hasExtendMore ? 'active' : 'default'"
              @click="toggleExtendMore"
            >
              <span>更多筛选</span>
              <DownOutlined
                class="icon"
                :class="{ active: hasExtendMore }"
              />
            </div>
            <!-- 额外 -->
            <slot
              name="extra"
              :search-data="formState"
            />
          </a-space>
        </template>
      </ExpandBox>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { RForm } from 'rrz-web-design';

import ExpandBox from '@/components/expand-box';
import { useRouteSessionData } from '@/composables/use-route-session-data';

import { useGenericForm } from '../../composables/generic-form';
import { IGenericFilterProps } from '../../composables/generic-form/data';

const props = withDefaults(defineProps<IGenericFilterProps>(), {
  formId: '',
  collapse: false,
  formDependencies: {},
  storageKey: '',
});

const gridConfig = ref({
  colProps: {
    md: 8,
    lg: 8,
    xl: 6,
    xxl: 4,
  },
  rowProps: {
    gutter: {
      md: 8,
      lg: 8,
      xl: 16,
    },
  },
});
/** 需要控制展开收起的高度（两行搜索列） */
const RESIZE_TARGET_HEIGHT = 2 * 56;

const formState = reactive({});

const emits = defineEmits(['search', 'reset']);

const refRForm = ref<RForm | null>(null);

const disabled = ref(false);

const { initData } = useRouteSessionData(props.storageKey);

const { formGroup, getFilterConfig, loading } = useGenericForm(props, disabled);

async function handleSearch() {
  await refRForm.value!.validate();
  emits('search', formState);
}

function handleReset() {
  refRForm.value!.getFormRef().resetFields();
  emits('reset');
}

function handleInitSearchData() {
  const groupKeys = formGroup.value
    .map(item => ('items' in item ? item.items.map(temp => temp.key) : [item.key]))
    .flat();
  groupKeys.forEach(key => {
    if (initData[key] !== undefined) {
      formState[key] = initData[key];
    }
  });
}

onMounted(async () => {
  await getFilterConfig();

  handleInitSearchData();
});
</script>

<style scoped lang="less">
.generic-filter {
  margin: 24px;
}

.more-filtering {
  display: inline-block;
  margin: auto 10px;
  color: #3777ff;
  cursor: pointer;
  user-select: none;

  .icon {
    margin-left: 4px;
    transition-duration: 0.3s;
  }
}

.more-filtering.default {
  .icon {
    transform: translateY(2px);
  }
}

.more-filtering.active {
  .icon {
    transform: rotateZ(180deg) translateY(0);
  }
}
</style>
