import { GET, IAxiosResponse, POST } from '@/services/api';
import { IOptionItem } from './data';

export const queryEvaluateList = (params: Record<string, any>): Promise<IAxiosResponse<IOptionItem[]>> => {
  return GET('/super/recycle/evaluate-option/list', params);
};

export const queryEvaluateNameList = (
  params: Record<string, any>,
): Promise<IAxiosResponse<{ id: string | number; name: string }[]>> => {
  return GET('/super/recycle/evaluate-option/name-list', params);
};

export const toggleEvaluateOptionOpen = (params: Record<string, any>) => {
  return GET('/super/recycle/evaluate-option/switch-open', params);
};

export const saveEvaluateOption = (data: Record<string, any>) => {
  return POST('/super/recycle/evaluate-option/save', data, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
};
