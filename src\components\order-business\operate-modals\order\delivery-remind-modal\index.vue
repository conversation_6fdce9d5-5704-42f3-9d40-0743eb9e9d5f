<template>
  <a-modal
    v-model:visible="modalVisible"
    :confirm-loading="confirmLoading"
    title="催发货"
    @cancel="modalVisible = false"
    @ok="handleOk"
  >
    <div class="content">
      确定进行催发货吗？
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

import { orderDeliveryRemind, orderDeliveryRemindStatus } from './services';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);

const modalVisible = ref(false);
const confirmLoading = ref(false);
const route = useRoute();
const isSuper = route.query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      openModal();
      bindVisible.value = false;
    }
  },
  { immediate: true },
);

function openModal() {
  orderDeliveryRemindStatus({
    order_id: props.orderId,
    role: isSuper ? 2 : 3,
  }).then(({ data }) => {
    if (data?.status) {
      modalVisible.value = true;
    } else {
      message.success('该订单已催发货，详情可点击“加急发货”查看催发货日志');
    }
  });
}

function handleOk() {
  confirmLoading.value = true;
  const params = {
    order_id: Number(props.orderId),
    role: isSuper ? 2 : 3,
    platform: 2,
  };
  orderDeliveryRemind(params)
    .then(() => {
      modalVisible.value = false;
      message.success('催发货成功');
      emit('refresh', props.orderId, ['data', 'all_remark']);
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}
</script>
