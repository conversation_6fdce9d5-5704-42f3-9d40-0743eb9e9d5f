<template>
  <CardDataItem
    tips-text="近30天内经过待入库状态的订单估价金额总和"
    title="GMV近30天变化趋势"
  >
    <div class="date-text">
      {{ dateText }}
    </div>
    <div class="date-wrap">
      <div class="date-wrap__value">
        <span class="date-wrap__value__num">{{ info.gmv_today }}</span>
        <span class="date-wrap__value__span">{{ info.unit }}</span>
      </div>
      <div class="date-wrap__explain">
        合计：{{ info.gmv_count }}<span style="margin: 0 4px">|</span>均值：{{ info.gmv_average }}
      </div>
    </div>
    <div
      id="dataContainer"
      class="dataContainer"
    />
  </CardDataItem>
</template>

<script lang="ts" setup>
import { computed, watch, onMounted, onBeforeUnmount, PropType } from 'vue';
import { loadG2 } from '@/utils/static-load';
import { IGmvDayRes, IGmvDayInfo } from '../data.d';
import CardDataItem from './card-data-item.vue';

const props = defineProps({
  info: {
    type: Object as PropType<Partial<IGmvDayRes>>,
    default: () => ({}),
  },
  list: {
    type: Array as PropType<IGmvDayInfo[]>,
    default: () => [],
  },
});

let chart: any = null;

const dateText = computed(() => {
  if (!Object.keys(props.info).length) return;
  const { start_at, end_at, date_between, update_time } = props.info;
  return `${start_at || '-- '}~${end_at || ' --'}${date_between ? ' | ' + date_between : ''} | 更新于${
    update_time || '--'
  }`;
});

watch(
  () => props.list,
  () => {
    createCharts();
  },
);

const createCharts = async () => {
  const container = document.getElementById('dataContainer') as HTMLDivElement;
  container.innerHTML = '';
  await loadG2();
  if (window.G2) {
    chart = new G2.Chart({
      container: 'dataContainer',
      forceFit: true,
      autoFit: true,
      height: 300,
      width: container.offsetWidth,
    });
    render();
  }
};

const render = () => {
  chart.data(props.list);
  chart.scale({
    date: {
      tickCount: 8,
    },
    gmv: {
      nice: true,
    },
  });

  chart.tooltip({
    showTitle: true,
    showCrosshairs: true,
    follow: true,
    title: (...args: any) => {
      return args[1].tipsText;
    },
    customItems: (items: any[]) => {
      items[0].name = items[0].data.label;
      items[0].value = items[0].data.gmv;
      return items;
    },
  });

  chart.line().position('date*gmv').color('#00C8BE').shape('smooth');

  chart
    .point()
    .position('date*gmv')
    .color('#00C8BE')
    .shape('circle')
    .tooltip('date*gmv&label', (date: string, gmv: number, label: string) => {
      return {
        date,
        gmv,
        label,
      };
    });

  chart.render();
};

onMounted(() => {
  window.addEventListener('resize', createCharts);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', createCharts);
});
</script>

<style lang="less" scoped>
.date-text {
  margin-bottom: 16px;
  color: #808080;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
}
.date-wrap {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 24px;
  &__value {
    vertical-align: bottom;
    &__num {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }
    &__span {
      margin-left: 3px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
    }
  }
  &__explain {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    vertical-align: bottom;
  }
}
</style>
