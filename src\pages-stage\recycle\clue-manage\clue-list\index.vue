<template>
  <layout-admin-page
    :navs="['趣回收', '线索管理', '线索列表']"
    title="线索列表"
  >
    <div class="container">
      <div class="search">
        <form-create
          ref="searchFormRef"
          v-model:value="searchParams"
          :form-group="searchFormGroup"
          :origin-props="{
            layout: 'inline',
          }"
        >
          <template #buttons>
            <a-space :size="8">
              <a-button
                style="margin-right: 10px"
                type="primary"
                @click="getTableList"
              >
                查询
              </a-button>
              <a-button @click="resetSearch">
                重置
              </a-button>
            </a-space>
          </template>
        </form-create>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table"
          :columns="clueListColumns"
          :data-source="list"
          :loading="listLoading"
          :pagination="page"
          :scroll="{ x: '100%' }"
          :sticky="true"
          style="margin-top: 12px"
          @change="tableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'phone'">
              {{ record.phone }}

              <DecryptField
                v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                :field-item="{
                  box: 'text',
                  id: record.id,
                  field: 'phone',
                  field_type: 'merchant_phone',
                  type: 140,
                }"
              />
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-popconfirm
                cancel-text="取消"
                ok-text="确认"
                title="你确定要对该条线索跟进吗？"
                @confirm="confirm(record.id)"
              >
                <a-button
                  v-if="record.status === 0"
                  type="link"
                >
                  确定
                </a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </layout-admin-page>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { AxiosResponse } from 'axios';

import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
import { useViewPlaintextAuth } from '@/composables/use-decrypt-data';
import { useTable } from '@/hook/component/use-table';

import { clueListColumns, searchFormGroup } from './config';
import { IClueList, IClueMangageSearch } from './data';
import { updataStatus } from './service';
const { viewPlaintextAuth } = useViewPlaintextAuth();
const searchParams = reactive<Partial<IClueMangageSearch>>({});
const searchFormRef = ref();
/*---------------- table hooks  --------------------*/
const { list, listLoading, page, getTableList, tableChange } = useTable<
  IClueList,
  Partial<IClueMangageSearch> | undefined
>({
  url: '/super/recycle/demand-regis/list',
  totalKey: 'data.pageInfo.count',
  searchForm: searchParams,
  formatHandle: (res: AxiosResponse<{ list: IClueList[] }>) => {
    return res.data.list;
  },
  formatSearchValue: res => {
    return res?.status === 2 ? {} : res;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

// 重置搜索
const resetSearch = () => {
  searchFormRef?.value?.getFormRef()?.resetFields();
  getTableList();
};
const confirm = async (id: string) => {
  try {
    await updataStatus({ id, type: 'done' });
    resetSearch();
  } catch (error) {}
};
getTableList();
</script>

<style lang="less" scoped>
@import '../../common/base.less';
.search {
  margin: 0 24px;
}
</style>
