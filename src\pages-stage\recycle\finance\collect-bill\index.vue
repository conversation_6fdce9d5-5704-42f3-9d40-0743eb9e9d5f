<template>
  <layout-admin-page
    :navs="['趣回收', '财务管理', '收款单']"
    title="收款单"
  >
    <template #extra>
      <!-- 导出 -->
      <a-button
        type="primary"
        @click="handleAddCollectBill"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        新增收款单
      </a-button>
    </template>
    <div class="container">
      <div class="search-bar pb-0">
        <!-- 搜索 -->
        <form-create
          ref="searchFormRef"
          :form-group="searchFormGroup"
          :origin-props="{ layout: 'inline' }"
          :value="searchParams"
        >
          <template #buttons>
            <slot name="buttons">
              <a-space :size="12">
                <a-button @click="resetSearchForm">
                  重置
                </a-button>
                <a-button
                  type="primary"
                  @click="getTableList('search')"
                >
                  查询
                </a-button>
                <a-button @click="exportAction">
                  <ExportOutlined />
                  导出
                </a-button>
              </a-space>
            </slot>
          </template>
        </form-create>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table table-list"
          :columns="saleBillColumns"
          :data-source="list"
          :loading="listLoading"
          :pagination="false"
          row-key="order_id"
          :row-selection="rowSelection"
          :scroll="{ x: '100%' }"
          :sticky="true"
          @change="tableChange(page)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'order_id'">
              <span
                class="link"
                @click="toBillDetail(record.order_id)"
              >
                {{ record.order_id }}
              </span>
            </template>
            <template v-if="column.key === 'status'">
              <div class="status-block">
                <div
                  class="point"
                  :style="{ background: record.status === '1' ? '#00C8BE' : '#FAAD14' }"
                />
                {{ record.status_text }}
              </div>
            </template>
          </template>
        </a-table>
        <div class="table-pagination">
          <div class="all-checked">
            <a-checkbox
              v-model:checked="checkAllState.checkAll"
              :indeterminate="checkAllState.indeterminate"
              @change="onCheckAllChange"
            >
              全选
            </a-checkbox>
            <div class="check-num">
              已选 <span class="num">{{ checkAllState.checkedList.length }}</span> 条
            </div>
            <a-button
              :disabled="checkAllState.checkedList.length === 0"
              :loading="applyLoading"
              style="margin-left: 12px"
              type="primary"
              @click="handleApply"
            >
              审核
            </a-button>
            <a-button
              :disabled="checkAllState.checkedList.length !== 1"
              style="margin-left: 7px"
              type="primary"
              @click="handlePay"
            >
              线上收款
            </a-button>
          </div>
          <a-pagination
            v-model:current="page.current"
            :default-page-size="10"
            :page-size="page.pageSize"
            :page-size-options="[10, 30]"
            show-quick-jumper
            :total="page.total"
            @change="tableChange(page)"
          />
        </div>
      </div>
    </div>
    <receipt-drawer
      v-if="receiptDrawerVisible"
      v-model:visible="receiptDrawerVisible"
      :order-ids="order_ids"
      @on-load-list="onLoadList"
    />
  </layout-admin-page>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { ExportOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { useTable } from '@/hook/component/use-table';
import useExportModalConfirm from '@/pages-stage/recycle/finance/self-order-pay/use-export-modal-confirm';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import { saleBillColumns, searchFormGroup } from './config';
import { ISearchParams, IReceiptList } from './data';
import useCheckAll from './composables/use-check-all';
import { exportList, checkList } from './service';
import ReceiptDrawer from './components/receipt-confirm-drawer.vue';

const router = useRouter();
const route = useRoute();

const [receiptDrawerVisible, { setTrue: showReceiptDrawer }] = useBoolean();

// 搜索参数
const searchParams = reactive({});
const searchFormRef = ref();
const applyLoading = ref(false);

/*---------------- table hooks  --------------------*/
const { list, listLoading, page, getTableList, tableChange } = useTable<any, Partial<ISearchParams>>({
  url: '/super/recycle/sale-verification/list',
  searchForm: searchParams,
  totalKey: 'data.pageData.count',
  formatHandle: res => {
    return res.data.listData;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
    pageSize: 30,
  },
});
const { checkAllState, onCheckAllChange, rowSelection } = useCheckAll(list);

const resetSearchForm = () => {
  const formRef = searchFormRef.value?.getFormRef();
  if (formRef) {
    formRef.resetFields();
    getTableList('search');
  }
};
// 导出
const exportAction = async () => {
  const { down: downOrder } = useExportModalConfirm(
    async () => {
      const res = await exportList(searchParams);
      return res;
    },
    `${route.query.origin}/super/async-export/index`,
    {
      title: '收账单导出',
      content: '请确认是否导出该收账单',
    },
  );
  downOrder();
};
const handleApply = async () => {
  try {
    applyLoading.value = true;
    await checkList({ order_ids: checkAllState.checkedList.map((item: any) => item.order_id) });
    message.success('审核成功');
    getTableList('search');
    checkAllState.checkedList = [];
  } finally {
    applyLoading.value = false;
  }
};

const order_ids = ref<string[]>([]);

const handlePay = () => {
  const isStatus = checkAllState.checkedList.every((item: IReceiptList) => item.status !== '1');
  if (!isStatus) {
    message.error('请选择未审核收款单');
    return;
  }
  order_ids.value = checkAllState.checkedList.map((item: any) => item.order_id);
  showReceiptDrawer();
};

const handleAddCollectBill = () => {
  router.push({ path: '/recycle/finance/collect-bill-detail', query: { type: 'create' } });
};
const toBillDetail = (id: string) => {
  router.push({ path: '/recycle/finance/collect-bill-detail', query: { id } });
};

const onLoadList = () => {
  getTableList('search');
};

onMounted(async () => {
  getTableList();
});
</script>

<style lang="less" scoped>
.container {
  padding: 0 24px;
}
.main {
  padding: 24px 0;
}
.table-list {
  font-weight: 500;
}
.search-bar.pb-0 {
  padding-bottom: 0;
}
.link {
  padding: 4px 0;
  color: #3777ff;
}
.table-pagination {
  position: fixed;
  right: 16px;
  bottom: 0;
  left: 16px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: #fff;
}
.all-checked {
  display: flex;
  align-items: center;
}
.check-num {
  margin-left: 28px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
}
.num {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
}
.status-block {
  display: flex;
  align-items: center;
  .point {
    width: 8px;
    height: 8px;
    margin-right: 10px;
    border-radius: 8px;
  }
}
</style>
