import { ref } from 'vue';

import { getReplacementProposal } from '../../../../services';

export default function useRepTable() {
  const exchangeTableData = ref<any[]>([]);
  const upgradeTableData = ref<any[]>([]);
  const loading = ref<boolean>(false);
  const getTableData = async (exchangeParams: any, upgradeParams: any, is_upgrade: boolean = false) => {
    loading.value = true;
    if (is_upgrade) {
      const resultList = await Promise.allSettled([
        getReplacementProposal(exchangeParams),
        getReplacementProposal(upgradeParams),
      ]);

      exchangeTableData.value = resultList[0].status === 'fulfilled' ? resultList[0].value.data : [];
      upgradeTableData.value = resultList[1].status === 'fulfilled' ? resultList[1].value.data : [];
    } else {
      const { data } = await getReplacementProposal(exchangeParams);
      exchangeTableData.value = data;
    }

    loading.value = false;
  };
  return {
    exchangeTableData,
    upgradeTableData,
    loading,
    getTableData,
  };
}
