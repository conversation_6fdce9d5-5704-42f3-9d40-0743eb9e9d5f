export type TMarkStatus = 'deleted' | 'added' | '';
export interface ICategoryIdsItem {
  id: string;
  name: string;
  status?: TMarkStatus;
  color?: 'red' | 'blue';
}
export interface ITbDataItem {
  condition: {
    id: number;
    name: string;
  };
  deposit: {
    id: number;
    name: string;
  };
  user_risk_levels: undefined | number[];
  ruleType: '0' | '1';
  type: {
    category: {
      id: string;
      name: string;
      status?: TMarkStatus;
    }[];
    model: {
      id: string;
      name: string;
      status?: TMarkStatus;
    }[];
  };
  id: string;
}

export interface IConfigItem {
  id: string;
  category_ids: ICategoryIdsItem[];
  model_ids: {
    id: string;
    name: string;
  }[];
  type: '0' | '1';
  tmp_config: {
    category_ids: number[];
    model_ids: number[];
    user_risk_levels: {
      [key: string]: number[];
    };
  };
}
