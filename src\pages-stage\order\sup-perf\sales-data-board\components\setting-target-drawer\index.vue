<template>
  <a-drawer
    v-model:visible="visible"
    v-bind="drawConfig"
  >
    <template #extra>
      <close-outlined @click="handleCloseModal" />
    </template>
    <div class="setting-drawer-wrap">
      <div class="form-wrap">
        <a-form
          ref="searchFormRef"
          layout="inline"
          :model="searchForm"
        >
          <a-form-item
            label="销售部门"
            name="dept_id"
          >
            <a-select
              v-model:value="searchForm.dept_id"
              :disabled="salesList.length === 1"
              :options="salesList"
              placeholder="请选择销售部门"
              style="width: 196px"
            />
          </a-form-item>

          <a-form-item
            label="时间"
            name="month"
          >
            <a-date-picker
              v-model:value="searchForm.month"
              picker="month"
              placeholder="请选择时间"
              style="width: 218px"
              value-format="YYYY-MM"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              type="primary"
              @click="getData"
            >
              查询
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      <RTable
        ref="settingTargetRef"
        v-bind="settingTargetTableConfig"
        :use-table-options="useTableOptions"
      >
        <template #tableBodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'updated_at'">
            {{ record.updated_at ? dayjs(record.updated_at * 1000).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-button
              style="color: #3777ff"
              type="link"
              @click="handleClickItem(record)"
            >
              {{ !record.target ? '设定目标' : '版本记录' }}
            </a-button>
          </template>
        </template>
      </RTable>
    </div>

    <operation-modal
      v-model:visible="editModalInfo.visible"
      v-bind="editModalInfo"
      @refresh="getData"
    />

    <log-modal
      v-model:visible="logModalInfo.visible"
      v-bind="logModalInfo"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue';
import { DrawerProps, message } from 'ant-design-vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { RTable } from 'rrz-web-design';
import { getSettingTargetConfig } from './config';
import { useVModel } from '@/hook';

import OperationModal from '../../../data-board-common-components/operation-modal.vue';
import LogModal from '../../../data-board-common-components/log-modal.vue';
import type { IModalInfo } from '../../../data-board-common-components/data';
import type { FormInstance } from 'ant-design-vue';
import type { ISelectItem } from '../../data';

interface IProps {
  visible: boolean;
  salesList: ISelectItem[];
}

const props = defineProps<IProps>();
const emit = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emit);
const settingTargetRef = ref<InstanceType<typeof RTable>>();
const searchFormRef = ref<FormInstance>();
const { settingTargetTableConfig } = getSettingTargetConfig();

const useTableOptions = computed(() => ({
  extraParams() {
    return {
      ...searchForm,
    };
  },
}));

const drawConfig: DrawerProps = {
  placement: 'right',
  width: 740,
  title: '设定目标',
  closable: false,
  destroyOnClose: true,
  onClose: () => handleCloseModal(),
};
const searchForm = reactive<{ dept_id: string | number | null; month: string }>({
  dept_id: null,
  month: '',
});

watch(
  () => props,
  async val => {
    if (val.visible) {
      if (val.salesList.length === 1) {
        searchForm.dept_id = val.salesList[0].value;
        await nextTick();
        getData();
      }
    }
  },
  { deep: true },
);

// 获取数据
const getData = (type = '') => {
  if (settingTargetRef.value) {
    if (type === 'reset') {
      settingTargetRef.value.resetFields();
    } else {
      if (!searchForm.dept_id) {
        message.warning('请选择销售部门');
        return;
      }
      settingTargetRef.value.getTableList();
    }
  }
};

const handleCloseModal = () => {
  searchFormRef.value?.resetFields();
  emit('update:visible', false);
};

const handleClickItem = (record: Record<string, any>) => {
  if (!record.target) {
    handleShowEditModal(record);
  } else {
    handleShowLogModal(record);
  }
};
// 打开编辑框
const editModalInfo = reactive<IModalInfo & { type: string }>({
  visible: false,
  type: 'add',
  searchForm: {
    dept_id: '',
    dept_name: '',
    month: '',
  },
});
const handleShowEditModal = (item: Record<string, any>) => {
  editModalInfo.visible = true;
  editModalInfo.searchForm['dept_id'] = item.dept_id;
  editModalInfo.searchForm['month'] = item.month;
  editModalInfo.searchForm['dept_name'] = item.dept_name;
};
// 查看版本记录
const logModalInfo = reactive<IModalInfo>({
  visible: false,
  searchForm: {
    dept_id: '',
    dept_name: '',
    month: '',
  },
});
const handleShowLogModal = (item: Record<string, any>) => {
  logModalInfo.visible = true;
  logModalInfo.searchForm['dept_id'] = item.dept_id;
  logModalInfo.searchForm['month'] = item.month;
  logModalInfo.searchForm['dept_name'] = item.dept_name;
};
</script>

<style scoped lang="less">
.setting-drawer-wrap {
  .form-wrap {
    display: flex;
    margin-bottom: 24px;
    > .ant-form-inline .ant-form-item {
      margin-right: 24px;
    }
  }
  :deep(.r-search-table-container .r-search-content) {
    margin: 0 0 24px 0;
    .r-search-form-grid {
      grid-template-columns: 1fr 1fr 65px !important;
    }
  }
  :deep(.r-bottom-fix-table .ant-table-pagination) {
    padding: 16px 0 0;
  }
  :deep(.r-bottom-fix-table .ant-table-pagination .ant-pagination-total-text) {
    line-height: 32px;
    text-align: right;
  }
}
</style>
