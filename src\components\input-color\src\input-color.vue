<template>
  <div class="input-color-container flex-wrap flex-y-center">
    <slot
      name="reset"
      :reset="reset"
    >
      <div
        class="my-link"
        @click="reset()"
      >
        重置
      </div>
    </slot>

    <div class="input-color-content">
      <input
        v-model="color"
        class="origin-color-input"
        :disabled="props.isDisabled"
        type="color"
      >
      <div
        class="visible-color"
        :style="{ backgroundColor: color }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const emits = defineEmits(['update:value']);

const props = defineProps<{
  value?: string;
  defaultValue: string;
  isDisabled?: boolean;
}>();

const color = computed({
  get: () => props.value || props.defaultValue,
  set: val => emits('update:value', val),
});
function reset(value?: string) {
  // 设置禁用不给重置
  if (props.isDisabled) {
    return;
  }
  color.value = value || props.defaultValue;
}
</script>

<style scoped lang="less">
.input-color-container {
  .input-color-content {
    position: relative;
    width: 48px;
    height: 32px;
    margin-left: 8px;
    padding: 4px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 2px;
    .origin-color-input {
      position: absolute;
      z-index: 100;
      opacity: 0;
      inset: 0;
    }
    .visible-color {
      height: 100%;
      border-radius: 2px;
    }
  }
}
</style>
