import type { IRemarkInfo, IStrOrNumOption } from '@/components/order-business/typing';
import { GET, IAxiosResponse, POST } from '@/services/api';

/** 获取快递公司选择项 - 运营后台 */
export function fetchShipperOptions() {
  return GET<undefined, IStrOrNumOption[]>('/super/warehouse/after-sale-work/logistic-code-filter');
}

/** 获取快递公司选择项 - 商家后台 （返回值与运营后台相同）） */
export function fetchMerShipperOptions() {
  return GET<undefined, IStrOrNumOption[]>('/logistic/shipper-list');
}

/** 获取订单取消原因 */
export const fetchOrderCancelReason = (params: { order_id: string[] }) => {
  return GET('/order/cancelData', params, { hostType: 'Golang' });
};

/** 商家获取苹果订单取消原因 */
export const fetchAppleOrderCancelReason = (params: { order_ids: string[] }) => {
  return GET('/v3-order/get-order-inter-list', params);
};

/** 运营获取苹果订单取消原因 */
export const fetchSuperAppleOrderCancelReason = (params: { order_ids: string[] }) => {
  return GET('/super/v3-order/get-order-inter-list', params);
};

/** crm备注最新一条数据获取*/
export function fetchOrderRemarks(data: {
  unionIds: string[];
  unionSuffix: string;
}): Promise<IAxiosResponse<IRemarkInfo[]>> {
  return POST('/crm/data', data);
}

/** 获取代发备注 */
export function getIssueRemark(orderIds: string[]) {
  return POST('/super/v2-order/get-quality-order-remark', {
    order_ids: orderIds,
  });
}

/** 查询订单相关统计数据查询订单相关统计数据 */
export function fetchOrderStatistics(params: { order_id: string[] }) {
  return GET('/order/orderStatistic', params, { hostType: 'Golang' });
}

/** 租赁设备管控：获取详情（商家端） */
export function getWorkFormFilter(data: any) {
  return GET('/lock-order-work/work-form-filter', data);
}

/** 租赁设备管控：获取详情（运营端） */
export function superGetWorkFormFilter(data: any) {
  return GET('/super/lock-order-work/work-form-filter', data);
}

/** 新版订单申请退保（运营端） */
export function cancelInsurance(data: any) {
  return POST('/ant-chain/cancellation-insurance', data);
}

/** 新版订单申请退保（商家端） */
export function superCancelInsurance(data: any) {
  return POST('/super/ant-chain/cancellation-insurance', data);
}

/** 根据订单获取实名 */
export function getAddressRemind(params: { order_id: string }) {
  return GET('/address-remind/message', params);
}

/** 获取订单订单状态的分页数量 */
export function getOrderStatusPageNum(params: any) {
  return GET('/order/count', params, { hostType: 'Golang' });
}

/** 获取房屋租赁订单订单状态的分页数量 */
export function getRentHouseOrderStatusPageNum(params: any) {
  return GET('/order/house/count', params, { hostType: 'Golang' });
}

/** 批量获得订单商品清单和设备序列号 */
export function getCommodityItem(params: { order_ids: string[] }) {
  return GET('/device-code/order-item-list', params);
}

/** 检测订单是否符合回流 */
export function postCheckReuseDistribute(params, isSuper) {
  const url = isSuper ? '/super/reuse-distribute-order/check' : '/reuse-distribute-order/check';
  return POST(url, params);
}

/** 订单回流 */
export function postHandleReuseDistribute(params, isSuper) {
  const url = isSuper ? '/super/reuse-distribute-order/handle' : '/reuse-distribute-order/handle';
  return POST(url, params);
}
