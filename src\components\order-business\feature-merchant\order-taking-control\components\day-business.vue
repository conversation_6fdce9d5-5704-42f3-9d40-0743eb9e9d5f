<template>
  <a-space>
    <!--  放假时间  -->
    <a-tag
      v-if="!receiveStatus?.is_dispatch && receiveStatus?.holiday_day"
      class="holiday-tag"
      closable
    >
      放假时间：{{ receiveStatus?.holiday_day }}
    </a-tag>
    <!--  营业时间  -->
    <a-tag
      v-if="receiveStatus?.dispatch_info?.is_platform || (receiveStatus?.is_dispatch && receiveStatus?.dispatch_info)"
      class="receive-tag"
      :closable="!receiveStatus?.dispatch_info?.is_platform"
    >
      <span
        v-if="receiveStatus?.dispatch_info.is_platform"
        style="color: var(--ant-error-color)"
      > 【平台配置】 </span>
      <span
        v-if="receiveStatus?.dispatch_info.is_default"
        style="color: rgba(6, 21, 51, 0.85)"
      >
        暂未设置营业时间，
      </span>
      <span style="margin-right: 3px">
        {{ receiveStatus?.dispatch_info.is_default ? '默认营业时间：' : '今日营业时间：' }}
        {{ businessTime }}
      </span>
      <span
        v-if="receiveStatus?.dispatch_info.is_platform"
        class="text-link"
        :style="{ color: applyTextData[receiveStatus?.dispatch_info.status]?.color }"
        @click="openApplyModal"
      >
        {{ applyTextData[receiveStatus?.dispatch_info.status]?.text }}
      </span>
    </a-tag>
    <!--  运营控制  -->
    <a-button
      type="link"
      @click="openParentPage"
    >
      <template #icon>
        <SettingOutlined style="transform: rotate(-30deg)" />
      </template>
      <span style="margin-left: 4px">接单高级设置</span>
    </a-button>

    <div class="receive-status">
      <span>接单状态</span>
      <a-tooltip placement="bottom">
        <template #title>
          <span>{{ tooltipText }}</span>
        </template>
        <InfoCircleOutlined style="margin: 0 6px; font-size: 12px" />
      </a-tooltip>
      ：
      <a-switch
        :checked="receiveStatus?.is_dispatch"
        checked-children="开启"
        un-checked-children="关闭"
        @change="handleReceiveChange"
      />
    </div>
  </a-space>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { InfoCircleOutlined, SettingOutlined } from '@ant-design/icons-vue';

import { basicModal } from '@/components/order-business/basic-modals';
import { useUnityModal } from '@/components/unity-modal';

import { useReceiveInfo } from '../composables/use-receive-info';

const {
  receiveStatus,
  applyTextData,
  tooltipText,
  openParentPage,
  getReceiveData,
  handleReceiveChange,
} = useReceiveInfo();

const { onToggleComponent } = useUnityModal();

/** 今日运营时间 */
const businessTime = computed(() => {
  if (!receiveStatus.value?.dispatch_info?.day_info) {
    return '--';
  }
  let timeText = '--';
  const { work_shift, time_rules, day_num, day_total_limit } = receiveStatus.value.dispatch_info.day_info;
  if (work_shift) {
    timeText = work_shift.map(item => `${item.label}（${item.time}）`).join();
  } else if (time_rules) {
    timeText = time_rules.map(item => `${item.start}-${item.end}`).join('、');
  }
  if (day_total_limit === '') {
    return timeText;
  } else {
    return `${timeText} | 日单量上限：${day_num}/${day_total_limit}`;
  }
});

function openApplyModal() {
  onToggleComponent(basicModal.platformConfigApply, {
    businessId: receiveStatus.value?.dispatch_info?.business_id,
    onResetOptions: getReceiveData,
  });
}

// 初始化获取
onMounted(() => {
  getReceiveData();
});
</script>

<style scoped lang="less">
.holiday-tag {
  padding: 1px 8px;
  color: #faad14;
  background: #fffbe6;
  border: none;
}

.receive-tag {
  padding: 1px 8px;
  color: #3777ff;
  background-color: #f0f7ff;
  border: none;
}

.receive-status {
  display: flex;
  align-items: center;
}
</style>
