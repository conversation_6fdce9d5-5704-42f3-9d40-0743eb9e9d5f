<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :confirm-loading="loading"
    :destroy-on-close="true"
    title="物流预约"
    :visible="visible"
    width="560px"
    @cancel="close"
    @ok="confirm"
  >
    <a-radio-group v-model:value="expressValue">
      <a-radio-button value="sf">
        顺丰速运（线上预约）
      </a-radio-button>
      <a-radio-button value="other">
        自行送出
      </a-radio-button>
    </a-radio-group>
    <div class="content">
      <component
        :is="currentComponent"
        ref="eleRef"
        v-bind="attr"
      />
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, type PropType,ref, useAttrs } from 'vue';
import { useModal } from 'rrz-web-design';

import OtherService from './components/other-service.vue';
import SfService from './components/sf-service.vue';

const props = defineProps({
  onConfirm: {
    type: Function as PropType<(type,data) => Promise<any>>,
    default: () => null,
    required: true,
  },
});

const attr = useAttrs();

const emits = defineEmits(['loadList']);

const eleRef = ref<InstanceType<typeof SfService | typeof OtherService>>();

const expressValue = ref<'sf' | 'other'>('sf');

const { loading, visible, confirm, close } = useModal(async () => {
  const res = await eleRef.value?.validate();
  if (props.onConfirm) {
    await props.onConfirm(expressValue.value,res);
    close();
    emits('loadList');
  } else {
    console.error('请配置props.onConfirm回调');
  }
});

const componentMap = {
  sf: SfService,
  other: OtherService,
};
const currentComponent = computed(() => {
  return componentMap[expressValue.value];
});
</script>

<style lang="less" scoped>
.content {
  margin-top: 16px;
}
</style>
