<template>
  <div class="module-open">
    <div class="sub-title">
      未开通模块
    </div>
    <not-open
      :loading="loading"
      :not-opened-list="notOpenedList"
      @reload-list="reloadList"
    />
    <div class="sub-title">
      已开通模块
    </div>
    <open-module
      :loading="loading"
      :opened-list="openedList"
      @reload-list="reloadList"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { OpenModuleItem } from './data';
import NotOpen from './not-open.vue';
import OpenModule from './open-module.vue';
import { getOpenList } from './service';

const functionObj = {
  1: {
    list: [
      { name: '逾期数据查询' },
      { name: '发货数据查询' },
      { name: '订单日历' },
      { name: '活动流量榜单' },
      { name: '商家层级查询' },
      { name: '码商数据查询' },
    ],
    icon: 'https://img1.rrzuji.cn/uploads/scheme/2304/17/m/5GZn0CrB4JtN7OrBIa1m.png',
  },
  2: {
    list: [{ name: '用户分析' }, { name: '类目分析' }],
    icon: 'https://img1.rrzuji.cn/uploads/scheme/2304/17/m/TA3RfT7RorbVGJniWR0i.png',
  },
  5: { list: [{ name: '收益模型' }], icon: 'https://img1.rrzuji.cn/uploads/scheme/2304/17/m/u9Jy7HdWkc8PNufCuyu0.png' },
  10: {
    list: [{ name: '活动流量监控' }],
    icon: 'https://img1.rrzuji.cn/uploads/scheme/2308/22/m/iJcXeIo7rXcYIIXYW4ST.png',
  },
};

const notOpenedList = ref<OpenModuleItem[]>([]);
const openedList = ref<OpenModuleItem[]>([]);
const loading = ref<boolean>(false);

const loadOpenList = () => {
  loading.value = true;
  getOpenList()
    .then(res => {
      res.data.forEach((i: OpenModuleItem) => {
        i.icon = functionObj[i.id].icon;
        i.functionList = functionObj[i.id].list;
        i.is_open_module === 0 ? notOpenedList.value.push(i) : openedList.value.push(i);
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

const reloadList = () => {
  openedList.value = [];
  notOpenedList.value = [];
  loadOpenList();
};

onMounted(() => {
  loadOpenList();
});
</script>

<style lang="less" scoped>
.module-open {
  padding: 24px;
  background-color: #fff;
  .sub-title {
    font-weight: bold;
    font-size: 16px;
  }
}
</style>
