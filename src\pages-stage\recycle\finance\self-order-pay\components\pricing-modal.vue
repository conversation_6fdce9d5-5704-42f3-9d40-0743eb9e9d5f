<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    title="确认结算"
    :visible="visible"
    width="400px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item
        label="流水号"
        name="trade_no"
        :rules="[{ required: true, message: '流水号!' }]"
      >
        <a-input-number
          v-model:value="formState.trade_no"
          :maxlength="15"
          placeholder="请输入流水号"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message, FormInstance } from 'ant-design-vue';
import { settlement } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const submitLoading = ref(false);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref<any>({
  trade_no: null,
});

const onCancel = () => emits('update:visible', false);

const onOk = () => {
  formRef.value?.validate().then(() => {
    submitLoading.value = true;
    settlement(props.id, formState.value.trade_no)
      .then(() => {
        onCancel();
        message.success('操作成功');
        emits('onLoadList');
      })
      .finally(() => (submitLoading.value = false));
  });
};
</script>
