<template>
  <a-card
    :bordered="true"
    style="width: 100%"
  >
    <div class="flex-justify">
      <div class="text">
        店铺经营类目
      </div>
      <a-button
        type="default"
        @click="handleApplyClassic"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        申请类目
      </a-button>
    </div>
    <div
      v-if="categoryList.length > 0"
      class="tags"
    >
      <a-tag
        v-for="(value, key) in selectCategory"
        :key="key"
      >
        {{ value }}
      </a-tag>
    </div>
    <div id="render-box" />
  </a-card>
</template>
<script lang="ts" setup>
import { h, watch } from 'vue';
import { Modal } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';

import { loadG2 } from '@/utils/static-load';
const props = defineProps({
  categoryList: {
    type: Array,
    default() {
      return [];
    },
  },
  selectCategory: {
    type: Object,
    default() {
      return {};
    },
  },
  goodsInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
const handleApplyClassic = () => {
  info();
};

const info = () => {
  Modal.info({
    title: '',
    content: h('div', {}, [h('p', '维护通知'), h('p', '平台类目维护中，请联系商家运营同学进行添加')]),
    onOk() {
      console.log('ok');
    },
  });
};

watch(
  () => props.goodsInfo,
  async val => {
    if (Object.keys(val.data).length > 0) {
      const data = Object.values(val.data).map((i: any) => ({
        item: i.label,
        count: Number(i.num),
        percent: i.rate,
        category: i.category,
      }));

      if (!chart) {
        await createCharts();
        await initCharts(data);
      } else {
        chart.changeData(data);
      }
    }
  },
);

let chart: any = null;

const createCharts = async () => {
  await loadG2();
  chart = new window.G2.Chart({
    container: 'render-box',
    autoFit: true,
    height: 500,
  });
};

async function initCharts(data: any) {
  chart.data(data);
  chart.scale('percent', {
    formatter: (val: number) => {
      // val = val * 100 + '%';
      return val * 100 + '%';
    },
  });
  chart.legend({
    position: 'right',
  });
  chart.coordinate('theta', {
    radius: 0.75,
    innerRadius: 0.6,
  });
  chart.tooltip({
    showTitle: false,
    showMarkers: false,
    itemTpl:
      '<li class="g2-tooltip-list-item"><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}</li>',
  });
  // 辅助文本
  chart
    .annotation()
    .text({
      position: ['50%', '50%'],
      content: '主机',
      style: {
        fontSize: 14,
        fill: '#8c8c8c',
        textAlign: 'center',
      },
      offsetY: -20,
    })
    .text({
      position: ['50%', '50%'],
      content: props.goodsInfo.goods_num,
      style: {
        fontSize: 20,
        fill: '#8c8c8c',
        textAlign: 'center',
      },
      offsetX: -10,
      offsetY: 20,
    })
    .text({
      position: ['50%', '50%'],
      content: '台',
      style: {
        fontSize: 14,
        fill: '#8c8c8c',
        textAlign: 'center',
      },
      offsetY: 20,
      offsetX: 20,
    });
  chart
    .interval()
    .adjust('stack')
    .position('percent')
    .color('item')
    .label('percent', (percent: number) => {
      return {
        content: (data: any) => {
          return `${data.item}: ${percent}%`;
        },
      };
    })
    .tooltip('item*percent', (item: string, percent: number) => {
      return {
        name: item,
        value: percent + '%',
      };
    });

  chart.interaction('element-active');

  chart.render();
}
</script>
<style scoped lang="less">
.flex-justify {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}
.tags {
  margin-top: 24px;
  :deep(.ant-tag) {
    padding: 2px 8px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 12px;
    background: #f5f7fa;
    border: 0;
  }
}

#render-box {
  width: 600px;
  margin: 0 auto;
}
</style>
