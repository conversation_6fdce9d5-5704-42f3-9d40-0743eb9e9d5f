<template>
  <layout-admin-page
    title="修改日志"
    top-fixed
  >
    <template #title-prefix>
      <ArrowLeftOutlined
        style="margin-right: 8px"
        @click="$router.go(-1)"
      />
    </template>
    <template #title-suffix>
      <a-radio-group
        v-model:value="ruleType"
        style="margin-left: 16px"
      >
        <a-radio-button value="ruleBefore">
          修改前
        </a-radio-button>
        <a-radio-button value="ruleAfter">
          修改后
        </a-radio-button>
      </a-radio-group>
    </template>
    <div class="change-type-tip flex-wrap flex-y-center">
      <div class="tip-item tip-change flex-wrap flex-y-center">
        修改
      </div>
      <div class="tip-item tip-add flex-wrap flex-y-center">
        新增
      </div>
      <div class="tip-item tip-del flex-wrap flex-y-center">
        删除
      </div>
    </div>
    <div class="detail-log">
      <div class="rule-item">
        <div class="rule-item-title">
          绑定渠道
        </div>
        <div>
          <a-table
            :columns="conditionCol"
            :data-source="conditionData"
            :pagination="false"
          />
        </div>
      </div>
      <div class="rule-item">
        <div class="rule-item-title">
          型号规则
        </div>
        <div>
          <a-table
            :columns="modelCol"
            :data-source="modelList[ruleType]"
            :pagination="false"
            :scroll="{ y: 300 }"
          >
            <template #bodyCell="{ record, column, text }">
              <template v-if="column.dataIndex === 'title'">
                <a-space wrap>
                  <a-tag
                    v-for="item in record.list"
                    :key="item.id"
                    :color="item.color"
                    style="margin-right: 0"
                  >
                    {{ item.title }}
                  </a-tag>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'deposit_ratio'">
                <a-tag :color="record.color">
                  {{ text }}%
                </a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <div class="rule-item">
        <div class="rule-item-title">
          类目规则
        </div>
        <div>
          <a-table
            :columns="categoryCol"
            :data-source="categoryList[ruleType]"
            :pagination="false"
          >
            <template #bodyCell="{ record, column, text }">
              <template v-if="column.dataIndex === 'title'">
                <a-space wrap>
                  <a-tag
                    v-for="item in record.list"
                    :key="item.id"
                    :color="item.color"
                    style="margin-right: 0"
                  >
                    {{ item.title }}
                  </a-tag>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'deposit_ratio'">
                <a-tag :color="record.color">
                  {{ text }}%
                </a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <div class="rule-item">
        <div class="rule-item-title">
          租赁方案
        </div>
        <div>
          <a-table
            :columns="rentalRuleCol"
            :data-source="rentalRuleList[ruleType]"
            :pagination="false"
          >
            <template #bodyCell="{ record, column, text }">
              <template v-if="column.dataIndex === 'title'">
                <a-space wrap>
                  <a-tag
                    v-for="item in record.list"
                    :key="item.id"
                    :color="item.color"
                    style="margin-right: 0"
                  >
                    {{ item.title }}
                  </a-tag>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'deposit_ratio'">
                <a-tag :color="record.color">
                  {{ text }}%
                </a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </layout-admin-page>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';

import { apiGetLogData } from './services';

const route = useRoute();
const ruleType = ref('ruleBefore');

const conditionData = ref([]);
const conditionCol = ref([]);

function handleCondition(config) {
  conditionCol.value = config.map(item => ({
    title: item.title,
    dataIndex: item.type_id,
  }));
  conditionData.value[0] = Object.fromEntries(config.map(item => [item.type_id, item.value]));
}

const modelCol = [
  {
    title: '型号',
    dataIndex: 'title',
    align: 'center',
  },
  {
    title: '押金比例',
    width: 200,
    dataIndex: 'deposit_ratio',
    align: 'center',
  },
];
const modelList = reactive({
  ruleAfter: [],
  ruleBefore: [],
});
const categoryCol = [
  {
    title: '二级类目',
    dataIndex: 'title',
    align: 'center',
  },
  {
    title: '押金比例',
    width: 200,
    dataIndex: 'deposit_ratio',
  },
];
const categoryList = reactive({
  ruleBefore: [],
  ruleAfter: [],
});

const rentalRuleList = reactive({
  ruleBefore: [],
  ruleAfter: [],
});

const rentalRuleCol = [
  {
    title: '租赁方案',
    dataIndex: 'title',
    align: 'center',
  },
  {
    title: '押金比例',
    width: 200,
    dataIndex: 'deposit_ratio',
  },
];

function getLogData() {
  apiGetLogData({
    rule_id: route.params.id,
  }).then(res => {
    const { configConditionData, ruleAfter, ruleBefore } = res.data;
    handleCondition(configConditionData);

    const modelResult = compareOtherness(ruleBefore.model, ruleAfter.model);
    modelList.ruleBefore = modelResult.before;
    modelList.ruleAfter = modelResult.after;

    const categoryResult = compareOtherness(ruleBefore.category, ruleAfter.category);
    categoryList.ruleBefore = categoryResult.before;
    categoryList.ruleAfter = categoryResult.after;

    const rentalRuleResult = compareOtherness(ruleBefore.rental_plan, ruleAfter.rental_plan);
    rentalRuleList.ruleBefore = rentalRuleResult.before;
    rentalRuleList.ruleAfter = rentalRuleResult.after;
  });
}

function compareOtherness(before, after) {
  const beforeMap = new Map(before.map(item => [item.id, item]));
  const afterMap = new Map(after.map(item => [item.id, item]));

  const processItems = (items, otherMap, color) =>
    items.map(item => {
      const otherItem = otherMap.get(item.id);
      return {
        ...item,
        color: otherItem ? (item.deposit_ratio === otherItem.deposit_ratio ? '' : 'cyan') : color,
        list: item.list.map(listItem => ({
          ...listItem,
          color: otherItem ? (otherItem.list.some(i => i.id === listItem.id) ? '' : color) : color,
        })),
      };
    });

  return {
    before: processItems(before, afterMap, 'red'),
    after: processItems(after, beforeMap, 'blue'),
  };
}

onMounted(() => {
  getLogData();
});
</script>

<style scoped lang="less">
.detail-log {
  padding: 0 24px;

  .rule-item {
    margin-bottom: 24px;

    .rule-item-title {
      margin-bottom: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }
  }
}

.change-type-tip {
  margin-bottom: 24px;
  padding: 0 24px;
  background-color: #fff;

  .tip-item {
    margin-right: 24px;
    font-size: 14px;

    &::before {
      display: inline-block;
      width: 24px;
      height: 4px;
      margin-right: 8px;
      border-radius: 4px;
      content: ' ';
    }
  }

  .tip-change {
    color: #00c8be;

    &::before {
      background: #00c8be;
    }
  }

  .tip-add {
    color: #3777ff;

    &::before {
      background: #3777ff;
    }
  }

  .tip-del {
    color: #ff4d4f;

    &::before {
      background: #ff4d4f;
    }
  }
}
</style>
