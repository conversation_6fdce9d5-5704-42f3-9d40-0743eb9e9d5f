<template>
  <div class="factor-conditional-rules">
    <slot
      :add-rules="addRules"
      name="addBox"
      :rules-info="rulesInfo"
    >
      <span
        class="add-box"
        @click="addChildRules(rulesInfo)"
      >
        <PlusCircleFilled :style="{ color: '#00c8be', marginRight: '4px' }" />
        添加条件
      </span>
    </slot>
    <div class="rules-content">
      <RelationContent
        v-if="isLogic(rulesInfo) && rulesInfo.rules.length > 0"
        v-model:dataInfo="rulesInfo"
        :level="level"
        @add-child-rules="addChildRules"
        @add-rules="addRules"
        @del-rules="delRules"
      >
        <template #rulesItemLevel="{ rulesItemInfo }">
          <slot
            :item-info="rulesItemInfo.rules"
            name="rulesItem"
          />
        </template>
      </RelationContent>
      <template v-else>
        <slot
          :item-info="rulesInfo.rules"
          name="rulesItem"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { PlusCircleFilled } from '@ant-design/icons-vue';

import { deepClone } from '@/utils/base';

import RelationContent from './components/relation-content.vue';
import { useRulesHandle } from './composables/use-rules-handle';
import type { TFactorConditionalRulesProps } from './data.d';
const props = defineProps<TFactorConditionalRulesProps>();

const emits = defineEmits(['change']);
const { rulesInfo, level, delRules, addRules, isLogic, getData, getTempRules, addChildRules } = useRulesHandle(props);
watch(
  () => props.dataInfo,
  val => {
    if (val && Object.keys(val).length !== 0) {
      rulesInfo.value = deepClone(props.dataInfo);
    } else {
      rulesInfo.value = getTempRules();
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => rulesInfo.value,
  val => {
    emits('change', val);
  },
  {
    deep: true,
  },
);

defineExpose({ getData });
</script>

<style lang="less" scoped>
.factor-conditional-rules {
  width: 100%;
  .add-box {
    display: inline-block;
    margin-bottom: 8px;
    cursor: pointer;
  }
  .rules-content {
    width: 100%;
    .relative-box {
      position: relative;
    }
    .rules-list-box {
      flex: 1;
    }
    .rules-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
    .right-box {
      margin-left: 32px;
    }
  }
}
</style>
