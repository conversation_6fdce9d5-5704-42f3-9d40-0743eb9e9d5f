<template>
  <layout-page title="下单成功页弹窗">
    <template #extra>
      <a-radio-group
        v-model:value="tabIndex"
        @change="hangleTabChange"
      >
        <a-radio-button
          v-for="tab in tabs"
          :key="tab.key"
          :value="tab.key"
        >
          {{ tab.title }}
        </a-radio-button>
      </a-radio-group>
    </template>
    <template #container>
      <div class="container-inner">
        <div class="content-mian">
          <a-spin
            size="large"
            :spinning="loading"
          >
            <div class="header flex-wrap flex-y-center">
              <div class="title flex-wrap flex-y-center">
                <span>弹窗图配置</span>
              </div>
              <a-button
                class="add-btn"
                size="large"
                style="margin-bottom: 0"
                type="link"
                @click="onEdit('create')"
              >
                <PlusOutlined />
                添加弹窗
              </a-button>
            </div>
            <div class="list-mian">
              <div
                v-for="(item, index) in list"
                :key="index"
                class="item-mian"
              >
                <CardImage
                  :card-switch-false-value="0"
                  :card-switch-name="'is_use'"
                  :card-switch-true-value="1"
                  :card-width="'200'"
                  :image-height="'200'"
                  :image-width="'200'"
                  :is-edit="true"
                  :item="{
                    cover: item.cover,
                    is_use: Number(item.status),
                    channelType: [''],
                  }"
                  style="margin-left: 7px"
                  @on-delete="onDelete(item.id)"
                  @on-edit="onEdit('edit', item.id)"
                  @on-switch="onSwitch(item)"
                />
              </div>
            </div>
          </a-spin>
        </div>
      </div>
      <EditModal
        :id="currentId"
        v-model:visible="visible"
        :ad-type="tabIndex === '1' ? 'orderSuccessAlipayPop' : 'orderSuccessWxPop'"
        :title="title"
        @get-list="getList"
      />
    </template>
  </layout-page>
</template>
<script setup lang="ts">
import CardImage from '@/components/card/card-image.vue';
import EditModal from './components/edit-modal.vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { orderSuccessPopList, orderSuccessPopUpdate, orderSuccessPopDelete } from './service';
import { ref } from 'vue';
const tabs = [
  {
    title: '支付宝小程序',
    key: '1',
  },
  {
    title: '微信小程序',
    key: '2',
  },
];
const tabIndex = ref('1');
const loading = ref(false);
const visible = ref(false);
const title = ref('添加弹窗');
const list = ref<any[]>([]);
const currentId = ref<string | undefined>('');
const onDelete = async (id: any) => {
  await orderSuccessPopDelete({
    id: id,
    adType: tabIndex.value === '1' ? 'orderSuccessAlipayPop' : 'orderSuccessWxPop',
  });
  getList();
};
const onEdit = (type: string, id?: string) => {
  if (type === 'edit') {
    title.value = '编辑弹窗';
    currentId.value = id;
  } else {
    currentId.value = '';
  }
  visible.value = true;
};
const onSwitch = async (param: any) => {
  await orderSuccessPopUpdate({
    id: param.id,
    adType: tabIndex.value === '1' ? 'orderSuccessAlipayPop' : 'orderSuccessWxPop',
    status: !Number(param.status) ? 1 : 0,
  });
  getList();
};
const getList = async () => {
  loading.value = true;
  const res = await orderSuccessPopList({
    adType: tabIndex.value === '1' ? 'orderSuccessAlipayPop' : 'orderSuccessWxPop',
  });
  list.value = res.data;
  loading.value = false;
};
const hangleTabChange = () => {
  getList();
};
getList();
</script>

<style lang="less" scoped>
.content-mian {
  height: 600px;
  overflow: auto;
}
:deep(.ant-tabs-top-content) {
  height: calc(100% - 60px);
  overflow: auto;
}
:deep(.ant-tabs-left-content) {
  height: 100%;
  overflow: auto;
}
:deep(.ant-tabs-left-bar) {
  height: 100%;
  overflow: auto;
}
:deep(.ant-tabs) {
  overflow: auto;
}
.form-wrap {
  padding-top: 40px;
}
:deep(.brande-form-extend) {
  display: flex;
  flex-wrap: wrap;
  .form-extend-wrap {
    width: 250px;
  }
}
.header {
  .title {
    position: relative;
    padding-left: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
  }
  .title::before {
    position: absolute;
    top: 50%;
    left: 6px;
    width: 4px;
    height: 16px;
    background: rgb(0, 200, 190);
    transform: translateY(-50%);
    content: '';
  }
}
.header-add-btn {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
.header-add-icon {
  width: 15px;
  height: 16px;
  margin: 0 4px 0 16px;
  color: rgba(6, 21, 51, 0.65);
}
.list-mian {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .item-mian {
    padding-right: 8px;
    padding-bottom: 16px;
  }
}
</style>
