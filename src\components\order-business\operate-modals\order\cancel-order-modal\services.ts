import { GET, POST, RequestConfig } from '@/services/api';
import { getCookie } from '@/utils/cookies';

const config: RequestConfig = {
  hostType: 'Golang',
};

/** 获取订单是否有取消限（运营端） */
export function superGetCancelOrderLimit(params: any) {
  return GET('/super/v3-order/order-cancel-compatible-supply', params);
}

/** 获取订单是否有取消限（商家端） */
export function getCancelOrderLimit(params: any) {
  return GET('/order/order-cancel-compatible-supply', params);
}

/**
 *  @description 是否有正常状态顺丰取件订单
 * @param order_id 订单id
 */
export function hasSfOrder(order_id: string) {
  return GET('/super/v3-order/has-sf-order', { order_id });
}

/**
 * @description: 订单取消
 * @param {any} data
 * @return {*}
 */
export function orderRefund(data: any) {
  const paramsData = {
    order_id: String(data.order_id),
    reason_type: isNaN(data.reason_type) ? undefined : Number(data.reason_type),
    reason: Number(data.reason),
    is_sms_send: isNaN(data.is_sms_send) ? undefined : Number(data.is_sms_send),
    refund_rental: data.refund_rental?.toString(),
    refund_deposit: data.refund_deposit?.toString(),
    refund_freight: data.refund_freight?.toString(),
    account_pay_log_refund_apply_id: data.account_pay_log_refund_apply_id,
  };
  return POST('/order/cancelOrder', paramsData, config);
}

/**
 * @description: 已在运行大工单流程的订单直接取消
 * @param {any} data
 * @return {*}
 */
export function largeOrderRefund(data: any) {
  const bodyData = {
    order_id: String(data.order_id),
    reason_type: isNaN(data.reason_type) ? undefined : Number(data.reason_type),
    reason: Number(data.reason),
  };
  return POST('/super/work-order-inter/white-direct-cancel', bodyData, {
    headers: {
      Authorization: getCookie('Go-Token'),
    },
  });
}

/**
 * @description: 获取退款押金、退款租金（运营端）
 * @param data
 */
export function superGetRefundMoney(data: { order_id: string }) {
  return GET('/super/v3-order/get-order-refund-amount', data);
}

/**
 * @description: 获取退款押金、退款租金（商家端）
 * @param data
 */
export function getRefundMoney(data: { order_id: string }) {
  return GET('/order/get-order-refund-amount', data);
}

/** 获取订单取消原因 */
export const fetchOrderCancelReason = (params: { order_id: string[] }) => {
  return GET('/order/cancelData', params, { hostType: 'Golang' });
};

//获取取消原因options
export const getOrderCancelReasonOptionsApi = () => {
  return GET('/newWorkOrder/new-template/order-cancel-reason-option');
};

//是否有风控结果
export const getRiskRepeatCancelWayApi = (params: { order_id: string }) => {
  return GET('/work-order-inter-risk-repeat/cancel-way', params);
};
