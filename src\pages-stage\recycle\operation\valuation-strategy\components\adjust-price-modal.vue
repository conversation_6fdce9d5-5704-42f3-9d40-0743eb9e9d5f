<template>
  <a-modal
    v-model:visible="visible"
    :mask-closable="false"
    :title="title"
  >
    <div class="progress-text">
      调价进度：<span class="progress-text__value">
        <i>{{ successNum }}</i>/{{ fullNm }}
      </span>
    </div>
    <a-progress
      class="progress-block"
      :percent="processValue"
      stroke-color="#00C8BE"
    />

    <template #footer>
      <a-button
        type="primary"
        @click="close"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useModal } from '@/hook/component/use-modal';
import { getBatchSettingProgress } from '../service';

let timer: any = null;

const title = ref<string>('');

const successNum = ref<number>(0);
const fullNm = ref<number>(1);

const { visible, open, close } = useModal(
  () => {
    return;
  },
  {
    beforeOpen: () => {
      clearTimer();
      setTiming();
    },
    beforeClose: () => {
      clearTimer();
    },
  },
);

const processValue = computed(() => (successNum.value / fullNm.value) * 100);

let isRquesting = false;
function setTiming() {
  timer = setInterval(() => {
    if (isRquesting) return;
    isRquesting = true;
    getBatchSettingProgress()
      .then(res => {
        const data = res.data || {};
        const { finish_num, count_num } = data;
        successNum.value = finish_num;
        fullNm.value = count_num;
        title.value = finish_num < count_num ? '正在调价' : '调价完成';
        if (finish_num >= count_num) {
          clearTimer();
        }
      })
      .finally(() => {
        isRquesting = false;
      });
  }, 1000);
}

function clearTimer() {
  clearInterval(timer);
  timer = null;
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.progress-text {
  margin-bottom: 8px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  &__value {
    font-weight: 500;
    i {
      color: #00c8be;
      font-style: normal;
    }
    &.primary {
      color: #00c8be;
    }
  }
}
.progress-block {
  :deep(.anticon-check-circle) {
    color: #00c8be;
  }
}
</style>
