<template>
  <div class="row-item">
    <div style="display: flex; align-items: center">
      <a-cascader
        allow-clear
        :options="slelctOptions"
        placeholder="请选择"
        :value="cascaderValue"
        @change="change"
      >
        <template #displayRender="{ labels }">
          {{ formatLableValue(labels) }}
        </template>
      </a-cascader>
      <a-input-number
        id="inputNumber"
        v-model:value="count"
        default-value="1"
        :max="10"
        :min="1"
        placeholder="请输入"
        style="margin-right: 4px; margin-left: 12px"
        @change="numberChange"
      />
      <div>台</div>
      <img
        v-if="showDelete"
        class="delete-img"
        src="https://img1.rrzuji.cn/uploads/scheme/2305/26/m/MMCZ16PywLKNtmZGXuvA.png"
        @click="deleteRow"
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
const props = defineProps({
  slelctOptions: {
    type: Array,
    default: () => [],
  },
  model: {
    type: Array,
    default: () => [],
  },
  currentIndex: {
    type: Number,
    default: 0,
  },
  item: {
    type: Object,
    default: () => ({}),
  },
});
const emits = defineEmits(['add-row', 'number-change', 'delete-row']);
const count = ref(1);
const cascaderValue = computed(() => {
  let arr = [];
  for (const key in props.item) {
    if (key === 'id') continue;
    if (key === 'count') continue;
    arr.push(props.item[key]);
  }
  return arr;
});
const formatLableValue = (value: string[]) => {
  return value.includes('') ? '请选择' : value.join('/');
};
const showDelete = computed(() => {
  if (props.currentIndex === 0) {
    return props.model.length > 1;
  } else {
    return props.currentIndex;
  }
});
const change = (data: string[]) => {
  if (!data) return;
  emits('add-row', {
    data: {
      id: Math.random() + new Date().getTime(),
      model: data[0],
      memory: data[1],
      new: data[2],
      count: count.value,
    },
    currentIndex: props.currentIndex,
  });
};
const deleteRow = () => {
  emits('delete-row', props.currentIndex);
};
const numberChange = (value: number | string) => {
  emits('number-change', value, props.currentIndex);
};
</script>

<style lang="less" scoped>
.row-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 10px;
  padding-right: 20px;
}
.delete-img {
  position: absolute;
  right: 0;
}
</style>
