<template>
  <CommodityCategoryCascader
    v-model:value="bindValue"
    :multiple="multiple"
    :scene="EScene.PlatformGoods"
    v-bind="attrs"
  />
</template>

<script setup lang="ts">
import { computed, useAttrs } from 'vue';

import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import { EScene } from '@/utils/enums/commodity';

defineOptions({
  inheritAttrs: true,
});

const props = defineProps<{
  value?: string[];
  multiple?: boolean;
  readonly?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:value', value?: string[]): void;
}>();

const bindValue = computed({
  get() {
    const value: number[][] = props.value?.map(item => item.split('_').map(Number)).filter(item => item?.length);
    return props.multiple ? value : value?.[0]; // 单选时只取数组第一个值
  },
  set(value) {
    if (props.multiple) {
      const val = value?.map(item => item?.join('_')).filter(Boolean);
      emit('update:value', val);
    } else {
      emit('update:value', value && [value.join('_')]);
    }
  },
});

const attrs = useAttrs();
</script>
