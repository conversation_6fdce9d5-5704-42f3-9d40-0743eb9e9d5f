export interface ISearchParams {
  order_id: string;
}

export interface ITransactionTableList {
  id: string;
  recycle_order_id: string;
  created_at: string;
  evaluate_price: string;
  purchased_price: string;
  status: string;
  status_text: string;
  comment_level: string;
  comment_level_text: string;
}
export interface ITransactionRecordDetail {
  id: string;
  recycle_order_id: string;
  user_id: string;
  goods_name: string;
  evaluate_price: string;
  purchased_price: string;
  quality_report_images: string[];
  show_comment: string;
  status: string;
  comment_level: number;
  comment_labels: string[];
  comment_content: string;
  comment_level_text: string;
  comment_labels_text: string;
}

export interface IModalState {
  recycle_order_id: string;
  show_comment: string;
}

interface IDispatchModal<T> {
  action: T;
  params?: {
    id?: string;
    [key: string]: any;
  };
}
