<template>
  <div class="order-status recycle-customer-table-flex-col">
    <div class="status">
      <table-status
        :options="searchOptions.orderStatusOptions"
        :status="baseData.order_status"
      />
    </div>
    <div class="order-origin">
      {{ searchOptions.orderOringinOptions.find(item => item.value === baseData.source_type)?.label ?? '' }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import TableStatus from '@/components/table-status/index';
import { searchOptions } from '../../config';
import { IBaseData } from '../../data';

defineProps({
  baseData: {
    type: Object as PropType<IBaseData>,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.order-status {
  flex: 1;
  justify-content: center;
  min-width: 116px;
  font-weight: 400;
  .order-origin {
    color: rgba(6, 21, 51, 0.45);
    text-align: center;
  }
  .status {
    text-align: center;
  }
  div {
    margin-bottom: 4px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
