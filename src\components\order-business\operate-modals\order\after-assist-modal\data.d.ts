import { SelectProps } from 'ant-design-vue';

export type AfterAssistInfoType = {
  base_info: {
    item_name: string;
    item_num: number;
    sku_name: string;
    freight: string;
    insurance_money: string;
  };
  period_num_list: SelectProps['options'];
  un_period_num_list: SelectProps['options'];
  rental_type: number;
  overdue: string;
  service_type: number;
  work_type_text: string;
  order_id: string;
  total_rental_money: string; // 订单租金
  rental_money: string; //日租金
  deposit_amount: string; // 订单押金
  time_date: string;
  rental_type_text: string;
  renewal_time_start: string; // 续租日期
  renewal_time_end?: string;
  honour_type_text: string;
  diffDay: string; // 续费租期差
  type: number; // 弹窗类型
  is_pass: number; // 1通过 0不通过
  err_msg: string; // 不通过的文案
  most_refund_money: number; // 最大退款金额
  periodOptions: SelectProps['options'];
  buyout_need_pay_data: {
    end_at: number;
    need_pay: number;
    period_num: number;
    start_at: number;
  }[];
  preview_is_pass: number; // 1通过 0不通过
  preview_err_msg: string; // 不通过的文案
};

type TbillArray = {
  period_num: number;
  bill_money: number;
  id: number;
  label: string;
  origin: string;
  /**期数 */
  value: number;
};
export type AfterAssistForm = {
  data: {
    refund_money?: number; // 退款金额
    most_refund_money?: number; // 最大退款金额
    bill_money?: number;
    period_num?: number | number[];
    cbs_version?: string;
    bill_list?: {
      bill_id: number;
      period_num: number;
      pay_money: number;
    }[];
    before_period?: number;
    after_period?: number;
    must_zero?: number; // 是否必须未付
    account?: number; // 是否转换结算值
    bill_status?: string;
    reason_type?: string;
    renewal_time_end?: string;
    renewal_time_start?: string;
    renewal_rental_money?: string;
    renewal_total_rental_money?: string;
    total_rental_money?: number;
    deposit_need_pay?: number;
    pay_money?: number;
    transfer_type?: string;
    bill_ids?: string[];
    transfer_money?: number;
    bill_array?: string; // 修改金额数组json
    bill_array_list?: TbillArray[]; // 修改金额数组
    order_ratio?: number; //期望结算比例
    ori_order_ratio?: number; //期望结算比例
    // 修改寄出方式和运费--start
    send_type: string;
    edit_send_json_data: string;
    // 修改寄出方式和运费--end
  };
  /** 更换帐期 */
  updated_period?: TUpdatedPeriod[];
  feedback: string;
  channel: number;
  type?: number;
  order_id?: string;
  proof_img: string[];
  proof_audio: string[];
};

//更换账单期数
export interface TUpdatedPeriod {
  before_period?: number;
  after_period?: number;
}

export interface IReletTipsData {
  status?: number;
  continue_add_money?: number;
  is_merchant?: number;
  renew_apply_day?: string;
  withhold_day?: string;
  max_renew_total_day?: string;
  max_renew_date?: string;
  bills?: {
    period_num?: string;
    time_start?: string;
    time_end?: string;
    bill_money?: string;
  }[];
}

export interface IFreezeData {
  /**预付账单金额 */
  advance_money: number;
  /**逾期账单金额 */
  bad_bill_money: number;
  buyout_money: number;
  buyout_need_pay: number;
  /**是否可转租金 */
  is_transfer_rent: boolean;
  /**是否可转买断   */
  is_transfer_buyout: boolean;
  /**可转金额 */
  transfer_money: number;
  /**逾期账单 */
  bad_bill: {
    id: string;
    time_start: string;
    period_num: string;
    bill_money: string;
    pay_money: string;
  }[];
  /**最多能扣取金额 */
  max_can_deposit_pay: number;
}

export enum EAssistType {
  /**申请续租 */
  leaseRenewal = 1,
  /**申请退款 */
  refund = 2,
  /**修改账单金额申请 */
  modifyBill = 3,
  /**修改订单总租金 */
  modifyOrderTotalRental = 4,
  /**申请更换账单期数 */
  changeBillPeriod = 5,
  /**修改订单归还状态 */
  modifyOrderReturnStatus = 6,
  /**修改账单支付状态 */
  modifyBillPayStatus = 7,
  /** 冻结押金转支付 */
  freezeDeposit = 11,
  /** 补订单完结申请 */
  supplementOrder = 12,
  /** 申请延长完结 */
  extendOrder = 13,
  /** 结算比例申诉 */
  orderRatioAppeal = 15,
  /** 修改寄出方式 */
  changeSend = 16,
}
