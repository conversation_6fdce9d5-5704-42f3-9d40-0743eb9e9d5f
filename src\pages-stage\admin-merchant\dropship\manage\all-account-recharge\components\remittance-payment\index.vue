<template>
  <a-drawer
    v-model:visible="visible"
    :body-style="{
      paddingBottom: '80px',
      background: 'rgba(240, 241, 243, 1)',
      paddingTop: '0',
      paddingLeft: '0',
      paddingRight: '0',
    }"
    :closable="false"
    placement="right"
    width="640"
    @close="onClose"
  >
    <template #title>
      <div style="display: flex">
        <div style="display: flex; flex-direction: column">
          <span class="text-title">汇款支付</span>
          <span class="text-small">线下银行卡/支付宝汇款支付，需显示提交汇款凭证，此汇款只适用于第三方采购账户的充值</span>
        </div>

        <i
          class="icon-close"
          @click="onClose"
        />
      </div>
    </template>
    <div class="content">
      <div class="content-item">
        <div class="content-item-title">
          收款方信息
        </div>
        <div class="content-item-content">
          <div class="tips">
            需汇款
            <span style="color: #ff4d4f; font-weight: 500">{{
              type === 'platform' ? totalPrice : rechargeConfig?.amount
            }}</span>
            元至以下账户。汇款成功后上传付款凭证信息，审核通过后到账。
          </div>
          <div class="form-box">
            <div class="form-item">
              <div class="label">
                收款方户名：
              </div>
              <div class="value">
                {{ type === 'platform' ? formState.payee_bank_user_name : rechargeConfig?.bank_user_name }}
              </div>
            </div>
            <div class="form-item">
              <div class="label">
                收款方开户行：
              </div>
              <div class="value">
                {{ type === 'platform' ? formState.payee_bank_name : rechargeConfig?.bank_name }}
              </div>
            </div>
            <div class="form-item">
              <div class="label">
                收款方账户：
              </div>
              <div class="value">
                {{ type === 'platform' ? formState.payee_bank_card_num : rechargeConfig?.bank_card_num }}
              </div>
              <CopyOutlined
                style="color: var(--ant-primary-color)"
                @click="
                  copyTradeNo(type === 'platform' ? formState.payee_bank_card_num : rechargeConfig?.bank_card_num)
                "
              />
            </div>
          </div>
        </div>
      </div>
      <div class="content-item">
        <div class="content-item-title">
          付款方信息
        </div>
        <div class="content-item-content">
          <a-form
            ref="formRef"
            layout="vertical"
            :model="formState"
            :rules="rules"
          >
            <a-form-item
              label="付款方式"
              name="pay_type"
            >
              <a-radio-group
                v-model:value="formState.pay_type"
                :options="[
                  {
                    label: '银行卡',
                    value: '11',
                  },
                  {
                    label: '支付宝',
                    value: '10',
                  },
                ]"
              />
            </a-form-item>
            <a-form-item
              v-if="formState.pay_type === '11'"
              name="bank_card_num"
            >
              <template #label>
                <div
                  class="bank-card-label"
                  style="display: flex; align-items: center"
                >
                  <span style="width: 200px">付款银行卡
                    <a-tooltip placement="top">
                      <template #title>
                        <span>第三方采购总账户支持对公账户和对私账户进行汇款支付，平台采购总账户仅支持对公账户进行汇款支付.</span>
                      </template>
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </span>
                  <div
                    v-if="bankOptions.length < 5"
                    class="add-bank-btn"
                    @click="handleAddBankCard"
                  >
                    <i class="add-bank-icon" />
                    添加银行卡
                  </div>
                </div>
              </template>
              <a-select
                v-model:value="formState.bank_card_num"
                placeholder="请选择付款银行卡"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in getBankOptions"
                  :key="item.id"
                  :value="item.bank_card_num"
                >
                  {{ item.bank_card_num }}({{ item.bank_name }})
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              v-if="formState.pay_type === '10'"
              name="alipay_payuser"
            >
              <template #label>
                <div
                  class="bank-card-label"
                  style="display: flex; align-items: center"
                >
                  <span style="width: 200px">支付宝户名</span>
                  <div
                    v-if="alipayOptions.length < 5"
                    class="add-bank-btn"
                    @click="handleAddAlipayCard"
                  >
                    <i class="add-bank-icon" />
                    添加支付宝
                  </div>
                </div>
              </template>
              <a-select
                v-model:value="formState.alipay_payuser"
                placeholder="请选择个人支付宝户名"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in alipayOptions"
                  :key="item.id"
                  :value="item.alipay_payuser"
                >
                  {{ item.alipay_payuser }}({{ item.alipay_account }})
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              label="付款日期"
              name="pay_time"
            >
              <a-date-picker
                v-model:value="formState.pay_time"
                :disabled-date="disabledDate"
                format="YYYY-MM-DD"
                placeholder="请选择付款日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
            <a-form-item
              label="交易流水号"
              name="trade_no"
            >
              <a-input
                v-model:value="formState.trade_no"
                placeholder="请输入交易流水号"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div class="content-item">
        <div class="content-item-title">
          付款凭证
        </div>
        <div class="content-item-content">
          <a-alert
            message=""
            show-icon
            type="warning"
          >
            <template #description>
              <div class="tips-list">
                <span>凭证上传须知：</span>
                <span v-if="formState.pay_type === '11'">1.打款凭证需看见付款时间、付款人、付款卡号、支行名称、金额等信息才予审核通过加入采购账户。</span>
                <span v-else>1.打款凭证需看见付款时间、付款人、支付宝卡号、金额等信息才予审核通过加入采购账户。</span>
                <span>2.如有图片模糊、信息不齐、查询不到流水、图片信息与申请表单信息不符等情况，将驳回申请，需重新修改上传。</span>
              </div>
            </template>
          </a-alert>
          <div class="form-box">
            <a-form
              ref="uploadRef"
              :model="uploadProof"
            >
              <a-form-item
                label="付款凭证"
                name="images"
                :rules="[{ required: true, message: '请上传付款凭证', trigger: 'blur', type: 'array' }]"
              >
                <template #extra>
                  <span>支持.jpg/.png格式，一张申请单仅支持上传一条流水截图</span>
                </template>
                <ImageUpload
                  v-model:value="uploadProof.images"
                  accept=".jpg, .jpeg, .png"
                  class-name="img_upload_wrap"
                  :max="1"
                  quality="o"
                  @change="imageUploadChange"
                />
              </a-form-item>
            </a-form>
          </div>
        </div>
      </div>
    </div>
    <div :style="footerStyle">
      <a-button
        style="margin-right: 8px"
        @click="onClose"
      >
        取 消
      </a-button>
      <template v-if="repeatRechargeConfig.status">
        <a-popconfirm
          v-model:open="repeatRechargeConfig.visible"
          cancel-text="取消"
          ok-text="确定"
          :title="repeatRechargeConfig.msg"
          @confirm="onPay"
        >
          <a-button type="primary">
            确 定
          </a-button>
        </a-popconfirm>
      </template>
      <template v-else>
        <a-button
          type="primary"
          @click="onPay"
        >
          确 定
        </a-button>
      </template>
    </div>

    <AddBankModal
      v-model:visible="addBankModalVisible"
      @ok="getBankData"
    />
    <AddAlipayModal
      v-model:visible="addAlipayModalVisible"
      @ok="getAlipayData"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import { createVNode, nextTick, onMounted, PropType, reactive, ref, UnwrapRef } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { CheckCircleFilled, CopyOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

import ImageUpload from '@/components/image-upload/image-upload.vue';
import { useVModel } from '@/hook';

import type { IBankList, IFooterStyle, IPageData, IRechargeConfig } from '../../data';
import { getBankList, postPayAccount } from '../../services';
import AddAlipayModal from '../add-alipay-modal/index.vue';
import AddBankModal from '../add-bank-modal/index.vue';
import type { FormState, TFormData } from './data';
import { checkRepeatSubmit, getAlipayAccountList, getViewData } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  totalPrice: {
    type: Number,
    default: 0,
  },
  formData: {
    type: Object as PropType<TFormData>,
    default: () => ({}),
  },
  pageData: {
    type: Object as PropType<IPageData>,
    default: () => ({}),
  },
  type: {
    type: String,
    default: '',
  },
  rechargeConfig: {
    type: Object as PropType<IRechargeConfig>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:visible', 'ok']);

const visible = useVModel(props, 'visible', emits);
// 重复充值配置
const repeatRechargeConfig = ref({
  visible: false,
  status: false,
  msg: '',
});
const route = useRoute();

const uploadRef = ref();

const uploadProof = reactive<{ images: string[] }>({
  images: [],
});

const footerStyle: IFooterStyle = {
  position: 'absolute',
  right: 0,
  bottom: 0,
  width: '100%',
  borderTop: '1px solid #e9e9e9',
  padding: '10px 16px',
  background: '#fff',
  textAlign: 'right',
  zIndex: 1,
};

const formState: UnwrapRef<FormState> = reactive({
  //开户人姓名
  bank_user_name: '',
  //开户行
  bank_name: '',
  //开户支行
  bank_branch_name: '',
  //银行卡号
  bank_card_num: null,
  //付款时间
  pay_time: '',
  //交易流水号
  trade_no: '',
  //交易金额
  money: '',
  //收款方开户人姓名
  payee_bank_user_name: '广州人人租供应链服务有限公司',
  //收款方开户行
  payee_bank_name: '上海浦东发展银行股份有限公司广州体育西支行',
  //收款方银行卡号
  payee_bank_card_num: '82230078801200002232',
  //付款凭证
  pay_voucher: '',
  recharge_acc: '',
  rec_config_id: '',
  supplier_id: '',
  pay_type: '11',
  alipay_payuser: undefined,
  alipay_account: '',
});

const rules: Record<string, Rule[]> = {
  bank_card_num: [
    {
      trigger: 'blur',
      required: true,
      message: '付款银行卡不能为空',
    },
  ],
  pay_type: [
    {
      trigger: 'blur',
      required: true,
      message: '付款方式不能为空',
    },
  ],
  pay_time: [
    {
      trigger: 'blur',
      required: true,
      message: '付款日期不能为空',
    },
  ],
  trade_no: [
    {
      trigger: 'blur',
      required: true,
      message: '交易流水号不能为空',
    },
  ],
  alipay_payuser: [
    {
      trigger: 'blur',
      required: true,
      message: '支付宝账户不能为空',
    },
  ],
};

function onClose() {
  visible.value = false;
  formRef.value.resetFields();
  uploadRef.value.resetFields();
  formState.pay_voucher = '';
}

async function getFormData(id: string | number) {
  const res = await getViewData({ id });

  nextTick(() => {
    const {
      bank_user_name,
      bank_name,
      bank_branch_name,
      bank_card_num,
      pay_time_text,
      trade_no,
      money,
      payee_bank_user_name,
      payee_bank_name,
      payee_bank_card_num,
      pay_voucher,
    } = res.data;

    Object.assign(formState, {
      id,
      bank_user_name,
      bank_name,
      bank_branch_name,
      bank_card_num,
      pay_time: pay_time_text,
      trade_no,
      money,
      payee_bank_user_name,
      payee_bank_name,
      payee_bank_card_num,
      pay_voucher,
    });

    uploadProof.images = [pay_voucher];
  });
}

function imageUploadChange(...args: Array<Array<string>>) {
  formState.pay_voucher = args[2] ? args[2].join() : '';
}

const addBankModalVisible = ref(false);
function handleAddBankCard() {
  if (bankOptions.value.length >= 5) {
    message.error('最多添加5张银行卡');
    return;
  }
  addBankModalVisible.value = true;
}
const addAlipayModalVisible = ref(false);
function handleAddAlipayCard() {
  if (bankOptions.value.length >= 5) {
    message.error('最多添加5个支付宝账号');
    return;
  }
  addAlipayModalVisible.value = true;
}

//平台采购总账户 充值按钮 master_account=1
const masterParams = {
  master_account: 1,
};

const bankOptions = ref<IBankList[]>([]);
const getBankOptions = ref<IBankList[]>([]);
const alipayOptions = ref<any[]>([]);
async function getBankData() {
  const res = await (route.query.type === 'platform' ? getBankList(masterParams) : getBankList());
  bankOptions.value = res.data;
  getBankOptions.value = bankOptions.value.filter(item => item.is_hide === 0);
}
async function getAlipayData() {
  const res = await getAlipayAccountList();
  alipayOptions.value = res.data;
}

const formRef = ref();
const showConfirm = () => {
  Modal.confirm({
    title: '提交成功',
    icon: createVNode(CheckCircleFilled, { style: 'color:green;' }),
    content: createVNode(
      'div',
      { style: 'color:rgba(6,21,51,0.65);' },
      '已提交汇款支付，审核完成后将自动入账至采购账户。',
    ),
    okText: '查看审核进度',
    cancelText: '我知道了',
    onOk() {
      emits('ok');
      const currentDomain = window.location.hostname.replace('-vue', '');
      window.open(`https://${currentDomain}/supply-chain-account-corporate-pay/index`);
      onClose();
    },
    onCancel() {
      emits('ok');
      onClose();
    },
    class: 'test',
  });
};
async function onPay() {
  await formRef.value.validate();
  if (!validateUploadProof()) return;
  const params = buildParams();
  if (params.length === 0) {
    message.error('请先输入充值金额！');
    return;
  }
  setFormStateDetails(params);
  const res = await checkRepeatSubmit(formState);
  if (res.data.repeat_status === 1) {
    repeatRechargeConfig.value = {
      visible: true,
      status: true,
      msg: res.message,
    };
  }
  Modal.confirm({
    title: '确认提交',
    content: '请确认实际汇款信息跟上述收款方信息是否一致？',
    async onOk() {
      await postPayAccount(formState);
      showConfirm();
    },
  });
}

function validateUploadProof() {
  if (!uploadProof.images.length) {
    message.error('请上传付款凭证！');
    return false;
  }
  return true;
}

function buildParams() {
  const { type, formData, rechargeConfig } = props;
  const data = props.pageData[type === 'platform' ? 'accounts' : 'accounts_party'];
  let params: any = [];
  if (type === 'platform') {
    params = Object.entries(formData)
      // eslint-disable-next-line
      .filter(([_, value]) => value && Number(value) > 0)
      .map(([key, value]) => {
        const account = data?.accounts.find((item: any) => item.account_id === key);
        return {
          account_id: key,
          amount: value,
          account_name: account?.acc_name || '',
          type: data?.type,
        };
      });
  }

  if (type !== 'platform') {
    const account = data?.accounts.find((item: any) => item.account_id === String(rechargeConfig.account_id));
    params.push({
      account_id: rechargeConfig.account_id as string,
      amount: rechargeConfig.amount as number,
      account_name: account?.acc_name || '',
      type: data?.type,
    });
  }
  return params;
}

function setFormStateDetails(params: any) {
  const detailObj = bankOptions.value.find((item: any) => item.bank_card_num === formState.bank_card_num);
  const detailAlipayObj = alipayOptions.value.find((item: any) => item.alipay_payuser === formState.alipay_payuser);
  formState.bank_user_name = detailObj?.bank_user_name || '';
  formState.bank_name = detailObj?.bank_name || '';
  formState.bank_branch_name = detailObj?.bank_branch_name || '';
  formState.money = props.totalPrice;
  formState.recharge_acc = JSON.stringify(params);
  formState.alipay_account = detailAlipayObj?.alipay_account || '';

  if (props.type !== 'platform') {
    formState.payee_bank_user_name = props.rechargeConfig.bank_user_name || '';
    formState.payee_bank_name = props.rechargeConfig.bank_name || '';
    formState.payee_bank_card_num = props.rechargeConfig.bank_card_num || '';
    formState.rec_config_id = props.rechargeConfig.id as string;
    formState.money = props.rechargeConfig.amount;
    formState.supplier_id = props.rechargeConfig.supplier_id || '';
  }
}

async function copyTradeNo(no: string) {
  try {
    await navigator.clipboard.writeText(no);
    message.success('复制成功！');
  } catch (e) {
    console.log(e);
    const input = document.createElement('input');
    input.value = no;
    document.body.appendChild(input);
    input.select();
    document.execCommand('copy');
    document.body.removeChild(input);
    message.success('复制成功');
  }
}

const disabledDate = (current: Dayjs) => {
  return current && current >= dayjs().endOf('day');
};

onMounted(() => {
  getBankData();
  getAlipayData();
});

defineExpose({
  getFormData,
});
</script>

<style scoped lang="less">
.icon-close {
  width: 16px;
  height: 16px;
  margin-left: auto;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2312/04/m/GLo64CqXUDrn4UooaphM.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

.text-title {
  margin-bottom: 8px;
}

.text-small {
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.content {
  display: flex;
  flex-direction: column;
  padding: 16px;

  .content-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;

    .content-item-title {
      padding-left: 24px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 56px;
      border-bottom: 1px solid rgba(6, 21, 51, 0.06);
    }

    .content-item-content {
      padding: 16px 24px 8px;

      .tips {
        padding-bottom: 16px;
        border-bottom: 1px solid rgba(6, 21, 51, 0.06);
      }

      .form-box {
        display: flex;
        flex-direction: column;
        padding-top: 16px;

        .form-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;

          .label {
            color: rgba(6, 21, 51, 0.65);
          }

          .value {
            margin-right: 4px;
            color: rgba(6, 21, 51, 0.85);
          }
        }
      }
    }
  }
}

.add-bank-btn {
  display: flex;
  align-items: center;
  color: #3777ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;
  .add-bank-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background: url(https://img1.rrzuji.cn/uploads/scheme/2312/06/m/3w0SEb9cDdDQzUpn4pI9.png) no-repeat;
    background-size: 100% 100%;
  }
}

.tips-list {
  display: flex;
  flex-direction: column;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
.bank-card-label {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  width: 580px;
}
</style>
