/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2023-03-15 10:13:33
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-22 10:23:36
 * @FilePath: \lease_digital_intelligence\src\pages\assess-board\composables\use-search.ts
 * @Description: 搜索栏
 */

import type { SearchConditionType } from '../data';
import type { Option } from '@/components/multiple-cascader/data';

import { reactive } from 'vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { ApiGetDistributeDate } from '../service';

type SearchType = {
  condition: SearchConditionType;
  loading: boolean;
  optionMap: Record<string, Option[] | string[]>;
  resetData(): void;
  getOptions: () => void;
};

export default function (): SearchType {
  const conditionDefault = {};
  const condition = cloneDeep(conditionDefault);

  const getOptions = async () => {
    const res = await ApiGetDistributeDate();
    search.optionMap.dateOptions = (res.data || []).map((item: any) => ({
      label: dayjs(item.date + '').format('YYYY-MM-DD'),
      value: item.date,
    }));
    res.data && res.data.length && (search.condition.date = res.data[0].date);
  };

  const search: SearchType = reactive({
    condition,
    loading: false,
    optionMapLoading: false,
    optionMap: {
      dateOptions: [],
    },
    resetData,
    getOptions,
  });

  function resetData() {
    search.condition = cloneDeep(conditionDefault);
  }

  return search;
}
