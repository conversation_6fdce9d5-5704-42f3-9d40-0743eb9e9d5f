<template>
  <a-cascader
    v-model:value="value"
    expand-trigger="click"
    :max-tag-count="3"
    multiple
    :options="options"
    placeholder="请选择"
    :show-search="{ filter }"
    style="min-width: 200px"
  />
</template>

<script setup lang="ts">
import { useVModel } from '@/hook';
import { onMounted, ref } from 'vue';
import type { ShowSearchType } from 'ant-design-vue/es/cascader';
import { categoryGoodsTree } from '../service';

const props = defineProps<{
  modelValue: number[][];
}>();
const emit = defineEmits(['update:modelValue']);

const value = useVModel(props, 'modelValue', emit);
const options = ref<any>([]);
const filter: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
};

const handleGetOptions = async () => {
  const res = await categoryGoodsTree();
  options.value = res.data;
};

onMounted(async () => {
  handleGetOptions();
});
defineExpose({
  handleGetOptions,
});
</script>
