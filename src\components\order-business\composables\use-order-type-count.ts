import { ref } from 'vue';

import type { ITypeCount } from '@/components/order-business/typing';
import { objectFormatKey } from '@/utils/base';

import { getOrderStatusPageNum, getRentHouseOrderStatusPageNum } from './services';
//是否为房屋租赁isRentHouse：true
export function useOrderTypeCount(isRentHouse: boolean = false) {
  /** 态栏的数量订单数量 */
  const typeCount = ref<ITypeCount>({});

  /** 设置状态栏的数量订单数量 */
  function setOrderTypeNum(params: Record<string, any>) {
    params = objectFormatKey(params, [
      ['tabValue', 'type'],
      ['showPageCount', 'show_order_count'],
      ['pageSize'],
      ['current'],
    ]);
    for (const key in params) {
      if (typeof params[key] === 'string') {
        params[key] = decodeURIComponent(params[key].trim());
      }
    }
    const api = isRentHouse ? getRentHouseOrderStatusPageNum : getOrderStatusPageNum;
    return api(params).then(res => {
      typeCount.value = res.data;
    });
  }

  /** 清空状态栏的数量订单数量 */
  function clearOrderTypeNum() {
    typeCount.value = {};
  }

  return {
    typeCount,
    setOrderTypeNum,
    clearOrderTypeNum,
  };
}
