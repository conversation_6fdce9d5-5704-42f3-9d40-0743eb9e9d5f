<template>
  <a-modal
    v-model:visible="bindVisible"
    class="credit-score-modal"
    :mask-closable="false"
    title="信用额度"
    :width="700"
  >
    <a-row class="content">
      <a-col
        v-for="item in dataList"
        :key="item"
        span="12"
      >
        {{ item }}
      </a-col>
    </a-row>
    <template #footer>
      <a-button
        type="primary"
        @click="submit"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  dataList: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

function submit() {
  bindVisible.value = false;
}
</script>

<style scoped lang="less">
.content {
  color: rgb(6, 21, 51);
  border: 1px solid #f0f1f3;

  :deep(.ant-col) {
    padding: 6px;
  }

  :deep(.ant-col:nth-child(n + 2)) {
    border-top: 1px solid #f0f1f3;
  }

  :deep(.ant-col:nth-child(2n)) {
    border-left: 1px solid #f0f1f3;
  }
}
</style>
