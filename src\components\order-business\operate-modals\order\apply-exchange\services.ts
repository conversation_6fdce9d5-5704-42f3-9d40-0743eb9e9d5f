// 寄回地址信息
import { GET } from '@/services/api';

export function getWarehouseList() {
  return GET('/super/warehouse/after-sale-work/confirm-warehouse-list');
}

//获取地址列表
export function getAddressApi(warehouse_id) {
  return GET('/super/warehouse/warehouse/warehouse-address', { warehouse_id, address_type: 1 });
}

//售后原因下拉
export function getAfterSaleReasonOptions() {
  return GET('/warehouse/after-sale-work/reason-options');
}
