import { computed, ComputedRef, Ref, ref } from 'vue';
import { message } from 'ant-design-vue';
import { isEmptyValue } from '@rrzu/utils';
import { nanoid } from 'nanoid';

import { deepClone } from '@/utils/base';

import { TFactorConditionalRulesProps, TRulesInfo } from '../data.d';

type TRulesFunction = {
  rulesInfo: Ref<TRulesInfo | undefined>;
  level: ComputedRef<number>;
  isLogic: (val: TRulesInfo) => boolean;
  getTempRules: () => TRulesInfo;
  addRules: (item: TRulesInfo) => void;
  addChildRules: (item: TRulesInfo) => void;
  delRules: (item: TRulesInfo, index: number) => void;
  getData: () => Promise<any>;
};

export function useRulesHandle(props: TFactorConditionalRulesProps): TRulesFunction {
  const rulesInfo = ref<TRulesInfo>();

  const level = computed(() => {
    return Number(props?.levelSetting) ? Number(props.levelSetting) : 1;
  });

  function isLogic(val: TRulesInfo) {
    return val.relationship === 'logic';
  }

  function getTempRules(level?: number) {
    return {
      _id: nanoid(8),
      relationship: 'condition',
      rules: {},
      level: level ? level : 0,
    } as TRulesInfo;
  }

  function addRules(item: TRulesInfo) {
    const tempRules = getTempRules(item.level + 1);
    if (props.zeroLevelMax && item.level === 0 && item.rules.length === props.zeroLevelMax) {
      return message.warn('最多添加' + props.zeroLevelMax + '个条件');
    }
    item.rules.push(tempRules);
  }

  function addChildRules(item: TRulesInfo) {
    if (item.relationship === 'condition') {
      const tempRules = getTempRules(item.level + 1);
      const temp = deepClone(item.rules);
      const origin = { ...getTempRules(item.level + 1), rules: temp };
      item.relation = 'and';
      item.relationship = 'logic';
      item._id = nanoid(8);
      item.rules = [origin, tempRules];
    } else {
      addRules(item);
    }
  }

  function delRules(item: TRulesInfo, index: number) {
    item.rules.splice(index, 1);
    if (item.relationship === 'logic' && Array.isArray(item.rules) && item.rules.length === 1) {
      const tempRulesData = deepClone(item.rules[0]);
      if (
        tempRulesData.relationship === 'logic' &&
        Array.isArray(tempRulesData.rules) &&
        tempRulesData.rules.length > 1
      ) {
        item.relationship = 'logic';
        item.relation = tempRulesData.relation;
        item._id = nanoid(8);
        item.rules = tempRulesData.rules;
      } else {
        item.relationship = 'condition';
        delete item.relation;
        item._id = nanoid(8);
        item.rules = tempRulesData.rules;
      }
    }
  }

  function allHave(rules: any) {
    return (
      rules &&
      Object.keys(rules).length &&
      Object.keys(rules).every((key: any) => {
        return !isEmptyValue(rules[key]);
      })
    );
  }

  function checkData(rules: any): boolean {
    if (Array.isArray(rules)) {
      return rules.some((item: any) => checkData(item.rules));
    } else {
      return !allHave(rules);
    }
  }

  function getData() {
    return new Promise((resolve, reject) => {
      const falge = checkData(rulesInfo.value?.rules);
      if (falge) {
        message.error('请填写完整');
        reject();
      }
      resolve(deepClone(rulesInfo.value));
    });
  }

  return {
    rulesInfo,
    delRules,
    addRules,
    isLogic,
    getData,
    getTempRules,
    addChildRules,
    level,
  };
}
