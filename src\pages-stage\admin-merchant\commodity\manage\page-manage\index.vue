<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px' }"
    title="页面管理"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="toPushPage('/shop/page/create')"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        新建页面
      </a-button>
    </template>

    <div class="search-wrap">
      <form-create
        ref="searchFormRef"
        v-model:value="searchParams"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="getTableList"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            type="default"
            @click="handleReset"
          >
            重置
          </a-button>
        </template>
      </form-create>
    </div>
    <div class="table-wrapper">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :pagination="page"
        @change="tableChange"
      >
        <template #headerCell="{ title, column }">
          <template v-if="column.key === 'meta_keywords'">
            <a-space :size="4">
              {{ title }}
              <a-tooltip placement="bottom">
                <template #title>
                  <span>百度SEO，搜索关键字最好填上，用户在百度搜索的时候会凭你的关键字搜索到您的旺铺</span>
                </template>
                <InfoCircleOutlined />
              </a-tooltip>
            </a-space>
          </template>
          <template v-if="column.key === 'meta_desription'">
            <a-space :size="4">
              {{ title }}
              <a-tooltip placement="bottom">
                <template #title>
                  <span>搜索描述：百度SEO，搜索关描述最好填上，用户在百度搜索的时候会凭你的描述关键字搜索到您的旺铺</span>
                </template>
                <InfoCircleOutlined />
              </a-tooltip>
            </a-space>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'shop_color'">
            <div
              class="color"
              :style="{ background: '#' + record.shop_color, width: '50px', height: '50px' }"
            />
          </template>
          <template v-if="column.key === 'status'">
            <a-space :size="4">
              <div :class="['dot', statusParams[record.status].color]" />
              {{ statusParams[record.status].text }}
            </a-space>
          </template>
          <template v-if="column.key === 'operation'">
            <a-button
              class="r-btn-link"
              type="link"
              @click.stop="handleView(record)"
            >
              预览
            </a-button>
            <a-button
              class="r-btn-link"
              type="link"
              @click.stop="toPushPage(`/shop/page/update?id=${record.id}`)"
            >
              修改
            </a-button>
            <a-button
              danger
              :disabled="record.is_home_page"
              style="padding-left: 0 !important"
              type="link"
              @click.stop="handleDelete(record)"
            >
              删除
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </layout-shops-page>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { InfoCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import { FormCreate } from '@/components/form-create';
import { useTable } from '@/hook/component/use-table';

import { columns, searchFormGroup } from './config';
import { deletePage } from './service';
/**
 * 检索表单
 */
const searchParams = reactive({
  title: '',
  meta_desription: '',
  meta_keywords: '',
  reason: '',
  status: undefined,
  created_at: [],
});

// 检索表单实例
const searchFormRef = ref();

const handleReset = () => {
  const formRef = searchFormRef.value.getFormRef();
  formRef.resetFields();
};

const { query } = useRoute();
const { origin } = query;

const frontDomain = ref<string>('');

const handleView = (record: any) => {
  window.open(`${frontDomain.value}/shop-page/${record.id}`);
};

const statusParams = {
  1: {
    text: '发布中',
    color: 'success',
  },
  2: {
    text: '审核失败',
    color: 'danger',
  },
  3: {
    text: '审核中',
    color: 'orange',
  },
};

// /distributor/index
const toPushPage = (url: string) => {
  const b_url = `${origin}${url}`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: b_url,
    },
    '*',
  );
};

const { list, listLoading, page, getTableList, tableChange } = useTable({
  url: '/shop/page/index-inf',
  formatHandle: res => {
    frontDomain.value = res.data.frontDomain;
    return res.data.data.list;
  },
  searchForm: {
    TblShopPageSearch: searchParams,
  },
});
getTableList();

const handleDelete = async (record: any) => {
  Modal.confirm({
    title: '是否删除该页面？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      const res = await deletePage({
        id: record.id,
      });
      message.success(res.message);
      getTableList();
    },
  });
};
</script>
<style lang="less" scoped>
.search-wrap {
  margin: 0 -8px;
  :deep(.ant-form) {
    display: flex;
    flex-wrap: wrap;
  }
  :deep(.ant-form-item) {
    flex-basis: 25%;
    margin: 0 0 28px 0;
    padding: 0 8px;
  }
}
.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  &.default {
    background: rgba(6, 21, 51, 0.15);
  }
  &.success {
    background: #52c41a;
  }

  &.orange {
    background: #fff7e6;
  }

  &.danger {
    background: #cf1322;
  }
}
</style>
