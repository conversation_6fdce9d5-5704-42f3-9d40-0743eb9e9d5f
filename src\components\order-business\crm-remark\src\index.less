.remark-component {
  .wrap-tow-line {
    display: -webkit-box;
    overflow: hidden;
    white-space: normal !important;
    text-overflow: ellipsis;
    word-wrap: break-word;
    word-break: break-all;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .mark-record {
    &.vertical {
      width: 200px;

      .last-remark {
        width: 200px;
        color: rgba(6, 21, 51, 0.65);
        line-height: 22px;
        text-decoration: underline;
        cursor: pointer;
      }

      .time-by {
        margin-bottom: 8px;
        color: rgba(6, 21, 51, 0.25);
        font-size: 12px;
        line-height: 20px;

        span:nth-child(1) {
          margin-right: 8px;
        }
      }

      .vertical-remark-num {
        color: #00c1b6;
        text-decoration: underline;
        cursor: pointer;
      }
    }

    &.horizontal {
      display: flex;

      .last-remark {
        max-width: 200px;
        overflow: hidden;
        color: #ff4a57;
        white-space: nowrap;
        text-decoration: underline;
        text-overflow: ellipsis;
        cursor: pointer;
      }

      .horizontal-remark-num {
        min-width: 75px;
        margin: 0 24px;
        color: #00c1b6;
        cursor: pointer;
      }

      .time-by {
        color: rgba(6, 21, 51, 0.45);
        font-size: 12px;
        line-height: 20px;

        span:nth-child(1) {
          margin-right: 8px;
        }
      }
    }
  }

  .small-text {
    margin-left: 8px;
    color: rgba(6, 21, 51, 0.65);
    text-decoration: underline;
    cursor: pointer;
  }

  .block-remark {
    flex-wrap: wrap;

    .left-content {
      width: 100%;
      padding-right: 80px;
      cursor: pointer;

      .block-remark-text {
        color: rgba(6, 21, 51, 0.85);
        white-space: pre-wrap;
        text-decoration: underline;
        word-break: break-all;

        &:hover {
          color: #3777ff;
        }
      }

      .block-time-by {
        color: rgba(6, 21, 51, 0.45);
      }
    }

    .no-allow-add {
      width: 100%;
      padding-right: 80px;
      cursor: auto;

      .block-remark-text {
        color: rgba(6, 21, 51, 0.85);
        white-space: pre-wrap;
        text-decoration: auto;
        word-break: break-all;

        &:hover {
          color: rgba(6, 21, 51, 0.85);
        }
      }

      .block-time-by {
        color: rgba(6, 21, 51, 0.45);
      }
    }

    .right-count {
      margin-left: 6px;
      color: rgba(6, 21, 51, 0.45);
      text-decoration: underline;
      cursor: pointer;

      &:hover {
        color: #3777ff;
      }
    }
  }
}

.remark-list {
  max-height: 639px;
  overflow-y: auto;

  .remark-item {
    padding: 16px;
    font-weight: 400;
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: PingFangSC-Regular, PingFang SC;
    background: #f5f7fa;
    border-radius: 4px;

    .remark-text {
      margin-bottom: 8px;
      color: rgba(6, 21, 51, 0.65);
      font-size: 14px;
      line-height: 22px;
    }

    .divider-class {
      margin: 8px 0;
    }

    .created-name,
    .created-time {
      color: rgba(6, 21, 51, 0.45);
      font-size: 14px;
      line-height: 22px;
    }

    .created-name {
      margin-right: 16px;
    }
  }

  .remark-item + .remark-item {
    margin-top: 16px;
  }
}
