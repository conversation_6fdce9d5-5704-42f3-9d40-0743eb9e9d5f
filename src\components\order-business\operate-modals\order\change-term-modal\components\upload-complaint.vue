<template>
  <a-upload-dragger
    accept=".jpg, .png, .mp3, .wav, .ogg"
    :before-upload="handleBeforeUpload"
    :disabled="!customLoading"
    :max-count="4"
    name="uploadfile"
    :show-upload-list="false"
  >
    <p>
      <inbox-outlined style="font-size: 48px" />
    </p>
    <p>选择文件</p>
  </a-upload-dragger>

  <div v-if="customFileList.length > 0">
    <div
      v-for="(item, index) in customFileList"
      :key="index"
      class="custom-flex"
    >
      <div class="custom-list-item">
        <a
          :href="item.url"
          target="_blank"
        >{{ item.name }}</a>
      </div>
      <delete-outlined
        style="cursor: pointer"
        @click="deleteCustomFile(item.url)"
      />
    </div>
  </div>
  <div
    v-if="customFile"
    class="custom-flex"
    style="margin-top: 6px"
  >
    <div class="text">
      {{ customFile.name }}
    </div>
    <a-button
      class="button"
      :loading="!customLoading"
      size="small"
      type="primary"
      @click="handleCustomUpload"
    >
      <span v-if="customLoading">上传文件</span>
      <span v-else>上传中</span>
    </a-button>
  </div>

  <div style="margin-top: 5px; color: rgba(6, 21, 51, 0.45)">
    建议增加「用户协商」或「其他」图片凭证以提高通过率。支持上传jpg、png、mp3、wav、ogg附件，最多可上传4张图片以及1个附件，图片大小建议8M以内，附件大小建议30M以内。
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { DeleteOutlined, InboxOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';

import { commonFile, commonImage, sliceFileUpload } from '../services';

const props = defineProps({
  proofImg: {
    type: Array,
    default: () => [],
  },
  proofAudio: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:proofImg', 'update:proofAudio']);

const bindProofImg = useVModel(props, 'proofImg', emit);
const bindProofAudio = useVModel(props, 'proofAudio', emit);

/** 凭证文件 */
const customFile = ref<any>();
const imageNum = ref<number>(0);
const videoNum = ref<number>(0);
const customLoading = ref<boolean>(true);
const customFileList = ref<{ url: string; name: string }[]>([]);

function handleBeforeUpload(file: any) {
  const types = file.type.split('/');
  const size = file.size;
  if (types[0] !== 'audio' && types[0] !== 'application') {
    if (imageNum.value >= 4) {
      message.error('上传的文件数量不得超过4张');
      return false;
    }
    if (size / 1048576 > 5) {
      message.error('上传的图片大小不得超过5M');
      return false;
    }
    customFile.value = file;
    return false;
  } else {
    if (videoNum.value >= 1) {
      message.error('上传的音频数量不得超过一个');
      return false;
    }
    if (size / 1048576 > 30) {
      message.error('上传的音频大小不得超过30M');
      return false;
      a;
    }
    customFile.value = file;
    return false;
  }
}

const xhrSendImage = (file: any) => {
  customLoading.value = false;
  const fd = new FormData();
  fd.append('uploadfile', file);
  commonImage(fd).then(res => {
    if (res.data.status === 1) {
      const img = res.data.imgOssServer + res.data.url;
      customLoading.value = true;
      bindProofImg.value.push(img);
      customFileList.value.push({
        url: img,
        name: customFile.value.name,
      });
      customFile.value = '';
      message.success('上传成功');
    } else {
      message.error('上传失败');
    }
  });
};

const xhrSend = (fd: any, cb: any) => {
  customLoading.value = false;
  sliceFileUpload(fd)
    .then(res => {
      if (res.data.status === 0) {
        cb && cb();
      } else {
        customLoading.value = true;
        return message.error('上传失败，请重试');
      }
    })
    .catch(() => {
      customLoading.value = true;
    });
};

const xhrSendMerge = (fd: any) => {
  commonFile(fd).then(res => {
    customLoading.value = true;
    if (res.data.status === 0) {
      videoNum.value++;
      bindProofAudio.value.push(res.data.oss_url);
      customFileList.value.push({
        url: res.data.oss_url,
        name: customFile.value.name,
      });
      customFile.value = '';
      message.success('上传成功');
    } else {
      message.error('上传失败');
    }
  });
};

const submitUpload = () => {
  const chunkSize = 2 * 1024 * 1024;
  const chunks: any[] = [],
    token = +new Date(),
    name = customFile.value.name.substring(0, customFile.value.name.indexOf('.')),
    ext = customFile.value.name.substring(customFile.value.name.indexOf('.') + 1);
  let chunkCount = 0;
  let sendChunkCount = 0;

  if (!customFile.value) {
    message.warn('请上传文件');
    return;
  }

  //拆分文件
  if (customFile.value.size > chunkSize) {
    //拆分文件
    let start = 0;
    let end = 0;
    while (true) {
      end += chunkSize;
      const blob = customFile.value.slice(start, end);
      start += chunkSize;
      if (!blob.size) {
        //拆分结束
        break;
      }
      chunks.push(blob);
    }
  } else {
    chunks.push(customFile.value.slice(0));
  }
  chunkCount = chunks.length;
  /**
   * 唯一标识
   * 文件名字
   * 总片数
   * 当前索引
   */
  //没有做并发限制，较大文件导致并发过多，tcp 链接被占光 ，需要做下并发控制，比如只有4个在请求在发送
  for (let i = 0; i < chunkCount; i++) {
    const fd = new FormData(); //构造FormData对象
    const file_name = name + token;
    fd.append('filename', file_name);
    fd.append('file', chunks[i]);
    fd.append('blob_num', i + '');
    fd.append('total_blob_num', chunkCount + '');

    xhrSend(fd, () => {
      sendChunkCount += 1;
      if (sendChunkCount === chunkCount) {
        const resultForm = new FormData();
        resultForm.append('filename', file_name);
        resultForm.append('type', ext);
        resultForm.append('total_blob_num', chunkCount + '');
        xhrSendMerge(resultForm);
      }
    });
  }
};

const handleCustomUpload = () => {
  if (!customLoading.value) return;
  const types = customFile.value.type.split('/');
  if (types[0] !== 'audio' && types[0] !== 'application') {
    xhrSendImage(customFile.value);
  } else {
    submitUpload();
  }
};

const deleteCustomFile = (fileUrl: string) => {
  const img_index = bindProofImg.value.indexOf(fileUrl);
  const video_index = bindProofAudio.value.indexOf(fileUrl);
  const cus_index = customFileList.value.findIndex((item: any) => item.url === fileUrl);
  if (img_index >= 0) {
    imageNum.value--;
    bindProofImg.value.splice(img_index, 1);
  }
  if (video_index >= 0) {
    videoNum.value--;
    bindProofAudio.value.splice(video_index, 1);
  }
  if (cus_index >= 0) {
    customFileList.value.splice(cus_index, 1);
  }
};
</script>

<style scoped lang="less">
.custom-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.custom-list-item {
  width: 500px;
  margin: 6px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
