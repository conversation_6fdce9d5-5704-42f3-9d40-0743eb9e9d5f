import { message } from 'ant-design-vue';
import { postAddAddress } from '../services';
import { defineComponent, reactive, ref, toRaw } from 'vue';
import { useButtonModal } from '@/pages-stage/stock/manage/device/composables/use-button-modal';

import type { Ref, UnwrapRef } from 'vue';
import type { ValidateErrorEntity } from 'ant-design-vue/lib/form/interface';
import type { IUseButtonModal } from '@/pages-stage/stock/manage/device/composables/use-button-modal';

interface ISetupData extends Omit<IUseButtonModal, 'request'> {
  confirmAddAddress: () => Promise<void>;
  formState: UnwrapRef<IFormState>;
  formRef: Ref<any>;
  rules: any;
  reset: () => void;
}

export interface IFormState {
  phone: string;
  name: string;
  address: string;
}

export default defineComponent({
  emits: ['refresh'],
  setup(): ISetupData {
    const { visible, loading, open, close, request } = useButtonModal();

    const formRef = ref();
    const formState: UnwrapRef<IFormState> = reactive({
      phone: '',
      name: '',
      address: '',
    });
    const rules = {
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
      ],
      name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
    };

    const reset = () => {
      Object.assign(formState, {
        phone: '',
        name: '',
        address: '',
      });
    };

    const confirmAddAddress = () => {
      return formRef.value
        .validate()
        .then(() => request(postAddAddress, toRaw(formState)))
        .then((res: any) => {
          message.success(res.message);
          reset();
          close();
        })
        .catch((error: ValidateErrorEntity<IFormState>) => {
          console.log('error', error);
        });
    };

    return {
      visible,
      loading,
      open,
      close,

      confirmAddAddress,

      formRef,
      formState,
      rules,
      reset,
    };
  },
  render() {
    return (
      <>
        <a-button type="primary" onClick={this.open}>
          {this.$slots.default && this.$slots.default()}
        </a-button>
        <a-modal
          vModel={[this.visible, 'visible']}
          okText="确定"
          cancelText="取消"
          destroyOnClose={true}
          title="添加寄回地址"
          onCancel={() => {
            this.reset();
            this.close();
          }}
          onOk={this.confirmAddAddress}
          confirmLoading={this.loading}
        >
          <a-form
            ref="formRef"
            model={this.formState}
            rules={this.rules}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
          >
            <a-form-item label="姓名" name="name">
              <a-input vModel={[this.formState.name, 'value']} placeholder="请输入姓名" />
            </a-form-item>
            <a-form-item label="手机号" name="phone">
              <a-input vModel={[this.formState.phone, 'value']} placeholder="请输入手机号" />
            </a-form-item>
            <a-form-item label="地址" name="address">
              <a-input vModel={[this.formState.address, 'value']} placeholder="请输入地址" />
            </a-form-item>
          </a-form>
        </a-modal>
      </>
    );
  },
});
