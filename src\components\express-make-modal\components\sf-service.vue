<template>
  <a-form
    ref="formRef"
    layout="vertical"
    :model="formState"
  >
    <a-form-item
      label="上门取件时间"
      name="times"
      :rules="{ required: true, message: '请选择上门取件时间！' }"
    >
      <a-cascader
        v-model:value="formState.times"
        :options="timesOptions"
        placeholder="请选择"
      />
    </a-form-item>
    <a-form-item
      label="上门取件地址"
      name="sf_address"
      :rules="{ required: true, message: '请选择上门取件地址' }"
    >
      <a-select
        v-model:value="formState.sf_address"
        :options="collectOptions"
        placeholder="请选择取件地址"
      />
    </a-form-item>
    <a-form-item
      v-if="receiveAddressRequire"
      label="收件地址"
      name="receive_address"
      :rules="{ required: true, message: '请完善收件地址', validator: receiveAddressValidator, trigger: 'change' }"
    >
      <a-form-item-rest>
        <ReceiveAddress v-model:value="formState.receive_address" />
      </a-form-item-rest>
    </a-form-item>
    <a-form-item
      v-if="sfExpressTypeOptions.length"
      name="sfServiceType"
    >
      <template #label>
        服务类型
        <InfoCircleOutlined
          :style="{
            color: '#aeb1bb',
            fontSize: '14px',
            marginLeft: '5px',
          }"
          @click="onOpen"
        />
      </template>
      <a-radio-group
        v-model:value="formState.sfServiceType"
        style="margin-top: 6px"
      >
        <a-radio
          v-for="(item, index) in sfExpressTypeOptions"
          :key="index"
          :value="item.value"
        >
          <div class="radio-text">
            <div
              v-if="item.tips"
              class="tips-text"
            >
              {{ item.tips }}
            </div>
            <div
              v-else-if="item.value === 255 && !item.tips"
              class="tips-text"
            >
              限时8折
            </div>
            <div class="radio-label">
              {{ item.label }}
            </div>
            <div class="radio-label-text">
              {{ item.value === 2 ? '首重(1.0kg)12.0元起' : '首重(20kg)40元起' }}
            </div>
          </div>
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      v-if="formState.serve?.includes('is_insure')"
      label="保价金额"
      name="goods_cost"
      :rules="[{ required: true, message: '请输入保价金额' }]"
    >
      <a-input-number
        v-model:value="formState.goods_cost"
        placeholder="请输入保价金额"
        style="width: 100%"
      />
    </a-form-item>
    <a-form-item
      v-if="serveOptions.length"
      label="已选服务"
      name="serve"
    >
      <a-checkbox-group v-model:value="formState.serve">
        <div
          v-for="(item, index) in serveOptions"
          :key="index"
        >
          <a-checkbox :value="item.value">
            {{ item.label }}
          </a-checkbox>
          <a-button
            v-if="item.hrefText"
            :href="item.href"
            style="padding: 0"
            target="_blank"
            type="link"
          >
            {{ `(${item.hrefText})` }}
          </a-button>
        </div>
      </a-checkbox-group>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { onMounted, type PropType,reactive, ref, unref } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment';

import type { IReceiveAddressProps,IServeOption,ISFExpressTypeOption,ISFFormState, IUseOptionsProps } from '../data';
import ReceiveAddress from './receive-address.vue';

const props = defineProps({
  // 上门取件地址
  collectOptions: {
    type: Array as PropType<{label: string, value: string}[]>,
    default: () => ([])
  },
  // 取件地址（省市区、详细地址）
  addresseeInfo: {
    type: Object as PropType<Partial<IReceiveAddressProps>>,
    default: () => ({})
  },
  //是否需要收件地址
  receiveAddressRequire: {
    type: Boolean,
    default:true,
  },
  // 可自定义渲染【服务类型、已选服务】
  useOptions: {
    type: Object as PropType<Partial<IUseOptionsProps>>,
    default: () => ({})
  },
})

const formRef = ref<FormInstance>();

const formState = reactive<ISFFormState>({
  times: [], // 上门取件时间
  sf_address: null,// 取件地址
  receive_address: {...props.addresseeInfo}, // 收件地址
  sfServiceType: null, // 服务类型
  serve: [], // 已选服务
})
// 上门取件时间级联
const timesOptions = createTimeOptions();
// 已选服务
const serveOptions = ref<IServeOption[]>([
  {label: '微签', value: 'is_sign', href: '/pdftpl/sf_service_sign.pdf', hrefText: '微签服务类型'},
  {label: '保价', value:'is_insure', href: '/img/sf_service_insure.png', hrefText: '保价规则'},
  {label: '包装', value: 'is_packed', href: 'https://www.sf-express.com/chn/sc/express/installation', hrefText: '包装费用说明'},
  {label: '纸质签回单', value: 'is_sign_paper'}
])

// 服务类型
const sfExpressTypeOptions = ref<ISFExpressTypeOption[]>([]);


function createTimeOptions(start = 8, end = 19) {
  // 生成配置信息
  let time: any[] = [];
  for (let i = start; i < end; i++) {
    time.push({
      id: i,
      value: `${i < 10 ? '0' + i : i}:00:00`,
      label: `${i < 10 ? '0' + i : i}:00-${i + 1 < 10 ? '0' + (i + 1) : i + 1}:00`,
    });
  }
  const options = ['今天', '明天', '后天'].map((item, index) => {
    return {
      value: moment().add(index, 'days').format('YYYY-MM-DD'),
      label: item,
      children: time,
    };
  });
  const todayHour = moment().hour();
  // 当前时间是否大于最大时间 ？ 移除今天 : 过滤今天时间
  todayHour >= end ? options.splice(0, 1) : (options[0].children = time.filter(item => item.id > todayHour));
  return options;
}

function onOpen() { window.open('https://img1.rrzuji.cn/uploads/scheme/2311/10/m/0xbBw2Bav72NiGMy5RDK.png') };

function receiveAddressValidator() {
  const { receive_address } = formState;
  const fields = ['province', 'city', 'area', 'address'];
  const hasEmpty = fields.some(key => !receive_address[key]);
  if (hasEmpty) {
    return Promise.reject('请完善收件地址信息')
  }
  return Promise.resolve();
}

function validate() {
  return formRef.value?.validate()
}

onMounted(async () => {
  const { useServiceType, useServe} = props.useOptions;
  if (useServe) {
    serveOptions.value = useServe(unref(serveOptions.value));
  }
  if (useServiceType) {
    sfExpressTypeOptions.value = await useServiceType() || [];
    if (sfExpressTypeOptions.value.length) {
      formState.sfServiceType = sfExpressTypeOptions.value[0].value
    }
  }
})


defineExpose({
  validate
})
</script>

<style>
.radio-text {
  position: relative;

  .tips-text {
    position: absolute;
    top: -13px;
    right: 16px;
    display: flex;
    justify-content: center;
    height: 17px;
    padding: 0 4px;
    color: #fff;
    font-size: 10px;
    text-align: center;
    background: #ff4d4f;
    border-radius: 2px;
  }
  .radio-label-text {
    margin-top: 4px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
