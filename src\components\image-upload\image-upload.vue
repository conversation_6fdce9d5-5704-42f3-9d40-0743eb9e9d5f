<template>
  <div
    class="img_upload_wrap"
    style="font-size: 0; line-height: 0"
  >
    <a-upload
      v-if="!locked"
      :accept="accept"
      :capture="null"
      :class="className"
      :custom-request="uploadAction"
      :disabled="disabled"
      :file-list="fileList"
      :headers="{ 'X-Requested-With': null }"
      list-type="picture-card"
      :multiple="multiple"
      name="uploadfile"
      :show="show"
      :show-upload-list="showUploadList"
      :with-credentials="true"
      @change="handleChange"
      @download="handleDown"
      @preview="handlePreview"
      @remove="handleRemove"
    >
      <div v-if="lockedTypeMaxLimit && !show">
        <PlusOutlined v-if="uploadIcon === 'plusOutlined'" />
        <UploadOutlined v-else-if="uploadIcon === 'uploadOutlined'" />
        <div
          v-if="showUploadText"
          class="ant-upload-text"
        >
          {{ uploadText }}
        </div>
      </div>
    </a-upload>

    <div
      v-else
      class="lock_container"
    >
      <div class="origin-img">
        <a-image
          v-for="item in fileList"
          :key="item.uid"
          :preview="true"
          :src="item.url"
        />
      </div>
      <a-upload
        :accept="accept"
        :capture="null"
        :class="[className]"
        :custom-request="uploadAction"
        :disabled="disabled"
        :file-list="extralFileList"
        :headers="{ 'X-Requested-With': null }"
        list-type="picture-card"
        :multiple="multiple"
        name="uploadfile"
        :show="show"
        :show-upload-list="showUploadList"
        :with-credentials="true"
        @change="handleChange"
        @download="handleDown"
        @preview="handlePreview"
      >
        <div v-if="!show">
          <PlusOutlined v-if="uploadIcon === 'plusOutlined'" />
          <UploadOutlined v-else-if="uploadIcon === 'uploadOutlined'" />
          <div class="ant-upload-text">
            {{ uploadText }}
          </div>
        </div>
      </a-upload>
    </div>
  </div>
</template>
<script>
/* eslint-disable no-param-reassign */
import { message, Modal } from 'ant-design-vue';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons-vue';

import { generateAndUploadImage2Oss } from '@/utils/oss-helper';

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  components: {
    PlusOutlined,
    UploadOutlined,
  },
  props: {
    //是否限制编辑初始加载的图片
    locked: {
      type: Boolean,
      default: false,
    },
    // 传入图片列表，支持双向绑定，列表例子 ['handleDown.png', 'bbb.png']
    value: {
      type: Array,
      default() {
        return [];
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    // action: {
    //   type: String,
    //   required: true,
    // },
    action: {
      type: String,
      default: 'site',
    },
    lockedMax: {
      type: [Number, String],
      default: 5,
    },
    max: {
      type: [Number, String],
      default: 5,
    },
    pathName: {
      type: String,
      default: 'imageUpload',
    },
    className: {
      type: String,
      default: '',
    },
    uploadText: {
      type: String,
      default: '上传图片',
    },
    onError: {
      type: Function,
      default: res => {
        // 人人组件接口错误是msg
        message.warning(res.msg || '抱歉，上传时发生未知错误，请重试');
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    show: {
      type: Boolean,
      default: false,
    },
    showUploadList: {
      type: [Boolean, Object],
      default: true,
    },
    // 图片质量，为空的话 默认为m
    // 只有三种类型 o m s
    quality: {
      type: String,
      default: '',
    },
    isDelCaptureAttr: {
      type: Boolean,
      default: false,
    },
    // 上传图标，可选值：plusOutlined、uploadOutlined
    uploadIcon: {
      type: String,
      default: 'plusOutlined',
    },
    showUploadText: {
      type: Boolean,
      default: () => true,
    },
    removeConfirm: {
      type: Boolean,
      default: () => false,
    },
    accept: {
      type: String,
      default: '.jpg, .jpeg, .png, .gif',
    },
    downName: {
      type: String,
      default: '',
    },
  },
  emits: ['change', 'update:value', 'preview'],
  data() {
    return {
      originFileList: [],
      fileList: [],
      path: [],
      extralFileList: [],
      hasChange: false,
    };
  },
  computed: {
    lockedTypeMaxLimit() {
      return this.fileList.length >= this.max ? false : true;
    },
  },
  watch: {
    value: {
      handler(value, oldVal) {
        if (JSON.stringify(value) === JSON.stringify(oldVal)) return;
        if (this.locked) {
          if (!this.hasChange) {
            //上锁数据
            this.fileList = !value.lockedValue
              ? []
              : value.lockedValue.map((url, index) => ({
                  url,
                  status: 'done',
                  uid: index,
                  name: url,
                  path: this.path[index],
                }));
          }

          //非上锁数据
          this.extralFileList = !value.value
            ? []
            : value.value.map((url, index) => ({
                url,
                status: 'done',
                uid: index,
                name: url,
                path: this.path[index],
              }));
        } else {
          this.fileList = !value
            ? []
            : value.map((url, index) => ({
                url,
                status: 'done',
                uid: index,
                name: url,
                path: this.path[index],
              }));
        }
      },
      immediate: true, // 立即以表达式的当前值触发回调
    },
  },
  mounted() {
    this.hasChange = false;
    // 删除input上的capture属性
    if (this.isDelCaptureAttr) {
      const el = document.querySelector('.img_upload_wrap #form_item_cover');
      el.removeAttribute('capture');
    }
  },
  methods: {
    /**
     *  自定义上传方法
     */
    async uploadAction(options) {
      try {
        // 阿里云压缩上传
        const res = await generateAndUploadImage2Oss(
          options.file,
          {
            progress: percent => options.onProgress({ percent: percent * 100 }),
          },
          this.action,
        );
        options.onSuccess({
          domain: '',
          imageUrls: { m: res.m, o: res.o, s: res.s },
          imgOssServer: res.oss,
          status: 1,
          url: res.mPath,
        });
      } catch (error) {
        // this.onError({ msg: '图片数据可能有问题,请选择其他图片', status: 0 });
        options.onError('图片数据可能有问题,请选择其他图片');
      }
    },
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      let previewImage = '';
      if (this.locked) {
        previewImage = this.extralFileList.map(item => item.url);
      } else {
        previewImage = this.fileList.map(item => item.url);
      }
      this.$preview.open({
        imgs: previewImage,
        index: previewImage.indexOf(file.url || file.preview),
      });
      this.$emit('preview', file);
    },
    async handleRemove() {
      if (this.removeConfirm) {
        try {
          await new Promise((resolve, reject) => {
            Modal.confirm({
              title: '确定要移除这张图片吗？',
              onOk: () => resolve(true),
              onCancel: () => reject(),
            });
          });
          return true;
        } catch (error) {
          return false;
        }
      }
    },
    // 图片下载
    handleDown(file) {
      let { url, file_name, name } = file;
      const link = document.createElement('a');
      fetch(url)
        .then(response => response.blob())
        .then(blob => {
          link.download = this.downName || file_name || name;
          link.href = URL.createObjectURL(blob);
          link.click();
        });
    },
    getFileExtension(filePath) {
      const regex = /\.([0-9a-z]+)(?:[\?#]|$)/i;
      const matches = filePath.match(regex);
      return matches ? matches[1] : null;
    },
    handleChange({ file, fileList }) {
      // status: 'done', // 状态有：uploading done error removed
      if (this.locked) {
        this.extralFileList = fileList;
      } else {
        this.fileList = fileList;
      }
      const list = [];
      // 保留图片上传后端返回的原始路径（即不带域名的路径）
      const path = [];
      fileList.forEach(item => {
        if (item.status === 'done') {
          if (this.quality && item.response && item.response.imageUrls) {
            // 有要求图片质量,拿到对应图片质量不包含域名的路径
            const qualityUrl = item.response.imageUrls[this.quality]?.replace(item.response.imgOssServer, '');

            path.push(qualityUrl || item.response.url);
            list.push(item.response.imageUrls[this.quality]);
          } else if (item.response && item.response.url) {
            path.push(item.response.url);
            list.push(`${item.response.imgOssServer || ''}${item.response.url}`);
          } else if (item.url) {
            path.push(item.path);
            list.push(item.url);
          } else {
            // 捕获服务器返回的提示错误
            this.onError(item.response || {});
          }
        }
      });
      this.path = path;
      if (file.status !== 'uploading') {
        if (this.locked) {
          this.$emit('update:value', {
            value: list,
            lockedValue: this.fileList,
          });
          this.$emit(
            'change',
            {
              value: list,
              lockedValue: this.fileList,
            },
            fileList,
            path,
          );
          this.hasChange = true;
        } else {
          this.$emit('update:value', list);
          this.$emit('change', list, fileList, path);
        }
      }
      if (file.status === 'error') {
        message.warning(file.error || '抱歉，上传时发生未知错误，请重试');
      }
    },
  },
};
</script>
<style lang="less" scoped>
.ant-upload-select-picture-card i {
  color: #999;
  font-size: 32px;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.ant-upload {
  margin-bottom: 0;
}

.lock_container {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
  .origin-img {
    display: flex;
  }
  :deep(.ant-upload-list) {
    display: flex;
  }
}

:deep(.ant-image) {
  width: 104px;
  height: 104px;
  margin: 0 8px 8px 0;
  padding: 8px;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 4px;
  img {
    width: 100%;
    height: 100%;
  }
  svg {
    width: 16px;
    height: 16px;
  }
}
:deep(.anticon) {
  margin-inline-end: 0 !important;
}
:deep(.ant-upload-picture-card-wrapper) {
  width: auto;
}
</style>
<style lang="less">
.ant-col-20 {
  max-width: 100% !important;
}
</style>
