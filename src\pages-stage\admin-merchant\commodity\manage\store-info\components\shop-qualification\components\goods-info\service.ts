import { GET, POST } from '@/services/api';
const hostType = 'Golang';
//商品资质列表
export function apiGetGoodsInfoList(params: any) {
  return GET('/server/ServerCredentialGoods/queryGoodsList', params, { hostType });
}
export function getHasQualificationCategory() {
  return GET('/server/ServerCredentialGoods/hasCredentialCategory', undefined, { hostType });
}
//删除商品资质
export function delCredentialGoodsApi(data: any) {
  return POST('/server/ServerCredentialGoods/delete', data, { hostType });
}
