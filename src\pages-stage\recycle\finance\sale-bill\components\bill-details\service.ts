import { GET } from '@/services/api';
import { IPayInfoList, ISaleBillDetail } from './data';

export const getSaleBillDetails = (rsp_order_id: string) => {
  return GET<{ rsp_order_id: string }, ISaleBillDetail>('/super/recycle/sale-order-pay/detail', { rsp_order_id });
};

export const getBillPayInfo = (params: { rsp_order_id: string; operate_type: string }) => {
  return GET<{ rsp_order_id: string; operate_type: string }, { list: IPayInfoList[] }>(
    '/super/recycle/sale-order-pay/pay-log-list',
    params,
  );
};
