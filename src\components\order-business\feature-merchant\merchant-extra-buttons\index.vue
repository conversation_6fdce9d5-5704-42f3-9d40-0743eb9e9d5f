<template>
  <OperateButtonGroup
    :button-attrs="{ style: { 'background-color': '#3EC0F3', color: '#fff' } }"
    :button-data="orderData.button_list || []"
    :button-group="otherButtonGroup"
    class="button-group"
    :modal-attrs="{ orderId: orderData.base_info?.order_id }"
  />
</template>

<script lang="tsx" setup>
import { computed, toRef } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';

import { useGoodsShip } from '@/components/order-business/composables/use-goods-ship';
import { OperateButtonGroup } from '@/components/order-business/feature-application';
import { operateModal } from '@/components/order-business/operate-modals';
import type { IButtonGroupItem, IOrderItem, TRefreshDataKey } from '@/components/order-business/typing';
import { useTopWindow } from '@/hook/common/use-top-window';
import { EOrderInsure, ERiskUrgentAuditStatus } from '@/typing';
import { ORDER_INSURE_TEXT } from '@/utils/constant/order';
import { spliceUrlEnv } from '@/utils/url';

import { equityRiskUrgent } from './services';

const props = defineProps<{
  orderData: IOrderItem;
}>();

const emit = defineEmits<{
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const orderId = toRef(() => props.orderData?.base_info?.order_id);
const route = useRoute();
const domain = route.query.origin || window.location.origin;

const { navigate } = useTopWindow();
const { openChangeGoodsModal } = useGoodsShip(
  orderId.value,
  toRef(() => props.orderData?.button_list),
);

const ORIGIN_STYLE = { 'background-color': '#FEB043', 'color': '#fff' };
const PURPLE_STYLE = { 'background-color': '#AA98FB', 'color': '#fff' };
const RED_STYLE = { 'background-color': '#FF7D73', 'color': '#fff' };
const BLUE_STYLE = { 'background-color': '#58A9FF', 'color': '#fff' };
const GREEN_STYLE = { 'background-color': '#00c8be', 'color': '#fff' };

const studentAuthStatusMap = { 0: '未上传', 1: '已通过', 2: '不通过', 11: '已过期', 9: '待审核' };

const otherButtonGroup = computed<IButtonGroupItem[]>(() => [
  {
    buttonCom: operateModal.riskRepeatResult,
    label: '复审结果',
    buttonAttrs: {
      onClick: () => {
        navigate('blank', `/work-order-inter-risk-repeat/index?order_id=${orderId.value}`);
      },
    },
  },
  {
    buttonCom: operateModal.copyOrder,
    modalAttrs: {
      goodsInfo: {
        spu_name: props.orderData.spu_info?.spu_name,
        sku_name: props.orderData.spu_info?.sku_name,
        num: props.orderData.base_info?.quantity,
        start_time: props.orderData.base_info?.tenancy_data?.start_time,
        end_time: props.orderData.base_info?.tenancy_data?.end_time,
      },
    },
  },
  {
    buttonCom: operateModal.pickupAppointService,
    label: extra => (extra?.is_exist ? '取件预约中' : '取件发货服务'),
    buttonAttrs: { style: PURPLE_STYLE },
    modalAttrs: {
      isDropShippingDelivery:
        props.orderData.device_info?.warehouse_deliver_imei?.type === 'drop_shipping_delivery_order' &&
        props.orderData.device_info?.warehouse_deliver_imei.imei?.length,
    },
  },
  {
    buttonCom: operateModal.returnAddress,
    label: extra => (extra?.ret_addr_id ? '已设置地址' : '未设置地址'),
    buttonAttrs: { style: ORIGIN_STYLE },
  },
  {
    buttonCom: operateModal.getTenantCredentials,
  },
  {
    buttonCom: operateModal.tailTenantCredentials,
  },
  {
    buttonCom: operateModal.studentAuth,
    label: extra => `学生认证-${studentAuthStatusMap[extra?.saStatus]}`,
    buttonAttrs: {
      onClick: extra => {
        if (Number(extra?.saStatus) === 9) {
          message.warn('您暂无操作权限，请等待平台审核');
        }
      },
    },
  },
  {
    buttonCom: operateModal.supplementaryProtocol,
    buttonAttrs: { style: PURPLE_STYLE },
    modalAttrs: {
      enableAdd: true,
      enableEdit: true,
    },
  },
  {
    buttonCom: operateModal.notaryProtocol,
    label: extra => extra?.title_str,
    buttonAttrs: { style: PURPLE_STYLE },
    children: [
      {
        buttonCom: operateModal.notaryProtocolViewEvidence,
      },
      {
        label: extra => (extra?.public_str || '') + '录入证据',
        buttonCom: operateModal.notaryProtocolEnterEvidence,
      },
      {
        label: extra => (extra?.public_str || '') + '录入身份证',
        buttonCom: operateModal.notaryProtocolEnterIdCard,
      },
    ],
  },
  {
    buttonCom: operateModal.snapShot,
    buttonAttrs: {
      onClick: () => {
        const url = spliceUrlEnv('https://www.rrzuji.com/order-snapshot/index');
        window.open(`${url}?order_id=${orderId.value}&item_id=${props.orderData?.spu_info?.code_url}`);
      },
    },
  },
  {
    buttonCom: operateModal.notarizationFile,
    buttonAttrs: extra => ({
      style: RED_STYLE,
      href: extra?.href,
      target: '_blank',
    }),
  },
  {
    buttonCom: operateModal.contractSigning,
    buttonAttrs: {
      style: RED_STYLE,
      href: `${domain}/order/get-esign-qr-code?order_id=${orderId.value}`,
      target: '_blank',
    },
  },
  {
    buttonCom: operateModal.signatureData,
  },
  {
    buttonCom: operateModal.riskRecord,
  },
  {
    buttonCom: operateModal.downloadProtocol,
    buttonAttrs: {
      target: '_blank',
      href: `${domain}/order/protocols?order_id=${orderId.value}`,
    },
  },
  {
    buttonCom: operateModal.insureInfo,
    label: extra => (
      <a-tooltip
        placement="top"
        title={
          [EOrderInsure.InsuredSucceed, EOrderInsure.InsureStatusCancelFail].includes(extra?.insure_status)
            ? '若订单在保险生效7天内已结束，您可在线申请退保'
            : ''
        }
      >
        {ORDER_INSURE_TEXT[extra?.insure_status] || '投保'}
      </a-tooltip>
    ),
    buttonAttrs: { style: ORIGIN_STYLE },
  },
  {
    buttonCom: operateModal.posLeaseAssistanceSubmit,
    children: [
      {
        buttonCom: operateModal.posLeaseAssistanceExtendedCompletion,
        modalAttrs: { modalType: 13 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceApplySuppleOrderComplete,
        modalAttrs: { modalType: 12 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceApplyRenew,
        modalAttrs: { modalType: 1 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceApplyRefund,
        modalAttrs: { modalType: 2 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceModifyBillMoney,
        buttonAttrs: extra => ({ disabled: Boolean(extra?.is_asset_order) }),
        modalAttrs: { modalType: 3 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceChangeBillPeriodNum,
        buttonAttrs: extra => ({
          disabled: Boolean(extra?.is_asset_order),
        }),
        modalAttrs: { modalType: 5 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceModifyBillPayStatus,
        modalAttrs: { modalType: 7 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceModifyTotalRent,
        modalAttrs: { modalType: 4 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceModifyOrderReturned,
        modalAttrs: { modalType: 6 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceFreezeDepositTransPay,
        modalAttrs: { modalType: 11 },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceReviseAllDeductionDates,
        modalAttrs: { modalType: 'all' },
      },
      {
        buttonCom: operateModal.posLeaseAssistanceReviseOneDeductionDates,
        modalAttrs: { modalType: 'one' },
      },
      {
        buttonCom: operateModal.appealClosing,
        modalAttrs: { modalType: 15 },
      },
      {
        buttonCom: operateModal.merchantChangeSendWay,
        modalAttrs: { modalType: 16 },
      },
    ],
  },
  {
    buttonCom: operateModal.historyReceipt,
  },
  {
    buttonCom: operateModal.checkEpInfo,
    buttonAttrs: {
      style: PURPLE_STYLE,
    },
  },
  {
    buttonCom: operateModal.serverEvidence,
    children: [
      {
        buttonCom: operateModal.serverEvidenceView,
      },
      {
        buttonCom: operateModal.serverEvidenceEnter,
      },
    ],
  },
  {
    buttonCom: operateModal.phoneBoxEvidence,
    children: [
      {
        buttonCom: operateModal.phoneBoxEvidenceView,
      },
      {
        buttonCom: operateModal.phoneBoxEvidenceEnter,
      },
    ],
  },
  {
    buttonCom: operateModal.changeGoods,
    buttonAttrs: {
      onClick: openChangeGoodsModal,
    },
  },
  {
    buttonCom: operateModal.userEvidence,
    buttonAttrs: {
      onClick: () => {}, // 点击不做任何操作
    },
    children: [
      {
        buttonCom: operateModal.userEvidence,
        label: '用户存证查看',
      },
      {
        buttonCom: operateModal.exportUserEvidence,
        label: '用户存证导出',
        buttonAttrs: {
          onClick: () => {
            window.open(`${domain}/order/export-user-evidence?order_id=${orderId.value}`);
          },
        },
      },
    ],
  },
  {
    buttonCom: operateModal.firstFlowSource,
  },
  {
    buttonCom: operateModal.qualityReport,
    children: [
      {
        buttonCom: operateModal.deliveryQualityReport,
        modalAttrs: { modalType: 'send' },
      },
      {
        buttonCom: operateModal.returningQualityReport,
        modalAttrs: { modalType: 'return' },
      },
    ],
  },
  {
    buttonCom: operateModal.epContract,
  },
  {
    buttonCom: operateModal.visaInterview,
    label: extra => extra?.text,
    buttonAttrs: extra => {
      if (extra?.sign_status === 0) return { style: { backgroundColor: '#ffd2d3', color: '#fff' } };
      if ([1, 2].includes(extra?.sign_status))
        return {
          style: {
            backgroundColor: '#FF4D4F',
            color: '#fff',
          },
        };
      if (extra?.sign_status === 3)
        return {
          style: {
            border: '#FF4D4F 1px solid',
            color: '#FF4D4F',
          },
        };
      return { style: { backgroundColor: 'rgb(192 192 192)', color: '#ffff' } };
    },
  },
  {
    buttonCom: operateModal.equityRiskUrgent,
    label: extra => {
      if (extra?.value === 2) return '风控加急已输出';
      if (extra?.value === 3) return '风控加急输出中';
      return '申请风控加急';
    },
    buttonAttrs: extra => {
      /** 按钮颜色 */
      let btnStyle = BLUE_STYLE;
      if (Number(extra?.value) === ERiskUrgentAuditStatus.Waiting) {
        btnStyle = ORIGIN_STYLE;
      } else if (Number(extra?.value) === ERiskUrgentAuditStatus.Exported) {
        btnStyle = GREEN_STYLE;
      }
      return {
        style: btnStyle,
        onClick: () => {
          if (Number(extra?.value) === ERiskUrgentAuditStatus.Not) {
            equityRiskUrgent({ order_id: orderId.value })
              .then(() => {
                message.success('加急成功！正在为您加急风控输出');
                emit('refresh', orderId.value, ['data']);
              })
              .catch((error: any) => {
                error?.message && Modal.warning({ closable: true, content: error.message });
              });
          }
        },
      };
    },
  },
  {
    buttonCom: operateModal.equityElectricAudit,
    label: extra => (extra?.id ? '已申请风控复审' : '未申请风控复审'),
    buttonAttrs: {
      style: BLUE_STYLE,
      onClick: extra => {
        const id = extra?.id;
        if (process.env.NODE_ENV === 'development') {
          const href = id
            ? `/merchant/marketing/right-center/my-right?id=electric_audit&type=detail&show_audit_file_modal=1&order_id=${orderId.value}&record_id=${id}`
            : `/merchant/marketing/right-center/my-right?id=electric_audit&type=detail&show_add_audit_modal=1&order_id=${orderId.value}`;
          window.open(window.location.origin + href, '_blank');
        } else {
          const href = id
            ? `/equity-application/my-equity-index?id=electric_audit&type=detail&show_audit_file_modal=1&order_id=${orderId.value}&record_id=${id}`
            : `/equity-application/my-equity-index?id=electric_audit&type=detail&show_add_audit_modal=1&order_id=${orderId.value}`;
          window.parent.postMessage({ action: 'blank', href }, '*');
        }
      },
    },
  },
  {
    buttonCom: operateModal.lockOrderControl,
    children: [
      { buttonCom: operateModal.overdueLock },
      { buttonCom: operateModal.overdueUnlock },
      { buttonCom: operateModal.lockSupportDel },
      { buttonCom: operateModal.requestPwdControl },
      { buttonCom: operateModal.unlockFunctionControl },
      {
        buttonCom: operateModal.viewControlHistory,
        buttonAttrs: {
          onClick: () => {
            window.parent.postMessage(
              {
                action: 'jump',
                jump_url: domain + '/lock-order-work/index?orderId=' + orderId.value,
              },
              '*',
            );
          },
        },
      },
    ],
  },
  {
    buttonCom: operateModal.interceptHistory,
    buttonAttrs: { style: BLUE_STYLE },
  },
  {
    buttonCom: operateModal.proceedingBad,
    buttonAttrs: {
      onClick: () => {
        navigate('blank', `/proceeding-info/one-click-index?orderId=${orderId.value}`);
      },
    },
  },
  {
    buttonCom: operateModal.exchangeRecord,
    buttonAttrs: { style: BLUE_STYLE },
  },
  {
    buttonCom: operateModal.signForReceipt,
    buttonAttrs: {
      onClick: extra => {
        window.open(extra.protocol_url);
      },
    },
  },
  {
    buttonCom: operateModal.userReturnCertificateBtn,
  },
  {
    buttonCom: operateModal.antChainSignBtn,
  },
  {
    buttonCom: operateModal.returnVerificationCodeBtn,
  },
  {
    buttonCom: operateModal.returnVoucherBtn,
  },
  {
    buttonCom: operateModal.requestForInterception,
    label: '拦截申请单',
  },
  {
    buttonCom: operateModal.retrieveReturnCode,
  },
]);
</script>

<style lang="less" scoped>
.button-group {
  width: 100%;
  padding: 4px 12px 16px 12px;

  :deep(.ant-btn) {
    border: none;
  }
}
</style>
