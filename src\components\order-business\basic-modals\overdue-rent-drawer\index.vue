<template>
  <a-drawer
    v-model:visible="bindVisible"
    :pagination="page"
    title="逾期租金记录"
    width="852"
    @after-visible-change="afterVisibleChange"
  >
    <div style="padding: 24rpx; padding-bottom: 0">
      <div class="title-box">
        <div>
          <InfoCircleOutlined style="font-size: 12px; line-height: 22px; vertical-align: middle" />
          {{
            `说明：该订单的最晚归还时间为「${last_repay_time}」；超过最晚归还时间，当天被算入逾期天数，将按照「逾期天数*订单日租金（优惠前）」计算当前订单的「逾期租金」；归还时，用户需要先缴清「逾期租金」才可以进行归还；逾期租金最大值<=购买价`
          }}
        </div>
        <div class="little-text">
          （若商家与用户有协商一致的「逾期租金」可通过「修改逾期租金」功能调整逾期租金）
        </div>
      </div>

      <div class="overdue-red">
        <span>逾期总租金: ￥{{ overdue_total_rent }}</span>
        <span style="margin: 0 4px">已生成账单逾期租金: ￥{{ pay_overdue_rent }}</span>
        <a-tooltip>
          <template #title>
            已生成账单逾期租金并不代表逾期租金已缴纳，仅代表对应租期段的逾期租金通过续租方式变为续租租金账单，后续可以从账单列表了解租金是否缴纳；
          </template>
          <QuestionCircleOutlined />
        </a-tooltip>
      </div>

      <a-table
        :columns="recordColum"
        :data-source="list"
        :loading="listLoading"
        :pagination="page"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'amount_details'">
            <span
              v-if="!!~record.amount_details.indexOf('+')"
              class="overdueDule-data-add"
            >{{
              record.amount_details
            }}</span>
            <span
              v-else
              class="overdueDule-data-subtract"
            >{{ record.amount_details }}</span>
          </template>
        </template>
      </a-table>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { InfoCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';

import { useTable } from '@/hook/component/use-table';

const emit = defineEmits(['update:visible']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});
const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});
const last_repay_time = ref('2020-03-02');
const overdue_total_rent = ref(0);
const pay_overdue_rent = ref(0);

const recordColum = [
  {
    title: '待缴逾期租金(元)',
    key: 'wait_pay',
    dataIndex: 'wait_pay',
  },
  {
    title: '明细(元)',
    key: 'amount_details',
    dataIndex: 'amount_details',
  },
  {
    title: '来源',
    key: 'source_copywriting',
    dataIndex: 'source_copywriting',
  },
  {
    title: '生成日期',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 200,
  },
  {
    title: '操作人',
    key: 'operator',
    dataIndex: 'operator',
  },
];

// 表格数据
const { list, listLoading, page, getTableList, tableChange } = useTable({
  method: 'POST',
  url: '/api/gateway',
  formatSearchValue() {
    return { method: 'overdue.rent.list', order_id: props.orderId };
  },
  formatHandle: async res => {
    const { other } = res.data;
    last_repay_time.value = other.last_repay_time;
    overdue_total_rent.value = other.overdue_total_rent;
    pay_overdue_rent.value = other.pay_overdue_rent;
    return res.data.list;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
  totalKey: 'data.page_info.total_count',
});

function afterVisibleChange(val) {
  if (val) {
    getTableList('search');
  }
  emit('update:visible', val);
}
</script>

<style scoped>
.title-box {
  width: 804px;
  margin-bottom: 24px;
  padding: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background: #f0f7ff;
}

.title-box .little-text {
  margin-top: 8px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.overdue-red {
  margin-bottom: 8px;
  color: #ff4d4f;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

.overdueDule-data-add {
  color: #ff4d4f;
}

.overdueDule-data-subtract {
  color: #00c8be;
}
</style>
