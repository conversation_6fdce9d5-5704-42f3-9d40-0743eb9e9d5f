import { computed, ref } from 'vue';

import { getCateTempDetail } from '@/pages-stage/user-ticket-v2/components/ticket-template-modal/service';
import useEditor from '@/pages-stage/user-ticket-v2/ticket-setting/template-setting/components/template-edit/composables/use-editor';

import { EAssistType } from '../data.d';

/** 租后协助类型与对应模版id映射 */
/** 此处为后续需求 */
const assistTypeTempalteTypeMap: Partial<Record<EAssistType, number>> = {
  [EAssistType.changeBillPeriod]: 8,
  [EAssistType.modifyOrderTotalRental]: 11,
  [EAssistType.changeSend]: 70,
  [EAssistType.extendOrder]: 16,
  [EAssistType.freezeDeposit]: 14,
  [EAssistType.modifyBill]: 7,
  [EAssistType.modifyBillPayStatus]: 10,
  [EAssistType.modifyOrderReturnStatus]: 9,
  [EAssistType.refund]: 5,
  [EAssistType.supplementOrder]: 15,
  [EAssistType.leaseRenewal]: 4,
};
export default function useTemplateRichData() {
  const { checkoutMaxLength } = useEditor();
  const editorContent = ref('');
  const maxLength = 50;
  const showOpenBtn = ref(false);
  const currentOpen = ref(false);

  const styleObj = computed(() => {
    return currentOpen.value || !showOpenBtn.value ? { height: 'auto' } : { height: '70px', overflow: 'hidden' };
  });

  async function getRichData(modalType: EAssistType) {
    editorContent.value = '';
    showOpenBtn.value = false;
    currentOpen.value = false;
    const tempId = assistTypeTempalteTypeMap[modalType];
    if (!tempId) return;
    const { data } = await getCateTempDetail({ id: String(tempId) });
    if (!data || !data.rich_data) return;
    //是否内容过长
    const isOutLength = checkoutMaxLength(data.rich_data, maxLength);
    const parser = new DOMParser();
    const doc = parser.parseFromString(data.rich_data, 'text/html');
    const pElment = doc.querySelectorAll('p');
    //是否行数过多
    const isOutRow = pElment.length > 2;
    showOpenBtn.value = isOutRow || isOutLength;
    //处理p标签bottom
    let result = '';
    pElment.forEach((el, index) => {
      el.style.marginBottom = index === pElment.length - 1 ? '0' : '8px';
      result += el.outerHTML;
    });
    editorContent.value = result;
  }

  return {
    getRichData,
    editorContent,
    showOpenBtn,
    currentOpen,
    styleObj,
  };
}
