import { ColumnProps } from 'ant-design-vue/lib/table';

export const columns: ColumnProps = [
  {
    dataIndex: 'date',
    title: '时间',
    width: 202,
  },
  {
    dataIndex: 'sale_num',
    title: '销售订单单数',
    width: 156,
    tipsText:
      '【统计时间内标记代发且下发至回收仓的订单单数，若订单发生转移则需要扣除对应订单数；筛选日期+广州回收总仓仓位，在订单发货表中筛选。】，更新频率【实时更新】，数据源【订单管理-订单发货表】。',
  },
  {
    dataIndex: 'sale_price',
    title: '销售订单金额(元)',
    width: 180,
    tipsText: '【销售订单数对应销售价格的总和】，更新频率【实时更新】，数据源【供应链】。',
  },
  {
    dataIndex: 'out_warehouse_device_num',
    title: '销售出库订单单数',
    width: 184,
    tipsText:
      '【统计时间内标记代发且下发至回收仓的全部订单中经过、变成已发货状态的总订单单数 】，更新频率【实时更新】，数据源【订单管理-订单发货表】',
    equipmentTipsText: '【统计时间内成功提交生成的总订单的设备数】，更新频率【实时更新】，数据源【设备查询表数据源】',
  },
  {
    dataIndex: 'out_warehouse_price',
    title: '销售出库订单金额',
    width: 208,
    tipsText: '【销售出库订单数对应销售价格的总和】，更新频率【实时更新】，数据源【供应链】。',
  },
  {
    dataIndex: 'return_goods_device_num',
    title: '销售退货订单单数',
    width: 184,
    tipsText:
      '【统计时间内的租赁订单，有标记代发且从回收仓已发货并被用户进行租赁订单退货的总订单单数 】，更新频率【实时更新】，数据源【订单管理-订单发货表】。',
    equipmentTipsText: '【统计时间内成功退货的总订单的设备数】，更新频率【实时更新】，数据源【设备查询表数据源】',
  },
  {
    dataIndex: 'return_goods_price',
    title: '销售退货订单金额',
    width: 208,
    tipsText:
      '【统计时间内的租赁订单，有标记代发且从回收仓已发货并被用户进行租赁订单退货的总订单单数 对应销售价格的总和】，更新频率【实时更新】，数据源【供应链】。',
  },
  {
    dataIndex: 'gross_profit',
    title: '销售毛利(元)',
    width: 152,
    tipsText: '【统计时间内销售订单金额-销售订单成本 】，更新频率【实时更新】，数据源【供应链】。',
  },
  {
    dataIndex: 'gross_margin',
    title: '毛利率(%)',
    width: 138,
    tipsText:
      '【（统计时间内销售订单金额-销售订单成本）/销售订单金额 x 100% 】，更新频率【实时更新】，数据源【供应链】。',
  },
];
