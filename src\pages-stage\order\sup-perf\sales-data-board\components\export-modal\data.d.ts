export enum EApi {
  increaseAndOutstanding = '/super/salesData/export/performance-fields',
  returnOf = '/super/salesData/export/terminate-lease-fields',
  overdue = '/super/salesData/export/overdue-fields',
}

export enum EExportApi {
  exportIncreaseAndOutstanding = '/super/salesData/export/performance',
  exportReturnOf = '/super/salesData/export/terminate-lease',
  exportOverdue = '/super/salesData/export/overdue',
}

export interface IListItem {
  label: string;
  value: number | string;
  options: { label: string; value: string }[];
  api: EApi;
  exportApi: EExportApi;
}
