import type { FormGroupItem } from '@/components/form-create/src/typing';
import type { ColumnType } from 'ant-design-vue/lib/table';

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'refund_order_id',
    originProps: { label: '单据编号', name: 'refund_order_id' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'server_name',
    originProps: { label: '商户名称', name: 'server_name' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'server_id',
    originProps: { label: '商户ID', name: 'server_id' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '167px' } },
    fragmentKey: 'renderInput',
  },
];

export const refundBillColumns: ColumnType[] = [
  {
    title: '单据日期',
    key: 'date',
    dataIndex: 'date',
    width: 120,
    fixed: 'left',
  },
  {
    title: '单据编号',
    key: 'refund_order_id',
    dataIndex: 'refund_order_id',
    width: 182,
    fixed: 'left',
  },
  {
    title: '审核状态',
    key: 'status',
    dataIndex: 'status',
    width: 96,
  },
  {
    title: '商户',
    key: 'company',
    dataIndex: 'company',
    width: 180,
  },
  {
    title: '商户ID',
    key: 'server_id',
    dataIndex: 'server_id',
    width: 74,
  },
  {
    title: '币别',
    key: 'coin_label',
    dataIndex: 'coin_label',
    width: 74,
  },
  {
    title: '退款金额合计',
    key: 'total_amount',
    dataIndex: 'total_amount',
    width: 120,
  },
  {
    title: '退款核销总金额',
    key: 'refund_total_price',
    dataIndex: 'refund_total_price',
    width: 130,
  },
  {
    title: '退款账户',
    key: 'pay_account_label',
    dataIndex: 'pay_account_label',
    width: 180,
  },
  {
    title: '退款方式',
    key: 'pay_type_label',
    dataIndex: 'pay_type_label',
    width: 180,
  },
  {
    title: '单据来源',
    key: 'source_type_label',
    dataIndex: 'source_type_label',
    width: 92,
  },
  {
    title: '制单人',
    key: 'create_name',
    dataIndex: 'create_name',
    width: 90,
  },
  {
    title: '审核人',
    key: 'check_name',
    dataIndex: 'check_name',
    width: 90,
  },
  {
    title: '修改人',
    key: 'update_name',
    dataIndex: 'update_name',
    width: 90,
  },
  {
    title: '制单时间',
    key: 'create_time',
    dataIndex: 'create_time',
    width: 120,
  },
  {
    title: '修改时间',
    key: 'update_time',
    dataIndex: 'update_time',
    width: 120,
  },
];
