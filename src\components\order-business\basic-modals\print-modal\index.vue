<template>
  <a-modal
    ok-text="打印"
    :title="title"
    :visible="visible"
    width="800px"
    @cancel="handleCancel"
    @ok="onPrintReceipt"
  >
    <iframe
      v-if="printReceiptSrc"
      height="600"
      name="print_page"
      :src="printReceiptSrc"
      style="border: none"
      width="750"
    />
  </a-modal>
</template>

<script lang="ts" setup>
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '打印',
  },
  printReceiptSrc: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['update:visible']);

const onPrintReceipt = () => {
  window.frames['print_page'].print();
};

const handleCancel = () => emits('update:visible', false);
</script>
