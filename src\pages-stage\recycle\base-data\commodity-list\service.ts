import { GET, POST } from '@/services/api';

// 获取table数据
export function getTableList(params: any) {
  return POST('/super/recycle/base-goods/list', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 查看商品详情
export function searchCommodity(id: string) {
  return GET(`/super/recycle/base-goods/detail?id=${id}`);
}

// 修改商品
export function updateCommodity(body: any) {
  return POST('/super/recycle/base-goods/update', body, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 创建商品
export function createCommodity(body: any) {
  return POST('/super/recycle/base-goods/create', body, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 上架商品
export function upCommodity(id: string) {
  return GET(`/super/recycle/base-goods/up?id=${id}`);
}

// 下架商品
export function downCommodity(id: string) {
  return GET(`/super/recycle/base-goods/down?id=${id}`);
}

// 获取商品分类下拉列表
export function getCommodityOptions() {
  return GET('/common/form-filter?page_from=recycleBaseGoods_create');
}
//商品树行数据
export function categoryGoodsTree() {
  return GET('/super/recycle/base-goods/category-goods-tree');
}
//机型下拉
export function warehouseGoodsList(category_id: number, warehouse_dict_id?: number) {
  return GET('/super/recycle/base-goods/warehouse-goods', {
    category_id,
    warehouse_dict_id,
  });
}
