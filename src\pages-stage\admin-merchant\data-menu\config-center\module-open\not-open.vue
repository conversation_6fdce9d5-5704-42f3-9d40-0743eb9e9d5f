<template>
  <div class="not-open">
    <a-spin :spinning="loading">
      <a-row
        v-if="notOpenedList.length > 0"
        :gutter="[24, 8]"
      >
        <a-col
          v-for="item in notOpenedList"
          :key="item.cn_name"
          :md="6"
          :sm="12"
          style="min-width: 268px"
        >
          <div class="item">
            <div class="item-header">
              <img
                class="item-header-icon"
                :src="item.icon"
              >
              <div class="item-header-title">
                {{ item.cn_name }}
              </div>
            </div>
            <div class="item-func">
              <div
                v-for="item2 in item.functionList"
                :key="item2.name"
                class="item-func-content"
              >
                <img src="https://img1.rrzuji.cn/uploads/scheme/2304/17/m/f8m8gQgoCs68h9wJfTrd.png">
                <div class="content-title">
                  {{ item2.name }}
                </div>
              </div>
            </div>
            <a-button
              block
              class="item-btn"
              size="large"
              type="primary"
              @click="handleOpen(item)"
            >
              立即开通
            </a-button>
          </div>
        </a-col>
      </a-row>

      <REmpty v-else />
    </a-spin>
  </div>
  <a-modal
    v-model:visible="visible"
    centered
    title="模块开通"
    width="480px"
    @ok="handleOk"
  >
    <div class="modal-content">
      <div class="content-module">
        <span class="content-sub-title">当前开通模块：</span>
        <img
          alt=""
          :src="openItem.icon"
        >
        <span class="content-sub-desc">{{ openItem.cn_name }}</span>
      </div>

      <div class="content-choose-title">
        选择开通时长：
      </div>
      <div class="content-choose-list">
        <!-- <div
          v-for="item in chooseList"
          :key="item.time"
          class="content-choose-item"
          :class="item.isActive ? 'active' : ''"
          @click="handleChoose(item.id)"
        >
          <div class="item-title">
            {{ item.title }}
          </div>
          <div class="item-time">
            到期时间：{{ item.time }}
          </div>
        </div>
      </div> -->
        <div class="content-choose-item active">
          <div class="item-title">
            内测时长
          </div>
          <div class="item-time">
            到期时间：{{ openItem.to_txt || '--' }}
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { PropType, ref } from 'vue';
import { message } from 'ant-design-vue';

import { OpenModuleItem } from './data';
import { openModule } from './service';

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  notOpenedList: {
    type: Array as PropType<OpenModuleItem[]>,
    default() {
      return () => [];
    },
  },
});

const emit = defineEmits(['reload-list']);

let openItem = ref<OpenModuleItem | Record<string, any>>({});

const visible = ref<boolean>(false);
const handleOpen = (item: OpenModuleItem) => {
  openItem.value = item;
  visible.value = true;
};
// const handleChoose = (id: number) => {
//   const list = chooseList.value;
//   list.forEach(i => {
//     if (i.id === id) {
//       i.isActive = true;
//     } else {
//       i.isActive = false;
//     }
//   });
// };

const handleOk = () => {
  openModule({ module_id: openItem.value.id }).then(() => {
    message.success('开通成功');
    emit('reload-list');
    visible.value = false;
  });
};
</script>

<style lang="less" scoped>
.not-open {
  margin: 16px 0 36px;
  .item {
    position: relative;
    height: 380px;
    padding: 0 24px 24px;
    border: 1px solid #e1e3e7;
    border-radius: 16px;

    &-header {
      display: flex;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid rgba(6, 21, 51, 0.06);

      &-icon {
        width: 32px;
        margin-right: 16px;
      }

      &-title {
        font-weight: bold;
        font-size: 16px;
      }
    }

    &-func {
      height: 63%;
      padding-top: 20px;
      overflow: auto;

      &-content {
        display: flex;
        align-items: center;
        margin-bottom: 14px;

        img {
          width: 23px;
          margin-right: 8px;
        }

        .content-title {
          color: rgba(6, 21, 51, 0.65);
        }
      }
    }
  }
}

.modal-content {
  .content-module {
    display: flex;
    align-items: center;

    .content-sub-title {
      color: rgba(6, 21, 51, 0.85);
    }

    img {
      width: 20px;
      margin-right: 8px;
    }
  }

  .content-sub-desc {
    color: #3777ff;
  }

  .content-choose-title {
    margin: 30px 0 8px;
    color: rgba(6, 21, 51, 0.85);
  }

  .content-choose-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .content-choose-item {
      width: 50%;
      padding: 8px 12px;
      border: 1px solid #e1e3e7;
      border-radius: 4px;
      cursor: pointer;

      .item-title {
        color: rgba(6, 21, 51, 0.85);
      }

      .item-time {
        margin-top: 4px;
        color: rgba(6, 21, 51, 0.45);
        font-size: 12px;
      }
    }

    .active {
      border-color: #3777ff;
    }
  }
}
</style>
