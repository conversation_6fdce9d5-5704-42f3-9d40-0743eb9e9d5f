<template>
  <div class="upload-file-wrap">
    <a-upload-dragger
      v-if="canUpload"
      accept=".doc,.docx,.mp3,.mp4,.mp4a,.xlsx,.xls,.csv,.pdf,.png,.jpg"
      :before-upload="beforeUpload"
      :file-list="fileList"
      :multiple="true"
      :show-upload-list="{ showDownloadIcon: true, showRemoveIcon: true }"
      style="margin-bottom: 16px"
      @change="handleChange"
      @drop="handleDrop"
      @reject="handleReject"
      @remove="handleRemove"
    >
      <template #downloadIcon="{ file }">
        <download-outlined @click.stop="handleDownload(file)" />
      </template>

      <p class="ant-upload-drag-icon">
        <container-outlined />
      </p>
      <p class="ant-upload-text">
        点击或将文件拖拽到这里上传
      </p>
      <p class="ant-upload-hint">
        支持扩展名：.doc .docx .mp3 .mp4 .mp4a .xlsx .xls .csv .pdf .png .jpg
      </p>
    </a-upload-dragger>
    <a-upload
      v-else
      :file-list="fileList"
      :show-upload-list="{ showRemoveIcon: false }"
      style="margin-bottom: 16px"
    />
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from 'vue';
import { message, Upload, UploadFile } from 'ant-design-vue';
import { ContainerOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { randomString, getSuffix, getOssImageParams } from '@/utils/oss-helper';
import { FileItemType } from '../../data';

interface ISubmitDepository {
  name: string;
  url: string;
  path?: string;
  status?: string;
}

const emit = defineEmits(['fileListUpdate']);
const props = defineProps({
  uploadText: {
    type: String,
    default: '存证',
  },
  // 外部重置数据的时机
  resetData: {
    type: Boolean,
    default: false,
  },
  defaultFile: {
    type: Object as PropType<FileItemType[]>,
    default: () => [],
  },
  canUpload: {
    type: Boolean,
    default: true,
  },
});

const handleReject = function () {
  message.error('仅支持上传以下文件格式类型：.doc .docx .mp3 .mp4 .mp4a .xlsx .xls .csv .pdf .png .jpg');
  return Upload.LIST_IGNORE;
};
const handleDownload = function (item: ISubmitDepository) {
  let { url, name } = item;
  const link = document.createElement('a');
  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      link.download = name;
      link.href = URL.createObjectURL(blob);
      link.click();
    });
};
const handleDrop = function () {
  return Upload.LIST_IGNORE;
};

const handleChange = () => {
  emit('fileListUpdate', fileList.value);
};

const fileList = ref<UploadFile[]>([]);

watch(
  () => props.resetData,
  val => {
    if (val) {
      fileList.value = [];
    }
  },
);

watch(
  () => props.defaultFile,
  val => {
    if (val) {
      fileList.value = val as any;
    }
  },
  {
    immediate: true,
  },
);

const beforeUpload = async function (file: File) {
  const { size, name } = file;
  if (size / 1024 / 1024 > 5) {
    message.error(`${name},上传失败，文件大小限制最大为5M！`);
    return Upload.LIST_IGNORE;
  }
  const imageParams = await getOssImageParams();
  const uploadParam = {
    key: imageParams['dir'] + randomString(20) + getSuffix(file.name),
    OSSAccessKeyId: imageParams.accessid,
    policy: imageParams.policy,
    signature: imageParams.signature,
    success_action_status: '200',
    dir: imageParams.dir,
    file,
  };
  await ossUpload(uploadParam, imageParams.imgOssServer, name);
  return false;
};

const handleRemove = (file: UploadFile) => {
  const index = fileList.value.findIndex((item: UploadFile) => item.uid === file.uid);
  fileList.value.splice(index, 1);
  emit('fileListUpdate', fileList.value);
};

const ossUpload = async function (uploadParam: any, host: string, name: string) {
  const formData = new FormData();
  for (const key in uploadParam) {
    formData.append(key, uploadParam[key]);
  }
  await fetch(host, {
    method: 'POST',
    body: formData,
  });

  // 这里添加url是为了可以直接上传后预览
  const data = {
    name,
    url: `${host}/${uploadParam['key']}`,
    path: `/${uploadParam['key']}`,
  } as any;
  fileList.value.push(data);
};
</script>

<style lang="less" scoped>
.upload-file-wrap {
  :deep(.ant-upload.ant-upload-drag) {
    .ant-upload {
      padding: 40px 0;
    }

    p.ant-upload-text {
      color: rgba(6, 21, 51, 0.65);
      font-size: 14px;
    }

    p.ant-upload-hint {
      color: rgba(6, 21, 51, 0.45);
      font-size: 12px;
    }
  }
}
</style>
