<template>
  <card-box
    page-url="/super/recycle/data-count/sale-daily-page"
    title="今日销售"
  >
    <template #suffix>
      （总计：{{ info.sale_price || '-' }}元）
    </template>
    <flex-container
      :columns-number="2"
      :gap="8"
    >
      <card-data-item
        title="销售订单"
        unit="元"
        :value="info.sale_price"
        :value-suffix="`/${info.sale_num || 0}单`"
      />
      <card-data-item
        title="销售出库"
        unit="元"
        :value="info.out_warehouse_price"
        :value-suffix="`/${info.out_warehouse_num || 0}单`"
      />
      <card-data-item
        title="销售退货"
        unit="元"
        :value="info.return_goods_price"
        :value-suffix="`/${info.return_goods_num || 0}单`"
      />
      <card-data-item
        title="毛利"
        unit="元"
        :value="info.gross_profit"
      />
    </flex-container>
  </card-box>
</template>

<script lang="ts" setup>
import useDaySellData from '../composables/use-day-sell-data';
import FlexContainer from './flex-container.vue';
import CardBox from './card-box.vue';
import CardDataItem from './card-data-item.vue';

const { info } = useDaySellData();
</script>
