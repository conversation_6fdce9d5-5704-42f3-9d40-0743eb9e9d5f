<template>
  <layout-admin-page
    :navs="['趣回收', '运营管理', '估价降价趋势']"
    title="估价降价趋势"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="addReduction"
      >
        <template #icon>
          <plus-outlined />
        </template>
        添加价格趋势
      </a-button>
    </template>
    <div class="container">
      <div class="search">
        <form-create
          ref="searchFormRef"
          v-model:value="searchParams"
          :form-group="searchFormGroup"
          :origin-props="{
            layout: 'inline',
          }"
        >
          <template #buttons>
            <a-button
              style="margin-right: 10px"
              type="primary"
              @click="getTableList"
            >
              查询
            </a-button>
            <a-button @click="resetSearch">
              重置
            </a-button>
          </template>
        </form-create>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table"
          :columns="columns"
          :data-source="list"
          :loading="listLoading"
          :pagination="page"
          :scroll="{ x: '100%' }"
          :sticky="true"
          style="margin-top: 12px"
          @change="tableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button
                class="link-btn"
                type="link"
                @click="editReduction(record.id)"
              >
                编辑
              </a-button>
              <a-button
                danger
                type="link"
                @click="deleteHandler(record.id)"
              >
                删除
              </a-button>
              <a-button
                class="link-btn"
                type="link"
                @click="openRecord(record.id)"
              >
                修改记录
              </a-button>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-switch
                v-model:checked="record.status"
                checked-children="启用"
                checked-value="1"
                un-checked-children="停用"
                un-checked-value="2"
                @change="statusChange($event, record)"
              />
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 添加趋势 -->
    <reduction-modal
      v-model:formState="formState"
      v-model:visible="visible"
      :loading="loading"
      :search-by-goods-id="searchByGoodsId"
      :type="type"
      @close="close"
      @confirm="confirm"
      @reset-table="resetSearch"
    />
    <!-- 修改记录 -->
    <operation-record
      :id="recordId"
      ref="operationRef"
    />
  </layout-admin-page>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { columns, searchFormGroup } from './config';
import { PlusOutlined } from '@ant-design/icons-vue';
import useReduction from './composiable/use-reduction';
import useSearch from './composiable/use-search';
import ReductionModal from './components/reduction-modal.vue';
import OperationRecord from './components/operation-record.vue';

const { list, listLoading, page, getTableList, tableChange, statusChange, searchParams, deleteHandler } = useSearch();
const {
  visible,
  editReduction,
  loading,
  close,
  confirm,
  addReduction,
  type,
  formState,
  searchByGoodsId,
} = useReduction();

const searchFormRef = ref(null);

// 重置搜索
const resetSearch = () => {
  (searchFormRef?.value as any)?.getFormRef().resetFields();
  getTableList();
};

const operationRef = ref<InstanceType<typeof OperationRecord> | null>(null);
const recordId = ref('');

const openRecord = (id: string) => {
  recordId.value = id;
  operationRef.value?.open();
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
@import '../../common/base.less';
.search {
  margin: 0 24px;
}
</style>
