import { ColumnType } from 'ant-design-vue/lib/table';
import { FormGroupItem, Option } from '@/components/form-create/src/typing';
import useCategory from './composables/use-category';
import { IOrderList } from './data';

type S = Option[];
interface ISearOptions {
  orderStatusOptions: Array<Option & { color?: string }>;
  customerTypeOtions: S;
  orderOringinOptions: S;
  payWaysOptions: S;
}
// 搜索项
export const searchOptions: ISearOptions = {
  // 订单状态筛选
  orderStatusOptions: [
    {
      label: '全部',
      value: '',
    },
    {
      label: '待付款',
      value: '1',
      color: '#FAAD14',
    },
    {
      label: '待发货',
      value: '2',
      color: '#3777FF',
    },
    {
      label: '待收货',
      value: '3',
      color: '#3777FF',
    },
    {
      label: '已签收',
      value: '4',
      color: '#00C8BE',
    },
    {
      label: '已完成',
      value: '10',
      color: '#FF4A57',
    },
    {
      label: '已关闭',
      value: '11',
      color: '#FF4A57',
    },
  ],
  // 客户类型
  customerTypeOtions: [
    {
      label: '个人',
      value: '3',
    },
    {
      label: '企业',
      value: '2',
    },
    {
      label: '企业【租赁商】',
      value: '1',
    },
  ],
  // 订单来源
  orderOringinOptions: [
    {
      label: '租赁采购',
      value: '1',
    },
  ],
  // 支付方式
  payWaysOptions: [
    {
      label: '预付款',
      value: '1',
    },
  ],
};

const { spuDataListloadData, handleSpuDataList } = useCategory();

// 搜索表单组件配置
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'rs_order_id',
    originProps: { label: '订单编号', name: 'rs_order_id' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'order_status',
    originProps: { label: '订单状态', name: 'order_status' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请输入',
      showArrow: true,
      showSearch: false,
    },
    fragmentKey: 'renderSelect',
    options: searchOptions.orderStatusOptions,
  },
  {
    key: 'category',
    originProps: { label: '品类', name: 'category' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请选择',
      changeOnSelect: true,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      loadData: () => {},
    },
    url: '/warehouse/SpuDataList',
    hostType: 'Golang',
    fragmentKey: 'renderCascader',
    optionsFormat: res => {
      return handleSpuDataList(res.data.category, 1);
    },
    changeHandler: ({ selectedOptions, value, options }) => {
      spuDataListloadData(selectedOptions, value, options);
    },
  },
  {
    key: 'server_id',
    originProps: { label: '客户ID/名称', name: 'server_id' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'custom_type',
    originProps: { label: '客户类型', name: 'custom_type' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入', showArrow: true },
    fragmentKey: 'renderSelect',
    options: searchOptions.customerTypeOtions,
  },
  {
    key: 'phone',
    originProps: { label: '收货人手机号', name: 'phone' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'source_type',
    originProps: { label: '订单来源', name: 'source_type' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请输入',
      mode: 'multiple',
      showArrow: true,
      showSearch: false,
    },
    fragmentKey: 'renderSelect',
    options: searchOptions.orderOringinOptions,
  },
  {
    key: 'logistic_code',
    originProps: { label: '物流单号', name: 'logistic_code' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
  {
    key: 'pay_type',
    originProps: { label: '支付方式', name: 'pay_type' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入' },
    fragmentKey: 'renderSelect',
    options: searchOptions.payWaysOptions,
  },
  {
    key: 'create_time',
    originProps: { label: '创建时间', name: 'create_time' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      format: 'YYYY-MM-DD HH:mm',
      showCount: true,
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm',
    },
    fragmentKey: 'renderRangePicker',
  },
];

//表格列
export const saleListColunms: ColumnType<IOrderList>[] = [
  {
    title: '商品信息',
    key: 'goods_info',
    dataIndex: 'goods_info',
  },
  {
    title: '价格/数量/其它',
    key: 'price',
    dataIndex: 'price',
  },
  {
    title: '实付款',
    key: 'real',
    dataIndex: 'real',
    align: 'center',
  },
  {
    title: '收货信息',
    key: 'address',
    dataIndex: 'address',
  },
  {
    title: '订单状态',
    key: 'order_status',
    dataIndex: 'order_status',
    align: 'center',
    className: 'vertical-middle',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    className: 'vertical-middle',
    align: 'center',
  },
];
