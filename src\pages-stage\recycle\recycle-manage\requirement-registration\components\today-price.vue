<template>
  <div class="today">
    {{ today }} 更新
  </div>
  <a-table
    bordered
    class="bottom-fix-table"
    :columns="columns"
    :data-source="mergedDataSource"
    :loading="listLoading"
    :pagination="false"
    :row-key="getRowKey"
    :scroll="{ x: '100%' }"
    :sticky="true"
    style="margin-top: 12px"
  />
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useTodayPrice } from '@/pages-stage/recycle/common/use-today-price';
import { getTodayPrice, getNoDropPrice } from '../service';

const props = defineProps({
  activeKey: {
    type: Number,
    default: 2,
  },
});

const apiConfig = {
  2: getTodayPrice,
  3: getNoDropPrice,
};

const { listLoading, mergedDataSource, clueListColumns, getRowKey, fetchTableData, today } = useTodayPrice({
  fetchDataHook: apiConfig[props.activeKey],
  onMounted: false,
});

const newAllObject = {
  title: '全新',
  key: 'new100',
  dataIndex: 'new100',
  width: 120,
};
const newEightObject = {
  title: '80新',
  key: 'new80',
  dataIndex: 'new80',
  width: 120,
};

const columns = computed(() => {
  const newColumns = [...clueListColumns];
  // 在第五个位置插入 newAllObject
  newColumns.splice(5, 0, newAllObject);
  // 在插入 newEightObject
  newColumns.push(newEightObject);
  const columnsToShow = props.activeKey === 3 ? newColumns.slice(1) : clueListColumns.slice(1);
  return columnsToShow;
});

onMounted(() => {
  fetchTableData();
});
</script>

<style lang="less" scoped>
.today {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 22px;
}
</style>
