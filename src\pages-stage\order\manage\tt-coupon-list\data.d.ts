export interface IOrder {
  order_id: string;
  item_name: string;
  coupon_id: string;
  rental_has_pay: string;
  quantity: number;
  order_status: string;
  name: string;
  phone: string;
  need_audit: boolean;
}
export interface IDetailInfo {
  created_by: string;
  updated_by: string;
  system_desc: string;
  updated_at: string;
  order_id: string;
  deny_message: string;
  coupon_title: string;
  user_name: string;
  id: string;
}
