import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'nickname',
    fragmentKey: 'renderInput',
    originProps: { label: '昵称', name: 'nickname' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'remark_name',
    originProps: { label: '备注名', name: 'remark_name' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
];

export const columns: ColumnType[] = [
  {
    title: '序号',
    width: 80,
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: { index: number }) => `${index + 1}`,
  },
  { title: '微信昵称', dataIndex: 'nickname', key: 'nickname' },
  { title: '备注名', dataIndex: 'remark_name', key: 'remark_name' },
  {
    title: '状态',
    dataIndex: 'status_text',
    key: 'status_text',
  },
  {
    title: '绑定时间',
    dataIndex: 'created_at',
    key: 'created_at',
  },
  {
    title: '操作',
    key: 'operation',
  },
];
