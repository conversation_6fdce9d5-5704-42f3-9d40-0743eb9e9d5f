<template>
  <div class="info">
    <div class="container">
      <div class="charge">
        <span>联系人：<span class="contact-value">{{ merchInfo.registerPerson }}</span></span>
        <span>联系手机：<span class="contact-value">{{ merchInfo.registerPhone }}</span></span>
        <span>店铺ID：<span class="contact-value">{{ merchInfo.server_id }}</span></span>
        <div
          v-if="files"
          class="pull-right"
        >
          <a
            v-for="(item, index) in files"
            :key="index"
            class="btn-file"
            download=""
            :href="item"
            target="_blank"
          >
            <i class="icon-paperclip" />
            <span>附件{{ index + 1 }}</span>
          </a>
        </div>
      </div>
      <table
        border="1"
        class="table"
        width="976"
      >
        <thead>
          <tr>
            <th colspan="4">
              线上店铺信息
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="160">
              线上店铺名称
            </th>
            <td colspan="3">
              {{ merchInfo.merchName }}
            </td>
          </tr>
          <tr>
            <th width="160">
              租赁范围
            </th>
            <td colspan="3">
              {{ merchInfo.category }}
            </td>
          </tr>
          <tr>
            <th width="160">
              运营联系人姓名
            </th>
            <td width="220">
              {{ merchInfo.contactUser }}
            </td>
            <th width="160">
              运营联系人电话
            </th>
            <td width="220">
              <sensitive-field
                field="contactPhone"
                field-type="1"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="tellphone"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              邮箱
            </th>
            <td width="220">
              <sensitive-field
                field="email"
                field-type="7"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="fax"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
            <th width="160">
              发票
            </th>
            <td width="220">
              {{ invoice }}
            </td>
          </tr>
        </tbody>
        <thead>
          <tr>
            <th colspan="4">
              企业信息
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="160">
              企业名称
            </th>
            <td colspan="3">
              <sensitive-field
                field="company_name"
                field-type="8"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="company_name"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              企业类型
            </th>
            <td colspan="3">
              <template v-if="merchInfo.companyType == 1">
                公司
              </template>
              <template v-else-if="merchInfo.companyType == 2">
                个体工商户
              </template>
            </td>
          </tr>
          <tr>
            <th width="160">
              企业收款支付宝账号
            </th>
            <td colspan="3">
              <sensitive-field
                :btn-show="!isSuper || (isSuper && viewPlaintextAuth.isAlipayIdPermission)"
                field="settle_target"
                field-type="13"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="settle_target"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              商户类别码mcc
            </th>
            <td colspan="3">
              {{ merchInfo.mcc_text }}
            </td>
          </tr>
          <tr>
            <th width="160">
              特殊行业
            </th>
            <td colspan="3">
              {{ merchInfo.industry_qualification_text }}
            </td>
          </tr>
          <tr>
            <th width="160">
              特殊行业资质证明
            </th>
            <td colspan="3">
              <a-image
                v-if="merchInfo.industry_qualification_img"
                :src="merchInfo.industry_qualification_img"
                style="max-height: 92px"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              注册城市
            </th>
            <td colspan="3">
              {{ merchInfo.city }}
            </td>
          </tr>
          <tr>
            <th width="160">
              法定代表人
            </th>
            <td>{{ merchInfo.represent }}</td>
            <th
              rowspan="2"
              width="160"
            >
              营业执照
            </th>
            <td rowspan="2">
              <a-image
                :preview="{
                  visible: licenseUrlVisible,
                  onVisibleChange: visible =>
                    lookSensitive(visible, 'license_image', 6, 'license', isSuper ? 169 : 1025),
                }"
                :src="merchInfo.licenseImage"
                style="max-height: 92px"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              统一社会信用代码
            </th>
            <td>
              <sensitive-field
                :btn-show="!isSuper || (isSuper && viewPlaintextAuth.isCompanyCodePermission)"
                field="license_no"
                field-type="9"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="license_no"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              公司地址
            </th>
            <td colspan="3">
              <sensitive-field
                :btn-show="!isSuper || (isSuper && viewPlaintextAuth.isAddressPermission)"
                field="provinces"
                field-type="3"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="provinces"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
        </tbody>
        <thead>
          <tr>
            <th colspan="4">
              个人信息
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="160">
              法人姓名
            </th>
            <td width="220">
              {{ merchInfo.behalfUser }}
            </td>
            <th width="160">
              法人联系方式
            </th>
            <td width="220">
              {{ merchInfo.legal_person_phone }}
            </td>
          </tr>
          <tr>
            <th width="160">
              身份证人像面
            </th>
            <td>
              <a-image
                :preview="{
                  visible: faceUrlVisible,
                  onVisibleChange: visible =>
                    lookSensitive(visible, 'legal_person_idcard', 5, 'face', isSuper ? 169 : 1025),
                }"
                :src="merchInfo.idcardFace"
                style="max-height: 92px"
              />
            </td>
            <th width="160">
              身份证国徽面
            </th>
            <td>
              <a-image
                :preview="{
                  visible: backUrlVisible,
                  onVisibleChange: visible =>
                    lookSensitive(visible, 'legal_person_other_idcard', 5, 'back', isSuper ? 169 : 1025),
                }"
                :src="merchInfo.idcardCountry"
                style="max-height: 92px"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              法人身份证号
            </th>
            <td colspan="3">
              {{ merchInfo.legal_idcard }}
            </td>
          </tr>
        </tbody>
        <thead>
          <tr>
            <th colspan="4">
              线下门店信息
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="160">
              实际经营地址
            </th>
            <td colspan="3">
              <sensitive-field
                :btn-show="!isSuper || (isSuper && viewPlaintextAuth.isAddressPermission)"
                field="real_address"
                field-type="3"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="real_address"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              门店定位
            </th>
            <td colspan="3">
              <sensitive-field
                :btn-show="!isSuper || (isSuper && viewPlaintextAuth.isAddressPermission)"
                field="locate_address"
                field-type="3"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="locate_address"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              门店照片
            </th>
            <td colspan="3">
              <a-space>
                <a-image
                  v-for="(item, index) in merchInfo.companyImages"
                  :key="index"
                  :src="item"
                  style="max-height: 92px"
                />
              </a-space>
            </td>
          </tr>
          <tr>
            <th width="160">
              门店视频
            </th>
            <td colspan="3">
              <video
                v-if="merchInfo.video"
                controls
                :src="merchInfo.video"
              />
            </td>
          </tr>
          <tr>
            <th width="160">
              办公地址租赁合同
            </th>
            <td colspan="3">
              <template v-if="isLeaseContractPdf">
                <div
                  v-for="(leaseContractImageUrl, index) in merchInfo.lease_contract"
                  :key="index"
                  class="lease-contract-pdf"
                  @click="handlePreview(leaseContractImageUrl)"
                >
                  {{ leaseContractImageUrl.split('/').pop() }}
                </div>
              </template>
              <a-space v-else>
                <a-image
                  v-for="(item, index) in merchInfo.lease_contract"
                  :key="index"
                  :src="item"
                  style="max-height: 92px"
                />
              </a-space>
            </td>
          </tr>
        </tbody>
        <!-- 增加面审信息 -->
        <thead>
          <tr>
            <th colspan="4">
              面审信息
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th>是否需要面审</th>
            <td colspan="3">
              <span>{{ Number(merchInfo.is_review) === 1 ? '是' : '否' }}</span>
            </td>
          </tr>
          <tr>
            <th>具体经营地址</th>
            <td colspan="3">
              <sensitive-field
                field="specific_business_address"
                field-type="882"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="specific_business_address"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr>
            <th>日常经营时间</th>
            <td colspan="3">
              <span>{{ merchInfo.daily_operate_hour || '-' }}</span>
            </td>
          </tr>
          <tr>
            <th width="160">
              联系人
            </th>
            <td>
              <sensitive-field
                field="contact"
                field-type="880"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="contact"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
              （是否为法人：{{ merchInfo.is_legal == 1 ? '是' : '否' }})
            </td>

            <th width="160">
              联系电话
            </th>
            <td>
              <sensitive-field
                field="contact_phone"
                field-type="881"
                id-key="server_id"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="contact_phone"
                :row="merchInfo"
                :type="isSuper ? 169 : 1025"
              />
            </td>
          </tr>
          <tr v-if="merchInfo.createdAt || merchInfo.updatedAt">
            <th width="160">
              提交时间
            </th>
            <td>{{ merchInfo.createdAt }}</td>
            <th width="160">
              更新时间
            </th>
            <td>{{ merchInfo.updatedAt }}</td>
          </tr>
          <tr v-if="merchInfo.status">
            <th>审核状态</th>
            <td colspan="3">
              {{ merchInfo.status }}
            </td>
          </tr>
          <tr>
            <th>享有权益</th>
            <td colspan="3">
              <span class="name">
                <a-space :size="16">
                  <a-space :size="4">
                    <safety-certificate-outlined />
                    <span>企业认证</span>
                  </a-space>
                  <a-space :size="4">
                    <shop-outlined />
                    <span>精选商铺</span>
                  </a-space>
                  <a-space :size="4">
                    <setting-outlined />
                    <span>快递维修</span>
                  </a-space>
                  <a-space :size="4">
                    <swap-outlined />
                    <span>7天无忧换机</span>
                  </a-space>
                </a-space>
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <RPreview
      v-model:preview="previewData.visibal"
      :value="previewData.url"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { SafetyCertificateOutlined, SettingOutlined, ShopOutlined, SwapOutlined } from '@ant-design/icons-vue';

import useDecryptSensitive from '@/composables/use-decrypt-sensitive';
import { getUserViewPlaintextAuth } from '@/utils/decrypt-method';

const props = defineProps({
  merchInfo: {
    type: Object,
    default: () => ({}),
  },
});
const route = useRoute();

const role = computed(() => route.query.role || '');
const viewPlaintextAuth = ref({});
const flag = ref(false);
const faceUrlVisible = ref(false);
const backUrlVisible = ref(false);
const licenseUrlVisible = ref(false);

// 计算属性
const invoice = computed(() => {
  let str = '';
  let { invoice } = props.merchInfo;
  if (invoice) {
    if (typeof invoice === 'string') {
      invoice = JSON.parse(invoice);
    }
    const arr = invoice.map(item => (String(item) === '1' ? '普通发票' : '增值税专用发票'));
    str = arr.join('，');
  }
  return str;
});

const files = computed(() => {
  let arr = [];
  let { license_copy_image, license_image_proof } = props.merchInfo;
  if (license_copy_image) arr.push(license_copy_image);
  if (license_image_proof) arr.push(license_image_proof);
  return arr;
});

const isSuper = computed(() => role.value === 'super');
const { decryptSensitiveData } = useDecryptSensitive({ isSuper: isSuper.value });

async function getBtnAuth() {
  const res = (await getUserViewPlaintextAuth()).data;
  viewPlaintextAuth.value = res;
}

function setDefaultVisible() {
  faceUrlVisible.value = false;
  backUrlVisible.value = false;
  licenseUrlVisible.value = false;
}

function lookSensitive(visible, field, field_type, type, params_type) {
  if (visible) {
    if (flag.value) return;
    flag.value = true;
    decryptSensitiveData(null, props.merchInfo.server_id, params_type, field_type, field, data => {
      if (data) {
        if (type === 'face') {
          faceUrlVisible.value = visible;
        } else if (type === 'back') {
          backUrlVisible.value = visible;
        } else {
          licenseUrlVisible.value = visible;
        }
      } else {
        setDefaultVisible();
      }
    });

    setTimeout(() => {
      flag.value = false;
    }, 500);
  } else {
    setDefaultVisible();
  }
}


// 办公地址租赁合同 是否为pdf
const isLeaseContractPdf = computed(() => {
  const { lease_contract } = props.merchInfo;
  if(!lease_contract) return false;
  return /\.pdf(\?|$)/i.test(lease_contract[0] ?? '');
});

const previewData = ref({
  visibal: false,
  url: '',
});
const handlePreview = (url: string) => {
  console.log('=>(info.vue:625) url', url);
  previewData.value = {
    visibal: true,
    url,
  }
};

// 生命周期钩子
onMounted(() => {
  getBtnAuth();
});
</script>

<style scoped lang="less">
i {
  display: inline-block;
}

.pull-right {
  float: right;
}

.container {
  margin-bottom: 24px;
  background: #fff;
}

.charge {
  padding: 0 0 8px 0;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 24px;

  .contact-value {
    padding-right: 24px;
    color: rgba(6, 21, 51, 0.85);
  }

  .btn-file {
    margin-left: 16px;
    color: #3777ff;

    .icon-paperclip {
      width: 16px;
      height: 16px;
      margin-right: 3px;
      vertical-align: middle;
      background: url(https://img1.rrzuji.cn/uploads/scheme/2412/05/m/gMP87kxYPcSGmYJafTNn.png) no-repeat;
      background-size: 100% 100%;
    }

    span {
      font-size: 14px;
      vertical-align: middle;
    }
  }
}

.table {
  font-size: 14px;
  word-break: break-all;
  table-layout: fixed;
  border-color: #e8e8e8;

  thead th {
    height: 54px;
    color: rgba(6, 21, 51, 0.85);
    font-size: 14px;
    background: #f5f5f7;
  }

  tbody th {
    height: 54px;
    color: rgba(6, 21, 51, 0.85);
    font-size: 14px;
    background: #f9f9fb;
  }

  th,
  td {
    padding: 8px 20px;
    color: rgba(6, 21, 51, 0.65);
  }
}

video {
  max-width: 368px;
  max-height: 207px;
}
.lease-contract-pdf {
  color: var(--ant-primary-color);
  cursor: pointer;
}
</style>
