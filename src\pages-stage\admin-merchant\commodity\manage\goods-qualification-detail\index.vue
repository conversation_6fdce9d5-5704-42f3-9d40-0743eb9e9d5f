<template>
  <layout-shops-page
    :content-style="{ padding: '24px' }"
    :title="isRelease ? '商品资质审核记录' : '商品资质详情'"
    :top-style="{ padding: '24px', borderBottom: '1px solid #DADCE0' }"
  >
    <template #title>
      <div
        class="title"
        @click="goBack(EQualificationFileType.GOODS, goStep)"
      >
        <ArrowLeftOutlined style="margin-right: 16px" />
        {{ isRelease ? '商品资质审核记录' : '商品资质详情' }}
      </div>
    </template>
    <header class="header-title">
      资质信息
    </header>
    <div class="form-box">
      <a-descriptions :column="1">
        <template
          v-for="(item, index) in detailsConfig"
          :key="index"
        >
          <a-descriptions-item
            :label="item.label"
            :label-style="item?.key ? {} : timeTitleStyle"
          >
            <template v-if="item.type === 'text'">
              {{ formState[item?.key] }}
            </template>
            <template v-else-if="item.type === 'img'">
              <ImgShow
                img-key="image"
                :imgs="formState[item?.key]"
              />
            </template>
          </a-descriptions-item>
        </template>
      </a-descriptions>
    </div>
    <header class="header-title">
      绑定商品列表
    </header>
    <RTable
      :api="apiGetGoodsInfoList"
      :columns="goodsInfoColumns"
      :search="false"
      :use-table-options="useTableOptions"
    />
  </layout-shops-page>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { RTableUseTableOptions } from 'rrz-web-design';

import { EQualificationFileType } from '@/pages-stage/commodity/dict/qualification/data.d';

import UseCommonFun from '../industry-qualification-edit/composables/use-common-fun';
import { ECurrentDataType } from '../store-info/components/shop-qualification/data.d';
import ImgShow from './components/img-show.vue';
import { goodsInfoColumns } from './config';
import { detailsConfig } from './config';
import { TQualificationDetail } from './data.d';
import { apiGetGoodsInfoList, getGoodsInfoDetailApi } from './service';
const { goBack } = UseCommonFun();
const timeTitleStyle = {
  marginLeft: '47px',
  fontWeight: '500',
  color: 'rgba(6,21,51,0.85)',
  fontSize: '14px',
};
const isRelease = computed(() => route.query?.dataType === ECurrentDataType.RELEASE);
const useTableOptions: RTableUseTableOptions = {
  pageKey: 'page_no',
  extraParams() {
    return {
      credential_id: detailId.value,
    };
  },
};
const route = useRoute();
const detailId = computed(() => route.query?.id);
const goStep = computed(() => Number(route.query?.go));

const formState = ref<TQualificationDetail>({
  config_name: '',
  customize_name: '',
  server_credential_goods_image: [],
  valid_start_at_label: '',
  valid_end_at_label: '',
  reject_reason: '',
});
async function getGoodsInfoDetail() {
  const { data } = await getGoodsInfoDetailApi({ id: detailId.value });
  const currentKey = isRelease.value ? 'release_goods_version' : 'use_goods_version';
  const info = data[currentKey];
  Object.assign(formState.value, { ...data, ...info });
}

onMounted(() => {
  if (detailId.value) {
    getGoodsInfoDetail();
  }
});
</script>

<style scoped lang="less">
.title {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  text-align: left;
}
.alert-wrapper {
  width: 100%;
  margin-bottom: 24px;
  padding: 16px;
  word-wrap: break-word;
  background: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  h4 {
    margin-bottom: 2px;
    color: #ff4d4f;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
  p {
    margin-bottom: 2px;
  }
}
.header-title {
  margin-bottom: 20px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 16px;
}
.header-title::before {
  position: relative;
  top: 2px;
  right: 5px;
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #3777ff;
  border-radius: 20px;
  content: '';
}
.form-box {
  width: 50%;
  :deep(.ant-upload-list) {
    width: 600px;
  }
  :deep(.ant-descriptions-item-label) {
    margin-right: 24px;
    margin-left: 60px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  :deep(.ant-descriptions-item-label)::after {
    margin: 0;
    content: '';
  }
  :deep(.ant-descriptions-item-content) {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
