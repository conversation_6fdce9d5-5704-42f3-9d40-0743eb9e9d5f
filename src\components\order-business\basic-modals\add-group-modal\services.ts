import { GET, POST } from '@/services/api';

/** 订单绑定的分组列表 */
export function getOrderGroupBindList(params: { order_id: string }) {
  return GET('/order/V2OrderServerGroupBind/query', params, { hostType: 'Golang' });
}

/** 绑定订单分组 */
export function bindOrderGroup(data: any) {
  const params = {
    ids: data.ids,
    order_id: Number(data.order_id),
  };
  return POST('/order/V2OrderServerGroupBind/updateList', params, { hostType: 'Golang' });
}
