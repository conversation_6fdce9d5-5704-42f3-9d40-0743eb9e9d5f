<template>
  <a-modal
    v-model:visible="bindVisible"
    class="transport-model"
    title="运输服务详情"
    :width="1000"
  >
    <div class="table">
      <a-spin
        :spinning="loading"
        tip="加载中..."
      >
        <template
          v-for="key in Object.keys(expressData)"
          :key="key"
        >
          <div class="row-title">
            {{ rowTitle[key] }}
          </div>
          <div class="thead row">
            <div class="th">
              寄送类型
              <question-circle-filled
                style="margin-left: 10px"
                @click="handleSendType"
              />
            </div>
            <div class="th">
              物流单号
            </div>
            <div class="th">
              支付金额
            </div>
            <div class="th">
              操作
            </div>
          </div>
          <a-collapse
            v-model:activeKey="activeKey"
            class="tbody"
            ghost
            @change="detail.change"
          >
            <a-collapse-panel
              v-for="(item, index) in expressData[key]"
              :key="key + index"
              :show-arrow="false"
            >
              <template #header>
                <div class="row">
                  <div class="td">
                    {{ item.express_type_title }}
                  </div>
                  <div class="td">
                    {{ item.waybill_no }}
                  </div>
                  <div class="td">
                    {{ item.total }}
                  </div>
                  <div class="td">
                    <div class="btn-group">
                      <a class="text-link">
                        {{ activeKey.includes(key + index) ? '收起' : '展开' }}
                      </a>

                      <template v-if="key === 'server_send' && !isSuper && extraData.express_type === 1">
                        <a-tooltip :title="isNotWaitSend ? emdTips || '订单已发货' : null">
                          <a
                            :class="['text-link', { 'disabled-link': isNotWaitSend }]"
                            @click="e => handleBarCode(e, index)"
                          >
                            查看条形码
                          </a>
                        </a-tooltip>
                        <a-tooltip :title="isNotWaitSend ? emdTips || '订单已发货，不允许重新预约' : null">
                          <a
                            :class="['link-warning', { 'disabled-link': isNotWaitSend }]"
                            @click="e => handleAgainBook(e, index)"
                          >
                            重新预约取件
                          </a>
                        </a-tooltip>
                        <a-tooltip :title="isNotWaitSend ? emdTips || '订单已发货，不允许取消寄件' : null">
                          <a
                            :class="['link-danger', { 'disabled-link': isNotWaitSend }]"
                            @click="e => handleCancelServer(e, index, 'sf')"
                          >
                            取消寄件
                          </a>
                        </a-tooltip>

                        <a
                          :class="[{ 'disabled-link': isPlatformOrder || isNotWaitSend }]"
                          @click="e => handlePrint(e, index, isPlatformOrder, isNotWaitSend)"
                        >
                          打印快递单
                        </a>
                      </template>

                      <template v-if="key === 'server_send' && !isSuper && extraData.express_type === 2">
                        <a-tooltip :title="isNotWaitSend ? emdTips || '订单已发货，不允许取消寄件' : null">
                          <a
                            :class="['link-danger', { 'disabled-link': isNotWaitSend }]"
                            @click="e => handleCancelServer(e, index, 'jd')"
                          >
                            取消寄件
                          </a>
                        </a-tooltip>
                      </template>
                    </div>
                  </div>
                </div>
              </template>
              <sf-transport-detail
                v-if="extraData.express_type === 1"
                :express-data-key="key"
                :have-tips="!!emdTips"
                :item="item"
                :loading="item.loading"
                :order-id="orderId"
                :order-status="orderStatus"
                type="sf"
              />
              <jd-transport-detail
                v-if="extraData.express_type === 2"
                :express-data-key="key"
                :have-tips="!!emdTips"
                :item="item"
                :loading="item.loading"
                :order-id="orderId"
                :order-status="orderStatus"
                type="jd"
              />
            </a-collapse-panel>
            <div class="empty-box">
              <a-empty
                v-show="!expressData[key].length"
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
              />
            </div>
          </a-collapse>
        </template>
      </a-spin>
    </div>
    <!-- 打印区域 -->
    <div
      v-if="printDataList.length"
      ref="printContainerRef"
      class="print-container"
    >
      <div
        v-for="printDataItem in printDataList"
        :key="printDataItem.waybill_no"
      >
        <PrintItem
          v-if="printDataItem.printingData?.waybill_no"
          class="print"
          :data="printDataItem.printingData"
          :print-date="printDate"
        />
      </div>
    </div>
    <template #footer>
      <a-button
        type="primary"
        @click="submit"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>

  <a-modal
    v-model:visible="sendTypeVisible"
    :body-style="{ color: 'rgb(6, 21, 51)' }"
    title="寄送类型说明"
    :width="1000"
  >
    <div>
      <strong>寄出包邮：</strong>
      寄出商家包邮，从商家租金扣除运费。
    </div>
    <div>
      <strong>寄出自付：</strong>
      若商家使用的是统一运费
      或运费模板，平台会将用户在首期账单中支付的运费结算至商家租金账户，发货时使用平台运输服务的费用将从商家租金中扣除。
    </div>
    <div>
      <strong>寄出到付：</strong>
      商家寄出后，由用户承担运费，平台代扣。
    </div>
    <div>
      <strong>归还包邮：</strong>
      用户归还时选择顺丰服务，从商家租金扣除运费，增值服务与商品发布时的选择一致。
    </div>
    <div>
      <strong>归还自付：</strong>
      用户归还时选择顺丰服务，增值服务用户自选，由用户承担运费，平台代扣。
    </div>
    <div>
      <strong>用户拒收：</strong>
      商家寄出包邮的情况下，用户拒收造成快递返程，返程运费从商家租金扣除。
    </div>
    <div>
      <strong>用户拒收 (用户支付)：</strong>
      寄出自付的情况西，用户拒收造成快递返程，由用户承担返程运费。
    </div>
    <div>
      <strong>售后包邮：</strong>
      用户售后退换货时选择顺丰服务，从商家租金扣除运费，增值服务与商品发布时的选择一致。
    </div>
    <div>
      <strong>售后自付：</strong>
      用户售后退换货时选择顺丰服务，增值服务用户自选，由用户承担运费，平台代扣。
    </div>
    <template #footer>
      <a-button
        type="primary"
        @click="sendTypeVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>

  <!-- 查看条形码弹窗 -->
  <BarCodeModal ref="barCodeModalRef" />

  <!-- 重新预约取件 -->
  <PickUpServiceModal
    v-model:visible="serviceModalVisible"
    :change-id="changeId"
    :extra-data="extraData"
    :is-take="isTake"
    :order-id="currentOrderId"
    :sf-pick-up-type="2"
    @refresh="handleRePickupSuccess"
  />
</template>

<script setup lang="ts">
import { computed, createVNode, h, type PropType, provide, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Empty, message, Modal } from 'ant-design-vue';
import { QuestionCircleFilled } from '@ant-design/icons-vue';

import PickUpServiceModal from '@/components/order-business/operate-modals/order/pickup-appoint-service-modal/pick-up-service-modal/index.vue';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

import BarCodeModal from './components/bar-code-modal.vue';
import JdTransportDetail from './components/jd-transport-detail/index.vue';
import PrintItem from './components/print-item/index.vue';
import SfTransportDetail from './components/sf-transport-detail/index.vue';
import useDetail from './composables/use-detail';
import usePrint from './composables/use-print';
import { IExpressData } from './data';
import {
  cancelJdPickUp,
  cancelSfPickUp,
  getJDTransportList,
  getSFTransportList,
  superGetJDTransportList,
} from './services';

type TExpressType = 'sf' | 'jd';

interface IExtraData {
  express_type?: number;
  is_wait_pay?: boolean;
  server_address_list?: { id: string; province: string; city: string; address: string }[];
  phone?: string;
  imei_code_data?: { code: string; sort: string }[];
  item_array?: number[];
  is_open_notarization_tips?: boolean;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
  orderId: {
    type: String,
    default: '',
  },
  orderStatus: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const expressData = ref<IExpressData>({
  server_send: [],
  user_return: [],
  after_sale_return: [],
});
const sendTypeVisible = ref<boolean>(false);
const activeKey = ref<string[]>([]);
const isSuper = useRoute().query.role === 'super';
//当前订单是在代发列表，且代发状态不等于已取消时
const emdTips = ref('');
const isNotWaitSend = computed(() => {
  /** 待发货或emdTips */
  return props.orderStatus !== 2 || emdTips.value;
});

const barCodeModalRef = ref();

const serviceModalVisible = ref(false);
const currentOrderId = ref('');
const changeId = ref(0);
const isTake = ref(0);
// 是否为平台订单
const isPlatformOrder = ref(false);

provide('dispatch', dispatch);

const detail = computed(() => {
  console.log(props.extraData.express_type)
  return useDetail({
    orderId: props.orderId,
    type: props.extraData.express_type === 1 ? 'sf' : 'jd',
    data: expressData.value,
  });
});

const rowTitle = {
  server_send: '商家寄出',
  user_return: '用户归还',
  after_sale_return: '用户售后',
};

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loading.value = true;
      const api = {
        1: getSFTransportList,
        2: isSuper ? superGetJDTransportList : getJDTransportList,
      }[props.extraData.express_type];
      api?.({
        order_id: props.orderId,
      })
        .then(res => {
          emdTips.value = res.data?.is_platform_order && !isSuper ? '该订单已交由平台发货，如需自行发货可操作“发货拦截”功能' : '';
          expressData.value.server_send = res.data.server_send || [];
          expressData.value.user_return = res.data.user_return || [];
          expressData.value.after_sale_return = res.data.after_sale_return || [];
          isPlatformOrder.value = res.data?.is_platform_order;
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      activeKey.value = [];
    }
  },
  { immediate: true },
);

function submit() {
  bindVisible.value = false;
}

const handleSendType = () => {
  sendTypeVisible.value = true;
};

const handleBarCode = async (e: Event, index: number) => {
  e.stopPropagation();
  if (isNotWaitSend.value) {
    return;
  }
  await detail.value.change(['server_send' + index]);
  barCodeModalRef.value?.open(expressData.value.server_send[index].waybill_no);
};

const handleAgainBook = async (e: Event, index: number) => {
  e.stopPropagation();
  if (isNotWaitSend.value) {
    return;
  }
  await detail.value.change(['server_send' + index]);
  const item = expressData.value.server_send[index];
  openBook(item.order_id, item.data.service.list[0].is_take, item.data.id);
};

const handleCancelServer = async (e: Event, index: number, type: TExpressType) => {
  e.stopPropagation();
  if (isNotWaitSend.value) {
    return;
  }
  await detail.value.change(['server_send' + index]);
  cancelService(type, expressData.value.server_send[index].order_id);
};

const printContainerRef = ref(null);
const { handlePrint, printDataList, printDate } = usePrint({
  expressData: expressData,
  printContainerRef: printContainerRef,
})

function handleRefresh(orderId: string, dataKey?: TRefreshDataKey[]) {
  emit('refresh', orderId, dataKey);
}

function handleRePickupSuccess(orderId: string, dataKey?: TRefreshDataKey[]) {
  handleRefresh(orderId, dataKey);
  serviceModalVisible.value = false;
  bindVisible.value = false;
}

function dispatch(action: string, ...args: any[]) {
  if (action === 'cancel') {
    cancelService(...args);
  } else if (action === 'book') {
    openBook(...args);
  }
}

function cancelService(type: TExpressType, orderId: string) {
  const isJD = type === 'jd';
  const content = isJD
    ? '确定取消京东上门取件服务吗？'
    : h('div', [
        h('span', { style: { marginBottom: '6px' } }, '当前订单已成功预约，取消寄件产生的后果将由'),
        h('span', { style: { color: '#ff4d4f' } }, '商家'),
        h('span', '承担，还要继续吗？'),
      ]);
  Modal.confirm({
    title: '取消寄件提示',
    icon: createVNode(QuestionCircleFilled),
    content,
    onOk: async () => {
      const api = isJD ? cancelJdPickUp : cancelSfPickUp;
      const res = await api({ order_id: props.orderId });
      handleRefresh(orderId, ['data']);
      bindVisible.value = false;
      message.success(res.message);
    },
  });
}

function openBook(order_id: string, is_take: string, change_id: string) {
  currentOrderId.value = order_id;
  isTake.value = is_take;
  changeId.value = change_id;
  serviceModalVisible.value = true;
}
</script>

<style scoped lang="less">
.row-title {
  margin-bottom: 12px;
  color: rgb(0, 0, 0);
  font-weight: bold;
}

.thead {
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.row {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #f0f0f0;

  .th,
  .td {
    display: flex;
    align-items: center;
    width: 25%;
    padding: 12px;
    border-right: 1px solid #f0f0f0;

    :deep(.ant-btn) {
      padding: 4px 0;
    }
  }

  .th:first-child,
  .td:first-child {
    border-left: 1px solid #f0f0f0;
  }
}

.empty-box {
  display: flex;
  justify-content: center;
}

.table {
  max-height: 70vh;
  padding-right: 16px;
  overflow-y: auto;

  :deep(.ant-collapse) {
    .ant-collapse-header {
      padding: 0;
    }

    .ant-collapse-content-box {
      padding: 12px;
      background-color: #ececec;
    }
  }

  .tbody {
    margin-bottom: 12px;
  }
}

.btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0 8px;
}

.link-warning {
  color: #eea236;
}

.link-danger {
  color: #ff4d4f;
}

.disabled-link {
  color: #ccc !important;
}

.print-container {
  position: fixed;
  top: -1000px;
  left: -1000px;
  width: 100%;
}
</style>
