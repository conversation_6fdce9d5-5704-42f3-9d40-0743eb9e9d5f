export interface ISearchParams {
  orderStatus: string | number;
  orderId: string;
  date: any[];
  tracking_num: string;
  batch_imei?: string | null;
  batch_no?: number;
  recycle_mode?: number;
  category_id: any;
  complete_time: any[];
}

export interface ICouponInfo {
  // 营销券Id
  couponMarketId: string;
  // 营销券名称
  couponName: string;
  // 营销券加价力度
  discount: string;
  // 加价金额
  money: string;
}

export interface IOrderList {
  canCloseOrder: number;
  canSetPurchasedPrice: number;
  evaluate_price: string;
  goodsInfo: [];
  logisticInfo: {
    addr: string;
    tracking_num: string;
    return_tracking_num: string;
  };
  orderId: string;
  orderInfo: {
    orderId: string;
    createAt: string;
  };
  order_status: string;
  order_status_text: string;
  purchased_price: string;
  showBtnViewQuaReport: number;
  userInfo: {
    userId: string;
    phone: string;
    paymentAccount: string;
    paymentName: string;
  };
  remarks?: any;
  evaluateDetail?: ICouponInfo;
  purchasedDetail?: ICouponInfo;
  // 自定义增加 营销券字段
  couponInfo?: null | ICouponInfo;
  // 是否展示【查看账单】
  showOrderPayBtn?: number;
}

export interface IOptionItem<T> {
  label: string;
  value: T;
  level?: number;
  isLeaf?: boolean;
  child?: IOptionItem<T>[];
}

export interface IDataStatistics {
  title: string;
  tips: string[];
  wait_pay_num: number;
  finish_pay_num: number;
}
