<template>
  <a-space :size="0">
    <a-button
      :disabled="historyIndex <= 0"
      :icon="h(ArrowLeftOutlined)"
      size="small"
      @click="handleLeft"
    />
    <span style="color: rgba(6, 21, 51, 0.65)">加载上次搜索</span>
    <a-button
      :disabled="historyIndex >= historyLength - 1"
      :icon="h(ArrowRightOutlined)"
      size="small"
      @click="handleRight"
    />
  </a-space>
</template>

<script lang="ts" setup>
import { computed, h } from 'vue';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons-vue';
import { useSearchHistory } from '@/composables/use-search-history';

const emit = defineEmits<{
  (e: 'change', value: number): void;
}>();

const { historyData, historyIndex } = useSearchHistory();

const historyLength = computed(() => historyData.value.length);

function handleLeft() {
  emit('change', historyIndex.value - 1);
}

function handleRight() {
  emit('change', historyIndex.value + 1);
}
</script>

<style lang="less" scoped>
:deep(.ant-btn) {
  background: none;
  border: none;
}
</style>
