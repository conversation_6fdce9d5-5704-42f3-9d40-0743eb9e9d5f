import { ref, reactive } from 'vue';
import { IRuleConfigItem } from '../data';
import { message } from 'ant-design-vue';
import { menuTreeOptions, hashMenuTreeOptions } from '../config';
interface ICategoryData {
  id: string;
  value: string[];
}
export function useCategoryModal() {
  const categoryVisible = ref(false);
  const categoryLoading = ref(false);
  // 当前整个列表当中已经选择的id
  const selectedCategory = ref<string[]>([]);
  // 当前点击的那个item
  const categoryData = reactive<ICategoryData>({
    id: '',
    value: [],
  });
  // 当前数据的响应式数据保存在这
  const itemProxyData = ref();
  /**
   *
   * @param mapId 系统中已经选择过的id
   * @returns 配置项
   */
  function handleTreeOptions(mapId: string[]) {
    return menuTreeOptions.value.map(item => {
      if (item.disabled && !item.children.length) {
        return item;
      }
      let childDisabledNum = 0;
      return {
        ...item,
        children: item.children.map((child: any) => {
          const disabled =
            mapId.includes(child.value) ||
            (selectedCategory.value.includes(child.value) && !categoryData.value.includes(child.value));
          disabled && (childDisabledNum += 1);
          return {
            ...child,
            disabled,
          };
        }),
        disabled: childDisabledNum === item.children.length,
      };
    });
  }
  function openCategoryModal(item: IRuleConfigItem, mapId: string[]) {
    const { id, configMap } = item;
    itemProxyData.value = item;
    categoryData.id = id;
    categoryData.value = configMap.map(item => item.id);
    categoryVisible.value = true;
    // mapId过滤掉value中的id
    mapId = mapId.filter(item => !categoryData.value.includes(item));
    const res = handleTreeOptions(mapId);
    menuTreeOptions.value = res;
  }
  function addCategoryRule() {
    const selectedIds: string[] = [];
    itemProxyData.value.configMap = categoryData.value.map((id: string) => {
      selectedIds.push(id);
      return { ...hashMenuTreeOptions[id], rule_id: categoryData.id };
    });
    selectedCategory.value = selectedIds;
    categoryVisible.value = false;
    message.success('添加成功');
  }

  return {
    categoryVisible,
    categoryLoading,
    categoryData,
    selectedCategory,
    openCategoryModal,
    addCategoryRule,
  };
}
