import { GET, POST } from '@/services/api';
// 归还地址
export function getListApi() {
  return GET('/return-address/index-inf');
}

// 增加地址接口
export function addReturnAddress(params: {
  address: string;
  union_id: string[] | number[];
  phone: string;
  name: string;
}): Promise<any> {
  return POST('/return-address/add', params);
}

// 修改地址接口
export function updateReturnAddress(data: {
  address: string;
  union_id: string | number;
  phone: string;
  name: string;
  id: string | number;
}): Promise<any> {
  return POST(`/return-address/update?id=${data.id}`, data);
}

// 修改地址接口
export function deleteReturnAddress(params: { id: string | number }): Promise<any> {
  return POST('/return-address/delete', params);
}
