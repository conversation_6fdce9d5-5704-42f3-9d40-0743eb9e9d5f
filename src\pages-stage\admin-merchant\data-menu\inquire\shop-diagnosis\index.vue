<template>
  <layout-shops-page title="店铺诊断">
    <a-spin :spinning="loading">
      <div
        v-if="shopData.perform"
        class="shop-diagnosis"
      >
        <div class="shop-condition">
          <div class="condition-header">
            <div class="condition-header-left">
              <img
                alt=""
                src="https://img1.rrzuji.cn/uploads/scheme/2412/11/m/1eLHTxouxaASZ6dsTKOg.png"
                :width="20"
              >

              <div class="condition-header-left-title">
                店铺情况：
              </div>
              <div
                class="condition-header-left-status"
                :style="{
                  color: dataObj.notify_type[shopData.overview.risk_level]?.color,
                }"
              >
                {{ dataObj.notify_type[shopData.overview.risk_level]?.label }}
              </div>

              <a-tooltip placement="topLeft">
                <template #title>
                  {{ dataObj.notify_type[shopData.overview.risk_level]?.tooltip_text }}
                </template>
                <QuestionCircleOutlined class="condition-header-left-icon font-black-45" />
              </a-tooltip>

              <div class="font-black-45">
                (更新时间：{{ shopData.overview.updated_at_txt }})
              </div>
            </div>

            <a-button
              :loading="loading"
              type="primary"
              @click="reload"
            >
              重新诊断
            </a-button>
          </div>

          <div class="condition-list">
            <section
              class="condition-list-item"
              style="width: 100%"
            >
              <div class="section-top">
                <div class="section-top-title">
                  商家营业情况
                </div>
              </div>

              <div class="section-list">
                <template
                  v-for="(item, index) in shopData.business.moduleLines"
                  :key="index"
                >
                  <ListInfo
                    v-if="!(item.indicator === 12 && item.notifyType === 3)"
                    :data-obj="dataObj"
                    info-class="section-list-item"
                    :item="item"
                  >
                    <template #right>
                      <span
                        class="link-btn"
                        :data-sensors_indicator_val="item.indicator"
                        @click="toLink(item.jumperLink, item.indicator)"
                      >{{ item.jumper_name }}</span>
                    </template>

                    <div
                      v-if="item.indicator === 12"
                      class="phone-advise font-black-45"
                    >
                      根据监管部门的虚拟号备案要求，商家需完善平台登记的客服手机号的相关资料；如未提交运营商将对相关号码的隐私号实施限呼拦截或关停，用户将无法通过虚拟号联系您。
                    </div>
                  </ListInfo>
                </template>
              </div>
            </section>
          </div>
        </div>
        <div class="early-warning">
          <div class="early-warning-header">
            <img
              alt=""
              src="https://img1.rrzuji.cn/uploads/scheme/2412/11/m/ZY1tWMBxwyCp8OA2mdNF.png"
              :width="20"
            >

            <div class="early-warning-header-title">
              商家经营情况
            </div>
          </div>

          <template
            v-for="(item, index) in shopData.operation.moduleLines"
            :key="index"
          >
            <ListInfo
              v-if="item.indicator === 6 || item.notifyType !== 3"
              :colon="false"
              :data-obj="dataObj"
              info-class="warning-item"
              is-large
              :item="item"
            >
              <template #advice>
                &nbsp;
                <!-- 商家违规 -->
                <template v-if="item.indicator === 4">
                  <span class="font-black-45">共检测到<span class="red-color">&nbsp;{{ item.value?.length || 0 }}&nbsp;</span>条进行中的违规信息</span>
                </template>
                <!-- 商家预警 -->
                <template v-if="item.indicator === 5">
                  <span class="font-black-45">共检测到
                    <span class="red-color">{{ warningData?.warning_num_info.all_warning_num }}&nbsp;</span>条预警 (
                    <template
                      v-for="tab in tabItems"
                      :key="tab.value"
                    >
                      <span
                        class="link-tab"
                        @click="tabClick(tab.value)"
                      >{{ tab.label }}</span>:&nbsp;
                      <span class="font-black-85">{{
                        warningData?.warning_num_info[
                          tab.value === EBasicIndicator.All
                            ? 'daily_operation_warning_num'
                            : tab.value + '_warning_num'
                        ]
                      }}&nbsp;
                      </span>
                    </template>
                    )
                  </span>
                </template>
                <!-- 活动报名不足 -->
                <template v-if="item.indicator === 7">
                  <span class="font-black-45">检测到符合报名条件的活动共<span class="font-black-85">&nbsp;{{ item.value[0].can_activity_num }}&nbsp;</span>个已报名<span class="font-black-85">&nbsp;{{ item.value[0].activity_num }}&nbsp;</span>个</span>
                </template>

                <!-- 商家冷启动规则 -->
                <template v-if="item.indicator === 9">
                  <span class="font-black-45">受影响结果：冷启动订单未处理完无法继续获得订单</span>
                </template>

                <!-- 订单奖励兑换券 -->
                <template v-if="item.indicator === 10">
                  <span class="font-black-45">请注意奖励订单兑换时间，谨防过期</span>
                </template>

                <!-- 客诉工单 -->
                <template v-if="item.indicator === 11">
                  <span class="font-black-45">请及时处理客诉工单</span>
                </template>
              </template>
              <template #right>
                <a-button
                  v-if="![5, 6, 8, 13].includes(item.indicator)"
                  :data-sensors_indicator_val="item.indicator"
                  ghost
                  style="background-color: #fff"
                  type="primary"
                  @click="toLink(item.jumperLink, item.indicator)"
                >
                  {{ item.jumper_name }}
                </a-button>
              </template>

              <a-spin :spinning="warnLoading">
                <div class="warning-content">
                  <!-- 商家违规 -->
                  <template v-if="item.indicator === 4">
                    <OutOfLine
                      v-for="(valueItem, valueIndex) in item.value"
                      :key="valueIndex"
                      :affect-value-item="item.affect_of_value[valueIndex]"
                      :data-obj="dataObj"
                      :item="valueItem"
                    >
                      <a-tag
                        class="tag"
                        :style="{
                          color: dataObj.violation_status[valueItem.status]?.color,
                          borderColor: dataObj.violation_status[valueItem.status]?.color,
                          backgroundColor: dataObj.violation_status[valueItem.status]?.backgroundColor,
                        }"
                      >
                        {{ dataObj.violation_status[valueItem.status]?.label }}
                      </a-tag>

                      <span
                        class="font-black-65"
                        style="margin-right: 16px"
                      >{{ valueItem.violation_id }}-{{ valueItem.title_content }}-{{ valueItem.content }}</span>
                    </OutOfLine>
                  </template>
                  <!-- 商家预警 -->
                  <template v-if="item.indicator === 5">
                    <WarningInfo
                      v-for="warnItem in warningData?.warning_daily_operation.indicator_warning_items"
                      :key="warnItem.hit_id"
                      :indicator-val="item.indicator"
                      :item="warnItem"
                      :rule-path="warningData?.others.period_num_rule_path || ''"
                      style="margin-bottom: 16px"
                      :tab-active-key="tabActiveKey"
                    />
                  </template>

                  <!-- 商家考核 -->
                  <template v-if="item.indicator === 6">
                    <ShopGrade
                      ref="shopGradeRef"
                      :data-obj="dataObj"
                      :module-item="item"
                    />
                  </template>
                  <!-- 机型报名不足 -->
                  <template v-if="item.indicator === 8">
                    <div
                      v-for="(valueItem, valueIndex) in item.value"
                      :key="valueIndex"
                      class="font-black-65"
                    >
                      {{ valueItem.activity_id }}-
                      {{ valueItem.title }}
                      机型报名率{{ valueItem.accept_new_rate.toFixed(2) }}%
                      <a-button
                        v-if="valueItem.accept_new_rate !== 100"
                        :data-sensors_indicator_val="item.indicator"
                        type="link"
                        @click="linkToActive(valueItem.activity_id)"
                      >
                        报名更多机型
                      </a-button>
                    </div>
                  </template>
                  <!-- 商家冷启动规则 -->
                  <template v-if="item.indicator === 9">
                    需要处理的订单如下
                    <div v-if="item.value && item.value.length">
                      <a-button
                        v-for="orderItem in item.value[0].order_ids"
                        :key="orderItem"
                        class="left-link"
                        type="link"
                        @click="linkToOrder(orderItem)"
                      >
                        {{ orderItem }}
                      </a-button>
                    </div>
                  </template>

                  <!-- 订单奖励兑换券 -->
                  <template v-if="item.indicator === 10">
                    <div
                      v-for="(valueItem, valueIndex) in item.value"
                      :key="valueIndex"
                      class="font-black-65"
                    >
                      {{ valueIndex + 1 }}. {{ valueItem.coupon_name }}（{{
                        dataObj['coupon_type'][valueItem.coupon_type]
                      }}）{{ valueItem.reward_order_num }}单-有效期：{{
                        dayjs.unix(valueItem.receive_at).format('YYYY-MM-DD HH:mm:ss')
                      }}~{{ dayjs.unix(valueItem.estimate_expire_at).format('YYYY-MM-DD HH:mm:ss') }}
                    </div>
                  </template>

                  <!-- 客诉工单 -->
                  <template v-if="item.indicator === 11 && item.value?.length">
                    <div
                      v-if="item.value[0].all_num"
                      class="font-black-65"
                    >
                      尚未受理的客诉工单数量：{{ item.value[0].all_num }}条
                    </div>
                    <div
                      v-if="item.value[0].timeout_num"
                      class="font-black-65"
                    >
                      尚未受理的客诉工单数量（已超时）：{{ item.value[0].timeout_num }}条
                    </div>
                  </template>

                  <!-- 订单管控预警 -->
                  <div v-if="item.indicator === 13">
                    <OrderControl
                      v-if="item.value[0].reject_order && item.value[0].reject_order.length"
                      desc="管控订单驳回未处理："
                      :order-list="item.value[0].reject_order"
                      title="以下管控订单-取消工单已被驳回，建议尽快跟进转化，确保符合规范"
                    />
                    <OrderControl
                      v-if="item.value[0].timeout_order && item.value[0].timeout_order.length"
                      desc="驳回未处理管控订单："
                      :order-list="item.value[0].timeout_order"
                      title="以下管控订单-临近决策超时节点，建议尽快处理订单，确保符合规范"
                    />
                  </div>
                </div>
              </a-spin>
            </ListInfo>
          </template>
        </div>
      </div>

      <REmpty
        v-else
        style="margin-top: 120px"
      />
    </a-spin>
  </layout-shops-page>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { useTopWindow } from '@/hook/common/use-top-window';

import ListInfo from './components/list-info.vue';
import OrderControl from './components/order-control.vue';
import OutOfLine from './components/out-of-line.vue';
import ShopGrade from './components/shop-grade.vue';
import WarningInfo from './components/warning-info.vue';
import { EBasicIndicator, statusMap, tabItems, violationStatusMap } from './config';
import { IDataObj, IShopData, IWarningRecord } from './data.d';
import { apiGetOptions, apiGetShopData, apiGetWarningRecords, apiReGenerate } from './service';

/* 默认打开全部页 */
const tabActiveKey = ref(EBasicIndicator.All);
const searchText = ref<string>('');
/* 预警数据 */
const warningData = ref<IWarningRecord>();
const warnLoading = ref<boolean>(false);

const loading = ref<boolean>(false);

// 请求预警数据
function getWarningList() {
  let is_pre_deposit = false;
  let basic_indicator: EBasicIndicator | undefined = tabActiveKey.value;
  if (tabActiveKey.value === EBasicIndicator.PreDeposit) {
    is_pre_deposit = true;
  }
  if (tabActiveKey.value === EBasicIndicator.All || tabActiveKey.value === EBasicIndicator.PreDeposit) {
    basic_indicator = undefined;
  }

  warnLoading.value = true;
  const params = { warning_type: 2, basic_indicator, search_key_word: searchText.value, is_pre_deposit };
  apiGetWarningRecords(params)
    .then(res => {
      warningData.value = res.data || {};
    })
    .finally(() => {
      warnLoading.value = false;
    });
}

const shopData = ref<IShopData>({
  business: {
    module: 1,
    moduleLines: [],
  },
  operation: {
    module: 2,
    moduleLines: [],
  },
  overview: {
    risk_level: 1,
    updated_at_txt: '--',
  },
  perform: false,
});
const dataObj = reactive<IDataObj>({
  exception_result_type: {},
  item: {},
  notify_type: {},
  penalty_content_type: {},
  punishment_type: {},
  risk_level: {},
  subject: {},
  indicator: {},
  violation_status: {},
  coupon_type: {},
});
const initData = () => {
  loading.value = true;
  Promise.all([apiGetOptions(), apiGetShopData()])
    .then(res => {
      Object.keys(res[0].data).forEach(key => {
        const obj: Record<string, any> = {};
        res[0].data[key].opts.forEach((i: any) => {
          if (key === 'notify_type' || key === 'violation_status') {
            const map = key === 'notify_type' ? statusMap : violationStatusMap;
            obj[i.val] = {
              ...map[i.val],
              label: i.cn_name,
            };
          } else {
            obj[i.val] = i.cn_name;
          }
        });
        dataObj[key as keyof IDataObj] = obj;
      });
      shopData.value = res[1].data;
    })
    .finally(() => {
      loading.value = false;
    });
};

function tabClick(value: EBasicIndicator) {
  tabActiveKey.value = value;
  getWarningList();
}

function toLink(url: string, indicator: number) {
  if ([11, 12].includes(indicator)) {
    window.sessionStorage.setItem('SHOP_DISAGNOSIS_BLANK', '1');
  }
  window.open(url, '_blank');
}

const shopGradeRef = ref<any>(null);

function reload() {
  Modal.confirm({
    title: '提示',
    icon: h(ExclamationCircleOutlined),
    content: '确认重新诊断吗？',
    onOk() {
      loading.value = true;
      apiReGenerate()
        .then(() => {
          initData();
          getWarningList();
          shopGradeRef.value && shopGradeRef.value[0].loadData();
          message.success('操作成功');
        })
        .finally(() => {
          loading.value = false;
        });
    },
  });
}

const { navigate } = useTopWindow();

function linkToActive(activity_id: number | string) {
  navigate('blank', `/commodity-promote-v2/index?activityId=${activity_id}&accept_new=2`);
}

function linkToOrder(order_id: number) {
  navigate('blank', `/order/v4-order-index?order_id=${order_id}&orderCache=1`);
}

onMounted(() => {
  initData();
  getWarningList();
});
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

.font-black-45 {
  color: @font-black-45;
}

.font-black-65 {
  color: @font-black-65;
}

.font-black-85 {
  color: @font-black-85;
}

.red-color {
  color: #ff4d4f;
}

.left-link {
  margin-right: 8px;
  font-size: 12px;
  padding-inline: 0;

  :deep(span) {
    text-decoration: underline;
  }
}

.shop-diagnosis {
  padding: 0 24px 24px;

  .shop-condition {
    background-image: url('https://img1.rrzuji.cn/uploads/scheme/2412/11/m/6e7bDBY6wIjOEWIq8blB.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .condition-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;

      &-left {
        display: flex;
        align-items: center;

        &-title {
          margin-left: 5px;
          font-weight: bold;
          font-size: 16px;
        }

        &-status {
          font-weight: bold;
          font-size: 16px;
        }

        &-icon {
          margin: 0 16px 0 6px;
        }
      }
    }

    .condition-list {
      display: flex;
      gap: 8px;

      &-item {
        display: flex;
        flex-direction: column;
        padding: 16px 0;
        background: #fff;
        border: 1px solid #eeeff3;
        border-radius: 8px;
      }

      .section-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        background: linear-gradient(90deg, #f2f8ff 1%, #fff 100%);

        &-right {
          padding: 0;
        }

        &-title {
          position: relative;
          padding: 2px 0;
          font-weight: bold;

          &::before {
            position: absolute;
            top: 50%;
            left: -16px;
            width: 4px;
            height: 20px;
            background: #3777ff;
            border-radius: 4px;
            transform: translate(0, -50%);
            content: '';
          }
        }
      }

      .section-list {
        flex: 1;
        padding: 16px 12px;

        .link-btn {
          color: #3777ff;
          font-size: 12px;

          &:hover {
            text-decoration: underline;
            cursor: pointer;
          }
        }

        :deep(.section-list-item) {
          margin-bottom: 10px;
        }

        .phone-advise {
          margin: 0 70px;
        }
      }

      .data-section-content {
        padding: 16px;

        .data-tips {
          margin: 12px 0 16px;
          padding: 4px 5px;
          background: #f8fbff;
          border-radius: 4px;
        }
      }
    }
  }

  .early-warning {
    margin-top: 24px;
    padding: 20px;
    border: 1px solid rgba(6, 21, 51, 0.15);
    border-radius: 8px;

    .early-warning-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      &-title {
        margin-left: 5px;
        font-weight: bold;
        font-size: 16px;
      }
    }

    :deep(.warning-item) {
      margin-bottom: 24px;

      .link-tab {
        text-decoration: underline;
        cursor: pointer;

        &:hover {
          color: #3777ff;
        }
      }
    }

    .warning-content {
      margin-bottom: 24px;
      padding: 0 16px;

      &-title {
        position: relative;
        margin-bottom: 8px;
        padding-left: 8px;
        font-weight: bold;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 4px;
          height: 14px;
          background: #ff4d4f;
          border-radius: 2px;
          transform: translate(0, -50%);
          content: '';
        }
      }
    }
  }
}
</style>
