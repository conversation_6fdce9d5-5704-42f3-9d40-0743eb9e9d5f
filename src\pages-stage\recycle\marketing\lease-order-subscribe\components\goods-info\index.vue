<template>
  <div class="goods-info">
    <Price src="https://img1.rrzuji.cn/uploads/scheme/2302/07/m/YTLZhEL8GbDyqMBXbp1G.png" />
    <div class="all-operate">
      <span
        class="all-operate-name"
        @click="linkItemSku"
      >
        {{ itemData.item_name }}
      </span>
      <span
        class="all-operate-sku"
        @click="linkItemSku"
      > 套餐：{{ itemData.sku_name }} </span>
      <div
        v-if="tenancy.time_start || tenancy.tenancy_text"
        class="sku-name"
      >
        租期：<span v-if="tenancy.time_start">{{ tenancy.time_start }} <span style="color: rgba(6, 21, 51, 0.45)">~</span> {{ tenancy.time_end }}</span>
        <span class="textStr">{{ tenancy.tenancy_text }}</span>
      </div>
      <div class="sku-name">
        数量：{{ other.item_num }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

import type { ItemDataType, OtherType,TenancyType } from '../../data';
import Price from './price/index.vue';

const props = defineProps({
  // 商品基本信息
  itemData: {
    type: Object as PropType<ItemDataType>,
    required: true,
  },
  // 租期
  tenancy: {
    type: Object as PropType<TenancyType>,
    default() {
      return {};
    },
  },
  other: {
    type: Object as PropType<OtherType>,
    default() {
      return {};
    },
  },
});

const linkItemSku = () => {
  window.open(props.itemData.item_href);
};
</script>

<style lang="less" scoped>
.goods-info {
  display: flex;
  padding: 16px;

  .all-operate {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 4px;
    margin-left: 16px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;

    &-name {
      color: #3777ff;
      cursor: pointer;
    }

    &-sku {
      cursor: pointer;
    }
  }
}
</style>
