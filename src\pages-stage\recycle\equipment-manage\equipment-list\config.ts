import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'order_id',
    originProps: { label: '订单号', name: 'order_id' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '202px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'sku_detail',
    originProps: { label: 'SKU信息', name: 'sku_detail' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '187px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'sku_id',
    originProps: { label: 'SKUID', name: 'sku_id' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '202px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'batch_no',
    originProps: { label: '批次', name: 'batch_no' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '216px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'receive_logistics_no',
    originProps: { label: '收件物流单号', name: 'receive_logistics_no' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '160px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'send_logistics_no',
    originProps: { label: '寄件物流单号', name: 'send_logistics_no' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '160px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'is_lock',
    originProps: { label: '是否锁机', name: 'is_lock' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    options: [
      { label: '否', value: 0 },
      { label: '是', value: 1 },
    ],
    fragmentKey: 'renderSelect',
  },
  {
    key: 'server_id',
    originProps: { label: '商家ID', name: 'server_id' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '202px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'recycle_from',
    originProps: { label: '回收来源', name: 'recycle_from' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'sale_to',
    originProps: { label: '销售去向', name: 'sale_to' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'return_type',
    originProps: { label: '退货类型', name: 'return_type' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    options: [
      { label: '退货', value: 1 },
      { label: '换货', value: 2 },
    ],
    fragmentKey: 'renderSelect',
  },
  {
    key: 'imei',
    fragmentKey: 'renderInput',
  },
  {
    key: 'sn',
    fragmentKey: 'renderInput',
  },
  {
    key: 'order_time',
    originProps: {
      label: '订单下发时间',
      name: 'order_time',
    },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    fragmentKey: 'renderRangePicker',
  },
  {
    key: 'afterSale_time',
    originProps: {
      label: '售后下发时间',
      name: 'afterSale_time',
    },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    fragmentKey: 'renderRangePicker',
  },
  {
    key: 'recycle_time',
    originProps: { label: '回收设备入库时间', name: 'recycle_time' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    fragmentKey: 'renderRangePicker',
  },
  {
    key: 'data_type',
    fragmentKey: 'renderSelect',
  },
];

export const listColumns: ColumnType[] = [
  {
    title: '设备信息',
    dataIndex: 'imei',
    key: 'imei',
    width: 246,
    className: 'vertical-middle',
  },
  {
    title: '回收来源',
    dataIndex: 'user_id',
    key: 'user_id',
    width: 196,
    className: 'vertical-middle',
  },
  {
    title: '销售去向',
    dataIndex: 'order_user_id',
    key: 'order_user_id',
    width: 196,
    className: 'vertical-middle',
  },
  {
    title: '退货类型',
    dataIndex: 'return_type',
    key: 'return_type',
    width: 104,
    className: 'vertical-middle',
  },
  {
    title: 'SKU信息',
    dataIndex: 'sku_detail',
    key: 'sku_detail',
    width: 264,
    className: 'vertical-middle',
  },
  {
    title: '货源信息',
    dataIndex: 'receive_logistics_no',
    key: 'receive_logistics_no',
    width: 224,
    className: 'vertical-middle',
  },
  {
    title: '付款信息',
    dataIndex: 'pay_at',
    key: 'pay_at',
    width: 250,
    className: 'vertical-middle',
  },
  {
    title: '退货信息',
    dataIndex: 'return_amount',
    key: 'return_amount',
    width: 250,
    className: 'vertical-middle',
  },
];
