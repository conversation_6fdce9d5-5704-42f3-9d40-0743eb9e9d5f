<template>
  <layout-shops-page :navs="['趣回收', '基础数据', '分类列表']">
    <div class="container">
      <div class="search-bar">
        <a-form
          ref="formRef"
          layout="inline"
          :model="formState"
          style="width: 100%"
        >
          <a-row style="width: 100%">
            <a-col :span="6">
              <a-form-item
                label="分类名称"
                name="name"
              >
                <a-input
                  v-model:value="formState.name"
                  placeholder="请输入分类名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-button
                  style="border-radius: 4px"
                  type="primary"
                  @click="onHandleControl().search"
                >
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索
                </a-button>
                <a-button
                  style="margin-left: 10px; border-radius: 4px"
                  @click="onHandleControl().reset"
                >
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-form-item>
            </a-col>
            <a-col
              :span="12"
              style="display: flex; justify-content: end"
            >
              <a-button
                type="primary"
                @click="onHandleControl().edit({ type: 'create' })"
              >
                添加一级分类
                <template #icon>
                  <plus-outlined />
                </template>
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="main">
        <a-table
          class="list-table"
          :columns="columns"
          :data-source="tableList"
          :loading="isLoading"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.count,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total: number) => `共 ${total} 条数据 第${pagination.current}页`
          }"
          row-key="id"
          :scroll="{ x: 1260 }"
          @change="onChangePageSize"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.name === 'status'">
              <a-badge
                :status="Number(record.status) === 1 ? 'success' : 'default'"
                :text="Number(record.status) === 1 ? '使用中' : '未使用'"
              />
            </template>

            <template v-if="column.name === 'action'">
              <div style="display: flex">
                <a-button
                  type="link"
                  @click="
                    onHandleControl(record.id).edit({
                      type: 'update',
                      pid: record.pid,
                      warehouse_dict_id: record.warehouse_dict_id,
                    })
                  "
                >
                  编辑
                </a-button>
                <a-button
                  v-if="record.level !== '二级'"
                  type="link"
                  @click="onHandleControl(record.id).edit({ type: 'createChildren', name: record.name })"
                >
                  添加下级
                </a-button>
                <a-button
                  danger
                  type="link"
                  @click="onHandleControl(record.id).remove"
                >
                  删除
                </a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <classified-modal
      v-if="classifiModalvisible"
      :id="currentId"
      v-model:visible="classifiModalvisible"
      :action-type="actionType"
      :parent-name="currentName"
      :pid="currentPid"
      :warehouse-dict-id="currentWarehouseDictId"
      @on-load-list="onLoadList"
    />
  </layout-shops-page>
</template>
<script setup lang="ts">
import { ref, reactive, createVNode } from 'vue';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { getTableList, deleteCategory } from './service';
import useTableList from '../../common/use-table-list';
import useBoolean from '../../common/use-boolean';
import ClassifiedModal from './components/classified-modal.vue';

type actionType = 'create' | 'createChildren' | 'update';

const columns: any[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '分类名称',
    dataIndex: 'name',
    width: 100,
  },
  {
    title: '层级',
    dataIndex: 'level',
    width: 60,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 60,
  },
  {
    title: '使用状态',
    dataIndex: 'status',
    name: 'status',
    width: 80,
  },
  {
    title: '操作',
    name: 'action',
    fixed: 'right',
    align: 'center',
    width: 90,
  },
];

const currentId = ref('');
const currentName = ref('');
const currentPid = ref<number | undefined>(undefined);
const currentWarehouseDictId = ref<string | undefined>('');

const actionType = ref<actionType>('create');

const [classifiModalvisible, { setTrue: showClassifiModal }] = useBoolean();

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  name: '', // 分类名称
});

// 分页器
let pagination = reactive({
  current: 1,
  pageSize: 10,
  count: 0,
});

const { onLoadList, tableList, isLoading } = useTableList({
  getTableList,
  payload: formState.value,
  pagination,
  isAutoInit: true,
});

const onChangePageSize = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  // 再次发起网络请求
  onLoadList();
};

const onHandleControl = (id?: string) => ({
  search() {
    pagination.current = 1;
    onLoadList();
  },
  reset() {
    // 1.重置所有字段
    formRef.value?.resetFields();
    // 2. 初始化页码
    pagination.current = 1;
    pagination.pageSize = 10;
    // 3. 重新获取table数据
    onLoadList();
  },
  edit(params: { type: actionType; pid?: number; warehouse_dict_id?: string; name?: string }) {
    currentId.value = id as string;
    if (params) {
      currentName.value = params.name as string;
      currentPid.value = params.pid;
      currentWarehouseDictId.value = params.warehouse_dict_id;
      actionType.value = params.type as actionType;
    }
    showClassifiModal();
  },
  remove() {
    Modal.confirm({
      title: '是否删除此类目?',
      content: createVNode(
        'div',
        {
          style: 'color: rgba(0,0,0,0.65);',
        },
        '类目删除后将不能恢复，确认删除所选类目吗？',
      ),
      onOk() {
        return deleteCategory(id as string).then(() => {
          message.success('删除成功');
          onLoadList();
        });
      },
    });
  },
});
</script>
<style scoped lang="less">
@import '../../common/base.less';
</style>
