import { GET, POST } from '@/services/api';

import { TAnnouncementReadResponse } from './data';

/**
 * @description: 公证记录信息
 * @param {number} data.type 1:长租 2:短租
 * @param {string} data.id 短租才有
 * @return {*}
 */
export function notarizationRecord(data: { type: 1 | 2; order_id: string; id?: number }) {
  return POST('/cai-hua-notarization-record/initiate-notarization', data);
}

export function pingShanRecord(data: any) {
  return POST('/ping-shan-notarization-record/initiate-notarization', data);
}

/**
 * @description: 长租发起公证
 * @param {any} data
 * @return {*}
 */
export function createNotarizationRecordLong(data: any) {
  data.version = 2;
  return POST('/cai-hua-notarization-record/create', data);
}

/**
 * @description: 坪山发起公证
 */
export function createNotarizationPingShan(data: any) {
  data.version = 2;
  return POST('/ping-shan-notarization-record/create', data);
}

/**
 * @description: 短租发起公证
 * @param {any} data
 * @return {*}
 */
export function createNotarizationRecordShort(data: any) {
  data.version = 2;
  return POST('/cai-hua-notarization-record/submit', data);
}

/**
 * @description: 上传法定代表人证明
 * @param {any} params
 * @return {*}
 */
export const uploadNotarization = (params: { path: string }) => {
  return POST('/cai-hua-notarization-application/upload-certificate', params);
};

/**
 * @description: 获取确认公证告知书和通知相关跳转信息
 * @return {*}
 */
export const getAnnouncementRead = <T>() => {
  return GET<T, TAnnouncementReadResponse>('/cai-hua-notarization-application/get-announcement-read');
};

export const getNotarizationScene = () => {
  return GET('/cai-hua-notarization-application/get-scene');
};
