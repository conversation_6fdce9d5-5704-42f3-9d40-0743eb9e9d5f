<template>
  <!-- <page-breadcrumb :items="['商品', '商品管理', '发布严选商品']" /> -->
  <layout-shops-page
    :content-style="{ padding: '0 24px' }"
    title="发布严选商品"
  >
    <div class="release-strict">
      <DataTable />
    </div>
  </layout-shops-page>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import DataTable from './data-table';

export default defineComponent({
  components: {
    DataTable,
  },
});
</script>
