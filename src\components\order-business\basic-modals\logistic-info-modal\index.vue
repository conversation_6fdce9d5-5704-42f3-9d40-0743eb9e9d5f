<template>
  <a-modal
    v-model:visible="bindVisible"
    :mask-closable="false"
    title="物流信息"
    @ok="confirm"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <div class="alert-box">
        <a-alert
          message="提示：【上门自取】与【上门安装】无需填写快递单号。"
          type="warning"
        />
      </div>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
      >
        <a-form-item
          label="快递公司"
          name="shipper_code"
        >
          <a-select
            v-model:value="formData.shipper_code"
            :allow-clear="true"
            option-filter-prop="label"
            :options="logisticOptions"
            placeholder="请选择"
            show-search
          />
        </a-form-item>
        <a-form-item
          v-show="isNeedLogisticNum"
          label="快递单号"
          name="logistic_code"
        >
          <a-input
            v-model:value="formData.logistic_code"
            placeholder="运单号一般为10~14位数字"
          />
        </a-form-item>
        <a-form-item
          label="发货图片"
          name="deliverImg"
        >
          <a-image-preview-group>
            <a-image
              v-for="item in orderLogisticImage"
              :key="item"
              :src="item"
              :width="100"
            />
          </a-image-preview-group>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { FormInstance, message } from 'ant-design-vue';

import { fetchGateway } from '@/components/order-business/basic-modals/services';
import { useLogisticOption } from '@/components/order-business/composables/use-logistic-option';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

interface ILogisticForm {
  /** 快递方式 */
  shipper_code?: string | number;
  /** 快递单号 */
  logistic_code?: string;
  /** 快递图片 */
  order_logistic_image?: string[];
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  shipperCode: {
    type: [Number, String],
    default: -1,
  },
  logisticCode: {
    type: String,
    default: '',
  },
  orderLogisticImage: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);

const { logisticOptions } = useLogisticOption();
const formRef = ref<FormInstance>();
const formData = ref<ILogisticForm>({});

/** 是否需要填写物流单号 */
const isNeedLogisticNum = computed(() => {
  return formData.value.shipper_code !== -1 && formData.value.shipper_code !== -2;
});

const rules = {
  shipper_code: [{ required: true, message: '物流名称不能为空', trigger: 'blur' }],
  logistic_code: [
    {
      required: true,
      validator: (_rule: any, value: string, callback: any) => {
        if (isNeedLogisticNum.value && !value)
          return callback(new Error('除了【上门自取】与【上门安装】都需要填写快递单号'));
        callback();
      },
      trigger: ['blur'],
    },
  ],
};

watch(
  bindVisible,
  val => {
    if (val) {
      formData.value = {
        shipper_code: Number(props.shipperCode) || props.shipperCode,
        logistic_code: props.logisticCode && props.logisticCode !== '-1' ? props.logisticCode : '',
      };
    }
  },
  { immediate: true },
);

const loading = ref(false);

const isSuper = useRoute().query.role === 'super';

/** 确认 */
function confirm() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    fetchGateway({
      orderId: props.orderId,
      nonLeaseType: isSuper ? 'nonLease' : undefined, // 管理员需加参数
      ordinary_delivery: 1,
      shipper_code: formData.value.shipper_code,
      logistic_code: isNeedLogisticNum.value ? formData.value.logistic_code : '-1',
      method: 'v2.order.edit.logistic',
    })
      .then(() => {
        message.success('修改成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data']);
      })
      .finally(() => {
        loading.value = false;
      });
  });
}
</script>

<style scoped lang="less">
.alert-box {
  margin-bottom: 24px;
}
</style>
