<template>
  <!-- TODO面审信息 -->
  <div class="tabs-box">
    <div class="flex-wrap flex-gap-8">
      <div
        v-for="item in options"
        :key="item.value"
        :class="['tab-btn', item.value === currentOption ? 'active' : '']"
        @click="optionsChange(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
    <div
      v-if="currentOption === EOptions.GOODS_QUALIFICATIONS"
      class="right-box"
    >
      <a-button
        ghost
        type="primary"
        @click="addGoodsQualification"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        添加商品资质
      </a-button>
    </div>
  </div>
  <component :is="currentComMap[currentOption]" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { PlusOutlined } from '@ant-design/icons-vue';

import { router } from '@/router/router';

import { currentComMap, options } from './config';
import { EHandleType, EOptions } from './data.d';
const props = defineProps<{
  current: EOptions | null;
}>();
const route = useRoute();
const currentKey = props.current || Number(route.query.current) || EOptions.INDUSTRY_QUALIFICATIONS;
const currentOption = ref(currentKey);

const optionsChange = (current: EOptions) => {
  currentOption.value = current;
};

function addGoodsQualification() {
  router.push({
    path: '/merchant/commodity/manage/goods-qualification-edit',
    query: {
      type: EHandleType.ADD,
    },
  });
}
</script>

<style scoped lang="less">
.tabs-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  .right-box {
    .tips {
      margin: 0 16px 0 6px;
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      text-align: left;
    }
  }
}

.tab-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 76px;
  height: 32px;
  color: rgba(6, 21, 51, 0.65);
  line-height: 22px;
  background: #f9f9fb;
  border: 1px solid rgba(6, 21, 51, 0.06);
  border-radius: 4px 4px 4px 4px;
  cursor: pointer;
}
.active {
  color: #3777ff;
  background: #f0f7ff;
  border: 1px solid #3777ff;
}
</style>
