import type { ColumnProps } from 'ant-design-vue/lib/table';

export const listColumns: ColumnProps = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 70,
  },
  {
    title: '兜底商名称',
    dataIndex: 'name',
    key: 'name',
    width: 240,
  },
  {
    title: '联系人',
    dataIndex: 'contact_infos_name',
    key: 'contact_infos_name',
    width: 80,
  },
  {
    title: '联系人电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 95,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 140,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 159,
    fixed: 'right',
    align: 'center',
  },
];
