import { GET, POST } from '@/services/api';

/**
 * @description: 获取顺丰物流详情（运营端）
 * @param {any} params
 * @return {*}
 */
export function superGetSFTransportDetail(params: any) {
  return GET('/super/v2-order-sf-express/detail', params);
}

/**
 * @description: 获取顺丰物流详情（商家端）
 * @param {any} params
 * @return {*}
 */
export function getSFTransportDetail(params: any) {
  return GET('/v2-order-sf-express/detail', params);
}

/**
 * @description: 获取顺丰运输服务列表
 * @param {any} params
 * @return {*}
 */
export function getSFTransportList(params: any) {
  return GET('/v2-order-sf-express/list-of-order', params);
}

/**
 * @description: 获取京东运输服务列表（运营端）
 * @param {any} params
 * @return {*}
 */
export function superGetJDTransportList(params: any) {
  return GET('/super/v2-order-jd-express/list-of-order', params);
}

/**
 * @description: 获取京东运输服务列表（商家端）
 * @param {any} params
 * @return {*}
 */
export function getJDTransportList(params: any) {
  return GET('/v2-order-jd-express/list-of-order', params);
}

/**
 * @description: 获取京东物流详情（运营端）
 * @param {any} params
 * @return {*}
 */
export function superGetJDTransportDetail(params: any) {
  return GET('/super/v2-order-jd-express/detail', params);
}

/**
 * @description: 获取京东物流详情（商家端）
 * @param {any} params
 * @return {*}
 */
export function getJDTransportDetail(params: any) {
  return GET('/v2-order-jd-express/detail', params);
}

/** 取消京东寄件 */
export function cancelJdPickUp(params: any) {
  return POST('/v2-order-jd-express/cancel', params);
}

/** 取消顺丰寄件 */
export function cancelSfPickUp(params: any) {
  return POST('/v2-order-sf-express/cancel', params);
}

/** 获取打印面单 */
export function getSFPrintData(params: { order_id: string }) {
  return GET('/v2-order-sf-express/single-sided-printing-data-sf', params);
}
