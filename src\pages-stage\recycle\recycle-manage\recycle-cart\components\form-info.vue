<template>
  <div class="info-block">
    <span class="info-block__title">
      {{ props.title }}
    </span>
    <div class="info-block__content">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.info-block {
  display: flex;
  // align-items: center;
  padding: 8px 13px 8px 16px;
  background: #f0f7ff;
  border-radius: 4px;
  &__title {
    margin-right: 122px;
    padding-top: 4px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
  &__content {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
  }
}
</style>
