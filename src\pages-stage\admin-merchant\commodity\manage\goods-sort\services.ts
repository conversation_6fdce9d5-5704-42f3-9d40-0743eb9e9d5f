import { GET, POST } from '@/services/api';
import { CategoryItem } from './data';
import { AxiosResponse } from 'axios';
// 获取商家分类
export function getCategory(): Promise<
  AxiosResponse<{
    list: CategoryItem[];
    updated_at: string;
  }>
> {
  return GET('/shop/decorate/get-server-category');
}
// 保存商家分类数据
export function saveCategory(params: { category_data: string }) {
  return POST('/shop/decorate/update-server-category-sort', params);
}
// 获取分类的商品列表
export function getCategoryGoods(params: { category: string }) {
  return GET('/product/queryServerSpuList', params, { hostType: 'Golang' });
}

// 更新商品排序
export function updateSort(params: { spu_ids: number[] }) {
  return POST('/product/updateServerSpuSort', params, { hostType: 'Golang' });
}

// 重置商品排序
export function resetSort(params: { category_id: number }) {
  return POST('/product/resetServerSpuSort', params, { hostType: 'Golang' });
}
