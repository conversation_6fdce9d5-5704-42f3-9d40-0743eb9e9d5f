<template>
  <RTable
    ref="tableRef"
    :api="apiGetGoodsInfoList"
    class="goods-info-table"
    :columns="goodsInfoColumns"
    :use-table-options="useTableOptions"
  >
    <template #tableBodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'operation'">
        <a-space>
          <a-button
            class="operation-btn"
            type="link"
            @click="handleDetail(record.id, EHandleType.DETAIL)"
          >
            详情
          </a-button>
          <a-button
            v-if="record.release_status !== ECredentialAuditStatus.WaitAudit"
            class="operation-btn"
            type="link"
            @click="
              handleDetail(
                record.id,
                EHandleType.EDIT,
                record.release_status === ECredentialAuditStatus.Reject
                  ? ECurrentDataType.RELEASE
                  : ECurrentDataType.CURRENTUSE,
              )
            "
          >
            修改
          </a-button>
          <a-button
            v-if="record.release_status !== ECredentialAuditStatus.Pass"
            class="operation-btn"
            type="link"
            @click="handleDetail(record.id, EHandleType.DETAIL, ECurrentDataType.RELEASE)"
          >
            审核记录
          </a-button>
          <a-popconfirm
            title="确认删除该资质？"
            @confirm="del(record.id)"
          >
            <a-button
              v-if="record.release_status === ECredentialAuditStatus.Pass && record.status === ECredentialStatus.Usable"
              class="operation-btn"
              danger
              type="link"
            >
              删除
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </template>
  </RTable>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { RTableInstance } from 'rrz-web-design';

import { ECredentialAuditStatus, ECredentialStatus } from '@/pages-stage/server/audit/qualification/data.d';

import { ECurrentDataType, EHandleType } from '../../data.d';
import { goodsInfoColumns } from './config';
import { apiGetGoodsInfoList, delCredentialGoodsApi } from './service';
const router = useRouter();
const tableRef = ref<RTableInstance>();
const useTableOptions = {
  pageKey: 'page_no',
};
function handleDetail(id: string, type: EHandleType, dataType?: ECurrentDataType) {
  const path =
    type === EHandleType.DETAIL
      ? '/merchant/commodity/manage/goods-qualification-detail'
      : '/merchant/commodity/manage/goods-qualification-edit';
  router.push({
    path,
    query: { id, type, dataType },
  });
}
async function del(id: string) {
  await delCredentialGoodsApi({ id });
  message.success('删除成功');
  tableRef.value?.getTableList();
}
</script>

<style lang="less" scoped>
.goods-info-table {
  :deep(.r-search-content) {
    margin: 0 0 24px;
  }
  :deep(.r-table-content) {
    padding: 0;
  }
}
.operation-btn {
  padding: 0;
}
</style>
