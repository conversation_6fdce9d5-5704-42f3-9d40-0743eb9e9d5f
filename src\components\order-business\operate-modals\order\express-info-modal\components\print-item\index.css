@media print {
  /* 分页 */
  .order {
    page-break-after: always;
    page-break-before: always;
  }
}
.order * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: #000;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
  line-height: normal;
  text-align: left;
}
.order {
  position: relative;
  box-sizing: border-box;
  width: 100mm;
  height: 215mm;
  padding: 2mm 3mm 0;
  overflow: hidden;
}
.proCode {
  position: absolute;
  top: 1mm;
  right: 3mm;
  font-weight: 700;
  font-size: 24pt;
  font-family: SimSun, sans-serif !important;
}
.barcode-area {
  margin-top: 10mm;
}
.print-info {
  display: flex;
  align-items: center;
  margin-left: 10mm;
  font-size: 6pt;
  font-family: SimSun, sans-serif !important;

  line-height: 1;
  transform-origin: left center;
}
.print-info span {
  margin-right: 14px;
}
.barcode {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
}
.barcode img {
  width: 80mm;
  height: 13mm;
}
.waybillno-info {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 10mm;
  padding: 5px;
}
.flex-center {
  align-items: center;
}
.waybillno-info .count {
  margin-right: 20px;
  margin-left: -12mm;
  font-size: 10pt;
}
.waybillno-info .no {
  font-weight: bold;
  font-size: 10pt;
}
.order-body .lian-1,
.order-body .lian-2 {
  border: 1px solid #000;
}
.order-body .lian-2 {
  margin-top: 2mm;
}
.received-area {
  height: 26mm;
  border-bottom: 1px solid #000;
}
.route {
  font-weight: 700;
  font-size: 27pt;
  text-align: center;
}
.received-area .info {
  position: relative;
  display: flex;
  /* align-items: center; */
  height:15mm;
  overflow: hidden;
}
.received-icon {
  width: 7mm;
  height: 7mm;
  margin-left: 1mm;
}
.receiver-address {
  margin-left: 2mm;
  font-size: 9pt;
  font-family: SimSun, sans-serif !important;
  line-height: 1.5;
}
.destTeam-code {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #909090;
  font-weight: bold;
  font-size: 37pt;
  line-height: 32pt;
  transform: translate(-50%, -50%);
  opacity: 0.5;
}
.receiver-address span,
.sender-address span,
.send-area span {
  margin-right: 1mm;
}
.receiver-right {
  margin-left: auto;
}
.receiver-right img {
  width: 17mm;
  height: 9mm;
}
.qrcode-area {
  display: flex;
  align-items: center;
  height: 26mm;
}
.qrcode-area .left {
  width: 33mm;
  height: 100%;
  border-right: 1px solid #000;
}
.paymethod {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 6mm;
  font-size: 9pt;
  font-family: SimSun, sans-serif !important;
  border-bottom: 1px solid #000;
}
.codingMapping {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20mm;
  font-weight: bold;
  font-size: 40pt;
}
.qrcode {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 33mm;
  height: 100%;
  border-right: 1px solid #000;
}
.qrcode img {
  width: 25mm;
  height: 25mm;
}
.yanshi {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10mm;
  height: 26mm;
  font-weight: bold;
  font-size: 20pt;
  font-family: SimSun, sans-serif !important;
  text-align: center;
  border-right: 1px solid #000;
}
.qrcode-area .right {
  height: 100%;
}
.proName {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20mm;
  height: 17mm;
  border-bottom: 1px solid #000;
}
.proName div {
  width: 12mm;
  text-align: center;
}
.codingMappingOut {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 9mm;
  font-weight: bold;
  font-size: 30pt;
}
.send-area {
  display: flex;
  /* align-items: center; */
  height: 11mm;
  overflow: hidden;
  border-bottom: 1px solid #000;
}
.send-icon {
  align-self: center;
  width: 7mm;
  height: 7mm;
  margin-left: 1mm
}
.send-area .info {
  margin-left: 2mm;
  font-size: 9pt;
  font-family: SimSun, sans-serif !important;
  line-height: 1.5;
}
.cargo-addserviec-area {
  display: flex;
  width: 100%;
  height: 24mm;
  overflow: hidden;
  border-bottom: 1px solid #000;
}
.cargo-addserviec-area .left {
  flex-shrink: 0;
  width: 47mm;
  height: 100%;
  padding: 1mm;
  font-size: 8pt;
  font-family: SimSun, sans-serif !important;
  border-right: 1px solid #000;
}
.cargo {
  font-size: 8pt;
  line-height: 1.5;
  transform-origin: 0 0;
}
.cargo-addserviec-area .right {
  flex-shrink: 0;
  width: 47mm;
  font-family: SimSun, sans-serif !important;

  line-height: 1.5;
}
.addserviec {
  box-sizing: border-box;
  height: 12mm;
  padding: 1mm;
  /* overflow: hidden; */
  font-size: 8pt;
  border-bottom: 1px solid #000;
}
.other {
  width: 100%;
  height: 12mm;
  padding-left: 1mm;
}
.other > div {
  font-size: 8pt;
  line-height: 1.2;
}
.weight {
  display: flex;
}
.weight div {
  margin-right: 10px;
}
.payMethod2 {
  display: flex;
}
.custom-abflag-area {
  display: flex;
  height: 22mm;
}
.custom {
  flex-shrink: 0;
  width: 47mm;
  height: 100%;
  padding: 1mm;
  border-right: 1px solid #000;
}
.remark {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 8pt;
  line-height: 1.4;
}
.abflag {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  width: 47mm;
  height: 100%;
  padding: 0 0.5mm;
}
.PIimg {
  width: 21mm;
  height: 21mm;
  margin-right: 5px;
}

.stub-body {
  margin-top: 2mm;
  border: 1px solid #000;
}
.logo-barcode-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 19mm;
  padding: 0 3mm;
}
.logo-barcode-area .left {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 16mm;
  padding: 1mm;
}
.logo-barcode-area .left .logo-icon {
  /* width: 16mm;
  height: 6mm; */
  margin-bottom: 2mm;
}
.logo-barcode-area .center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 54mm;
  font-size: 10pt;
}
.logo-barcode-area .barcode {
  width: 54mm;
  height: 14mm;
}
.logo-barcode-area .right {
  width: 10mm;
  font-size: 14pt;
  font-family: SimSun, sans-serif !important;

  line-height: 1.3;
}
.stub-body .sender-area {
  display: flex;
  /* align-items: center; */
  height: 9mm;
  overflow: hidden;
  border-top: 1px solid #000;
}
.sender-area .sender-icon {
  flex-shrink: 0;
  align-self: center;
  width: 5mm;
  height: 5mm;
  margin-left: 1mm;
}
.stub-body .sender-area .sender-address {
  margin: 0 1mm;
  font-size: 6pt;
  line-height: 1.2;
}
.stub-body .recevier-area {
  display: flex;
  /* align-items: center; */
  height: 10mm;
  overflow: hidden;
  border-top: 1px solid #000;
}
.stub-body .recevier-area .receiver-icon {
  flex-shrink: 0;
  align-self: center;
  width: 5mm;
  height: 5mm;
  margin-left: 1mm;
}
.stub-body .recevier-area .receiver-address {
  margin: 2px  1mm 0;
  font-size: 6pt;
  line-height: 1.1;
}
.stub-body .recevier-area .receiver-address span{
  line-height: 1.1;
}
.stub-body .cargo-addservice-area {
  display: flex;
  height: 10mm;
  border-top: 1px solid #000;
}
.stub-body .cargo-addservice-area .left {
  width: 47mm;
  height: 100%;
  padding: 1mm;
  border-right: 1px solid #000;
}
.stub-body .cargo-addservice-area .left > div {
  font-size: 8pt;
  line-height: 1.3;
}
.stub-body .cargo-addservice-area .left .cargo-name {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.stub-body .cargo-addservice-area .right {
  padding: 1mm;
}
.stub-body .cargo-addservice-area .right > div {
  font-size: 8pt;
  line-height: 1.3;
}
.paymethod-remark-area {
  display: flex;
  height: 6mm;
  border-top: 1px solid #000;
}
.paymethod-remark-area .left {
  display: flex;
  align-items: center;
  width: 47mm;
  height: 100%;
  padding: 1mm;
  border-right: 1px solid #000;
}
.paymethod-remark-area .left > div {
  display: flex;
  align-items: center;
  font-size: 6pt;
  line-height: 1.3;
}
.label {
  flex-shrink: 0;
}
.paymethod-remark-area .right {
  height: 100%;
  padding: 1mm;
}
.paymethod-remark-area .right > div {
  font-size: 8pt;
  line-height: 1.3;
}
.stub-body .other-area {
  display: flex;
  height: 5mm;
  border-top: 1px solid #000;
}
.other-area-item {
  padding: 1mm;
  border-right: 1px solid #000;
}
.other-area-item:nth-of-type(1) {
  width: 25mm;
}
.other-area-item:nth-of-type(2) {
  width: 30mm;
}
.other-area-item > div {
  font-size: 8pt;
  line-height: 1.3;
}
