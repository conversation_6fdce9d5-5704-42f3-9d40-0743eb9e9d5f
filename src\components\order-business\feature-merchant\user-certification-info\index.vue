<template>
  <div
    ref="infoRef"
    class="info-box"
  >
    <a-skeleton
      v-if="dataLoading"
      active
      loading
      :paragraph="{ rows: 1 }"
    />
    <div
      v-else-if="userData"
      class="identity-info"
    >
      <span>
        认证资料：
        <template v-if="userData.user_info?.username || userData.user_info?.idcard">
          <!--  姓名  -->
          {{ isShowName ? realName : userData.user_info?.username }}
          <EyeToggleIcon
            :value="isShowName"
            @change="toggleShowName"
          />
          <!--  身份证  -->
          ({{ isShowIdCard ? realIdCard : userData.user_info?.idcard }})
          <EyeToggleIcon
            :value="isShowIdCard"
            @change="toggleShowIdCard"
          />
        </template>
        <template v-else> 无 </template>
      </span>
      <span v-if="userData.pre_authorization_info?.face_recognition_result">
        {{ userData.pre_authorization_info?.face_recognition_result }}
      </span>
      <span v-if="userData.user_info?.sex">性别：{{ userData.user_info?.sex }}</span>
      <span v-if="userData.user_info?.age">年龄：{{ userData.user_info?.age }}</span>
      <span style="color: rgb(255, 77, 79)">会员等级：{{ userData.user_info?.level || 'VIP0' }}</span>
    </div>
    <div v-else>
      <a-alert
        message="用户认证资料获取失败，请重新刷新"
        type="warning"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { EyeToggleIcon } from '@/components/order-business/feature-application';
import type { IUserData } from '@/components/order-business/typing/index.d';
import { useLazyLoader } from '@/composables/use-lazy-loader';
import { decryptSensitiveInfo } from '@/utils/sensitive-info';

import { fetchOrderUserInfo } from './services';

const props = defineProps<{
  orderId?: string;
}>();

// ==================================  用户信息  ====================================
const infoRef = ref();
const userData = ref<IUserData>();
const dataLoading = ref(false);

async function getUserData() {
  try {
    if (userData.value || dataLoading.value || !props.orderId) {
      return;
    }
    dataLoading.value = true;
    const orderId = props.orderId;
    const { data } = await fetchOrderUserInfo({ order_id: [orderId] });
    if (data.list?.[orderId]) {
      userData.value = data.list[orderId];
    }
  } finally {
    dataLoading.value = false;
  }
}

// 懒加载用户信息
useLazyLoader(getUserData, infoRef);

// ====================================== 姓名 ========================================
/** 姓名是否脱敏 */
const isShowName = ref(false);
/** 真实姓名 */
const realName = ref('');

/** 切换姓名显示 */
async function toggleShowName(value: boolean) {
  await getNameValue();
  isShowName.value = value;
}

/** 获取姓名 */
function getNameValue() {
  if (!realName.value) {
    return decryptSensitiveInfo({ order_id: props.orderId, sign: 1024, type: 2 }).then(res => {
      if (res?.data?.user_info) {
        realName.value = res.data.user_info.username;
      }
    });
  }
  return Promise.resolve();
}

// ====================================== 身份证 ========================================
/** 身份证是否脱敏 */
const isShowIdCard = ref(false);
/** 真实身份证 */
const realIdCard = ref('');

/** 切换身份证显示 */
async function toggleShowIdCard(value: boolean) {
  await getIdCardValue();
  isShowIdCard.value = value;
}

/** 获取身份证 */
function getIdCardValue() {
  if (!realIdCard.value) {
    return decryptSensitiveInfo({ order_id: props.orderId, sign: 2048, type: 5 }).then(res => {
      if (res?.data?.user_info) {
        realIdCard.value = res.data.user_info.idcard;
      }
    });
  }
  return Promise.resolve();
}
</script>

<style scoped lang="less">
.info-box {
  min-height: 40px;
  margin: 12px 16px 0 12px;
}

.identity-info {
  font-size: 16px;

  > span:not(:last-child) {
    margin-right: 10px;
    padding-right: 10px;
    border-right: 1px solid #f0f1f3;
  }
}
</style>
