<template>
  <!-- 配件清单 -->
  <div
    v-if="!!accessoryData?.length"
    class="accessories-container"
  >
    <div class="accessories-container-title">
      配件清单
    </div>
    <!-- 1是赠品 2是需归还 -->
    <div
      v-for="typeItem in accessoryData"
      :key="typeItem.type"
      class="accessories-box"
    >
      <div class="accessories-title flex-wrap flex-y-center">
        {{ typeItem.type === 1 ? '赠品' : '需归还' }}
        <span
          v-if="typeItem.type === 2"
          class="accessories-little-title"
        >(注：订单购买后，以下配件默认无需归还)</span>
      </div>
      <div class="accessories-content flex-wrap flex-y-center">
        <div
          v-for="item in typeItem.list"
          :key="item.accessory_type"
          class="content-item flex-wrap flex-y-center"
        >
          <div>{{ item.accessory_name }}</div>
          <div class="count">
            x{{ item.num }}
          </div>
          <div class="line" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IAccessoryData } from '../../../../data';

defineProps<{
  accessoryData: IAccessoryData[];
}>();
</script>

<style scoped lang="less">
.accessories-container {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;

  .accessories-container-title {
    padding-bottom: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }

  .accessories-box {
    margin: 0 auto;
    border: 1px solid rgba(6, 21, 51, 0.15);
    border-radius: 6px;

    .accessories-title {
      height: 54px;
      padding-left: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      background: rgba(6, 21, 51, 0.06);
      border-radius: 6px 6px 0 0;

      .accessories-little-title {
        height: 22px;
        padding-left: 8px;
        color: rgba(6, 21, 51, 0.45);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }

    .gift-box {
      background-color: rgba(0, 200, 190, 0.15);
    }

    .accessories-content {
      flex-wrap: wrap;
      padding: 16px;
      padding-bottom: 12px;

      .content-item {
        height: 22px;
        margin-bottom: 4px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 400;
        font-size: 14px;

        line-height: 22px;

        .count {
          margin-left: 4px;
          color: rgba(6, 21, 51, 0.4 5);
        }
      }

      .line {
        width: 1px;
        height: 14px;
        margin: 0 8px;
        background: rgba(6, 21, 51, 0.15);
      }
    }
  }

  .accessories-box:not(:last-of-type) {
    margin-bottom: 16px;
  }
}
</style>
