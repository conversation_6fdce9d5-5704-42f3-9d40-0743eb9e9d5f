import type { CSSProperties } from 'vue';
export type TreeNode = {
  id: string | number;
  name: string;
  children?: TreeNode[];
  [key: string]: unknown;
};

export type FieldNames = {
  label?: string;
  value?: string;
  children?: string;
};

export type CascaderExtra = {
  placeholder?: string;
  style?: CSSProperties;
  maxTagCount?: number | 'responsive';
  fieldNames?: FieldNames;
  [key: string]: unknown;
};
