<template>
  <div class="authority-center">
    <header class="page-header">
      <span class="title">权限配置</span>

      <div class="right">
        <add-auth
          v-model:configuration-list="configurationList"
          v-model:isShowAddAccount="isShowAddAccount"
          :config-loading="configLoading"
          :value="list.length < 5"
          @load-config="loadConfigurationList"
          @reset-list="loadList"
        />
      </div>
    </header>
    <div class="descript">
      <span>租赁数智链接：</span>
      <a
        class="shuzhi-link"
        :href="'https://' + shuzhiLink"
        target="_blank"
      >
        <img
          class="icon-link"
          src="https://img1.rrzuji.cn/uploads/scheme/2211/23/m/woiGgjxNsPEv2UxlJmzD.png"
        >
        <span
          data-sensors="$ConfigToShuzhi"
          style="margin-left: 3px"
        >{{ shuzhiLink }}</span>
      </a>
    </div>
    <main class="data-table">
      <div class="title-box">
        <span class="title">已关联账号</span>
        <span class="desc">(当前会员最多支持绑定5个账号)</span>
      </div>
      <authority-center-table
        :list="list"
        :loading="loading"
        @edit="editAccount"
        @reset-list="loadList"
      />
    </main>

    <div class="course">
      点击查看账号权限操作教程：
      <a
        class="shuzhi-link"
        href="https://oxa3qz.yuque.com/oxa3qz/fyz65u/blida1id1rkwo7sx"
        target="_blank"
      >
        <img
          class="icon-link"
          src="https://img1.rrzuji.cn/uploads/scheme/2211/23/m/woiGgjxNsPEv2UxlJmzD.png"
        >
        <span style="margin-left: 3px">https://oxa3qz.yuque.com/oxa3qz/fyz65u/blida1id1rkwo7sx</span>
      </a>
    </div>

    <add-account-modal
      v-model:data="addAccountInfo"
      v-model:value="isShowAddAccount"
      :config-loading="configLoading"
      :configuration-list="configurationList"
      @reset-list="loadList"
    />
  </div>
</template>

<script setup lang="ts">
import { getAccountList, getConfigurationList } from './service';

import { ref, onMounted } from 'vue';
import { AuthorityCenterRow, ConfigurationItem } from './data';

import AuthorityCenterTable from './authority-center-table.vue';
import AddAccountModal from './add-account-modal.vue';

import AddAuth from './add-auth.vue';

const loading = ref(false);
const list = ref([]);
const configurationList = ref<ConfigurationItem[]>([]);
const configLoading = ref<boolean>(false);

const shuzhiLink = ((referrer: string) => {
  if (import.meta.env.DEV || /dev\d*\-/.test(referrer)) {
    return 'dev1-shuzhi.rrzuji.com';
  } else if (/test\d*\-/.test(referrer)) {
    return 'test1-shuzhi.rrzuji.com';
  } else {
    return 'shuzhi.rrzu.com';
  }
})(document.referrer);

const addAccountInfo = ref<AuthorityCenterRow>({
  phone: '',
  name: '',
  account_id: 0,
  auth_export: 0,
  auth_search: 1,
  created_at: '',
  created_at_text: '',
  id: 0,
  server_id: 0,
});
const isShowAddAccount = ref(false);

const moduleObj = {
  1: 'https://img1.rrzuji.cn/uploads/scheme/2304/17/m/g85q0RPI9c8hYCz4M28l.png',
  2: 'https://img1.rrzuji.cn/uploads/scheme/2304/17/m/b5Q4VPYAeDeM7hruoRQM.png',
  5: 'https://img1.rrzuji.cn/uploads/scheme/2304/17/m/YDVHAWJ0aCznUiYIbEmV.png',
};
/**
 * @description: 获取功能配置
 * @return {*}
 */
function loadConfigurationList(account_id: number) {
  configLoading.value = true;
  getConfigurationList({ account_id }).then(res => {
    res.data.forEach((item1: ConfigurationItem) => {
      item1.is_conf ? item1.is_conf : (item1.is_conf = false);
      item1.icon = moduleObj[item1.module_id];
      item1.function.forEach(item2 => {
        item2.is_conf ? item2.is_conf : (item2.is_conf = false);
      });
    });
    configLoading.value = false;
    configurationList.value = res.data;
  });
}

// 编辑账号
function editAccount(row: AuthorityCenterRow) {
  loadConfigurationList(row.account_id);

  addAccountInfo.value = row;
  isShowAddAccount.value = true;
}

// 搜索
function loadList() {
  loading.value = true;
  getAccountList()
    .then(res => {
      list.value = res.data || [];
    })
    .finally(() => {
      loading.value = false;
    });
}

onMounted(() => {
  loadList();
});
</script>

<style scoped lang="less">
@import '@/style/variables.less';
.authority-center {
  height: 100%;
  padding: 24px;
  background-color: #fff;

  .page-header {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    background-color: #fff;
    .title {
      color: @font-black-85;
      font-weight: bold;
      font-size: 20px;
    }

    .right {
      position: absolute;
      right: 0;
    }
  }

  .data-table {
    margin-top: 24px;
    .title-box {
      margin-bottom: 8px;
      .title {
        color: rgba(6, 21, 51, 0.85);
        font-weight: bold;
        font-size: 16px;
      }
      .desc {
        margin-left: 8px;
        color: rgba(6, 21, 51, 0.65);
      }
    }
  }
  .descript {
    display: flex;
    align-items: center;
    padding: 12px 14px;
    background-color: #f5f7fa;
  }

  .course {
    display: flex;
    align-items: center;
    margin-top: 24px;
  }
}

.shuzhi-link {
  display: flex;
  align-items: center;
}
.icon-link {
  width: 16px;
  height: 16px;
}
</style>
