/**
 * 仅对当前目录下src进行查询
 */
import fs from 'fs';
import { dirname, join, resolve as pathResolve } from 'path';

// 用于存储文件引用关系的对象
const referenceFileList = [];
// 异步文件查找进程
const asyncFilePromiseList = [];
// 当前输入的文件路径
let targetPath = '';
// 记录已查询的文件个数
let fileNum = 0;

// 遍历目录，解析文件引用关系
function traverseDirectory(dir, targetFile) {
  const files = fs.readdirSync(dir).filter(path => path[0] !== '.');
  for (const file of files) {
    const filePath = join(dir, file);
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      traverseDirectory(filePath, targetFile);
    } else if (stats.isFile()) {
      const promise = analyzeFile(filePath, targetFile);
      asyncFilePromiseList.push(promise);
    }
  }
  return Promise.resolve();
}

// 分析文件的引用关系
function analyzeFile(filePath, targetFile) {
  fileNum++;
  return new Promise(resolve => {
    const stream = fs.createReadStream(filePath, { encoding: 'utf8' });
    let fileContent = '';

    stream.on('data', data => {
      fileContent += data;
    });

    stream.on('end', () => {
      const importStatements = fileContent.match(/import\s+.*?from\s+(['"].*?['"])/g);
      if (importStatements) {
        importStatements.forEach(importStatement => {
          const relativePathMatch = importStatement.match(/import\s+.*?from\s+(['"])(.*?)\1/);
          if (relativePathMatch) {
            let relativePath = relativePathMatch[2];
            // 将@/ 替换成 src/
            relativePath = relativePath.replace('@/', 'src/');
            // 检测是否有相对路径 - ../ 或 ./
            if (isRelativePath(relativePath)) {
              relativePath = pathResolve(dirname(filePath), relativePath);
            }
            const resolvePathMatch = relativePath.match(/src\/(.*)/);
            if (resolvePathMatch) {
              const fullResolvePath = resolvePathMatch[0];
              if (fullResolvePath === targetFile) {
                referenceFileList.push(filePath);
              }
            }
          }
        });
      }
      resolve(filePath);
    });
    stream.on('error', error => {
      reject(error);
    });
  });
}

// 判断以../ ./相对路径开头的引用路径
function isRelativePath(path) {
  return /^(\.\.\/|\.\/)/.test(path);
}

// 输出查询结果
async function printReferences() {
  try {
    await Promise.all(asyncFilePromiseList);
    console.table(referenceFileList);
    console.log(`路径：${targetPath}, 共有${referenceFileList.length}个文件被引用`);
    console.log(`- src目录下已查询${fileNum}个文件 - `);
  } catch (error) {
    console.error('Error occurred:', error);
  }
}

// 初始化 - 处理终端输入
function handleCommandLineInput() {
  const targetFiles = process.argv.slice(2);
  if (targetFiles.length > 0) {
    const rootDir = './src'; // 修改为你的项目根目录下的 src 目录
    targetPath = targetFiles[0];
    targetPath = targetPath.replace('.ts', '');

    process.stdout.write('🚀正在执行中...');

    const startTime = new Date().getTime();
    const updateExecutionTime = setInterval(() => {
      const currentTime = new Date().getTime();
      const elapsedTime = Math.floor((currentTime - startTime) / 1000);
      process.stdout.write(`🚀正在执行中，已执行${elapsedTime}秒\r`);
    }, 1000);
    traverseDirectory(rootDir, targetPath)
      .then(() => {
        printReferences();
      })
      .catch(error => {
        console.error('Error occurred:', error);
      })
      .finally(() => {
        clearInterval(updateExecutionTime);
        process.stdout.write('\n'); // 换行
        process.stdout.write('- 执行结束，以下为执行结果 -');
        process.stdout.write('\n'); // 换行
      });
  } else {
    console.log('请输入正确的查询格式');
  }
}

// 执行处理终端输入的函数
handleCommandLineInput();
