import { GET, POST } from '@/services/api';

export function orderContinueValidate(data: { order_id: string }) {
  return GET('/super/v2-order/order-continue-validate', data);
}

export function previewRenewBuyoutPrice(data: { order_id: string; end_date: string }) {
  return POST('/super/v2-order/preview-renew-buyout-price', data);
}

/** 获取价格预览 */
export function getPreviewRenewPrice(data: any) {
  return POST('/super/v2-order/preview-renew-price', data);
}

/** 订单续租*/
export function orderContinueRenew(data: any) {
  data.method = 'v2.order.order.renew';
  return POST('/api/gateway', data);
}

export function createRenew(data: any) {
  return POST('/super/order-renew-v2/create', data);
}
