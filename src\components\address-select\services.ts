import { message } from 'ant-design-vue';
import axios from 'axios';

/** 获取城市数据 */
export const fetchCityData = () => {
  return new Promise((resolve, reject) => {
    axios
      .get('https://img1.rrzuji.cn/js/cdn/city.json', {
        headers: {
          contentType: 'application/json',
        },
      })
      .then(
        res => {
          resolve(res.data);
        },
        err => {
          message.error('获取城市数据失败');
          reject(err);
        },
      );
  });
};
