<template>
  <a-modal
    v-model:visible="copyVisible"
    :title="modalData.title"
    width="760px"
  >
    <template #footer>
      <a-button
        type="primary"
        @click="$emit('update:visible', false)"
      >
        知道了
      </a-button>
    </template>
    <div class="modal-content">
      <a-alert
        v-if="modalData.alertShow"
        message="注意事项说明"
        show-icon
        type="warning"
      >
        <template #description>
          <div>
            <div>1. 不同类型的扣款，将会按照不同的比例结算流水，扣款后用户冻结押金会减少，无法撤销</div>
            <div>2. 当订单商品没有设置购买价，无法支持转购买款</div>
            <div>
              3.
              冻结押金小于购买款时，则部分抵扣；若购买款全部抵扣完成，避免用户投诉，商家需前往修改订单状态为交易完成，否则系统将自动处理为交易完成
            </div>
          </div>
        </template>
      </a-alert>
      <div
        v-for="card in modalData.cardList"
        :key="card.cardTitle"
        class="card-container"
      >
        <div class="card-title">
          {{ card.cardTitle }}
        </div>
        <div
          v-for="contentItem in card.content"
          :key="contentItem.title"
          class="card-content"
        >
          <div class="card-icon-title">
            <SmileOutlined
              v-if="contentItem.icon === 'smile'"
              class="little-title-icon"
            />
            <FileTextOutlined
              v-if="contentItem.icon === 'file'"
              class="little-title-icon"
            />
            {{ contentItem.title }}
          </div>
          <div
            v-for="item in contentItem.content"
            :key="item"
            :style="item.style || ''"
          >
            {{ item.text || item }}
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, watch } from 'vue';
import { FileTextOutlined, SmileOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';

import { modalConfigData } from '../config.ts';

export default defineComponent({
  components: {
    SmileOutlined,
    FileTextOutlined,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    freezeType: {
      type: String as PropType<'condition' | 'rule'>,
      default: 'condition',
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const copyVisible = useVModel(props, 'visible', emit);
    const modalData = ref(modalConfigData[props.freezeType]);
    watch(
      () => props.visible,
      val => {
        if (val) {
          modalData.value = modalConfigData[props.freezeType];
        }
      },
    );
    return {
      copyVisible,
      modalData,
    };
  },
});
</script>
<style scoped lang="less">
.card-container {
  margin-top: 16px;
  padding: 16px 24px;
  background: #f9f9fb;
  border-radius: 4px;

  .card-title {
    margin-bottom: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
}

.card-content {
  margin-bottom: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;

  &:last-of-type {
    margin-bottom: 0;
  }

  .little-title-icon {
    padding-right: 5px;
  }
}

.card-icon-title {
  color: rgba(6, 21, 51, 0.85);
}

.modal-content {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
