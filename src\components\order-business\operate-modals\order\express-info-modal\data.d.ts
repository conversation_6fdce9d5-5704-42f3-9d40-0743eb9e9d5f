export interface ITransportData {
  id: string;
  express_type_title: string; // 寄送类型
  waybill_no: string; // 物流单号
  total: string; // 支付金额
  data?: Record<string, any>; // 详情信息
  loading?: boolean; // 详情加载状态
}
type TAfterSaleReturn = {
  // 运单ID
  id: string;
  // 租赁订单号
  order_id: string;
  // 运单号
  waybill_no: string;
  // 运单费用
  total: string;
  // 运单类型枚举值
  express_type: string;
  // 运单类型文案
  express_type_title: string;
};
export interface IExpressData {
  server_send: ITransportData[];
  user_return: ITransportData[];
  after_sale_return: TAfterSaleReturn[];
}

export interface IPrintingData {
  total: string;
  payment_method: string;
  cargo_name: string;
  senderInfo: ISenderInfo;
  receiveInfo: IReceiveInfo;
  value_added_str: string;
  weight_qty: number;
  item_num: number;
  qrcode_base64: string;
  waybill_no: string;
}

export interface IPrintOrders {
  order_id: string;
  imei: string;
  waybill_no: string;
  send_start_time: string;
  printingData: IPrintingData;
}

/** 返回打印数据 */
export interface IPrintData {
  [string]: {
    batchNo: string;
    orders: IPrintOrders[];
    time: string;
  };
}
