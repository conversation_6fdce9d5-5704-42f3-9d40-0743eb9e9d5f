import { ref } from 'vue';
import { getUserDetail } from '../service';

type IInfo = {
  // 新增下单用户 个人
  single_user_count: string;
  // 新增下单用户 企业
  batch_user_count: string;
  // 下单总用户数
  user_count: string;
  // 平均估价
  average_evaluate_price: string;
};

export default function () {
  const info = ref<Partial<IInfo>>({});

  const init = async () => {
    const { data } = await getUserDetail();
    info.value = data;
  };

  init();

  return {
    info,
  };
}
