<template>
  <a-modal
    :destroy-on-close="true"
    title=" 修改退货信息"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-form
      ref="modalFormRef"
      autocomplete="off"
      :label-col="{ span: 6 }"
      :model="formState"
      name="basic"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item
        label="收件人姓名"
        name="return_name"
        :rules="[{ required: true, message: '请输入收件人姓名!' }]"
        :wrapper-col="{ span: 18 }"
      >
        <a-input
          v-model:value="formState.return_name"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="收件人手机号"
        name="return_phone"
        :rules="[{required:true,message:'请输入正确号码',validator: checkReturnPhone }]"
        :wrapper-col="{ span: 18 }"
      >
        <a-input-number
          v-model:value="formState.return_phone"
          :controls="false"
          :min="0"
          placeholder="请输入"
          :precision="0"
          style="width:100%"
        />
      </a-form-item>
      <a-row>
        <a-col :span="12">
          <a-form-item
            label="收件地址"
            :label-col="{ span: 8,offset:3 }"
            name="return_province_name"
            :rules="[{ required: true, message: '请选择省份!' }]"
            :wrapper-col="{span:8,offset:1}"
          >
            <a-select
              v-model:value="formState.return_province_name"
              :filter-option="filterOption"
              :options="provinceOptions"
              placeholder="省"
              show-search
              style="width:105px"
              @change="
                () => {
                  formState.return_city_name = null;
                  formState.return_area_name = null;
                }
              "
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label=""
            name="return_city_name"
            :rules="[{ required: true, message: '请选择城市!' }]"
          >
            <a-select
              v-model:value="formState.return_city_name"
              :filter-option="filterOption"
              :options="cityOptions"
              placeholder="市"
              show-search
              style="width:105px"
              @change="
                () => {
                  formState.return_area_name = null;
                }
              "
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label=""
            name="return_area_name"
            :rules="[{ required: true, message: '请选择市区!' }]"
          >
            <a-select
              v-model:value="formState.return_area_name"
              :filter-option="filterOption"
              :options="areaOptions"
              placeholder="区/县"
              show-search
              style="width:105px"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item
        label=""
        name="return_addr_detail"
        :rules="[{ required: true, message: '请输入详细地址!' }]"
        :wrapper-col="{offset:6}"
      >
        <a-textarea
          v-model:value="formState.return_addr_detail"
          :maxlength="50"
          placeholder="请输入详细地址"
          :row="4"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { useVModel } from '@/hook';
import { message } from 'ant-design-vue';
import {  ref,defineProps,defineEmits } from 'vue';
import { getReturnAddr,updateReAddr } from '../service';
import { IFormState } from '../../recycle-cart/data';
import { useAddress } from '@/pages-stage/recycle/order/self-order-list/composables/use-address';
import type { FormInstance } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },

});
const {
  provinceValue,
  cityValue,
  areaValue,
  addressValue,

  provinceOptions,
  cityOptions,
  areaOptions,

  filterOption,
} = useAddress();
const formState = ref<IFormState>({
  return_name: null,
  return_phone: null,
  return_province_name: provinceValue,
  return_city_name: cityValue,
  return_area_name:areaValue,
  return_addr_detail:addressValue,
})
//检查手机号
const checkReturnPhone = (_: any, return_phone:string) => {
  console.log('aaa',return_phone);

  const phoneRegExp = /^1[3456789]\d{9}$/;
  if (!phoneRegExp.test(return_phone)) {
    return Promise.reject(new Error('请输入正确的手机格式!'));
  } else {
    return Promise.resolve();
  }
}
//获取修改地址信息
const getReturnDetail = () => {
  getReturnAddr(props.orderId).then(({ data }) => {
    // formState.value = {...data}
    formState.value.return_name = data.return_name ?? null;
    formState.value.return_phone = data.return_phone ?? null;
    formState.value.return_province_name = data.return_province_name ?? null;
    formState.value.return_city_name = data.return_city_name ?? null;
    formState.value.return_area_name = data.return_area_name ?? null;
    formState.value.return_addr_detail = data.return_addr_detail ?? null;
  })
}
getReturnDetail()
const emits = defineEmits(['update:visible', 'getRefresh']);
const visible = useVModel(props, 'visible', emits);
const onCancel = () => emits('update:visible', false);
const modalFormRef = ref<FormInstance>();
const handleOk = () => {
  modalFormRef.value.validateFields().then( async() => {
    const res = await updateReAddr({ order_id: props.orderId, ...formState.value })
    message.success(res.message || '操作成功');
    emits('getRefresh')
    emits('update:visible', false);
  }).catch(err => {
    console.log(err)
  })
}
</script>

