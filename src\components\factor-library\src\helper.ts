import { message } from 'ant-design-vue';
import { nanoid } from 'nanoid';

import { createGlobalRequest } from '@/hook/global-manage';

import { IFactorContentData, IFactorContentRule } from './data';
import { IFactorData } from './factor-library';
import { factorModuleDetail, fetchFactorOptions } from './services';

export const { getData: getLibraryData, clearData: clearLibraryData } = createGlobalRequest(factorModuleDetail);

export const { getData: getFactorOptions, clearData: clearFactorOptions } = createGlobalRequest(fetchFactorOptions);

/** 组件数据转化为标准的因子库组件数据 */
export function toFactorLibraryData(data: IFactorContentData = {}): IFactorData {
  const contentRules =
    !data.rules || Array.isArray(data.rules) ? data.rules?.map(item => item.rules as IFactorContentRule) : [data.rules];
  return {
    type: 'rulesRelation',
    relation: data.relation || 'and',
    rules: contentRules?.map(item => ({
      type: 'profileRule',
      factor_code: item.condition,
      function: item.judge,
      value: item.result,
    })),
  } as IFactorData;
}

/** 因子库组件数据转化为组件数据 */
export function toFactorContentData(data?: IFactorData): IFactorContentData {
  try {
    const rules = data?.rules;
    if (!rules?.length) {
      return {
        _id: nanoid(8),
        relationship: 'condition',
        level: 0,
        rules: {},
      };
    }
    if (rules.length === 1) {
      return {
        _id: nanoid(8),
        relationship: 'condition',
        level: 0,
        rules: {
          condition: rules[0].factor_code,
          judge: rules[0].function,
          result: rules[0].value,
        },
      };
    }
    return {
      _id: nanoid(8),
      relationship: 'logic',
      relation: data?.relation || 'and',
      level: 0,
      rules: rules.map(item => ({
        _id: nanoid(8),
        relationship: 'condition',
        level: 1,
        rules: {
          condition: item.factor_code,
          judge: item.function,
          result: item.value,
        },
      })),
    };
  } catch (e) {
    message.error('组件数据解析异常');
    console.error(e);
    return {};
  }
}
