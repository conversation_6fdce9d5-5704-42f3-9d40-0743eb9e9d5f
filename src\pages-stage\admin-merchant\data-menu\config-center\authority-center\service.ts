import { GET, POST, RequestConfig } from '@/services/api';

import { Account } from './data';
const config: RequestConfig = {
  headers: { 'content-type': 'application/json;charset=utf-8' },
  hostType: '<PERSON><PERSON><PERSON>',
};
// 查询商家账户列表
export function getAccountList() {
  return GET(
    '/merchant/rDI/auth/list',
    {},
    {
      hostType: '<PERSON><PERSON><PERSON>',
    },
  );
}

export function checkPhoneExistence(data: { phone: string }) {
  return POST('/merchant/rDI/auth/phoneExistAccount', data, config);
}

// 添加租赁数智账号
export function addAccount(data: Account) {
  return POST('/merchant/rDI/auth/addAccount', data, config);
}

// 发送验证码
export function getCode(data: any) {
  return POST('/merchant/rDI/sentSmsForNewAccount', data, config);
}

// 验证手机号验证码
export function checkCode(data: any) {
  return POST('/merchant/rDI/verifyNewAccountCode', data, config);
}

// 解绑账号
export function unbindAccount(data: { account_id: number }) {
  return POST('/merchant/rDI/auth/unbindingAcc', data, config);
}

// 查询商家可配置权限列表
export function getConfigurationList(data: any) {
  return POST('/merchant/rDI/auth/serverCanConfAuth', data, config);
}
