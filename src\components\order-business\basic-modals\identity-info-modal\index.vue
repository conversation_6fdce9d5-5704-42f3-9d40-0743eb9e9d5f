<template>
  <a-modal
    v-model:visible="bindVisible"
    class="desensitization-modal"
    title="查看信息"
    :width="736"
  >
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <div
        v-if="checkInfoList.length"
        class="msg-list"
      >
        <!--  用户信息  -->
        <div
          v-if="checkInfoList.includes('user_info')"
          class="msg-item"
        >
          <div class="msg-item-title">
            用户信息
          </div>

          <div class="msg-item-content">
            <span>{{ userData?.user_info?.user_id }}</span>
            <span v-if="userData?.user_info?.username">{{ userData?.user_info?.username }}</span>
            <span v-if="userData?.user_info?.idcard">{{ userData?.user_info?.idcard }}</span>
            <span>性别：{{ userData?.user_info?.sex }}</span>
            <span>年龄：{{ userData?.user_info?.age }}</span>
            <span>会员等级：{{ userData?.user_info?.level || 'VIP0' }}</span>
            <span>下单手机号： {{ userData?.user_info?.register_phone || '暂无' }} </span>
          </div>
        </div>
        <!--  企业信息  -->
        <div
          v-if="checkInfoList.includes('server_info')"
          class="msg-item"
        >
          <div class="msg-item-title">
            企业信息
          </div>
          <div class="msg-item-content">
            <span>{{ userData?.server_info?.company_name }}</span>
            <span>统一社会信用代码：{{ userData?.server_info?.license_no }}</span>
            <span>法人：{{ userData?.server_info?.legal }}</span>
            <span>法人身份证：{{ userData?.server_info?.legal_idcard }}</span>
          </div>
        </div>
        <!--  预授权信息  -->
        <div
          v-if="checkInfoList.includes('pre_authorization_info')"
          class="msg-item"
        >
          <div class="msg-item-title">
            预授权信息
          </div>
          <div class="msg-item-content">
            <span>
              <template v-if="userData?.pre_authorization_info?.pre_authorization_alipay_id">
                {{ '预授权支付宝id：' + userData?.pre_authorization_info.pre_authorization_alipay_id }}，
                {{ '下单支付宝手机号：' + userData?.pre_authorization_info.pay_order_alipay_phone }}
              </template>
              <template v-else> 非预授权订单 </template>
            </span>
            <span v-if="userData?.pre_authorization_info?.alipay_user_info?.length">
              <span v-if="userData?.pre_authorization_info.alipay_user_info.length > 1">用户存在多个支付宝id</span>
              <span
                v-for="(alipayInfoItem, index) in userData?.pre_authorization_info.alipay_user_info"
                :key="index"
              >
                (支付宝资料：{{ alipayInfoItem?.province }}， {{ alipayInfoItem?.city }}，
                {{ alipayInfoItem?.alipay_user_id }}， {{ '芝麻分等级：' + (alipayInfoItem?.zm_level || '查无') }})
              </span>
            </span>
          </div>
        </div>
        <!--  身份验证信息  -->
        <div
          v-if="checkInfoList.includes('authenication_info')"
          class="msg-item"
        >
          <div class="msg-item-title">
            身份验证信息
          </div>
          <div class="msg-item-content">
            <span>
              (身份验证信息：{{ '姓名：' + (userData?.authenication_info?.name || '暂无') }}，
              {{ '身份证：' + (userData?.authenication_info?.idcard || '暂无') }}，
              {{ '人脸' + facePlatform + 'id：' + (userData?.authenication_info?.face_alipay_id || '查无') }})
            </span>
          </div>
        </div>
        <!--  用户授权身份信息  -->
        <div
          v-if="checkInfoList.includes('user_authoriztion_identity_info')"
          class="msg-item"
        >
          <div class="msg-item-title">
            用户授权身份信息
          </div>
          <div class="msg-item-content">
            <span>
              (用户授权信息：{{ '姓名：' + (userData?.user_authoriztion_identity_info?.name || '暂无') }}，
              {{ '身份证：' + (userData?.user_authoriztion_identity_info?.idcard || '暂无') }}

              )
            </span>
          </div>
        </div>
      </div>

      <a-empty
        v-else
        description="未选择筛选项"
      />
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="submit"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, ref, watch } from 'vue';

import type { IUserData, TUserDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import { getTopWindowUrl } from '@/utils/url';

import { fetchLogList, fetchOrderUserInfo } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  checkInfoList: {
    type: Array as PropType<TUserDataKey[]>,
    default: () => [],
  },
  payStatus: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const userData = ref<IUserData>();
/** 人脸认证平台 */
const facePlatform = computed(() => ([23, 30].includes(props.payStatus) ? '微信' : '支付宝'));

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  fetchOrderUserInfo({
    order_id: [props.orderId],
    check_auth: true,
  })
    .then(res => {
      userData.value = res?.data?.list?.[props.orderId];
      sendFetchLog();
    })
    .finally(() => {
      loading.value = false;
    });
}

interface ILogItem {
  type: number;
  value: string;
}

/** 埋点 */
function sendFetchLog() {
  if (!userData.value) {
    return;
  }
  const logList: ILogItem[][] = [];
  // ============= 用户信息 ============
  if (props.checkInfoList.includes('user_info')) {
    logList[0] = [];
    pushLog(logList[0], userData.value?.user_info?.username, 2);
    pushLog(logList[0], userData.value?.user_info?.idcard, 5);
  }
  // ============= 企业信息 ============
  if (props.checkInfoList.includes('server_info')) {
    logList[1] = [];
    pushLog(logList[1], userData.value?.server_info?.company_name, 8);
    pushLog(logList[1], userData.value?.server_info?.license_no, 9);
    pushLog(logList[1], userData.value?.server_info?.legal, 2);
    pushLog(logList[1], userData.value?.server_info?.legal_idcard, 5);
  }
  // ============= 预授权信息 ============
  if (props.checkInfoList.includes('pre_authorization_info')) {
    logList[2] = [];
    pushLog(logList[2], userData.value?.pre_authorization_info?.pre_authorization_alipay_id, 4);
    pushLog(logList[2], userData.value?.pre_authorization_info?.pay_order_alipay_phone, 1);
    userData.value?.pre_authorization_info?.alipay_user_info?.forEach(alipayUserInfo => {
      pushLog(logList[2], alipayUserInfo?.alipay_user_id, 4);
    });
  }
  // ============= 身份验证信息 ============
  if (props.checkInfoList.includes('authenication_info')) {
    logList[3] = [];
    pushLog(logList[3], userData.value?.authenication_info?.name, 2);
    pushLog(logList[3], userData.value?.authenication_info?.idcard, 5);
    pushLog(logList[3], userData.value?.authenication_info?.face_alipay_id, 4);
  }
  // ============= 用户授权身份信息 ============
  if (props.checkInfoList.includes('user_authoriztion_identity_info')) {
    logList[4] = [];
    pushLog(logList[4], userData.value?.user_authoriztion_identity_info?.name, 2);
    pushLog(logList[4], userData.value?.user_authoriztion_identity_info?.idcard, 5);
  }
  getTopWindowUrl().then(({ url }) => {
    const urlObject = new URL(url);
    const route = urlObject.pathname;
    fetchLogList({
      page_type: 1,
      url: route,
      fields: logList.filter(item => item.length),
    });
  });
}

/** 判断是否有值且未脱敏 */
function pushLog(list: ILogItem[], value: string, type: number) {
  if (Boolean(value) && /^(?!.*(无|暂无|查无)).*$/.test(value) && value.indexOf('*') === -1) {
    list.push({
      field_type: type,
      value: value,
    });
  }
}

function submit() {
  bindVisible.value = false;
}
</script>

<style scoped lang="less">
.msg-list {
  .msg-item {
    margin-bottom: 16px;

    &-title {
      position: relative;
      margin-bottom: 4px;
      padding-left: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: bold;
      font-size: 16px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        background: #3777ff;
        border-radius: 2px;
        transform: translate(0, -50%);
        content: '';
      }
    }

    &-content {
      color: rgba(6, 21, 51, 0.85);

      > span:not(:last-child) {
        margin-right: 10px;
        padding-right: 10px;
        border-right: 1px solid #f0f1f3;
      }
    }
  }
}
</style>
