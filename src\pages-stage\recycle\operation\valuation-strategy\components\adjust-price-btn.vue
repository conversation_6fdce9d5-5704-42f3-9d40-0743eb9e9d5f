<template>
  <a-button
    type="primary"
    @click="validator(() => {})"
  >
    <template #icon>
      <interaction-outlined />
    </template>
    一键调价
  </a-button>
  <!-- 所有商品sku信息 -->
  <adjust-price-drawer
    ref="skuDrawerRef"
    @confirm="validator"
  />
  <!-- 调价异步任务进度 -->
  <adjust-price-modal ref="processRef" />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { InteractionOutlined } from '@ant-design/icons-vue';
import { checkBatchSetting } from '../service';
import AdjustPriceDrawer from './adjust-price-drawer.vue';
import AdjustPriceModal from './adjust-price-modal.vue';

const skuDrawerRef = ref<any>(null);
const processRef = ref<any>(null);

async function validator(callBack?: () => void) {
  const res = await checkBatchSetting();
  callBack && callBack();
  const checkResult = !!res.data.check_result;
  checkResult ? processRef.value.open() : skuDrawerRef.value.open();
}
</script>
