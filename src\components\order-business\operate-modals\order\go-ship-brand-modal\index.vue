<template>
  <a-modal
    v-model:visible="modalVisible"
    class="store-delivery-modal"
    destroy-on-close
    :footer="null"
    style="width: 575px"
    title="门店发货"
    @cancel="onCancel"
  >
    <div class="flex-wrap flex-y-center">
      <img
        class="img"
        src="https://img1.rrzuji.cn/uploads/scheme/2406/26/m/1lH8V2u9ax3aKfPNrD2Z.png"
      >
      <div>
        <div>
          你已成功向品牌线下门店【{{ extraData.store_name }}】下发发货指令，门店发货后，订单状态将自动变为【待收货】；
        </div>
        <div>你可以在左侧图片对应位置查看门店发出货物关联的设备串码；</div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { PropType,ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

import { postSyncOrderToLenovo, superPostSyncOrderToLenovo } from './services';

interface IExtraData {
  /**是否是品牌门店订单 */
  is_third_party_store_order?: boolean;
  /** 门店昵称 */
  store_name: string;
  /** 是否已经同步过品牌门店订单 */
  is_sync?: boolean;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

function onCancel() {
  bindVisible.value = false;
}

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emits);

const modalVisible = ref(false);

const isSuper = useRoute().query.role === 'super';

watch(
  () => bindVisible.value,
  () => {
    if (bindVisible.value) {
      dataSynchronization();
    } else {
      modalVisible.value = false;
    }
  },
  { immediate: true },
);

async function dataSynchronization() {
  // 如果是已经同步过联想订单数据了，就直接显示弹框
  if (props.extraData.is_sync) {
    modalVisible.value = true;
    return;
  }
  // 调用联想接口同步订单数据
  // 请求接接口，成功后才显示弹框
  try {
    message.loading('同步数据中...', 0);
    const api = isSuper ? superPostSyncOrderToLenovo : postSyncOrderToLenovo;
    await api({ order_id: props.orderId });
    message.destroy();
    modalVisible.value = true;
    // 刷新列表
    emits('refresh', props.orderId, ['data', 'all_remark']);
  } catch (err: any) {
    message.destroy();
    message.error(err.error);
    bindVisible.value = false;
  }
}
</script>

<style lang="less" scoped>
.img {
  width: 297px;
  height: 297px;
  margin-right: 16px;
}
</style>
