### AreaCascader 通用表单组件

`AreaCascader` 是基于 `a-cascader` 级联器组件提供省市区选择地址能力，目前已注册到全局组件中。

#### 代码演示

```vue
<template>
  // 单选
  <area-cascader v-model:modelValue="selectValue" />

  // 多选, 默认选择到区
  <area-cascader
    v-model:modelValue="selectValue"
    :extra="{
      multiple: true,
    }"
  />

  // level：4，可选择到街道
  <area-cascader
    v-model:modelValue="selectValue"
    :extra="{
      level: 4,
      multiple: true,
    }"
  />

  // ps，由于四级街道数据是动态获取的，所以再回显的时候需要手动调用 `setDynamicData` 方法获得该层级对应的四级街道数据

  <area-cascader
    v-if="visible"
    ref="areaCascaderRef"
    v-model:value="selectValue"
    :extra="{
      level: 4,
      multiple: true,
    }"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
const selectValue = ref();

function edit() {
  areaCascaderRef.value?.setDynamicData();
}
</script>
```

#### 组件属性

| 属性名     | 类型   | 默认值 | 必填 | 描述                      |
| ---------- | ------ | ------ | ---- | ------------------------- |
| level      | number | 3      | 否   | 可选择层级 1-省 2-市 3-区 |
| modelValue | Array  | []     | 是   | 双向绑定（v-model）       |
| extra      | Object | {}     | 否   | 组件额外拓展内容          |

<!-- ## 组件事件 -->

<!-- ## 组件插槽 -->

<!-- ## 组件示例导出 -->
