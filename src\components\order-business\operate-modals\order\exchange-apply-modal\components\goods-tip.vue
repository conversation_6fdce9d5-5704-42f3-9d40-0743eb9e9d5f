<template>
  <!-- 应发货品 -->
  <div class="delivery-card">
    <div class="delivery-card-header">
      <span style="padding-right: 4px">应发货品:</span>
      <span class="delivery-message">{{ goodsName }}</span>
    </div>
    <div class="delivery-hint">
      <img
        alt="警告"
        class="delivery-hint-icon"
        src="https://img1.rrzuji.cn/uploads/scheme/2412/10/m/zJcXlFBzqEA4KeJPCbLn.png"
      >
      <span style="padding: 0 16px 0 4px">
        {{ isGrayscale ? '若当前无库存，可与用户协商换货，请选择正确的换货原因' : ' 若与订单信息不一致，可申请换货说明原因' }}
      </span>
      <template v-if="!isGrayscale">
        <img
          alt="注意"
          class="delivery-hint-icon"
          src="https://img1.rrzuji.cn/uploads/scheme/2305/27/m/HNkug7nKudQeVmH9iXKK.png"
        >
        <span>若无当前库存，可与用户协商换货</span>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  goodsName?: string;
  isGrayscale: boolean;
}>();
</script>

<style lang="less" scoped>
.delivery-card {
  position: relative;
  margin-bottom: 24px;
  padding: 16px;
  color: rgba(6, 21, 51, 1);
  background-color: #f0f7ff;
  border-radius: 4px;

  .delivery-card-header {
    font-size: 16px;
  }

  .delivery-message {
    color: #3777ff;
    font-weight: 500;
  }

  .delivery-hint {
    display: flex;
    align-items: center;
    padding-top: 10px;
  }

  .delivery-hint-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
