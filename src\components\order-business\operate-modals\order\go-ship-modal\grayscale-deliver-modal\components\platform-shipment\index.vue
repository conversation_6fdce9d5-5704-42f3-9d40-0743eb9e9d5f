<template>
  <div
    class="flex-con flex-wrap flex-vertical flex-gap-16"
    style="position: relative"
  >
    <!-- 配件清单 -->
    <Accessory
      v-if="accessoryData?.length"
      class="accessory-pill"
      :data="accessoryData"
    />
    <!-- 是否是全新（仅激活）设备 -->
    <template v-if="hasDeviceAuth">
      <a-tabs
        v-model:activeKey="state.activate_only"
        class="clear-tab"
        @change="refreshStock"
      >
        <a-tab-pane
          :key="false"
          tab="全新"
        />
        <a-tab-pane :key="true">
          <template #tab>
            <div
              class="flex-wrap flex-con"
              style="align-items: center"
            >
              全新（仅激活）
              <img
                src="https://img1.rrzuji.cn/uploads/scheme/2412/07/m/VfiQCzPbFIAZ3gvqHGMo.png"
                style="width: 16px"
              >
            </div>
          </template>
        </a-tab-pane>
      </a-tabs>
    </template>
    <!-- 手动选择下发商品 -->
    <template v-if="showSearchStock">
      <SelectModel
        v-model:stockStatusLoading="state.stockStatusLoading"
        :brand-id="data.pdm_brand_id"
        :category-id="data.pdm_category_id"
        :sku-id="data.sku_id"
        @get-stock="getStockHandle"
      />
    </template>
    <template v-if="showSelectControls">
      <MiddleReminder
        v-bind="{
          skuId: data.sku_id,
          hasDeviceAuth,
          state,
          isNewOnlineServer: data.is_new_online_server,
          isNew: data.is_new,
          orderId: data.order_id,
          reserveStatus: data.reserve_status,
          skuInfo: data.sku_info || state.sku_info,
          stockStatus,
        }"
        @refresh="() => emit('refresh')"
      />
      <!-- 新网商家才有的票据选择 -->
      <template v-if="data?.is_new_online_server && !state.is_checked">
        <a-radio-group
          v-model:value="bindInvoice"
          style="margin-bottom: 16px"
          @change="handleRefreshStock"
        >
          <a-radio-button :value="2">
            有票
          </a-radio-button>
          <a-radio-button
            :disabled="data.is_new"
            :value="3"
          >
            无票
          </a-radio-button>
        </a-radio-group>
      </template>
      <!-- 是否支持监管 「租赁服务设备」、「非租赁服务设备」 -->
      <div class="flex-con flex-wrap">
        <a-radio-group
          v-model:value="state.is_support"
          class="radio-group"
          :options="[
            {
              label: '租赁服务设备',
              value: true,
              disabled: isForbid,
            },
            {
              label: '非租赁服务设备',
              value: false,
              disabled: state.activate_only,
            },
          ]"
          @change="handleSupportChange"
        />
        <!-- 预估价格 -->
        <div
          class="radio-group"
          style="margin-right: 16px"
        >
          <div
            v-for="(item, key) in state.warehouseInformation"
            :key="key"
            class="status-item alert-text flex-wrap flex-y-center flex-gap-4"
          >
            <template v-if="item.showPrice && item.inventoryStatus">
              <span>预估价：￥{{ item?.totalPrice }}</span>
              <a-popover>
                <template #content>
                  <div class="flex-wrap flex-vertical flex-gap-8">
                    <div class="flex-wrap flex-y-center flex-gap-8">
                      <InfoCircleOutlined />
                      <span>实际价格按设备下发日期的价格为准</span>
                    </div>
                    <div class="flex-wrap flex-y-center flex-gap-8 alert-text">
                      <div>预估价<br>({{ item?.totalPrice }})</div>
                      <div>=</div>
                      <a-space>
                        <template #split>
                          <span>+</span>
                        </template>
                        <template v-if="item.deviceMoney !== false">
                          <div>设备价<br>({{ item.deviceMoney }})</div>
                        </template>
                        <template v-if="item.inspectMoney !== false && !item.isNew">
                          <div>检测价<br>({{ item.inspectMoney }})</div>
                        </template>
                        <template v-if="item.sendMoney !== false">
                          <div>发货费<br>({{ item.sendMoney }})</div>
                        </template>
                        <template v-if="item.accessoryMoney !== false">
                          <div>配件费<br>({{ item.accessoryMoney }}{{ item.isNew ? `，线下结算` : '' }})</div>
                        </template>
                        <template v-if="item.lockMoney !== false">
                          <div>租赁服务费<br>({{ item.lockMoney }})</div>
                        </template>
                      </a-space>
                    </div>
                  </div>
                </template>
                <InfoCircleOutlined
                  class="cursor"
                  style="font-size: 12px"
                />
              </a-popover>
            </template>
            <template v-else>
              <span>预估价：暂无</span>
            </template>
          </div>
        </div>
        <!-- 库存情况 -->
        <div class="radio-group">
          <div
            v-for="(tag, key) in state.warehouseInformation"
            :key="key"
            class="status-item"
          >
            <a-tag :color="tag.tagType">
              <SyncOutlined :spin="state.stockStatusLoading" />
              {{ tag.newTagText || tag.tagText || '暂无数据' }}
            </a-tag>
          </div>
        </div>

        <!--  全新仅激活的预估价格  -->
        <template v-if="hasDeviceAuth && !state.activate_only && !state.is_checked">
          <div class="division-box">
            <div class="division" />
          </div>
          <div
            class="radio-group"
            style="padding: 6px 0"
          >
            <div
              v-for="(item, key) in activateOnlyWarehouse"
              :key="key"
              class="activate-text"
            >
              <span v-if="item.showPrice && item.inventoryStatus">(全新仅激活：￥{{ item?.totalPrice }})</span>
              <span v-else>(全新仅激活：暂无)</span>
            </div>
          </div>
        </template>
      </div>

      <!-- 是否送检 -->
      <template v-if="hasCheckedAuth">
        <div>
          <a-checkbox
            v-model:checked="state.is_checked"
            class="bold"
            :disabled="hasOneDeviceCode"
            @change="handleInspectOption"
          >
            送检设备（选择后无需采购）
          </a-checkbox>
        </div>
        <a-alert
          v-if="hasInspectTips"
          type="info"
        >
          <template #message>
            <span>当前平台暂无可销售的商家库存，如已有设备送往平台质检，请催促仓库及时质检，也可以</span>
            <span
              style="color: #3777ff; text-decoration: underline"
              @click="handleGoInspect"
            >发起送检单</span>
            <span>，登记设备送往平台质检。</span>
          </template>
        </a-alert>
      </template>

      <!--  换货建议 -->
      <ReplacementProposalTable
        v-if="stockStatus === 1 && (exchangeTableData.length || upgradeTableData.length)"
        :checked="state.is_checked"
        :exchange-table-data="exchangeTableData"
        :is-support="state.is_support"
        :is-upgrade="data?.storage_memory_up_model"
        :upgrade-table-data="upgradeTableData"
        @affirm-barter="affirmBarter"
        @upgrade-barter="upgradeBarter"
      />
    </template>
    <!-- 采购余额展示 -->
    <AccountPriceShow
      :account-amount="accountAmount"
      :is-special-category="isSpecialCategory"
      :show-select-controls="showSelectControls"
      :state="state.priceInfoLoading"
      @get-price-info="getPriceInfo"
      @go-recharge-route="goRechargeRoute"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, readonly, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined, SyncOutlined } from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';

import { useVModel } from '@/hook/use-vmodel/index';
import isSufficientWarehouse from '@/utils/is-sufficient-warehouse';

import type { IAccessoryData, IAcountAmount, IDeliverStore, IExtraData, IModalState } from '../../../data';
import {
  getActivateInventory,
  getPurchaseInfo,
  getPurchaseInfoSuper,
  issueStock,
  sendServerRecharge,
  subwarehouseService,
} from '../../../services';
import Accessory from './components/accessory-pill.vue';
import AccountPriceShow from './components/account-price-show.vue';
import MiddleReminder from './components/middle-reminder.vue';
import ReplacementProposalTable from './components/replacement-proposal-table.vue';
import SelectModel from './components/select-model.vue';
import { useAdviceParams } from './composables/use-exchange-advice';
import useRepTable from './composables/use-rep-table';
defineOptions({ inheritAttrs: false });

type TProps = {
  data?: IDeliverStore;
  stockStatus: number;
  invoice: number;
  extraData: IExtraData;
  accessoryData: IAccessoryData[];
};

const props = withDefaults(defineProps<TProps>(), {
  data: () => ({}),
  stockStatus: 1,
  invoice: 2,
  extraData: () => ({}),
  accessoryData: () => [],
});
const emit = defineEmits(['update:stockStatus', 'refresh', 'update:invoice']);
const setStockStatus = (val: number) => emit('update:stockStatus', val);

const route = useRoute();
const isSuper = route.query.role === 'super';

const { exchangeTableData, upgradeTableData, getTableData } = useRepTable();

const isForbid = ref(false);

const bindInvoice = useVModel(props, 'invoice', emit);

const state = reactive<IModalState>({
  // 「租赁服务设备」或「非租赁服务设备」
  is_support: true,
  // 是否是全新（仅激活）设备
  activate_only: false,
  // 是否送检
  is_checked: false,
  // 手动搜索匹配后的sku_id
  sku_id: undefined,
  sku_info: undefined,
  // 「租赁服务设备」、「非租赁服务设备」选项数据
  warehouseInformation: {
    rental: {},
    nonRental: {},
  },
  // 库存情况加载中
  stockStatusLoading: false,
  // 项目余额加载中
  priceInfoLoading: false,
  //判断是否是一单一结商家
  is_one_pay_account: false,
});

// 全新仅激活设备的分仓信息的记录
const activateOnlyWarehouse = reactive<IModalState['warehouseInformation']>({
  rental: {},
  nonRental: {},
});

// 特殊品类(目前指游戏机)
const isSpecialCategory = computed(() => props.data.special_category);
// 是否可选全新/全新（仅激活）
const hasDeviceAuth = computed(() => !!props.data.device_auth || isActivateInventory.value);

// 是否有送检权限
const hasCheckedAuth = computed(() => !!props.data.is_checked && !isSpecialCategory.value);
// 是否显示手动选择商品
const showSearchStock = computed(() => !isSpecialCategory.value && !props.data.sku_id);
// 是否显示租赁/非租赁选项
const showSelectControls = computed(() => isSpecialCategory.value || props.data.sku_id || state.sku_id);
// 当前项目余额
const accountAmount = computed<IAcountAmount>(() => {
  const { is_support, warehouseInformation, is_one_pay_account } = state;
  const is_lock_machine = is_support ? 'rental' : 'nonRental';
  return {
    is_one_pay_account,
    ...warehouseInformation[is_lock_machine],
  };
});
//送检提示
const hasInspectTips = computed(() => {
  return props.stockStatus === 1 && state.is_checked;
});
//一机一码送检选择disable
const hasOneDeviceCode = computed(() => {
  return Number(props.data.one_device_code);
});

function tagTypeFormatter(inventoryStatus?: boolean) {
  return typeof inventoryStatus === 'boolean' ? (inventoryStatus ? 'success' : 'error') : 'default';
}

function tagTextFormatter(inventoryStatus?: boolean) {
  const INVENTORY_STATUS_MAP = {
    success: '库存充足',
    error: '缺货',
  };
  return INVENTORY_STATUS_MAP[tagTypeFormatter(inventoryStatus)];
}

// 切换租赁服务
function handleSupportChange(ev: any) {
  const { value: currVal } = ev.target;
  const currKey = currVal ? 'rental' : 'nonRental';
  const currInventoryStatus = state.warehouseInformation[currKey].inventoryStatus;
  // 更新当前的库存状态
  if (currInventoryStatus) {
    setStockStatus(2);
  } else {
    setStockStatus(1);
    // 请求换货推荐
    const sku_id = state.sku_id || props.data.sku_id;
    if (!sku_id) return;
    const params = handleWarehouseBaseParams(sku_id);

    const exchangeParams = useAdviceParams(params, currKey === 'rental');
    const upgradeParams = useAdviceParams(params, currKey === 'rental', {
      get_other_sku_type: 2,
      skip_server_config: props.data.storage_memory_up_auth ? undefined : 1,
    });
    getTableData(exchangeParams, upgradeParams, props.data.storage_memory_up_model);
  }
}

const isActivateInventory = ref(false);
/** 获取是否有全新仅激活的库存 */
async function getActivate(sku_id) {
  const { data } = await getActivateInventory({ sku_id, activate_only: 1 });
  isActivateInventory.value = !!data?.distributable;
}

function handleRefreshStock() {
  nextTick(() => {
    refreshStock();
  });
}

/**
 * @Description 初始化
 * @returns {any}
 */
onMounted(async () => {
  setStockStatus(1);
  // 如果是特殊类目，库存充足可直接下发
  if (isSpecialCategory.value) {
    setStockStatus(2);
    // 特殊类目的库存默认为 非租赁-充足
    state.is_support = false;
    state.warehouseInformation = {
      rental: {
        inventoryStatus: false,
      },
      nonRental: {
        // 9-游戏机 13-苹果笔记本
        warehouse_id: props.data.special_category ? 9 : props.data.mac_book ? 13 : 0,
        warehouse_type: 0,
        inventoryStatus: true,
        tagType: 'success',
        tagText: '库存充足',
      },
    };
    return;
  }

  const { sku_id, replace_auth, device_auth } = props.data;

  if (!!sku_id && !device_auth) {
    await getActivate(sku_id);
  }

  //replace_auth为false，hasCheckedAuth必选
  if (hasCheckedAuth.value && (!replace_auth || hasOneDeviceCode.value)) {
    state.is_checked = true;
    bindInvoice.value = 0;
  }
  // 有维护skuid，那么自动查询库存，并择优选择
  if (!!sku_id) {
    // 如果有可选全新/全新（仅激活）的权限
    if (device_auth || isActivateInventory.value) {
      await selectOptimalWarehouseService();
      activateOnlyWarehouseService(sku_id);
    } else {
      selectOptimalWarehouseService();
    }
  }
});
/**
 * @Description 查询全新仅激活设备的分仓信息
 * 1. 「租赁服务设备」、「非租赁服务设备」(本质就是监管机和非监管机)，哪个有货就有些命中对应的那个
 * @returns {any} warehouseInformation库存信息 is_support 库存状态
 */
async function activateOnlyWarehouseService(sku_id: string) {
  // 轮询租赁/非租赁分仓结果、是否有货
  const resultList = await Promise.allSettled(
    [true, false].map(is_lock_machine =>
      subwarehouseService({ ...handleWarehouseBaseParams(sku_id), is_activate_only: true, is_lock_machine }),
    ),
  );

  resultList.forEach(({ status, value }, index) => {
    if (status !== 'fulfilled') return;

    const { data } = value;
    const { warehouse_id, warehouse_type } = data;
    const is_lock_machine = index === 0;

    const inventoryStatus = isSufficientWarehouse({
      warehouse_type,
      warehouse_id,
      regulation: is_lock_machine,
    });

    activateOnlyWarehouse[is_lock_machine ? 'rental' : 'nonRental'] = {
      ...data,
      inventoryStatus,
      tagType: tagTypeFormatter(inventoryStatus),
      tagText: tagTextFormatter(inventoryStatus),
    };
  });

  // 查询预估价格、余额等信息
  const { rental, nonRental } = activateOnlyWarehouse;
  const { warehouse_id: lock_warehouse_id, warehouse_type: lock_warehouse_type, sku_id: lock_sku_id } = rental as any;
  const {
    warehouse_id: unlock_warehouse_id,
    warehouse_type: unlock_warehouse_type,
    sku_id: unlock_sku_id,
  } = nonRental as any;
  const need_condition_skuIds = [lock_sku_id, unlock_sku_id].filter(id => id != sku_id).join(',');

  const params = {
    order_id: props.data.order_id,
    is_activity_only: state.activate_only ? 1 : 0,
    is_checked: state.is_checked ? 1 : 0,
    lock_sku_id,
    lock_warehouse_id,
    lock_warehouse_type,
    unlock_sku_id,
    unlock_warehouse_id,
    unlock_warehouse_type,
    need_condition_skuIds,
  };
  const request = isSuper ? getPurchaseInfoSuper : getPurchaseInfo;

  request(params).then(({ data }) => {
    const {
      evaluatePriceItem: { lock, unlock },
      needConditionSkuIdsRet,
      is_one_pay_account,
    } = data;

    state.is_one_pay_account = !!is_one_pay_account;

    Object.assign(activateOnlyWarehouse.rental, {
      ...lock,
      showPrice: !(lock instanceof Array) && Number(lock?.totalPrice) !== 0,
      newTagText: needConditionSkuIdsRet?.[lock_sku_id]?.text ? '库存充足' : '',
    });
    Object.assign(activateOnlyWarehouse.nonRental, {
      ...unlock,
      showPrice: !(unlock instanceof Array) && Number(unlock?.totalPrice) !== 0,
      newTagText: needConditionSkuIdsRet?.[unlock_sku_id]?.text ? '库存充足' : '',
    });

    // 如果全新的租赁服务设备无货 且 全新仅激活的租赁服务设备有货
    if (!state.warehouseInformation.rental.inventoryStatus && activateOnlyWarehouse.rental.inventoryStatus) {
      // 1.把tab切换到全新仅激活
      state.activate_only = true;
      // 2、更新其余的 state
      state.warehouseInformation = cloneDeep(activateOnlyWarehouse);
      state.is_support = true;
      setStockStatus(2);
    }
  });
}

/**
 * @Description 轮询分仓服务，分配默认选择
 * 1. 「租赁服务设备」、「非租赁服务设备」(本质就是监管机和非监管机)，哪个有货就有些命中对应的那个
 * 2. 如果双方都有货，那么优先选择「租赁服务设备」
 * 3. 没货的需要置灰不允许切换
 * @returns {any}
 */
async function selectOptimalWarehouseService() {
  const sku_id = state.sku_id || props.data.sku_id;
  if (!sku_id) return;

  state.stockStatusLoading = true;
  const params = handleWarehouseBaseParams(sku_id);

  try {
    // 轮询租赁/非租赁分仓结果、是否有货
    const resultList = await Promise.allSettled(
      [true, false].map(is_lock_machine => subwarehouseService({ ...params, is_lock_machine })),
    );

    resultList.forEach(({ status, value }, index) => {
      if (status !== 'fulfilled') return;
      const { data } = value;
      const { warehouse_id, warehouse_type } = data;
      const is_lock_machine = index === 0;
      const inventoryStatus = isSufficientWarehouse({
        warehouse_type,
        warehouse_id,
        regulation: is_lock_machine,
      });
      state.warehouseInformation[is_lock_machine ? 'rental' : 'nonRental'] = {
        ...data,
        inventoryStatus,
        tagType: tagTypeFormatter(inventoryStatus),
        tagText: tagTextFormatter(inventoryStatus),
      };
    });

    /** 舆论问题临时处理 */
    const { pdm_brand_id, pdm_category_id, is_new } = props.data;
    isForbid.value =
      state.warehouseInformation.rental.is_stock_out &&
      !is_new &&
      Number(pdm_category_id) === 34 &&
      Number(pdm_brand_id) !== 26;
    if (isForbid.value) {
      state.warehouseInformation.rental.inventoryStatus = false;
      state.warehouseInformation.rental.tagType = tagTypeFormatter(false);
      state.warehouseInformation.rental.tagText = tagTextFormatter(false);
      setStockStatus(1);
    }
    /** 舆论问题临时处理 */

    const { rental, nonRental } = state.warehouseInformation;
    // 根据分仓结果分配默认选择，都有货优先选租赁设备，否则选非租赁
    state.is_support = !!rental.inventoryStatus;
    // 查询预估价格、余额等信息
    getPriceInfo();
    const stockStatus = rental.inventoryStatus || nonRental.inventoryStatus ? 2 : 1;
    // 只要任一有库存即更新库存状态
    setStockStatus(stockStatus);
    // 如果缺货，查询换货推荐
    if (stockStatus === 1) {
      const exchangeParams = useAdviceParams(params, state.is_support);
      const upgradeParams = useAdviceParams(params, state.is_support, {
        get_other_sku_type: 2,
        skip_server_config: props.data?.storage_memory_up_auth ? undefined : 1,
      });
      getTableData(exchangeParams, upgradeParams, props.data?.storage_memory_up_model);
    }
  } finally {
    state.stockStatusLoading = false;
  }
}

/**
 * @Description 查询预估价格、余额等信息
 * @returns {any}
 */
function getPriceInfo() {
  const ori_sku_id = props.data.sku_id || state.sku_id;
  if (!ori_sku_id) return;
  state.priceInfoLoading = true;
  const { rental, nonRental } = state.warehouseInformation;
  const { warehouse_id: lock_warehouse_id, warehouse_type: lock_warehouse_type, sku_id: lock_sku_id } = rental as any;
  const {
    warehouse_id: unlock_warehouse_id,
    warehouse_type: unlock_warehouse_type,
    sku_id: unlock_sku_id,
  } = nonRental as any;
  const need_condition_skuIds = [lock_sku_id, unlock_sku_id].filter(sku_id => sku_id != ori_sku_id).join(',');

  const params = {
    order_id: props.data.order_id,
    is_activity_only: state.activate_only ? 1 : 0,
    is_checked: state.is_checked ? 1 : 0,
    lock_sku_id,
    lock_warehouse_id,
    lock_warehouse_type,
    unlock_sku_id,
    unlock_warehouse_id,
    unlock_warehouse_type,
    need_condition_skuIds,
  };
  const request = isSuper ? getPurchaseInfoSuper : getPurchaseInfo;
  request(params)
    .then(({ data }) => {
      const {
        evaluatePriceItem: { lock, unlock },
        needConditionSkuIdsRet,
        is_one_pay_account,
      } = data;

      state.is_one_pay_account = !!is_one_pay_account;

      Object.assign(state.warehouseInformation.rental, {
        ...lock,
        showPrice: !(lock instanceof Array) && Number(lock?.totalPrice) !== 0,
        newTagText: needConditionSkuIdsRet?.[lock_sku_id]?.text ? '库存充足' : '',
      });
      Object.assign(state.warehouseInformation.nonRental, {
        ...unlock,
        showPrice: !(unlock instanceof Array) && Number(unlock?.totalPrice) !== 0,
        newTagText: needConditionSkuIdsRet?.[unlock_sku_id]?.text ? '库存充足' : '',
      });
    })
    .finally(() => {
      state.priceInfoLoading = false;
    });
}

/**
 * @Description 手动选择下发货品后处理
 * @param {any} sku_id
 * @returns {any}
 */
function getStockHandle(sku_id?: string, sku_info?: string) {
  state.sku_id = sku_id;
  state.sku_info = sku_info;
  if (!sku_id) {
    setStockStatus(0);
    state.warehouseInformation = {
      rental: {},
      nonRental: {},
    };
    return;
  }
  selectOptimalWarehouseService();
}

/**
 * @Description 换货下发
 * @param {any} sku_id
 * @returns {any}
 */
function affirmBarter(sku_id: string) {
  getForm().then(data => {
    issueStock({
      ...data,
      sku_id,
      old_sku_id: props.data.sku_id,
      c_type: 2, // 标记是灰度版本的下发仓库的换货建议
    }).then(() => {
      message.success('换货下发成功');
      emit('refresh');
    });
  });
}

/**
 * @Description 升级下发
 * @param {any} sku_id
 * @returns {any}
 */
function upgradeBarter(sku_id: string) {
  getForm().then(data => {
    const params = {
      ...data,
      sku_id,
      c_type: 21, //手动下发:21
      storage_memory_up: 1,
    };
    issueStock(params).then(() => {
      message.success('补贴升级下发成功');
      emit('refresh');
    });
  });
}

/**
 * @Description 重新查询库存
 * @returns {any}
 */
function refreshStock() {
  (state.sku_id || props.data.sku_id) && selectOptimalWarehouseService();
}

function handleInspectOption(e) {
  const val = e.target.checked;
  bindInvoice.value = val ? 0 : 2;
  if (hasCheckedAuth.value && !props.data.replace_auth) {
    state.is_checked = true;
    return;
  }
  refreshStock();
}

function handleWarehouseBaseParams(sku_id: string) {
  const { item_num, order_id, device_special_tag, is_new_online_server } = props.data;
  const params = {
    sku_id,
    item_num: Number(item_num),
    order_id,
    up_stock: true,
    is_activate_only: state.activate_only,
    is_checked: state.is_checked,
    server_id: Number(props.extraData.server_id),
    created_port: isSuper ? 1 : 2,
    device_special_tag,
    device_code: props.data.one_device_code,
    is_new_online_server,
  };
  if (is_new_online_server) {
    params.invoice = bindInvoice.value;
  }
  return params;
}

function buildQueryString(params: Record<string, unknown>) {
  return Object.entries(params)
    .filter(([, value]) => value !== undefined) // 过滤掉无效值
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value as string)}`)
    .join('&');
}

async function goRechargeRoute(account_type: string, account_info: any) {
  const { account_id, type, need_recharge, need_recharge_amount } = account_info;
  const { warehouse_id, warehouse_type, is_one_pay_account } = accountAmount.value;

  const params = {
    action: 'goAllRecharge',
    type: type === 'plat' ? 'platform' : 'thirdParty',
    id: account_id,
  };

  if (account_type === 'notPlat' && is_one_pay_account) {
    const { order_id, sku_id: origin_sku_id } = props.data;
    const { is_support, activate_only } = state;
    const warehouseSkuId = state.warehouseInformation[is_support ? 'rental' : 'nonRental'].sku_id || origin_sku_id;
    await sendServerRecharge({
      v3_order_id: order_id,
      sku_id: warehouseSkuId,
      warehouse_id,
      warehouse_type,
      is_activate_only: activate_only ? 1 : 0,
      is_lock: is_support ? 1 : 0,
    });
  }
  if (account_type === 'plat' && need_recharge && need_recharge_amount && is_one_pay_account) {
    params.acc_amount = need_recharge_amount;
  }
  const devUrl = `${window.location.origin}/merchant/dropship/manage/purchasing-management`;
  const testUrl = `${route.query.origin}/supply-chain-account/index`;
  const finalUrl =
    process.env.NODE_ENV === 'development'
      ? `${devUrl}?${buildQueryString(params)}`
      : `${testUrl}?${buildQueryString(params)}`;
  window.open(finalUrl, '_blank');
}

/**
 * @Description 获取下发仓库表单
 * @returns {any}
 */
function getForm() {
  const { is_support, warehouseInformation } = state;
  const key = is_support ? 'rental' : 'nonRental';
  const { warehouse_type, warehouse_id } = warehouseInformation[key];

  const params = {
    type: 'mark',
    markType: 299,
    value: 1,
    is_lock: 2,
    is_grayscale: true, // 标记是灰度版本的下发仓库
    c_type: 1, // 标记是灰度版本的下发仓库的正常查询
    isServer: isSuper ? undefined : 1,
    orderId: props.data.order_id,
    sku_id: props.data.sku_id || state.sku_id,
    is_support: state.is_support ? 1 : 0,
    activate_only: state.activate_only ? 1 : 0,
    is_checked: state.is_checked ? 1 : 0,
    warehouse_type,
    warehouse_id,
  };

  return Promise.resolve(params);
}
function handleGoInspect() {
  window.open(route.query.origin + '/iem-device/index');
}

defineExpose({
  getForm,
  warehouseInformation: readonly(state.warehouseInformation),
  hasInspectTips,
});
</script>

<style lang="less" scoped>
.bold {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
}

.clear-tab {
  :deep(.ant-tabs-nav) {
    margin-bottom: 8px;
  }
}

.accessory-pill {
  position: absolute;
  top: 0;
  right: 0;
}

.alert-text {
  color: var(--ant-error-color);
}

.cursor {
  cursor: pointer;
}

.checkbox-group,
.radio-group {
  position: relative;
  display: flex;
  flex-flow: column wrap;
  gap: 24px;

  .status-item {
    height: 34px;
    line-height: 34px;
  }

  :deep(.ant-radio-wrapper),
  :deep(.ant-checkbox-wrapper) {
    position: relative;
    gap: 8px;
    margin: 0;

    & > span.ant-radio + *,
    & > span.ant-checkbox + * {
      box-sizing: border-box;
      min-width: 130px;
      padding: 5px 0;
      color: rgba(6, 21, 51, 0.85);
      text-align: center;
      background-color: #f5f7fa;
      border: 1px solid #f5f7fa;
      border-radius: 4px;
    }

    &:not(:has(.ant-radio-disabled)) > span.ant-radio + *:hover,
    &:not(:has(.ant-checkbox-disabled)) > span.ant-checkbox + *:hover {
      border-color: var(--ant-primary-color);
      transition: border-color 0.3s linear;
    }

    &:not(:first-child)::before {
      position: absolute;
      top: -29px;
      left: 8px;
      width: 1px;
      height: 34px;
      background-color: rgba(6, 21, 51, 0.06);
      content: '';
    }

    &.ant-radio-wrapper-checked,
    &.ant-checkbox-wrapper-checked {
      & > span.ant-radio + *,
      & > span.ant-checkbox + * {
        color: var(--ant-primary-color);
        background-color: var(--ant-primary-1);
        border-color: var(--ant-primary-color);
        transition: border-color 0.3s linear;
      }

      &::before {
        background-color: var(--ant-primary-color);
      }
    }

    &.ant-radio-wrapper-disabled > .ant-radio-disabled + span,
    &.ant-checkbox-wrapper-disabled > .ant-checkbox-disabled + span {
      color: rgba(0, 0, 0, 0.25);
      background-color: #f5f7fa;
      border-color: #f5f7fa;
      cursor: not-allowed;
    }
  }
}

.action-icon {
  color: var(--ant-primary-color);
  cursor: pointer;
}

.division-box {
  padding: 6px 16px 6px 8px;
  .division {
    height: 100%;
    border-left: 1px solid rgba(6, 21, 51, 0.15);
  }
}

.activate-text {
  height: 34px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 12px;
}

.line-btn {
  color: var(--ant-primary-color);
  border-color: var(--ant-primary-color);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:hover,
  &:active,
  &:focus,
  &:visited {
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    background: var(--ant-primary-color);
    border-color: var(--ant-primary-color);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }
}
</style>
