import { GET, POST, type RequestConfig } from '@/services/api';

const config: RequestConfig = {
  hostType: 'Golang',
};

// 商家门店列表
export function getServerStoreList(params) {
  return GET('/logistics/admin/serverStore/list',params,config);
}

//  闪送预下单
export function flashDeliveryPreOrder(params) {
  return POST('/logistics/admin/iss/order', params,config);
}

// 闪送提交订单
export function flashDeliveryOrderPlace(params) {
  return POST('/logistics/admin/iss/orderPlace', params,config);
}

// 闪送订单详情
export function flashDeliveryOrderDetail(params) {
  return GET('/logistics/admin/issOrder/detail', params,config);
}

