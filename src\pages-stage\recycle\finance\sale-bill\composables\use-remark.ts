import { ref } from 'vue';

import { ISaleBillList, RemarkType } from '../data';
import { ApiGetRemarks } from '../service';

export default () => {
  const orderRemarks = ref<Record<string, RemarkType>>({});

  const getCrmList = async (unionIds: string[], type: string) => {
    return ApiGetRemarks({
      unionIds,
      unionSuffix: '_' + type,
    }).then(res => Object.fromEntries(res.data.map(item => [item.union_id, item])));
  };

  const loadRemark = async (list: ISaleBillList[]) => {
    const orderIds = list.map(item => item.rsp_order_id);
    orderRemarks.value = await getCrmList(orderIds, 'pay.recycle');
  };

  return {
    loadRemark,
    orderRemarks,
  };
};
