<template>
  <a-select
    v-bind="targetAttrs"
    v-model:value="selectedValue"
    @change="handleChange"
    @search="handleSearch"
  >
    <template
      v-if="loading"
      #notFoundContent
    >
      <a-spin size="small" />
    </template>
    <!-- 继承插槽 -->
    <template
      v-for="(_index, name) in slots"
      #[name]="slotData"
    >
      <slot
        :name="name"
        v-bind="slotData || {}"
      />
    </template>
  </a-select>
</template>

<script setup lang="ts" generic="V extends number | number[]">
import { computed, ref, useAttrs, useSlots } from 'vue';
import { debounce } from 'lodash-es';
import { useCommodityCategoryList } from '@/composables/commodity-category';
import { EScene } from '@/utils/enums/commodity';
import { useVModel } from '@/hook';
import type { IOption, TDisabledType } from '@/composables/commodity-category/data';

defineOptions({ inheritAttrs: false });

type TGetOption<T> = T extends (infer U)[] ? TGetOption<U>[] : IOption;

const props = defineProps<{
  /** 绑定值 */
  value?: V;
  /** 禁用项的特殊处理，'show':显示；'enable':启用 */
  disabledType?: TDisabledType;
  /** 可获取到的分类级别：1 一级| 2 二级| 3 品牌| 4 模型, 不填默认取到第4级模型层 */
  level?: 1 | 2 | 3 | 4;
  /** 接口获取的场景，默认1或2(根据用户类型role判断)，1 平台商品 2 商家商品 3 平台订单 4 商家订单 */
  scene?: EScene;
  /** 根据第几层的父id进行筛选（需要传入父级pid获取分类） */
  filterPidLevel?: 1 | 2 | 3;
  /** 父级id，也可使用完整的父级层级id数组（推荐，完整各个父级id数组查询性能更好） */
  pid?: number | (number | undefined)[];
  /** 是否异步搜索名称，与filterPidLevel不能同时使用 */
  asyncSearch?: boolean;
  /** 定义选项 */
  defineOptions?: (options: IOption[]) => IOption[];
}>();

const emit = defineEmits<{
  (e: 'update:value', value?: V): void;
  (e: 'change', value?: V, selectedOptions?: TGetOption<V>): void;
  (e: 'search', value: string): void;
}>();

const selectedValue = useVModel(props, 'value', emit);

/** 搜索框输入内容 */
const searchText = ref('');
/** 远程搜索内容 */
const searchName = ref('');

/** 输入框搜索 */
function handleSearch(value: string) {
  searchText.value = value;
  emit('search', value);
  // 开启异步远程搜索
  if (props.asyncSearch) {
    searchData();
    // 数据为空时直接赋值置空，不需要等待
    if (!value && searchName.value) {
      searchName.value = '';
    }
  }
}

/** 异步防抖搜索 */
const searchData = debounce(() => {
  // 有输入时才触发赋值
  if (searchText.value) {
    searchName.value = searchText.value;
  }
}, 700);

/** 更改选择项 */
function handleChange(value?: V, selectedOptions?: TGetOption<V>) {
  searchText.value && handleSearch(''); // 选择项后，清空搜索内容
  emit('change', value, selectedOptions);
}

const { loading, categoryList } = useCommodityCategoryList(() => ({
  disabledType: props.disabledType,
  level: props.level,
  scene: props.scene,
  filterPidLevel: props.filterPidLevel,
  pid: props.pid,
  isNameSearch: props.asyncSearch,
  searchName: searchName.value || undefined,
}));

const options = computed(() => {
  if (props.defineOptions) {
    return props.defineOptions(categoryList.value);
  }
  return categoryList.value;
});

// 属性继承，可以属性传递继承ant组件
const attrs = useAttrs();
const targetAttrs = computed(() => {
  return {
    allowClear: true,
    placeholder: '请选择',
    showSearch: true,
    notFoundContent: loading.value ? undefined : null,
    ...attrs,
    loading: loading.value,
    optionFilterProp: 'label',
    options: options.value,
  };
});

// 继承插槽
const slots = useSlots();

// 向外暴露
defineExpose({
  options,
});
</script>
