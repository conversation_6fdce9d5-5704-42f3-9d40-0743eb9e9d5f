<template>
  <a-modal
    class="shan-song-logistics-track-modal"
    :footer="null"
    title="闪送物流轨迹"
    :visible="visible"
    width="800px"
    @cancel="handleCancel"
  >
    <ShanSongLogisticsTrack
      ref="shanSongLogisticsTrackRef"
      height="500px"
      width="752px"
    />
  </a-modal>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import ShanSongLogisticsTrack from '@/components/shan-song-logistics-track/index.vue';

import { getSSTransportDetail, superGetSSTransportDetail } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const route = useRoute();
const isSuper = route.query.role === 'super';

const emits = defineEmits(['update:visible']);
const handleCancel = () => emits('update:visible', false);

const shanSongLogisticsTrackRef = ref();

async function handleGetWarehouseList() {
  const api = isSuper ? superGetSSTransportDetail : getSSTransportDetail;
  const { data } = await api({
    order_id: props.orderId,
  });
  await nextTick();
  shanSongLogisticsTrackRef.value.openBMapTrack(data[0]);
}

watch(
  () => props.visible,
  value => {
    if (value) {
      handleGetWarehouseList();
    }
  },
  { immediate: true },
);
</script>
