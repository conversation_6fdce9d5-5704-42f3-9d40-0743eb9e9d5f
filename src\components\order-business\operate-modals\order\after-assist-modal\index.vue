<template>
  <a-drawer
    v-model:visible="afterAssist.visible"
    class="after-assist-modal"
    :closable="false"
    :title="afterAssist.info.work_type_text"
    :width="640"
  >
    <template #extra>
      <CloseOutlined @click="afterAssist.visible = false" />
    </template>
    <a-spin :spinning="afterAssist.loading">
      <a-alert
        v-if="editorContent"
        class="editor-content-alert"
        show-icon
        type="info"
      >
        <template #message>
          <div class="message-content">
            <div
              class="editor-content"
              :style="styleObj"
              v-html="editorContent"
            />
            <a-button
              v-if="showOpenBtn"
              size="small"
              type="link"
              @click="currentOpen = !currentOpen"
            >
              {{ currentOpen ? '收起' : '展开' }}
            </a-button>
          </div>
        </template>
      </a-alert>
      <div class="drawer-content">
        <div class="info">
          <div class="base-data">
            <div class="sub-title">
              基本信息
            </div>
            <div class="good-info">
              <div class="good-info-title">
                订单商品规格
              </div>
              <div
                v-for="item in goodItem"
                :key="item.key"
                class="row"
              >
                <div class="row-label">
                  {{ item.label }}：
                </div>
                <div class="row-value">
                  {{ afterAssist.info.base_info && afterAssist.info.base_info[item.key] }}
                </div>
              </div>
            </div>
            <div class="normal-info">
              <div
                v-for="item in normalItem"
                :key="item.key"
                class="row"
              >
                <div class="row-label">
                  {{ item.label }}：
                </div>

                <div
                  v-if="item.key === 'total_rental_money'"
                  class="row-value"
                >
                  {{ afterAssist.info[item.key] }}
                  <a-tag style="border: none">
                    日租金：{{ afterAssist.info['rental_money'] }}元/天
                  </a-tag>
                </div>

                <div
                  v-else
                  class="row-value"
                >
                  {{ afterAssist.info[item.key] || '--' }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-data">
            <div class="sub-title">
              {{ afterAssist.info.work_type_text }}
              <question-circle-outlined
                v-if="afterAssist.info.type === 11"
                style="padding-left: 10px"
                @click="explainTitle"
              />
            </div>

            <a-form
              ref="FormCompoent"
              class="info-form"
              layout="vertical"
              :model="afterAssist.data"
            >
              <div class="info-form-content">
                <a-form-item
                  v-if="afterAssist.info.type === 1"
                  label="续租租期"
                  name="renewal_time_end"
                >
                  {{ afterAssist.info.renewal_time_start }}~
                  <a-date-picker
                    v-if="!afterAssist.info.renewal_time_end"
                    v-model:value="afterAssist.data.data.renewal_time_end"
                    :disabled-date="disabledDate"
                    value-format="YYYY-MM-DD"
                    @change="
                      afterAssist.changeRenwalData({
                        renewal_time_start: afterAssist.info.renewal_time_start,
                        renewal_time_end: afterAssist.data.data.renewal_time_end,
                        rental_money: afterAssist.data.data.renewal_rental_money,
                      });
                      afterAssist.checkPreview();
                      afterAssist.checkRenwalData();
                    "
                  />
                  <span v-else> {{ afterAssist.info.renewal_time_end }}</span>

                  共续租{{ afterAssist.info.diffDay }}天

                  <div
                    v-if="!afterAssist.info.preview_is_pass"
                    class="err_msg"
                  >
                    {{ afterAssist.info.preview_err_msg }}
                  </div>
                </a-form-item>

                <a-form-item
                  v-if="afterAssist.info.type === 1"
                  label="续租租金"
                >
                  <a-input-number
                    v-model:value="afterAssist.data.data.renewal_rental_money"
                    :max="afterAssist.info.rental_money"
                    :min="0"
                    :step="0.01"
                    @change="
                      afterAssist.changeRenwalData({
                        renewal_time_start: afterAssist.info.renewal_time_start,
                        renewal_time_end: afterAssist.data.data.renewal_time_end,
                        rental_money: afterAssist.data.data.renewal_rental_money,
                      });
                      afterAssist.checkRenwalData();
                    "
                  />
                  总租金{{ afterAssist.data.data.renewal_total_rental_money }}元
                  <div
                    v-if="!afterAssist.info.is_pass"
                    class="err_msg"
                  >
                    {{ afterAssist.info.err_msg }}
                  </div>
                </a-form-item>

                <a-form-item v-if="afterAssist.info.type === 2">
                  <!-- 选择退款流水 -->
                  <SelectRefundStatement
                    ref="selectRefundStatementRef"
                    :order-id="orderId"
                    :type="ERefundType.ApplyForRefund"
                    :visible-dependency="afterAssist.visible"
                  />
                </a-form-item>

                <a-form-item
                  v-if="afterAssist.info.type === 7"
                  label="申请修改账单支付状态"
                >
                  <a-select
                    v-model:value="afterAssist.data.data.bill_status"
                    :options="payStateAll"
                    placeholder="请选择"
                    style="width: 360px"
                    @change="() => (afterAssist.data.data.period_num = undefined)"
                  />
                </a-form-item>

                <a-form-item
                  v-if="afterAssist.info.type === 3 || afterAssist.info.type === 7"
                  label="申请修改账单期数"
                >
                  <a-select
                    v-model:value="afterAssist.data.data.period_num"
                    mode="multiple"
                    :options="afterAssist.info.type === 7 ? periodOptions : afterAssist.info.periodOptions"
                    placeholder="请选择"
                    style="width: 443px"
                    @change="periodNumChange"
                  />
                </a-form-item>
                <div
                  v-if="afterAssist.data.data.bill_array_list?.length"
                  class="bill-change-list-box"
                >
                  <a-space
                    v-for="item in afterAssist.data.data.bill_array_list"
                    :key="item.value"
                    :size="16"
                    style="margin-bottom: 26px"
                  >
                    <div class="bill-change-list-label">
                      {{ item.label }}
                    </div>
                    <div class="bill-change-list-origin">
                      原金额：￥{{ item.origin }}
                    </div>
                    <a-space>
                      <span class="bill-change-list-change">申请修改金额</span>
                      <a-input-number
                        v-model:value="item.bill_money"
                        :min="0"
                        placeholder="请输入"
                        :step="0.01"
                      />
                      元
                    </a-space>
                  </a-space>
                  <div>
                    <a-space>
                      <div class="bill-change-list-label">
                        合计
                      </div>
                      <div class="bill-change-list-origin">
                        累计原金额：{{ billMoneySummary.origin }}
                      </div>
                      <span class="bill-change-list-change">累计修改后金额：{{ billMoneySummary.bill_money }}</span>
                    </a-space>
                  </div>
                </div>
                <template v-if="afterAssist.info.type === EAssistType.changeBillPeriod">
                  <a-table
                    :columns="changeBillPeriodColumns"
                    :data-source="afterAssist.data.updated_period"
                    :pagination="false"
                  >
                    <template #bodyCell="{ column, record, index }">
                      <template v-if="column.dataIndex === 'before_period'">
                        <a-select
                          v-model:value="record.before_period"
                          :options="peropdOptions"
                          style="width: 100%"
                        />
                      </template>
                      <template v-if="column.dataIndex === 'after_period'">
                        <a-select
                          v-model:value="record.after_period"
                          :options="unPeropdOptions"
                          style="width: 100%"
                        />
                      </template>
                      <template
                        v-if="column.dataIndex === 'operation' && index !== afterAssist.data.updated_period.length - 1"
                      >
                        <a-button
                          danger
                          type="link"
                          @click="delUpdatedReriod(index)"
                        >
                          删除
                        </a-button>
                      </template>
                    </template>
                  </a-table>
                </template>
                <!-- 支付金额表单显示条件：修改账单支付状态且状态为已付款，且选择了期数 -->
                <a-form-item
                  v-if="
                    afterAssist.info.type === 7 &&
                      afterAssist.data.data.bill_status === '10' &&
                      afterAssist.data.data.period_num?.length
                  "
                  label="支付金额"
                >
                  <!-- 若只选择一期，则只显示支付金额和输入框 -->
                  <template v-if="afterAssist.data.data.period_num.length === 1">
                    <a-input-number
                      v-model:value="afterAssist.data.data.bill_list[0].pay_money"
                      :min="0"
                      placeholder="请输入"
                      :precision="2"
                      :step="0.01"
                      style="width: 360px"
                    />
                  </template>
                  <!-- 否则列表展示 -->
                  <template v-else>
                    <a-space direction="vertical">
                      <template
                        v-for="item in afterAssist.data.data.bill_list"
                        :key="item.period_num"
                      >
                        <a-space>
                          <span class="period_tag">第{{ item.period_num }}期</span>
                          <a-input-number
                            v-model:value="item.pay_money"
                            :min="0"
                            placeholder="请输入"
                            :precision="2"
                            :step="0.01"
                          />
                          <span>元</span>
                        </a-space>
                      </template>
                      <a-space>
                        <span class="period_tag">合&nbsp;&nbsp;计</span>
                        <a-input-number
                          disabled
                          :precision="2"
                          :value="afterAssist.data.data.bill_list?.reduce((acc: number, cur: any) => acc + Number(cur.pay_money), 0)"
                        />
                        <span>元</span>
                      </a-space>
                    </a-space>
                  </template>
                </a-form-item>

                <a-form-item
                  v-if="afterAssist.info.type === 6"
                  label="修改订单归还状态"
                >
                  <div style="display: flex; align-items: center">
                    归还中
                    <i class="right-row" />
                    待归还
                  </div>
                </a-form-item>

                <a-form-item
                  v-if="afterAssist.info.type === 6"
                  label="修改订单归还原因"
                >
                  <a-select
                    v-model:value="afterAssist.data.data.reason_type"
                    :options="reasonTypeList"
                    placeholder="请选择"
                    style="width: 200px"
                  />
                </a-form-item>

                <a-form-item
                  v-if="afterAssist.info.type === 4"
                  label="申请修改总租金"
                >
                  <a-input-number
                    v-model:value="afterAssist.data.data.total_rental_money"
                    :min="0"
                    placeholder="请输入"
                    :step="0.01"
                  />
                </a-form-item>

                <a-form-item
                  v-if="afterAssist.info.type === 4"
                  label="需付/冻结押金"
                >
                  <a-input-number
                    v-model:value="afterAssist.data.data.deposit_need_pay"
                    :min="0"
                    placeholder="请输入"
                    :step="0.01"
                  />
                </a-form-item>

                <!-- 从冻结押金转支付: -->
                <template v-if="afterAssist.info.type === 11">
                  <a-form-item label="从冻结押金转支付">
                    <div class="flex-wrap flex-y-center">
                      <a-input-number
                        v-model:value="afterAssist.data.data.transfer_money"
                        :max="afterAssist.freezeData.max_can_deposit_pay"
                        :min="0"
                        placeholder="请输入"
                        :precision="2"
                        style="width: 200px"
                      />
                      <div class="gray-tips">
                        最多扣取{{ afterAssist.freezeData.max_can_deposit_pay }}元
                      </div>
                    </div>
                  </a-form-item>
                  <a-form-item label="转支付类型">
                    <a-radio-group v-model:value="afterAssist.data.data.transfer_type">
                      <a-radio value="10">
                        租金
                      </a-radio>
                      <a-radio
                        :disabled="!afterAssist.freezeData.is_transfer_buyout"
                        value="11"
                      >
                        购买款
                      </a-radio>
                    </a-radio-group>
                    <div
                      v-if="!afterAssist.freezeData.is_transfer_rent && afterAssist.data.data.transfer_type === '10'"
                      class="red-tips"
                    >
                      需存在未付款账单，才可申请转租金，请商家先申请续租产生账单再选择转租金
                    </div>
                  </a-form-item>

                  <template v-if="afterAssist.data.data.transfer_type === '10'">
                    <a-form-item label="抵扣逾期账单">
                      <a-select
                        v-model:value="afterAssist.data.data.bill_ids"
                        :field-names="{ label: 'period_num', value: 'id' }"
                        :filter-option="(input, option) => option.period_num.indexOf(input) > -1"
                        mode="multiple"
                        :options="afterAssist.freezeData.bad_bill"
                        placeholder="请选择"
                        style="width: 200px"
                      />
                    </a-form-item>
                    <div class="form-item">
                      <span class="form-title"> 逾期账单金额： </span>
                      {{ overdueBillMoney }}元
                    </div>
                  </template>

                  <template v-if="afterAssist.data.data.transfer_type === '11'">
                    <div class="form-item">
                      <span class="form-title"> 商品购买价： </span>
                      {{ Number(afterAssist.freezeData.buyout_money).toFixed(2) }}元
                    </div>
                    <div class="form-item">
                      <span class="form-title"> 购买尾款（以当前已付租金计算）： </span>
                      {{ Number(afterAssist.freezeData.buyout_need_pay).toFixed(2) }}元
                    </div>
                  </template>

                  <div class="form-item">
                    <span class="form-title"> 实际转支付金额： </span>
                    {{ realTransMondey }}元
                    <span
                      v-if="
                        afterAssist.data.data.transfer_type === '10' &&
                          afterAssist.freezeData.advance_money &&
                          Number(afterAssist.freezeData.advance_money) > 0
                      "
                      class="gray-tips"
                    >(因存在预收租金{{
                      Number(afterAssist.freezeData.advance_money).toFixed(2)
                    }}元，目前该订单最多支持转支付{{ mostFreezeMoney }}元)</span>
                  </div>
                </template>
                <!--                修改寄出方式-->
                <ChangeSendWay
                  v-if="afterAssist.info.type === EAssistType.changeSend"
                  v-model:form="afterAssist.data.data"
                  :order-id="orderId"
                  :visible="afterAssist.visible"
                />
              </div>
              <div
                v-if="afterAssist.info.type === 1"
                class="buyout"
              >
                <div class="sub-title">
                  购买尾款（以当前已付租金计算）
                </div>
                <div
                  v-for="(item, index) in afterAssist.info.buyout_need_pay_data"
                  :key="index"
                  class="buyout-item"
                >
                  <div>
                    {{ item.period_num }}期
                    <span style="color: rgba(6, 21, 51, 0.45)">({{ item.start_at }} ~ {{ item.end_at }})</span>
                  </div>
                  <div>{{ item.need_pay }} 元</div>
                </div>
              </div>

              <div
                v-if="afterAssist.info.type === 12"
                class="info-form-content"
                :style="{ marginBottom: [21, 41].includes(extraData.created_by) ? 0 : '24px' }"
              >
                <div class="row">
                  <div class="row-label">
                    补订单用途：
                  </div>
                  <div class="row-value">
                    {{ subTypeMap[extraData.created_by] }}
                  </div>
                </div>
                <div class="row">
                  <div class="row-label">
                    金额：
                  </div>
                  <div class="row-value">
                    {{ afterAssist.info.total_rental_money }}元
                  </div>
                </div>
                <a-form-item
                  v-if="[21, 41].includes(extraData.created_by)"
                  :auto-link="false"
                  label="申请退回用户金额"
                  name="refund_money"
                  :rules="refundRules"
                  style="margin-top: 16px"
                >
                  <a-input-number
                    v-model:value="afterAssist.data.data.refund_money"
                    addon-after="元"
                    :max="afterAssist.info.total_rental_money"
                    :min="0"
                    placeholder="请输入"
                    :precision="2"
                    style="width: 200px"
                    @change="FormCompoent?.validate()"
                  />
                </a-form-item>
              </div>

              <a-form-item
                v-if="afterAssist.info.type === 13"
                label="延长原因分类"
                name="reason_type"
                :rules="delayReasonRule"
              >
                <a-select
                  v-model:value="afterAssist.data.data.reason_type"
                  :options="delayReasonTypeOptions"
                  placeholder="请选择"
                  style="width: 300px"
                  @change="FormCompoent?.validate('proof')"
                />
              </a-form-item>
              <!--              结算比例申诉-->
              <template v-if="afterAssist.info.type === 15">
                <a-form-item
                  label="期望修改为"
                  :name="['data', 'order_ratio']"
                  :rules="roderRatiosRules"
                >
                  <a-input-number
                    v-model:value="afterAssist.data.data.order_ratio"
                    addon-after="%"
                    :max="100"
                    :min="0"
                    :step="0.01"
                  />
                </a-form-item>
                <div>原结算比例为{{ afterAssist.data?.data?.ori_order_ratio.toFixed(2) || 0.0 }}%</div>
              </template>
              <a-divider />

              <div class="sub-title">
                问题描述
              </div>
              <div class="info-form-content">
                <a-form-item
                  :label="feedbackInfo.label"
                  name="feedback"
                  :required="isFeedbackRequired"
                >
                  <a-textarea
                    v-model:value="afterAssist.data.feedback"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                    :maxlength="300"
                    :placeholder="feedbackInfo.placeholder"
                    show-count
                  />
                </a-form-item>

                <a-form-item
                  v-if="isProofNeeded"
                  :label="proofInfo"
                  name="proof"
                  :rules="proofRules"
                >
                  <a-upload-dragger
                    accept=".jpg, .png, .mp3, .wav, .ogg"
                    :before-upload="handleBeforeUpload"
                    :disabled="!customloading"
                    :max-count="4"
                    name="uploadfile"
                    :show-upload-list="false"
                  >
                    <p>
                      <inbox-outlined style="font-size: 48px" />
                    </p>
                    <p>选择文件</p>
                  </a-upload-dragger>

                  <div v-if="customFileList.length > 0">
                    <div
                      v-for="(item, index) in customFileList"
                      :key="index"
                      class="custom-flex"
                    >
                      <div class="custom-list-item">
                        <a
                          :href="item.url"
                          target="_blank"
                        >{{ item.name }}</a>
                      </div>
                      <delete-outlined
                        style="cursor: pointer"
                        @click="deleteCustomFile(item.url)"
                      />
                    </div>
                  </div>
                  <div
                    v-if="customFile"
                    class="custom-flex"
                    style="margin-top: 6px"
                  >
                    <div class="text">
                      {{ customFile.name }}
                    </div>
                    <a-button
                      class="button"
                      :loading="!customloading"
                      size="small"
                      type="primary"
                      @click="handleCustomUpload"
                    >
                      <span v-if="customloading">上传文件</span>
                      <span v-else>上传中</span>
                    </a-button>
                  </div>

                  <div style="margin-top: 5px; color: rgba(6, 21, 51, 0.45)">
                    支持上传jpg、png、mp3、wav、ogg附件，最多可上传4张图片以及1个附件，图片大小建议8M以内，附件大小建议30M以内。
                  </div>
                </a-form-item>
              </div>
            </a-form>
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <div class="bottom-drawer">
        <a-button
          :disabled="judgeDisabled"
          style="margin-left: 8px"
          type="primary"
          @click="submit"
        >
          确认
        </a-button>
      </div>
    </template>
  </a-drawer>

  <a-modal
    v-model:visible="afterAssist.reletTipsVisible"
    :cancel-button-props="afterAssist.reletTipsData.status ? { style: { display: 'none' } } : null"
    :cancel-text="afterAssist.reletTipsData.status ? null : '上一步'"
    class="after-assist-modal"
    :ok-text="afterAssist.reletTipsData.status ? '知道了' : '确定续租'"
    :title="afterAssist.reletTipsData.status ? '申请续租失败' : '续租申请'"
    :width="640"
    @cancel="cancelReletTipsVisible"
    @ok="onReletTipsOk"
  >
    <template v-if="!afterAssist.reletTipsData.status">
      <div class="modal-content">
        <a-alert
          message="温馨提示"
          show-icon
          style="margin-bottom: 24px"
          type="warning"
        >
          <template #icon>
            <info-circle-filled
              :style="{
                fontSize: '14px',
                marginTop: '4px',
              }"
            />
          </template>
          <template #description>
            <div class="description-box">
              <span>该订单当前剩余预授权金额小于续租金额，请谨慎续租。</span>
              <span>1.续租后该订单无法通过预授权进行代扣，续租后若逾期无法收回租金的风险较高；</span>
              <template v-if="afterAssist.reletTipsData.is_merchant">
                <span>2.申请续租后，系统将生成续租账单发送给用户，需引导用户通过主动支付整期续租账单，支付后才续租成功；若存在未支付的续租账单，则无法再次申请新的续租账单。</span>
                <span>3.订单支持最长续租天数：剩余预授权金额可支持续租天数+30天</span>
              </template>
              <template v-else>
                <span>2.申请续租后，系统将生成续租账单发送给用户，用户可选择授权免密代扣方式进行分期支付账单；或是主动支付整个续租账单，续租才生效；</span>
                <span>3.用户若无授权免密代扣，且存在未支付的续租账单，则无法再新申请续租。</span>
              </template>
            </div>
          </template>
        </a-alert>
        <div class="info">
          <div
            v-if="afterAssist.reletTipsData.is_merchant"
            class="base-data"
          >
            <div class="sub-title">
              续租信息
            </div>
            <div class="good-info">
              <div class="good-info-title">
                当前申请续租天数：{{ afterAssist.reletTipsData.renew_apply_day }}天
              </div>
              <div class="row">
                <div
                  class="row-label"
                  style="flex-basis: max-content"
                >
                  剩余预授权金额可支持续租天数：
                </div>
                <div class="row-value">
                  {{ afterAssist.reletTipsData.withhold_day }}日
                </div>
              </div>
              <div class="row">
                <div
                  class="row-label"
                  style="flex-basis: max-content"
                >
                  最长续租天数：
                </div>
                <div class="row-value">
                  {{ afterAssist.reletTipsData.max_renew_total_day }}日
                </div>
              </div>
              <div class="row">
                <div
                  class="row-label"
                  style="flex-basis: max-content"
                >
                  可申请最晚结束租期：
                </div>
                <div class="row-value">
                  {{ afterAssist.reletTipsData.max_renew_date }}
                </div>
              </div>
            </div>
          </div>
          <div class="form-data">
            <div class="info-form">
              <div class="buyout">
                <div class="sub-title">
                  续租账单
                </div>
                <div
                  class="buyout-item"
                  style="flex-direction: column; align-items: normal; justify-content: left"
                >
                  <div
                    v-for="(item, index) in afterAssist.reletTipsData.bills"
                    :key="index"
                    class="bills-text"
                  >
                    <div>
                      <span class="num">{{ item.period_num }}期</span>
                      <span style="color: rgba(6, 21, 51, 0.45)">（{{ item.time_start }}～{{ item.time_end }}）</span>
                    </div>
                    <div>{{ item.bill_money }}元</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div v-if="afterAssist.reletTipsData.status === 1001">
        该订单已存在未生效的续租申请，请引导用户进行付款后再发起新的续租需求
      </div>
      <div
        v-if="afterAssist.reletTipsData.status === 1002"
        style="display: flex; flex-direction: column"
      >
        <span>失败原因：当前申请续租天数超过该订单最长可申请续租时间</span>
        <span style="color: #ff4d4f">(1) 由于订单申请的续租总金额大于剩余可支配预授权金额时，系统将限制该订单最长可续租天数；</span>
        <span style="color: #ff4d4f">(2) 订单最长可续租天数=剩余预授权金额可支持天数+30天。</span>
      </div>
    </template>
  </a-modal>

  <!-- 冻结押金转支付申请说明 -->
  <FreezeTransPayDetail
    v-model:visible="afterAssist.freezeTransPayDetailVisible"
    :freeze-type="afterAssist.freezeTransPayType"
  />
</template>

<script setup lang="ts">
import { computed, type PropType, ref, watch } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { DeleteOutlined, InboxOutlined, InfoCircleFilled, QuestionCircleOutlined } from '@ant-design/icons-vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { ERefundType } from '@/components/order-business/feature-application/select-refund-statement/data.d';
import SelectRefundStatement from '@/components/order-business/feature-application/select-refund-statement/index.vue';
import type { TRefreshDataKey } from '@/components/order-business/typing';

import ChangeSendWay
  from './components/change-send-way.vue';
import FreezeTransPayDetail from './components/freeze-trans-pay-detail.vue';
import useAfterAssist from './composables/use-after-assist';
import useTemplateRichData from './composables/use-template-rich-data';
import { changeBillPeriodColumns, delayReasonTypeOptions, goodItem, normalItem, SUBTYPE } from './config';
import { EAssistType } from './data.d';
import { commonFile, commonImage, sliceFileUpload } from './services';

const subTypeMap = {};
for (const key in SUBTYPE) {
  SUBTYPE[key].forEach(value => {
    subTypeMap[value] = key;
  });
}

interface IExtraData {
  is_asset_order?: number;
  created_by?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 订单id和列表数据
  orderId: {
    type: String,
    default: '',
  },
  modalType: {
    type: Number,
    default: 1,
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const { getRichData, editorContent, showOpenBtn, currentOpen, styleObj } = useTemplateRichData();

const afterAssist = useAfterAssist(props, emit);

const peropdOptions = computed(() => {
  return afterAssist.info.period_num_list?.map(item => {
    return {
      ...item,
      disabled: afterAssist.data.updated_period?.some(dataItem => Number(dataItem.before_period) === Number(item.value)),
    };
  });
});

const unPeropdOptions = computed(() => {
  return afterAssist.info.un_period_num_list?.map(item => {
    return {
      ...item,
      disabled: afterAssist.data.updated_period?.some(dataItem => Number(dataItem.after_period) === Number(item.value)),
    };
  });
});

const FormCompoent = ref<FormInstance>();

function explainTitle() {
  if (afterAssist.info.type === 11) {
    afterAssist.freezeTransPayDetailShow('rule');
  }
}

/**
 * 提交表单
 */
async function submit() {
  await FormCompoent.value?.validate();
  const canSubmit = await interceptData(afterAssist.data);
  if (!canSubmit) {
    return;
  }
  afterAssist.beforeSubmit(afterAssist.data);
}

const selectRefundStatementRef = ref();

async function interceptData(data: any) {
  // 申请退款
  if (afterAssist.info.type === 2) {
    const params = await selectRefundStatementRef.value?.requestParams();
    if (params) {
      data.refund_rental = params.refund_rental;
      data.account_pay_log_refund_apply_id = params.account_pay_log_refund_apply_id;
    }
    return params !== null;
  }
  return true;
}

const payStateAll = [
  {
    value: '1',
    label: '未付款',
  },
  {
    value: '10',
    label: '已付款',
  },
];

// 根据选择的修改账单支付状态，判断租期是否已满足状态修改，若已满足，则选项置灰不可选
const periodOptions = computed(() =>
  afterAssist.info.periodOptions?.map((item: any) => ({
    label: item.label,
    value: item.value,
    disabled: item.bill_status != (afterAssist.data.data.bill_status == '10' ? '1' : '10'),
  })),
);

watch(
  () => afterAssist.data.data?.period_num,
  newVal => {
    let newBillList = [];
    if (Array.isArray(newVal) && newVal?.length) {
      const options = afterAssist.info.periodOptions;
      newBillList = newVal
        .map((item: string) => {
          const option = options.find(o => o.value == item)!;
          const billItem = afterAssist.data.data.bill_list?.find(bill => bill.period_num === item);
          return {
            bill_id: option.id,
            period_num: item,
            pay_money: billItem?.pay_money || Number(option.bill_money) || 0,
          };
        })
        .sort((a, b) => Number(a.period_num) - Number(b.period_num));
    }
    if (afterAssist.data.data) {
      afterAssist.data.data.bill_list = newBillList;
    }
  },
  {
    deep: true,
  },
);

// 申请修改订单归还状态
const reasonTypeList = [
  {
    label: '已和商家核实未归还设备,返回待归还正确状态。',
    value: 1,
  },
  {
    label: '订单查无归还物流信息，返回待归还正确状态。',
    value: 2,
  },
  {
    label: '物流单号显示错误，请重新填写正确物流信息',
    value: 3,
  },
];

//限制当天之前的日期不可选
const disabledDate = (current: any) => {
  return current && current < dayjs(afterAssist.info.renewal_time_start).subtract(1, 'days').endOf('day');
};

// 上传凭证是否必选
const isRequired = computed(() => {
  const { rental_type, overdue, service_type } = afterAssist.info;

  // 短租 逾期60天以下 续租60天以上
  // 短租 未逾期 到期前0-7天 续租60天以上
  // 短租 未逾期 到期前7天以上
  // 长租 逾期 逾期0-7
  // 长租 未逾期
  const speedApplyRequired =
    (rental_type == '0' && overdue < 60 && afterAssist.info.diffDay > 60) ||
    (rental_type == '0' && overdue == '-1' && afterAssist.info.diffDay > 60) ||
    (rental_type == '0' && overdue == '0') ||
    (rental_type == '1' && overdue == '1') ||
    (rental_type == '1' && (overdue == '0' || overdue == '-1'));

  // 短租 逾期 逾期0-7
  // 短租 未逾期
  // 长租 逾期 逾期0-7
  // 长租 未逾期
  const generalApplyRequired =
    (rental_type == '0' && overdue == '1') ||
    (rental_type == '0' && (overdue == '0' || overdue == '-1')) ||
    (rental_type == '1' && overdue == '1') ||
    (rental_type == '1' && (overdue == '0' || overdue == '-1'));
  return Number(service_type) === 0 ? generalApplyRequired : speedApplyRequired;
});

const imageNum = ref<number>(0);
const videoNum = ref<number>(0);
const customFile = ref<any>();
const customloading = ref<boolean>(true);
const customFileList = ref<any>([]);

function handleBeforeUpload(file: any) {
  const types = file.type.split('/');
  const size = file.size;
  if (types[0] !== 'audio' && types[0] !== 'application') {
    if (imageNum.value >= 4) {
      message.error('上传的文件数量不得超过4张');
      return false;
    }
    if (size / 1048576 > 5) {
      message.error('上传的图片大小不得超过5M');
      return false;
    }
    customFile.value = file;
    return false;
  } else {
    if (videoNum.value >= 1) {
      message.error('上传的音频数量不得超过一个');
      return false;
    }
    if (size / 1048576 > 30) {
      message.error('上传的音频大小不得超过30M');
      return false;
    }
    customFile.value = file;
    return false;
  }
}

const xhrSendImage = (file: any) => {
  customloading.value = false;
  const fd = new FormData();
  fd.append('uploadfile', file);
  commonImage(fd).then(res => {
    if (res.data.status === 1) {
      const img = res.data.imgOssServer + res.data.url;
      customloading.value = true;
      afterAssist.data.proof_img.push(img);
      FormCompoent.value?.validate('proof');
      customFileList.value.push({
        url: img,
        name: customFile.value.name,
      });
      customFile.value = '';
      message.success('上传成功');
    } else {
      message.error('上传失败');
    }
  });
};

const xhrSend = (fd: any, cb: any) => {
  customloading.value = false;
  sliceFileUpload(fd)
    .then(res => {
      if (res.data.status === 0) {
        cb && cb();
      } else {
        customloading.value = true;
        return message.error('上传失败，请重试');
      }
    })
    .catch(() => {
      customloading.value = true;
    });
};

const xhrSendMerge = (fd: any) => {
  commonFile(fd).then(res => {
    customloading.value = true;
    if (res.data.status === 0) {
      videoNum.value++;
      afterAssist.data.proof_audio.push(res.data.data.oss_url);
      FormCompoent.value?.validate('proof');
      customFileList.value.push({
        url: res.data.data.oss_url,
        name: customFile.value.name,
      });
      customFile.value = '';
      message.success('上传成功');
    } else {
      message.error('上传失败');
    }
  });
};

const submitUpload = () => {
  const chunkSize = 2 * 1024 * 1024;
  const chunks: any[] = [],
    token = +new Date(),
    name = customFile.value.name.substring(0, customFile.value.name.indexOf('.')),
    ext = customFile.value.name.substring(customFile.value.name.indexOf('.') + 1);
  let chunkCount = 0;
  let sendChunkCount = 0;

  if (!customFile.value) {
    alert('请选择文件');
    return;
  }

  //拆分文件
  if (customFile.value.size > chunkSize) {
    //拆分文件
    let start = 0,
      end = 0;
    while (true) {
      end += chunkSize;
      const blob = customFile.value.slice(start, end);
      console.log('切片大小：' + blob.size);
      start += chunkSize;

      if (!blob.size) {
        //拆分结束
        break;
      }

      chunks.push(blob);
    }
  } else {
    chunks.push(customFile.value.slice(0));
  }
  chunkCount = chunks.length;
  /**
   * 唯一标识
   * 文件名字
   * 总片数
   * 当前索引
   */
  //没有做并发限制，较大文件导致并发过多，tcp 链接被占光 ，需要做下并发控制，比如只有4个在请求在发送
  for (let i = 0; i < chunkCount; i++) {
    const fd = new FormData(); //构造FormData对象
    const file_name = name + token;
    fd.append('filename', file_name);
    fd.append('file', chunks[i]);
    fd.append('blob_num', i + '');
    fd.append('total_blob_num', chunkCount + '');

    xhrSend(fd, () => {
      sendChunkCount += 1;
      if (sendChunkCount === chunkCount) {
        const resultForm = new FormData();
        resultForm.append('filename', file_name);
        resultForm.append('type', ext);
        resultForm.append('total_blob_num', chunkCount + '');
        xhrSendMerge(resultForm);
      }
    });
  }
};

const handleCustomUpload = () => {
  if (!customloading.value) return;
  const types = customFile.value.type.split('/');
  if (types[0] !== 'audio' && types[0] !== 'application') {
    xhrSendImage(customFile.value);
  } else {
    submitUpload();
  }
};

const deleteCustomFile = (fileUrl: string) => {
  let img_index = afterAssist.data.proof_img.indexOf(fileUrl);
  let video_index = afterAssist.data.proof_audio.indexOf(fileUrl);
  let cus_index = customFileList.value.findIndex((item: any) => item.url === fileUrl);
  if (img_index >= 0) {
    imageNum.value--;
    customFileList.value.splice(cus_index, 1);
    afterAssist.data.proof_img.splice(img_index, 1);
  }
  if (video_index >= 0) {
    videoNum.value--;
    customFileList.value.splice(cus_index, 1);
    afterAssist.data.proof_audio = [];
  }
  FormCompoent.value?.validate('proof');
};

const isFeedbackRequired = computed(() => {
  return props.modalType === 13 || SUBTYPE.赔偿款.includes(props.extraData.created_by);
});

const isProofNeeded = computed(() => {
  return afterAssist.info.type !== 11;
});

const isProofRequired = computed(() => {
  const { type, total_rental_money } = afterAssist.info;
  const { created_by: subType } = props.extraData;
  const { refund_money, reason_type } = afterAssist.data.data;
  if (type === 13) return reason_type !== 2;
  if (type === 12) {
    return SUBTYPE.补押金.includes(subType)
      ? Number(refund_money) !== Number(total_rental_money)
      : !SUBTYPE.购买订单.includes(subType);
  }
  return isRequired.value;
});

const feedbackInfo = computed(() => {
  const { type } = afterAssist.info;
  const { created_by: subType } = props.extraData;
  let label = '我要反馈',
    placeholder = '输入续租申请内容/改价内容';
  if (type === 13) {
    label = '延长完结内容';
    placeholder = '请输入延长完结内容';
  }
  if (type === 12) {
    label = '问题描述';
    placeholder = SUBTYPE.赔偿款.includes(subType) ? '请输入赔偿款说明（必填）' : '请输入备注信息';
  }
  return { label, placeholder };
});

const proofInfo = computed(() => {
  if (afterAssist.info.type !== 12) return '上传凭证';
  return SUBTYPE.运费.includes(props.extraData.created_by) ? '上传运费凭证' : '上传沟通凭证';
});

const refundRules = [
  {
    required: true,
    validator: () => {
      const valid = typeof afterAssist.data.data.refund_money === 'number';
      return Promise[valid ? 'resolve' : 'reject'](valid ? undefined : '请输入申请退回用户金额');
    },
  },
];

const delayReasonRule = [
  {
    required: true,
    validator: () => {
      if (!afterAssist.data.data.reason_type) return Promise.reject('请选择延长完结分类');
      return Promise.resolve();
    },
  },
];

const proofRules = computed(() => [
  {
    required: isProofRequired.value,
    validator: () => {
      const { proof_img, proof_audio } = afterAssist.data;
      if (isProofRequired.value && !proof_img?.length && !proof_audio?.length) {
        return Promise.reject('请上传凭证');
      }
      return Promise.resolve();
    },
  },
]);
const roderRatiosRules = [
  { required: true, message: '请填入要修改的比例' },
  {
    trigger: 'change',
    validator: () => {
      const { order_ratio, ori_order_ratio } = afterAssist.data.data;
      if ((order_ratio || order_ratio === 0) && Number(order_ratio) === Number(ori_order_ratio)) {
        return Promise.reject('修改值与原有值不能为同一个值');
      }
      return Promise.resolve();
    },
  },
];
const cancelReletTipsVisible = () => {
  afterAssist.reletTipsVisible = false;
};

const onReletTipsOk = () => {
  if (!afterAssist.reletTipsData.status) {
    afterAssist.data['continue_add_money'] = afterAssist.reletTipsData.continue_add_money;
    afterAssist.data['is_create_order_renew_apply'] = 1;
    submit();
    emit('refresh', props.orderId, ['data', 'all_remark', 'super2_remark']);
  }
  afterAssist.reletTipsVisible = false;
};

/**续租租期、续租租金有报错提醒时，确认按钮禁用 */
const judgeDisabled = computed(() => {
  if (afterAssist.info.type === 11) {
    return !afterAssist.freezeData.is_transfer_rent && afterAssist.data.data.transfer_type === '10';
  }

  if (afterAssist.info.type !== 1) return false;
  return !afterAssist.info.preview_is_pass || !afterAssist.info.is_pass;
});

const overdueBillMoney = computed(() => {
  const billList = new Set(afterAssist.data.data.bill_ids || []);
  const result = afterAssist.freezeData.bad_bill.reduce((pre, cur) => {
    if (billList.has(cur.id)) {
      return pre + Number(cur.bill_money) - Number(cur.pay_money || 0);
    } else {
      return pre;
    }
  }, 0);
  return result.toFixed(2);
});

// 订单最多支持转支付
const mostFreezeMoney = computed(() => {
  // 减去预收金额
  const result = overdueBillMoney.value - (afterAssist.freezeData.advance_money || 0);
  return Number(result > 0 ? result : 0).toFixed(2);
});

const realTransMondey = computed(() => {
  const { transfer_type, transfer_money } = afterAssist.data.data;
  // 租金： 对比填入价 与 最多支持转账
  // 购买款： 对比填入价、购买价、购买尾（取三者最小）
  const { buyout_money, buyout_need_pay } = afterAssist.freezeData;
  let result = 0;
  if (transfer_type === '10') {
    const trans_advance = Number(transfer_money || 0);
    result = Math.min(Number(mostFreezeMoney.value), trans_advance);
  } else if (transfer_type === '11') {
    result = Math.min(Number(buyout_money || 0), Number(buyout_need_pay || 0), Number(transfer_money || 0));
  }
  return result > 0 ? result.toFixed(2) : '0.00';
});

const delUpdatedReriod = (index: number) => {
  (afterAssist.data.updated_period as [])?.splice(index, 1);
};

watch(
  () => props.visible,
  value => {
    if (value) {
      emit('update:visible', false);
      customFileList.value = [];
      customFile.value = '';
      afterAssist.showModal(props.modalType);
      getRichData(props.modalType);
    }
  },
  { immediate: true },
);
// 要修改的账单金额列表
// const billMoneyList = ref([])
function periodNumChange(period_num: string[]) {
  if (!afterAssist?.data?.data?.period_num || afterAssist.info.type !== 3) {
    return (afterAssist.data.data.bill_array_list = []);
  }
  const currentList = afterAssist.data.data.bill_array_list;
  const options = afterAssist.info.periodOptions;
  afterAssist.data.data.bill_array_list = period_num.map(per_item => {
    const currentResult = currentList.find(item => item.value === per_item);
    if (currentResult) {
      return currentResult;
    }
    const result = options.find(item => per_item === item.value) || {};
    return {
      ...result,
      origin: Number(result.bill_money),
      bill_money: Number(result.bill_money),
    };
  });
}

const billMoneySummary = computed(() => {
  return afterAssist.data.data.bill_array_list?.reduce(
    (pre, cur) => ({
      origin: NumberAdd(cur.origin, pre.origin),
      bill_money: NumberAdd(cur.bill_money, pre.bill_money),
    }),
    { origin: 0, bill_money: 0 },
  );
});

function NumberAdd(a: string, b: string) {
  return (Number(a) + Number(b)).toFixed(2);
}
</script>

<style scoped lang="less">
.after-assist-modal {
  .editor-content-alert {
    align-items: flex-start;
    margin-bottom: 24px;

    :deep(.ant-alert-icon) {
      margin-top: 4px;
    }

    .message-content {
      display: flex;
      justify-content: space-between;

      .editor-content {
        width: 100%;
        overflow: hidden;
        word-break: break-all;
      }
    }
  }

  .sub-title {
    margin-bottom: 12px;
    padding-left: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: bold;

    &::before {
      position: relative;
      top: 2px;
      right: 5px;
      display: inline-block;
      width: 4px;
      height: 14px;
      background: #3777ff;
      border-radius: 2px;
      content: '';
    }
  }

  .row {
    display: flex;
    align-items: center;
    margin-top: 8px;

    &-label {
      flex-basis: 84px;
      color: rgba(6, 21, 51, 0.45);
    }

    &-value {
      flex: 1;
      color: rgba(6, 21, 51, 0.85);
    }
  }

  .modal-content {
    height: 500px;
    overflow-y: auto;
  }

  .bottom-drawer {
    display: flex;
    justify-content: flex-end;
  }

  .info {
    .base-data {
      .good-info {
        padding: 16px;
        background-color: #f9f9fb;
        border-radius: 4px;

        &-title {
          color: rgba(6, 21, 51, 0.85);
          font-weight: bold;
        }
      }

      .normal-info {
        margin-top: 16px;
        padding: 0 16px;
      }
    }

    .form-data {
      margin-top: 24px;

      .info-form {
        &-content {
          padding: 0 16px;
        }

        .custom-list-item {
          width: 500px;
          margin: 6px 0;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .custom-flex {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .err_msg {
          margin-top: 10px;
          color: #ff4d4f;
        }

        .right-row {
          display: block;
          width: 16px;
          height: 16px;
          margin: 0 8px;
          background: url('https://img1.rrzuji.cn/uploads/scheme/2212/01/m/L2ZjcybULbgHg1PRUOhl.png') no-repeat;
          background-size: 100% 100%;
        }

        .buyout {
          margin-bottom: 12px;
        }

        .buyout-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 8px;
          padding: 12px 16px;
          background-color: rgb(245, 247, 250);
          border-radius: 4px;
        }
      }
    }
  }

  .description-box {
    display: flex;
    flex-direction: column;
  }

  .bills-text {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;

    .num {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 14px;
    }
  }
}

.red-tips {
  color: #ff4d4f;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.gray-tips {
  padding-left: 10px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.form-title {
  color: rgba(6, 21, 51, 0.85);
}

.form-item {
  margin-bottom: 24px;
}

.period_tag {
  display: inline-block;
  width: 52px;
  padding: 2px 0;
  color: #061533a6;
  font-size: 12px;
  text-align: center;
  background-color: #f9f9fb;
  border: 1px solid #06153326;
  border-radius: 4px;
}

.bill-change-list {
  &-box {
    width: 443px;
    padding: 16px;
    background: #f9f9fb;
  }

  &-label {
    width: 52px;
    height: 22px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    background: #f9f9fb;
    border: 1px solid rgba(6, 21, 51, 0.15);
    border-radius: 4px 4px 4px 4px;
  }

  &-origin {
    min-width: 120px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }

  &-change {
    font-weight: bold;
  }
}
</style>
