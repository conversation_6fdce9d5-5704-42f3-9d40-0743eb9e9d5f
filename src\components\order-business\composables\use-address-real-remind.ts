import { ref, watch } from 'vue';

import { queryServerSensitiveData, queryV2SensitiveData } from '@/services/api';

import { getAddressRemind } from './services';

const TIPS_CONTENT = '签收人信息不符，请提前联系下单人确认信息，确保能本人签收';

// 收货地址姓名请求源
export enum RequestTypeEnum {
  /**
   * 运营后台
   */
  OPERATION,
  /**
   * 商家后台
   */
  SHOP,
}

/**
 * 收货信息实名制提醒
 * @param watchSource
 * @param sensitiveType RequestTypeEnum
 */
export const useAddressRealRemind = (
  watchSource: () => string,
  sensitiveType: RequestTypeEnum = RequestTypeEnum.OPERATION,
) => {
  const showRemind = ref<boolean>(false);
  const resetRemind = () => {
    showRemind.value = false;
  };
  /**
   * 获取提醒
   */
  const getRemind = async (orderId: string) => {
    try {
      // [实名,收货姓名]
      const res = await Promise.all([
        getAddressRemind({ order_id: orderId }),
        sensitiveType === RequestTypeEnum.OPERATION
          ? queryV2SensitiveData({
              id: orderId,
              url: window.location.href,
              field: 'name',
              field_type: 2,
              type: 119,
            })
          : queryServerSensitiveData({
              id: orderId,
              url: window.location.href,
              field: 'name',
              field_type: 2,
              type: 1001,
            }),
      ]);
      // 实名与收货人姓名不一致时提醒，收货姓名去首尾掉空格后判断
      if (res[0].data.user_name && res[1].data.data && res[0].data.user_name !== res[1].data.data.trim()) {
        showRemind.value = true;
      }
    } catch (e) {
      resetRemind();
      console.error('订单用户信息获取失败', e, 'orderId:', orderId);
    }
  };
  watch(
    watchSource,
    v => {
      resetRemind();
      v && getRemind(v);
    },
    { immediate: true, deep: true },
  );
  return {
    remindText: TIPS_CONTENT,
    showRemind,
    getRemind,
    resetRemind,
  };
};
