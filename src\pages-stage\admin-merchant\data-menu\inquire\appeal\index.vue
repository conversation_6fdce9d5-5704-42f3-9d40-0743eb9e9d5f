<template>
  <layout-admin-page
    :navs="['数据', '数据查询', '考核结果查询', pageTitle]"
    :title="pageTitle"
  >
    <template #title-prefix>
      <arrow-left-outlined
        style="margin-right: 16px; font-size: 16px"
        @click="router.go(-1)"
      />
    </template>
    <a-divider style="margin-top: 0" />
    <div class="appeal">
      <a-spin :spinning="recordLoading">
        <section
          v-if="pageType !== '1' && recordResultData.status !== 1"
          class="appeal-count"
        >
          <span class="appeal-count-text">已检查 </span><span class="appeal-count-num">{{ recordResultData.deal_num }}</span> 单
          <a-divider
            style="margin: 0 16px"
            type="vertical"
          />
          <span class="appeal-count-text">已通过 </span><span class="appeal-count-num">{{ recordResultData.pass_num }}</span> 单
        </section>

        <section class="appeal-content">
          <a-row
            align="middle"
            class="item-row"
            :gutter="[0, 16]"
            style="column-gap: 200px"
          >
            <a-col>
              <span class="item-label">考核日期：</span>
              <span class="item-content">{{ filterTime }}（{{ filterCycleTime }}接单订单）</span>
            </a-col>
            <a-col>
              <span class="item-label">考核维度：</span>
              <span class="item-content">常规考核</span>
            </a-col>
          </a-row>
          <a-row
            align="middle"
            class="item-row"
          >
            <span class="item-label">申诉类型：</span>
            <a-radio-group
              v-if="pageType === '1'"
              v-model:value="search.condition.appeal_type"
              name="radioGroup"
              @change="changeAppeal"
            >
              <a-radio :value="1">
                数据不准
              </a-radio>
              <a-radio :value="2">
                客观原因（客观原因导致的无法发货需提交70%以上订单无法发货的证据，否则不予受理且无法提交）
              </a-radio>
            </a-radio-group>
            <span
              v-else
              class="item-content"
            >{{ recordResultData.appeal_type === 1 ? '数据不准' : '客观原因' }}</span>
          </a-row>
          <a-row
            align="middle"
            class="item-row"
          >
            <a-col>
              <span class="item-label">申诉说明：</span>
              <a-input
                v-if="pageType === '1'"
                v-model:value="search.condition.appeal_desc"
                placeholder="请简要说明申诉理由"
                style="width: 511px"
              />
              <span
                v-else
                class="item-content"
              >{{ recordResultData.appeal_desc || '--' }}</span>
            </a-col>
          </a-row>
          <a-row
            v-if="pageType !== '1'"
            align="middle"
            justify="space-between"
          >
            <a-col>
              <span class="item-label">申诉结果说明：</span>
              {{ recordResultData.audit_desc || '--' }}
            </a-col>
            <a-col v-if="pageType !== '1'">
              <img
                :src="statusIcon"
                :width="89"
              >
            </a-col>
          </a-row>
        </section>
      </a-spin>
      <a-divider />

      <div class="table-search">
        <div class="table-search-left">
          订单明细（结果有效统计时间截止{{ filterTime }} 00:00:00）
        </div>
        <a-input
          v-model:value="search.condition.order_id"
          placeholder="搜索订单号"
          style="width: 203px"
          @press-enter="handlePressEnter"
        >
          <template #suffix>
            <SearchOutlined style="color: rgba(6, 21, 51, 0.25)" />
          </template>
        </a-input>
      </div>

      <a-table
        class="appeal-table"
        :columns="tableColumns"
        :data-source="table.list"
        :loading="table.loading"
        :pagination="table.pagination"
        row-key="id"
        :scroll="{ x: 'max-content' }"
        @change="tableChange"
      >
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.key === 'risk_qualitative'">
            {{ table.tableMap.risk_qualitative[text] || '--' }}
          </template>
          <template v-if="boolKeys.includes(column.key)">
            {{ text ? '是' : '否' }}
          </template>
          <template v-if="column.key === 'inspection_dimension'">
            {{ text === 1 ? '是' : '否' }}
          </template>
          <template v-if="column.key === 'reason'">
            {{ table.tableMap.reasonObj[text] || '--' }}
          </template>
          <template v-if="column.key === 'audit_status'">
            <a-badge
              v-if="pageType === '1'"
              :status="table.tableMap.submitStatus[record.isEvidence ? 1 : 0].status"
              :text="table.tableMap.submitStatus[record.isEvidence ? 1 : 0].label"
            />
            <a-badge
              v-else
              :status="table.tableMap.auditStatus[text].status"
              :text="table.tableMap.auditStatus[text].label"
            />
          </template>
          <template v-if="column.key === 'delivery_second'">
            {{ (text / 3600).toFixed(2) + 'h' }}
          </template>
          <template v-if="column.key === 'op'">
            <a-button
              style="padding: 0"
              type="link"
              @click="handleEvidence(record)"
            >
              {{ pageType === '1' ? (record.isEvidence ? '修改证据' : '上传证据') : '查看证据' }}
            </a-button>
          </template>
        </template>
      </a-table>
    </div>

    <template #extra>
      <a-button
        v-if="pageType === '1'"
        :disabled="submitBtnDisabled"
        :loading="submitLoading"
        type="primary"
        @click="submit"
      >
        提交申诉
      </a-button>
    </template>

    <evidence-modal
      v-model:visible="evidenceVisible"
      :data="evidenceData"
      :evidence-type="search.condition.appeal_type"
      :filter-time="(filterTime as string)"
      :page-type="(pageType as string)"
      :record-data="recordData"
      @change-cache-data="table.changeCacheData"
      @change-sub-count="updateSubCount"
      @change-sub-status="table.changeSubStatus"
    />
  </layout-admin-page>
</template>

<script setup lang="ts">
import { computed, h, onMounted, ref } from 'vue';
import type { PaginationProps } from 'ant-design-vue';
import { Modal, message } from 'ant-design-vue';
import { SearchOutlined, ArrowLeftOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useRoute, useRouter } from 'vue-router';

import EvidenceModal from './components/evidence-modal/index.vue';

import useSearch from './composables/use-search';
import useTable from './composables/use-table';

import { getOrderList, ApiSubmitAppeal, ApiGetRecord, ApiGetOrderDetail } from './service';
import { RecordDataType, RecordItem, TableCacheItem } from './data';

const boolKeys = ['server_can_delivery_sign', 'is_delivery', 'no_mark_deliver_pass_48h'];

const search = useSearch();
const table = useTable();
const route = useRoute();
const router = useRouter();

const evidenceVisible = ref<boolean>(false);
const evidenceData = ref<TableCacheItem>({});
const recordData = ref<RecordItem>({});

const { pageType, filterTime, filterCycleTime, distribute_id, appeal_id } = route.query;

const pageTitle = {
  1: '结果申诉',
  2: '申诉记录',
}[pageType as string];

const tableColumns = computed(() => {
  const columns = table.columns;
  columns.forEach(item => {
    if (item.key === 'is_delivery') {
      item.title = `是否${filterTime}前已发货订单`;
    }
  });
  return columns;
});

const submitLoading = ref<boolean>(false);
const submitCount = ref<number>(0);
const submitBtnDisabled = computed(() => {
  let len = Math.round((table.pagination.total || 0) * 0.7);
  return search.condition.appeal_type === 1 ? submitCount.value === 0 : submitCount.value < len;
});

const loadList = () => {
  table.loading = true;
  getOrderList({
    order_id: search.condition.order_id + '',
    distribute_id: distribute_id ? Number(distribute_id) : undefined,
    page_comp: {
      page: table.pagination.current,
      page_size: table.pagination.pageSize,
    },
  })
    .then(res => {
      table.list = (res.data.list || []).map((item: any) => {
        const cacheItem = table.tableCache[search.condition.appeal_type][item.order_id];
        const isEvidence = cacheItem && (cacheItem.isEvidence ? true : false);
        const audit_status = recordResultData.value.status === 1 ? 1 : item.audit_status;

        return {
          ...item,
          isEvidence,
          audit_status,
        };
      });
      table.pagination.total = res.data.page_info.count;
    })
    .finally(() => {
      table.loading = false;
    });
};

/**
 * @description pageType为2时获取申诉详情
 */
const recordLoading = ref<boolean>(false);
const recordResultData = ref<RecordDataType>({});
const statusIcon = computed(() => {
  return {
    1: 'https://img1.rrzuji.cn/uploads/scheme/2311/22/m/L1TN0UIRgEGRBWZBkDsW.png',
    2: 'https://img1.rrzuji.cn/uploads/scheme/2311/08/m/rx94EOppQNiPQak4RflC.png',
    3: 'https://img1.rrzuji.cn/uploads/scheme/2311/08/m/2w98V8IPfVNXEfC7Zo1T.png',
  }[recordResultData.value.status as number];
});
const getRecord = () => {
  if (pageType === '2') {
    recordLoading.value = true;
    ApiGetRecord({
      appeal_id: Number(appeal_id),
    })
      .then(res => {
        recordResultData.value = { ...res.data.appeal, ...res.data.appeal_order_stat };
        search.condition.appeal_type = res.data.appeal.appeal_type;
      })
      .finally(() => {
        recordLoading.value = false;
      });
  }
};

const handlePressEnter = () => {
  table.resetPagination();
  loadList();
};

const handleEvidence = (record: RecordItem) => {
  if (pageType === '1') {
    const cacheData = table.tableCache[search.condition.appeal_type][record.order_id as number] || {
      order_id: record.order_id,
      appeal_type: search.condition.appeal_type,
      file_evidence: [],
    };
    recordData.value = record;
    evidenceData.value = {
      ...cacheData,
      appeal_type: search.condition.appeal_type,
    };
  } else {
    ApiGetOrderDetail({
      appeal_id: Number(appeal_id),
      order_id: record.order_id,
    }).then(res => {
      let { appeal_order_reasons, file_evidence, desc } = res.data.order || {};
      appeal_order_reasons = appeal_order_reasons ? JSON.parse(appeal_order_reasons) : [];
      file_evidence = file_evidence ? JSON.parse(file_evidence) : [];
      const obj = {
        appeal_order_reasons,
        file_evidence,
        desc,
        order_id: record.order_id,
        appeal_type: search.condition.appeal_type,
      };
      evidenceData.value = obj;
    });
  }
  evidenceVisible.value = true;
};

/**
 * @description 更新列表状态
 */
const updateListStatus = () => {
  let count = 0;
  const cacheItem = table.tableCache[search.condition.appeal_type];
  const cacheKeys = Object.keys(cacheItem);
  cacheKeys.forEach(key => {
    cacheItem[key].isEvidence && count++;
  });

  table.list.forEach(item => {
    const index = cacheKeys.indexOf(item.order_id + '');
    if (index !== -1) {
      item.isEvidence = cacheItem[cacheKeys[index]].isEvidence ? true : false;
    } else {
      item.isEvidence = false;
    }
  });
  return count;
};

/**
 * @description 类型切换时表格根据缓存的数据进行更新
 */
const changeAppeal = () => {
  submitCount.value = updateListStatus();
};

const tableChange = (pagination: PaginationProps) => {
  table.pagination.current = pagination.current;
  table.pagination.pageSize = pagination.pageSize;
  updateListStatus();
  loadList();
};

const updateSubCount = (count: number) => {
  submitCount.value = submitCount.value + count;
};

const submit = () => {
  Modal.confirm({
    title: '确认提交？',
    icon: h(ExclamationCircleOutlined),
    content: '确认提交申诉吗？',
    onOk() {
      submitLoading.value = true;
      const postArr: any[] = [];
      const cache = table.tableCache[search.condition.appeal_type];
      Object.keys(cache).forEach(key => {
        const item = cache[key];
        if (item.isEvidence) {
          postArr.push({
            order_id: item.order_id,
            appeal_order_reasons: item.appeal_order_reasons,
            desc: item.desc,
            file_evidence: item.file_evidence,
          });
        }
      });
      const postData = {
        distribute_id: distribute_id ? Number(distribute_id) : undefined,
        appeal_type: search.condition.appeal_type,
        appeal_desc: search.condition.appeal_desc,
        orders: postArr,
      };
      ApiSubmitAppeal(postData)
        .then(() => {
          message.success('操作成功');
          router.push('/data-menu/inquire/examination-result');
        })
        .finally(() => {
          submitLoading.value = false;
        });
    },
  });
};

const initData = async () => {
  await getRecord();
  loadList();
};

onMounted(() => {
  initData();
});
</script>

<style lang="less" scoped>
:deep(.page-top) {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
}

.appeal {
  padding: 0 24px;
  background-color: #fff;

  &-content {
    padding: 24px;
    background: #f9f9fb;
    border-radius: 4px;

    .item-row {
      margin-bottom: 16px;
    }
  }

  &-count {
    margin-bottom: 12px;
    padding: 12px 16px;
    background: #ebfafa;
    border: 1px solid #7fe0da;
    border-radius: 4px;

    &-text {
      color: rgba(6, 21, 51, 0.65);
    }

    &-num {
      color: #00c8be;
      font-weight: bold;
    }
  }

  .table-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 0 8px;

    &-left {
      color: rgba(6, 21, 51, 0.85);
    }
  }
}
</style>
