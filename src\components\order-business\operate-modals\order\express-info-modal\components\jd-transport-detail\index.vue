<template>
  <div class="transport-detail">
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <a-card
        :bordered="false"
        class="card"
        title="物流信息"
      >
        <a-card-grid
          :hoverable="false"
          style="width: 100%"
        >
          物流单号：{{ data.waybill_no }}
          <a
            v-if="data.logistics?.voucher"
            class="sign_order"
            :href="data.logistics.voucher"
            target="_blank"
          >签回单</a>
        </a-card-grid>
        <a-card-grid
          v-if="data.sender_info"
          :hoverable="false"
          style="width: 50%"
        >
          <div>取件信息：{{ data.sender_info.senderName + ' ' + data.sender_info.senderMobile }}</div>
          <div>
            {{ data.sender_info.senderAddress }}
          </div>
        </a-card-grid>
        <a-card-grid
          v-if="data.receive_info"
          :hoverable="false"
          style="width: 50%"
        >
          <div>寄送信息：{{ data.receive_info.receiveName + ' ' + data.receive_info.receiveMobile }}</div>
          <div>
            {{ data.receive_info.receiveAddress }}
          </div>
        </a-card-grid>
        <a-card-grid
          v-if="data.goods_info?.serialNo"
          :hoverable="false"
          style="width: 50%"
        >
          寄送设备串码：{{ data.goods_info.serialNo }}
        </a-card-grid>
      </a-card>
      <a-card
        :bordered="false"
        class="card"
        title="服务信息"
      >
        <a-card-grid
          v-if="data.send_start_time"
          :hoverable="false"
          style="width: 50%"
        >
          预约上门时间：{{ data.send_start_time }}
        </a-card-grid>
        <a-card-grid
          v-if="data.pick_up_time"
          :hoverable="false"
          style="width: 50%"
        >
          实际上门时间：{{ data.pick_up_time }}
        </a-card-grid>
        <a-card-grid
          :hoverable="false"
          style="width: 100%"
        >
          <a-table
            bordered
            :columns="service.columns"
            :data-source="data.serviceInfoList"
            :pagination="false"
          >
            <template #bodyCell="{ column, index }">
              <template v-if="column.key === 'project' && index === 0">
                <span>上门取件</span>
              </template>
            </template>
          </a-table>
        </a-card-grid>
      </a-card>
      <a-card
        v-if="data.billInfoList"
        :bordered="false"
        class="card"
        title="账单明细 (以取件成功后的价格为准)"
      >
        <a-card-grid
          :hoverable="false"
          style="width: 100%"
        >
          <a-table
            bordered
            :columns="billing.columns"
            :data-source="data.billInfoList"
            :pagination="false"
          />
        </a-card-grid>
      </a-card>
      <a-card
        v-if="data.paymentInfoList"
        :bordered="false"
        class="card"
        title="支付信息 (以取件成功后的价格为准)"
      >
        <a-card-grid
          :hoverable="false"
          style="width: 100%"
        >
          <a-table
            bordered
            :columns="pay.columns"
            :data-source="data.paymentInfoList"
            :pagination="false"
          />
        </a-card-grid>
      </a-card>

      <template v-if="!isSuper && expressDataKey === 'server_send'">
        <div class="footer-btn">
          <a-button
            :class="{ 'btns-danger': isWaitSend }"
            :disabled="!isWaitSend"
            @click="dispatch('cancel', 'jd', orderId)"
          >
            取消寄件
          </a-button>
        </div>
      </template>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, type PropType } from 'vue';
import { useRoute } from 'vue-router';
import { noop } from 'lodash-es';

import useBilling from './composables/use-billing';
import usePay from './composables/use-pay';
import useService from './composables/use-service';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default() {
      return {};
    },
  },
  type: {
    type: String as PropType<'sf' | 'jd'>,
    required: true,
  },
  orderId: {
    type: String,
    required: true,
  },
  orderStatus: {
    type: Number,
    required: true,
  },
  haveTips: {
    type: Boolean,
    default: false,
  },
  expressDataKey: {
    type: String,
    required: true,
  },
});

const dispatch = inject('dispatch', noop);

const service = useService(props);
const billing = useBilling(props);
const pay = usePay(props);
const isSuper = useRoute().query.role === 'super';

const data = computed(() => {
  return props.item.data || {};
});
const isWaitSend = computed(() => {
  return props.orderStatus === 2 && !props.haveTips /** 待发货 */;
});
</script>

<style scoped lang="less">
.transport-detail {
  display: flex;
  flex-direction: column;
  align-items: center;

  .card + .card {
    margin-top: 12px;
  }

  :deep(.ant-spin-nested-loading) {
    width: 100%;
  }

  .sign_order {
    margin-left: 5px;
  }

  .alert-box {
    padding: 16px;
    background-color: #fff;
  }

  :deep(.ant-card-body) {
    display: flex;
    flex-wrap: wrap;

    .ant-table-wrapper {
      width: 100%;
    }
  }
}

.footer-btn {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.btns-danger {
  color: #fff;
  background-color: #ff4d4f;
  border: none;
}
</style>
