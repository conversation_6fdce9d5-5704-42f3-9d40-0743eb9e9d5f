<template>
  <div class="logistic-info">
    <div>
      <span>寄件人姓名：{{ logisticInfo?.name }}，</span>
      <span>寄件人手机号：{{ logisticInfo?.phone }}，</span>
      <span>寄件地址：{{ logisticInfo?.addr }}</span>
    </div>
    <div>
      <span>退货收件人：{{ returnLogisticInfo?.name }}，</span>
      <span>退货收件人手机号：{{ returnLogisticInfo?.phone }}，</span>
      <span>退货收件地址：{{ returnLogisticInfo?.addr }}</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { PropType } from 'vue';
import { ILogisticInfo } from '../../data';
defineProps({
  logisticInfo: {
    type: Object as PropType<ILogisticInfo>,
    default() {
      return {};
    },
  },
  returnLogisticInfo: {
    type: Object as PropType<ILogisticInfo>,
    default() {
      return {};
    },
  },
});
</script>
<style scoped lang="less">
.logistic-info {
  color: #061533d9;
}
</style>
