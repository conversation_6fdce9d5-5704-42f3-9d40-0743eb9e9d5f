import { ICommodityCategoryCascaderProps } from '@/components/commodity-category-cascader/src/data';
import FactorCategoryCascader from '@/components/factor-library/src/components/factor-category-cascader.vue';
import { EFactorCondition, EFactorJudge } from '@/utils/enums/factor';

import FactorBatchInput from './components/factor-batch-input.vue';
import FactorInput from './components/factor-input.vue';
import FactorOptionSelect from './components/factor-option-select.vue';
import FactorRangerPicker from './components/factor-ranger-picker.vue';
import InputBetweenNumber from './components/input-between-number.vue';
import { IComponentConfig } from './data';

export const judgeMap = {
  [EFactorJudge.Greater]: '大于',
  [EFactorJudge.GreaterEqual]: '大于等于',
  [EFactorJudge.Equal]: '等于',
  [EFactorJudge.Unequal]: '不等于',
  [EFactorJudge.LessEqual]: '小于等于',
  [EFactorJudge.Less]: '小于',
  [EFactorJudge.Between]: '范围内',
  [EFactorJudge.Include]: '属于',
  [EFactorJudge.UnInclude]: '不属于',
  [EFactorJudge.Like]: '模糊搜索',
};

type TConditionConfig = {
  judges: EFactorJudge[];
  default?: IComponentConfig;
} & Partial<Record<EFactorJudge, IComponentConfig>>;

type TConditionMap = Record<EFactorCondition, TConditionConfig>;

/** 默认条件映射内容 */
export const defaultConditionMap: TConditionMap = {
  [EFactorCondition.InputNumber]: {
    judges: [
      EFactorJudge.Equal,
      EFactorJudge.Unequal,
      EFactorJudge.Greater,
      EFactorJudge.GreaterEqual,
      EFactorJudge.Less,
      EFactorJudge.LessEqual,
      EFactorJudge.Between,
    ],
    default: { target: FactorInput, props: { type: 'number' } as InstanceType<typeof FactorInput> },
    [EFactorJudge.Between]: { target: InputBetweenNumber },
  },
  [EFactorCondition.InputString]: {
    judges: [EFactorJudge.Include, EFactorJudge.UnInclude, EFactorJudge.Like],
    default: { target: FactorBatchInput },
    [EFactorJudge.Like]: { target: FactorInput },
  },
  [EFactorCondition.SingleSelect]: {
    judges: [EFactorJudge.Equal, EFactorJudge.Unequal],
    default: { target: FactorOptionSelect },
  },
  [EFactorCondition.MultiSelect]: {
    judges: [EFactorJudge.Include, EFactorJudge.UnInclude],
    default: { target: FactorOptionSelect, props: { mode: 'multiple' } as InstanceType<typeof FactorOptionSelect> },
  },
  [EFactorCondition.DateTime]: {
    judges: [EFactorJudge.Between],
    [EFactorJudge.Between]: { target: FactorRangerPicker },
  },
};

interface IFactorComponent {
  value: string;
  label: string;
  limit?: EFactorCondition[];
  condition: TConditionMap;
}

/** 因子组件列表（value一旦确定不可更改，命名时请尽量清晰，前缀带webComponent） */
export const factorComponentList: Readonly<IFactorComponent>[] = [
  {
    value: 'webComponentCommodityCategory',
    label: '商品类目',
    limit: [EFactorCondition.SingleSelect, EFactorCondition.MultiSelect],
    condition: {
      [EFactorCondition.SingleSelect]: {
        judges: [EFactorJudge.Equal, EFactorJudge.Unequal],
        default: {
          target: FactorCategoryCascader,
          props: {
            asyncSearch: true,
            changeOnSelect: true,
          } as ICommodityCategoryCascaderProps,
        },
      },
      [EFactorCondition.MultiSelect]: {
        judges: [EFactorJudge.Include, EFactorJudge.UnInclude],
        default: {
          target: FactorCategoryCascader,
          props: {
            multiple: true,
            asyncSearch: true,
            changeOnSelect: true,
          } as ICommodityCategoryCascaderProps,
        },
      },
    },
  },
];
