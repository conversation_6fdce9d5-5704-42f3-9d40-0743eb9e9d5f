export const damageTypeOptions = [
  {
    label: '货损',
    value: '1',
  },
  {
    label: '磨损',
    value: '2',
  },
];

export const initData = {
  list: [
    {
      item: '',
      desc: '',
      damage_type: '',
      money: '',
    },
  ],
  url: [],
};

export const initDataV2 = {
  list: [
    {
      item: '',
      desc: '',
      damage_type: '',
      money: '',
      ratio: null,
      money_calculation_type: 1,
    },
  ],
  url: [],
};

export const bottomCommonShow = [
  { name: '总计赔付金额', key: 'money', extra: '元' },
  { name: '总计赔付项', key: 'template_num', extra: '项' },
];

export const bottomDetailShow = [
  { name: '平台免赔保障补贴', key: 'platform_pay_money', extra: '元' },
  { name: '平台支付状态', key: 'platform_pay_status', isDetail: true },
  { name: '用户支付赔偿款', key: 'user_pay_money', extra: '元' },
  { name: '用户支付状态', key: 'user_pay_status', isDetail: true },
];

export const statusKeys = ['user_pay_status', 'platform_pay_status'];

export const compensationTableCloums = [
  {
    title: '异常项',
    dataIndex: 'item',
    width: 200,
  },
  {
    title: '异常描述（异常程度）',
    dataIndex: 'descAndDegree',
    width: 280,
  },
  {
    title: '货损类型',
    dataIndex: 'damage_type',
    width: 200,
  },
  {
    title: '赔付金额',
    dataIndex: 'money',
    width: 200,
  },
  {
    title: '平台赔付金额',
    dataIndex: 'platform_pay_money',
    width: 200,
  }
];

export const damageTypeMap: Record<string, string> = {
  1: ' 货损',
  2: '磨损',
};
export const statusTextMap = ['未支付', '已支付', '无需支付', '未发起'];

export const statusColorMap = {
  0: 'red-status',
  1: 'green-status',
  2: '',
  3: '',
};

export const maxPlatformPayMoney = 200;
