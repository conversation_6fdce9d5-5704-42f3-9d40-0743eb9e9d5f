<template>
  <div class="flex-wrap flex-wrap-line flex-y-center flex-gap-8">
    <a-select
      v-model:value="provinceKey"
      :field-names="{ value: 'key', label: 'value' }"
      :filter-option="filterOption"
      :loading="loading"
      :options="provinceOptions"
      placeholder="请选择"
      show-search
      style="flex: 1"
      @change="handleProvinceChange"
    />
    <a-select
      v-model:value="cityKey"
      :field-names="{ value: 'key', label: 'value' }"
      :filter-option="filterOption"
      :loading="loading"
      :options="getOptionsByKey(provinceKey)"
      placeholder="请选择"
      show-search
      style="flex: 1"
      @change="handleCityChange"
    />
    <a-select
      v-model:value="areaKey"
      :field-names="{ value: 'key', label: 'value' }"
      :filter-option="filterOption"
      :loading="loading"
      :options="getOptionsByKey(cityKey)"
      placeholder="请选择"
      show-search
      style="flex: 1"
      @change="handleAreaChange"
    />
    <ReloadOutlined
      class="flex-static text-link"
      @click="getAddressData(true)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useInjectFormItemContext } from 'ant-design-vue/es/form';
import { ReloadOutlined } from '@ant-design/icons-vue';

import useAddress from '@/components/address-select/composables/use-address';

import type { IAddressItem, TAddress } from './data';

const props = defineProps<{
  value?: TAddress;
  labels?: TAddress;
}>();

const emits = defineEmits<{
  (e: 'update:value', value: TAddress): void;
  (e: 'update:labels', value: TAddress): void;
}>();

const provinceKey = ref<string>();
const provinceName = ref<string>();
const cityKey = ref<string>();
const cityName = ref<string>();
const areaKey = ref<string>();
const areaName = ref<string>();

const formItemContext = useInjectFormItemContext();

const { loading, provinceOptions, getAddressData, getOptionsByKey, getNameByKey } = useAddress();

watch(
  () => props.value,
  ([province, city, area] = []) => {
    Promise.all([province, city, area].map(key => getNameByKey(key))).then(res => {
      provinceName.value = res[0];
      cityName.value = res[1];
      areaName.value = res[2];
      provinceKey.value = province;
      cityKey.value = city;
      areaKey.value = area;
    });
  },
  { immediate: true },
);

watch(
  () => [provinceKey.value, cityKey.value, areaKey.value],
  () => {
    const values = [provinceKey.value, cityKey.value, areaKey.value] as TAddress;
    const names = [provinceName.value, cityName.value, areaName.value] as TAddress;
    emits('update:value', values);
    emits('update:labels', names);
    formItemContext.onFieldChange();
  },
);

function handleProvinceChange(_value, option: IAddressItem) {
  provinceName.value = option.value;
  cityKey.value = undefined;
  cityName.value = undefined;
  areaKey.value = undefined;
  areaName.value = undefined;
}

function handleCityChange(_value, option: IAddressItem) {
  cityName.value = option.value;
  areaKey.value = undefined;
  areaName.value = undefined;
}

function handleAreaChange(_value, option: IAddressItem) {
  areaName.value = option.value;
}

function filterOption(input: string, option: IAddressItem) {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) > -1;
}
</script>
