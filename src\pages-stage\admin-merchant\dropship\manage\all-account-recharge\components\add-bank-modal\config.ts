import { LocationQueryRaw } from 'vue-router';
import { FormGroupItem } from '@/components/form-create/src/typing';

const addBankModalFormGroup: (FormGroupItem & { exist?: (params: LocationQueryRaw) => boolean })[] = [
  {
    key: 'bank_user_name',
    fragmentKey: 'renderInput',
  },
  {
    key: 'bank_name',
    originProps: { label: '开户银行', name: 'bank_name' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请输入银行名称，如：中国建设银行',
      controls: false,
    },
    fragmentKey: 'renderInput',
  },
  {
    key: 'bank_branch_name',
    originProps: { label: '开户支行', name: 'bank_branch_name' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入开户支行全称', controls: false },
    fragmentKey: 'renderInput',
  },
  {
    key: 'bank_card_num',
    originProps: { label: '银行卡号', name: 'bank_card_num' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入银行卡号', controls: false },
    fragmentKey: 'renderInput',
  },
];

export { addBankModalFormGroup };
