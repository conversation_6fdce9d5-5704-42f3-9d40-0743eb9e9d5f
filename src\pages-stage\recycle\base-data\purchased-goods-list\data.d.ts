export interface ITemplate {
  id?: number | string;
  name: string;
  condition_type: string[];
  qua_check_option: IOption[];
  function_option: IOption[];
  condition_set: IConditionSet[];
}
export interface ISelectOption {
  label: string;
  value: any;
  [key: string]: any;
}
export interface IOption {
  id?: number;
  option?: any[];
  sort?: number;
  title?: string;
  is_multi?: number | boolean;
  [key: string]: any;
}
export interface IConditionSet {
  condition: string;
  setting: {
    type?: string;
    option: IOption[];
  };
}
