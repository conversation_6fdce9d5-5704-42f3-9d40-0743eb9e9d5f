<template>
  <layout-admin-page
    :navs="['趣回收', '财务管理', '企业对账单']"
    title="企业对账单"
  >
    <template #custom-box>
      <div class="custom-box">
        <a-spin :spinning="loading_qhs">
          <DataStatisticsBoard :info="dataStatisticsInfo_qhs" />
        </a-spin>
        <a-spin :spinning="loading_gyl">
          <DataStatisticsBoard :info="dataStatisticsInfo_gyl" />
        </a-spin>
      </div>
    </template>
    <template #extra>
      <a-button
        style="margin-right: 8px; border-radius: 4px"
        @click="down"
      >
        <template #icon>
          <delivered-procedure-outlined />
        </template>
        导出
      </a-button>
      <a-button
        :disabled="selectedRowKeys.length === 0"
        style="border-radius: 4px"
        type="primary"
        @click="bulkRegistration"
      >
        批量结算登记
      </a-button>
    </template>
    <div class="container">
      <div class="search-bar">
        <a-form
          ref="formRef"
          layout="inline"
          :model="formState"
          style="width: 100%"
        >
          <a-row style="gap: 16px 0; width: 100%">
            <a-col :span="6">
              <a-form-item
                label="设备类型"
                name="report_result"
              >
                <a-select
                  v-model:value="formState.report_result"
                  :options="optionsMap.oualityOptions"
                  placeholder="请选择"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="店铺名称"
                name="company"
              >
                <a-input
                  v-model:value="formState.company"
                  :maxlength="30"
                  placeholder="请输入"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="流水号"
                name="trade_no"
              >
                <a-input
                  v-model:value="formState.trade_no"
                  placeholder="请输入"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="订单号"
                name="order_id"
              >
                <a-input
                  v-model:value="formState.order_id"
                  placeholder="请输入"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="状态"
                name="status_list"
              >
                <a-select
                  v-model:value="formState.status_list"
                  mode="multiple"
                  :options="optionsMap.statusOptions"
                  placeholder="请选择"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="账单时间"
                name="time"
              >
                <a-range-picker
                  v-model:value="formState.time"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="结算时间"
                name="close_time"
              >
                <a-range-picker
                  v-model:value="formState.close_time"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-button
                  style="border-radius: 4px"
                  type="primary"
                  @click="onHandleControl().search"
                >
                  查询
                </a-button>
                <a-button
                  style="margin-left: 10px; border-radius: 4px"
                  @click="onHandleControl().reset"
                >
                  重置
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="tabs-mian">
        <a-tabs @tab-click="hanleClickTabs">
          <a-tab-pane
            v-for="ite in optionsMap.statusOptions"
            :key="ite.value"
            :tab="ite.label"
          />
        </a-tabs>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table"
          :columns="columns"
          :data-source="tableList"
          :loading="isLoading"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.page_size,
            defaultPageSize:30,
            pageSizeOptions:['30'],
            total: pagination.count,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total: number) => `共 ${total} 条数据 第${pagination.current}页`
          }"
          row-key="bill_no"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :scroll="{ x: 1725 }"
          @change="onChangePageSize"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'Opponent'">
              <span v-if="record.payType == 1">{{ record.tradeNo }}</span>
              <span v-if="record.payType == 3">{{ record.paymentAccount }}
                <DecryptField
                  v-if="viewPlaintextAuth.isPhonePermission && record.payType == 3"
                  :field-item="{
                    box: 'text',
                    id: record.order_id,
                    field: 'paymentAccount',
                    field_type: 'phone',
                    type: 138,
                  }"
                />
              </span>
            </template>
            <template v-if="column.dataIndex === 'statusName'">
              <div :class="['status-block', { primary: record.status === '1' }]">
                {{ record.statusName }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'paymentName'">
              <div>
                {{ record.paymentName }}
                <DecryptField
                  v-if="viewPlaintextAuth.isNamePermission"
                  :field-item="{
                    box: 'text',
                    id: record.order_id,
                    field: 'paymentName',
                    field_type: 'name',
                    type: 138,
                  }"
                />
              </div>
            </template>
            <template v-if="column.dataIndex === 'crmRemark'">
              <div class="crmRemark">
                <crm-remark
                  :id="record.bill_no + unionSuffix"
                  id-key="unionId"
                  :item="remarkListMap[record.bill_no + unionSuffix]"
                  size="default"
                  @add-success="onLoadList"
                />
              </div>
            </template>
            <template v-if="column.name === 'action'">
              <div style="display: flex">
                <a-button
                  v-if="record.showPay === 1 && !['1'].includes(record.status)"
                  style="color: #3777ff"
                  type="link"
                  @click="onHandleControl(record.bill_no).settleAccount(record.bill_money)"
                >
                  结算
                </a-button>
                <a-button
                  v-if="record.showPay !== 1 && ['0'].includes(record.status)"
                  style="color: #3777ff"
                  type="link"
                  @click="onHandleControl(record.bill_no).changeStatus"
                >
                  结算登记
                </a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <pricing-modal
      v-if="pricingModalVisible"
      :id="currentId"
      v-model:visible="pricingModalVisible"
      @on-load-list="handlonLoadList"
    />
  </layout-admin-page>
</template>
<script lang="ts" setup>
import { createVNode, h,reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { FormInstance, message,Modal } from 'ant-design-vue';
import { DeliveredProcedureOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import dayJS from 'dayjs';

import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
import { useViewPlaintextAuth } from '@/composables/use-decrypt-data';
import useExportModalConfirm from '@/pages-stage/recycle/finance/self-order-pay/use-export-modal-confirm';

import useBoolean from '../../common/use-boolean';
import useTableList from '../../common/use-table-list';
import PricingModal from './components/pricing-modal.vue';
import { IListItem } from './data';
import {
  exportList,
  getCrmListApi,
  getFormFilter,
  getTableList,
  getWarehouseDataCount,
  getZfbDataCount,
  serverBillPay,
} from './service';
const { viewPlaintextAuth } = useViewPlaintextAuth();
import DataStatisticsBoard from '../self-order-pay/components/data-statistics-board.vue';
import useDataStatistics from '../self-order-pay/composables/use-data-statistics';

type Key = string | number;

const columns: any[] = [
  {
    title: '账单id',
    dataIndex: 'bill_no',
    width: 116,
  },
  {
    title: '账单类型',
    dataIndex: 'billType',
    width: 120,
  },
  {
    title: '订单号',
    dataIndex: 'order_id',
    width: 180,
  },
  {
    title: '设备类型',
    dataIndex: 'reportResultText',
    width: 88,
  },
  {
    title: '设备imei码',
    dataIndex: 'imei',
    width: 180,
  },
  {
    title: '收款商家ID',
    dataIndex: 'server_id',
    width: 103,
  },
  {
    title: '店铺名称',
    dataIndex: 'company',
    width: 160,
  },
  {
    title: '收款对象姓名',
    dataIndex: 'paymentName',
    width: 116,
  },

  {
    title: '收款通道',
    dataIndex: 'paymentType',
    width: 100,
  },
  {
    title: '交易对手',
    dataIndex: 'Opponent',
    width: 138,
  },
  {
    title: '账单总金额',
    dataIndex: 'bill_money',
    width: 112,
  },
  {
    title: '第三方流水号',
    dataIndex: 'tradeNo',
    width: 200,
  },
  {
    title: '账单状态',
    dataIndex: 'statusName',
    width: 100,
  },
  {
    title: '结算时间',
    dataIndex: 'pay_at',
    width: 120,
  },
  {
    title: '账单生成时间',
    dataIndex: 'created_at',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'crmRemark',
    width: 320,
  },
  {
    title: '操作',
    name: 'action',
    fixed: 'right',
    width: 110,
  },
];

const route = useRoute();

const [pricingModalVisible, { setTrue: showPricingModal }] = useBoolean();

const currentId = ref<any>([]);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  order_id: route.query?.orderId || '',
  trade_no: null,
  status: null,
  status_list: [],
  report_result: null,
  company: null,
  time: [],
  close_time: [],
});

const optionsMap = reactive({
  statusOptions: [],
  oualityOptions: [
    { value: '', label: '全部' },
    { value: 1, label: '检测合格' },
    { value: 2, label: '检测不合格' },
  ],
});

const remarkListMap = ref({});
const selectedRowKeys = ref<Key[]>([]);
const selectedRow = ref<any[]>([]);
const unionSuffix = '_recySerBill';

// 分页器
let pagination = reactive({
  page: 1,
  page_size: 30,
  count: 0,
});

// 趣回收账
const { info: dataStatisticsInfo_qhs, loading: loading_qhs, init: requestDataStatistics_qhs } = useDataStatistics({
  request: getZfbDataCount,
  title: '企业回收结算(趣回收)',
  tips: [
    '统计范围为企业支付宝在线转账的全部账单',
    '应付金额：所筛选数据表中，全部待结算账单定价金额+结算失败账单定价金额的总和',
    '已付金额：所筛选数据表中，全部已结算账单定价金额的总和',
  ],
});

// 供应链账
const { info: dataStatisticsInfo_gyl, loading: loading_gyl, init: requestDataStatistics_gyl } = useDataStatistics({
  request: getWarehouseDataCount,
  title: '企业回收结算（供应链线下结算登记）',
  tips: [
    '统计范围为线下结算登记的全部账单',
    '应付金额：所筛选数据表中，全部待结算账单定价金额+结算失败账单定价金额的总和',
    '已付金额：所筛选数据表中，全部已结算账单定价金额的总和',
  ],
});

const getUrlParams = () => {
  const query = route.query || {};
  const keysArr = Object.keys(query).filter(key => !['role', 'origin'].includes(key));
  if (keysArr.length) {
    keysArr.forEach(key => {
      const value = query[key];
      switch (key) {
        case 'startTime':
          formState.value.time[0] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'endTime':
          formState.value.time[1] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'statusList':
          formState.value.status_list = value.split(',');
          break;
        default:
          if (formState.value.hasOwnProperty(key)) {
            formState[key] = value;
          }
          break;
      }
    });
  }
};

const { onLoadList, tableList, isLoading } = useTableList({
  getTableList,
  payload: formState.value,
  pagination,
  isAutoInit: true,
  beforeInit: getUrlParams,
  formatPayLoad: () => {
    const params: any = { ...formState.value };
    params.created_at_gt = (params.time && params.time[0]) || '';
    params.created_at_lt = (params.time && params.time[1]) || '';
    params.pay_at_gt = (params.close_time && params.close_time[0]) || '';
    params.pay_at_lt = (params.close_time && params.close_time[1]) || '';
    delete params.time;
    delete params.close_time;
    const obj = {
      ...params,
      ...pagination,
    };
    requestDataStatistics_qhs(obj);
    requestDataStatistics_gyl(obj);
    return obj;
  },
  cb: (data: any) => {
    const { list } = data;
    const ids = list.map((item: IListItem) => item.bill_no);
    if (!ids || !ids.length) return;
    getRemarkAll(ids);
  },
});

const getRemarkAll = async (ids: string[]) => {
  const params = {
    unionIds: ids,
    unionSuffix,
  };
  const res = await getCrmListApi(params);
  const data = res.data || [];
  data.forEach((item: { union_id: string }) => {
    const { union_id } = item;
    remarkListMap.value[union_id] = item;
  });
};

const onChangePageSize = (pageInfo: { current: number; pageSize: number }) => {
  pagination.page = pageInfo.current;
  // 再次发起网络请求
  onLoadList();
};

const onHandleControl = (id?: string) => ({
  search() {
    pagination.page = 1;
    onLoadList();
  },
  reset() {
    // 1.重置所有字段
    initFormState();
    // 2. 初始化页码
    pagination.page = 1;
    pagination.page_size = 30;
    // 3. 重新获取table数据
    onLoadList();
  },
  changeStatus() {
    currentId.value = [id];
    Modal.confirm({
      title: '是否切换至已结算?',
      content: createVNode(
        'span',
        {
          style: 'color: rgba(0,0,0,0.65);',
        },
        '切换状态后将不能恢复，确认切换结算状态吗？',
      ),
      onOk() {
        showPricingModal();
      },
    });
  },
  settleAccount(money: string) {
    Modal.confirm({
      title: '确认付款?',
      icon: createVNode(ExclamationCircleOutlined),
      content: `付款后将无法取消，是否对该账单下的${money}元进行付款？`,
      onOk() {
        const params = {
          billNos: id,
          payType: 3,
        };
        serverBillPay(params).then(res => {
          if (res.data.status && res.data.status === 20006) {
            message.error(res.message);
          } else {
            message.success(res.message);
          }
          onHandleControl().reset();
        });
      },
    });
  },
});

const initFormState = () => {
  formRef.value?.resetFields();
};

const getOptions = async () => {
  const res = await getFormFilter();
  const data = res.data || {};
  const statusList = (data.status || []).map(item => {
    item.value = String(item.value);
    return item;
  });
  optionsMap.statusOptions = statusList;
};
const { down } = useExportModalConfirm(async () => {
  const res = await exportList({
    order_id: formState.value.order_id,
    trade_no: formState.value.trade_no,
    status: formState.value.status,
    report_result: formState.value.report_result,
    company: formState.value.company,
    created_at_gt: formState.value.time && formState.value.time[0],
    created_at_lt: formState.value.time && formState.value.time[1],
    pay_at_gt: formState.value.close_time && formState.value.close_time[0],
    pay_at_lt: formState.value.close_time && formState.value.close_time[1],
    status_list: formState.value.status_list,
  });
  return res;
}, `${route.query.origin}/super/async-export/index`);
const hanleClickTabs = (val: any) => {
  formState.value.status = val;
  onLoadList();
  selectedRowKeys.value = [];
  selectedRow.value = [];
};
const onSelectChange = (selectedRowKey: any, selectedRows: any) => {
  selectedRow.value = selectedRows;
  selectedRowKeys.value = selectedRowKey;
};
const bulkRegistration = () => {
  const statusRow = selectedRow.value.filter((item: any) => item.status === '1');
  if (statusRow.length) {
    Modal.error({
      title: '登记提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: h('div', [
        h('span', '请检查所选账单状态，批量结算登记的账单需要为'),
        h('span', { style: 'color:red' }, '待结算'),
        h('span', '状态!'),
      ]),
      okText: '确认',
    });
  } else {
    currentId.value = selectedRowKeys.value;
    showPricingModal();
  }
};
const handlonLoadList = () => {
  onLoadList();
  selectedRowKeys.value = [];
  selectedRow.value = [];
};

getOptions();
</script>
<style lang="less" scoped>
@import '../../common/base.less';
.list-table {
  padding-top: 14px;
}
.status-block {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 16px;
  &::after {
    position: absolute;
    top: 7px;
    left: 0;
    width: 8px;
    height: 8px;
    background: #faad14;
    border-radius: 50%;
    content: '';
  }
  &.primary {
    &::after {
      background: #00c8be;
    }
  }
}
.crmRemark {
  :deep(.ant-btn) {
    padding-left: 0 !important;
  }
  // 文字颜色调整为蓝色
  :deep(.ant-btn-link) {
    color: #3777ff !important;
  }
}
.tabs-mian {
  padding: 0 24px;
}
.custom-box {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin: 0 24px 24px 24px;
}
</style>
