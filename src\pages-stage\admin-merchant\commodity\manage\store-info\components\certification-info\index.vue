<template>
  <div class="certification-info">
    <template v-if="code">
      <div class="title-box">
        <div class="line" />
        <div class="title">
          店铺二维码
        </div>
      </div>
      <ShopCode
        :code="code"
        style="margin-bottom: 32px"
      />
    </template>
    <div class="title-box">
      <div class="line" />
      <div class="title">
        认证资料
      </div>
      <a-button
        style="margin-left: 16px"
        type="primary"
        @click="modifyHandle(merchInfo.server_id)"
      >
        修改资料
      </a-button>
      <a-button
        style="margin-left: 16px"
        type="default"
        @click="toModifyList(merchInfo.server_id)"
      >
        <template #icon>
          <ContainerOutlined />
        </template>
        修改记录
      </a-button>
    </div>
    <Info :merch-info="merchInfo" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ContainerOutlined } from '@ant-design/icons-vue';

import { queryWhetherWaitToAuditData } from '@/pages-stage/server/manage/info-modify-record/service';

import Info from './components/info.vue';
import ShopCode from './components/shop-code.vue';
import { getCodeInfo, getViewDetail, getViewModifyDetail } from './service';
const route = useRoute();
const router = useRouter();

const merchInfo = ref({});
const id = ref('');
const server_id = ref('');

const code = ref<string>('');
const fetchData = async () => {
  const res = await getCodeInfo();
  code.value = res.data.imgUrl;
};

const emits = defineEmits(['update:value']);

onMounted(() => {
  initData();
  fetchData();
});

function initData() {
  const { id: newId, server_id: serverId } = route.query;
  server_id.value = serverId;
  emits('update:value', serverId);
  if (newId) {
    id.value = newId;
    getModifyInfo();
  } else {
    getBaseInfo();
  }
}

async function getBaseInfo() {
  const { data } = await getViewDetail({ server_id: server_id.value });
  setMerchInfo(data);
}
async function getModifyInfo() {
  const { data } = await getViewModifyDetail({ id: id.value });
  setMerchInfo(data.after_edit_data);
}

function setMerchInfo(data: any = {}) {
  const {
    server_id,
    registered_name: registerPerson,
    registered_phone: registerPhone,
    company: merchName,
    category_list: category,
    city,
    contact_user: contactUser,
    tellphone: contactPhone,
    fax: email,
    invoice,
    company_name,
    company,
    type: companyType,
    behalf_user: represent,
    license_image: licenseImage,
    license_no: licenseNo,
    behalf_user: behalfUser,
    legal_person_idcard: idcardFace,
    legal_person_other_idcard: idcardCountry,
    legal_person_phone,
    legal_idcard,
    real_address: realAddress,
    company_images,
    video,
    submit_at: createdAt,
    updated_at: updatedAt,
    status,
    license_copy_image,
    license_image_proof,
    provinces: companyAddress,
    settle_target,
    mcc_text,
    industry_qualification_img,
    industry_qualification_text,
    is_review,
    is_legal,
    specific_business_address,
    daily_operate_hour,
    contact,
    contact_phone,
    lease_contract,
    locate_address,
  } = data;

  let companyImages: any[] = [];
  if (company_images && typeof company_images === 'string') {
    companyImages = JSON.parse(company_images);
  } else {
    companyImages = company_images;
  }

  merchInfo.value = {
    server_id,
    registerPerson,
    registerPhone,
    merchName,
    category,
    city,
    contactUser,
    contactPhone,
    email,
    invoice,
    company,
    companyType,
    represent,
    licenseImage,
    licenseNo,
    behalfUser,
    idcardFace,
    idcardCountry,
    legal_person_phone,
    legal_idcard,
    realAddress,
    companyImages,
    video,
    createdAt,
    updatedAt,
    status,
    license_copy_image,
    license_image_proof,
    companyAddress,
    settle_target,
    mcc_text,
    industry_qualification_img,
    industry_qualification_text,
    company_address: companyAddress,
    license_no: licenseNo,
    real_address: realAddress,
    provinces: companyAddress,
    company_name,
    is_review,
    is_legal,
    specific_business_address,
    daily_operate_hour,
    contact,
    contact_phone,
    lease_contract,
    locate_address,
  };

  emits('update:value', server_id);
}

const { origin } = route.query;

const toModifyList = (id: number | string) => {
  // 开发环境调试用
  if (process.env.NODE_ENV === 'development') {
    router.push({
      path: '/merchant/info-modify-record',
      query: {
        server_id: id,
        role: '',
      },
    });
    return;
  }
  const url = `${origin}/site/modify-list?server_id=${id}`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};

// 修改功能
async function modifyHandle(id: string) {
  await queryWhetherWaitToAuditData();
  router.push({
    path: '/merchant/info-modify',
    query: {
      server_id: id,
      ...route.query,
    },
  });
}
</script>

<style lang="less" scoped>
.certification-info {
  margin-top: 8px;
}
.title-box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .line {
    width: 4px;
    height: 14px;
    margin-right: 8px;
    background: #3777ff;
    border-radius: 2px 2px 2px 2px;
  }
  .title {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
  }
}
</style>
