<template>
  <a-modal
    v-model:visible="bindVisible"
    :destroy-on-close="true"
    title="物流变更"
    width="510px"
    @cancel="onCancel"
  >
    <div class="hint-card">
      <div class="hint-card-header">
        处理时效说明
      </div>
      <div class="hint-card-content">
        <div>前一天18点后申请拦截的物流单号，处理时效是次日10点30分处理；</div>
        <div>当天10点30分 —— 下午2点30分前申请拦截的物流单号，处理时效当天下午2点30分处理；</div>
        <div>当天2点30分 —— 下午18点前申请拦截的物流单号，处理时效当天处理。</div>
      </div>
    </div>

    <a-alert
      v-if="submitBtnDisabled"
      banner
      message="请您在提交申请之前，先对地址等相关信息进行必要的修改。"
      style="margin-bottom: 24px"
    />

    <a-form
      ref="formRef"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item
        label="变更类型"
        name="change_type"
        :rules="[{ required: true }]"
      >
        <a-radio-group v-model:value="formState.change_type">
          <a-radio-button :value="1">
            物流拦截
          </a-radio-button>
          <a-radio-button :value="2">
            地址变更
          </a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item
        v-if="formState.change_type === 1"
        label="拦截原因"
        name="intercept_reason"
        :rules="[{ required: true }]"
      >
        <a-select
          v-model:value="formState.intercept_reason"
          :options="reasonOptions"
          placeholder="请选择"
        />
      </a-form-item>

      <template v-else>
        <a-form-item
          label="新收货人"
          name="name"
          :rules="[{ required: true }]"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入"
          />
        </a-form-item>

        <a-form-item
          label="新手机号"
          name="phone"
          :rules="[{ required: true }]"
        >
          <a-input
            v-model:value="formState.phone"
            placeholder="请输入"
          />
        </a-form-item>

        <a-form-item
          label="新地址"
          name="address"
          :rules="[{ required: true }]"
        >
          <a-input
            v-model:value="formState.address"
            placeholder="请输入"
          />
        </a-form-item>
      </template>

      <a-form-item
        label="备注"
        :label-col="{ span: 4 }"
        name="apply_remark"
      >
        <a-textarea
          v-model:value="formState.apply_remark"
          :maxlength="200"
          show-count
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button
        style="margin-right: 8px"
        @click="onCancel"
      >
        取消
      </a-button>

      <a-popover
        v-model:visible="popoverVisible"
        overlay-class-name="my-popover"
        :overlay-style="{ width: '290px' }"
        trigger="click"
      >
        <template #content>
          <div style="line-height: 22px">
            <ExclamationCircleOutlined style="color: #faad14" />
            {{ popoverText }}
          </div>
          <div style="display: flex; justify-content: end; padding-top: 8px">
            <a-button
              size="small"
              @click="popoverVisible = false"
            >
              取消
            </a-button>
            <a-button
              :loading="submitLoading"
              size="small"
              style="margin-left: 6px"
              type="primary"
              @click="onSubmit"
            >
              确认
            </a-button>
          </div>
        </template>
        <a-button
          :disabled="submitBtnDisabled"
          :loading="submitLoading"
          type="primary"
        >
          提交
        </a-button>
      </a-popover>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { FormInstance, message } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import { sendIntercept } from '@/components/order-business/operate-modals/order/delivery-intercept-modal/services';
import type { TRefreshDataKey } from '@/components/order-business/typing/async-order-data.d';
import { useVModel } from '@/hook';

import { getOriginBaseInfo } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);

const reasonOptions = [
  {
    label: '客户要求退货',
    value: 1,
  },
  {
    label: '预收租金未收完',
    value: 2,
  },
  {
    label: '商家二次审核不通过',
    value: 3,
  },
  {
    label: '其他',
    value: 4,
  },
];
const formRef = ref<FormInstance>();
const formState = ref({
  change_type: 1,
  intercept_reason: null,
  apply_remark: '',
  name: '',
  phone: '',
  address: '',
});
/** 初始化数据 */
const initBaseInfo = ref({
  name: '',
  phone: '',
  address: '',
});
const popoverVisible = ref(false);
const submitLoading = ref(false);

/** 禁止提交（判断当前表单内容是否修改） */
const submitBtnDisabled = computed(() => {
  const { change_type, name, phone, address } = formState.value;
  if (change_type === 2) {
    return (
      initBaseInfo.value.name === name && initBaseInfo.value.phone === phone && initBaseInfo.value.address === address
    );
  }
  return false;
});

const popoverText = computed(() => {
  const { change_type } = formState.value;
  if (change_type === 1) {
    return '发起物流拦截并非百分百成功，提交后平台将尽力协助拦截。';
  } else {
    return submitBtnDisabled.value ? '检测到地址等相关信息未发生变化，请编辑后再提交' : '确定提交地址变更申请吗？';
  }
});

const onCancel = () => {
  bindVisible.value = false;
  popoverVisible.value = false;
};

const onSubmit = () => {
  if (submitLoading.value) return;

  if (formState.value.change_type === 2 && submitBtnDisabled.value) {
    popoverVisible.value = false;
    return;
  }
  formRef.value?.validateFields().then(() => {
    const { change_type, intercept_reason, apply_remark, name, phone, address } = formState.value;

    const params = {
      order_id: Number(props.orderId),
      intercept_reason: change_type === 1 ? intercept_reason : 0,
      name: change_type === 2 ? name : '',
      phone: change_type === 2 ? phone : '',
      address: change_type === 2 ? address : '',
      change_type,
      apply_remark,
      not_grayscale_server: 1, // 标记给后端是旧版本的物流拦截
    };

    submitLoading.value = true;

    sendIntercept(params)
      .then(() => {
        message.success('提交成功');
        emit('refresh', props.orderId, ['data']);
        onCancel();
      })
      .finally(() => {
        submitLoading.value = false;
      });
  });
};

watch(
  () => props.visible,
  () => {
    if (!props.visible) {
      // 重置表单状态
      formRef.value?.resetFields();
    } else {
      getOriginBaseInfo(props.orderId).then(({ data }) => {
        const { name, phone, address } = data;
        formState.value.name = name;
        formState.value.phone = phone;
        formState.value.address = address;
        initBaseInfo.value = { name, phone, address };
      });
    }
  },
);
</script>

<style lang="less" scoped>
.hint-card {
  margin-bottom: 24px;
  padding: 12px;
  color: rgba(6, 21, 51, 0.65);
  background-color: #f0f7ff;
  border-radius: 4px;

  .hint-card-header {
    padding-bottom: 8px;
    color: rgba(6, 21, 51, 0.75);
    font-weight: 500;
    font-size: 16px;
    text-align: center;
  }

  .hint-card-content {
    line-height: 24px;
  }
}
</style>
