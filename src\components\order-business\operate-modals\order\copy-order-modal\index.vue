<template>
  <a-modal
    v-model:visible="bindVisible"
    class="copy-order-modal"
    title="复制订单"
  >
    <a-textarea
      id="order-copy-id"
      ref="copyRef"
      v-model:value="copyText"
      :auto-size="{ minRows: 4 }"
    />

    <template #footer>
      <a-button
        class="green-btn"
        type="primary"
        @click="onCopy"
      >
        复制到剪贴板
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { PropType, ref, watch } from 'vue';

import { useVModel } from '@/hook';
import { queryServerSensitiveData } from '@/services/api';
import { copy } from '@/utils/base';
import { getTopWindowUrl } from '@/utils/url';

interface IGoodsInfo {
  spu_name?: string;
  sku_name?: string;
  num?: number;
  start_time?: string;
  end_time?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  /** 商品信息 */
  goodsInfo: {
    type: Object as PropType<IGoodsInfo>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);

const copyText = ref('');

//复制订单 埋点触发 累加调用次数
const onHandleCopyOrder = () => {
  copyText.value = '';
  getTopWindowUrl().then(({ url }) => {
    queryServerSensitiveData({
      id: props.orderId,
      url: url,
      type: 1031,
      field_type: 27,
      field: 'order_id',
    }).then(res => {
      if (res.data?.data) {
        const address = res.data.data.map(item => item?.replace('复制订单-', '')).join('，');
        const info = `${props.goodsInfo.spu_name}（${props.goodsInfo.sku_name}），租${props.goodsInfo.num}个，租期 ${props.goodsInfo.start_time}至${props.goodsInfo.end_time}`;
        copyText.value = `${address}\n${info}`;
      }
    });
  });
};

function onCopy() {
  copy(copyText.value);
}

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      onHandleCopyOrder();
    }
  },
  { immediate: true },
);
</script>
