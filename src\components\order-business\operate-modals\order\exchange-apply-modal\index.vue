<template>
  <a-modal
    v-model:visible="bindVisible"
    destroy-on-close
    title="提交换货申请单"
    width="660px"
  >
    <a-spin
      v-if="!isSuccess"
      :spinning="loading"
    >
      <!-- 提示信息 -->
      <GoodsTip
        :goods-name="initData?.sku_info"
        :is-grayscale="extraData.is_grayscale"
      />
      <!-- 编辑表单 -->
      <template v-if="initData">
        <!--  灰度版本，现已全量 -->
        <GrayscaleEditForm
          ref="grayscaleEditFormRef"
          v-model:form-data="formData"
          v-model:invoice="invoice"
          :init-data="initData"
          @affirm-barter="onSubmitShipment"
          @close-modal="closeModal"
          @upgrade-barter="onSubmitShipment"
        />
      </template>
    </a-spin>

    <template v-else>
      <div class="success-content">
        <img
          alt="申请成功"
          src="https://img1.rrzuji.cn/uploads/scheme/2306/01/m/o3L5iuOgOHkdvOL6A1Zt.png"
        >
        <div class="success-content-title">
          {{ extraData.is_grayscale ? '下发成功' : '申请成功' }}
        </div>
        <a-button
          style="margin-top: 24px"
          @click="handleCheck"
        >
          查看申请
        </a-button>
      </div>
    </template>
    <template #footer>
      <div
        v-if="!isSuccess"
        class="flex-wrap flex-y-center"
      >
        <div class="flex-con flex-wrap flex-x-start">
          <div class="flex-wrap flex-y-center">
            <a-checkbox v-model:checked="agreementChecked">
              勾选即同意并遵守
            </a-checkbox>
            <div class="flex-wrap flex-vertical">
              <a
                class="text-link"
                href="http://img1.rrzuji.cn/template_doc/%E4%BE%9B%E5%BA%94%E9%93%BE%E9%87%87%E8%B4%AD%E6%A1%86%E6%9E%B6%E5%8D%8F%E8%AE%AE-240516.pdf"
                target="_blank"
              >《平台代发服务协议》</a>
            </div>
          </div>
        </div>
        <div class="flex-wrap flex-gap-8">
          <a-button @click="closeModal">
            取消
          </a-button>
          <a-button
            :disabled="barterBtnDisabled"
            :loading="loading"
            type="primary"
            @click="() => onSubmitShipment({ c_type: 1 })"
          >
            换货下发
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed,type PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useCompRef } from 'rrz-web-design';

import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

import GoodsTip from './components/goods-tip.vue';
import GrayscaleEditForm from './components/grayscale-edit-form.vue'
import { EStockStatus } from './composables/use-check-stock'
import { IFormData, IInitData } from './data';
import {
  getSendWarehouseInfo,
  submitGoodsBarter,
  superGetSendWarehouseInfo,
  superSubmitGoodsBarter,
} from './services';

interface IExtraData {
  server_id?: number | string;
  is_grayscale?: boolean;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
  /** 打开换货记录弹窗 */
  (e: 'showChangeGoodsModal'): void;
}>();

const defaultFormData: IFormData = {
  type: 1,
  cause: '',
  activate_only: false,
  isChecked: false,
  regulation: false,
};

/** 加载状态 */
const loading = ref(false);
const bindVisible = useVModel(props, 'visible', emit);
const formData = ref<IFormData>({ ...defaultFormData });
const invoice = ref(2);
const initData = ref<IInitData>();
const isSuccess = ref(false);
// 是否勾选代发协议
const agreementChecked = ref(false);
const isSuper = useRoute().query.role === 'super';

const barterBtnDisabled = computed(() => {
  return !(agreementChecked.value && formData.value.stockStatus === EStockStatus.Enough && grayscaleEditFormRef.value?.isQueryStock)||Number(initData.value?.one_device_code)
})

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      getIntiData();
      isSuccess.value = false;
      formData.value = { ...defaultFormData };
    }
  },
  { immediate: true },
);

function getIntiData() {
  loading.value = true;
  const api = isSuper ? superGetSendWarehouseInfo : getSendWarehouseInfo;
  initData.value = undefined;
  api(props.orderId)
    .then(res => {
      initData.value = { ...res.data, serverId: props.extraData.server_id, orderId: props.orderId };
    })
    .catch(() => {
      bindVisible.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

function closeModal() {
  bindVisible.value = false;
}

function handleCheck() {
  emit('showChangeGoodsModal');
  closeModal();
}

const grayscaleEditFormRef = useCompRef(GrayscaleEditForm);

function onSubmitShipment (payload: {c_type: 1 | 2 | 21, new_sku_id?: string, type?: 'exchange' | 'upgrade' }) {
  grayscaleEditFormRef.value?.validate().then(() => {
    submitApply(payload)
  })
}

function submitApply(payload = {}) {
  const { type, ...otherPayload } = payload;
  const params = { ...handleparams(), ...otherPayload }
  if (type && type === 'upgrade') {
    params.storage_memory_up = 1;
  }

  loading.value = true;
  const api = isSuper ? superSubmitGoodsBarter : submitGoodsBarter;
  api(params)
    .then(() => {
      isSuccess.value = true;
      emit('refresh', props.orderId, ['data', 'all_remark']);
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleparams () {
  const { skuId, type, cause, regulation, activate_only, stockStatus, isChecked } = formData.value
  const { is_new_online_server } = initData.value
  const params = {
    order_id: props.orderId,
    old_sku_id: initData.value?.sku_id,
    new_sku_id: skuId,
    reason: ['用户原因', '商家原因', '平台原因'][type] || '未知',
    remark: cause,
    is_support: regulation ? 1 : 0,
    activate_only: activate_only ? 1 : 0,
    stock_status: stockStatus,
    is_checked: isChecked ? 1 : 0,
    is_grayscale: props.extraData?.is_grayscale, // 标记是灰度版本的换货
  }
  if(is_new_online_server) {
    params.invoice = invoice.value;
  }
  return  params;
}
</script>

<style lang="less" scoped>
.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px 0;
  color: rgba(6, 21, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.success-content-title {
  padding: 8px 0;
  color: rgb(6, 21, 51);
  font-weight: 500;
  font-size: 20px;
}
</style>
