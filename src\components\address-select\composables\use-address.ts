import { ref } from 'vue';
import { useBoolean } from 'rrz-web-design';

import { fetchCityData } from '@/components/address-select/services';
import { createGlobalState } from '@/hook';

import type { IAddressItem, TAddress } from '../data';

export const useAddress = createGlobalState(() => {
  let addressDataLoader: Promise<Record<string, IAddressItem[]>> | null = null;
  /** 地址选项加载中 */
  const [loading, setLoading] = useBoolean();
  /** 地址选项对象 */
  const addressData = ref<Record<string, IAddressItem[]>>({});
  /** 省选项 */
  const provinceOptions = ref([] as IAddressItem[]);

  /** 获取/刷新 地址选项 */
  function getAddressData(force = false) {
    if (force || !addressDataLoader) {
      addressDataLoader = new Promise(resolve => {
        setLoading(true);
        fetchCityData()
          .then((data: any) => {
            addressData.value = data;
            provinceOptions.value = data['86'];
            resolve(data);
          })
          .finally(() => setLoading(false));
      });
    }
    return addressDataLoader;
  }

  /** 根据上一级的值获取下一级选项 */
  function getOptionsByKey(key?: string) {
    return key ? addressData.value[key] : [];
  }

  /** 根据省市区的中文获取value数组 */
  async function getValuesByNames([province, city, area]: TAddress) {
    let matchProvince = '';
    let matchCity = '';
    let matchArea = '';
    await getAddressData();
    for (const _province of provinceOptions.value) {
      if (_province.value === province) {
        matchProvince = _province.key;
        break;
      }
    }
    for (const _city of getOptionsByKey(matchProvince)) {
      if (_city.value === city) {
        matchCity = _city.key;
        break;
      }
    }
    for (const _area of getOptionsByKey(matchCity)) {
      if (_area.value === area) {
        matchArea = _area.key;
        break;
      }
    }
    return [matchProvince, matchCity, matchArea];
  }

  async function getNameByKey(key: string = '') {
    await getAddressData();
    /** 省 */
    if (key.match(/^.*0000$/)) {
      return provinceOptions.value.find((p: any) => p.key === key)?.value;
    }
    /** 市 */
    if (key.match(/^.*00$/)) {
      const provinceKey = getProvinceKeyByCityKey(key);
      return getOptionsByKey(provinceKey).find((p: any) => p.key === key)?.value;
    }
    /** 区县 */
    const cityKey = getCityKeyByAreaKey(key);
    return getOptionsByKey(cityKey)?.find((p: any) => p.key === key)?.value;
  }

  function getProvinceKeyByCityKey(key: string) {
    return key.slice(0, 2).concat('0000');
  }

  function getCityKeyByAreaKey(key: string) {
    return key.slice(0, 4).concat('00');
  }

  getAddressData();

  return {
    loading,
    addressData,
    provinceOptions,
    getAddressData,
    getOptionsByKey,
    getValuesByNames,
    getNameByKey,
  };
});

export default useAddress;
