<template>
  <div class="version-block">
    <HeaderBlock title="估价版本">
      <div class="handle-slot">
        <div class="flex-wrap flex-con flex-y-center">
          <a-select
            v-model:value="versionValue"
            :field-names="{
              label: 'name',
              value: 'data_version_id',
            }"
            :options="versionOptions"
            placeholder="请选择"
            style="width: 114px; margin-right: 8px"
            @change="onChange"
          />
          <span class="detail-text">创建者：{{ currentInfo.created_by }}</span>
          <span class="detail-text create-time">创建时间：{{ currentInfo.created_at }}</span>
        </div>
        <a-button
          v-if="isOperationVersion && detailIsLoad"
          type="primary"
          @click="emits('onChangeEdit')"
        >
          编辑
        </a-button>
      </div>
    </HeaderBlock>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, PropType } from 'vue';
import { IVersionOption } from '../../data.d';
import HeaderBlock from './header-block.vue';

const props = defineProps({
  versionOptions: {
    type: Array as PropType<IVersionOption[]>,
    default: () => [],
  },
  // 详情数据是否已加载完毕
  detailIsLoad: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['onChangeEdit', 'onVersionChange']);

const versionValue = ref<string>('');

// 当前选中的版本
const currentInfo = computed(() => {
  const item = props.versionOptions.find(item => item.data_version_id === versionValue.value);
  return item || { created_by: '', created_at: '' };
});

// 是否为当前最新版本
const isOperationVersion = computed(() => {
  const versionOptions = props.versionOptions || [];
  const item = versionOptions[0] || {};
  return !versionValue.value || item.data_version_id === versionValue.value;
});

function init() {
  versionValue.value = props.versionOptions[0].data_version_id || '';
  onChange();
}

function onChange() {
  emits('onVersionChange', versionValue.value, isOperationVersion.value);
}

onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.version-block {
  padding: 20px 24px;
  border-bottom: 1px solid #e9e9e9;
  .handle-slot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .detail-text {
      color: rgba(6, 21, 51, 0.25);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
    }
    .create-time {
      display: inline-block;
      margin-left: 8px;
    }
  }
}
</style>
