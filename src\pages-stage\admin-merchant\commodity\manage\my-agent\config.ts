import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'item_id',
    fragmentKey: 'renderInput',
    originProps: { label: '产品ID', name: 'item_id' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'category',
    originProps: { label: '类目', name: 'category' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'scheme_title',
    originProps: { label: '产品标题', name: 'scheme_title' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'sku_name',
    originProps: { label: '套餐名称', name: 'sku_name' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'server_id',
    originProps: { label: '店铺ID', name: 'server_id' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'rental',
    originProps: { label: '租金', name: 'rental' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'deposit',
    originProps: { label: '押金', name: 'deposit' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'agent_ratio',
    originProps: { label: '百分比代理佣金分成', name: 'agent_ratio' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'created_at',
    fragmentKey: 'renderRangePicker',
    originProps: { label: '创建时间', name: 'created_at' },
    elProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
      style: { width: '100%' },
    },
  },
];

export const columns: ColumnType[] = [
  {
    title: '序号',
    width: 100,
    dataIndex: 'name',
    key: 'index',
    customRender: ({ index }: { index: number }) => `${index + 1}`,
  },
  { title: '产品ID', width: 150, dataIndex: 'item_id', key: 'item_id' },
  {
    title: '类目',
    dataIndex: 'category',
    key: 'category',
    width: 150,
    // sortDirections: ['descend', 'ascend'],
    // sorter: (a: any, b: any) => new Date(a.pay_at).getTime() - new Date(b.pay_at).getTime(),
  },
  { title: '产品标题', dataIndex: 'scheme_title', key: 'scheme_title', width: 170 },
  { title: '套餐名称', dataIndex: 'sku_name', key: 'sku_name', width: 150 },
  { title: '店铺ID', dataIndex: 'server_id', key: 'server_id', width: 150 },
  {
    title: '租金',
    dataIndex: 'rental',
    key: 'rental',
    width: 150,
    customRender: ({ record }) => record.rental + '元/天',
  },
  {
    title: '押金',
    dataIndex: 'deposit',
    key: 'deposit',
    width: 160,
    customRender: ({ record }) => record.deposit + '元',
  },
  {
    title: '百分比代理佣金分成',
    dataIndex: 'agent_ratio',
    key: 'agent_ratio',
    width: 160,
    customRender: ({ record }) => (record.agent_ratio > 0 ? record.agent_ratio * 100 + '%' : record.agent_ratio),
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150,
    fixed: 'right',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 100,
  },
];
