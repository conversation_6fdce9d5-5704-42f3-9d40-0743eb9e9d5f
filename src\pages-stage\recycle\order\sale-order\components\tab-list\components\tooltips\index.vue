<template>
  <a-tooltip :placement="placement">
    <template #title>
      <div
        v-for="(item, index) in content"
        :key="index"
      >
        {{ item }}
      </div>
    </template>
    <div
      class="content-list"
      :style="{ ...style, '-webkit-line-clamp': clamp, 'white-space': nowrap ? 'nowrap' : 'normal' }"
    >
      <div
        v-for="(item, index) in content"
        :key="index"
      >
        {{ item }}
      </div>
    </div>
  </a-tooltip>
</template>

<script setup lang="ts">
defineProps({
  content: {
    type: Array,
    default: () => [],
  },
  // 显示多少行开始省略
  clamp: {
    type: Number,
    default: 1,
  },
  nowrap: {
    type: Boolean,
    default: false,
  },
  placement: {
    type: String,
    default: 'bottom',
  },
  style: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.content-list {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
