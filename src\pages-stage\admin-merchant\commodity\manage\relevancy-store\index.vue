<template>
  <layout-shops-page
    :content-style="{ padding: '0 24px' }"
    title="关联店铺"
  >
    <!-- 页面功能按钮，标题右侧 -->
    <template #extra>
      <a-tooltip
        v-if="hasBeenAssociated && hasBeenAssociatedText"
        placement="bottomRight"
      >
        <template #title>
          <span>您的账号已被店铺关联了，需要解除关系后才能进行关联。</span>
        </template>
        <a-button
          disabled
          type="primary"
          @click="relevancyForm.visible = true"
        >
          <template #icon>
            <plus-outlined />
          </template>
          添加关联
        </a-button>
      </a-tooltip>
      <a-button
        v-else-if="hasBeenAssociated"
        disabled
        type="primary"
        @click="relevancyForm.visible = true"
      >
        <template #icon>
          <plus-outlined />
        </template>
        添加关联
      </a-button>
      <a-button
        v-else
        type="primary"
        @click="relevancyForm.visible = true"
      >
        <template #icon>
          <plus-outlined />
        </template>
        添加关联
      </a-button>
    </template>

    <a-alert
      show-icon
      type="info"
    >
      <template #icon>
        <exclamation-circle-outlined />
      </template>
      <template #message>
        <div class="alert-text-con">
          <span>您可以关联旗下管理的店铺，关联成功后，即可通过目前登录账号的微信进行扫码登录
            <question-circle-outlined
              style="margin: 0 4px; color: var(--ant-info-color); cursor: pointer"
              @click="loginDemoVisible = true"
            />，详细规则可点击查看
          </span>
          <a
            class="alert-rule"
            href="javascript:;"
            @click="gotoRulesDetails"
          >《商家账号管理规则》</a>
        </div>
      </template>
    </a-alert>
    <div
      v-if="hasPassiveServer"
      class="flex-wrap flex-y-center has-been-associated"
    >
      <info-circle-outlined />
      <span class="text">您的账号已被关联，以下是关联您的账号和微信信息。</span>
    </div>

    <ConfigProvider>
      <template #renderEmpty>
        <REmpty
          class="table-no-data"
          description="暂无关联账号"
        />
      </template>
      <a-table
        class="bottom-fix-table"
        :columns="columns"
        :data-source="list"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            <span>{{ index + 1 }}</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-button
              class="action-text"
              type="link"
              @click="onUnbindModal(record)"
            >
              解除关联
            </a-button>
          </template>
        </template>
      </a-table>
    </ConfigProvider>

    <!-- 添加关联 -->
    <a-modal
      v-model:visible="relevancyForm.visible"
      title="添加关联"
      :width="480"
      @cancel="relevancyForm.visible = false"
    >
      <div class="relevancy-content">
        <div class="relevancy-info">
          <div class="relevancy-title">
            绑定您关联的店铺账号
          </div>
          <div class="flex-wrap flex-y-center">
            <img
              alt=""
              src="https://img1.rrzuji.cn/uploads/scheme/2201/22/m/09KvRfx6SXaID4A803uK.png"
            >
            <span>绑定后，可使用您的微信扫码登录到关联的店铺账号。</span>
          </div>
        </div>
        <div class="relevancy-item">
          <a-input
            v-model:value="relevancyForm.form.phone"
            class="input-phone"
            placeholder="请输入关联店铺的手机号"
          />
          <div class="code-con flex-wrap flex-x-justify">
            <a-input
              v-model:value="relevancyForm.form.code"
              class="input-code"
              placeholder="请输入验证码"
            />
            <span
              :class="['get-code', relevancyForm.form.times === null ? 'show-pointer' : '']"
              @click="onGetCode('alone')"
            >{{ relevancyForm.form.times !== null ? `${relevancyForm.form.sec}s` : '获取验证码' }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="relevancy-footer flex-wrap flex-x-justify flex-y-center">
          <div class="left">
            绑定代表你已同意
            <a
              href="https://img-t1.rrzuji.cn/excel/files/risk_report/protocol/323979_864287.pdf"
              style="border-bottom: 1px solid var(--ant-primary-color)"
              target="_blank"
            >《人人租商家账号协议》</a>
          </div>
          <div class="right">
            <a-button @click="relevancyForm.visible = false">
              取消
            </a-button>
            <a-button
              :loading="relevancyForm.confirmLoading"
              type="primary"
              @click="onRelevancyOk"
            >
              关联店铺
            </a-button>
          </div>
        </div>
      </template>
    </a-modal>
    <!-- 解绑店铺 -->
    <a-modal
      v-model:visible="unbindForm.visible"
      :confirm-loading="unbindForm.confirmLoading"
      title="解绑店铺"
      :width="480"
      @cancel="closeUnbindModal"
      @ok="onUnbindOk"
    >
      <div class="unbind-content">
        <div class="unbind-item">
          <div class="item-title">
            该账号手机号码
          </div>
          <a-input
            v-model:value="unbindForm.form.activePhone"
            disabled
            placeholder=""
          />
          <div class="code-con flex-wrap flex-x-justify">
            <a-input
              v-model:value="unbindForm.form.activeCode"
              class="input-code"
              placeholder="请输入验证码"
            />
            <span
              :class="['get-code', unbindForm.form.activeTimes === null ? 'show-pointer' : '']"
              @click="onGetCode('active')"
            >{{ unbindForm.form.activeTimes !== null ? `${unbindForm.form.activeSec}s` : '获取验证码' }}</span>
          </div>
        </div>
        <div class="unbind-item">
          <div class="item-title">
            关联账号手机号码
          </div>
          <a-input
            v-model:value="unbindForm.form.passivePhone"
            disabled
            placeholder=""
          />
          <div class="code-con flex-wrap flex-x-justify">
            <a-input
              v-model:value="unbindForm.form.passiveCode"
              class="input-code"
              placeholder="请输入验证码"
            />
            <span
              :class="['get-code', unbindForm.form.passiveTimes === null ? 'show-pointer' : '']"
              @click="onGetCode('passive')"
            >{{ unbindForm.form.passiveTimes !== null ? `${unbindForm.form.passiveSec}s` : '获取验证码' }}</span>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 登录图例 -->
    <div
      ref="logindModalRef"
      class="login-modal"
    >
      <a-modal
        v-model:visible="loginDemoVisible"
        :footer="null"
        :get-container="() => logindModalRef"
        :title="null"
        :width="408"
      >
        <div class="login-title">
          - 登录图例 -
        </div>
        <div class="form-demo">
          <div class="title">
            选择店铺登录
          </div>
          <div class="line-item">
            店铺：xxxxx｜店铺一
          </div>
          <div class="line-item">
            店铺：xxxxx｜店铺二
          </div>
          <div class="form-footer flex-wrap">
            <span class="enter">进入店铺</span>
            <span class="go-back">返 回</span>
          </div>
        </div>
      </a-modal>
    </div>

    <!-- 微信授权弹窗 -->
    <a-modal
      v-model:visible="isBindWeChat"
      cancel-text="关闭"
      :footer="null"
      title="绑定微信账号"
      :width="500"
    >
      <div
        id="login_div"
        style="text-align: center"
      />
    </a-modal>
  </layout-shops-page>
</template>
<script setup lang="ts">
import { createVNode, nextTick, onUnmounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { ConfigProvider, message } from 'ant-design-vue';
import { Modal } from 'ant-design-vue';
import {
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons-vue';

import { columns, EConfig } from './config';
import { IRelevancyForm, IUnbindForm } from './data';
import {
  addServerRelation,
  deleteServerRelation,
  getIsBind,
  getList,
  getRelevancyCode,
  getServerRelationPhone,
  getUnbindCode,
  getWxConfig,
} from './service';

const route = useRoute();
if (route.query.error) {
  message.error(decodeURI(route.query.error as string));
}
// 判断是否禁用 添加关联 按钮
let hasBeenAssociated = ref<boolean>(false);
//判断是否显示 ’添加关联‘ 按钮禁用的提示
let hasBeenAssociatedText = ref<boolean>(true);
// 被关联商家提示语
let hasPassiveServer = ref<boolean>(false);

// 登录图例
let loginDemoVisible = ref<boolean>(false);
let logindModalRef = ref();

let unbindForm = reactive<IUnbindForm>({
  visible: false,
  confirmLoading: false,
  form: {
    activeSec: EConfig.Sec,
    activeTimes: null,
    activePhone: '',
    activeCode: '',
    activeHasClick: true,
    passiveSec: EConfig.Sec,
    passiveTimes: null,
    passivePhone: '',
    passiveCode: '',
    passiveHasClick: true,
  },
});
let relevancyForm = reactive<IRelevancyForm>({
  visible: false,
  confirmLoading: false,
  form: {
    sec: EConfig.Sec,
    times: null,
    phone: '',
    code: '',
    hasClick: true,
  },
});

// 关联微信弹窗
let isBindWeChat = ref<boolean>(false);

let list = ref<any>([]);

const getTableList = async function (): Promise<void> {
  let { status, data, message: msg } = await getList();
  if (status === 0) {
    list.value = data.list;
    return;
  }
  message.error(msg);
};
getTableList();

const showConfirm = async () => {
  let { status, data } = await getIsBind();
  if (status != 0) return;
  // 是否绑定微信  0 未绑定 1 绑定
  if (data?.is_bind_wx == EConfig.IsNotBind) {
    hasBeenAssociated.value = true;
    hasBeenAssociatedText.value = false;
    Modal.confirm({
      title: createVNode('div', { style: 'font-weight: bold;' }, '扫码绑定确认'),
      icon: createVNode(ExclamationCircleOutlined),
      content: createVNode(
        'div',
        { style: 'color: rgba(6,21,51,0.65);' },
        '由于关联店铺是需要做微信绑定，请您先完成微信扫码绑定哦',
      ),
      okText: '扫码绑定',
      cancelButtonProps: { style: { display: 'none' } } as any,
      onOk() {
        isBindWeChat.value = true;
        nextTick(async () => {
          let { status, data } = await getWxConfig();
          if (status === 0) {
            let wxObject = new WxLogin({
              id: 'login_div',
              appid: data.appid,
              scope: 'snsapi_login',
              redirect_uri: data.redirect_url,
              state: data.state,
              style: 'black',
              href: '',
            });
            if (wxObject) {
              let wxIframeEle = document.querySelector('#login_div iframe') as any;
              if (wxIframeEle) {
                // 对嵌入的微信iframe设置以下属性，可以阻止浏览器默认对重定向的拦截
                wxIframeEle.setAttribute(
                  'sandbox',
                  'allow-top-navigation allow-popups-to-escape-sandbox allow-same-origin allow-scripts',
                );
                wxIframeEle.src = `${wxIframeEle.src}&self_redirect=true`;
              }
            }
          }
        });
      },
      class: 'test',
    });
  } else {
    // 是否已被关联 0 否 1 是
    hasBeenAssociated.value = data?.is_passive_server;
  }
  // 判断是否显示被关联商家的提示语
  hasPassiveServer.value = data?.is_passive_server === 1;
};
showConfirm();

// 刷新当前页面
const onReLoadPage = function () {
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: '/server-relation/index',
    },
    '*',
  );
};
const onUnbindModal = async function (item: any): Promise<void> {
  unbindForm.form.activeTimes && clearInterval(unbindForm.form.activeTimes);
  unbindForm.form.passiveTimes && clearInterval(unbindForm.form.passiveTimes);

  unbindForm.form.activeTimes = null;
  unbindForm.form.activeSec = EConfig.Sec;
  unbindForm.form.activeHasClick = true;

  unbindForm.form.passiveTimes = null;
  unbindForm.form.passiveSec = EConfig.Sec;
  unbindForm.form.passiveHasClick = true;

  let {
    status,
    data: { active_user_phone, passive_user_phone },
    message: msg,
  } = await getServerRelationPhone({ relation_id: item.relation_id });
  if (status === 0) {
    unbindForm.form.relation_id = item.relation_id;
    unbindForm.form.activePhone = active_user_phone;
    unbindForm.form.passivePhone = passive_user_phone;
    unbindForm.visible = true;
    return;
  }
  message.error(msg);
  console.log(item);
};
const onUnbindOk = async function (): Promise<void> {
  let { relation_id, activePhone, passivePhone, passiveCode, activeCode } = unbindForm.form;
  let data = {
    relation_id,
    active_user_phone: activePhone,
    active_user_code: activeCode,
    passive_user_phone: passivePhone,
    passive_user_code: passiveCode,
  };
  unbindForm.confirmLoading = true;
  try {
    let { status } = await deleteServerRelation(data);
    if (status === 0) {
      unbindForm.visible = false;
      message.success('解除关联成功！');
      getTableList();
      return;
    }
  } finally {
    unbindForm.confirmLoading = false;
  }
};
const onRelevancyOk = async function (): Promise<void> {
  if (!relevancyForm.form.code.trim() || !relevancyForm.form.phone.trim()) {
    message.error('请输入关联店铺信息！');
    return;
  }
  relevancyForm.confirmLoading = true;
  try {
    let { status } = await addServerRelation({
      code: relevancyForm.form.code,
      phone: relevancyForm.form.phone,
    });
    if (status === 0) {
      onReLoadPage();
    }
  } finally {
    relevancyForm.confirmLoading = false;
  }
};
const closeUnbindModal = function () {
  unbindForm.visible = false;
};

const checkPhone = function (type: string) {
  if (type === 'active' && !unbindForm.form.activePhone && !/^1[3-9]\d{9}$/.test(unbindForm.form.activePhone)) {
    message.warn('请输入正确的手机号!');
    return false;
  }
  if (type === 'passive' && !unbindForm.form.passivePhone && !/^1[3-9]\d{9}$/.test(unbindForm.form.passivePhone)) {
    message.warn('请输入正确的关联账号!');
    return false;
  }
  if (type === 'alone' && !relevancyForm.form.phone && !/^1[3-9]\d{9}$/.test(relevancyForm.form.phone)) {
    message.warn('请输入正确的手机号!');
    return false;
  }
  return true;
};
const onGetCode = function (type: string) {
  if (!checkPhone(type)) return;
  if (type === 'active' && unbindForm.form.activeHasClick) {
    countDownFn(type, 'activeTimes', 'activePhone', 'activeHasClick', 'activeSec', 'unbindForm');
  } else if (type === 'passive' && unbindForm.form.passiveHasClick) {
    countDownFn(type, 'passiveTimes', 'passivePhone', 'passiveHasClick', 'passiveSec', 'unbindForm');
  } else if (type === 'alone' && relevancyForm.form.hasClick) {
    countDownFn(type, 'times', 'phone', 'hasClick', 'sec', 'relevancyForm');
  }
};
const countDownFn = async function (
  type: string,
  timesName: string,
  phoneName: string,
  clickName: string,
  secName: string,
  modalType: string,
): Promise<void> {
  let form = modalType === 'unbindForm' ? unbindForm.form : relevancyForm.form;
  let phone = modalType === 'unbindForm' ? unbindForm.form[phoneName] : relevancyForm.form[phoneName];

  form[clickName] = false;
  try {
    let { status } =
      modalType === 'unbindForm'
        ? await getUnbindCode({ type, relation_id: unbindForm.form.relation_id as number, phone })
        : await getRelevancyCode({ phone });
    if (status === 0) {
      form[timesName] = setInterval(() => {
        if (form[secName] >= 1) {
          form[secName] -= 1;
          form[clickName] = false;
        } else {
          clearInterval(form[timesName]);
          form[timesName] = null;
          form[secName] = EConfig.Sec;
          form[clickName] = true;
        }
      }, 1000);
    }
  } catch (error) {
    form[clickName] = true;
  }
};

const gotoRulesDetails = function () {
  const origin = decodeURIComponent(window.location.search.split('origin=')[1]);
  const linkElement = document.createElement('a');
  linkElement.href = window.location.href;
  const baseUrl = linkElement.protocol + '//' + linkElement.host;
  window.open(baseUrl + '/admin-merchant/university?' + `type=rules&id=139&origin=${origin}`);
};

const loadingJSFile = function () {
  let scriptDom = document.createElement('script'); //创建script标签
  scriptDom.type = 'text/javascript';
  scriptDom.src = 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js';
  document.body.appendChild(scriptDom);
};
loadingJSFile();

// 更改页面顶级iframe属性，不然safari浏览器中，扫码成功重定向会被拦截
const changePageIframeAttr = function () {
  console.log('changePageIframeAttr');
  window.parent.postMessage(
    {
      action: 'changeFrameAttr',
    },
    '*',
  );
};
changePageIframeAttr();

onUnmounted(() => {
  clearInterval(unbindForm.form.activeTimes);
  clearInterval(unbindForm.form.passiveTimes);
});
</script>
<style lang="less" scoped src="./index.less"></style>
