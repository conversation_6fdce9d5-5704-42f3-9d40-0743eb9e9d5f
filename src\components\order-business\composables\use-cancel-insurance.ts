import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';

import { cancelInsurance, superCancelInsurance } from '@/components/order-business/composables/services';

export default function () {
  const isSuper = useRoute().query.role === 'super';
  const cancelInsuranceApi = isSuper ? superCancelInsurance : cancelInsurance;

  function confirmCancel(config: { content?: string; orderId: string; successCallback?: () => void }) {
    Modal.confirm({
      title: '温馨提示',
      content: config?.content || '订单已取消，系统识别当前订单在退保时效内，是否申请退保？',
      onOk() {
        cancelInsuranceApi({ order_id: config.orderId }).then(res => {
          message.success(res.message);
          typeof config.successCallback === 'function' && config.successCallback();
        });
      },
    });
  }

  return {
    isSuper,
    cancelInsuranceApi,
    confirmCancel,
  };
}
