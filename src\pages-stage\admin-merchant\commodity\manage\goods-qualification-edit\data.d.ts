import { EQualificationFileType } from '@/pages-stage/commodity/dict/qualification/data.d';

import { TImgae } from '../store-info/components/shop-qualification/data.d';

export type TFormState = {
  dict_id?: number;
  config_name: string;
  customize_name?: string;
  image_list: TImgae[];
  valid_start_at_label: string;
  valid_end_at_label: string;
  reject_reason: string;
  prompt?: string;
  config_type?: EQualificationFileType;
};
