<template>
  <a-modal
    v-model:visible="bindVisible"
    class="revoke-modal"
    title="撤销原因选择"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 4 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item
          label="原因"
          name="reason_type"
        >
          <a-select
            v-model:value="formData.reason_type"
            :allow-clear="true"
            :options="reasonOptions"
            placeholder="请选择"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useVModel } from '@/hook';
import { FormInstance, message } from 'ant-design-vue';
import { revokeOrderStatus } from './services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

interface IFormData {
  reason_type?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const formData = ref<IFormData>({ reason_type: '1' });
const bindVisible = useVModel(props, 'visible', emit);

const reasonOptions = [
  {
    label: '已和商家核实未归还设备,返回待归还正确状态',
    value: '1',
  },
  {
    label: '订单查无归还物流信息，返回待归还正确状态',
    value: '2',
  },
  {
    label: '物流单号显示错误，请重新填写正确物流信息',
    value: '3',
  },
];

const rules = {
  reason_type: [{ required: true, message: '原因不能为空', trigger: 'blur' }],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      order_id: props.orderId,
      reason_type: formData.value.reason_type,
    };
    revokeOrderStatus(params)
      .then(() => {
        message.success('操作成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data', 'all_remark']);
      })
      .finally(() => (loading.value = false));
  });
}
</script>
