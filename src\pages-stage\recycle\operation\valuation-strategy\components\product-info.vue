<template>
  <div class="product-info">
    <div class="product-info__header">
      {{ item.goods_name }}
    </div>
    <div class="product-info__container">
      <span
        v-for="(skuText, skuIndex) in item.sku_list"
        :key="skuIndex"
        class="sku-tag"
      >
        {{ skuText }}
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { IBatchSettingGoods } from '../data.d';

defineProps({
  item: {
    type: Object as PropType<IBatchSettingGoods>,
    default: () => ({}),
  },
});
</script>

<style lang="less" setup>
.product-info {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 2px;
  &__header {
    margin-bottom: 16px;
    padding-bottom: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    border-bottom: 1px solid #e9e9e9;
  }
  &__container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}
.sku-tag {
  padding: 0 8px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  background: #fff;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 4px;
}
</style>
