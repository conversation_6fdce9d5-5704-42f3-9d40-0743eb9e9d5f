<template>
  <div>
    <a-upload
      v-model:file-list="fileList"
      accept=".jpg, .jpeg, .png, .gif"
      :before-upload="() => false"
      :class="className"
      :disabled="disabled"
      list-type="picture-card"
      :multiple="multiple"
      :show-upload-list="showUploadList"
      :with-credentials="true"
      @change="handleChange"
      @preview="handlePreview"
    >
      <div v-if="fileList.length < Number(max)">
        <plus-outlined />
        <div class="ant-upload-text">
          {{ uploadText }}
        </div>
      </div>
    </a-upload>
    <!-- 预览图片 -->
    <a-image
      :preview="{
        visible: preview.visible,
        onVisibleChange: (visible: boolean) => preview.visible = visible,
      }"
      :src="preview.image"
      style="display: none"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, PropType, watch } from 'vue';
import { message } from 'ant-design-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import { InternalUploadFile } from 'ant-design-vue/lib/upload/interface';
import { PlusOutlined } from '@ant-design/icons-vue';
import { IAxiosResponse } from '@/services/api';
import { Quality, StartUploadOptions, ParseFile } from './data';
import { generateAndUploadImage2Oss } from '@/utils/oss-helper';
import type { TUploadImageType } from '@/utils/oss-helper';

function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

export default defineComponent({
  components: {
    PlusOutlined,
  },
  props: {
    // 传入图片列表，支持双向绑定，列表例子 ['aaa.png', 'bbb.png']
    value: {
      type: Array as PropType<string[]>,
      default(): string[] {
        return [];
      },
    },
    // 部分url无携带域名，可传此参数
    domain: {
      type: String,
      default: '',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    action: {
      type: String as PropType<TUploadImageType>,
      default: 'site',
    },
    max: {
      type: [Number, String],
      default: 5,
    },
    className: {
      type: String,
      default: '',
    },
    uploadText: {
      type: String,
      default: '上传图片',
    },
    onError: {
      type: Function,
      default: (res: IAxiosResponse<void>) => {
        message.warning(res.message || '抱歉，上传时发生未知错误，请重试');
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    showUploadList: {
      type: Boolean,
      default: true,
    },
    // 图片质量，为空的话 默认为m
    quality: {
      type: String as PropType<Quality>,
      default: '',
    },
    isDelCaptureAttr: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    const fileList = ref<(InternalUploadFile | ParseFile)[]>([]);
    const path = ref<string[]>([]);
    const preview = reactive({
      image: '',
      visible: false,
      title: '',
    });

    watch(
      props.value,
      (value, oldVal) => {
        if (JSON.stringify(value) === JSON.stringify(oldVal)) return;
        fileList.value = !value
          ? []
          : (value.map((url, index) => ({
              url: `${props.domain}${url}`,
              status: 'done',
              uid: index,
              name: url,
              path: path.value[index], // 可能没有这个参数
            })) as any);
      },
      {
        immediate: true,
      },
    );

    // 传递原始的change事件
    function handleChange(info: UploadChangeParam) {
      emit('change', info);
    }

    // 预览
    async function handlePreview(file: InternalUploadFile) {
      if (!file.url && !file.preview) {
        file.preview = (await getBase64(file.originFileObj)) as string;
      }
      Object.assign(preview, {
        image: file.url || file.preview,
        visible: true,
      });
    }

    // 上传到oss
    async function startUpload(options: StartUploadOptions = {}) {
      const PROGRESS_MAX = fileList.value.length * 100; // 进度走满的数值
      let progress_current = 0; // 当前进度
      const progressList = new Array(fileList.value.length).fill(0);
      try {
        const [path, list]: string[][] = [[], []];
        // 阿里云压缩上传
        fileList.value.forEach(async (file, index) => {
          if (!(file as InternalUploadFile).originFileObj) {
            // 没有该属性说明是已经上传过的图片
            progressList[index] = 100;
            path.push((file as ParseFile).path || '');
            list.push(file.url || '');
            return;
          }
          const res = await generateAndUploadImage2Oss(
            (file as InternalUploadFile).originFileObj,
            {
              progress: percent => {
                progressList[index] = percent * 100;
                // 计算总进度
                progress_current = progressList.reduce((sum, current) => sum + current);
                options.onProgress && options.onProgress(progress_current / PROGRESS_MAX); // 算出最大值为100的比例
              },
            },
            props.action,
          );
          const imageUrls = { m: res.m, o: res.o, s: res.s };
          if (props.quality) {
            // 有要求图片质量
            path.push(res.mPath as string);
            list.push(imageUrls[props.quality] as string);
          } else {
            path.push(res.mPath as string);
            list.push(`${res.oss || ''}${res.mPath}`);
          }
        });
        // 上传完成后再更新列表
        emit('update:value', list);
        options.onSuccess && options.onSuccess(list, fileList.value, path);
      } catch (error) {
        props.onError();
      }
    }

    // 删除input上的capture属性
    if (props.isDelCaptureAttr) {
      const el = document.querySelector('.img_upload_wrap #form_item_cover');
      el?.removeAttribute('capture');
    }

    return {
      fileList,
      path,
      preview,
      handleChange,
      handlePreview,
      startUpload,
    };
  },
});
</script>
<style lang="less" scoped>
.ant-upload-select-picture-card i {
  color: #999;
  font-size: 32px;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.ant-upload {
  margin-bottom: 0;
}
</style>
