<!-- 反馈与建议 -->
<template>
  <layout-admin-page
    :navs="['趣回收', '运营管理', '反馈与建议']"
    title="反馈与建议"
  >
    <div class="container">
      <div class="search">
        <form-create
          ref="searchFormRef"
          v-model:value="searchParams"
          class="search-form-grid"
          :form-group="searchFormGroup"
          :origin-props="{
            layout: 'inline',
          }"
        >
          <template #buttons>
            <a-button
              style="margin-right: 10px"
              type="primary"
              @click="getTableList"
            >
              查询
            </a-button>
            <a-button @click="reloadContenxt">
              重置
            </a-button>
          </template>
        </form-create>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table"
          :columns="columns"
          :data-source="list"
          :loading="listLoading"
          :pagination="page"
          :scroll="{ x: '100%' }"
          sticky
          @change="tableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button
                class="link-btn"
                type="link"
                @click="reviewFeedback(record.id)"
              >
                查看详情
              </a-button>
            </template>

            <template v-if="column.dataIndex === 'phone'">
              <sensitive-field
                field="phone"
                field-type="1"
                id-key="id"
                :is-super-v2="true"
                :row="record"
                :type="850"
              />
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 详情 -->
    <feedback-detail ref="feedbackDetailRef" />
  </layout-admin-page>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { useTable } from '@/hook/component/use-table';

import FeedbackDetail from './components/feedback-detail.vue';
import { columns, searchFormGroup } from './config';
import type { IFeedbackCollectionList, IFeedBackSearch } from './data';

const searchParams = reactive<Partial<IFeedBackSearch>>({
  time: [],
});

const searchFormRef = ref();

const feedbackDetailRef = ref<InstanceType<typeof FeedbackDetail> | null>(null);

const { list, listLoading, page, getTableList, tableChange } = useTable<
  IFeedbackCollectionList,
  Partial<IFeedBackSearch>
>({
  url: '/super/recycle/feedback/list',
  totalKey: 'data.pageInfo.count',
  searchForm: searchParams,
  formatSearchValue: res => {
    return {
      ...res,
      created_at_gt: res?.time?.[0],
      created_at_lt: res?.time?.[1],
    };
  },
});

const reloadContenxt = () => {
  searchFormRef.value?.getFormRef().resetFields();
  getTableList();
};

const reviewFeedback = (id: string) => {
  feedbackDetailRef.value?.disPatch({
    action: 'VIEW',
    params: {
      id,
    },
  });
  return 0;
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
.container {
  margin: 24px;
  .search {
    margin: 24px 0;
  }
}
.link-btn {
  padding-left: 0;
  color: #3777ff;
}
</style>
