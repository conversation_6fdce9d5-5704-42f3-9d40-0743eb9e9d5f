import { ref } from 'vue';
import { useRouter } from 'vue-router';
import moment from 'moment';

import { EQualificationFileType } from '@/pages-stage/commodity/dict/qualification/data.d';
import { getOssImageParams } from '@/utils/oss-helper';
import uploadToOss from '@/utils/oss-upload';

import { TImgae } from '../../store-info/components/shop-qualification/data.d';
import { TResultImage } from '../data.d';
interface IUseCommonFun {
  endDisabledDate: (current: moment.Moment, valid_start_at_label: string) => boolean;
  startDisabledDate: (current: moment.Moment, valid_end_at_label: string) => boolean;
  getFormatImgs: (imgs: TImgae[]) => Promise<TResultImage[]>;
  handleResponeImgs: (imgs: any[]) => TImgae[];
  goBack: (back: EQualificationFileType, num?: number) => void;
}
export default function useCommonFun(): IUseCommonFun {
  const router = useRouter();
  const imgOssServer = ref('https://img-t1.rrzuji.cn');
  function endDisabledDate(current: moment.Moment, valid_start_at_label: string) {
    if (valid_start_at_label) {
      const targetDate = moment(valid_start_at_label, 'YYYY-MM-DD');
      return current && current <= targetDate;
    } else {
      return false;
    }
  }

  function startDisabledDate(current: moment.Moment, valid_end_at_label: string) {
    if (valid_end_at_label) {
      const targetDate = moment(valid_end_at_label, 'YYYY-MM-DD');
      return current && current >= targetDate;
    } else {
      return false;
    }
  }

  async function getFormatImgs(imgs: TImgae[]) {
    const length = imgs?.length;
    const result: TResultImage[] = [];
    for (let index = 0; index < length; index++) {
      const element = imgs[index];
      const isDetail = element.status === 'isDetail';
      const watermark_image = isDetail
        ? element.watermark_image?.replace(imgOssServer.value, '')
        : await initCanvas(element.url);
      result.push({
        image: isDetail ? element.url.replace(imgOssServer.value, '') : '/' + element.response?.path[0],
        watermark_image,
      });
    }
    return Promise.resolve(result);
  }

  function handleResponeImgs(imgs: any[]) {
    return (
      imgs?.map((imgItem: any, index: number) => {
        return {
          uid: index,
          status: 'isDetail',
          name: `file${index}.png`,
          url: imgItem.image as string,
          watermark_image: imgItem.watermark_image,
        };
      }) || []
    );
  }

  function base64ToFile(base64String: string, fileName: string, mimeType: string) {
    // 1. 解析 Base64 数据
    const byteCharacters = atob(base64String.split(',')[1]); // 去掉 Base64 前缀（如 `data:image/png;base64,`）
    // 2. 创建二进制数组
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    // 3. 创建 Uint8Array
    const byteArray = new Uint8Array(byteNumbers);
    // 4. 创建 Blob 对象
    const blob = new Blob([byteArray], { type: mimeType });
    // 5. 创建 File 对象
    return new File([blob], fileName, { type: mimeType });
  }

  /**
   *  imgSrc: 将传入的图片地址转到canvas上
   *  flag: 默认将传入的图片添加水印
   */
  const initCanvas = function (imgSrc: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const cvs = document.createElement('canvas') as HTMLCanvasElement;
      const ctx = cvs.getContext('2d') as CanvasRenderingContext2D;
      const imgEle = new Image();
      imgEle.src = imgSrc;
      imgEle.setAttribute('crossOrigin', 'Anonymous');
      cvs.setAttribute('width', '1');
      cvs.setAttribute('height', '1');
      imgEle.onload = async function () {
        cvs.setAttribute('width', imgEle.width.toString());
        cvs.setAttribute('height', imgEle.height.toString());
        ctx.drawImage(imgEle, 0, 0);
        const resuleSrc = drawWaterMark(ctx, imgEle.width, imgEle.height, cvs);
        const file = base64ToFile(resuleSrc, 'file.png', 'image/png');
        const result = await uploadToOss({
          files: [file],
          file_type: 'image',
        });
        resolve('/' + result.path[0]);
      };
      imgEle.onerror = function () {
        reject('资质图片加载失败!');
      };
    });
  };

  // 给对应的canvas添加水印
  const drawWaterMark = function (
    ctx: CanvasRenderingContext2D,
    imgWidth: number,
    imgHeight: number,
    cvs: HTMLCanvasElement,
  ): string {
    const maskText = '仅限人人租平台租赁使用'; // 水印文字
    const fontColor = 'rgba(0, 0, 0, 0.1)'; // 水印颜色
    const fontSize = imgWidth / 20; // 基于图片宽度的字体大小
    const lineHeight = fontSize * 5; // 行高为字体大小的5倍
    const textWidth = fontSize * 15; // 水印文字宽度
    const diagonalLength = imgHeight > imgWidth ? imgHeight * 2 : imgWidth; // 选取最长边
    ctx.translate(-imgWidth / 2, imgHeight / 2); // 画布旋转原点 移到 图片中心
    ctx.rotate(-Math.PI / 5);
    // ctx.setGlobalAlpha(0.8);
    const crossTime = Math.ceil((diagonalLength * 2) / textWidth);
    // 竖向循环次数
    const verticalTime = Math.ceil((diagonalLength * 2) / lineHeight);
    for (let j = 0; j < verticalTime; j++) {
      // 纵向循环
      ctx.font = `${fontSize}px Arial`;
      ctx.fillStyle = fontColor;
      ctx.fillText(maskText, 0, lineHeight * j);
      for (let i = 1; i < crossTime; i++) {
        // 横向循环
        ctx.font = `${fontSize}px Arial`;
        ctx.fillStyle = fontColor;
        ctx.fillText(maskText, textWidth * i, lineHeight * j);
      }
    }
    return cvs.toDataURL('image/png', 1);
  };

  function goBack(back: EQualificationFileType, num?: number) {
    window.sessionStorage.setItem('backQualificationType', back + '');
    router.go(num || -1);
  }

  async function getImageHost() {
    const data = await getOssImageParams();
    imgOssServer.value = data.imgOssServer;
  }
  getImageHost();
  return {
    endDisabledDate,
    startDisabledDate,
    getFormatImgs,
    handleResponeImgs,
    goBack,
  };
}
