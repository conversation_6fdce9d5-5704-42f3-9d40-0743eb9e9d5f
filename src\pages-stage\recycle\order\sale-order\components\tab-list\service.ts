import { GET, POST } from '@/services/api';

/**
 * @description: 获取物流轨迹
 */
export function getLogisticsTrajectory(data: any) {
  data.method = 'logistic.code.get';
  return POST('/api/gateway', data, {
    successStatusCheckValue: -3,
  });
}

// 获取物流轨迹(新)
export function getTrackingInfo(params: any) {
  return GET('/warehouse/external/getTrackingInfo', params, { hostType: 'Golang' });
}

/**
 * crm备注最新一条数据获取
 */
export function ApiGetRemarks(data: { unionIds: string[]; unionSuffix: string }) {
  return POST('/crm/data', data);
}
