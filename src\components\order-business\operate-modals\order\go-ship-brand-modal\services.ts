import { POST } from '@/services/api';

/**
 * @description: 人人调用联想接口同步订单数据 - 运营端后台
 * @param {any} data
 * @return {*}
 */
export function superPostSyncOrderToLenovo(data: { order_id: string }) {
  return POST('/super/third-party-store/sync-order-to-lenovo', data);
}

/**
 * @description: 人人调用联想接口同步订单数据 - 商家端后台
 * @param {any} data
 * @return {*}
 */
export function postSyncOrderToLenovo(data: { order_id: string }) {
  return POST('/third-party-store/sync-order-to-lenovo', data);
}
