export interface IFile extends File {
  lastModifiedDate?: Date;
}

export type HasUserCredentialsData = {
  new_is_upload_watermark_face_img?: string;
  new_is_upload_watermark_back_img?: string;
  old_watermark_face?: string;
  watermark_face?: string;
  is_upload_watermark_face_img?: string;
  faceFile?: IFile;
  old_watermark_back?: string;
  watermark_back?: string;
  is_upload_watermark_back_img?: string;
  backFile?: IFile;
};
