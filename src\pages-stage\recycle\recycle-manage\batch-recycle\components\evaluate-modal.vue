<template>
  <a-modal
    :destroy-on-close="true"
    :footer="false"
    title="预估信息"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
  >
    <a-spin :spinning="isLoading">
      <div
        v-for="item in formConfig"
        :key="item.key"
        class="info-item"
      >
        <span class="info-item__label">{{ item.label }}：</span>
        <span class="info-item__value">
          {{ info[item.key] }}
        </span>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { GET } from '@/services/api';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  requestUrl: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible']);
const onCancel = () => emits('update:visible', false);

const isLoading = ref<boolean>(false);

const formConfig = [
  { label: 'IMEI码', key: 'imei' },
  { label: '型号', key: 'model' },
  { label: '内存', key: 'memory' },
  { label: '颜色', key: 'color' },
  { label: '版本', key: 'version' },
  { label: '成新度', key: 'condition' },
];

const info = ref({});

onMounted(() => {
  isLoading.value = true;
  GET(props.requestUrl, { orderId: props.orderId })
    .then(res => {
      info.value = res.data || {};
    })
    .finally(() => {
      isLoading.value = false;
    });
});
</script>
<style lang="less" scoped>
.info-item {
  display: flex;
  margin-bottom: 24px;
  &:nth-last-child(1) {
    margin-bottom: 0;
  }
  &__label {
    flex-shrink: 0;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  &__value {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
