<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    class="classified-modal"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    :title="title"
    :visible="visible"
    width="560px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        layout="vertical"
        :model="formState"
        style="width: 100%"
      >
        <a-form-item
          label="商品简称"
          name="subName"
          :rules="[{ required: true, message: '请输入商品简称' }]"
        >
          <a-input
            v-model:value="formState.subName"
            placeholder="请输入商品名称(最多20个字符)"
          />
        </a-form-item>
        <a-form-item
          label="商品分类"
          name="classify"
          :rules="[{ required: true, message: '请选择商品分类' }]"
        >
          <a-cascader
            v-model:value="formState.classify"
            expand-trigger="hover"
            :options="commodityOptions"
            placeholder="请选择商品分类"
            @change="handleClassifyChange"
          />
        </a-form-item>
        <a-form-item
          label="机型"
          name="warehouse_dict_id"
          :rules="[{ required: true, message: '请选择机型' }]"
        >
          <a-select
            v-model:value="formState.warehouse_dict_id"
            :options="modelOptions"
            placeholder="请输入机型名称"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item
          class="upload-item"
          label="商品图片"
          :rules="[{ required: true }]"
        >
          <image-upload
            ref="imageUpload"
            v-model:value="images"
            :max="1"
            quality="o"
            upload-text="上传图片"
          />
          <div class="text">
            建议上传图片：格式jpg、jpeg、png，尺寸200x200像素。
          </div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { getCommodityOptions, searchCommodity, createCommodity, updateCommodity, warehouseGoodsList } from '../service';
import ImageUpload from '@/components/image-upload/image-upload.vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  actionType: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const TITLE_MAP = {
  create: '创建商品',
  update: '编辑商品',
  check: '查看商品',
};

const title = computed(() => {
  return TITLE_MAP[props.actionType] || '编辑';
});

const formRef = ref<FormInstance>();
const formState = ref<any>({
  subName: '',
  classify: [],
});

const images = ref<string[]>([]);

const loading = ref(false);
const submitLoading = ref(false);

const commodityOptions = ref<any[]>([]);
const modelOptions = ref<any[]>([]);

const onCancel = () => emits('update:visible', false);

const onOk = () => {
  if (props.actionType === 'check') {
    message.info('当前商品已上架，不可编辑');
    return;
  }

  formRef.value?.validateFields().then(() => {
    if (!images.value.length) {
      message.warn('请上传商品图片');
      return;
    }
    submitLoading.value = true;
    let a = document.createElement('a');
    a.href = images.value[0];
    const baseParams = {
      subName: formState.value.subName,
      categoryId: formState.value.classify[formState.value.classify.length - 1],
      warehouse_dict_id: formState.value.warehouse_dict_id,
      image: [a.pathname],
    };
    if (props.actionType === 'create') {
      handleSubmit(createCommodity.bind('', baseParams));
    }
    if (props.actionType === 'update') {
      handleSubmit(
        updateCommodity.bind('', {
          id: props.id,
          ...baseParams,
        }),
      );
    }
  });
};

const handleSubmit = (callback: (...args: any) => Promise<any>) => {
  callback &&
    callback()
      .then(() => {
        onCancel();
        emits('onLoadList');
        message.success('操作成功');
      })
      .finally(() => (submitLoading.value = false));
};

onMounted(() => {
  if (['update', 'check'].includes(props.actionType)) {
    if (!props.id) return;
    loading.value = true;
    searchCommodity(props.id)
      .then(async ({ data: commodity }: any) => {
        modelOptions.value = [];
        formState.value.name = commodity.name;
        formState.value.subName = commodity.sub_name;
        formState.value.classify = [Number(commodity.parent_category_id), Number(commodity.category_id)];
        formState.value.warehouse_dict_id = commodity.warehouse_dict_id;
        const res = await warehouseGoodsList(commodity.category_id, commodity.warehouse_dict_id);
        modelOptions.value = res.data;
        images.value = commodity.image;
      })
      .finally(() => (loading.value = false));
  }

  getCommodityOptions().then(({ data }: any) => {
    data.category.forEach((item: any) => {
      const params = {
        value: item.label.id,
        label: item.label.name,
        children: [
          ...item.label.child.map((item: any) => ({
            value: item.id,
            label: item.name,
          })),
        ],
      };
      commodityOptions.value && commodityOptions.value.push(params);
    });
  });
});
const handleClassifyChange = async (category_id: number) => {
  modelOptions.value = [];
  formState.value.warehouse_dict_id = undefined;
  const res = await warehouseGoodsList(category_id[1]);
  modelOptions.value = res.data;
};
</script>
<style scoped lang="less">
.classified-modal .ant-modal-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 16px;
}
.upload-item {
  position: relative;
}
.text {
  position: absolute;
  bottom: 6px;
  left: 120px;
  color: #666;
}
</style>
