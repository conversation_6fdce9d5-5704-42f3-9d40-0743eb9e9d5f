// shop/template/select
import { GET, POST } from '@/services/api';

export function fetchSelect(): Promise<any> {
  return GET('/shop/template/select-inf');
}

export function updateSelect(data: any): Promise<any> {
  return POST('/shop/template/select-inf', data);
}

export function createShop(): Promise<any> {
  return POST('/shop/template/open');
}

export function storeShopInfo(data: { id: number }): Promise<any> {
  return POST('/shop/template/backup', data);
}
