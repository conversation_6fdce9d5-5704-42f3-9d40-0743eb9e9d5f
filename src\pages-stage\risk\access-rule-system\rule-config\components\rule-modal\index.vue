<template>
  <a-modal
    :confirm-loading="ruleModalLoading"
    :title="ruleModalData.title"
    :visible="props.visible"
    @cancel="emits('update:visible', false)"
    @ok="submitRuleModal"
  >
    <a-form
      ref="ruleModalFormRef"
      :model="ruleModalForm"
    >
      <a-form-item
        label="渠道名称"
        name="title"
        :rules="[{ required: true, message: '请输入渠道名称' }]"
      >
        <a-input
          v-model:value="ruleModalForm.title"
          :maxlength="20"
          placeholder="请输入渠道名称"
          show-count
        />
      </a-form-item>
      <a-form-item
        label="渠道绑定"
        name="method"
        :rules="[{ required: true, message: '请选择渠道绑定' }]"
      >
        <a-select
          v-model:value="ruleModalForm.method"
          :filter-option="filterOption"
          mode="multiple"
          :options="ruleOptions.method"
          placeholder="请选择渠道绑定"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';
import { apiSaveRuleConfig } from '../../services';
import { ruleOptions } from '../../config';
import { IRuleModalForm } from './data';
import { message } from 'ant-design-vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  record: {
    type: Object || undefined,
    default: undefined,
  },
});
const emits = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  // success
  (event: 'success'): void;
}>();
const type = ref<'create' | 'edit'>('create');
const ruleModalTitleMap = {
  create: '新建准入规则',
  edit: '修改准入规则',
};

const ruleModalFormRef = ref();
const ruleModalLoading = ref(false);
const ruleModalData = reactive({
  id: '',
  visible: false,
  title: ruleModalTitleMap.create,
});
const ruleModalForm = reactive<IRuleModalForm>({
  title: '',
  method: [],
});
function filterOption(input: string, option: any) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}
function initRuleModal(record?: any) {
  ruleModalData.id = '';
  ruleModalFormRef.value.resetFields();
  type.value = record ? 'edit' : 'create';
  ruleModalData.title = ruleModalTitleMap[type.value];

  if (type.value === 'edit') {
    ruleModalData.id = record.id;
    ruleModalForm.title = record.title;
    ruleModalForm.method = record.method;
  }
}
function submitRuleModal() {
  ruleModalFormRef.value.validate().then(() => {
    ruleModalLoading.value = true;
    apiSaveRuleConfig({
      id: ruleModalData.id || undefined,
      ...ruleModalForm,
    })
      .then(() => {
        message.success('操作成功');
        emits('update:visible', false);
        emits('success');
      })
      .finally(() => {
        ruleModalLoading.value = false;
      });
  });
}
watch(
  () => props.visible,
  val => {
    if (val) {
      initRuleModal(props.record);
    }
  },
  {
    flush: 'post',
  },
);
</script>
