<template>
  <div class="factor-select">
    <a-spin
      :spinning="loading"
      wrapper-class-name="factor-select-loading"
    >
      <a-select
        v-model:value="bindValue"
        :mode="mode"
        option-filter-prop="label"
        :options="rendOptions"
        placeholder="请选择"
        show-search
        v-bind="attrs"
      />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, useAttrs } from 'vue';

import { getFactorOptions } from '../helper';

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  value?: string[];
  code?: string;
  mode?: string;
  options?: { label: string; value: string | number }[];
}>();

const emit = defineEmits(['update:value']);

const bindValue = computed({
  get() {
    if (props.mode === 'multiple') {
      return props.value;
    }
    if (Array.isArray(props.value)) {
      // 非多选时选择第一个
      return props.value[0];
    }
    return props.value;
  },
  set(value?: string[]) {
    if (value) {
      // value固定为数组格式
      emit('update:value', Array.isArray(value) ? value : [value]);
    } else {
      emit('update:value', value);
    }
  },
});

const attrs = useAttrs();

const rendOptions = ref(props.options);

const loading = ref(false);

/** 如果未设选择项，则需要通过接口异步获取 */
function getOptions() {
  if (props.code) {
    loading.value = true;
    getFactorOptions({ code: props.code })
      .then(res => {
        // 选择项值统一使用string类型，确保与回显数据类型一致
        rendOptions.value =
          res.data?.[props.code]?.map(item => ({ label: item.label, value: String(item.value) })) || [];
      })
      .finally(() => (loading.value = false));
  }
}

getOptions();
</script>

<style lang="less" scoped>
.factor-select {
  width: 100%;

  :deep(.factor-select-loading) {
    width: 100%;
  }
}
</style>
