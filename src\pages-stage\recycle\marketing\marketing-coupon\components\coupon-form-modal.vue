<template>
  <a-modal
    :destroy-on-close="true"
    :title="editForm ? '修改营销券' : '添加营销券'"
    :visible="visible"
    :width="480"
    @cancel="close"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="formInfo"
      :rules="rules"
      style="width: 100%"
    >
      <a-form-item
        label="券名称"
        name="name"
      >
        <a-input
          v-model:value="formInfo.name"
          :disabled="!!editForm"
          :maxlength="6"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="活动名称"
        name="activity_name"
      >
        <a-input
          v-model:value="formInfo.activity_name"
          :disabled="!!editForm"
          :maxlength="20"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="适用商品"
        name="goods_type"
      >
        <a-radio-group
          v-model:value="formInfo.goods_type"
          :disabled="!!editForm"
          @change="goodsTypeChange"
        >
          <a-radio
            v-for="item in goodsTypeOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
            <span
              v-show="item.value === 2 && !editForm"
              class="goods-type--placeholder"
            >
              多个商品ID可用逗号、空格和回车键隔开
            </span>
          </a-radio>
        </a-radio-group>
        <a-form-item-rest v-if="formInfo.goods_type === 2">
          <GoodsUse
            ref="goodsUse"
            :is-edit="!!editForm"
            style="margin-top: 16px"
            @on-confirm="goodsUseCallBack"
          />
        </a-form-item-rest>
      </a-form-item>
      <a-form-item
        label="优惠券类型"
        name="type"
      >
        <a-select
          v-model:value="formInfo.type"
          :options="typeOptions"
          placeholder="请选择"
          style="width: 160px"
          @change="formInfo.discount = ''"
        />
      </a-form-item>
      <a-form-item
        v-show="formInfo.type"
        label="加价力度"
        name="discount"
      >
        <a-input-number
          v-model:value="formInfo.discount"
          :addon-after="formInfo.type === 2 ? '%' : null"
          :controls="false"
          :max="formInfo.type === 2 ? 100 : null"
          :min="0"
          :placeholder="formInfo.type === 2 ? '请输入百分比' : '请输入金额'"
          :precision="formInfo.type === 2 ? 2 : 0"
          style="width: 160px"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button
        :disabled="loading"
        @click="close"
      >
        取消
      </a-button>
      <a-button
        :loading="loading"
        type="primary"
        @click="onSubmit"
      >
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue';
import { useModal } from '@/hook/component/use-modal';
import { IDataInfo } from '../data';
import { createCoupon, modifyCoupon, getTypeOptions } from '../service';
import GoodsUse from './goods-use.vue';

const emits = defineEmits(['init']);

const formRef = ref<any>(null);
const goodsUse = ref<any>(null);

const editForm = ref<any>(null);

const BASEFORM = {
  name: '',
  activity_name: '',
  goods_type: null,
  goods_id: [],
  type: null,
  discount: '',
};
const formInfo = reactive<any>(JSON.parse(JSON.stringify(BASEFORM)));

const formInit = () => {
  const keyArr = Object.keys(BASEFORM);
  keyArr.forEach((key: string) => {
    if (typeof BASEFORM[key] === 'object') {
      formInfo[key] = JSON.parse(JSON.stringify(BASEFORM[key]));
    } else {
      formInfo[key] = BASEFORM[key];
    }
  });
};

const { visible, loading, open, close } = useModal(
  () => {
    return;
  },
  {
    beforeClose: () => {
      editForm.value = null;
      formInit();
    },
  },
);

const rules = {
  name: [{ required: true, message: '请输入券名称', trigger: 'blur' }],
  activity_name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  goods_type: [
    {
      required: true,
      validator: () => {
        if (!formInfo.goods_type) return Promise.reject('请选择适用商品类型');
        if (formInfo.goods_type === 2 && !formInfo.goods_id.length) return Promise.reject('请录入商品ID');
        return Promise.resolve();
      },
    },
  ],
  type: [{ required: true, message: '请选择优惠券类型', trigger: 'blur' }],
  discount: [{ required: true, message: '请输入加价力度', trigger: 'blur' }],
};

const goodsTypeOptions = ref<Record<string, number>[]>([]);
const typeOptions = ref<Record<string, number>[]>([]);
const getOpions = async () => {
  const res = await getTypeOptions();
  const data = res.data || {};
  const goods_type = data.goods_type || [];
  const type = data.type || [];
  goodsTypeOptions.value = goods_type;
  typeOptions.value = type;
};

const onShow = (info?: IDataInfo) => {
  open();
  if (info) {
    editForm.value = info;
    const keys = Object.keys(formInfo);
    keys.forEach((key: string) => {
      if (info.hasOwnProperty(key)) {
        formInfo[key] = info[key];
      }
    });
    nextTick(() => {
      goodsUse.value?.setGoodsTag(info.goods_id);
    });
  }
};

const goodsTypeChange = () => {
  formInfo.goods_id = [];
  goodsUse.value?.onReset();
};

const goodsUseCallBack = (list: string[], cb: (list: string[]) => void) => {
  const arr = [...formInfo.goods_id, ...list];
  formInfo.goods_id = [...new Set(arr)];
  cb(formInfo.goods_id);
  formRef.value.validateFields('goods_type');
};

const onSubmit = async () => {
  await formRef.value.validate();
  let params: any = {};
  if (!editForm.value) {
    params = {
      ...formInfo,
    };
    params.goods_id = [...formInfo.goods_id].join(',');
  } else {
    params.coupon_market_id = editForm.value.id;
    params.type = formInfo.type;
    params.discount = formInfo.discount;
  }
  const requestFn = editForm.value ? modifyCoupon : createCoupon;
  loading.value = true;
  try {
    await requestFn(params);
    close();
    emits('init');
  } finally {
    loading.value = false;
  }
};

getOpions();

defineExpose({
  onShow,
});
</script>

<style>
.goods-type--placeholder {
  color: #c1c4cc;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
</style>
