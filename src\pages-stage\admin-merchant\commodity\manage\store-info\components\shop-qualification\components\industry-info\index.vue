<template>
  <div class="container">
    <p class="desc">
      根据平台要求，特殊行业资质需要上传对应的资质图片；不符合行业资质要求的商品将无法进行发布 相关资质文件：<span
        class="link-text"
        @click="goLink"
      >《人人租平台资质》</span>
    </p>
    <div
      v-if="tagsData"
      class="catgroy-box"
    >
      <h4 class="catgroy-title">
        经营类目：
      </h4>
      <p>{{ tagsData }}</p>
    </div>
    <div class="industry-info">
      <header class="title">
        行业资质
        <a-button
          type="link"
          @click="getTableList"
        >
          <template #icon>
            <RedoOutlined />
          </template>
        </a-button>
        <a-button
          class="add-btn"
          ghost
          size="small"
          type="primary"
          @click="addIndustry"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          添加行业资质
        </a-button>
      </header>
      <a-spin :spinning="listLoading">
        <ul
          v-if="industryInfoList.length"
          class="info-list"
        >
          <li
            v-for="(item, index) in industryInfoList"
            :key="index"
            :class="['info-item', releaseStatusColorMap[item.release_status].className]"
          >
            <ListItem
              :index="index"
              :item="item"
              :page="page"
              @go-detail="goDetail"
              @refush="getTableList"
            />
          </li>
          <div class="page-bottom">
            <a-pagination
              v-model:current="page.current"
              v-model:page-size="page.pageSize"
              :show-total="total => `总数 ${total} 条`"
              :total="page.total"
              @change="getTableList"
            />
          </div>
        </ul>
        <a-empty
          v-else
          class="empty-box"
        />
      </a-spin>
    </div>
  </div>
  <AddCategoryModal ref="addCategoryModalref" />
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { PlusOutlined, RedoOutlined } from '@ant-design/icons-vue';
import { useTable } from 'rrz-web-design';

import { getBusinessUniversityBaseURL } from '@/utils';
import { getTopWindowUrl } from '@/utils/url';

import { ECurrentDataType, EHandleType } from '../../data.d';
import AddCategoryModal from './components/add-category-modal/index.vue';
import ListItem from './components/list-item/index.vue';
import { releaseStatusColorMap } from './config';
import { apiGetIndustryInfoList, getServerTargetApi } from './service';
const tagsData = ref();
const router = useRouter();
const addCategoryModalref = ref();

const { list: industryInfoList, getTableList, page, listLoading } = useTable({
  api: apiGetIndustryInfoList,
  pageKey: 'page_no',
  pagination: {
    pageSize: 8,
  },
});
function addIndustry() {
  addCategoryModalref.value?.openModal();
}

function goDetail(info: Record<string, any>, type: EHandleType, dataType: ECurrentDataType) {
  router.push({
    path: '/merchant/commodity/manage/industry-qualification-edit',
    query: {
      type,
      dataType,
      id: info?.id,
      cateInfo: JSON.stringify({
        one_name: info.top_category_name,
        two_name: info.secondary_category_name,
        target_id: info.secondary_category,
      }),
    },
  });
}

async function goLink() {
  const { url } = await getTopWindowUrl({ getParams: false });
  const { origin } = new URL(url);
  const obj = {
    type: 'rules',
    id: '392',
    origin,
  };

  const queryText = new URLSearchParams(obj).toString();
  window.parent.postMessage(
    {
      action: 'blank',
      href: getBusinessUniversityBaseURL(origin) + `/admin-merchant/university?${queryText}`,
    },
    '*',
  );
}

async function getServerTarget() {
  const { data } = await getServerTargetApi();
  tagsData.value = data?.server_target_label || '';
}

onMounted(() => {
  getTableList();
  getServerTarget();
});
</script>

<style scoped lang="less">
.container {
  width: 60%;
  font-weight: 400;
  font-size: 14px;
  .desc {
    margin-bottom: 24px;
    color: rgba(6, 21, 51, 0.45);
    line-height: 22px;
    .link-text {
      color: #409eff;
      cursor: pointer;
    }
  }
  .catgroy-box {
    margin-bottom: 30px;
    padding: 16px 24px;
    background: #f6faff;
    border: 1px solid rgba(6, 21, 51, 0.04);
    border-radius: 4px 4px 4px 4px;
    .catgroy-title {
      margin-bottom: 14px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
      line-height: 18px;
    }
  }
  .industry-info {
    .title {
      margin-bottom: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 16px;
      .add-btn {
        margin-left: 16px;
      }
    }
    .title::before {
      position: relative;
      top: 2px;
      right: 5px;
      display: inline-block;
      width: 4px;
      height: 14px;
      background: var(--ant-primary-color);
      border-radius: 2px;
      content: '';
    }
    .info-list {
      margin: 0;
      padding: 16px;
      list-style: none;
      border: 1px solid rgba(6, 21, 51, 0.15);
      border-radius: 4px 4px 4px 4px;
      .success {
        color: #3777ff;
        background: #f6faff;
      }
      .processing {
        color: #faad14;
        background: #fffbf6;
      }
      .reject {
        color: #ff4d4f;
        background: #fff6f6;
      }
      .info-item {
        position: relative;
        z-index: 1;
        height: 84px;
        margin-bottom: 16px;
        padding: 16px;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        border: 1px solid rgba(6, 21, 51, 0.04);
        border-radius: 4px 4px 4px 4px;
      }
      .page-bottom {
        right: 0;
        bottom: 0;
        padding: 0;
        text-align: right;
        background-color: #fff;
        :deep(.ant-pagination-total-text) {
          font-size: 14px;
          line-height: 32px;
        }
      }
    }
    .empty-box {
      padding: 24px 0;
    }
  }
}
</style>
