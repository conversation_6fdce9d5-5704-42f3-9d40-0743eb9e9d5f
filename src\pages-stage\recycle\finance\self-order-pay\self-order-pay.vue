<template>
  <layout-shops-page :navs="['趣回收', '财务管理', '个人对账单']">
    <template #custom-box>
      <div style="width: 544px; margin: 0 24px 24px 24px">
        <a-spin :spinning="loading">
          <DataStatisticsBoard :info="dataStatisticsInfo" />
        </a-spin>
      </div>
    </template>
    <div class="container">
      <div class="search-bar">
        <a-form
          ref="formRef"
          class="form-wrap"
          layout="inline"
          :model="formState"
          style="width: 100%"
        >
          <a-form-item
            label="订单号"
            name="order_id"
          >
            <a-input
              v-model:value="formState.order_id"
              placeholder="请输入订单号"
            />
          </a-form-item>
          <a-form-item
            label="账单日期"
            name="date"
          >
            <a-range-picker
              v-model:value="formState.date"
              type="DatePicker"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
          <a-form-item
            label="结算日期"
            name="settleDate"
          >
            <a-range-picker
              v-model:value="formState.settleDate"
              type="DatePicker"
            />
          </a-form-item>
          <a-form-item
            label="状态"
            name="status_list"
          >
            <a-select
              v-model:value="formState.status_list"
              mode="multiple"
              :options="statusOptions"
              placeholder="请选择"
              style="width: 214px"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              style="border-radius: 4px"
              type="primary"
              @click="onHandleControl().search"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              搜索
            </a-button>
            <a-button
              style="margin-left: 8px; border-radius: 4px"
              @click="onHandleControl().reset"
            >
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
            <a-button
              style="margin-left: 8px; border-radius: 4px"
              type="primary"
              @click="down"
            >
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="main">
        <a-tabs
          v-model:activeKey="formState.status"
          @change="onLoadList"
        >
          <a-tab-pane
            key=""
            tab="全部账单"
          />
          <a-tab-pane
            key="0"
            tab="待结算账单"
          />
          <a-tab-pane
            key="1"
            tab="已结算账单"
          />
          <a-tab-pane
            key="11"
            tab="已关闭账单"
          />
        </a-tabs>

        <a-table
          class="list-table"
          :columns="columns"
          :data-source="tableList"
          :loading="isLoading"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.count,
            defaultPageSize:30,
            pageSizeOptions:['30'],
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total: number) => `共 ${total} 条数据 第${pagination.current}页`
          }"
          :scroll="{ x: 1580 }"
          @change="onChangePageSize"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'payment_account'">
              <div>
                姓名：{{ record.payment_name }}

                <DecryptField
                  v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                  :field-item="{
                    box: 'text',
                    id: record.order_id,
                    field: 'payment_name',
                    field_type: 'name',
                    type: 137,
                  }"
                />
              </div>
              <div>
                收账帐号：{{ record.payment_account }}

                <DecryptField
                  v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                  :field-item="{
                    box: 'text',
                    id: record.order_id,
                    field: 'payment_account',
                    field_type: 'phone',
                    type: 137,
                  }"
                />
              </div>
            </template>
            <template v-if="column.dataIndex === 'remark_number'">
              <span v-if="!Number(record.remark_number)">暂无备注</span>
              <a
                v-else
                style="color: #3777ff"
                type="link"
                @click="onHandleControl(record.id).seeRemark"
              >
                {{ record.remark_number }}条记录
              </a>
            </template>
            <template v-if="column.dataIndex === 'order_id'">
              <a
                style="color: #3777ff"
                @click="onHandleControl(record.id, record).jump"
              >{{ record.order_id }}</a>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-badge
                :status="STATUS_MAP[Number(record.status)]"
                :text="record.status_text"
              />
              <div v-if="Number(record.status) === 2">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>{{ record.remark }}</span>
                  </template>
                  <div class="status">
                    {{ record.remark }}
                  </div>
                </a-tooltip>
              </div>
            </template>
            <template v-if="column.dataIndex === 'created_at'">
              {{ makeMoment(record.created_at) }}
            </template>
            <template v-if="column.dataIndex === 'settlement_at'">
              {{ makeMoment(record.settlement_at) }}
            </template>
            <template v-if="column.name === 'action'">
              <div style="display: flex; flex-wrap: wrap">
                <a
                  v-if="['2'].includes(record.status)"
                  style="margin-right: 24px; color: #3777ff"
                  @click="onHandleControl(record.id).resettle"
                >
                  重新结算
                </a>
                <a
                  v-if="['0'].includes(record.status)"
                  style="margin-right: 24px; color: #3777ff"
                  @click="onHandleControl(record.id).changeStatus"
                >
                  结算
                </a>
                <a
                  style="margin-right: 24px; color: #3777ff"
                  @click="onHandleControl(record.id).remark"
                > 备注 </a>
                <a
                  v-if="
                    ['2'].includes(record.status) || (['0'].includes(record.status) && record.payment_account === '')
                  "
                  style="color: #ff4d4f"
                  @click="onHandleControl(record.id, record).edit"
                >
                  修改收款人信息
                </a>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <remark-modal
      v-if="remarkModalVisible"
      :id="currentId"
      v-model:visible="remarkModalVisible"
      @on-load-list="onLoadList"
    />

    <remark-list-modal
      v-if="remarkListModalVisible"
      :id="currentId"
      v-model:visible="remarkListModalVisible"
      @on-load-list="onLoadList"
    />
    <edit-modal
      v-if="editModalVisible"
      :id="currentId"
      v-model:visible="editModalVisible"
      :form-obj="currentRecord"
      @on-load-list="onLoadList"
    />
  </layout-shops-page>
</template>
<script setup lang="ts">
import { ref, reactive, createVNode } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayJS from 'dayjs';
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { FormInstance, Modal, message } from 'ant-design-vue';
import moment from 'moment';
import { clearObject } from '@/utils';
import { getTableList, settlement, settlementFail, exportList, payTry, getDataCount } from './service';
import { getFormFilter } from '../enterprise-order-pay/service';
import useExportModalConfirm from './use-export-modal-confirm';
import useTableList from '../../common/use-table-list';
import useBoolean from '../../common/use-boolean';
import RemarkModal from './components/remark-modal.vue';
import remarkListModal from './components/remark-list-modal.vue';
import EditModal from './components/edit-modal.vue';
import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
import { useViewPlaintextAuth } from '@/composables/use-decrypt-data';
const { viewPlaintextAuth } = useViewPlaintextAuth();
import DataStatisticsBoard from './components/data-statistics-board.vue';
import useDataStatistics from './composables/use-data-statistics';

const router = useRouter();
const columns: any[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 100,
  },
  {
    title: '流水号',
    dataIndex: 'trade_no',
    width: 160,
  },
  {
    title: '订单号',
    dataIndex: 'order_id',
    width: 180,
  },
  {
    title: '收款渠道',
    dataIndex: 'payment_type',
    width: 100,
  },
  {
    title: '收款人ID',
    dataIndex: 'user_id',
    width: 120,
  },
  {
    title: '收款人帐号',
    dataIndex: 'payment_account',
    width: 260,
  },

  {
    title: '金额类型',
    dataIndex: 'type',
    width: 140,
  },
  {
    title: '估价金额',
    dataIndex: 'evaluate_price',
    width: 120,
  },
  {
    title: '定价金额',
    dataIndex: 'purchased_price',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'remark_number',
    width: 120,
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    width: 220,
  },
  {
    title: '结算时间',
    dataIndex: 'settlement_at',
    width: 120,
  },
  {
    title: '账单生成时间',
    dataIndex: 'created_at',
    width: 120,
  },
  {
    title: '操作',
    name: 'action',
    fixed: 'right',
    width: 280,
  },
];

const STATUS_MAP = {
  0: 'default',
  1: 'success',
  2: 'error',
  3: 'default',
};

const route = useRoute();

const [remarkModalVisible, { setTrue: showRemarkModal }] = useBoolean();
const [remarkListModalVisible, { setTrue: showRemarkListModal }] = useBoolean();
const [editModalVisible, { setTrue: showeditModalVisiblel }] = useBoolean();

const currentId = ref('');
const currentRecord = ref({});

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  order_id: '',
  status: '',
  status_list: [],
  date: [],
  settleDate: [],
});

const statusOptions = ref([]);

const getOptions = async () => {
  const res = await getFormFilter();
  const data = res.data || {};
  const statusList = (data.status || []).map(item => {
    item.value = String(item.value);
    return item;
  });
  statusOptions.value = statusList;
};

const makeformat = (time: any[], format: string) => {
  if (time && time.length) {
    return [moment(time[0]).format(format), moment(time[1]).format(format)];
  } else {
    return ['', ''];
  }
};
const { down } = useExportModalConfirm(async () => {
  const res = await exportList(
    clearObject({
      order_id: formState.value.order_id,
      settlement_at_gt: makeformat(formState.value.settleDate, 'YYYY-MM-DD')[0],
      settlement_at_lt: makeformat(formState.value.settleDate, 'YYYY-MM-DD')[1],
      created_at_gt: makeformat(formState.value.date, 'YYYY-MM-DD')[0],
      created_at_lt: makeformat(formState.value.date, 'YYYY-MM-DD')[1],
      status_list: formState.value.status_list,
    }),
  );
  return res;
}, `${route.query.origin}/super/async-export/index`);
// 分页器
let pagination = reactive({
  current: 1,
  pageSize: 30,
  count: 0,
});
const makeMoment = (updated_at: any) => {
  if (updated_at && updated_at !== '0') {
    return moment(updated_at * 1000).format('YYYY-MM-DD HH:mm:ss');
  } else {
    return '/';
  }
};

const getUrlParams = () => {
  const query = route.query || {};
  const keysArr = Object.keys(query).filter(key => !['role', 'origin'].includes(key));
  if (keysArr.length) {
    keysArr.forEach(key => {
      const value = query[key];
      switch (key) {
        case 'startTime':
          formState.value.date[0] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'endTime':
          formState.value.date[1] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'statusList':
          formState.value.status_list = value.split(',');
          break;
        default:
          if (formState.value.hasOwnProperty(key)) {
            formState[key] = value;
          }
          break;
      }
    });
  }
};

const { info: dataStatisticsInfo, loading, init: requestDataStatistics } = useDataStatistics({
  request: getDataCount,
  title: '个人回收结算',
  tips: [
    '个人回收账单表内的全部账单',
    '应付金额：所筛选数据表中，全部待结算账单定价金额的总和',
    '已付金额：所筛选数据表中，全部已结算账单定价金额的总和',
  ],
});

const { onLoadList, tableList, isLoading } = useTableList({
  getTableList,
  payload: formState.value,
  pagination,
  isAutoInit: true,
  beforeInit: () => {
    if (route.query?.orderId) {
      // 处理路由跳转携带的参数
      formState.value.order_id = route.query?.orderId as string;
    }
    getUrlParams();
  },
  formatPayLoad: val => {
    const obj = {
      order_id: val.order_id,
      status: val.status,
      status_list: val.status_list,
      settlement_at_gt: makeformat(val.settleDate, 'YYYY-MM-DD')[0],
      settlement_at_lt: makeformat(val.settleDate, 'YYYY-MM-DD')[1],
      created_at_gt: makeformat(val.date, 'YYYY-MM-DD')[0],
      created_at_lt: makeformat(val.date, 'YYYY-MM-DD')[1],
      page: val.page,
      page_size: val.page_size,
    };
    requestDataStatistics(obj);
    return obj;
  },
});

const onChangePageSize = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  // 再次发起网络请求
  onLoadList();
};

const onHandleControl = (id?: string, obj?: any) => ({
  search() {
    pagination.current = 1;
    onLoadList();
  },
  reset() {
    // 1.重置所有字段
    formRef.value?.resetFields();
    // 2. 初始化页码
    pagination.current = 1;
    pagination.pageSize = 30;
    // 3. 重新获取table数据
    onLoadList();
  },
  changeStatus() {
    currentId.value = id as string;
    Modal.confirm({
      title: '是否切换至已结算?',
      content: createVNode(
        'div',
        {
          style: 'color: rgba(0,0,0,0.65);',
        },
        '切换状态后将不能恢复，确认切换结算状态吗？',
      ),
      onOk: async () => {
        const res = await settlement(currentId.value);

        if (res.data.status === 20006) {
          message.error(res.message);
        } else {
          message.success('结算成功');
        }

        onLoadList();
      },
    });
  },
  remark() {
    currentId.value = id as string;
    showRemarkModal();
  },
  seeRemark() {
    currentId.value = id as string;
    showRemarkListModal();
  },
  fail() {
    currentId.value = id as string;
    Modal.confirm({
      title: '是否切换至结算失败？',
      content: createVNode(
        'div',
        {
          style: 'color: rgba(0,0,0,0.65);',
        },
        '切换状态后将不能恢复，确认切换结算状态吗？',
      ),
      onOk() {
        return settlementFail(id as string).then(() => {
          message.success('已切换到结算失败');
          onLoadList();
        });
      },
    });
  },
  jump() {
    router.push({
      path: '/recycle/order/self-order-list',
      query: { order_id: obj.order_id },
    });
  },
  edit() {
    currentId.value = id as string;
    currentRecord.value = obj;
    showeditModalVisiblel();
  },
  resettle() {
    currentId.value = id as string;
    Modal.confirm({
      title: '确认重新结账吗？',
      content: '是否确认重新对此账单进行结账',
      onOk: async () => {
        const res = await payTry(currentId.value);

        if (res.data.status === 20006) {
          message.error(res.message);
        } else {
          message.success('结算成功');
        }
        onLoadList();
      },
    });
  },
});

getOptions();
</script>
<style scoped lang="less">
@import '../../common/base.less';
.list-table {
  padding-top: 14px;
}
.status {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.form-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 0;
}
</style>
