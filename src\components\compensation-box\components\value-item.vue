<template>
  <div class="value-box">
    <template v-if="isCustom">
      <a-input
        v-model:value="current.item"
        class="value-item"
        :maxlength="50"
        placeholder="请输入异常项"
      />
      <a-input
        v-model:value="current.desc"
        class="value-item"
        :maxlength="50"
        placeholder="请输入异常描述"
      />
    </template>
    <template v-else>
      <a-select
        v-model:value="current.item"
        class="value-item"
        :options="currentItemOptions"
        placeholder="请选择异常项"
        @change="clearValue(['desc', 'degree'])"
      />
      <a-select
        v-model:value="current.desc"
        class="value-item"
        :options="currentDescOptions"
        placeholder="请选择异常描述"
        @change="clearValue(['degree'])"
      />
      <a-select
        v-if="currentDegreeOptions.length > 0"
        v-model:value="current.degree"
        class="value-item"
        :options="currentDegreeOptions"
        placeholder="请选择异常程度"
      />
    </template>
  </div>
  <div class="value-box">
    <template v-if="isFromOrderList">
      <a-form-item
        class="value-item"
        label="金额计算方式:"
      >
        <a-radio-group
          v-model:value="current.money_calculation_type"
          :disabled="isEditStatus"
          @change="onRadioChange"
        >
          <a-radio :value="1">
            自定义
          </a-radio>
          <a-radio :value="2">
            按商品押金比
          </a-radio>
        </a-radio-group>
      </a-form-item>
    </template>
    <a-form-item
      class="value-item"
      label="赔付金额:"
    >
      <div class="money-line">
        <span v-if="showMoneyCompute">￥{{ damageExpansionInfo.total_deposit }} *</span>
        <a-input-number
          v-if="showMoneyCompute"
          v-model:value="current.ratio"
          :addon-after="addonAfter"
          :disabled="isDisabledMoney"
          :min="0"
          placeholder="请输入比例"
          :precision="2"
          style="flex: 1"
          @change="onRatioChange"
        />
        <a-input-number
          v-else
          v-model:value="current.money"
          :addon-after="isFromOrderList ? addonAfter : undefined"
          :disabled="isDisabledMoney"
          :min="0"
          placeholder="赔偿金额不得高于平台标准或单项维修费用"
          :precision="2"
          style="flex: 1"
        />
        <span v-if="showMoneyCompute && current.ratio">= {{ current.money }}</span>
      </div>
    </a-form-item>
  </div>
  <div
    v-if="showPlatformPay"
    class="value-box"
  >
    <a-form-item
      class="value-item"
      label="平台赔付:"
    >
      <a-input-number
        v-model:value="current.platform_pay_money"
        :disabled="isDisabledPlatformMoney"
        :max="!isFromOrderList ? current.money : undefined"
        :min="0"
        :precision="2"
        style="width: 100%"
        @change="onPlatformPayMoneyChange"
      />
    </a-form-item>
    <a-form-item
      v-if="isFromOrderList"
      class="value-item"
      label="用户赔付:"
    >
      <a-input-number
        v-model:value="current.user_pay_money"
        :disabled="isDisabledUserPayMoney"
        :max="!isFromOrderList ? current.money : undefined"
        :min="0"
        :precision="2"
        style="width: 100%"
        @change="onUserPayMoneyChange"
      />
    </a-form-item>
  </div>
  <div class="value-box">
    <a-form-item
      class="value-item"
      label="货损类型:"
    >
      <a-select
        v-model:value="current.damage_type"
        :options="damageTypeOptions"
        placeholder="请选择货损类型"
        style="width: 100%"
      />
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import floatCalculator from '@/utils/float-calculator';

import { damageTypeOptions } from '../config';
import type { TItemValueList, TItemValueListKeys, TListOptions } from '../data.d';
import {
  damageExpansionInfoSymbol,
  detailPropsSymbol,
  isCanEditAllSymbol,
  isCanEditPlatformSymbol,
  isEditStatusSymbol,
  isFromOrderListSymbol,
} from '../injection-symbols';
const props = defineProps<{
  currentValue: TItemValueList;
  options: TListOptions[];
  isCustom: boolean;
  allList: TItemValueList[];
  keyName?: string;
}>();
const isCanEditPlatform = inject(isCanEditPlatformSymbol, ref(false));
const isCanEditAll = inject(isCanEditAllSymbol, ref(false));
const isFromOrderList = inject(isFromOrderListSymbol, ref(false));
const isEditStatus = inject(isEditStatusSymbol, ref(false));
const damageExpansionInfo = inject(
  damageExpansionInfoSymbol,
  ref({
    total_deposit: '',
    rejection_reason: '',
    number: 0,
  }),
);
const detailProps = inject(detailPropsSymbol, ref(null));
const emits = defineEmits(['update:currentValue']);

const current = computed({
  get() {
    return props.currentValue;
  },
  set(val) {
    emits('update:currentValue', val);
  },
});

const showPlatformPay = computed(() => {
  if (isFromOrderList) {
    return isCanEditAll.value || isCanEditPlatform.value;
  }
  return (isCanEditAll.value || isCanEditPlatform.value) && current.value.money;
});

const currentItemOptions = computed(() => {
  if (props.isCustom) return [];
  return (
    props.options.map(item => {
      return {
        label: item.name,
        value: item.name,
        children: item.questions.map(child => {
          return {
            label: child.label,
            value: child.label,
            children: child.levels
              .filter(levelsItem => !!levelsItem.label)
              .map(levelsItem => {
                return {
                  label: levelsItem.label,
                  value: levelsItem.value,
                };
              }),
          };
        }),
      };
    }) || []
  );
});

function clearValue(keys: TItemValueListKeys[]) {
  keys.forEach(key => {
    current.value[key] = '';
    if (key === 'degree' && currentDegreeOptions.value.length === 0) {
      delete current.value.degree;
    }
  });
}

const currentDescOptions = computed(() => {
  if (props.isCustom) return [];
  return currentItemOptions.value?.find(item => item.label === current.value.item)?.children || [];
});

const currentDegreeOptions = computed(() => {
  if (props.isCustom) return [];
  return currentDescOptions.value?.find(item => item.label === current.value.desc)?.children || [];
});

const addonAfter = computed(() => {
  const { money_calculation_type } = current.value;
  if (isEditStatus.value) {
    return '元';
  }

  return money_calculation_type === 1 ? '元' : '%';
});

const showMoneyCompute = computed(() => {
  const { money_calculation_type } = current.value;

  return !isEditStatus.value && money_calculation_type === 2 && isFromOrderList.value;
});

const route = useRoute();
const isSuper = computed(() => route.query.role === 'super');

const isDisabledMoney = computed(() => {
  return isEditStatus.value && isFromOrderList.value;
});

const isDisabledPlatformMoney = computed(() => {
  return isEditStatus.value && !isSuper.value && isFromOrderList.value;
});

function onPlatformPayMoneyChange(e: number) {
  if (detailProps.value && isFromOrderList.value && isEditStatus.value) {
    const userPay = Number(current.value.user_pay_money) || 0;
    current.value.money = floatCalculator.add(e, userPay);
    if(isDisabledUserPayMoney.value){
      current.value.user_pay_money = floatCalculator.subtract(current.value.money,e);
    }
  }
}

function onUserPayMoneyChange(e: number) {
  if (detailProps.value && isFromOrderList.value && isEditStatus.value) {
    const platformPay = Number(current.value.platform_pay_money) || 0;
    current.value.money = floatCalculator.add(e, platformPay);
  }
}

function onRatioChange(e: number) {
  const totalDeposit = Number(damageExpansionInfo.value.total_deposit);
  const ratio = floatCalculator.divide(e, 100);
  const result = floatCalculator.multiply(totalDeposit, ratio);
  current.value.money = Number(result.toFixed(2));
}

function onRadioChange() {
  current.value.money = '';
  current.value.ratio = null;
}

const isDisabledUserPayMoney = computed(()=>{
  const flag = Number(detailProps.value.user_pay_status) === 1 && Number(detailProps.value.user_pay_money) === 0;
  return isEditStatus.value && isFromOrderList.value && flag && isSuper.value;
})

watch(
  () => current.value.money,
  () => {
    if (!isCanEditAll.value || isFromOrderList.value) return;
    if ((showPlatformPay.value && !current.value?.platform_pay_money) || !current.value.money) {
      current.value.platform_pay_money = 0;
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  flex-direction: row !important;
  gap: 8px;
  align-items: center;
  margin-bottom: 0;
}
:deep(.ant-form-item-label) {
  padding: 0;
}
:deep(.ant-row) {
  flex-flow: nowrap;
}

.value-box {
  display: flex;
  gap: 8px;
  justify-content: start;
  width: 100%;
  :deep(.ant-form-item-control-input-content) {
    width: 150px;
  }
  .value-item {
    flex: 1;
  }
}
.value-box:not(:last-child) {
  margin-bottom: 8px;
}

.money-line {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
