<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    title="确认结算"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form
      ref="formRef"
      :label-col="{
        style: { width: '82px' },
      }"
      :model="formState"
      style="width: 100%"
    >
      <a-form-item
        label="收款类型"
        name="payType"
        :rules="[{ required: true, message: '请选择收款类型!' }]"
      >
        <a-select
          v-model:value="formState.payType"
          :options="payTypeOptions"
          placeholder="请选择"
          style="width: 352px"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.payType === 1"
        label="流水号"
        name="tradeNo"
        :rules="[{ required: true, message: '请输入流水号!' }]"
      >
        <a-input-number
          v-model:value="formState.tradeNo"
          :controls="false"
          :maxlength="15"
          placeholder="请输入流水号"
          :precision="0"
          style="width: 352px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message, FormInstance } from 'ant-design-vue';
import { settlement, getBillPayFilter } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: Array,
    default: [],
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const submitLoading = ref(false);

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref<any>({
  payType: null,
  tradeNo: null,
});

const payTypeOptions = ref([]);

const onCancel = () => emits('update:visible', false);

const onOk = () => {
  formRef.value?.validate().then(() => {
    submitLoading.value = true;
    const params: any = {
      billNos: props.id.join(','),
      payType: formState.value.payType,
    };
    if (formState.value.payType === 1) {
      params.tradeNo = formState.value.tradeNo;
    }
    settlement(params)
      .then(() => {
        onCancel();
        message.success('操作成功');
        emits('onLoadList');
      })
      .finally(() => (submitLoading.value = false));
  });
};

const getPayTypeOptions = async () => {
  const res = await getBillPayFilter();
  const data = res.data || {};
  const options = data.traceNo || [];
  payTypeOptions.value = options;
};

getPayTypeOptions();
</script>
