## 组件使用指南

import type { ISFExpressTypeOption, IServeOption } from './data.d.ts'

- props

  参数：
  <!-- 配置收件地址 -->

  collectOptions: {label: string, value: any}[]
  <!-- 配置收件地址 -->

  addresseeInfo: { province: string | null; city: string | null; area: string | null; address: string | null}
  <!-- 配置服务类型、已选服务 -->

  useOptions: { useServiceType: () => Promise<ISFExpressTypeOption[]>; useServe: (options: IServeOption[]) => IServeOption[]; }
  <!-- 提交回调 -->

  onConfirm: (data) => Promise<any>

  示例：

  ```vue
  // 实际使用参考：【订单-订单管理-订单代发】
  <template>
    <ExpressMarkModal v-bind="obj" />
  </template>
  <script lang="ts" setup>
  const obj = {
    collectOptions: addressList,
    useOptions: {
      useServiceType: async () => {
        const countRes = (await getExpressDiscount({ order_id })).data || [];
        const expressRes = (await getSfExpressType(order_id)).data;
        const { option_list } = expressRes;
        countRes.forEach(item => {
          const matchingOptionIndex = option_list.findIndex(option => option.label === item.express_type_name);
          if (matchingOptionIndex !== -1) {
            option_list[matchingOptionIndex].tips = item.discount_value_label;
          }
        });
        return option_list;
      },
      useServe: options => {
        return options.filter(item => ['is_sign'].includes(item.value));
      },
    },
    addresseeInfo: {
      province: '广东省',
      city: '广州市',
      area: '白云区',
      address: '测试地址',
    },
    onConfirm: async res => {
      console.log('onConfirm---', res);
      return Promise.resolve();
      // return Promise.reject();
    },
  };
  </script>
  ```
