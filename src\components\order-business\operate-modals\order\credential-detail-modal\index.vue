<template>
  <a-modal
    v-model:visible="modalVisible"
    title="凭证"
    :width="453"
    @ok="handleConfirm"
  >
    <a-form
      ref="formRef"
      :model="formData"
    >
      <a-form-item
        label="凭证审核"
        name="status"
      >
        {{ ticketStatusTextMap[formData.status] }}
      </a-form-item>

      <a-form-item
        label="凭证图片"
        name="urls"
      >
        <div class="image-cover flex-wrap">
          <div
            v-for="item in formData.urls"
            :key="item"
            class="image-item flex-wrap flex-x-center flex-y-center"
          >
            <a-image
              class="image"
              :src="item"
            />
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { PropType, reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

import { useVModel } from '@/hook';
import { ETicketStatus } from '@/typing';

interface IExtraData {
  order_sort?: string;
  status?: number;
  ticket_url?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  /** 演唱会门票凭证 */
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});
const emits = defineEmits(['update:visible']);
const bindVisible = useVModel(props, 'visible', emits);

const modalVisible = ref(false);
/** 演唱会门票映射 */
const ticketStatusTextMap = {
  [ETicketStatus.WaitAudit]: '待审核',
  [ETicketStatus.AuditSuccess]: '通过',
  [ETicketStatus.AuditFail]: '不通过',
  [ETicketStatus.WaitUpload]: '待上传',
};
const formRef = ref();
const formData = reactive({
  status: '',
  urls: [],
});

function handleConfirm() {
  modalVisible.value = false;
}

watch(
  bindVisible,
  val => {
    if (val) {
      if (props.extraData.status === ETicketStatus.WaitUpload) {
        message.info('演唱会门票待上传');
      } else {
        modalVisible.value = true;
        formData.status = props.extraData.status;
        formData.urls = props.extraData.ticket_url ? JSON.parse(props.extraData.ticket_url) : [];
      }
      bindVisible.value = false;
    }
  },
  {
    immediate: true,
  },
);
</script>

<style scoped lang="less">
:deep(.ant-form-item) {
  .image-cover {
    flex-wrap: wrap;
    column-gap: 8px;

    .image-item {
      width: 104px;
      height: 104px;
      margin-bottom: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px 4px 4px 4px;

      .image {
        width: 86px;
        height: 86px;
        object-fit: cover;
        border-radius: 8px 8px 8px 8px;
      }
    }
  }
}

:deep(.ant-modal-body) {
  padding-bottom: 0;
}
</style>
