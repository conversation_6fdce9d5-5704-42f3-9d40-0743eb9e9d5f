<template>
  <div class="config-detail">
    <div
      v-for="(item, index) in applicableModelList"
      :key="index"
    >
      <a-collapse
        v-model:activeKey="activeKey"
        :bordered="false"
        collapsible="icon"
        ghost
      >
        <a-collapse-panel
          key="1"
          :show-arrow="false"
        >
          <template #header>
            <div
              class="flex-wrap flex-y-center"
              style="width: 100%"
            >
              <a-tooltip>
                <template #title>
                  {{ item.option_name }}
                </template>
                <span style="margin: 0 20px 0 8px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis">{{
                  item.option_name
                }}</span>
              </a-tooltip>
            </div>
          </template>
          <div class="option-item">
            <a-radio-group
              v-model:value="item.value"
              disabled
            >
              <ul class="ul-container">
                <li
                  v-for="(option, i) in item.options"
                  :key="i"
                  class="check-item"
                >
                  <a-radio :value="option.option_id" />
                  <a-tooltip>
                    <template #title>
                      {{ option.option_name }}
                    </template>
                    <label class="label-name">{{ option.option_name }}</label>
                  </a-tooltip>
                </li>
              </ul>
            </a-radio-group>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { PropType } from 'vue';
import type { IApplicableModelType, IOptionList, IOptionDict } from '../data';
const props = defineProps({
  optionsList: {
    type: Array as PropType<IOptionList[]>,
    default: () => [],
  },
  optionsDict: {
    type: Array as PropType<IOptionDict[]>,
    default: () => [],
  },
});

const activeKey = ['1']; //默认下拉状态

const optionsObject = {};
const applicableModelList = ref<IApplicableModelType[]>([]);

const handleDetail = () => {
  props.optionsList.forEach(item => {
    const { option_name, option_id } = item;
    optionsObject[option_name] = option_id;
  });
  applicableModelList.value = props.optionsDict
    .filter((item: any) => !['存储容量', '容量'].includes(item.option_name))
    .map((item: any) => {
      const options = item.options.map(op => ({ ...op, option_id: op.option_id.toString() }));
      return {
        option_name: item.option_name,
        options: options,
        value: optionsObject[item.option_name] || '',
      };
    });
};

onMounted(() => {
  handleDetail();
});
</script>
<style scoped lang="less">
.config-detail {
  height: 641px;
  overflow: hidden;
  overflow-y: auto;
}

.ant-radio-group,
.ant-checkbox-group {
  display: block;
}

.ul-container {
  margin: 0;
  padding-left: 34px;
  overflow: hidden;
  list-style: auto;
  border-left: 2px solid var(--ant-primary-color);
  .check-item {
    position: relative;
    height: 22px;
    padding-left: 2px;
    font-size: 14px;
    line-height: 22px;
    & + .check-item {
      margin-top: 12px;
    }
    .ant-checkbox-wrapper,
    .ant-radio-wrapper {
      position: absolute;
      top: 0;
      right: 0;
      justify-content: flex-end;
      width: 100px;
      margin-right: 16px;
    }
    &:has(.ant-checkbox-wrapper-checked),
    &:has(.ant-radio-wrapper-checked) {
      &::marker {
        color: var(--ant-primary-color);
      }
    }
    .ant-checkbox-wrapper-checked,
    .ant-radio-wrapper-checked {
      & + label {
        color: var(--ant-primary-color);
      }
    }
    &:has(.ant-checkbox-wrapper-disabled),
    &:has(.ant-radio-wrapper-disabled) {
      &::marker {
        color: rgba(6, 21, 51, 0.25);
      }
    }
    .ant-checkbox-wrapper-disabled,
    .ant-radio-wrapper-disabled {
      & + label {
        color: rgba(6, 21, 51, 0.25);
      }
    }
  }
}
.label-name {
  display: block;
  width: 200px;
  height: 50px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
