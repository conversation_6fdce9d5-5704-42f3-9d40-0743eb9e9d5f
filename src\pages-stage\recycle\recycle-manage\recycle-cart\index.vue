<template>
  <layout-shops-page
    :navs="['趣回收', '回收管理', '企业回收', '回收车']"
    title="回收车"
  >
    <template #title-prefix>
      <ArrowLeftOutlined
        style="margin-right: 16px; font-size: 16px"
        @click="onBack"
      />
    </template>
    <template #extra>
      <div class="flex-box">
        <a-alert
          banner
          message="现已支持非代发设备回收"
          style="height: 32px; background-color: #f0f7ff; border-radius: 2px"
          type="info"
        />
        <a-button
          type="primary"
          @click="onAdd"
        >
          <PlusOutlined />
          添加回收商品
        </a-button>
      </div>
    </template>
    <div class="main">
      <BaseForm v-model:value="formState" />
      <Empty v-if="!dataSource.length" />
      <a-table
        v-else
        class="sku-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="false"
        :row-class-name="rowClassName"
        row-key="carId"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, getCheckboxProps }"
        :scroll="{ x: '1144' }"
        :sticky="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'goodsName'">
            <div :class="['sku-info', { disabled: !!record.is_invalid }]">
              <img
                class="sku-info__img"
                :src="record.image"
              >
              {{ record.goodsName }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'sku_info'">
            <template
              v-for="(key, i) in Object.keys(record.sku_info)"
              :key="i"
            >
              <span
                v-if="!['condition', 'is_replace_device'].includes(key)"
                :class="['sku-info-tag', { primary: key === 'imei' }, { disabled: !!record.is_invalid }]"
              >
                {{ `${key === 'imei' ? 'IMEI码：' : ''}${record.sku_info[key]}` }}
              </span>
              <span
                v-else-if="key === 'is_replace_device'"
                :class="['sku-info-tag', { primary: key === 'is_replace_device' }, { disabled: !!record.is_invalid }]"
              >
                {{ record.sku_info[key] === 1 ? '代发设备' : '非代发设备' }}
              </span>
            </template>
          </template>
          <template v-if="column.dataIndex === 'evaluatePrice'">
            <span
              v-if="record.is_invalid"
              class="price-text invalid"
            >已失效</span>
            <span
              v-else
              class="price-text num"
            >{{ record.evaluatePrice }}</span>
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <a-button
              v-if="!record.is_invalid"
              class="handle-btn"
              type="link"
              @click="onEdit(record)"
            >
              编辑
            </a-button>
            <a-button
              class="handle-btn"
              danger
              type="text"
              @click="onDel(String(record.carId))"
            >
              删除
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
    <div class="page-bottom">
      <a-checkbox
        v-model:checked="isSelectAll"
        :indeterminate="indeterminate"
        @change="onSelectAll"
      >
        全选
      </a-checkbox>
      <div
        class="bottom-text"
        style="margin: 0 36px 0 24px"
      >
        已选<span style="margin: 0 4px; color: rgba(6, 21, 51, 0.85)">{{ selectedRowKeys.length }}</span>件商品
      </div>
      <span
        v-if="selectedRowKeys.length"
        class="bottom-text"
        style="cursor: pointer"
        @click="onDelSelectAll"
      >
        删除已选商品
      </span>
      <div class="bottom-settle-block">
        <span class="total-price">合计：￥{{ totalPrice }}</span>
        <a-button
          :class="{ 'disabled-btn': !selectedRowKeys.length }"
          :loading="submitLoading"
          style="width: 160px; height: 52px"
          type="primary"
          @click="() => selectedRowKeys.length && onSubmit()"
        >
          提交订单
        </a-button>
      </div>
    </div>
  </layout-shops-page>
  <AddCommodityModal
    ref="addCommodityModal"
    @init="listInit"
  />
  <AgreementModal ref="agreementModal" />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined, PlusOutlined } from '@ant-design/icons-vue';

import AddCommodityModal from './components/add-commodity-modal.vue';
import AgreementModal from './components/agreement-modal.vue';
import BaseForm from './components/base-form.vue';
import Empty from './components/empty.vue';
import { useHooks } from './composables/use-hooks';
import { columns } from './config';
import { IArgeementForm, IDataInfo } from './data';
import { submitCar } from './service';

const addCommodityModal = ref<any>(null);
const agreementModal = ref<any>(null);

const submitLoading = ref<boolean>(false);

const router = useRouter();
const onBack = () => {
  router.go(-1);
};

const {
  formState,
  dataSource,
  loading,

  selectedRowKeys,
  indeterminate,
  isSelectAll,
  onSelectChange,
  getCheckboxProps,
  onSelectAll,

  onDel,
  onDelSelectAll,
  rowClassName,

  totalPrice,

  listInit,

  validatorImei,
} = useHooks();

const onAdd = () => {
  addCommodityModal.value.onShow('add');
};

const onEdit = (item: IDataInfo) => {
  addCommodityModal.value.onShow('detail', item);
};

const onSubmit = async () => {
  const baseState = formState.value;
  const returnKeys = [
    'return_name',
    'return_phone',
    'return_province_name',
    'return_city_name',
    'return_area_name',
    'return_addr_detail',
  ];
  const hasEmpty = Object.keys(baseState).some((key: string) => {
    if (Array.isArray(baseState[key]) && !baseState[key].length) return true;
    if (baseState.use_send_info === 1) {
      if (!returnKeys.includes(key) && !baseState[key]) return true;
    } else {
      if (!baseState[key]) return true;
    }
  });
  if (hasEmpty || !Object.keys(baseState).length) {
    return message.error('请完善基础信息填写');
  }
  const phoneRegExp = /^1[3456789]\d{9}$/;
  if (!phoneRegExp.test(formState.value.phone)) {
    return message.error('【寄件人手机号】请填写有效格式');
  }
  if (baseState.use_send_info !== 1 && !phoneRegExp.test(formState.value.return_phone)) {
    return message.error('【收件人手机号】请填写有效格式');
  }
  const formRequest = async (modalParams: IArgeementForm) => {
    submitLoading.value = true;
    try {
      const result = await validatorImei();
      if (!result) {
        submitLoading.value = false;
        return;
      }
      const params = {
        ...baseState,
        bookTime: baseState.bookTime.join(' '),
        ids: selectedRowKeys.value,
        ...modalParams,
      };
      const res = await submitCar(params);
      message.success(res.message || '操作成功');
      listInit();
      selectedRowKeys.value = [];
    } finally {
      submitLoading.value = false;
    }
  };

  agreementModal.value.open(formRequest);
};
</script>

<style lang="less" scoped>
.main {
  box-sizing: border-box;
  min-height: calc(100vh - 112px);
  padding: 0 24px 88px 24px;
}
.flex-box {
  display: flex;
  gap: 8px;
  align-items: center;
}
.page-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 18px 24px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background: #fff;
  box-shadow: 0 5px 12px 4px rgba(6, 21, 51, 0.09), 0 3px 6px 0 rgba(6, 21, 51, 0.12),
    0 1px 2px -2px rgba(6, 21, 51, 0.16);
  .bottom-text {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  .bottom-settle-block {
    flex: 1;
    text-align: right;
    .total-price {
      margin-right: 24px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 20px;
      line-height: 28px;
    }
    .disabled-btn {
      cursor: no-drop;
      opacity: 0.5;
    }
  }
}
.sku-table {
  .sku-info {
    display: flex;
    align-items: center;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    &__img {
      display: inline-block;
      width: 52px;
      height: 52px;
      margin-right: 12px;
      overflow: hidden;
      border: 1px solid #e1e1e1;
      border-radius: 4px;
    }
    &.disabled {
      color: rgba(6, 21, 51, 0.25);
    }
  }
  .sku-info-tag {
    display: inline-block;
    margin: 0 8px 8px 0;
    padding: 0 8px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    background: #fff;
    border: 1px solid rgba(6, 21, 51, 0.15);
    border-radius: 2px;
    &.primary {
      color: #3777ff;
      background: #f0f7ff;
      border: 1px solid #b3d2ff;
    }
    &.disabled {
      color: rgba(6, 21, 51, 0.25);
      background: rgba(6, 21, 51, 0.04);
      border-color: rgba(6, 21, 51, 0.15);
    }
  }
  .price-text {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    &.invalid {
      color: rgba(6, 21, 51, 0.25);
      font-weight: 400;
    }
    &.num {
      color: rgba(6, 21, 51, 0.85);
    }
  }
  .handle-btn {
    &:nth-child(1) {
      padding-left: 0 !important;
    }
  }
}
:deep(.ant-table-row) {
  &.isError {
    .ant-table-cell {
      background: #fff1f0;
    }
    .sku-info {
      color: #ff4d4f;
      &__img {
        border-color: #ff4d4f;
      }
    }
    .sku-info-tag {
      color: #ff4d4f;
      background: #fff1f0;
      border-color: #ffa39e;
    }
    .price-text {
      color: #ff4d4f;
    }
  }
}
</style>
