<template>
  <div class="transport-detail">
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <a-card
        v-if="data.logistics"
        :bordered="false"
        class="card"
        title="物流信息"
      >
        <a-card-grid
          :hoverable="false"
          style="width: 50%"
        >
          物流单号：{{ data.logistics.waybill_no }}
          <a
            v-if="data.logistics.voucher"
            class="sign_order"
            :href="data.logistics.voucher"
            target="_blank"
          >签回单</a>
        </a-card-grid>
        <a-card-grid
          v-if="data.logistics.sender_info"
          :hoverable="false"
          style="width: 50%; text-align: center"
        >
          <div>取件信息：</div>
          <div style="text-align: left">
            <sensitive-field
              :id="data.id"
              field="name"
              field-type="2"
              :is-super-v2="isSuper"
              :post-type="isSuper ? 'super' : 'server'"
              real-field="sender_name"
              :row="data.logistics.sender_info"
              :type="654"
            />

            <sensitive-field
              :id="data.id"
              field="tel"
              field-type="1"
              :is-super-v2="isSuper"
              :post-type="isSuper ? 'super' : 'server'"
              real-field="sender_phone"
              :row="data.logistics.sender_info"
              style="padding-left: 8px"
              :type="654"
            />
            <div>
              {{ data.logistics.sender_info.province + data.logistics.sender_info.city }}
              <sensitive-field
                :id="data.id"
                field="detail"
                field-type="3"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="sender_address"
                :row="data.logistics.sender_info"
                :type="654"
              />
            </div>
          </div>
        </a-card-grid>
        <a-card-grid
          v-if="data.logistics.receive_info"
          :hoverable="false"
          style="width: 50%; text-align: center"
        >
          <div>寄送信息：</div>
          <div style="text-align: left">
            <sensitive-field
              :id="data.id"
              field="name"
              field-type="2"
              :is-super-v2="isSuper"
              :post-type="isSuper ? 'super' : 'server'"
              real-field="receive_name"
              :row="data.logistics.receive_info"
              :type="654"
            />

            <sensitive-field
              :id="data.id"
              field="tel"
              field-type="1"
              :is-super-v2="isSuper"
              :post-type="isSuper ? 'super' : 'server'"
              real-field="receive_phone"
              :row="data.logistics.receive_info"
              style="padding-left: 8px"
              :type="654"
            />
            <div>
              {{ data.logistics.receive_info.province + data.logistics.receive_info.city }}
              <sensitive-field
                :id="data.id"
                field="detail"
                field-type="3"
                :is-super-v2="isSuper"
                :post-type="isSuper ? 'super' : 'server'"
                real-field="receive_address"
                :row="data.logistics.receive_info"
                :type="654"
              />
            </div>
          </div>
        </a-card-grid>
        <a-card-grid
          v-if="data.logistics.imei"
          :hoverable="false"
          style="width: 50%"
        >
          寄送设备串码：{{ data.logistics.imei }}
        </a-card-grid>
        <a-card-grid
          v-if="data.logistics?.sf_express_type_desc"
          :hoverable="false"
          style="width: 50%; text-align: center"
        >
          服务类型：{{ data.logistics.sf_express_type_desc }}
        </a-card-grid>
      </a-card>
      <a-card
        v-if="data.service"
        :bordered="false"
        class="card"
        title="服务信息"
      >
        <a-card-grid
          v-if="data.service.book_time"
          :hoverable="false"
          style="width: 50%"
        >
          预约上门时间：{{ data.service.book_time }}
        </a-card-grid>
        <a-card-grid
          v-if="data.service.last_time"
          :hoverable="false"
          style="width: 50%"
        >
          实际上门时间：{{ data.service.last_time }}
        </a-card-grid>
        <a-card-grid
          :hoverable="false"
          style="width: 100%"
        >
          <a-table
            bordered
            :columns="service.columns"
            :data-source="data.service.list"
            :pagination="false"
          >
            <template #bodyCell="{ column, index }">
              <template v-if="column.key === 'project' && index === 0">
                <span>上门取件</span>
              </template>
            </template>
          </a-table>
        </a-card-grid>
      </a-card>
      <a-card
        v-if="data.bill_info"
        :bordered="false"
        class="card"
      >
        <template #title>
          账单明细 (以取件成功后的价格为准)
          <span class="title-icon">
            <InfoCircleOutlined
              :style="{
                color: 'rgba(250, 173, 20, 100)',
                fontSize: '13px',
                marginLeft: '5px',
              }"
            />
            顺丰卡航：以实际运费8折计算优惠价
          </span>
        </template>
        <a-card-grid
          :hoverable="false"
          style="width: 100%"
        >
          <a-table
            bordered
            :columns="billing.columns"
            :data-source="[data.bill_info]"
            :pagination="false"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.key === 'total' && Number(text) < 0">
                <span style="color: red">{{ text }}</span>
              </template>
              <template v-if="data.express_type == 6 && /^\d+\.?\d*$/.test(text)">
                <template v-if="column.key === 'sign_fee' || column.key === 'sign_paper_fee'">
                  <span style="color: red">{{ text }}</span>
                </template>
              </template>
              <template v-if="column.key === 'platform_substract'">
                <a-popover
                  v-if="record.discount_list?.length"
                  :get-popup-container="getPopoverNode"
                  placement="bottom"
                  trigger="click"
                >
                  <div
                    class="flex-wrap flex-y-center flex-x-justify"
                    style="cursor: pointer"
                  >
                    <span style="color: red">{{ text }}</span>
                    <DownOutlined style="font-size: 12px" />
                  </div>
                  <template #content>
                    <div
                      v-for="(discountItem, idx) in record.discount_list"
                      :key="idx"
                    >
                      <span>{{ discountItem.label }}：</span>
                      <span>{{ discountItem.value }}</span>
                    </div>
                  </template>
                </a-popover>

                <span
                  v-else
                  style="color: red"
                >{{ text }}</span>
              </template>
            </template>
          </a-table>
        </a-card-grid>
      </a-card>
      <a-card
        v-if="data.pay_info"
        :bordered="false"
        class="card"
        title="支付信息 (以取件成功后的价格为准)"
      >
        <a-card-grid
          :hoverable="false"
          style="width: 100%"
        >
          <a-table
            bordered
            :columns="pay.columns"
            :data-source="[data.pay_info]"
            :pagination="false"
          />
        </a-card-grid>
      </a-card>
      <div
        v-if="data.vary && data.vary.show"
        class="alert-box card"
      >
        <a-alert
          :message="`备注：异地转寄${data.vary.pay_way} / ${data.vary.state_desc} ${data.vary.total} 元 / 支付时间：${data.vary.pay_time}`"
          show-icon
          type="warning"
        />
      </div>

      <template v-if="!isSuper && expressDataKey === 'server_send'">
        <div
          v-show="isWaitSend && !data.service?.list[0].is_take"
          class="bar-code"
        >
          <img id="sfTransportBarcode">
          <div>快递员上门时，请出示此条形码</div>
        </div>
        <div class="footer-btn">
          <a-button
            :class="{ 'btns-warning': isWaitSend }"
            :disabled="!isWaitSend"
            @click="dispatch('book', orderId, data.service.list[0].is_take, data.id)"
          >
            重新预约取件
          </a-button>
          <a-button
            :class="{ 'btns-danger': isWaitSend }"
            :disabled="!isWaitSend"
            @click="dispatch('cancel', 'sf', orderId)"
          >
            取消寄件
          </a-button>
        </div>
      </template>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, onMounted, type PropType, watch } from 'vue';
import { useRoute } from 'vue-router';
import { DownOutlined,InfoCircleOutlined } from '@ant-design/icons-vue';
import JsBarcode from 'jsbarcode';
import { noop } from 'lodash-es';

import useBilling from './composables/use-billing';
import usePay from './composables/use-pay';
import useService from './composables/use-service';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default: () => {
      return {};
    },
  },
  type: {
    type: String as PropType<'sf' | 'jd'>,
    required: true,
  },
  orderId: {
    type: String,
    required: true,
  },
  orderStatus: {
    type: Number,
    required: true,
  },
  haveTips: {
    type: Boolean,
    default: false,
  },
  expressDataKey: {
    type: String,
    required: true,
  },
});

const dispatch = inject('dispatch', noop);

const service = useService(props);
const billing = useBilling(props);
const pay = usePay(props);
const isSuper = useRoute().query.role === 'super';

const data = computed(() => {
  return props.item.data || {};
});
const isWaitSend = computed(() => {
  return props.orderStatus === 2  && !props.haveTips/** 待发货 */;
});

function loadCode() {
  if (isWaitSend.value) {
    nextTick(() => {
      JsBarcode('#sfTransportBarcode',  props.item.waybill_no, {
        format: 'CODE128B',
      });
    });
  }
}

function getPopoverNode() {
  return document.querySelector('.transport-detail')
}

onMounted(() => {
  if (data.value) {
    loadCode();
  } else {
    watch(() => props.loading, loadCode);
  }
});
</script>

<style scoped lang="less">
.transport-detail {
  display: flex;
  flex-direction: column;
  align-items: center;

  .card + .card {
    margin-top: 12px;
  }

  .sign_order {
    margin-left: 5px;
  }

  :deep(.ant-spin-nested-loading) {
    width: 100%;
  }

  .alert-box {
    padding: 16px;
    background-color: #fff;
  }

  :deep(.ant-card-body) {
    display: flex;
    flex-wrap: wrap;

    .ant-card-grid {
      display: flex;
      align-items: center;
    }

    .ant-table-wrapper {
      width: 100%;
    }
  }
}

.title-icon {
  color: rgba(250, 173, 20, 100);
  font-weight: 400;
  font-size: 14px;
}

.bar-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 24px;
}

.footer-btn {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 10px;
}

.btns-warning {
  color: #fff;
  background-color: #eea236;
  border: none;
}

.btns-danger {
  color: #fff;
  background-color: #ff4d4f;
  border: none;
}
</style>
