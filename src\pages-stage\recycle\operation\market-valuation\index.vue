<template>
  <layout-admin-page
    class="flex-wrap flex-vertical"
    title="市场估价"
  >
    <template #extra>
      <a-button
        style="margin-right: 8px"
        type="primary"
        @click="handlVisibleMark"
      >
        <PlusOutlined />
        新增图表
      </a-button>
    </template>
    <div class="search-form">
      <a-form layout="inline">
        <a-form-item label="商品型号">
          <a-cascader
            v-model:value="state.breed"
            allow-clear
            change-on-select
            :load-data="spuDataListloadData"
            :max-tag-count="2"
            multiple
            :options="breedList"
            placeholder="请选择"
            style="width: 184px"
          />
        </a-form-item>
        <a-form-item>
          <a-button
            style="margin-right: 8px"
            type="primary"
            @click="handleSearch"
          >
            查询
          </a-button>
          <a-button @click="reset">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </div>
    <div class="line" />
    <component
      :is="currentComp"
      ref="allChartRef"
      v-model:breadCrumbList="breadCrumbList"
      :model-list="modelList"
    />
  </layout-admin-page>
  <AddMarkerDrawer
    id="0"
    v-model:visible="addVisible"
    type="create"
    @on-load-list="reset"
  />
</template>

<script setup lang="ts">
import { reactive, ref, watch, shallowRef, nextTick } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';

import { useCategory } from './composables/use-category';
import type { IState, IBreadCrumb } from './data';
import { EModelEvaluation } from './enums';

import AddMarkerDrawer from './components/add-marker-drawer.vue';
import AllChart from './components/all-chart.vue';
import ModelDetail from './components/model-detail.vue';
import InternalStorage from './components/internal-storage.vue';
import NewDegree from './components/new-degree.vue';

const state = reactive<Partial<IState>>({
  breed: [],
});

const { spuDataListloadData, breedList } = useCategory();

const compMap = {
  [EModelEvaluation.allChart]: AllChart,
  [EModelEvaluation.modelDetail]: ModelDetail,
  [EModelEvaluation.internalStorage]: InternalStorage,
  [EModelEvaluation.newDegree]: NewDegree,
};

const currentComp = shallowRef(compMap[EModelEvaluation.allChart]);

const breadCrumbList = ref<IBreadCrumb[]>([]);

const allChartRef = ref<any>(null);

const modelList = ref<string[]>([]);

const formatCategoryPrams = (data: number[][]) => {
  const category_id: number[] = [],
    brand_id: number[] = [],
    model_id: number[] = [];
  if (data.length > 0) {
    data.forEach(item => {
      const [c, b, m] = item;
      if (item.length === 1) {
        category_id.push(c);
      } else if (item.length === 2) {
        category_id.push(c);
        brand_id.push(b);
      } else if (item.length === 3) {
        category_id.push(c);
        brand_id.push(b);
        model_id.push(m);
      }
    });
  }
  return [[...new Set(category_id)], [...new Set(brand_id)], [...new Set(model_id)]].map(i => i.join(','));
};

const handleSearch = () => {
  modelList.value = [];
  const [category_id, brand_id, model_id] = formatCategoryPrams(state.breed as number[][]);
  modelList.value.push(category_id, brand_id, model_id);
  if (breadCrumbList.value.length === 0) {
    nextTick(() => {
      allChartRef.value?.pageInit();
      allChartRef.value?.fetchCharts();
    });
  } else {
    breadCrumbList.value = [];
  }
};

const reset = () => {
  state.breed = [];
  modelList.value = [];
  if (breadCrumbList.value.length === 0) {
    nextTick(() => {
      allChartRef.value?.pageInit();
      allChartRef.value?.fetchCharts();
    });
  } else {
    breadCrumbList.value = [];
  }
};

const addVisible = ref(false);

const handlVisibleMark = () => {
  addVisible.value = true;
};

//根据breadCrumbList切换组件
watch(
  () => breadCrumbList.value,
  newValue => {
    currentComp.value = compMap[newValue.length];
  },
);
</script>

<style scoped lang="less">
.block {
  width: 234px;
  padding-right: 12px;
}
:deep(.page-content) {
  display: flex;
  flex: 1;
  flex-direction: column;
}
.search-form {
  padding: 0 24px 24px 24px;
}

.line {
  height: 16px;
  background: #f0f2f5;
}
</style>
