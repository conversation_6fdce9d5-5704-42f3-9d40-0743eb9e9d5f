export interface IRiskReportData {
  base_info?: IBaseInfo;
  user_info?: IUserInfo[];
  result_info?: IResultInfo;
  log_info?: ILogInfo[];
  judicial_risk_info?: any[];
  platform_trade_info?: any[];
  evaluate?: string;
  isFace?: boolean;
}

interface IBaseInfo {
  id: string;
  version_id: string;
  title: string;
  business_type: string;
  category_type: string;
  goods_deposit_type: string;
  fee: string;
  server_id: any[];
  created_time: string;
}

interface IUserInfo {
  id: number;
  label: string;
  value: string;
  desc: string;
  type: number;
}

interface IRiskQualitative {
  id: number;
  label: string;
  value: string;
  desc: string;
  type: number;
  result: string;
}

interface IRiskResult {
  id: number;
  label: string;
  value: string;
  desc: string;
  type: number;
}

interface IRiskAdvise {
  id: number;
  label: string;
  value: string;
  desc: string;
  type: number;
}

interface IResultInfo {
  risk_qualitative: IRiskQualitative;
  risk_result: IRiskResult;
  risk_advise: IRiskAdvise;
}

interface ILogInfo {
  id: number;
  label: string;
  value: number;
  desc: string;
  type: number;
}
