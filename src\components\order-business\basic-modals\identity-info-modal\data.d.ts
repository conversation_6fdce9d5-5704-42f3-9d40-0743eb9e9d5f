export interface IIdentityItem {
  group: string;
  color: string;
  type?: number;
  isPermission: boolean;
  isShowMoreAndHide: number;
  prefix: string;
  isHide: number;
  hide?: boolean;
  name: string;
  text: string;
  suffix: string;
  value: string;
  values: IIdentityItem[] | IIdentityItem[][];
  goToCertAddressBtn: IVerifyAddressBtn;
}

export interface IVerifyAddressBtn {
  btn_text: string;
  form_data: {
    address: string;
    id_card: string;
    id_name: string;
    user_phone: string;
  };
}

export interface ILogItem {
  field_type: number;
  value: string;
}
