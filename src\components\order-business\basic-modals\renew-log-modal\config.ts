import type { TableColumnType } from 'ant-design-vue';

export const oldLogTableColumns: TableColumnType[] = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '次数',
    dataIndex: 'count',
    key: 'count',
  },
  {
    title: '租期',
    dataIndex: 'lease_term',
    key: 'lease_term',
  },
  {
    title: '续加租金',
    dataIndex: 'money',
    key: 'money',
  },
  {
    title: '旧订单结算',
    dataIndex: 'acc_rental',
    key: 'acc_rental',
  },
  {
    title: '续租时间',
    dataIndex: 'created_at',
    key: 'created_at',
  },
];

export const newLogTableColumns: TableColumnType[] = [
  {
    title: '期数',
    dataIndex: 'period_num',
    key: 'period_num',
  },
  {
    title: '续租单号',
    dataIndex: 'continue_log_id',
    key: 'continue_log_id',
  },
  {
    title: '租期',
    dataIndex: 'date_start',
    key: 'date_start',
  },
  {
    title: '账单金额',
    dataIndex: 'bill_money',
    key: 'bill_money',
  },
  {
    title: '续租费率',
    dataIndex: 'buyout_rate',
    key: 'buyout_rate',
  },
  {
    title: '购买价',
    dataIndex: 'buyout_price',
    key: 'buyout_price',
  },
  {
    title: '创建人',
    dataIndex: 'created_by',
    key: 'created_by',
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
  },
  {
    title: '同意人',
    dataIndex: 'agree_user',
    key: 'agree_user',
  },
  {
    title: '同意时间',
    dataIndex: 'agree_at',
    key: 'agree_at',
  },
  {
    title: '相关合同',
    dataIndex: 'protocol_url',
    key: 'protocol_url',
  },
];
