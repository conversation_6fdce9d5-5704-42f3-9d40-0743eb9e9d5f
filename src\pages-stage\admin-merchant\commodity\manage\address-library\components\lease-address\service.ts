import { GET, POST } from '@/services/api';

// 删除租赁地址
export function deleteLeaseAddress(params: {
  id: number | string;
  method: 'store.address.delete';
  address_id: number | string;
}): Promise<any> {
  return POST('/api/gateway', params);
}

// 查询单个租赁地址
export function findOneLeaseAddress(params: { id: number | string }): Promise<any> {
  return GET('/address/view', params);
}

// 设为默认地址
export function setAsDefault(params: { id: string }) {
  return POST('/address/default', params);
}

export function getAddressIndexInf(params: any): Promise<any> {
  return GET('/address/index-inf', params);
}
