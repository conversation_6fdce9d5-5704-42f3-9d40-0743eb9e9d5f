import { useModal } from '@/hook/component/use-modal';
import { h, reactive, ref } from 'vue';
import { IReduction } from '../data';
import { createReduciton, getGoodsName, getReducitonDetail, editReducitons } from '../service';
import { FormInstance, Modal, message } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { IAxiosResponse } from '@/services/api';

type TEmit = (event: string, ...args: unknown[]) => void | Promise<void>;

export default () => {
  const formState = reactive<Partial<IReduction>>({});
  const type = ref(1); // 1创建 2 编辑
  const currentId = ref('');

  const editReduction = (id: string) => {
    type.value = 2;
    currentId.value = id;
    open();
  };

  const searchByGoodsId = async (searchValue: string) => {
    try {
      loading.value = true;
      const { data } = await getGoodsName(searchValue);
      formState.goods_name = data.name;
    } finally {
      loading.value = false;
    }
  };

  const checkRepeat = ({ status, data, error, message: msg }: Partial<IAxiosResponse<{ id?: string }>>) => {
    if (status === 20003) {
      return Modal.confirm({
        title: h(
          'div',
          {
            style: {
              fontWeight: 'bold',
            },
          },
          error ?? '',
        ),
        content: h('div', null, [h('div', null, '“前往编辑”去编辑已经存在的降价趋势')]),
        icon: h(ExclamationCircleOutlined),
        okText: '前往编辑',
        cancelText: '',
        onOk: async () => {
          if (data && data?.id) {
            // 切换为编辑状态
            type.value = 2;
            currentId.value = data?.id;
            await getReduceDetail();
          }
        },
        width: 408,
      });
    } else {
      message.error(error || msg || '请求失败');
    }
  };

  const addReduction = () => {
    type.value = 1;
    open();
  };

  const { open, loading, visible, close, confirm } = useModal(
    async (formRef: FormInstance, emits: TEmit) => {
      await formRef.validate();
      try {
        const { status } =
          type.value === 1
            ? await createReduciton(formState, { closeErrorMessage: true })
            : await editReducitons({ ...formState, id: currentId.value }, { closeErrorMessage: true });
        if (status === 0) {
          message.success('操作成功');
          emits('reset-table');
          close();
        }
      } catch (error: any) {
        checkRepeat(error);
      }
    },
    {
      afterClose: () => {
        for (const key in formState) {
          formState[key] = undefined;
        }
      },
      afterOpen: async () => {
        if (type.value === 2) {
          await getReduceDetail();
        }
      },
    },
  );

  // 获取降价趋势详情
  const getReduceDetail = async () => {
    try {
      loading.value = true;
      const { data } = await getReducitonDetail({ id: currentId.value });
      Object.assign(formState, data);
    } finally {
      loading.value = false;
    }
  };

  return {
    open,
    loading,
    visible,
    close,
    confirm,
    editReduction,
    addReduction,
    type,
    formState,
    searchByGoodsId,
  };
};
