<template>
  <div class="customer-table">
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <table>
        <thead :class="['table-thead', tableTheadClass.join(' ')]">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :style="{ textAlign: column.align }"
            >
              {{ column.title }}
            </th>
          </tr>
        </thead>
        <tbody>
          <template
            v-for="(record, index) in dataSource"
            :key="index"
          >
            <tr>
              <td :colspan="columns.length">
                <div class="spacing" />
              </td>
            </tr>
            <tr class="row-header">
              <td :colspan="columns.length">
                <slot
                  :index="index"
                  name="rowHeader"
                  :record="record"
                />
              </td>
            </tr>
            <tr>
              <td
                v-for="column in columns"
                :key="column.key"
                :class="['td-box', column?.className ?? '']"
                :width="column.width"
              >
                <slot
                  :column="column"
                  :index="index"
                  name="bodyCell"
                  :record="record"
                  :text="record[column.key as string]"
                >
                  {{ record[column.key as string] || '' }}
                </slot>
              </td>
            </tr>
            <slot
              name="expandedRowRender"
              :record="record"
            />
          </template>
          <tr v-show="!dataSource.length">
            <td :colspan="columns.length">
              <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
            </td>
          </tr>
        </tbody>
      </table>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import type { TableColumnType } from 'ant-design-vue';

import { Empty } from 'ant-design-vue';

defineProps({
  dataSource: {
    type: Array as PropType<any[]>,
    default() {
      return [];
    },
  },
  columns: {
    type: Array as PropType<TableColumnType[]>,
    default() {
      return [];
    },
  },
  loading: {
    type: Boolean,
    default: false,
  },
  tableTheadClass: {
    type: Array,
    default: () => [],
  },
});
</script>

<style scoped lang="less">
.customer-table {
  padding: 0 24px 54px;
  table {
    width: 100%;
    padding: 16px;
    thead {
      th {
        position: relative;
        padding: 16px;
        color: #061533d9;
        font-weight: bold;
        white-space: nowrap;
        text-align: left;
        overflow-wrap: break-word;
        background: #f0f7ff;
        border-bottom: 1px solid #f0f0f0;
        transition: background 0.3s ease;
      }
    }
    tbody {
      border-bottom: 1px solid #f0f0f0;
      tr.row-header {
        background-color: #f5f7fa;
      }
      tr {
        border-top: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        border-left: 1px solid #f0f0f0;
      }
      td {
        vertical-align: top;
      }
      td + td {
        border-left: 1px solid #f0f0f0;
      }
    }

    .empty-data {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.spacing {
  position: relative;
  width: calc(100% + 4px);
  height: 24px;
  margin-left: -2px;
  background-color: #fff;
}

// thead悬浮样式
.table-thead.ceiling {
  position: fixed;
  top: 0;
  z-index: 10;
}
</style>
