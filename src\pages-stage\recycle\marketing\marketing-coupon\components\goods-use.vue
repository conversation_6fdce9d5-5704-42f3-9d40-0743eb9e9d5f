<template>
  <div class="goods-block">
    <div
      v-show="goodsTag.length"
      class="goods-tag-block"
    >
      <a-tag
        v-for="(item, index) in goodsTag"
        :key="item"
        :closable="!isEdit"
        style="margin: 0 8px 8px 0"
        @close="onDel(index)"
      >
        {{ item }}
      </a-tag>
    </div>
    <div
      v-if="!props.isEdit"
      class="goods-input-block"
    >
      <a-input
        v-model:value="inputValue"
        class="goods-input"
        placeholder="请输入商品ID"
        @blur="onSubmit('pressEnter')"
        @change="onSubmit('change')"
        @press-enter="onSubmit('pressEnter')"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { debounce } from '@/utils/base';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['onConfirm']);

const inputValue = ref<string>('');

const goodsTag = ref<string[]>([]);

const setGoodsTag = (arr: string[]) => {
  goodsTag.value = arr;
};

const onDel = (index: number) => {
  goodsTag.value.splice(index, 1);
};

const onReset = () => {
  inputValue.value = '';
  goodsTag.value = [];
};

const onSubmit = debounce((type: string) => {
  if (type === 'change' && !new RegExp(/,|，|\s/g).test(inputValue.value)) return;
  const inputArr = inputValue.value.split(/,|，|\s/g).filter(o => o);
  const hasNaN = inputArr.some(item => {
    return isNaN(Number(item));
  });
  if (hasNaN) {
    inputValue.value = '';
    return message.error('请输入有效的商品id');
  }
  inputValue.value = '';
  emits('onConfirm', inputArr, (list: string[]) => {
    goodsTag.value = list;
  });
}, 400);

defineExpose({
  setGoodsTag,
  onReset,
});
</script>

<style lang="less" scoped>
.goods-block {
  padding: 8px 12px 0 12px;
  background: #fff;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 4px;
  .goods-tag-block {
    display: flex;
    flex-wrap: wrap;
  }
  .goods-input-block {
    .goods-input {
      margin-bottom: 8px;
      padding: 0;
      border: none;
      &::placeholder {
        color: rgba(6, 21, 51, 0.25);
      }
      &:focus {
        outline: none;
        box-shadow: none;
      }
    }
  }
}
</style>
