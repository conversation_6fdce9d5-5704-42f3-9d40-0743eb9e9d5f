<template>
  <!--  引导使用弹窗  -->
  <TourGuideModal
    v-model:visible="deliveryGuideVisible"
    @close="initDeliverGoods"
  />
  <!-- 闪送预发起弹窗 -->
  <ShanSongModal
    ref="shanSongModalRef"
    @cancel="() => {
      onCancel();
      emit('refresh', orderId, ['data']);
    }"
    @success="
      () => {
        onCancel();
        emit('refresh', orderId, ['data']);
      }
    "
  />
  <!--  发货弹窗  -->
  <a-modal
    v-if="modalVisible"
    :body-style="{
      maxHeight: '800px',
      overflow: 'auto',
    }"
    class="delivery-store-modal"
    destroy-on-close
    style="top: 40px; width: 680px"
    title="去发货"
    :visible="modalVisible"
    @cancel="onCancel"
  >
    <div class="flex-wrap flex-vertical flex-gap-16">
      <!-- 下发指南 -->
      <Guide
        :is-platform-shipment="isPlatformShipment"
        :is-special-category="isSpecialCategory"
        :sku="deliveryStoreData"
      />
      <!-- 下发类型 -->
      <a-radio-group
        v-if="hasReplaceAuth"
        v-model:value="storeType"
        class="block-radio-group"
        option-type="button"
        :options="[
          { label: '平台代发', value: 2, disabled: platformDisabled },
          { label: '自行发货', value: 1, disabled: spontaneousDisabled },
        ]"
        @change="storeTypeChangeHandler"
      />

      <component
        :is="currentShipmentComponent"
        ref="shipmentRef"
        v-model:invoice="invoice"
        v-model:stockStatus="stockStatus"
        :order-id="orderId"
        :set-is-pick-mode="setIsPickMode"
        v-bind="{
          accessoryData,
          extraData,
          data: deliveryStoreData,
          serverId,
        }"
        @refresh="
          () => {
            onCancel();
            emit('refresh', orderId, ['data']);
          }
        "
      />
    </div>
    <template #footer>
      <template v-if="isPickUpMode">
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button
          :disabled="isSelfSpecialDis"
          :loading="submitBtnLoading"
          type="primary"
          @click="openPopConfirm"
        >
          确定
        </a-button>
      </template>
      <template v-else>
        <div class="flex-wrap flex-y-center">
          <div class="flex-con flex-wrap flex-x-start">
            <div
              v-show="isPlatformShipment"
              class="flex-wrap flex-y-center"
            >
              <a-checkbox v-model:checked="agreementChecked">
                <span>勾选即同意并遵守</span>
              </a-checkbox>
              <div
                class="flex-wrap flex-vertical"
                style="text-align: left"
              >
                <a
                  class="text-link"
                  href="https://img1.rrzuji.cn/cdn/doc/2508/13/689c05546d2fa.pdf"
                  target="_blank"
                >《供应链采购框架协议》</a>
                <a
                  class="text-link"
                  href="https://img1.rrzuji.cn/cdn/doc/2508/13/689c054675088.pdf"
                  target="_blank"
                >《产品采购居间合同》</a>
                <!-- <a
                  class="text-link"
                  href="https://img1.rrzuji.cn/cdn/doc/2502/12/67ac3ecd10f93.pdf"
                  target="_blank"
                >《一键代发服务协议》</a> -->
              </div>
            </div>
          </div>
          <div class="flex-wrap flex-gap-8">
            <a-button
              style="margin: 0"
              @click="onCancel"
            >
              取消
            </a-button>
            <a-button
              v-if="deliveryStoreData.sku_id && isHideExchange"
              style="margin: 0"
              @click="handleShowExchangeApplyModal"
            >
              申请换货
            </a-button>
            <!-- 该订单存在【缺货记录表】中 && 库存状态为“缺货” -->
            <a-button
              v-if="autoWarehouseText"
              danger
              style="margin: 0"
              type="text"
            >
              到货自动下发仓库
            </a-button>
            <!-- 下发仓库弹窗二次确认 -->
            <a-popover
              v-else
              placement="topLeft"
              title="温馨提示"
              trigger="click"
              :visible="tipPopoverVisible"
            >
              <template #content>
                <div style="width: 350px">
                  <div>{{ deliveryStoreData.proxy_project_notice_msg }}</div>
                  <div style="margin-top: 10px; text-align: right">
                    <a-button
                      style="margin-right: 15px"
                      @click="knowed"
                    >
                      知道了
                    </a-button>
                    <a-button
                      v-if="!isSuper"
                      type="primary"
                      @click="goToSign"
                    >
                      前往签署
                    </a-button>
                  </div>
                </div>
              </template>
              <a-popover
                placement="topLeft"
                :title="isPlatformShipment ? '下发仓库确认' : '发货确定'"
                trigger="click"
                :visible="popoverVisible"
              >
                <template #content>
                  <a-space direction="vertical">
                    <div>商品信息：{{ extraData.spu_name + ' ' + extraData.sku_name }}</div>
                    <div>货品信息：{{ deliveryStoreData.sku_info }}</div>
                    <!-- 【舆情】去发货弹窗送检逻辑调整 -->
                    <div
                      v-if="hasSelectVisible && false"
                      class="flex-block"
                    >
                      <div>请选择下发检测仓：</div>
                      <a-select
                        v-model:value="designated_warehouse_id"
                        :options="warehouseOptions"
                        placeholder="请选择"
                        style="width: 200px"
                      />
                    </div>
                    <a-space class="popover-button-area">
                      <a-button @click="popoverVisible = false">
                        取消
                      </a-button>
                      <a-button
                        :loading="submitBtnLoading"
                        type="primary"
                        @click="onIssueStock"
                      >
                        确定
                      </a-button>
                    </a-space>
                  </a-space>
                </template>
                <a-button
                  :disabled="submitBtnDisabled"
                  style="margin: 0"
                  type="primary"
                  @click="openPopover"
                >
                  {{ isPlatformShipment ? '下发仓库' : '确定' }}
                </a-button>
              </a-popover>
            </a-popover>
          </div>
        </div>
      </template>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, h, PropType, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useTopWindow } from '@/hook/common/use-top-window';

import { saveJD, saveSF } from '../../pickup-appoint-service-modal/pick-up-service-modal/services';
import type { IAccessoryData, IDeliverStore, IExtraData } from '../data.d';
import {
  autoToWarehouse,
  cancelSfPickUp,
  getOrderLimitIssued,
  getStoreSendWarehouseInfo,
  getWarehouseList,
  goToShip,
  issueStock,
  superGetSendWarehouseInfo,
} from '../services';
import Guide from './components/guide.vue';
import PlatformShipment from './components/platform-shipment/index.vue';
import SelfShipment from './components/self-shipment/index.vue';
import ShanSongModal from './components/shan-song-modal.vue';
import TourGuideModal from './components/tour-guide-modal.vue';

enum TipStatus {
  noTips = 1, // 可下发-不用提示
  tips = 2, // 可下发-需要提示
  unallowed = 3, // 不可下发
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  // 门店id
  serverId: {
    type: Number,
    default: 0,
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
  (e: 'showExchangeApplyModal'): void;
}>();

const route = useRoute();
const router = useRouter();
const isSuper = route.query.role === 'super';

const accessoryData = computed<IAccessoryData[]>(() => {
  const list: IAccessoryData[] = [
    { type: 1, list: [] },
    { type: 2, list: [] },
  ];
  // 根据accessory_type拆分两个分组
  if (Array.isArray(props.extraData.accessory_data)) {
    props.extraData.accessory_data.forEach(item => {
      const newData = {
        accessory_name: item.accessory_name,
        accessory_price: item.accessory_price,
        accessory_type: item.accessory_type,
        num: item.quantity,
      };
      if (newData.accessory_type === '1') {
        list[0].list.push(newData);
      } else if (newData.accessory_type === '2') {
        list[1].list.push(newData);
      }
    });
  }
  return list.filter(item => item.list.length);
});

const modalVisible = ref(false);
const deliveryStoreData = ref<IDeliverStore>({});
// 下发类型 1-自行发货 2-平台代发
const storeType = ref<1 | 2>(2);
// 是否可以代发
const hasReplaceAuth = ref(true);
// 订单是否在【缺货记录表】
const hasInAutoWarehouse = ref(false);

// 是否是取件发货服务
const isPickUpMode = ref(false);

function setIsPickMode(bool: boolean) {
  console.log('触发了==', bool);
  isPickUpMode.value = bool;
}

/** 初始化商品信息并打开弹窗 */
async function initDeliverGoods() {
  const api = isSuper ? superGetSendWarehouseInfo : getStoreSendWarehouseInfo;
  const { data } = await api(props.orderId);

  // =============================== 初始化商品数据 ===============================
  deliveryStoreData.value = {
    order_id: props.orderId,
    special_category: props.extraData.special_category,
    ...data,
  };
  modalVisible.value = true;
  // ================================ 初始化显示信息 ==============================
  const { order_id, replace_auth, is_new, is_checked } = deliveryStoreData.value;
  hasReplaceAuth.value = is_new || replace_auth || is_checked;
  if (platformDisabled.value) {
    storeType.value = 1;
  } else {
    storeType.value = hasReplaceAuth.value ? 2 : 1;
  }
  if (!order_id) return;
  const { data: autoWarehouseData } = await autoToWarehouse({ order_id });
  hasInAutoWarehouse.value = !!autoWarehouseData.is_auto;
  selfSendNotice();
}

const { navigate } = useTopWindow();

// 库存状态 1-缺货 2-充足
const stockStatus = ref(1);
// 票据
const invoice = ref(2);
// 是否平台代发
const isPlatformShipment = computed(() => storeType.value === 2);
const currentShipmentComponent = computed(() => (isPlatformShipment.value ? PlatformShipment : SelfShipment));
// 下发按钮状态
const submitBtnDisabled = computed(() => {
  //送检特判
  /**【舆情】去发货弹窗送检逻辑调整 */
  const sendSpecial = hasSelectVisible.value || stockStatus.value !== 2;
  console.log(
    'isPlatformShipment.value',
    isPlatformShipment.value,
    'sendSpecial',
    sendSpecial,
    'agreementChecked.value',
    agreementChecked.value,
    hasSelectVisible.value,
  );
  return isPlatformShipment.value ? sendSpecial || !agreementChecked.value : isSelfSpecialDis.value || false;
});
// 自行发货按钮置灰状态
const spontaneousDisabled = computed(() => {
  const { can_send_self, replace_auth, pdm_category_id, is_new } = deliveryStoreData.value;

  if (isSuper || can_send_self) return false;
  const isPhone = Number(pdm_category_id) === 34;
  //非二手手机
  if (!(isPhone && !is_new)) {
    if (!replace_auth) return false;
    if (isPlatformShipment.value) {
      const { rental, nonRental } = shipmentRef.value?.warehouseInformation || {};
      // 有代发权限 且 租赁服务设备 或者 非租赁服务设备 有库存
      const hasInventory = rental?.inventoryStatus || nonRental?.inventoryStatus;
      return deliveryStoreData.value.is_can_distribute && hasInventory;
    }
  }

  return true;
});

// 平台代发按钮置灰
const platformDisabled = computed(() => {
  const {device_source, is_can_distribute } = deliveryStoreData.value;
  return !is_can_distribute || (device_source && device_source === 2);
});


const submitBtnLoading = ref(false);
// 特殊品类(目前指游戏机)
const isSpecialCategory = computed(() => !!deliveryStoreData.value?.special_category);
// 是否勾选代发协议
const agreementChecked = ref(false);

// 下发按钮二级弹窗显示状态
const popoverVisible = ref(false);
// 闪送预发起弹窗
const shanSongModalRef = ref();

// 提示文案显示状态
const tipPopoverVisible = ref(false);

// 展示到货自动下发文案
const autoWarehouseText = computed(
  () => isPlatformShipment.value && stockStatus.value === 1 && hasInAutoWarehouse.value,
);

//有scm是企销且二手手机类目未绑码,限制自行发货置灰
const isSelfSpecialDis = computed(() => {
  const { scm_auth, system_make_up_order, is_scm_order, is_pengquan_order } = deliveryStoreData.value;
  const { imei_code_data } = props.extraData;
  if (isSuper || storeType.value !== 1 || system_make_up_order) return false;
  if (scm_auth && is_scm_order && !imei_code_data.length && !is_pengquan_order) {
    return true;
  }
  return false;
});

// 切换代发之后代发协议取消勾选
function storeTypeChangeHandler() {
  agreementChecked.value = false;
  //平台自发时商家端区取件发货服务为false 出现代发协议
  if (!isSuper && storeType.value === 2) {
    isPickUpMode.value = false;
  }
  //自发时出现
  if (isSelfSpecialDis.value) {
    message.warning({
      content: '当前订单需要先填写发货的设备串码后再进行发货。',
      class: 'message-class',
    });
  }
}

function onCancel() {
  modalVisible.value = false;
}

function openPopover() {
  const { proxy_project_status, is_white_server } = deliveryStoreData.value;
  if (!is_white_server || proxy_project_status === TipStatus.noTips) {
    openPopConfirm();
  } else if ([TipStatus.tips, TipStatus.unallowed].includes(proxy_project_status)) {
    tipPopoverVisible.value = true;
  }
}

function knowed() {
  tipPopoverVisible.value = false;
  if (deliveryStoreData.value.proxy_project_status === TipStatus.unallowed) return;
  openPopConfirm();
}

function goToSign() {
  if (import.meta.env.DEV) {
    router.push({
      path: '/application-introduction/merchant',
      query: {
        id: 35,
      },
    });
  } else {
    window.open(`${route.query.origin}/application-center/page`);
  }
}

function openPopConfirm() {
  if (isPlatformShipment.value) {
    popoverVisible.value = true;
  } else {
    shipmentRef.value?.validate().then((formData: any) => {
      // 同城闪送 并且 服务类型选了 闪送
      if (formData.express_id === 'SS' && formData.ss_express_type === 'shanSong') {
        shanSongModalRef.value.sendOrder(Number(props.orderId), props.serverId, formData);
        return;
      }

      // 还原参数走下面之前的接口
      if (formData.express_id === 'SS' && formData.ss_express_type === 263) {
        formData.sf_express_type = formData.ss_express_type;
        formData.express_id = 'SF';
      }

      if (isPickUpMode.value) {
        if (shipmentRef.value?.isWithdraw) {
          message.error('订单未获得用户代扣授权，暂时无法使用平台物流服务');
          return;
        }
        submitPickUp(formData);
      } else {
        popoverVisible.value = true;
      }
    });
  }
}

async function submitPickUp(formData: any) {
  submitBtnLoading.value = true;
  const url = formData.express_id === 'SF' ? saveSF : saveJD;
  const sendDate = formData.send_start_time[0];
  let sendStartTime = sendDate + ' ' + formData.send_start_time[1];
  if (formData.express_id === 'SF') {
    sendStartTime += ':00';
  }
  const params = {
    order_id: props.orderId,
    ...formData,
    is_sign_paper: formData.is_sign_paper ? 1 : 0,
    send_start_time: sendDate === 'door_anytime' ? sendDate : sendStartTime,
  };
  if (formData.express_id === 'SF' && deliveryStoreData.value.scm_auth && !isSuper) {
    params.scm_device_codes = formData.device_codes ? formData.device_codes.split(',') : undefined;
  }
  delete params.device_codes;
  url(params)
    .then(res => {
      if (res.status === 0) {
        message.success(res.message);
        onCancel();
        emit('refresh', props.orderId, ['data']);
      }
    })
    .finally(() => {
      submitBtnLoading.value = false;
    });
}

const shipmentRef = ref();
const designated_warehouse_id = ref();

async function onIssueStock() {
  const {
    data: { express_result },
  } = await getOrderLimitIssued({ order_id: props.orderId });
  const methodKey = isPlatformShipment.value ? 'getForm' : 'validate';
  shipmentRef.value?.[methodKey]?.().then(params => {
    submitBtnLoading.value = true;
    //商家去发货且有scm权限
    if (deliveryStoreData.value.scm_auth && !isSuper) {
      params.scm_device_codes = params.device_codes ? params.device_codes.split(',') : undefined;
    }
    if (deliveryStoreData.value.is_new_online_server) {
      params.invoice = invoice.value;
    }
    /**【舆情】去发货弹窗送检逻辑调整 */
    if (hasSelectVisible.value && false) {
      params.designated_warehouse_id = designated_warehouse_id.value;
    }
    // 一机一码信息
    if (deliveryStoreData.value.one_device_code) {
      params.one_device_code = deliveryStoreData.value.one_device_code;
      params.one_device_code_type = deliveryStoreData.value.one_device_code_type;
    }
    delete params.device_codes;
    const request = isPlatformShipment.value ? issueStock : goToShip;
    request(params)
      .then(async ({ data, auto_send_result }) => {
        const childList = [h('span', '下发仓库成功')];
        if (data?.deviceOrderIds?.length && !isSuper) {
          const url = '/shop-purchased-device-order/index?ids=' + data.deviceOrderIds.join(',');
          childList.push(
            h('span', '，设备价格将按预估价进行冻结，采购记录可前往“'),
            h('span', { class: 'text-link', underlined: true, onClick: () => navigate('blank', url) }, '设备订单'),
            h('span', '”进行查看'),
          );
        }
        message.success({
          content: h('span', childList),
          duration: 5,
        });
        if (express_result === 1) {
          await cancelSfPickUp({ order_id: props.orderId });
        }
        onCancel();
        emit('refresh', props.orderId, ['data', 'all_remark']);
        if (isPlatformShipment.value && deliveryStoreData.value.is_new_online_server) {
          emit('deliver-success', auto_send_result);
        }
      })
      .finally(() => {
        submitBtnLoading.value = false;
        popoverVisible.value = false;
      });
  });
}

const deliveryGuideVisible = ref(false);

const isHideExchange = computed(() => {
  return storeType.value !== 1 || isSuper;
});
const hasSelectVisible = computed(() => {
  return shipmentRef.value?.hasInspectTips;
});
const warehouseOptions = ref<any[]>([]);
const handleGetWarehouseList = async () => {
  const { data } = await getWarehouseList();
  warehouseOptions.value = data.map((item: any) => {
    return {
      label: item.warehouse_name,
      value: item.id,
    };
  });
};
const handleShowExchangeApplyModal = () => {
  if (deliveryStoreData.value.one_device_code) {
    message.warning('一机一码订单禁止换货');
    return;
  }
  emit('showExchangeApplyModal');
  onCancel();
};
function selfSendNotice() {
  if (!hasReplaceAuth.value && isSelfSpecialDis.value) {
    message.warning({
      content: '当前订单需要先填写发货的设备串码后再进行发货。',
      class: 'message-class',
    });
  }
}
watch(
  () => props.visible,
  value => {
    if (value) {
      handleGetWarehouseList();
      if (isSuper) {
        initDeliverGoods();
      } else {
        // 商家端判断是否需要引导使用
        localStorage.getItem('_NEW_DELIVERY_FORM') ? initDeliverGoods() : (deliveryGuideVisible.value = true);
      }
    }
    emit('update:visible', false);
  },
  { immediate: true },
);
</script>

<style lang="less" scoped>
.block-radio-group {
  display: flex;
  width: 100%;

  :deep(.ant-radio-button-wrapper) {
    flex: 1;
    height: auto;
    padding: 8px 15px;
    line-height: 22px;
    text-align: center;
  }
}

.popover-button-area {
  justify-content: end;
  width: 100%;
}
</style>
<style lang="less">
.message-class {
  margin-top: 100px;
}
</style>
