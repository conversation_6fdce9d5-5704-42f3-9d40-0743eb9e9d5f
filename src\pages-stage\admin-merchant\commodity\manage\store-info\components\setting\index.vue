<template>
  <a-spin :spinning="spinLoading">
    <a-space
      class="setting"
      direction="vertical"
      :size="16"
    >
      <div class="title-box">
        <div class="line" />
        <div class="title">
          店铺展示设置
        </div>
      </div>
      <a-space :size="8">
        是否开启公域投放
        <a-switch
          v-model:checked="isPublicDomainAd"
          checked-children="开"
          :checked-value="1"
          un-checked-children="关"
          :un-checked-value="2"
          @change="switchChange"
        />
      </a-space>
      <a-space
        class="question-box"
        :size="8"
      >
        <QuestionCircleOutlined class="question-icon" />
        <span>什么是公域投放</span>
      </a-space>
      <div class="tips">
        用户可在支付宝小程序、微信小程序、闲鱼小程序、钉钉小程序、APP等端口通过输入店铺名称搜索查找到店铺以及商品
      </div>

      <a-image src="https://img1.rrzuji.cn/uploads/scheme/2502/21/m/ACrkpw4FdR218PMFfHrj.png" />
    </a-space>
    <RModal
      v-model:open="modalOpen"
      @ok="handleModalOk"
    >
      <div class="description">
        是否关闭公域投放？关闭后用户将无法在支付宝小程序、微信小程序、闲鱼小程序、钉钉小程序、APP等端口通过搜索查找到店铺以及商品
      </div>
    </RModal>
  </a-spin>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { message } from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

import { getShopSetting, setShopSetting } from './service';

const props = defineProps({
  serverId: {
    type: String,
    default: '',
  },
});

const spinLoading = ref(false);

/** 公域投放状态是否开启 1: 开启 2: 关闭 */
const isPublicDomainAd = ref(1);

const modalOpen = ref(false);

const switchChange = async (checked: number) => {
  isPublicDomainAd.value = checked === 2 ? 1 : 2;
  // 如果是关闭，则先弹窗进行二次确认
  if (checked === 2) {
    modalOpen.value = true;
  } else {
    spinLoading.value = true;
    handleRequest(checked);
  }
};

const handleModalOk = async () => {
  handleRequest(2, false);
};

const handleRequest = (checked: number, open = false) => {
  setShopSetting({
    is_public_domain_ad: checked,
    server_id: props.serverId,
  })
    .then(() => {
      message.success('设置成功');
      isPublicDomainAd.value = checked;
      modalOpen.value = open;
    })
    .finally(() => {
      spinLoading.value = false;
    });
};

const getShopSettingValue = () => {
  spinLoading.value = true;
  getShopSetting({ server_id: props.serverId })
    .then((res: any) => {
      isPublicDomainAd.value = Number(res.data?.is_public_domain_ad);
    })
    .finally(() => {
      spinLoading.value = false;
    });
};

onMounted(() => {
  getShopSettingValue();
});
</script>

<style lang="less" scoped>
.setting {
  margin-top: 8px;
}
.title-box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .line {
    width: 4px;
    height: 14px;
    margin-right: 8px;
    background: #3777ff;
    border-radius: 2px 2px 2px 2px;
  }
  .title {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
  }
}

.question-box {
  :deep(.ant-space-item) {
    display: flex;
    align-items: center;
  }
  .question-icon {
    font-size: 16px;
  }
}

.tips {
  margin: -16px 0 -4px 16px;
}

.description {
  margin: 24px 16px;
  line-height: 2;
}
</style>
