import { defaultEnv, defaultTarget } from '@/../devHostConfig';
import { getCurrentServerConfig } from '@/services/format-url';

const isReal = document.location.host === 'admin-vue.rrzu.com';
const host = window.location.host.split('-')[0];
const localHost = defaultEnv + defaultTarget;
const { isLocal } = getCurrentServerConfig();
const origin = isReal ? 'https://www.rrzu.com' : `https://${isLocal ? localHost : host}-www.rrzuji.com`;

export async function ApiGetCityLibraryData() {
  const res = await fetch(`${origin}/address-library/get-province-city-data`);
  return res.json();
}

// 通过pid查找子节点
export async function ApiGetChildByPid(params: { pid: string }) {
  const res = await fetch(`${origin}/address-library/get-child-by-pid?pid=${params.pid}`);
  return res.json();
}
