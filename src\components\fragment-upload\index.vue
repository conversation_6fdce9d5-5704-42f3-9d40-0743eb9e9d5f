<template>
  <RUpload
    v-model:value="videoList"
    class="upload-box"
    :hide-entry="allShowPort ? uploadMaxCount + 1 : uploadMaxCount"
    :max-size="maxSize"
    :remove-confirm="true"
    :template="template"
    :upload-api="myCustomUpload"
    :upload-props="{
      maxCount: uploadMaxCount,
      accept: acceptInfo,
      disabled: !canEdit,
    }"
    value-type="file"
  >
    <div
      ref="uploadRef"
      class="upload-container"
    >
      <slot name="extend" />
      <p class="ant-upload-drag-icon">
        <component
          :is="uploadIcon"
          :style="{ 'font-size': uploadIconSize }"
        />
      </p>
      <p class="ant-upload-text">
        {{ showText[0] || '请上传' }}
      </p>
      <p
        v-if="showText[1]"
        class="ant-upload-hint"
      >
        {{ showText[1] }}
      </p>
    </div>
  </RUpload>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { PlusOutlined, VerifiedOutlined } from '@ant-design/icons-vue';
import { TUploadValue } from 'rrz-web-design';

import { multipartUploadToOss } from '@/utils/oss-upload';

import { fileTypeAcceptMap, fileTypeIconMap } from './config';
import type { TPropsType } from './data.d';
const props = withDefaults(defineProps<TPropsType>(), {
  data: () => [],
  maxCount: 6,
  maxSize: '200MB',
  canEdit: true,
  partSize: 1024 * 1024 * 40,
  parallel: 5,
  template: 'pictureCard',
  showText: () => ['上传'],
  allShowPort: false,
});
const uploadRef = ref();
const emits = defineEmits(['update:data']);
const acceptInfo = computed(() => {
  const temp: string[] = [];
  Object.keys(fileTypeAcceptMap).forEach(key => {
    temp.push(fileTypeAcceptMap[key as keyof typeof fileTypeAcceptMap]);
  });
  return props.accept || (props?.fileType && fileTypeAcceptMap[props?.fileType]) || temp.join(',');
});
const videoList = computed<TUploadValue>({
  get: () => props.data,
  set: val => {
    emits('update:data', val);
  },
});
const uploadMaxCount = computed(() => {
  return props.canEdit ? props.maxCount : 1;
});
const uploadIcon = computed(() => {
  if (props.fileType) {
    return fileTypeIconMap[props.fileType];
  }
  return props.template === 'pictureCard' ? PlusOutlined : VerifiedOutlined;
});
const uploadIconSize = computed(() => {
  return props.template === 'pictureCard' ? '14px' : '40px';
});
function myCustomUpload(params: any) {
  return multipartUploadToOss({
    ...params,
    parallel: props.parallel,
    partSize: props.partSize,
  });
}

function exposeClick() {
  uploadRef.value.click();
}

defineExpose({ exposeClick });
</script>
<style lang="less" scoped>
.upload-box {
  z-index: 9;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
