<template>
  <a-modal
    v-model:visible="bindVisible"
    class="review-modal"
    :mask-closable="false"
    title="审核"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="姓名"
          name="id_name"
        >
          <a-input
            v-model:value="formData.id_name"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          label="身份证号"
          name="id_card"
        >
          <a-input
            v-model:value="formData.id_card"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          label="手机号"
          name="user_phone"
        >
          <a-input
            v-model:value="formData.user_phone"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          label="地址"
          name="address"
        >
          <a-input
            v-model:value="formData.address"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue';
import { useVModel } from '@/hook';
import { FormInstance, message } from 'ant-design-vue';
import { verifyUserAddress } from './services';
import { decryptSensitiveInfo } from '@/utils/sensitive-info';

interface IFormData {
  uid?: number;
  id_name?: string;
  id_card?: string;
  user_phone?: string;
  address?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}>();

const formData = ref<IFormData>({});
const bindVisible = useVModel(props, 'visible', emit);
const rules = {
  id_name: [{ type: 'string', required: true, message: '姓名不能为空', trigger: 'blur' }],
  id_card: [{ type: 'string', required: true, message: '身份证号不能为空', trigger: 'blur' }],
  user_phone: [{ type: 'string', required: true, message: '手机号不能为空', trigger: 'blur' }],
  address: [{ type: 'string', required: true, message: '地址不能为空', trigger: 'blur' }],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loading.value = true;
      decryptSensitiveInfo({ order_id: props.orderId, sign: 17, type: 0 })
        .then(res => {
          if (res.data.receipt_info && res.data.user_info) {
            formData.value = {
              uid: res.data.user_info.user_id,
              id_name: res.data.user_info.username,
              id_card: res.data.user_info.idcard,
              user_phone: res.data.receipt_info.register_phone,
              address: res.data.receipt_info.desensitized_address,
            };
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  },
  { immediate: true },
);

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      type: 'antiFraud',
      ordinary_delivery: 1,
      ...formData.value,
    };
    verifyUserAddress(params)
      .then(() => {
        message.success('操作成功');
        bindVisible.value = false;
        emit('success');
      })
      .finally(() => (loading.value = false));
  });
}
</script>
