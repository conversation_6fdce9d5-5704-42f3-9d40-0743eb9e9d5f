<template>
  <a-modal
    :destroy-on-close="true"
    title="订阅"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-button
      style="margin-bottom: 16px"
      type="primary"
      @click="onhandleAdd"
    >
      <template #icon>
        <plus-outlined />
      </template>
      添加{{ messageSubscriptionTypeMapper[messageType].tips }}用户
    </a-button>
    <a-table
      :columns="messageSubscriptionTypeMapper[messageType].columns"
      :data-source="list"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <div>
            <a-popconfirm
              cancel-text="取消"
              ok-text="确定"
              title="你确定要删除这个联系人吗？"
              @confirm="onHandleDel(record.id)"
            >
              <a-button type="link">
                删除
              </a-button>
            </a-popconfirm>
          </div>
        </template>
      </template>
    </a-table>
  </a-modal>
  <component
    :is="messageSubscriptionTypeMapper[messageType].component"
    v-if="isModalVisivle"
    v-model:visible="isModalVisivle"
    :channel-id="props.channelId"
    :server-id="props.serverId"
    :wexin-qrcode="props.wexinQrcode"
    @get-close="getClose"
  />
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';

import { EMessageSubscriptionType } from '../../message-subscription/enums';
import { phoneColumns, smsColumns, weixinColumns } from '../config';
import { IBindContactList } from '../data';
import { channelContactDel, channelContactList } from '../service';
import AddSmsModal from './add-sms-modal.vue';
import AddWeixinModal from './add-weixin-modal.vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  channelId: {
    type: String,
    default: '',
  },
  messageType: {
    type: Number,
    default: 0,
  },
  serverId: {
    type: Number,
    default: 0,
  },
  wexinQrcode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible']);
const onCancel = () => emits('update:visible', false);

const messageSubscriptionTypeMapper = {
  [EMessageSubscriptionType.SmsCode]: {
    tips: '短信',
    columns: smsColumns,
    component: AddSmsModal,
  },
  [EMessageSubscriptionType.WeChat]: {
    tips: '微信',
    columns: weixinColumns,
    component: AddWeixinModal,
  },
  [EMessageSubscriptionType.Phone]: {
    tips: '电话',
    columns: phoneColumns,
    component: AddSmsModal,
  },
};
//获取列表
const list = ref<IBindContactList[]>();
const getContactList = () => {
  channelContactList(props.channelId).then(({ data }) => {
    list.value = data?.list;
  });
};

//删除
const onHandleDel = async (id: string) => {
  const res = await channelContactDel({ channel_id: props.channelId, contact_id: id });
  message.success(res.message || '操作成功');
  getContactList();
};

//弹窗-添加用户
const isModalVisivle = ref<boolean>(false);

const onhandleAdd = () => {
  isModalVisivle.value = true;
};

const getClose = () => {
  isModalVisivle.value = false;
  getContactList();
};
const handleOk = () => {
  onCancel();
};

onMounted(() => {
  getContactList();
});
</script>
