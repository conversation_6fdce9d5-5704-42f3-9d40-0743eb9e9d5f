<template>
  <layout-admin-page
    class="layout-page"
    :navs="['趣回收', '首页']"
    title="经营分析"
  >
    <flex-container
      :columns-number="2"
      :gap="16"
    >
      <!-- 今日销售 -->
      <day-sell-block />
      <!-- 用户情况 -->
      <user-data-block />
      <!-- 回收情况 -->
      <recycle-data-block />
      <!-- 库存情况 -->
      <inventory-data-block />
      <!-- 今日付款 -->
      <pay-data-block />
      <!-- 今日收款 -->
      <collect-data-block />
    </flex-container>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import FlexContainer from './components/flex-container.vue';
import DaySellBlock from './components/day-sell-block.vue';
import UserDataBlock from './components/user-data-block.vue';
import RecycleDataBlock from './components/recycle-data-block.vue';
import InventoryDataBlock from './components/inventory-data-block.vue';
import PayDataBlock from './components/pay-data-block.vue';
import CollectDataBlock from './components/collect-data-block.vue';
</script>

<style lang="less" scoped>
.layout-page {
  background: transparent;
  :deep(.page-top) {
    margin-bottom: 16px;
  }
  :deep(.page-content) {
    padding-bottom: 16px;
  }
}
</style>
