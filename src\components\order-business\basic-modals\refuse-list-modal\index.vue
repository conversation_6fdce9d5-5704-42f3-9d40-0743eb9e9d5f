<template>
  <!-- 拒单列表 -->
  <a-modal
    v-model:visible="bindVisible"
    :mask-closable="false"
    title="拒单列表"
    :width="700"
  >
    <a-table
      :columns="columns"
      :data-source="dataList"
      :loading="loading"
      :pagination="false"
    />
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { TableColumnType } from 'ant-design-vue';
import { getRefuseOrder } from './services';
import { useVModel } from '@/hook';

interface IRefuseOrderType {
  id: string;
  server_id: string;
  company: string;
  created_at: string;
}

const props = defineProps<{
  visible?: boolean;
  orderId: string;
}>();

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const dataList = ref<IRefuseOrderType[]>([]);
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  getRefuseOrder({ id: props.orderId })
    .then(res => {
      dataList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

const columns: TableColumnType[] = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '商家id',
    dataIndex: 'server_id',
    key: 'server_id',
  },
  {
    title: '店铺',
    dataIndex: 'company',
    key: 'company',
  },
  {
    title: '拒单时间',
    dataIndex: 'created_at',
    key: 'created_at',
  },
];
</script>
