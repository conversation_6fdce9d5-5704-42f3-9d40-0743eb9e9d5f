<template>
  <OldDeliverModal
    v-model:visible="visibleState.oldModalVisible"
    :extra-data="extraData"
    :order-id="orderId"
    @refresh="handleRefresh"
    @show-exchange-apply-modal="handleShowExchangeApplyModal"
  />
  <!-- 灰度版本(现已全量) -->
  <GrayscaleDeliverModal
    v-model:visible="visibleState.grayscaleModalVisible"
    :extra-data="extraData"
    :order-id="orderId"
    :server-id="serverId"
    @deliver-success="handleDeliverSuccess"
    @refresh="handleRefresh"
    @show-exchange-apply-modal="handleShowExchangeApplyModal"
  />
  <AutoDeliverySuccessModal
    v-model:visible="visibleState.autoSuccessVisible"
    :data="autoSendResultData"
    :title="autoSuccessTitle"
  />
</template>

<script lang="ts" setup>
import { h, nextTick, PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { MessageType } from 'ant-design-vue/lib/message';

import type { TRefreshDataKey } from '@/components/order-business/typing';

import reporter from '../../../composables/reporter';
import type { IExtraData } from './data';
import AutoDeliverySuccessModal from './grayscale-deliver-modal/components/auto-delivery-success-modal.vue';
import useAutoDelivery from './grayscale-deliver-modal/components/platform-shipment/composables/use-auto-delivery';
import GrayscaleDeliverModal from './grayscale-deliver-modal/index.vue';
import OldDeliverModal from './old-deliver-modal/index.vue';
import { isInnerMerchant } from './services';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  serverId: {
    type: Number,
    default: 0,
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
  (e: 'showExchangeApplyModal'): void;
}>();

function handleRefresh(orderId: string, dataKey?: TRefreshDataKey[]) {
  emit('refresh', props.orderId, dataKey);
}

function handleShowExchangeApplyModal() {
  emit('showExchangeApplyModal');
}

const route = useRoute();

const isSuper = route.query.role === 'super';
const closeLoading = ref<MessageType>();

const autoSendResultData = ref({}); //自动下发结果

const visibleState = ref({
  oldModalVisible: false,
  grayscaleModalVisible: false,
  autoSuccessVisible: false,
});
const autoSuccessTitle = ref('自动下发成功');

function handleDeliverSuccess(val: Record<string, any>) {
  autoSendResultData.value = val;
  autoSuccessTitle.value = '下发成功';
  visibleState.value.autoSuccessVisible = true;
}

watch(
  () => props.visible,
  value => {
    emit('update:visible', false); // 重新置false，以便反复触发弹窗打开判断
    if (value) {
      // 自选租期 & 提前三天
      if (props.extraData?.send_time_tip === 1) {
        Modal.confirm({
          title: '发货提醒',
          okText: '提前发货',
          cancelText: '暂不发货',
          content: () =>
            h('div', {}, [
              h('span', '该订单起租日为 '),
              h('span', { style: { color: 'red' }, innerHTML: props.extraData?.time_start }),
              h('span', ' 请商家和用户协商好发货日期，并至少保障在订单起租日前一天将商品按时送达至用户收货地址处！'),
            ]),
          onOk: () => {
            openModal();
          },
        });
      } else {
        openModal();
      }
    }
  },
  { immediate: true },
);

function openModal() {
  // 运营人员直接使用新下发弹窗
  if (isSuper) {
    visibleState.value.grayscaleModalVisible = true;
    return;
  }
  if (closeLoading.value) {
    return;
  }
  closeLoading.value = message.loading('加载中...');
  // 是否是内测商家，是使用新的下发弹窗
  isInnerMerchant()
    .then(async ({ data }) => {
      const { is } = data;
      if (is) {
        const { isAutoDelivery, getCheckAuth, autoSendResult } = useAutoDelivery();
        try {
          await getCheckAuth({ orderId: props.orderId, server_id: props.extraData.server_id });
          if (isAutoDelivery.value) {
            //自动下发成功弹窗
            autoSendResultData.value = autoSendResult.value;
            emit('refresh', props.orderId);
            nextTick(() => {
              visibleState.value.autoSuccessVisible = true;
            });
          } else {
            visibleState.value.grayscaleModalVisible = true;
          }
        } catch {
          visibleState.value.grayscaleModalVisible = true;
        }
      } else {
        visibleState.value.oldModalVisible = true;
      }

      reporter.setReportData('order_id', Number(props.orderId));
      reporter.setReportData('is_internal_server', data.is ? 1 : 0);
    })
    .catch(() => {
      visibleState.value.oldModalVisible = true;
    })
    .finally(() => {
      closeLoading.value?.();
      closeLoading.value = undefined;
    });
}
</script>
