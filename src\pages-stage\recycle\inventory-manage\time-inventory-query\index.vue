<template>
  <layout-admin-page
    :navs="['趣回收', '库存管理', '时点库存查询']"
    title="时点库存查询"
  >
    <div class="container">
      <SearchGroup
        v-model:value="searchInfo"
        @confirm="getTableList"
      />
      <a-table
        class="table-box bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: 1112 }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'type'">
            {{ record.type === '1' ? '汇总行' : '明细行' }}
          </template>
          <template v-if="['start_stock', 'end_stock', 'stock'].includes(column.dataIndex)">
            <span
              :class="{
                active: record[column.dataIndex] && Number(record[column.dataIndex]) != 0,
              }"
              @click="onOpenPage(column.dataIndex, record)"
            >{{ record[column.dataIndex] }}</span>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import dayjs from 'dayjs';
import { useTable } from '@/hook/component/use-table';
import { ISearchInfo } from './data.d';
import { getTimeQueryList } from './service';
import { columns } from './config';
import SearchGroup from './components/search-group.vue';

const defaultTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD');

const searchInfo = ref<ISearchInfo>({
  category_brand_model: [],
  data_type: '1',
  date: defaultTime,
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  api: getTimeQueryList,
  searchForm: searchInfo.value,
  totalKey: 'data.pageData.count',
  formatHandle: (res: any) => {
    return res.data.listData;
  },
  formatSearchValue: (params: ISearchInfo) => {
    const requestParams = { ...params };
    delete requestParams.category_brand_model;
    const [category_id, brand_id, model_id] = formatCategoryPrams(params.category_brand_model);
    return {
      ...requestParams,
      category_id,
      brand_id,
      model_id,
    };
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const formatCategoryPrams = (data: number[][]) => {
  const category_id: number[] = [],
    brand_id: number[] = [],
    model_id: number[] = [];
  if (data.length > 0) {
    data.forEach(item => {
      const [c, b, m] = item;
      if (item.length === 1) {
        category_id.push(c);
      } else if (item.length === 2) {
        category_id.push(c);
        brand_id.push(b);
      } else if (item.length === 3) {
        category_id.push(c);
        brand_id.push(b);
        model_id.push(m);
      }
    });
  }
  return [[...new Set(category_id)], [...new Set(brand_id)], [...new Set(model_id)]].map(i => i.join(','));
};

const onOpenPage = (key: string, record: any) => {
  const value = record[key];
  if (!value || Number(value) === 0) return;
  const pageUrl = {
    start_stock: '/super/recycle/order-device/index',
    end_stock: '/super/recycle/order-device/index',
    stock: '/super/recycle/data-count/warehouse-page',
  };
  const url = pageUrl[key];
  const params = {};

  if (['start_stock', 'end_stock'].includes(key)) {
    params['stock_count_id'] = record['id'];
    params['stock_type'] = key === 'start_stock' ? '1' : '2';
    params['data_type'] = searchInfo.value.data_type;
  }

  if (['start_stock', 'end_stock', 'stock'].includes(key)) {
    params['category_id'] = record['category_id'];
    params['brand_id'] = record['brand_id'];
    params['model_id'] = record['model_id'];
  }

  if (key === 'stock' && record.type === '2') params['sku_id'] = record['sku_id'];

  const urlParamsStr = Object.keys(params).reduce((pre, cur) => {
    return pre ? `${pre}&${cur}=${params[cur]}` : `?${cur}=${params[cur]}`;
  }, '');

  window.parent.postMessage(
    {
      action: 'blank',
      href: url + urlParamsStr,
    },
    '*',
  );
};

onMounted(() => {
  getTableList();
});
</script>
<style lang="less" scoped>
.container {
  padding: 2px 24px 26px 24px;
}
.table-box {
  margin-top: 24px;
}

.active {
  color: #3777ff;
  cursor: pointer;
}
</style>
