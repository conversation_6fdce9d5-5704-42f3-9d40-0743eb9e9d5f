<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    class="classified-modal"
    :footer="null"
    :style="[assessPriceDrawerVisible ? 'transform: translateX(-145px); transition: all .3s ease-out;' : '']"
    title="添加回收商品"
    :visible="visible"
    width="540px"
    @cancel="onCancel"
  >
    <div class="content">
      <a-form
        layout="inline"
        :model="formState"
        style="width: 100%"
      >
        <a-form-item
          label="搜索商品"
          name="name"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入商品名称"
          />
        </a-form-item>
        <a-button
          type="primary"
          @click="onLoadList"
        >
          查询
        </a-button>
      </a-form>
      <div style="padding: 12px 0; color: #ff4d4f">
        *红色商品代表该商品已添加
      </div>
      <a-table
        :columns="columns"
        :data-source="tableList"
        :loading="isLoading"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.count,
        }"
        @change="onChangePageSize"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.name === 'name'">
            <div :style="{ color: Number(record.bind_purchased_goods) === 1 ? '#FF4D4F' : '' }">
              {{ record.name }}
            </div>
          </template>
          <template v-if="column.name === 'action'">
            <a-button
              v-if="Number(record.bind_purchased_goods) !== 1"
              type="link"
              @click="() => onSelect(record.id, record.name)"
            >
              选择
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
  <assess-price-drawer
    v-if="assessPriceDrawerVisible"
    v-model:visible="assessPriceDrawerVisible"
    action-type="create"
    :current-commodity="currentCommodity"
    @on-cancel="onCancel"
    @on-load-list="onAppendDone"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { clearObject } from '@/utils';
import { getPurchasedGoodList } from '../service';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import AssessPriceDrawer from './assess-price-drawer.vue';

import useAsync from '@/hook/common/use-async';
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const columns: any[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '商品名称',
    dataIndex: 'name',
    name: 'name',
    width: 180,
  },
  {
    title: '执行动作',
    name: 'action',
    fixed: 'right',
    align: 'center',
    width: 90,
  },
];

const { run, dataList: tableList, isLoading } = useAsync();

const [assessPriceDrawerVisible, { setTrue: showAssessPriceDrawer }] = useBoolean();

const formState = ref({
  name: '',
});

const currentCommodity = ref({
  id: '',
  name: '',
});

// 分页器
let pagination = reactive({
  current: 1,
  pageSize: 10,
  count: 0,
});

const onLoadList = async () => {
  const page = pagination.current;
  const page_size = pagination.pageSize;
  await run(getPurchasedGoodList(clearObject({ page, page_size, name: formState.value.name })), {
    callback(data: any) {
      pagination.current = data.pageInfo.page;
      pagination.pageSize = data.pageInfo.pageSize;
      pagination.count = data.pageInfo.count;
      return data.list;
    },
  });
};

const onChangePageSize = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  // 再次发起网络请求
  onLoadList();
};

onMounted(onLoadList);

const onSelect = (id: string, name: string) => {
  currentCommodity.value = { id, name };
  showAssessPriceDrawer();
};

const onCancel = () => emits('update:visible', false);
const onAppendDone = () => {
  emits('onLoadList');
  onCancel();
};
</script>
<style scoped lang="less">
.classified-modal .ant-modal-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 16px;
}
</style>
