export const AuditStatus = props => {
  const { status, backarray, value } = props;
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div
        className="status"
        style={{ background: backarray[value], width: '8px', height: '8px', borderRadius: '50%', marginRight: '7px' }}
      ></div>
      <div>{status[value]}</div>
    </div>
  );
};
export const TableToolTips = title => {
  const tips = title.split('、');
  return (
    <a-tooltip
      placement="bottomLeft"
      title={tips.map(item => (
        <div>{item}</div>
      ))}
      trigger="click"
    >
      <div style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}> {title} </div>
    </a-tooltip>
  );
};
