<template>
  <a-drawer
    :body-style="{ padding: 0 }"
    :closable="false"
    destroy-on-close
    :title="type === 'edit' ? '编辑价格图表' : '新增价格图表'"
    :visible="visible"
    width="780px"
    @close="onClose"
  >
    <template #footer>
      <div class="flex-wrap flex-x-end">
        <a-button
          style="margin-right: 8px"
          @click="onClose"
        >
          取消
        </a-button>
        <a-button
          :loading="submitLoading"
          type="primary"
          @click="onConfirm"
        >
          确认
        </a-button>
      </div>
    </template>
    <div class="form-mian">
      <a-spin :spinning="fromLoading">
        <a-form
          ref="formRef"
          :model="state"
          :rules="rules"
        >
          <a-form-item
            label="商品类型"
            v-bind="validateInfos.breed"
            name="breed"
          >
            <a-cascader
              v-model:value="state.breed"
              allow-clear
              change-on-select
              :disabled="props.type === 'edit'"
              :field-names="{ label: 'title', value: 'id' }"
              :options="breedList"
              placeholder="请选择"
              @change="handleBreed"
            />
          </a-form-item>
          <a-form-item
            label="参考平台"
            v-bind="validateInfos.platform"
            name="platform"
          >
            <a-select
              v-model:value="state.platform"
              :disabled="props.type === 'edit'"
              :options="opionSelect.platform"
              placeholder="请选择参考平台"
              @change="handlePlatformChange"
            />
          </a-form-item>
          <a-form-item
            label="适用机型"
            v-bind="validateInfos.number_id"
            name="number_id"
          >
            <div class="select-mian">
              <a-select
                v-model:value="state.number_id"
                :disabled="props.type === 'edit'"
                :field-names="{
                  label: 'name',
                  value: 'number_id',
                }"
                :filter-option="filterOption"
                :options="modalNumberOptions"
                placeholder="请选择适用机型"
                show-search
              />
              <a-button
                :disabled="props.type === 'edit' || modelBtnDisabled"
                ghost
                type="primary"
                @click="handlGetOptions"
              >
                获取机型选项
              </a-button>
            </div>
          </a-form-item>
          <a-form-item
            label="存储内存"
            v-bind="validateInfos.memory_option_ids"
            name="memory_option_ids"
          >
            <a-select
              v-model:value="state.memory_option_ids"
              :disabled="props.type === 'edit'"
              :field-names="{ label: 'option_name', value: 'option_id' }"
              mode="multiple"
              :options="internalMemory[0]?.options"
              placeholder="请选择存储内存"
            />
          </a-form-item>
          <a-form-item
            label="成新度"
            name="new_ratio"
            v-bind="validateInfos.new_ratio"
          >
            <a-select
              v-model:value="state.new_ratio"
              :disabled="props.type === 'edit'"
              :options="opionSelect.new_ratio"
              placeholder="请选择成新度"
            />
          </a-form-item>
        </a-form>
      </a-spin>
      <a-divider />
      <div class="applicable-model">
        <a-row
          :gutter="24"
          style="margin-top: -12px; margin-bottom: -12px"
        >
          <a-col
            v-for="(item, index) in applicableModelList"
            :key="index"
            :span="12"
            style="margin-top: 12px; margin-bottom: 12px"
          >
            <a-collapse
              v-model:activeKey="activeKey"
              :bordered="false"
              collapsible="icon"
              ghost
            >
              <a-collapse-panel
                key="1"
                :show-arrow="false"
              >
                <template #header>
                  <div
                    class="flex-wrap flex-y-center"
                    style="width: 100%"
                  >
                    <a-tooltip>
                      <template #title>
                        {{ item.option_name }}
                      </template>
                      <span
                        style="margin: 0 20px 0 8px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
                      >{{ item.option_name }}</span>
                    </a-tooltip>
                  </div>
                </template>
                <div class="option-item">
                  <a-radio-group v-model:value="item.value">
                    <ul class="ul-container">
                      <li
                        v-for="(option, i) in item.options"
                        :key="i"
                        class="check-item"
                      >
                        <a-radio :value="option.option_id" />
                        <a-tooltip>
                          <template #title>
                            {{ option.option_name }}
                          </template>
                          <label class="label-name">{{ option.option_name }}</label>
                        </a-tooltip>
                      </li>
                    </ul>
                  </a-radio-group>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </a-col>
        </a-row>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { useVModel } from '@/hook';
import { ref, watch, onMounted } from 'vue';
import { Form, message } from 'ant-design-vue';
import {
  createNumberTask,
  numberTaskDetail,
  editNumberTask,
  getPlatNew,
  platformModelList,
  platformModelDetail,
  getBreedList,
} from '../service';

interface IApplicableModelType {
  option_name: string;
  value: string;
  options: any[];
}
interface IModalNumberOptions {
  name: string;
  number_id: string;
}
interface IStateType {
  title: string;
  breed: number[];
  platform?: number;
  number_id?: number;
  memory_option_ids?: number[];
  new_ratio?: string;
}

const opionSelect = ref({
  platform: [],
  new_ratio: [],
});

const applicableModelList = ref<IApplicableModelType[]>([]);
const internalMemory = ref([]);
const activeKey = ['1'];

const modalNumberOptions = ref<IModalNumberOptions[]>([]);
const props = defineProps<{ visible: boolean; id: string; type: string }>();
const emits = defineEmits(['update:visible', 'onLoadList']);
const visible = useVModel(props, 'visible', emits);
const submitLoading = ref<boolean>(false);
const fromLoading = ref<boolean>(false);
const formRef = ref();
const optionsMap = new Map();
const state = ref<IStateType>({
  title: '',
  breed: [],
});

const breedList = ref();
const getBreed = () => {
  getBreedList({}).then(res => {
    breedList.value = res.data.spuRelation?.children || [];
  });
};

//获取选中标题
const handleBreed = (_: any, selectedOptions: any[]) => {
  if (selectedOptions.length === 3) {
    state.value.title = selectedOptions[2].title;
  }
};

watch(
  () => visible.value,
  (value: boolean) => {
    if (value) {
      optionsMap.clear();
      if (props.id && props.type === 'edit') {
        handleNameTaskDetail(props.id);
      }
    }
  },
);
const onReset = () => {
  state.value = {};
  optionsMap.clear();
  applicableModelList.value = [];
  internalMemory.value = [];
  modalNumberOptions.value = [];
};
const onClose = () => {
  visible.value = false;
  onReset();
};
const onConfirm = async () => {
  await formRef.value.validate();
  const option_list = applicableModelList.value
    .filter((item: any) => item.value !== '')
    .map((item: any) => ({ option_name: item.option_name, option_id: item.value }));

  const { breed, number_id, ...rest } = state.value;
  const params = {
    ...rest,
    category_id: Number(breed[0]),
    brand_id: Number(breed[1]),
    model_id: Number(breed[2]),
    number_id: Number(number_id),
    memory_option_name: state.value.platform !== 3 ? '存储容量' : '容量',
    option_list,
  };
  if (props.id && props.type === 'edit') {
    await editNumberTask({ market_id: props.id, ...params });
    message.success('修改成功');
  } else {
    await createNumberTask(params);
    message.success('添加成功');
  }

  emits('onLoadList');
  visible.value = false;
  onReset();
};

const rules = {
  breed: [{ required: true, message: '请选择' }],
  platform: [{ required: true, message: '请选择参考平台' }],
  number_id: [{ required: true, message: '请选择适用机型' }],
  memory_option_ids: [{ required: true, message: '请选择存储类型' }],
  new_ratio: [{ required: true, message: '请选择' }],
};

const { validateInfos } = Form.useForm(state, rules);
const filterOption = (input: string, option: any) => {
  return option.name.indexOf(input) >= 0;
};

//获取机型选项
const modelBtnDisabled = ref(true);

const handlePlatformChange = async (e: number) => {
  const res = await platformModelList(e);
  modalNumberOptions.value = res.data;
  if (props.type === 'create') {
    state.value.number_id = undefined;
    applicableModelList.value = [];
  }
  modelBtnDisabled.value = false;
};

const handlGetOptions = async () => {
  if (props.type === 'create') {
    state.value.memory_option_ids = undefined;
  }
  if (state.value.number_id) {
    const res = await platformModelDetail(state.value.number_id);
    internalMemory.value = res.data.filter((item: any) => ['存储容量', '容量'].includes(item.option_name));
    applicableModelList.value = res.data
      .filter((item: any) => !['存储容量', '容量'].includes(item.option_name))
      .map((item: any) => {
        const options = item.options.map(op => ({ ...op, option_id: op.option_id.toString() }));
        return {
          option_name: item.option_name,
          options: options,
          value: optionsMap.get(item.option_name) || '',
        };
      });
  }
};
const handleNameTaskDetail = async (value: string) => {
  const res = await numberTaskDetail(value);
  const {
    category_id,
    brand_id,
    model_id,
    title,
    platform,
    number_id,
    memory_option_ids,
    option_list,
    new_ratio,
  } = res.data;
  fromLoading.value = true;
  state.value.breed = [Number(category_id), Number(brand_id), Number(model_id)];
  state.value.title = title;
  state.value.platform = Number(platform);
  state.value.number_id = number_id;
  state.value.memory_option_ids = memory_option_ids;
  state.value.new_ratio = new_ratio;
  option_list.forEach((item: any) => {
    const { option_name, option_id } = item;
    optionsMap.set(option_name, option_id);
  });
  handlePlatformChange(platform);
  handlGetOptions();
  fromLoading.value = false;
};

//获取平台和成新度
const getOptionList = async () => {
  const { data } = await getPlatNew();
  opionSelect.value.new_ratio = data.new_ratio.map((item: string) => ({ label: item, value: item }));
  opionSelect.value.platform = data.platform || [];
};

onMounted(() => {
  getOptionList();
  getBreed(); //获取机型
});
</script>

<style scoped lang="less">
.form-mian {
  padding: 24px;
}
.select-mian {
  display: flex;
}
.ant-radio-group,
.ant-checkbox-group {
  display: block;
}

.ul-container {
  margin: 0;
  padding-left: 34px;
  overflow: hidden;
  list-style: auto;
  border-left: 2px solid var(--ant-primary-color);
  .check-item {
    position: relative;
    height: 22px;
    padding-left: 2px;
    font-size: 14px;
    line-height: 22px;
    & + .check-item {
      margin-top: 12px;
    }
    .ant-checkbox-wrapper,
    .ant-radio-wrapper {
      position: absolute;
      top: 0;
      right: 0;
      justify-content: flex-end;
      width: 100px;
      margin-right: 16px;
    }
    &:has(.ant-checkbox-wrapper-checked),
    &:has(.ant-radio-wrapper-checked) {
      &::marker {
        color: var(--ant-primary-color);
      }
    }
    .ant-checkbox-wrapper-checked,
    .ant-radio-wrapper-checked {
      & + label {
        color: var(--ant-primary-color);
      }
    }
    &:has(.ant-checkbox-wrapper-disabled),
    &:has(.ant-radio-wrapper-disabled) {
      &::marker {
        color: rgba(6, 21, 51, 0.25);
      }
    }
    .ant-checkbox-wrapper-disabled,
    .ant-radio-wrapper-disabled {
      & + label {
        color: rgba(6, 21, 51, 0.25);
      }
    }
  }
}
.label-name {
  display: block;
  width: 200px;
  height: 50px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
