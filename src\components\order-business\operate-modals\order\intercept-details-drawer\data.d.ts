export interface IDetailInfo {
  id?: number;
  address?: string;
  apply_remark?: string;
  change_type?: number;
  created_at?: string;
  images?: string[];
  intercept_reason?: number;
  logistic_code?: string;
  name?: string;
  phone?: string;
  remark?: string;
  result?: number;
  status?: number;
  updated_at?: string;
}

export interface IBaseInfo {
  name: string;
  address: string;
  phone: string;
}
