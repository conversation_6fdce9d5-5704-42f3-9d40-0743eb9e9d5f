<template>
  <a-card
    :bordered="true"
    style="flex: 1; width: 0"
  >
    <div class="flex-justify">
      <div class="text">
        店铺资产情况
      </div>
      <span
        style="color: var(--ant-primary-color); font-size: 14px; cursor: pointer"
        @click="toAccountAsset"
      >
        <PropertySafetyFilled />
        店铺押金管理
      </span>
    </div>
    <div class="banner">
      <div class="rent">
        <div>租金账户(元)</div>
        <div class="count">
          {{ numInfo.rental_num }}
        </div>
      </div>
      <div
        class="custom-button"
        @click="toWithdraw"
      >
        <img
          alt=""
          class="img"
          src="https://img1.rrzuji.cn/uploads/scheme/2206/17/s/LVDEex0AnCSCLDMkHYNf.png"
        >
        <span style="margin-left: 4px">提现</span>
      </div>
    </div>
    <div class="card-wrapper">
      <div
        v-for="item in moneyNumInfo"
        :key="item.key"
        class="item"
        @click="toMoneyNum(item.url)"
      >
        <a-card
          :bordered="false"
          style="background: #f9f9fb; border-radius: 8px"
        >
          <div class="title">
            <div class="line" />
            <div>{{ item.title }}</div>
          </div>
          <div class="num">
            {{ item.value }}
          </div>
        </a-card>
      </div>
    </div>
    <div class="division-line" />
    <div class="detail">
      <div class="item-wrap">
        <div class="item">
          <div
            v-for="item in detailInfo"
            :key="item.title"
          >
            {{ item.title }}
          </div>
        </div>
        <div class="label">
          <a-space
            class="flex items-center pb-8"
            :size="5"
          >
            <div :class="['dot', Number(baseInfo.status) === 10 ? 'active' : 'default']" />
            <div>
              {{ Number(baseInfo.status) === 10 ? '开启' : '关闭' }}
              <!-- <setting-outlined /> -->
            </div>
          </a-space>
          <a-space
            class="flex items-center py-8"
            :size="5"
          >
            <div :class="['dot', Number(baseInfo.can_apply_choice) === 1 ? 'active' : 'default']" />
            {{ Number(baseInfo.can_apply_choice) === 1 ? '开启' : '关闭' }}
            <!-- <setting-outlined /> -->
          </a-space>
          <a-space
            class="flex items-center py-8"
            :size="5"
          >
            <div :class="['dot', Number(baseInfo.is_quality) === 1 ? 'active' : 'default']" />
            {{ Number(baseInfo.is_quality) === 1 ? '开启' : '关闭' }}
            <!-- <setting-outlined /> -->
          </a-space>
          <a-space
            class="flex items-center py-8"
            :size="5"
          >
            <div :class="['dot', Number(memberInfo.antChainServer) === 1 ? 'active' : 'default']" />
            {{ Number(memberInfo.antChainServer) === 1 ? '开启' : '关闭' }}
            <!-- <setting-outlined /> -->
          </a-space>
          <div class="py-8">
            ￥ {{ numInfo.activity_deposit }}
          </div>
          <div class="py-8">
            ￥ {{ numInfo.gua_num }}
            <EditOutlined @click="toAccountAsset" />
          </div>
          <div class="py-8">
            {{ numInfo.limit_scheme_num }}个
          </div>
          <div class="py-8">
            {{ numInfo.limit_address_num }}个
          </div>
          <div class="py-8">
            {{ memberInfo.service_name === '暂无' ? '暂无' : memberInfo.content }}
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>
<script lang="ts" setup>
// import { formatDate } from '@/utils/time';
import { computed, PropType } from 'vue';
import { useRoute } from 'vue-router';
import { EditOutlined, PropertySafetyFilled } from '@ant-design/icons-vue';

import { DataModal, IGoodsNumInfo } from '../types';
const props = defineProps({
  numInfo: {
    type: Object as PropType<DataModal>,
    default() {
      return {};
    },
  },
  goodsInfo: {
    type: Object as PropType<Partial<IGoodsNumInfo>>,
    default() {
      return {};
    },
  },
  serverCity: {
    type: Object,
    default() {
      return {};
    },
  },
  memberInfo: {
    type: Object,
    default() {
      return {};
    },
  },
  baseInfo: {
    type: Object,
    default() {
      return {};
    },
  },
});
const detailInfo = [
  {
    title: '旺铺状态：',
  },
  {
    title: '精选商城：',
  },
  {
    title: '商品严选：',
  },
  {
    title: '投保设置：',
  },
  {
    title: '活动押金：',
  },
  {
    title: '质保金：',
  },
  {
    title: '商品上限：',
  },
  {
    title: '地址上限：',
  },
  {
    title: '包含服务：',
  },
];

const moneyNumInfo = computed(() => [
  {
    title: '商品数量',
    key: 'scheme_num',
    url: '/scheme/index',
    value: props.numInfo.scheme_num,
  },
  {
    title: '地址数量',
    key: 'address_num',
    url: '/address/index',
    value: props.numInfo.address_num,
  },
  {
    title: '协议数量',
    key: 'baas_num',
    url: '/baas-server-singer/index',
    value: props.numInfo.baas_num,
  },
  {
    title: '分销商数量',
    key: 'code_merch_num',
    url: '/distributor/index',
    value: props.numInfo.code_merch_num,
  },
]);

const { query } = useRoute();
const { origin } = query;

// 租金提现
const toWithdraw = () => {
  const url = `${origin}/account/asset-index?withdraw=true`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};

const toAccountAsset = () => {
  const url = `${origin}/account/asset-index`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: url,
    },
    '*',
  );
};

const toMoneyNum = (url: string) => {
  const b_url = `${origin}${url}`;
  window.parent.postMessage(
    {
      action: 'jump',
      jump_url: b_url,
    },
    '*',
  );
};
</script>
<style scoped lang="less">
.flex-justify {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}
.banner {
  display: flex;
  align-items: center;
  height: 64px;
  margin-top: 24px;
  padding: 0 16px;
  color: #fff;
  background: var(--ant-primary-color);
  border-radius: 8px;
  .rent {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    .count {
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }
  }
}

.card-wrapper {
  display: flex;
  margin: 24px -4px;
  background-color: #fff;
  .item {
    flex-basis: 25%;
    padding: 0 4px;
    cursor: pointer;
    :deep(.ant-card-body) {
      padding: 8px;
    }
    .title {
      display: flex;
      align-items: center;
      overflow: hidden;
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      white-space: nowrap;

      .line {
        width: 4px;
        height: 12px;
        margin-right: 4px;
        background: var(--ant-primary-color);
        border-radius: 2px;
      }
    }
    .num {
      margin-left: 8px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }
  }
}

.open-svip {
  display: flex;
  align-items: center;
  padding: 12px 24px 12px 20px;
  background: var(--ant-primary-1);

  .svip-label {
    display: flex;
    align-items: center;
    .img {
      width: 42px;
      height: 48px;
    }
    .text {
      margin-left: 16px;
      color: var(--ant-primary-6);
      font-weight: 400;
      font-size: 12px;
    }
  }
  .button {
    margin-left: auto;
  }
}

.opened-svip {
  display: flex;
  align-items: center;
  padding: 8px 24px;
  background: linear-gradient(270deg, #fffdf4 0%, #fffbe6 100%);

  .img {
    width: 64px;
    height: 74px;
    margin-right: 24px;
  }

  .svip-info {
    flex: 1;
    .header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      .name {
        color: rgba(6, 21, 51, 0.85);
        font-weight: 400;
        font-size: 16px;
      }
      .link {
        color: #3777ff;
        font-weight: 400;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  .time {
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
  }
}
.division-line {
  width: 100%;
  height: 1px;
  margin: 24px 0;
  background: rgba(6, 21, 51, 0.06);
}
.detail {
  width: 100%;
  .item-wrap {
    display: flex;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 14px;
    .item {
      display: inline-block;
      flex-shrink: 0;
      width: 90px;
      div {
        &:first-child {
          padding: 0 0 8px 0;
        }
        padding: 8px 0;
      }
    }
    .label {
      flex: 1;
      overflow: hidden;
      color: rgba(6, 21, 51, 0.85);
      word-wrap: break-word;
      word-break: break-all;
      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        &.default {
          background: rgba(6, 21, 51, 0.15);
        }
        &.active {
          background: #52c41a;
        }
      }
    }
  }
}
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.pb-8 {
  padding-bottom: 8px;
}

.py-8 {
  padding: 8px 0;
}

.pl-5 {
  padding-left: 5;
}

.custom-button {
  display: flex;
  align-items: center;
  margin-left: auto;
  padding: 4px 15px;
  border: 1px solid #fff;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
  .img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
  }
}
</style>
