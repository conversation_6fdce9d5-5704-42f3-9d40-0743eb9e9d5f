<template>
  <a-modal
    v-model:visible="bindVisible"
    class="old-ship-modal"
    title="去发货"
    :width="650"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 4 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="订单号">
          <a-input
            disabled
            :value="props.orderId"
          />
        </a-form-item>
        <a-form-item
          label="快递公司"
          name="shipper_code"
        >
          <a-select
            v-model:value="formData.shipper_code"
            :allow-clear="true"
            :options="logisticOptions"
            placeholder="请选择"
          />
        </a-form-item>
        <a-form-item
          v-show="isNeedLogisticNum"
          label="快递单号"
          name="logistic_code"
        >
          <a-input
            v-model:value="formData.logistic_code"
            placeholder="运单号一般为10~14位数字"
          />
        </a-form-item>

        <a-form-item
          v-if="extraData.ms_loan_limit"
          label="手机序列号(IMEI码)"
          :label-col="{ span: 8 }"
          name="ms_loan_limit_imei"
          :wrapper-col="{ span: 16 }"
        >
          <a-input
            v-model:value="formData.ms_loan_limit_imei"
            placeholder="请输入手机序列号"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          v-if="extraData.ms_loan_limit"
          label="请上传手机包装盒正背面:"
          :label-col="{ span: 8 }"
          name="ms_loan_limit_image"
          :wrapper-col="{ span: 16 }"
        >
          <a-upload
            v-model:file-list="formData.ms_loan_limit_image"
            accept=".jpg, .jpeg, .png"
            :custom-request="(option: any) => customRequest(option)"
            list-type="picture-card"
            @preview="handlePreview"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">
                添加图片
              </div>
            </div>
          </a-upload>
        </a-form-item>

        <template v-if="extraData.offline_contract_show">
          <a-form-item
            label="线下租赁协议"
            name="is_support_offline_contract"
          >
            <a-select
              v-model:value="formData.is_support_offline_contract"
              :allow-clear="true"
              :options="[
                { label: '是', value: '1' },
                { label: '否', value: '0' },
              ]"
              placeholder="请选择"
            />
          </a-form-item>

          <a-form-item
            :colon="false"
            label=" "
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 16 }"
          >
            <div style="margin-bottom: 8px">
              请上传线下租赁协议(线下协议选‘是’时必填):
            </div>

            <a-upload
              v-model:file-list="formData.offline_contract_file[0].file_url"
              accept=".jpg, .jpeg, .png"
              :custom-request="(option: any) => customRequest(option,0)"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <div>
                <plus-outlined />
                <div style="margin-top: 8px">
                  添加图片
                </div>
              </div>
            </a-upload>
          </a-form-item>

          <a-form-item label="存证标题">
            <a-input
              v-model:file-list="formData.offline_contract_file[1].title"
              placeholder="请输入存证标题"
            />
          </a-form-item>
          <a-form-item
            :colon="false"
            label=" "
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 16 }"
          >
            <div style="margin-bottom: 8px">
              请上传用户手持证明等其他资料（选填）:
            </div>

            <a-upload
              v-model:file-list="formData.offline_contract_file[1].file_url"
              accept=".jpg, .jpeg, .png"
              :custom-request="(option: any) => customRequest(option,1)"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <div>
                <plus-outlined />
                <div style="margin-top: 8px">
                  添加图片
                </div>
              </div>
            </a-upload>
          </a-form-item>
        </template>

        <a-form-item
          label="发货图片"
          name="deliverImg"
        >
          <image-upload
            v-model:value="formData.image"
            action="site"
            class="image-upload"
            :max="4"
          />
          <div class="img-tips">
            说明：请上传此订单的高清实拍发货图片，最多支持上传4张图片
          </div>
        </a-form-item>
      </a-form>

      <!-- 配件清单 -->
      <div
        v-if="accessoryData.length > 0"
        class="accessories-container"
      >
        <div class="accessories-container-title">
          配件清单
        </div>
        <!-- 1是赠品 2是需归还 -->
        <div
          v-for="typeItem in accessoryData"
          :key="typeItem.type"
          class="accessories-box"
        >
          <div class="accessories-title flex-wrap flex-y-center">
            {{ typeItem.type === 1 ? '赠品' : '需归还' }}
            <span
              v-if="typeItem.type === 2"
              class="accessories-little-title"
            >(注：订单购买后，以下配件默认无需归还)</span>
          </div>
          <div class="accessories-content flex-wrap flex-y-center">
            <div
              v-for="item in typeItem.list"
              :key="item.accessory_type"
              class="content-item flex-wrap flex-y-center"
            >
              <div>{{ item.accessory_name }}</div>
              <div class="count">
                x{{ item.num }}
              </div>
              <div class="line" />
            </div>
          </div>
        </div>
      </div>

      <div class="alert-box">
        <a-alert type="warning">
          <template #message>
            <div style="color: #ff4d4f">
              <p>提示：【上门自取】与【上门安装】无需填写快递单号。</p>
              <p>
                请商家注意收集并录入凭证，<span style="font-weight: bold">如租户清晰身份证正反图片、纸质签收回单上须有租户签名、沟通记录等。</span>如有原件，注意保存原件。
              </p>
              <p>若用户为【上门自取/安装】，需让租户在回单上写明"上门自取/安装，本人确认已收到设备"。</p>
              <template v-if="extraData.offline_contract_show">
                <p>
                  线下租赁服务协议：用户与商家在线下签署的租赁协议，上传时需要完整且清晰地展现协议的所有内容<b>(若不上传，后续产生客诉，平台将以线上协议作为判断纠纷的依据)；</b>
                </p>
                <p>
                  其他补充资料：可以证明用户已签协议(例如手持合同拍照)、已提车(签字的提车证明)、已发货的凭证(寄给用户的物流单)等;
                </p>
                <p>如发生客诉，上传的补充资料可快速厘清责任，避免因凭证不足，产生的客诉拉扯，增加商家自身处理时间；</p>
              </template>
              <template v-if="extraData.ms_loan_limit">
                <p>
                  为了规避经营风险，需上传设备串码及手机包装盒正背面（人人租管家app扫一扫功能支持扫描设备条码自动识别）；
                </p>
                <p>注意：</p>
                <p>1、一个订单仅需要录入一台设备串码即可；</p>
                <p>2、全新手机上传手机包装盒正背面，二手手机上传手机正背面；</p>
              </template>
            </div>
          </template>
        </a-alert>
      </div>
    </a-spin>
  </a-modal>

  <a-modal
    :footer="null"
    :title="previewTitle"
    :visible="previewVisible"
    @cancel="handleCancel"
  >
    <img
      alt="example"
      :src="previewImage"
      style="width: 100%"
    >
  </a-modal>
</template>

<script setup lang="ts">
import { PropType, toRaw } from 'vue';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { FormInstance, message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';

import ImageUpload from '@/components/image-upload/image-upload.vue';
import { useLogisticOption } from '@/components/order-business/composables/use-logistic-option';
import { useVModel } from '@/hook';

import type { IAccessoryData, IExtraData, IOldShipForm } from '../../data';
import { getOperateMark, goToShip, uploadImg } from '../../services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh'): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);

/** 配件信息 */
const accessoryData = computed<IAccessoryData[]>(() => {
  const list: IAccessoryData[] = [
    { type: 1, list: [] },
    { type: 2, list: [] },
  ];
  // 根据accessory_type拆分两个分组
  if (Array.isArray(props.extraData.accessory_data)) {
    props.extraData.accessory_data.forEach(item => {
      const newData = {
        accessory_name: item.accessory_name,
        accessory_price: item.accessory_price,
        accessory_type: item.accessory_type,
        num: item.quantity,
      };
      if (newData.accessory_type === '1') {
        list[0].list.push(newData);
      } else if (newData.accessory_type === '2') {
        list[1].list.push(newData);
      }
    });
  }
  return list.filter(item => item.list.length);
});

const formRef = ref<FormInstance>(null);
// 快递公司选项
const { logisticOptions } = useLogisticOption();
const route = useRoute();

const formData = ref<IOldShipForm>({
  offline_contract_file: [
    {
      title: '线下租赁协议',
      file_url: [],
    },
    {
      title: '',
      file_url: [],
    },
  ],
  ms_loan_limit_image: [],
  is_support_offline_contract: '1',
  image: [],
});

// 是否需要填写物流单号
const isNeedLogisticNum = computed(() => {
  return formData.value.shipper_code !== -1 && formData.value.shipper_code !== -2;
});

const rules = {
  shipper_code: [{ required: true, message: '快递公司不能为空', trigger: 'blur' }],
  logistic_code: [
    {
      required: true,
      validator: (rule: any, value: string, callback: any) => {
        if (isNeedLogisticNum.value && !value)
          return callback(new Error('除了【上门自取】与【上门安装】都需要填写快递单号'));
        callback();
      },
      trigger: ['blur'],
    },
  ],
  ms_loan_limit_imei: [{ required: true, message: '手机序列号不能为空', trigger: 'blur' }],
};

const handleCancel = () => {
  previewVisible.value = false;
  previewTitle.value = '';
};

async function customRequest(option: any, type?: number) {
  const data = new FormData();
  data.append('uploadfile', option.file);
  data.append('upload_from', 'server_v3_order');
  await uploadImg(data)
    .then(res => {
      if (res.data.status === 1) {
        option.file.response = res.data;
        option.onSuccess(res.data.oss_url);
      } else {
        message.success(res.data.msg);
      }
    })
    .catch(() => {
      message.error('文件上传失败');
      if (type === 0 || type === 1) {
        formData.value.offline_contract_file[type].file_url.pop();
      } else {
        formData.value.ms_loan_limit_image.pop();
      }
    });
}

function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');
const handlePreview = async (file: any) => {
  if (!file.url && !file.preview) {
    file.preview = (await getBase64(file.originFileObj)) as string;
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};

const loading = ref(false);

function submit() {
  formRef.value?.validate().then(() => {
    const postForm = cloneDeep({
      ...formData.value,
      image: toRaw(formData.value.image) || [],
      server_delivery: route.name.includes('merchantShipmentsWorkbench') ? 1 : '',
    });
    postForm.ms_loan_limit_image &&
      (postForm.ms_loan_limit_image = postForm.ms_loan_limit_image.map(i => i.response).join(','));
    postForm.offline_contract_file[0].file_url &&
      (postForm.offline_contract_file[0].file_url = postForm.offline_contract_file[0].file_url.map(i => i.response));
    postForm.offline_contract_file[1].file_url &&
      (postForm.offline_contract_file[1].file_url = postForm.offline_contract_file[1].file_url.map(i => i.response));

    if (formData.value.is_support_offline_contract === '1' && props.extraData.offline_contract_show) {
      if (!formData.value.offline_contract_file[0].file_url.length) {
        return message.error('线下租赁合同文件不能为空');
      }
      if (
        formData.value.offline_contract_file[0].file_url.length &&
        !formData.value.offline_contract_file[1].file_url.length
      ) {
        postForm.offline_contract_file.pop();
      }
    } else {
      delete postForm.offline_contract_file;
    }

    !props.extraData.offline_contract_show && delete postForm.is_support_offline_contract;

    loading.value = true;
    goToShip({
      order_id: props.orderId,
      orderId: props.orderId,
      ordinary_delivery: 1,
      logistic_code: '',
      ...postForm,
    })
      .then(() => {
        bindVisible.value = false;
        emit('refresh');
        message.success('发货成功');
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

watch(
  () => props.visible,
  value => {
    // 埋点
    if (value) {
      if (route.name.includes('merchantShipmentsWorkbench')) {
        getOperateMark({ type: 4 });
      }
      formRef.value?.resetFields();
      formData.value = {
        offline_contract_file: [
          {
            title: '线下租赁协议',
            file_url: [],
          },
          {
            title: '',
            file_url: [],
          },
        ],
        ms_loan_limit_image: [],
        is_support_offline_contract: '1',
        image: [],
      };
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="less">
.accessories-container {
  min-height: 304px;
  margin-top: 24px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;

  .accessories-container-title {
    padding-bottom: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }

  .accessories-box {
    margin: 0 auto;
    border: 1px solid rgba(6, 21, 51, 0.15);
    border-radius: 6px;

    .accessories-title {
      height: 54px;
      padding-left: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      background: rgba(6, 21, 51, 0.06);
      border-radius: 6px 6px 0 0;

      .accessories-little-title {
        height: 22px;
        padding-left: 8px;
        color: rgba(6, 21, 51, 0.45);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }

    .gift-box {
      background-color: rgba(0, 200, 190, 0.15);
    }

    .accessories-content {
      flex-wrap: wrap;
      padding: 16px;
      padding-bottom: 12px;

      .content-item {
        height: 22px;
        margin-bottom: 4px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;

        .count {
          margin-left: 4px;
          color: rgba(6, 21, 51, 0.4 5);
        }
      }

      .line {
        width: 1px;
        height: 14px;
        margin: 0 8px;
        background: rgba(6, 21, 51, 0.15);
      }
    }
  }

  .accessories-box:not(:last-of-type) {
    margin-bottom: 16px;
  }
}

.img-tips {
  height: 22px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
</style>
