<template>
  <div class="add-commodity-item">
    <div class="add-commodity-item__label">
      {{ props.label }}
    </div>
    <div class="add-commodity-item__content">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  label: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.add-commodity-item {
  &__label {
    position: relative;
    margin-bottom: 16px;
    padding-left: 12px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    &::after {
      position: absolute;
      top: 4px;
      left: 0;
      width: 4px;
      height: 14px;
      background: #3777ff;
      border-radius: 2px;
      content: '';
    }
  }
}
</style>
