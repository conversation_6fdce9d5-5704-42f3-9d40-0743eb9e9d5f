<template>
  <div
    v-if="payData.pay_price"
    class="real-pay recycle-customer-table-flex-col"
  >
    <div
      class="pay-money"
      @click="toBillDetail"
    >
      ￥{{ payData.pay_price }}
    </div>
    <div class="refund">
      已退￥{{ payData.back_price }}
    </div>
    <div class="activity">
      （无优惠）
    </div>
    <div class="pay-way">
      {{ searchOptions.payWaysOptions.find(item => Number(item.value) === Number(payData.pay_type))?.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { IPayData } from '../../data';
import { searchOptions } from '../../config';
const props = defineProps({
  payData: {
    type: Object as PropType<IPayData>,
    default: () => ({}),
  },
});

// 跳账单详情
const toBillDetail = () => {
  window.parent.postMessage(
    {
      action: 'blank',
      href: `/super/recycle/sale-order-pay/index?rsp_id=${props.payData.rsp_order_id}`,
    },
    '*',
  );
};
</script>

<style lang="less" scoped>
.real-pay {
  align-items: center;
  justify-content: center;
  min-width: 152px;
  font-weight: 400;

  .pay-money {
    color: #00c8be;
    cursor: pointer;
  }
  .refund {
    color: #ff4d4f;
  }
  .activity {
    color: rgba(6, 21, 51, 0.85);
  }
  .pay-way {
    color: rgba(6, 21, 51, 0.45);
  }
  div {
    margin-bottom: 4px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
