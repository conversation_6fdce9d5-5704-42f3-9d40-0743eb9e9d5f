export interface IOrderList {
  baseData: IBaseData;
  commonHead: ICommonHead;
  goodsData: IGoodsData;
  priceData: IPriceData;
  payData: IPayData;
  customData: ICustomData;
  orderLogisticData: IOrderLogisticData;
}

export interface IBaseData {
  order_status: string;
  source_type: string;
}

export interface ICommonHead {
  rs_order_id: string;
  created_at: string;
}

export interface IGoodsData {
  goods_name: string;
  goods_main_image: string;
  sku_info: string;
  imei_list: IImeiList[];
}

export interface IPriceData {
  goods_price: string;
  goods_num: string;
  order_price: string;
  other_price: string;
  logistics_price: string;
  quality_check_price: string;
  lock_price: string;
  accessory_price: string;
  delivery_service_price: string;
}

export interface IPayData {
  pay_price: number;
  pay_type: number;
  rsp_order_id: number;
  back_price: number;
}

export interface ICustomData {
  receive_name: string;
  address: string;
  server_id: string;
  server_name: string;
  zl_order_id: string;
  custom_type: string;
  phone: string;
}
export interface IOrderLogisticData {
  logistic_name: string;
  logistic_code: string;
  receiver_mobile: string;
  shipper_code: string;
}
export interface IImeiList {
  imei: string;
  cost_price: string;
}

export interface ISearchParams {
  rs_order_id: string;
  category_brand_model: string;
  category: number[];
  order_status: string;
  server_id: string;
  custom_type: string;
  source_type: number[];
  phone: string;
  pay_type: string;
  logistic_code: string;
  start_at: string;
  end_at: string;
  create_time: string[];
}
// 备注
export type RemarkType = {
  created_at: string;
  last_remark: string;
  remark_by: string;
  remark_num: number;
  remark_tag: string;
  union_id: string;
};
