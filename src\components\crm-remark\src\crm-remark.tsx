import './index.less';

import { defineComponent, nextTick, onBeforeUnmount, ref } from 'vue';
import { message } from 'ant-design-vue';
import { throttle } from 'lodash-es';

import { GET, POST } from '@/services/api';

import { remarkProps } from './props';
import { RemarkItem } from './typing';

export default defineComponent({
  name: 'crm-remark',
  props: remarkProps,
  emits: ['add-success', 'before-open'],
  setup(props, { emit, slots }) {
    // 添加弹窗
    const addRemarkVisible = ref(false);

    function handleOpenAddModal() {
      if (props.allowAdd) {
        const obj = { stop: false };
        // 不需要打开则设置stop为true
        emit('before-open', obj);
        if (obj.stop) {
          return;
        }
        addRemarkVisible.value = true;
      }
    }

    const addLoading = ref(false);
    const addText = ref('');
    const textareaRef = ref<any>(null);

    async function addRemarkHandle() {
      if (!addText.value) return;
      addLoading.value = true;
      try {
        await POST(props.pushLink, {
          ...props.extraParams,
          [props.idKey]: props.item ? props.item.union_id : props.id,
          remark: addText.value,
        });
        addRemarkVisible.value = false;
        message.success('添加成功！');
        emit('add-success', { text: addText.value });
        addText.value = '';
      } finally {
        addLoading.value = false;
      }
    }

    const renderAddModal = () => {
      return (
        <a-modal
          vModel={[addRemarkVisible.value, 'visible']}
          confirmLoading={addLoading.value}
          title={props.title}
          onCancel={() => (addRemarkVisible.value = false)}
          onOk={addRemarkHandle}
        >
          <a-textarea
            ref={textareaRef}
            autofocus
            vModel={[addText.value, 'value']}
            autosize={{ minRows: 12 }}
            placeholder="请输入"
          />
          {slots.addModalText && slots.addModalText()}
        </a-modal>
      );
    };

    // 记录展示弹窗
    const remarkListVisible = ref(false);
    const remarkListLoading = ref(false);
    const remarkList = ref<RemarkItem[]>([]);
    const pagination = ref({
      current: 1,
      pageSize: 100,
      total: 0,
      hasMore: true,
    });
    const listContainerRef = ref<HTMLElement | null>(null);

    // 滚动事件处理
    const handleScroll = throttle(() => {
      if (!listContainerRef.value || remarkListLoading.value || !pagination.value.hasMore) return;

      const container = listContainerRef.value;
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isBottom = scrollTop + clientHeight >= scrollHeight - 20;

      if (isBottom) {
        pagination.value.current++;
        getRemarkList();
      }
    }, 200);

    // 添加滚动监听
    const addScrollListener = () => {
      if (listContainerRef.value) {
        listContainerRef.value.addEventListener('scroll', handleScroll, { passive: true });
      }
    };

    // 移除滚动监听
    const removeScrollListener = () => {
      if (listContainerRef.value) {
        listContainerRef.value.removeEventListener('scroll', handleScroll);
      }
    };

    onBeforeUnmount(() => {
      handleScroll.cancel();
      removeScrollListener();
    });

    async function getRemarkList() {
      if (pagination.value.current === 1) {
        remarkList.value = [];
      }

      remarkListLoading.value = true;
      try {
        const res = await GET(props.logLink, {
          [props.idKey]: props.item ? props.item.union_id : props.id,
          page: pagination.value.current,
          page_size: pagination.value.pageSize,
        });

        if (res.data?.length) {
          const newData = props.formatLogs ? props.formatLogs(res.data) : res.data;
          remarkList.value = [...remarkList.value, ...newData];

          pagination.value.total = res.total || 0;
          pagination.value.hasMore = newData.length >= pagination.value.pageSize;
        } else {
          pagination.value.hasMore = false;
        }
      } finally {
        remarkListLoading.value = false;
      }
    }

    // 重置分页状态
    const resetPagination = () => {
      pagination.value = {
        current: 1,
        pageSize: 100,
        total: 0,
        hasMore: true,
      };
    };

    // 打开记录弹窗
    async function openRemarkListModal() {
      resetPagination();
      remarkListVisible.value = true;
      await getRemarkList();

      // 等待DOM更新后添加监听
      nextTick(() => {
        addScrollListener();
      });
    }

    // 关闭记录弹窗
    function closeRemarkListModal() {
      removeScrollListener();
      remarkListVisible.value = false;
    }

    const renderListModal = () => {
      return (
        <a-modal
          vModel={[remarkListVisible.value, 'visible']}
          footer={null}
          title="标记记录"
          width={720}
          afterClose={resetPagination}
          onCancel={closeRemarkListModal}
        >
          <a-spin spinning={remarkListLoading.value}>
            <div class="remark-list" ref={listContainerRef} style="max-height: 620px">
              {...remarkList.value.map(item => (
                <div class="remark-item" key={item.id}>
                  <div class="remark-text" v-html={item.remark} />
                  <a-divider class="divider-class" />
                  <div class="created-by">
                    <span class="created-name">{item.remark_by}</span>
                    <span class="created-time">{item.created_at}</span>
                  </div>
                </div>
              ))}
              {remarkListLoading.value && pagination.value.current > 1 && <div class="loading-more">加载中...</div>}
              {!pagination.value.hasMore && remarkList.value.length > 0 && (
                <div class="no-more-data">没有更多数据了</div>
              )}
            </div>
            {remarkList.value.length === 0 && <a-empty></a-empty>}
          </a-spin>
        </a-modal>
      );
    };

    // size: default
    const renderDefaultSize = () => {
      return (
        <>
          {props.item ? (
            <div class={`mark-record ${props.layout}`}>
              <div
                class={`last-remark ${props.layout === 'vertical' ? 'wrap-tow-line' : ''}`}
                onClick={() => handleOpenAddModal()}
              >
                {props.item.last_remark}
              </div>
              {props.layout === 'horizontal' && (
                <div class="horizontal-remark-num" onClick={openRemarkListModal}>
                  • {props.item.remark_num}次记录
                </div>
              )}
              <div class="time-by">
                <span>by{props.item.remark_by}</span>
                <span>{props.item.created_at}</span>
              </div>
              {props.layout === 'vertical' && (
                <div class="vertical-remark-num" onClick={openRemarkListModal}>
                  • {props.item.remark_num}次记录
                </div>
              )}
            </div>
          ) : (
            <a-button type="link" onClick={() => handleOpenAddModal()}>
              备注
            </a-button>
          )}
        </>
      );
    };

    // size: small
    const renderSmallSize = () => {
      return (
        <>
          <span class="my-link" onClick={() => handleOpenAddModal()}>
            备注
          </span>
          {props.item && (
            <span class="small-text" onClick={openRemarkListModal}>
              {props.item.remark_num}次记录 {props.item.created_at}
            </span>
          )}
        </>
      );
    };

    // size: block
    const renderBlockSize = () => {
      return (
        <div class={`block-remark ${props.blockFlex ? 'flex-wrap' : ''} flex-y-start`}>
          <div
            class={` ${!props.allowAdd ? 'no-allow-add' : ''} left-content flex-con`}
            onClick={() => {
              handleOpenAddModal();
              // 组件渲染需要时间，需要将该操作加入异步队列
              setTimeout(() => textareaRef.value?.focus(), 200);
            }}
          >
            {props.item ? (
              <>
                <span class="block-remark-text">
                  {props.prefixText}
                  {props.item.last_remark}
                </span>
                <span class="block-time-by">——{props.item.remark_by}</span>
                <span class="block-time-by">{props.item.created_at}</span>
              </>
            ) : (
              <span class="block-remark-text">
                {slots.emptyText ? slots.emptyText() : props.prefixText + '暂无备注'}
              </span>
            )}
          </div>
          <div class="right-count" onClick={openRemarkListModal}>
            {props.item ? props.item.remark_num : 0}次记录
          </div>
        </div>
      );
    };

    return () => (
      <>
        <div class="remark-content remark-component">
          {props.size === 'default' && renderDefaultSize()}
          {props.size === 'small' && renderSmallSize()}
          {props.size === 'block' && renderBlockSize()}
        </div>
        {renderAddModal()}
        {renderListModal()}
      </>
    );
  },
});
