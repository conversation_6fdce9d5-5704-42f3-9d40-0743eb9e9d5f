import { GET, POST } from '@/services/api';
export function apiGetRuleOptions() {
  return GET('/super/risk-access-channel/get-options');
}
export function apiGetRuleConfigList(params: { channel_name?: string; channel_bind?: number; current: number }) {
  return GET('/super/risk-access-channel/get-list-data', params);
}
export function apiDeleteRuleConfig(params: { id: string }) {
  return POST('/super/risk-access-channel/delete', params);
}
export function apiSaveRuleConfig(params: { id?: string; title: string; method: string[] }) {
  return POST('/super/risk-access-channel/save-data', params);
}

export function apiChangeRuleStatus(params: { id: string; status: 1 | 0 }) {
  return POST('/super/risk-access-channel/change', params);
}

export function apiGetRuleLog(params: { pageSize: number; currentPage: number; created_at: [string, string] }) {
  return GET('/super/risk-access-channel/get-list-log', params);
}

export function apiGetRuleDetailLog(params: {
  id: string;
  pageSize: number;
  currentPage: number;
  created_at: [string, string];
}) {
  return GET('/super/risk-access-channel/get-config-log', params);
}

export function apiChangePayWay(params: any) {
  return POST('/super/system-setting/update-one', params);
}

export function apiGetPayWay() {
  return GET('/super/system-setting/get-one');
}
