<template>
  <a-modal
    :body-style="{ paddingBottom: '8px' }"
    class="classified-modal"
    :confirm-loading="submitLoading"
    :destroy-on-close="true"
    :title="title"
    :visible="visible"
    width="400px"
    @cancel="onCancel"
    @ok="onOk"
  >
    <div class="content">
      <div>
        {{ `是否确认${currentCommodity.status ? '上架' : '暂停'}回收当前商品？` }}
      </div>
      <div style="font-weight: 600">
        {{ currentCommodity.name }}
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, PropType } from 'vue';
import { message } from 'ant-design-vue';
import { changePurchasedGoodStatus } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  currentCommodity: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const title = computed(() => `${props.currentCommodity.status ? '暂停' : '上架'}回收商品`);

const submitLoading = ref(false);

const onCancel = () => emits('update:visible', false);

const onOk = () => {
  submitLoading.value = true;
  const status = props.currentCommodity.status ? 'open' : 'close';
  changePurchasedGoodStatus(props.id, status)
    .then(() => {
      onCancel();
      message.success('操作成功');
      emits('onLoadList');
    })
    .finally(() => (submitLoading.value = false));
};
</script>
<style scoped lang="less">
.classified-modal .ant-modal-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 16px;
}
.content {
  color: rgba(0, 0, 0, 0.65);
}
</style>
