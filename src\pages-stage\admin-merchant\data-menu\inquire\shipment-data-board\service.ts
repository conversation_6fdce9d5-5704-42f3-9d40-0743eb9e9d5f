import { GET, POST } from '@/services/api';

export function getStatisticsData(data: any) {
  return POST('/merchant/rDI/deliveryBoard/serQuery', data, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
    hostType: '<PERSON><PERSON><PERSON>',
  });
}

export function getShipmentList(data: any) {
  return POST('/merchant/rDI/deliveryBoard/serQueryListGroupDate', data, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
    hostType: 'Shu<PERSON><PERSON>',
  });
}

export function getSearchMap() {
  return GET(
    '/merchant/rDI/deliveryBoard/searchMap',
    {},
    {
      hostType: '<PERSON><PERSON><PERSON>',
    },
  );
}

export function loginShuzhi() {
  return GET(
    '/merchant/rDI/login2Shuzhi',
    {},
    {
      hostType: '<PERSON><PERSON><PERSON>',
    },
  );
}
