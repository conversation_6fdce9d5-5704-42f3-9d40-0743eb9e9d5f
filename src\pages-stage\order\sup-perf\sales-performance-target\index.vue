<template>
  <layout-admin-page v-bind="pageLayoutConfig">
    <template #extra>
      <a-button
        type="primary"
        @click="() => (showModal = true)"
      >
        <PlusOutlined />
        反馈工单
      </a-button>
    </template>
    <RTable
      ref="salesPerformanceTargetRef"
      v-bind="performanceTargetTableConfig"
    >
      <template #buttons>
        <template v-if="tableBtnList.length">
          <a-button
            v-for="(item, index) in tableBtnList"
            :key="index"
            :loading="item.field === 'export' && exportLoading"
            :type="item.type"
            @click="item.callback"
          >
            {{ item.text }}
          </a-button>
        </template>
      </template>
      <template #tableBodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operation'">
          <div class="operate-wrap">
            <template v-if="tableBtnList.length">
              <div
                v-for="(item, index) in tableOperationBtnList"
                :key="index"
                class="operate-wrap-item"
                @click="item.callback(record)"
              >
                {{ item.text }}
              </div>
            </template>
          </div>
        </template>
      </template>
    </RTable>

    <operation-modal
      v-model:visible="editModalInfo.visible"
      v-bind="editModalInfo"
      @refresh="getData"
    />

    <log-modal
      v-model:visible="logModalInfo.visible"
      v-bind="logModalInfo"
    />
    <!-- 创建工单 -->
    <CreatWorkOrder
      :category-id="1"
      :visible="showModal"
      @cancel="() => (showModal = false)"
      @ok="() => (showModal = false)"
    />
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { RTable } from 'rrz-web-design';
import { getPageBaseConfig } from './config';
import { asyncEduce } from '@/utils/educe';

import OperationModal from '../data-board-common-components/operation-modal.vue';
import LogModal from '../data-board-common-components/log-modal.vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import CreatWorkOrder from '@/pages-stage/company-sell/sales-feedback-work-order/work-order-list/components/create-work-order/index.vue';
import type { IBtnItem, IModalInfo, TR } from '../data-board-common-components/data';
const { pageLayoutConfig, performanceTargetTableConfig } = getPageBaseConfig();
const salesPerformanceTargetRef = ref<InstanceType<typeof RTable>>();
const route = useRoute();

// 获取数据
const getData = (type = '') => {
  if (salesPerformanceTargetRef.value) {
    if (type === 'search') {
      salesPerformanceTargetRef.value.resetFields();
    } else {
      salesPerformanceTargetRef.value.getTableList();
    }
  }
};
// 导出数据

const showModal = ref(false);
const exportLoading = ref(false);
const handleExportData = async () => {
  exportLoading.value = true;
  try {
    await asyncEduce(
      '/super/salesData/dept-target/export-manger-list',
      salesPerformanceTargetRef?.value?.searchForm,
      route.query.origin as string,
    );
  } finally {
    exportLoading.value = false;
  }
};

// 打开编辑框
const editModalInfo = reactive<IModalInfo & { type: string }>({
  visible: false,
  type: 'edit',
  searchForm: {
    dept_id: '',
    month: '',
    dept_name: '',
    target: '',
  },
});
const handleShowEditModal = (item: TR) => {
  editModalInfo.visible = true;
  editModalInfo.searchForm['dept_id'] = item.dept_id;
  editModalInfo.searchForm['month'] = item.month;
  editModalInfo.searchForm['dept_name'] = item.dept_name;
  editModalInfo.searchForm['target'] = item.target;
};
// 查看版本记录
const logModalInfo = reactive<IModalInfo>({
  visible: false,
  searchForm: {
    dept_id: '',
    month: '',
    dept_name: '',
  },
});
const handleShowLogModal = (item: TR) => {
  logModalInfo.visible = true;
  logModalInfo.searchForm['dept_id'] = item.dept_id;
  logModalInfo.searchForm['month'] = item.month;
  logModalInfo.searchForm['dept_name'] = item.dept_name;
};

const tableBtnList = ref<IBtnItem[]>([
  {
    type: 'primary',
    text: '查询',
    field: 'query',
    callback: getData,
  },
  {
    text: '重置',
    field: 'reset',
    callback: () => getData('search'),
  },
  {
    text: '导出',
    field: 'export',
    callback: handleExportData,
  },
]);
const tableOperationBtnList = ref<IBtnItem[]>([
  {
    text: '修改',
    field: 'edit',
    callback: item => handleShowEditModal(item),
  },
  {
    text: '版本记录',
    field: 'log',
    callback: item => handleShowLogModal(item),
  },
]);
</script>

<style lang="less" scoped>
.operate-wrap {
  display: flex;
  &-item {
    color: #3777ff;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    cursor: pointer;
    &:first-child {
      margin-right: 16px;
    }
  }
}
</style>
