<template>
  <div
    class="drawer"
    :class="value ? 'active' : 'default'"
    :style="{
      height: (value ? height : 0) + 'px',
    }"
  >
    <div
      ref="InnerBox"
      class="inner-box"
      :style="{
        transform: `translateY(${value ? 0 : -height}px)`,
      }"
    >
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

defineProps({
  value: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['change']);

const InnerBox = ref<HTMLElement>(null as any);
const height = ref(0);

let observer: MutationObserver | null = null;

onMounted(() => {
  height.value = InnerBox.value.clientHeight;
  emit('change', !!height.value);
  observer = new MutationObserver(() => {
    height.value = InnerBox.value.clientHeight;
    emit('change', !!height.value);
  });
  observer.observe(InnerBox.value, { childList: true });
});

onUnmounted(() => {
  observer && observer.disconnect();
});
</script>

<style scoped lang="less">
.drawer {
  overflow: hidden;
  transition: all 0.2s linear;
  .inner-box {
    transition: all 0.2s linear;
  }
}
.drawer.default {
  opacity: 0;
}
.drawer.active {
  opacity: 1;
}
</style>
