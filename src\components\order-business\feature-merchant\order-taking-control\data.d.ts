export interface IReceiveStatus {
  /** 是否运营中 */
  is_dispatch?: boolean;
  /** 运营信息 */
  dispatch_info?: IDispatchInfo;
  /** 发假信息 */
  holiday_day?: string;
}

interface IDispatchInfo {
  next_start: string;
  next_end: string;
  next_update_time: string;
  day_info: IDayInfo;
  no_order_type: number;
  is_default: number;
  is_platform: boolean;
  order_auth: boolean;
  status: number;
  business_id: string;
  /** 是否是派单商家 */
  is_dispatch: boolean;
}

interface IDayInfo {
  work_shift?: IWorkShiftItem[];
  is_open: number;
  time_rules?: ITimeRule[];
  /** 日单量上限 */
  day_total_limit: string;
  /** 今日单量 */
  day_num: number;
}

interface IWorkShiftItem {
  label: string;
  time: string;
}

interface ITimeRule {
  start: string;
  end: string;
}

export interface IHoliday {
  id: string;
  title: string;
  start_day: string;
  end_day: string;
}

export interface ICalendar {
  show_panel?: boolean;
  show_pop?: boolean;
  order_auth?: string;
  has_change?: boolean;
  relieve_status?: string;
  business_id?: string;
  current_week?: string;
  date_config?: string;
  day_num?: number;

  [key: string]: {
    content: string;
    label: string;
    limit?: string;
    type: number;
  };
}
