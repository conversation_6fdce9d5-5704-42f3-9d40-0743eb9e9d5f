export interface IRecycleData {
  // 入库数量
  in_warehouse_num: string;
  // 入库金额
  in_warehouse_price: string;
  // 成交订单数量
  finish_order_num: string;
  // 成交订单金额
  finish_order_price: string;
  // 退货金额
  return_price: string;
  // 退货数量
  return_num: string;
  // 设备商品列表
  device_model_list: {
    // 型号名称
    model_name: string;
    // 商品数量
    num: number;
    // 比例
    ratio: string;
    // 成新度列表
    device_ratio_pass_list: {
      // 成新度
      device_ratio_pass: string;
      // 数量
      num: number;
    }[];
  }[];
  // 设备成型度列表
  device_ratio_pass_list: {
    // 成新度
    device_ratio_pass: string;
    // 数量
    num: number;
    // 比例
    ratio: string;
    // model_list
    model_list: {
      // 型号名称
      model_name: string;
      // 数量
      num: number;
    }[];
  }[];
}

export interface IInventoryData {
  // 库存金额
  purchased_price: string;
  // 库存
  stock: string;
  // 周转率
  device_turnover_time: string;
  // 型号列表
  device_model_list: {
    // 型号
    model_name: string;
    // 数量
    num: number;
    //  比例
    ratio: string;

    device_ratio_pass_list: {
      // 成新度
      device_ratio_pass: string;
      // 数据
      num: number;
    }[];
  }[];
  //  成新度比例
  device_ratio_pass_list: {
    // 成新度
    device_ratio_pass: string;
    // 数量
    num: number;
    // 比例
    ratio: string;
    model_list: {
      // 型号名称
      model_name: string;
      // 数量
      num: number;
    }[];
  }[];
}

export interface IPayData {
  // 待付
  wait_pay_num: string;
  // 已付
  finish_pay_num: string;
  // 总计
  count_num: string;
  count_list: {
    name: string;
    num: number;
  }[];
}

export type ICollectData = IPayData;
