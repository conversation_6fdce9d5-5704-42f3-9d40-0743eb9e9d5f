import { GET, POST } from '@/services/api';

// 获取应用活动下拉
export async function getFormOptions(): Promise<any> {
  return GET('/super/recycle/booking-recycle-rule/form-filter ', {});
}

// 获取表单下拉
export async function formSelect(): Promise<any> {
  return GET('/super/recycle/booking-recycle-rule/order-form-filter ');
}

// 获取商品类目数据
export async function getCategoryMenu(): Promise<any> {
  return GET('/super/produceDict/category/get-menu', {});
}

// 添加规则
export async function onAddRuleFormData(params: any): Promise<any> {
  return POST('/super/recycle/booking-recycle-rule/create', params, {
    headers: { 'content-type': 'application/json' },
  });
}

// 修改规则
export async function onEditRuleFormData(params: any): Promise<any> {
  return POST('/super/recycle/booking-recycle-rule/update', params, {
    headers: { 'content-type': 'application/json' },
  });
}

// 版本详情
export async function getRuleVersionDetail(params: any): Promise<any> {
  return GET('/super/recycle/booking-recycle-rule/version-detail', params);
}

// 版本下拉数据
export async function getRuleVersionSelect(params: any): Promise<any> {
  return GET('/super/recycle/booking-recycle-rule/version-list', params);
}

// 修改规则应用状态
export async function switchStatus(params: any): Promise<any> {
  return GET('/super/recycle/booking-recycle-rule/switch-status', params);
}

// 删除规则
export async function ruleDel(params: any): Promise<any> {
  return GET('/super/recycle/booking-recycle-rule/del', params);
}
