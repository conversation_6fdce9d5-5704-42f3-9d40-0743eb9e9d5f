<template>
  <layout-admin-page
    :navs="['趣回收', '基础数据', '回收商品列表']"
    title="回收商品列表"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="showAppendGoodsModal"
      >
        添加回收商品
        <template #icon>
          <plus-outlined />
        </template>
      </a-button>
    </template>
    <div class="container">
      <div class="search-bar">
        <a-form
          ref="formRef"
          layout="inline"
          :model="formState"
          style="width: 100%"
        >
          <a-row style="width: 100%">
            <a-col
              :span="6"
              style="display: flex; align-items: center"
            >
              <a-form-item
                label="回收商品名称"
                name="category_id"
              >
                <ProdutionCascader v-model:modelValue="formState.category_id" />
              </a-form-item>
              <a-form-item>
                <a-button
                  style="border-radius: 4px"
                  type="primary"
                  @click="onHandleControl().search"
                >
                  查询
                </a-button>
                <a-button
                  style="margin-left: 10px; border-radius: 4px"
                  @click="onHandleControl().reset"
                >
                  重置
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="main">
        <a-tabs
          v-model:activeKey="formState.status"
          @change="onLoadList"
        >
          <a-tab-pane
            key=""
            tab="全部商品"
          />
          <a-tab-pane
            key="0"
            tab="回收中商品"
          />
          <a-tab-pane
            key="1"
            tab="暂停回收商品"
          />
        </a-tabs>

        <a-table
          class="list-table"
          :columns="columns"
          :data-source="tableList"
          :loading="isLoading"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.count,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total: number) => `共 ${total} 条数据 第${pagination.current}页`
          }"
          :scroll="{ x: 1260 }"
          @change="onChangePageSize"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.name === 'image'">
              <a-image
                :src="record.image[0]"
                style="
                  width: 80px;
                  height: 80px;
                  object-fit: scale-down;
                  overflow: hidden;
                  background-color: #f5f5f7;
                  border: 1px solid rgba(6, 21, 51, 0.06);
                  border-radius: 4px;
                "
              />
              <span style="padding-left: 12px">{{ record.name }}</span>
            </template>
            <template v-if="column.dataIndex === 'base_price'">
              <div class="base-price-block">
                <div class="value">
                  {{ record.base_price }}
                </div>
                <a-tag
                  v-if="record.market_change && !purchasedChangeList.includes(record.id)"
                  closable
                  color="warning"
                  @close="handleCloseMessage(record.id)"
                >
                  市场估价有变
                </a-tag>
              </div>
            </template>
            <template v-if="column.name === 'action'">
              <a-button
                type="link"
                @click="onHandleControl(record.id).edit(record.name, Number(record.status) === 1 ? 'update' : 'check')"
              >
                {{ Number(record.status) === 1 ? '编辑' : '查看' }}
              </a-button>
              <a-button
                :danger="Number(record.status) !== 1"
                type="link"
                @click="onHandleControl(record.id).changeStatus(record.status, record.name)"
              >
                {{ Number(record.status) === 1 ? '上架回收' : '暂停回收' }}
              </a-button>
              <a-button
                v-if="Number(record.status) === 1"
                danger
                type="link"
                @click="onHandleControl(record.id).delete"
              >
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <affirm-modal
      v-if="affirmModalVisible"
      :id="currentId"
      v-model:visible="affirmModalVisible"
      :current-commodity="currentCommodity"
      @on-load-list="onLoadList"
    />

    <append-goods-modal
      v-if="appendGoodsModalVisible"
      v-model:visible="appendGoodsModalVisible"
      @on-load-list="onLoadList"
    />
    <assess-price-drawer
      v-if="assessPriceDrawerVisible"
      v-model:visible="assessPriceDrawerVisible"
      :action-type="actionType"
      :current-commodity="assessInfo"
      @on-load-list="onLoadList"
    />
  </layout-admin-page>
</template>
<script setup lang="ts">
import { ref, reactive, createVNode, onMounted } from 'vue';
import dayjs from 'dayjs';
import { PlusOutlined } from '@ant-design/icons-vue';
import { FormInstance, Modal, message } from 'ant-design-vue';
import { getTableList, deletePurchasedGood } from './service';
import useTableList from '../../common/use-table-list';
import useBoolean from '../../common/use-boolean';
import AffirmModal from './components/affirm-modal.vue';
import AppendGoodsModal from './components/append-goods-modal.vue';
import AssessPriceDrawer from './components/assess-price-drawer.vue';
import ProdutionCascader from '@/pages-stage/recycle/base-data/commodity-list/components/prodution-cascader.vue';

type actionType = 'create' | 'update' | 'check';

const columns: any[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '商品信息',
    dataIndex: 'image',
    name: 'image',
    width: 180,
  },
  {
    title: '最高回收价',
    dataIndex: 'base_price',
    width: 80,
    align: 'center',
  },
  {
    title: '商品排序',
    dataIndex: 'sort',
    width: 60,
  },
  {
    title: '发布时间',
    dataIndex: 'createdAt',
    width: 100,
  },
  {
    title: '操作',
    name: 'action',
    fixed: 'right',
    width: 130,
  },
];

const [affirmModalVisible, { setTrue: showAffirmModal }] = useBoolean();
const [appendGoodsModalVisible, { setTrue: showAppendGoodsModal }] = useBoolean();
const [assessPriceDrawerVisible, { setTrue: showAssessPriceDrawer }] = useBoolean();

const currentId = ref('');
const purchasedChangeList = ref([]);
const currentCommodity = ref({
  status: false,
  name: '',
});

const assessInfo = ref({
  id: '',
  name: '',
});

const actionType = ref<actionType>('create');

// 表单实例
const formRef = ref<FormInstance>();
const formState = ref({
  category_id: [], // 分类名称
  status: '',
});

// 分页器
let pagination = reactive({
  current: 1,
  pageSize: 10,
  count: 0,
});

const { onLoadList, tableList, isLoading } = useTableList({
  getTableList,
  payload: formState.value,
  pagination,
  isAutoInit: true,
});

const onChangePageSize = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  // 再次发起网络请求
  onLoadList();
};

type status = '0' | '1';
const onHandleControl = (id?: string) => ({
  search() {
    pagination.current = 1;
    onLoadList();
  },
  reset() {
    // 1.重置所有字段
    formRef.value?.resetFields();
    // 2. 初始化页码
    pagination.current = 1;
    pagination.pageSize = 10;
    // 3. 重新获取table数据
    onLoadList();
  },
  edit(name: string, type?: actionType) {
    currentId.value = id as string;
    actionType.value = type as actionType;
    console.log(type, '==type');
    assessInfo.value = {
      id: currentId.value,
      name,
    };
    showAssessPriceDrawer();
  },
  delete() {
    Modal.confirm({
      title: '是否删除此回收商品?',
      content: createVNode(
        'div',
        {
          style: 'color: rgba(0,0,0,0.65);',
        },
        '回收商品删除后将不能恢复，确认删除所选商品吗？',
      ),
      onOk() {
        return deletePurchasedGood(id as string).then(() => {
          message.success('删除成功');
          onLoadList();
        });
      },
    });
  },
  changeStatus(status: status, name: string) {
    currentId.value = id as string;
    currentCommodity.value = {
      status: Number(status) !== 0,
      name: name,
    };
    showAffirmModal();
  },
});
const handleCloseMessage = (id: never) => {
  updatelocalStorage(id);
};
const getLocalStorage = () => {
  const localStorage = window.localStorage.getItem('purchased-goods-list-change');
  if (localStorage) {
    const localStorageData = JSON.parse(localStorage);
    purchasedChangeList.value = localStorageData.value;
  }
};
const initlocalStorage = () => {
  const localStorage = window.localStorage.getItem('purchased-goods-list-change');
  if (localStorage) {
    const localStorageData = JSON.parse(localStorage);
    if (localStorageData.effectiveTime === dayjs().format('YYYY-MM-DD')) {
      getLocalStorage();
      return;
    }
  }
  const params = {
    value: purchasedChangeList.value,
    effectiveTime: dayjs().format('YYYY-MM-DD'),
  };
  window.localStorage.setItem('purchased-goods-list-change', JSON.stringify(params));
};
const updatelocalStorage = (id: never) => {
  purchasedChangeList.value.push(id);
  const params = {
    value: purchasedChangeList.value,
    effectiveTime: dayjs().format('YYYY-MM-DD'),
  };
  window.localStorage.setItem('purchased-goods-list-change', JSON.stringify(params));
};

onMounted(() => {
  initlocalStorage();
});
</script>
<style scoped lang="less">
@import '../../common/base.less';
.list-table {
  padding-top: 14px;
  .ant-btn-link {
    color: #3777ff;
    &:hover,
    &:focus {
      color: #3776ffd4;
    }
    &.ant-btn-dangerous {
      color: var(--ant-error-color);
      &:hover,
      &:focus {
        color: var(--ant-error-color-hover);
      }
    }
  }
}
:deep(.ant-alert-warning) {
  background-color: #fff;
  border: 1px solid #fff;
}
:deep(.ant-alert) {
  padding: 0;
}
.base-price-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
