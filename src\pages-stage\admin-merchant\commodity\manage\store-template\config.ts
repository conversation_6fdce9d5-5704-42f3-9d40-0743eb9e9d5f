import { ColumnProps } from 'ant-design-vue/lib/table';

export const columns: ColumnProps[] = [
  {
    dataIndex: 'updated_at',
    title: '备份时间',
    key: 'title',
    // width: 300,
  },
  {
    dataIndex: 'shop_template',
    title: '模板样式',
    // width: 300,
  },
  {
    dataIndex: 'shop_color',
    key: 'shop_color',
    title: '主题颜色',
  },
  {
    dataIndex: 'action',
    title: '操作',
    fixed: 'right',
    key: 'operation',
    // width: 300,
  },
];
