export const basicModal = {
  accessories: {
    label: '商品配件',
    component: () => import('./accessories-modal/index.vue'),
  },
  addGroup: {
    label: '添加分组',
    component: () => import('./add-group-modal/index.vue'),
  },
  additionalService: {
    label: '增值服务',
    component: () => import('./additional-service-modal/index.vue'),
  },
  allIdentityInfo: {
    label: '全部身份信息',
    component: () => import('./all-identity-info-modal/index.vue'),
  },
  bindRecord: {
    label: '绑定记录',
    component: () => import('./bind-record-modal/index.vue'),
  },
  creditScore: {
    label: '信用额度',
    component: () => import('./credit-score-modal/index.vue'),
  },
  editReceivingAddress: {
    label: '修改收货地址',
    component: () => import('./edit-receiving-address-modal/index.vue'),
  },
  editRegisterPhone: {
    label: '更改绑定手机',
    component: () => import('./edit-register-phone-modal/index.vue'),
  },
  editStatus: {
    label: '订单状态修改',
    component: () => import('./edit-status-modal/index.vue'),
  },
  gradsBuyout: {
    label: '梯度购买',
    component: () => import('./grads-buyout-modal/index.vue'),
  },
  buyout: {
    label: '买断',
    component: () => import('./buyout-tip-modal/index.vue'),
  },
  html: {
    label: 'html文本',
    component: () => import('./html-modal/index.vue'),
  },
  identityInfo: {
    label: '身份信息',
    component: () => import('./identity-info-modal/index.vue'),
  },
  invoice: {
    label: '发票信息',
    component: () => import('./invoice-modal/index.vue'),
  },
  leaveMessage: {
    label: '留言',
    component: () => import('./leave-message-modal/index.vue'),
  },
  logisticInfo: {
    label: '物流信息',
    component: () => import('./logistic-info-modal/index.vue'),
  },
  logisticChange: {
    label: '物流变更',
    component: () => import('./logistic-change-modal/index.vue'),
  },
  markReply: {
    label: '标记回复',
    component: () => import('./mark-reply-drawer/index.vue'),
  },
  orderGroup: {
    label: '订单分组',
    component: () => import('./order-group-modal/index.vue'),
  },
  orderReceivingState: {
    label: '接单状态设置',
    component: () => import('./order-receiving-state-modal/index.vue'),
  },
  orderTimeline: {
    label: '订单时间线',
    component: () => import('./order-timeline-modal/index.vue'),
  },
  overdueRentDrawer: {
    label: '逾期租金',
    component: () => import('./overdue-rent-drawer/index.vue'),
  },
  payDetail: {
    label: '支付详情',
    component: () => import('./pay-detail-modal/index.vue'),
  },
  platformConfigApply: {
    label: '解除平台配置申请',
    component: () => import('./platform-config-apply-modal/index.vue'),
  },
  receivingAddressLog: {
    label: '收货地址变更记录',
    component: () => import('./receiving-address-log-modal/index.vue'),
  },
  refundList: {
    label: '拒单列表',
    component: () => import('./refuse-list-modal/index.vue'),
  },
  renewEnable: {
    label: '是否支持续租',
    component: () => import('./renew-enable-modal/index.vue'),
  },
  renewLog: {
    label: '续费记录',
    component: () => import('./renew-log-modal/index.vue'),
  },
  revokeReturn: {
    label: '撤销退货',
    component: () => import('./revoke-return-modal/index.vue'),
  },
  serverRemark: {
    label: '商家备注',
    component: () => import('./server-remark-modal/index.vue'),
  },
  settledRent: {
    label: '租金结算详情',
    component: () => import('./settled-rent-modal/index.vue'),
  },
  supple: {
    label: '关联订单',
    component: () => import('./supple-modal/index.vue'),
  },
  uploadTripartite: {
    label: '上传三方信息',
    component: () => import('./upload-tripartite-modal/index.vue'),
  },
  verifyAddress: {
    label: '验证收货地址',
    component: () => import('./verify-address-modal/index.vue'),
  },
  orderExport: {
    label: '订单导出',
    component: () => import('./order-export-modal/index.vue'),
  },
  rentCouponDetail: {
    label: '租金券使用明细',
    component: () => import('./rent-coupon-detail-modal/index.vue'),
  },
  currentPurchase: {
    label: '当期购买价说明',
    component: () => import('./current-purchase-modal/index.vue'),
  },
  printModal: {
    label: '打印',
    component: () => import('./print-modal/index.vue'),
  },
  shanSongLogisticsTrackModal: {
    label: '闪送物流轨迹',
    component: () => import('./shan-song-logistics-track-modal/index.vue'),
  },
};
