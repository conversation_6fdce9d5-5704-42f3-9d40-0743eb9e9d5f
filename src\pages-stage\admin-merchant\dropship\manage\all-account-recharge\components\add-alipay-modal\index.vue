<template>
  <a-modal
    v-model:visible="addBankModalVisible"
    cancel-text="取消"
    ok-text="添 加"
    title="添加支付宝"
    @cancel="handleAddBankCancel"
    @ok="handleAddBankOk"
  >
    <form-create
      ref="addBankModalFormRef"
      v-model:value="addBankModalForm"
      :form-group="addBankModalFormGroup"
      :origin-props="{ layout: 'vertical', rules: addBankModalRules }"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';

import { useVModel } from '@/hook';

import type { TAddBankModalForm } from '../../data';
import { createAlipayAccount } from '../../services';
import { addBankModalFormGroup } from './config';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:visible', 'ok']);
const addBankModalVisible = useVModel(props, 'visible', emits);

const addBankModalForm = ref<TAddBankModalForm>({
  alipay_account: '',
  alipay_payuser: '',
});

const addBankModalRules = {
  alipay_account: [
    {
      required: true,
      message: '请输入支付宝账号',
    },
  ],
  alipay_payuser: [
    {
      required: true,
      message: '请输入支付宝户名',
    },
  ],
};

const addBankModalFormRef = ref();

function handleAddBankCancel() {
  const formRef = addBankModalFormRef.value?.getFormRef();
  formRef?.resetFields();
  addBankModalVisible.value = false;
}

async function handleAddBankOk() {
  const formRef = addBankModalFormRef.value?.getFormRef();
  await formRef?.validate();
  await createAlipayAccount(addBankModalForm.value);
  message.success('添加成功！');
  handleAddBankCancel();
  emits('ok');
}
</script>

<style scoped lang="less">
.ant-alert {
  align-items: baseline !important;
}
.ant-alert-icon {
  margin-top: 2px;
}
.radio-flex {
  display: flex;
  width: 480px;
}
</style>
