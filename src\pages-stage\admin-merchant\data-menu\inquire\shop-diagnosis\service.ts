import { POST, RequestConfig } from '@/services/api';

import { IGetWarningRecordsParams, IWarningRecord } from './data.d';

const config: RequestConfig = {
  headers: { 'content-type': 'application/json;charset=utf-8' },
  hostType: '<PERSON><PERSON><PERSON>',
};

/**
 * 商家后台-获取商家预警记录列表
 */
export function apiGetWarningRecords(params: IGetWarningRecordsParams) {
  return POST<IGetWarningRecordsParams, IWarningRecord>('/merchant/rDI/sws/display', params, config);
}

/**
 * 店铺诊断获取详情
 */
export function apiGetShopData() {
  return POST(
    '/merchant/rDI/shopDiag/resultV2',
    {},
    {
      hostType: 'ShuZhi',
    },
  );
}

/**
 * @description: 获取店铺诊断option
 * @return {*}
 */
export function apiGetOptions() {
  return POST('/merchant/rDI/shopDiag/options', {}, config);
}

export function apiReGenerate() {
  return POST(
    '/merchant/rDI/shopDiag/reGenerate',
    {},
    {
      hostType: 'ShuZhi',
    },
  );
}

export function apiGetReportDetail(data: any) {
  return POST('/merchant/rDI/serverAssessment/myV2TranscriptDetail', data, config);
}
