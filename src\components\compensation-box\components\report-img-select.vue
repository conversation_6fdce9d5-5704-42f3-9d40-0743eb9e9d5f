<template>
  <a-modal
    v-model:visible="visible"
    ok-text="确定上传"
    title="选取文件"
    width="832px"
    @cancel="close"
    @ok="confirm"
  >
    <div class="checkbox-wrap">
      <a-checkbox
        :checked="isAllSelected"
        @click="selectImageAll"
      >
        全选
      </a-checkbox>
    </div>
    <div class="image-list">
      <div
        v-for="(item, index) in imageList"
        :key="index"
        class="image-item"
      >
        <a-image
          class="img-box"
          :height="144"
          :src="item.url"
          :width="144"
        />
        <a-checkbox
          :checked="!!item.selected"
          class="checkbox"
          @click="selectImage(index)"
        />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { message } from 'ant-design-vue';
import { useModal } from 'rrz-web-design';

import { deepClone } from '@/utils/base';

import { TUrl } from '../data.d';
const props = defineProps<{
  size: number;
}>();
const emits = defineEmits(['setImages']);

const imageList = ref<TUrl[]>([]);
const { visible, open, close, confirm } = useModal(
  () => {
    const temp = deepClone(imageList.value.filter(item => !!item.selected));
    if (temp.length === 0) {
      message.warning('请选择图片');
      return;
    }
    if (props.size < currentSize.value) {
      message.warning('图片数量达到上限');
      return;
    }
    emits('setImages', temp);
    close();
  },
  {
    beforeOpen(images: TUrl[]) {
      imageList.value = images.map(item => ({
        ...item,
        selected: false,
      }));
    },
  },
);

const currentSize = computed(() => {
  return imageList.value.filter(item => !!item.selected).length;
});

function selectImage(index: number) {
  if (imageList.value[index]?.selected) {
    imageList.value[index].selected = false;
  } else {
    imageList.value[index].selected = true;
  }
}

function selectImageAll() {
  const flag = !isAllSelected.value;
  imageList.value.forEach(item => {
    item.selected = flag;
  });
}

const isAllSelected = computed(() => {
  return imageList.value.every(item => item.selected);
});

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.checkbox-wrap {
  margin-bottom: 16px;
  padding: 10px 12px;
  background: #f5f7fa;
  border-radius: 8px 8px 8px 8px;
}
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  .image-item {
    width: 144px;
    text-align: center;
    :deep(.ant-image-img) {
      border: 1px solid rgba(6, 21, 51, 0.15);
      border-radius: 8px 8px 8px 8px;
    }
    .checkbox {
      margin-top: 8px;
    }
  }
}
</style>
