<template>
  <form-card title="成新度设定">
    <a-tabs
      :active-key="state.currentTab"
      type="card"
      @tab-click="switchTab"
    >
      <a-tab-pane
        v-for="(item, index) in state.condition_type"
        :key="index"
        :tab="item"
      />
    </a-tabs>
    <a-form>
      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item
            label="定义"
            v-bind="validateInfos.type"
          >
            <a-select
              v-model:value="state.type"
              :options="typeOptions"
              placeholder="请选择定义"
              @change="onTypeChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item>
            <a-button
              :loading="savingConfig"
              type="primary"
              @click="saveCurrentConfig"
            >
              保存当前配置
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row
        :gutter="24"
        style="margin-top: -8px; margin-bottom: -8px"
      >
        <a-col
          v-for="(item, index) in state.allOption"
          :key="index"
          :span="8"
          style="margin-top: 8px; margin-bottom: 8px"
        >
          <div class="option-item">
            <div
              :class="[
                {
                  title: true,
                  error: state.type === '&' && item.error,
                },
              ]"
            >
              {{ item.title }}
            </div>
            <a-checkbox-group v-model:value="item.selected">
              <a-radio-group v-model:value="item.selected">
                <ul class="ul-container">
                  <li
                    v-for="(option, i) in item.option"
                    :key="i"
                    class="check-item"
                  >
                    <template v-if="state.type === '||'">
                      <a-checkbox
                        :disabled="option.hasChecked"
                        :value="option.value"
                      />
                    </template>
                    <template v-if="state.type === '&'">
                      <a-radio
                        :disabled="option.hasChecked"
                        :value="option.value"
                        @change="item.error && (item.error = undefined)"
                      />
                    </template>
                    <label>{{ option.value }}</label>
                  </li>
                </ul>
              </a-radio-group>
            </a-checkbox-group>
          </div>
        </a-col>
      </a-row>
    </a-form>
  </form-card>
</template>

<script setup lang="ts">
import FormCard from '../../../common/components/form-card.vue';
import { ITemplate } from '../data';
import useStepSecond from '../composables/use-step-second';

const props = defineProps<{ formData: ITemplate; ready: boolean }>();
const emit = defineEmits(['update:ready']);

const {
  typeOptions,

  state,
  validateInfos,

  switchTab,
  onTypeChange,

  savingConfig,
  saveCurrentConfig,
} = useStepSecond(props, emit);
</script>

<style scoped lang="less">
.option-item {
  .title {
    margin-bottom: 12px;
    padding: 16px;
    overflow: hidden;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    background: #f7f9fc;
    border-radius: 4px;
    &.error {
      color: var(--ant-error-color);
    }
  }
  .ant-radio-group,
  .ant-checkbox-group {
    display: block;
  }
  .ul-container {
    margin: 0;
    padding-left: 34px;
    overflow: hidden;
    list-style: auto;
    border-left: 2px solid var(--ant-primary-color);
    .check-item {
      position: relative;
      height: 22px;
      padding-left: 2px;
      font-size: 14px;
      line-height: 22px;
      & + .check-item {
        margin-top: 12px;
      }
      .ant-checkbox-wrapper,
      .ant-radio-wrapper {
        position: absolute;
        top: 0;
        right: 0;
        justify-content: flex-end;
        width: 100%;
        margin-right: 16px;
      }
      &:has(.ant-checkbox-wrapper-checked),
      &:has(.ant-radio-wrapper-checked) {
        &::marker {
          color: var(--ant-primary-color);
        }
      }
      .ant-checkbox-wrapper-checked,
      .ant-radio-wrapper-checked {
        & + label {
          color: var(--ant-primary-color);
        }
      }
      &:has(.ant-checkbox-wrapper-disabled),
      &:has(.ant-radio-wrapper-disabled) {
        &::marker {
          color: rgba(6, 21, 51, 0.25);
        }
      }
      .ant-checkbox-wrapper-disabled,
      .ant-radio-wrapper-disabled {
        & + label {
          color: rgba(6, 21, 51, 0.25);
        }
      }
    }
  }
}
</style>
