<template>
  <div class="tag-list">
    <a-tag
      v-for="(tag, index) in tags"
      :key="index"
      color="orange"
      style="margin-bottom: 10px"
    >
      {{ tag }}
    </a-tag>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { IQueryGroupItem } from '../data';

const props = defineProps<{
  /** 路由传参搜索配置 */
  queryGroup?: IQueryGroupItem[];
  /** 传入的值 */
  queryValue?: Record<string, string>;
}>();

/** 路由参数对应的显示文本标签 */
const tags = computed(() => {
  const text: string[] = [];
  props.queryGroup?.forEach(item => {
    const value = props.queryValue?.[item.key];
    if (value && item.text) {
      if (typeof item.text === 'function') {
        text.push(item.text(String(value)));
      } else {
        text.push(item.text);
      }
    }
  });
  return text;
});
</script>

<style lang="less" scoped>
.tag-list {
  width: 100%;
}
</style>
