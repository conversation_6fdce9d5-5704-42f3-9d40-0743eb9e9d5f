import { ref } from 'vue';
import { message } from 'ant-design-vue';

import { GET } from '@/services/api';
const isVideoRegexp = /video\/(3gpp|3gp|mp4|mov|avi|mpeg|webm|mkv|MOV|quicktime)/;
let isSuccess;
// 获取图片上传服务端地址
export const getOssUploadInfo = function (fileType: string) {
  return GET('/common/ossimage', {
    pathName: 'baas',
    file_type: fileType,
  });
};

const useCustomRequest = (video: any, limt: number = 5) => {
  const checkUpload = (file: any) => {
    if (file.size === 0) {
      message.error('上传文件大小不能为0');
      return false;
    }
    const isLimt = file.size / 1024 / 1024 < limt;

    const isVideo = isVideoRegexp.test(file.type);
    if (!isVideo) {
      message.error('请上传视频文件（mp4、mpeg、WEBM 等格式）');
    }
    if (!isLimt) {
      message.error(`文件大小超出指定范围，当前上传的视频大于${limt}M！`);
      return false;
    }
    isSuccess = isVideo;

    return isSuccess;
  };

  // 上传图片路径
  const ossUploadUrl = ref<string>('');
  const confirmLoading = ref<boolean>(false);
  const customRequest = async (option: any) => {
    if (!checkUpload(option.file)) {
      return video.pop();
    }
    confirmLoading.value = true;
    const fileType = option.file.name.split('.')[1];

    // 1. 获取最新的oss资源服务器信息
    const { data: ossData } = await getOssUploadInfo(fileType);
    const fileName = getRandomName() + getSuffix(option.file.name);
    const key = ossData.dir + fileName;
    const uploadParam = {
      key,
      OSSAccessKeyId: ossData.accessid,
      policy: ossData.policy,
      signature: ossData.signature,
      success_action_status: 200,
      dir: ossData.dir,
      file: option.file,
    };
    ossUploadUrl.value = ossData.host;

    const data = new FormData();

    for (const key in uploadParam) {
      data.append(key, uploadParam[key]);
    }

    if (ossUploadUrl.value) {
      fetch(ossUploadUrl.value, {
        method: 'POST',
        body: data,
      })
        .then(res => {
          if (res.status !== 200) {
            message.error('文件上传失败');
            video.pop();
            return;
          }
          option.file.response = {
            url: `${ossData.imgOssServer}/${key}`,
            size: uploadParam.file.size,
            ori_name: fileName,
          };
          option.onSuccess(`${ossData.imgOssServer}/${key}`);
        })
        .catch(err => {
          console.log('err--', err);
          message.error('文件上传失败');
          video.pop();
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  };

  const getSuffix = (filename: string) => {
    // 上传文件后缀
    const pos = filename.lastIndexOf('.');
    let suffix = '';
    if (pos != -1) {
      suffix = filename.substring(pos);
    }
    return suffix;
  };

  const getRandomName = (len = 20) => {
    // 上传文件名
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const maxPos = chars.length;
    let pwd = '';
    for (let i = 0; i < len; i++) {
      pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
  };

  return { customRequest, confirmLoading };
};

export default useCustomRequest;
