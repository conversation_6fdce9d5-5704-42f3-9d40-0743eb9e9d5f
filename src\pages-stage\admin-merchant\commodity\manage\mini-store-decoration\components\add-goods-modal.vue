<template>
  <div>
    <a-modal
      v-model:visible="isShow"
      :confirm-loading="confirmLoading"
      :destroy-on-close="true"
      :title="isEdit ? '编辑' : '添加' + title"
      @cancel="onCancel"
      @ok="onOk"
    >
      <a-form
        ref="form"
        layout="vertical"
        :model="formState"
      >
        <a-form-item
          label="商品Id"
          name="goodsId"
          :rules="[{ required: true, message: '请输入商品ID' }]"
        >
          <a-input
            v-model:value.number="formState.goodsId"
            :allow-clear="true"
            style="width: 214px"
          />
        </a-form-item>
        <a-form-item
          label="商品图片"
          name="goodsPic"
          :rules="[{ required: true, message: '请选择图片' }]"
        >
          <ImageUploadWithImageLibrary
            v-model:value="formState.goodsPic"
            class="image_upload_wrap"
            :height="104"
            :max="1"
            :show-delete="false"
            :show-preview="true"
            :width="104"
          />
          <span class="goods_pic_des">建议图片尺寸：{{ props.options.size }}</span>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed } from 'vue';
import ImageUploadWithImageLibrary from '@/components/image-upload-with-image-library/image-upload-with-image-library.vue';
import { APIVerifyId } from '../services';
import { message } from 'ant-design-vue';
const props = defineProps({
  visible: Boolean,
  title: {
    type: String,
    required: true,
  },
  options: {
    type: Object,
    default: () => ({
      size: '',
      id: '',
      activeGoodsIds: [],
    }),
  },
});
interface IFormState {
  goodsId: string;
  goodsPic: Array<{ dragId: string; src: string }>;
}

const isEdit = computed(() => Boolean(props.options.id || props.options.idx !== -1));

const form = ref();
const confirmLoading = ref(false);
const emit = defineEmits(['update:visible', 'confirm']);
const isShow = ref(false);
const formState = reactive<IFormState>({
  goodsId: '',
  goodsPic: [],
});

const onCancel = () => {
  form.value.resetFields();
  emit('update:visible', false);
};

const onOk = () => {
  form.value.validate().then(async () => {
    if (isEdit.value) {
      if (formState.goodsId !== props.options.id && props.options.activeGoodsIds.includes(formState.goodsId)) {
        return message.warning('将要编辑的商品已存在');
      }
    } else {
      if (props.options.activeGoodsIds.includes(formState.goodsId)) {
        return message.warning('将要添加商品已存在');
      }
    }
    confirmLoading.value = true;
    await APIVerifyId({ spu_id: formState.goodsId })
      .then(() => {
        emit('confirm', { id: formState.goodsId, src: formState.goodsPic[0].src });
        onCancel();
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  });
};
watch(
  () => props.visible,
  val => {
    if (isEdit.value) {
      formState.goodsId = props.options.id;
    } else {
      formState.goodsId = '';
    }
    isShow.value = val;
  },
);
</script>

<style scoped lang="less">
.image_upload_wrap {
  margin-bottom: 8px;
}
.goods_pic_des {
  color: rgba(0, 0, 0, 0.65);
  font-weight: 400;
  font-size: 14px;
}
</style>
