<template>
  <a-modal
    v-model:visible="bindVisible"
    class="review-modal"
    :ok-text="extraData.imei ? '去管控' : '确认'"
    title="锁机售后"
    @ok="submit"
  >
    <template v-if="extraData.imei">
      <div class="row">
        当前设备为锁机设备，点击去管控即可进入管控工作台
      </div>
      <div class="row">
        设备号：{{ extraData.number }}
      </div>
      <div class="row">
        序列号：{{ extraData.imei }}
      </div>
    </template>
    <template v-else>
      <div class="row">
        工作台暂不支持该设备的管控，请人工匹配数据
      </div>
      <div class="row">
        设备号：---
      </div>
      <div class="row">
        序列号：---
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';

import { useRoute } from 'vue-router';
import { computed } from 'vue';

interface IExtraData {
  imei?: string;
  number?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:visible']);
const route = useRoute();
const domain = route.query.origin || window.location.origin;

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

function submit() {
  if (props.extraData.imei) {
    window.open(`${domain}/super/tbl-warehouse/device-data-index?query={"imei":"${props.extraData.imei}"}`);
  }
  bindVisible.value = false;
}
</script>

<style scoped lang="less">
.row + .row {
  margin-top: 8px;
}
</style>
