import { POST, RequestConfig } from '@/services/api';
const config: RequestConfig = {
  headers: { 'content-type': 'application/json;charset=utf-8' },
  hostType: '<PERSON><PERSON><PERSON>',
};
export function getOrderList(data: any) {
  return POST('/merchant/rDI/serverAssessment/appealOrders', data, config);
}

export function ApiSubmitAppeal(data: any) {
  return POST('/merchant/rDI/serverAssessment/appeal', data, config);
}

export function ApiGetRecord(data: any) {
  return POST('/merchant/rDI/serverAssessment/appealDetail', data, config);
}

export function ApiGetOrderDetail(data: any) {
  return POST('/merchant/rDI/serverAssessment/appealOrderDetail', data, config);
}
