<template>
  <div class="open-module">
    <a-spin :spinning="loading">
      <a-row
        v-if="openedList.length > 0"
        :gutter="[0, 16]"
      >
        <a-col
          v-for="item in openedList"
          :key="item.cn_name"
          :span="24"
        >
          <div class="item">
            <div class="item-header">
              <div class="item-header-left">
                <img
                  class="item-header-icon"
                  :src="item.icon"
                >
                <div class="item-header-title">
                  {{ item.cn_name }}
                </div>
              </div>
              <div class="item-header-time">
                到期时间：{{ item.expire_at_txt || '--' }}
              </div>
            </div>
            <div class="item-func">
              <div
                v-for="item2 in item.functionList"
                :key="item2.name"
                class="item-func-content"
              >
                <img src="https://img1.rrzuji.cn/uploads/scheme/2304/17/m/YBj2EUN0fEP0NPR7rp78.png">
                <div class="content-title">
                  {{ item2.name }}
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>

      <REmpty
        v-else
        style="margin-top: 24px"
      />
    </a-spin>
  </div>
</template>
<script setup lang="ts">
import { PropType } from 'vue';

import { OpenModuleItem } from './data';

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  openedList: {
    type: Array as PropType<OpenModuleItem[]>,
    default() {
      return () => [];
    },
  },
});
</script>

<style lang="less" scoped>
.open-module {
  :deep(.ant-col) {
    &:nth-child(1) {
      margin-top: 24px;
    }
  }
  .item {
    padding: 20px 24px 10px;
    background-color: #f9f9fb;

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(6, 21, 51, 0.06);

      &-left {
        display: flex;
        align-items: center;
      }

      &-icon {
        width: 32px;
        margin-right: 16px;
      }

      &-title {
        color: rgba(6, 21, 51, 0.85);
        font-weight: bold;
      }

      &-time {
        color: rgba(6, 21, 51, 0.45);
        font-weight: 400;
      }
    }

    &-func {
      display: flex;
      align-items: center;
      padding: 20px 0;

      &-content {
        display: flex;
        align-items: center;
        margin-right: 30px;

        img {
          width: 23px;
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
