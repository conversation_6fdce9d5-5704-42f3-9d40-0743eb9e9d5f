<template>
  <div
    ref="CustomerTable"
    class="customer-table"
    :class="{ 'is-border': bordered }"
  >
    <div class="ant-table-wrapper">
      <a-spin :spinning="loading">
        <div
          v-if="dataSource && dataSource.length"
          class="ant-table ant-table-fixed-header ant-table-fixed-column ant-table-scroll-horizontal ant-table-has-fix-left"
        >
          <div
            class="ant-table-container"
            :style="{ transform: `translateX(${relativeDistance - x * tableWidthRatio}px)` }"
          >
            <table>
              <thead class="ant-table-header">
                <tr>
                  <th
                    v-for="(item, index) in showColumns"
                    :key="index"
                  >
                    <div
                      class="th"
                      :style="`width: ${
                        totalTableWidth > tableWidth ? item.width + 'px' : 'none'
                      }; justify-content: ${formatAlignStyle(item.align)}`"
                    >
                      <slot
                        :column="item"
                        :index="index"
                        name="headerCell"
                        :title="item.title"
                      >
                        {{ item.title }}
                      </slot>
                      <span
                        v-if="item.sorter"
                        class="ant-table-column-sorter ant-table-column-sorter-full"
                      >
                        <span class="ant-table-column-sorter-inner">
                          <caret-up-outlined
                            class="ant-table-column-sorter-up"
                            @click="sortHandler(item)"
                          />
                          <caret-down-outlined
                            class="ant-table-column-sorter-down"
                            @click="reverseHandler(item)"
                          />
                        </span>
                      </span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody class="ant-table-body">
                <tr
                  v-for="(row, i) in bindListData"
                  :key="i"
                  class="tr"
                >
                  <template
                    v-for="(column, j) in showColumns"
                    :key="j"
                  >
                    <td
                      v-if="
                        row[column.dataIndex === undefined ? column.key : column.dataIndex] &&
                          bindCustomCell[i][column.dataIndex === undefined ? column.key : column.dataIndex].rowSpan !== 0
                      "
                      :align="column.align"
                      :colspan="
                        bindCustomCell[i][column.dataIndex === undefined ? column.key : column.dataIndex].colSpan
                      "
                      :rowspan="
                        bindCustomCell[i][column.dataIndex === undefined ? column.key : column.dataIndex].rowSpan
                      "
                    >
                      <span
                        class="td"
                        :style="{
                          width:
                            totalTableWidth > tableWidth
                              ? colWidth(column) - (j === showColumns.length - 1 ? 2 : 1) + 'px'
                              : 'none',
                        }"
                      >
                        <slot
                          :column="column"
                          :index="i"
                          name="bodyCell"
                          :record="row"
                          :text="row[column.dataIndex === undefined ? column.key : column.dataIndex]"
                        >
                          {{ row[column.dataIndex === undefined ? column.key : column.dataIndex] }}
                        </slot>
                      </span>
                    </td>
                    <td v-if="!row[column.dataIndex === undefined ? column.key : column.dataIndex]" />
                  </template>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            v-if="totalTableWidth > tableWidth"
            class="scroll-bar"
          >
            <div
              class="handle"
              :style="{
                width: Math.max(tableWidth / totalTableWidth, 0) * 100 + '%',
                transform: `translateX(${x}px)`,
              }"
              @mousedown="onMouseDown"
            />
          </div>
        </div>
        <div v-else>
          <a-empty :image="simpleImage" />
        </div>
        <div class="footer">
          <a-pagination
            v-model:current="pageCurrent"
            v-model:page-size="pageSize"
            show-size-changer
            :total="listData.length"
            @show-size-change="pageChange"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, PropType, reactive, ref, watch } from 'vue';
import { Empty } from 'ant-design-vue';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue';

import { Column } from '../data';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  columns: {
    type: Array as PropType<Column[]>,
    default() {
      return [];
    },
  },
  dataSource: {
    type: Array as PropType<any>,
    default() {
      return [];
    },
  },
  bordered: {
    type: Boolean,
    default: false,
  },
});

const listData = ref<any>(props.dataSource);
const CustomerTable = ref<any>(null);
const tableWidth = ref(0);
const startIndex = ref(0);
const endIndex = ref(0);
const showColumns = ref<Column[]>(reactive([]));
const pageCurrent = ref(1);
const pageSize = ref(10);

const bindListData = computed(() => {
  const startIndex = (pageCurrent.value - 1) * pageSize.value;
  const endIndex = pageCurrent.value * pageSize.value;
  return listData.value.slice(startIndex, endIndex);
});

const bindCustomCell = computed(() => {
  const map: Record<
    number,
    Record<
      number | string,
      {
        colSpan?: number;
        rowSpan?: number;
      }
    >
  > = {};
  bindListData.value.forEach((row: any, index: number) => {
    if (!map[index]) map[index] = {};
    props.columns.forEach(column => {
      map[index][column.dataIndex === undefined ? column.key : column.dataIndex] =
        (column.customCell && column.customCell(bindListData.value, index)) || {};
    });
  });

  return map;
});

function pageChange(current: number, size: number) {
  pageCurrent.value = current;
  pageSize.value = size;
}

// 总列表宽度
const totalTableWidth = computed(() => {
  let total = 0;
  for (let i = 0; i < props.columns.length; i++) {
    total += props.columns[i].width || 200;
  }
  return total;
});

// 总列表宽度和列表宽度比例
const tableWidthRatio = computed(() => {
  return totalTableWidth.value / tableWidth.value;
});

// 拖动累计距离
let dragDistance = 0;
// 求该在页面上显示多少表单列
const relativeDistance = ref(0);
function calculateHowManyCol(value: number) {
  if ((dragDistance < 0 && value > 0) || (dragDistance > 0 && value < 0)) dragDistance = 0;
  dragDistance += value;
  if (dragDistance > 0) {
    let needAffixDistance = dragDistance;
    while (needAffixDistance > 0 && endIndex.value + 1 < props.columns.length) {
      const itemWidth = props.columns[endIndex.value + 1].width || 200;
      if (needAffixDistance > itemWidth) {
        endIndex.value++;
        startIndex.value++;
        needAffixDistance -= itemWidth;
        dragDistance -= itemWidth;
        showColumns.value.push(props.columns[endIndex.value]);
        if (startIndex.value > 2) {
          const shiftItem = showColumns.value.shift();
          if (shiftItem) {
            relativeDistance.value += shiftItem.width || 200;
          }
        }
      } else {
        needAffixDistance = 0;
      }
    }
  } else {
    let needDeductDistance = -dragDistance;
    while (needDeductDistance > 0 && startIndex.value - 1 >= 0) {
      const itemWidth = props.columns[startIndex.value].width || 200;
      if (needDeductDistance > itemWidth) {
        endIndex.value--;
        startIndex.value--;
        needDeductDistance -= itemWidth;
        dragDistance += itemWidth;
        if (startIndex.value > 1) {
          showColumns.value.unshift(props.columns[startIndex.value - 2]);
        }
        const popItem = showColumns.value.pop();
        if (popItem && startIndex.value > 1) {
          relativeDistance.value -= props.columns[startIndex.value - 2].width || 200;
        }
      } else {
        needDeductDistance = 0;
      }
    }
  }
}

// 初始化
function init() {
  let needAffixDistance = tableWidth.value * 1.3;
  while (needAffixDistance > 0 && endIndex.value < props.columns.length) {
    const itemWidth = props.columns[endIndex.value].width || 200;
    needAffixDistance -= itemWidth;
    showColumns.value.push(props.columns[endIndex.value]);
    endIndex.value++;
    if (props.columns[endIndex.value] && needAffixDistance <= itemWidth) {
      needAffixDistance -= itemWidth;
      showColumns.value.push(props.columns[endIndex.value]);
      needAffixDistance = 0;
    }
  }
}

// 剩余平均宽度
function colWidth(column: Column) {
  let remainingWidth = tableWidth.value;
  let remainingCount = 0;
  for (let i = startIndex.value; i < endIndex.value; i++) {
    if (props.columns[i].width) {
      remainingWidth -= props.columns[i].width || 0;
    } else {
      remainingCount++;
    }
  }
  if (remainingCount === 0 && remainingWidth > 0) {
    return Math.round(tableWidth.value / endIndex.value - startIndex.value);
  }
  return column.width || remainingWidth / remainingCount;
}

watch(
  () => props.columns,
  () => {
    restoreOriginalState();
  },
);

watch(
  () => props.dataSource,
  () => {
    listData.value = props.dataSource;
    restoreOriginalState();
  },
);

// 开启拖动
let isDrop = false;
// 起点
let startX = 0;
// 偏移值
let x = ref(0);
// 手柄宽度
let handleWidth = 0;

// 拖动滚动条
function onMouseDown(e: any) {
  isDrop = true;
  !startX && (startX = e.screenX);
  handleWidth = e.currentTarget.getBoundingClientRect().width;
  dragDistance = 0;
}

// 拖动中
function onMouseMove(e: any) {
  if (isDrop) {
    // 计算滚动条容器的宽度（等于表格宽度）
    const scrollBarWidth = tableWidth.value;
    // 计算手柄的最大可移动距离（确保手柄不会超出滚动条边界）
    const maxDistance = Math.max(0, scrollBarWidth - handleWidth);
    // 计算当前鼠标相对于起始位置的偏移
    const mouseOffset = e.screenX - startX;
    // 限制拖动距离在有效范围内
    const distance = Math.max(0, Math.min(maxDistance, mouseOffset));
    // 计算表格内容的滚动距离
    const contentScrollDistance = (distance - x.value) * tableWidthRatio.value;
    calculateHowManyCol(contentScrollDistance);
    x.value = distance;
  }
}

// 拖动结束
function onMouseUp() {
  isDrop = false;
}

// 排序处理
function sortHandler(column: Column) {
  listData.value = listData.value.sort(column.sorter);
}
// 颠倒处理
function reverseHandler(column: Column) {
  listData.value = listData.value.sort(column.sorter).reverse();
}
// 恢复原始状态
function restoreOriginalState() {
  startIndex.value = 0;
  endIndex.value = 0;
  isDrop = false;
  startX = 0;
  x.value = 0;
  handleWidth = 0;
  dragDistance = 0;
  relativeDistance.value = 0;
  showColumns.value = [];
  if (CustomerTable.value) {
    tableWidth.value = CustomerTable.value.clientWidth;
  }
  init();
}

// 格式化对其样式
function formatAlignStyle(align?: string) {
  if (align == 'left') return 'flex-start';
  if (align == 'right') return 'flex-end';
  else return 'center';
}

onMounted(() => {
  tableWidth.value = CustomerTable.value.clientWidth;
  init();
  window.addEventListener('mouseup', onMouseUp);
  window.addEventListener('mousemove', onMouseMove);
  window.addEventListener('resize', restoreOriginalState);
});

onUnmounted(() => {
  window.removeEventListener('mouseup', onMouseUp);
  window.removeEventListener('mousemove', onMouseMove);
});
</script>

<style lang="scss">
.ant-table {
  width: 100%;
  overflow-x: hidden;
}
.ant-table table {
  border-collapse: collapse;
}
.ant-table-header {
  th {
    position: relative;
    padding: 0;
    color: #000000d9;
    font-weight: 500;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.3s ease;
  }
  th:not(:last-child)::after {
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 1.6em;
    background-color: #0000000f;
    transform: translateY(-50%);
    transition: background-color 0.3s;
    content: '';
  }
  .th {
    display: flex;
    align-items: center;
    padding: 16px;
  }
}
.customer-table {
  td {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
  }
  .td {
    display: inline-block;
    padding: 14px;
    word-wrap: break-word;
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.scroll-bar {
  width: 100%;
  height: 8px;
  .handle {
    width: 100px;
    height: 100%;
    background-color: rgb(229, 229, 229);
    border-radius: 4px;
    user-select: none;
  }
  .handle:hover {
    background-color: rgb(159, 159, 159);
  }
}
.ant-table-column-sorter {
  cursor: pointer;
  user-select: none;
}
.customer-table.is-border {
  .ant-table-body {
    td + td {
      border-left: 1px solid #f0f0f0;
    }
    td:last-child {
      border-right: 1px solid #f0f0f0;
    }
    tr {
      td:first-child {
        border-left: 1px solid #f0f0f0;
      }
    }
  }
}
</style>
