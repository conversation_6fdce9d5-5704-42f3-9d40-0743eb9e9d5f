import { FormGroupItem } from '@/components/form-create/src/typing';

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'source_type',
    fragmentKey: 'renderSelect',
    originProps: { label: '数据来源', name: 'source_type' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '188px' } },
    url: '/super/recycle/data-count/filter-params',
    optionsFormat: (res: any) => res.data.source_type || [],
  },
];

export const parentColumns = [
  {
    title: '时间',
    key: 'date',
    dataIndex: 'date',
    width: 164,
  },
  {
    title: '回收入库订单数',
    key: 'in_warehouse_num',
    dataIndex: 'in_warehouse_num',
    width: 260,
    tipsText: '统计时间内成功提交生成的全部订单中绑定设备入库过的总订单单数，单个订单号最多只能算1入库订单数',
  },
  {
    title: '回收完成订单数',
    key: 'finish_num',
    dataIndex: 'finish_num',
    width: 260,
    tipsText: '统计时间内成功提交生成的全部订单中状态为交易完成的总订单单数，单个订单号最多只能算1完成订单数',
  },
  {
    title: '回收完成订单金额',
    key: 'finish_price',
    dataIndex: 'finish_price',
    width: 260,
    tipsText: '统计时间内成功提交生成的全部订单中状态为交易完成订单对应已付款账单的定价金额之和',
  },
];

export const childColumns = [
  {
    title: '价格区间',
    key: 'evaluate_price_between',
    dataIndex: 'evaluate_price_between',
    width: 154,
  },
  {
    title: '商品SKU',
    key: 'detail',
    dataIndex: 'detail',
    width: 312,
  },
  {
    title: '回收入库订单数',
    key: 'in_warehouse_num',
    dataIndex: 'in_warehouse_num',
    width: 154,
  },
  {
    title: '回收完成订单数',
    key: 'finish_num',
    dataIndex: 'finish_num',
    width: 154,
  },
  {
    title: '回收完成订单金额',
    key: 'finish_price',
    dataIndex: 'finish_price',
    width: 170,
  },
];
