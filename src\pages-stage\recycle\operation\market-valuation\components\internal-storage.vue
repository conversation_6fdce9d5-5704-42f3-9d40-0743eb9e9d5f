<template>
  <div class="model flex-wrap flex-vertical flex-con">
    <div class="top">
      <div class="top-left">
        <a-button @click="handleAll">
          全部机型
        </a-button>
        <div class="title">
          <a-breadcrumb>
            <a-breadcrumb-item
              v-for="(item, index) in breadCrumbList"
              :key="item.type"
              class="title-label"
              @click="handleBread(index)"
            >
              {{ item.label }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <week-picker
        v-model:value="dateRange"
        class="block"
        @pick="(timeArr: dayjs.Dayjs[]) => onRangeChange(timeArr)"
      />
    </div>
    <a-spin
      :spinning="modelLoading"
      wrapper-class-name="flex-con model-chart"
    >
      <a-row
        :gutter="16"
        :wrap="true"
      >
        <template
          v-for="item in modelChartList"
          :key="item.new_ratio"
        >
          <a-col
            :lg="8"
            :sm="12"
            :xl="6"
            :xs="24"
          >
            <div class="chart-item">
              <div class="item-top">
                <div class="item-top-left">
                  {{ item.new_ratio }}
                </div>
                <div
                  class="item-top-right"
                  @click="handleRoute(item.new_ratio)"
                >
                  <div class="text">
                    详细评估项
                  </div>
                  <RightOutlined
                    class="right-icon"
                    style="width: 12px; height: 12px"
                  />
                </div>
              </div>
            </div>
            <div
              class="chart-container"
              @click="handleRoute(item.new_ratio)"
            >
              <div
                :id="item.chart_key"
                class="chart-container-item"
              />
            </div>
          </a-col>
        </template>
      </a-row>
    </a-spin>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { PropType } from 'vue';
import { message } from 'ant-design-vue';
import { RightOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import type { IBreadCrumb, IMemoryData, IRequestModelParams } from '../data';
import { queryMemoryList } from '../service';
import WeekPicker from '../../../common/components/week-picker.vue';
import { useLineChart } from '../../../common/use-line-chart';

const props = defineProps({
  breadCrumbList: {
    type: Array as PropType<IBreadCrumb[]>,
    default: () => [],
  },
});

const emits = defineEmits(['update:breadCrumbList']);

const dateFormat = 'YYYY-MM-DD';
const dateRange = ref<[Dayjs, Dayjs]>([dayjs(), dayjs()]);
const modelLoading = ref(false);

const modelChartList = ref<IMemoryData[]>([]);

const { render: renderLineChart } = useLineChart();
const generateChart = async (item: any) => {
  const { chart_key } = item;
  const currentContainer = document.querySelector(`#${chart_key}`);
  if (currentContainer) {
    currentContainer.innerHTML = '';
  }
  const source = item.platform_list.flatMap(option => option.platform_data).flat();
  const chart = await renderLineChart({
    container: chart_key,
    source,
    height: 270,
  });
  item.chart = chart;
};

/**
 *
 * @param breadCrumbList 获取型号和内存请求参数
 */
const generateParams = (breadCrumbList: IBreadCrumb[]): IRequestModelParams => {
  const [category_id, brand_id, model_id] = breadCrumbList[0].value;
  const { value: memory } = breadCrumbList[1];
  return {
    category_id,
    brand_id,
    model_id,
    memory,
  };
};

const processQueryChart = async (params: IRequestModelParams) => {
  const { data } = await queryMemoryList(params);
  if (!data.memory_list?.length) {
    message.error('该区间暂无数据');
    return;
  }
  dateRange.value = [dayjs(data.start_date, dateFormat), dayjs(data.end_date, dateFormat)];
  const tempData = data.memory_list.find(item => item.memory === params.memory);
  //返回的是所有内存的数据-找到对应内存
  if (!tempData) {
    message.error('该区间暂无数据');
    return;
  }
  modelChartList.value = tempData.new_ratio_list.map(item => {
    item.chart_key = `r_${Math.ceil(Math.random() * 10e5).toString(36)}`;
    generateChart(item);
    return item;
  });
};

// 图表按日期筛选
const onRangeChange = (timeArr: dayjs.Dayjs[]) => {
  const [start_date, end_date] = timeArr.map(_ => _.format('YYYY-MM-DD'));
  modelChartList.value.length = 0;
  const params = generateParams(props.breadCrumbList);
  params.start_at = start_date;
  params.end_at = end_date;
  processQueryChart(params);
};

//获取图表详情
const fetchCharts = () => {
  const params = generateParams(props.breadCrumbList);
  processQueryChart(params);
};

const handleAll = () => {
  emits('update:breadCrumbList', []);
};

const handleBread = (index: number) => {
  if (index === 1) return;
  const newBreadList = props.breadCrumbList.slice(0, index + 1);
  emits('update:breadCrumbList', newBreadList);
};

//去到详细评估项
const handleRoute = (new_ratio: string) => {
  const newBreadList = JSON.parse(JSON.stringify(props.breadCrumbList));
  newBreadList.push({ type: 'degree', label: new_ratio, value: new_ratio });
  emits('update:breadCrumbList', newBreadList);
};

onMounted(() => {
  fetchCharts();
});
</script>
<style scoped lang="less">
.model {
  background-color: #fff;
  border-radius: 4px;
}
.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 0 16px;
  .top-left {
    display: flex;
    gap: 12px;
    align-items: center;
    .title {
      position: relative;
      padding-left: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;

      .title-label:hover:not(:last-child) {
        color: #00c8be;
        cursor: pointer;
      }

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        background: #00c8be;
        border-radius: 2px;
        transform: translateY(-50%);
        content: '';
      }
    }
  }
}

.model-chart {
  padding: 0 16px;
  .chart-item {
    box-sizing: border-box;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
    .item-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .item-top-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }
    .item-top-right {
      display: flex;
      gap: 4px;
      align-items: center;
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      .right-icon {
        width: 12px;
        height: 12px;
      }
      &:hover {
        color: #00c8be;
        cursor: pointer;
      }
    }
  }
  .chart-box {
    box-sizing: border-box;
    border: 1px solid rgba(6, 21, 51, 0.06);
    border-radius: 4px;
  }
}

.chart-container {
  box-sizing: border-box;
  margin-top: 12px;
  padding: 12px;
  border: 1px solid rgba(6, 21, 51, 0.06);
  border-radius: 4px;
  &:hover {
    border: 1px solid #00c8be;
    box-shadow: 0 0 12px 0 rgba(0, 200, 190, 0.2);
    cursor: pointer;
  }
  .chart-container-item {
    width: 100%;
  }
}
</style>
