<template>
  <a-modal
    v-model:visible="bindVisible"
    :body-style="{ padding: '0', background: '#f2f4f5' }"
    closable
    :footer="null"
    width="100%"
    wrap-class-name="full-modal"
    :z-index="1001"
  >
    <iframe
      ref="iframeRef"
      class="iframe preview"
      :src="link"
    />
  </a-modal>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  link: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});
</script>

<style lang="less" scoped>
.full-modal {
  .ant-modal {
    top: 0;
    max-width: 100%;
    margin: 0;
    padding-bottom: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 54px);
  }

  .ant-modal-body {
    flex: 1;
  }
}

.iframe {
  display: block;
  width: 80%;
  min-width: 800px;
  height: 92vh;
  margin: 16px auto 0;
  border: 0;
}

.preview {
  height: calc(100vh - 54px) !important;
  margin-top: 0 !important;
}
</style>
