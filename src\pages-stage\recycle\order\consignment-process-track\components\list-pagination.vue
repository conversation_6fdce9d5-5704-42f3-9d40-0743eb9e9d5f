<template>
  <div class="pagination-box">
    <div class="total-box">
      <span class="total-num">共<span class="value">{{ total }}</span>条记录</span>
      <span class="page-num">第<span class="value">{{ current }} / {{ Math.ceil((total as number) / (pageSize as number)) }}</span>页</span>
    </div>
    <div class="pagination">
      <a-pagination
        v-model:current="current"
        :page-size="pageSize"
        show-quick-jumper
        :total="total"
        @change="onTableChange({ current: $event, pageSize })"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import type { PropType } from 'vue';
import { PaginationProps } from 'ant-design-vue';
export default defineComponent({
  props: {
    page: {
      type: Object as PropType<PaginationProps>,
      default: () => ({}),
    },
    onTableChange: {
      type: Function,
      default: null,
    },
  },
  setup(props) {
    return {
      ...toRefs(props.page),
    };
  },
});
</script>

<style scoped lang="less">
.pagination-box {
  position: fixed;
  right: 16px;
  bottom: 0;
  left: 16px;
  z-index: 9;
  height: 64px;
  margin: 0;
  padding: 21px 24px;
  background-color: #fff;
  border-top: 1px solid rgba(6, 21, 51, 0.06);
  .ant-pagination-total-text {
    flex: 1;
  }
  .total-box {
    float: left;
    color: rgba(6, 21, 51, 0.45);
    .page-num {
      margin-left: 16px;
    }
    .value {
      margin: 0 4px;
      color: rgba(6, 21, 51, 0.65);
    }
  }
  .pagination {
    float: right;
  }
}
</style>
