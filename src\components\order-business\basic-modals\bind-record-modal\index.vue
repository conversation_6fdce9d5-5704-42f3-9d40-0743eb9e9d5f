<template>
  <a-modal
    v-model:visible="bindVisible"
    class="bind-record-modal height-limit"
    :mask-closable="false"
    title="换绑记录"
    :width="800"
  >
    <a-table
      :columns="columns"
      :data-source="list"
      :pagination="false"
    />
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import type { TableColumnType } from 'ant-design-vue';
import { useVModel } from '@/hook';

export interface IBindRecord {
  change_time: string;
  name: string;
  idcard: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array as PropType<IBindRecord[]>,
    default() {
      return [];
    },
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emit);

const columns: TableColumnType[] = [
  {
    title: '关联时间',
    dataIndex: 'change_time',
    key: 'change_time',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '身份证',
    dataIndex: 'idcard',
    key: 'idcard',
  },
];
</script>
