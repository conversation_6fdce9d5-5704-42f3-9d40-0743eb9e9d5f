import { ColumnProps } from 'ant-design-vue/lib/table';

export const columns: ColumnProps = [
  {
    dataIndex: 'date',
    title: '时间',
    width: 212,
  },
  {
    dataIndex: 'commit_num',
    title: '回收订单单数',
    width: 180,
    tipsText: '【统计时间内成功提交生成的总订单单数】，更新频率【实时更新】，数据源【对应数据源】。',
  },
  {
    dataIndex: 'in_warehouse_num',
    title: '回收入库订单单数',
    width: 180,
    tipsText:
      '【统计时间内全部订单中发生绑定设备入库过的总订单单数，单个订单号最多只能算1入库订单数】，更新频率【实时更新】，数据源【对应数据源】。',
    equipmentTipsText:
      '【统计时间内成功提交生成的总订单的设备数】，更新频率【实时更新】，数据源【订单设备查询表数据源】',
  },
  {
    dataIndex: 'finish_num',
    title: '回收完成订单单数',
    width: 180,
    tipsText:
      '【统计时间内成全部订单中状态变更为交易完成的总订单单数，单个订单号最多只能算1完成订单数】，更新频率【实时更新】，数据源【对应数据源】。',
  },
  {
    dataIndex: 'purchased_price_num',
    title: '回收完成订单金额',
    width: 180,
    tipsText:
      '【统计时间内成功提交生成的全部订单中状态为交易完成订单对应已付款账单的定价金额之和】，更新频率【实时更新】，数据源【对应数据源】。',
  },
  {
    dataIndex: 'return_num',
    title: '回收退货订单数',
    width: 180,
    tipsText:
      '【统计时间内成全部订单中绑定设备入库过且被用户拒收或被用户退货的总订单单数】，更新频率【实时更新】，数据源【对应数据源】。',
  },
];
