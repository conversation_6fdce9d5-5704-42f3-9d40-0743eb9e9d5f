import { TableColumnType } from 'ant-design-vue';
import { IRefundInfoList, IPayInfoList } from './data';
import { AuditStatus } from '@/pages-stage/recycle/clue-manage/config';
import dayjs from 'dayjs';

const status = {
  1: '退款成功',
  2: '退款失败',
};

const backarray = ['', '#00C8BE', '#FF3141'];

export const payInfoColumns: TableColumnType<IPayInfoList>[] = [
  {
    title: '支付流水号',
    key: 'payment_flow_sn',
    dataIndex: 'payment_flow_sn',
    width: 149,
  },
  {
    title: '支付时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 116,
    customRender: ({ value }: { value: number }) => {
      return dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '实付金额',
    key: 'amount',
    dataIndex: 'amount',
    width: 100,
  },
  {
    title: '支付方式',
    key: 'pay_type_text',
    dataIndex: 'pay_type_text',
    width: 104,
  },
  {
    title: '操作人',
    key: 'username',
    dataIndex: 'username',
    width: 74,
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 60,
  },
];

export const refundInfoColumns: TableColumnType<IRefundInfoList>[] = [
  {
    title: '退款流水号',
    key: 'payment_flow_sn',
    dataIndex: 'payment_flow_sn',
    width: 149,
  },
  {
    title: '退款时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 116,
    customRender: ({ value }: { value: number }) => {
      return dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '退款金额',
    key: 'amount',
    dataIndex: 'amount',
    width: 100,
  },
  {
    title: '退款状态',
    key: 'status',
    dataIndex: 'status',
    width: 104,
    customRender: ({ value }: { value: number }) => {
      return AuditStatus({ status, backarray, value });
    },
  },
  {
    title: '操作人',
    key: 'username',
    dataIndex: 'username',
    width: 74,
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 60,
  },
];
