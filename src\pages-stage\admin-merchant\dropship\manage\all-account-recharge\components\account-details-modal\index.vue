<template>
  <a-modal
    v-model:visible="detailsModalVisible"
    :cancel-button-props="{ style: { display: 'none' } }"
    ok-text="确定"
    :title="itemDetails.acc_name"
    width="648px"
    @ok="handleOk"
  >
    <a-alert
      v-if="Number(itemDetails.acc_amount) < Number(itemDetails.remind_amount)"
      banner
      :message="`账户余额低于${itemDetails.remind_amount}元，请尽快前往充值，避免影响代发功能。`"
      style="margin-bottom: 12px"
    />
    <div class="item-body">
      <div class="item">
        <div class="left-item line-r">
          <div class="title">
            账户余额(元)
          </div>
          <div class="price">
            {{ itemDetails.acc_amount?.split('.')[0] ?? '0' }}
            <div class="price-small">
              . {{ itemDetails.acc_amount?.split('.')[1] ?? '00' }}
            </div>
          </div>
        </div>
        <div
          class="left-item"
          style="padding-left: 24px"
        >
          <div class="title">
            已下发待出账金额(元)
          </div>
          <div class="price">
            {{ itemDetails.freeze_amount?.split('.')[0] ?? '0' }}
            <div class="price-small">
              . {{ itemDetails.freeze_amount?.split('.')[1] ?? '00' }}
            </div>
          </div>
        </div>
      </div>
      <div class="commodity-box">
        <div class="title">
          可下发商品
        </div>
        <div class="commodity-list">
          <div class="commodity-type-new">
            {{ itemDetails.newness === '1' ? '全新' : '二手' }}
          </div>
          <div class="list-box">
            <div
              v-for="(item, index) in itemDetails.categories"
              :key="index"
              class="tag-box"
            >
              {{ item.category_name }}｜{{ item.brand_name }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="itemDetails.acc_tips?.length"
        class="tips-box"
      >
        <span>限制条件: </span>
        <span
          v-for="(items, indexs) in itemDetails.acc_tips"
          :key="indexs"
        >{{ indexs + 1 }}.{{ items }}；</span>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import type { IItemDetails } from '../../data';
import { PropType } from 'vue';
import { useVModel } from '@/hook';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  itemDetails: {
    type: Object as PropType<IItemDetails>,
    default: {},
  },
  type: {
    type: String,
    default: 'platform',
  },
});

const emits = defineEmits(['update:visible']);

const detailsModalVisible = useVModel(props, 'visible', emits);

function handleOk() {
  detailsModalVisible.value = false;
}
</script>

<style scoped lang="less">
.item-body {
  display: flex;
  flex-direction: column;
  padding: 16px 24px 28px;
  background: #f9f9fb;
  border-radius: 4px;

  .item {
    display: flex;
    border-bottom: 1px solid #f0f1f3;

    .left-item {
      display: flex;
      flex: 1;
      flex-direction: column;

      .title {
        margin-bottom: 6px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }

      .price {
        display: flex;
        margin-bottom: 17px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;

        .price-small {
          margin-top: 7px;
          color: rgba(6, 21, 51, 0.85);
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
        }
      }
    }

    .line-r {
      position: relative;
    }

    .line-r::before {
      position: absolute;
      right: 0;
      bottom: 16px;
      width: 1px;
      height: 60px;
      background: #f0f1f3;
      content: '';
    }
  }
}

.commodity-box {
  display: flex;
  flex-direction: column;
  padding: 16px 0 8px;
  border-bottom: 1px solid #f0f1f3;

  .title {
    margin-bottom: 6px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }

  .commodity-list {
    display: flex;

    .commodity-type-new {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 66px;
      height: 22px;
      color: #52c41a;
      font-weight: 400;
      font-size: 12px;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 4px;
    }

    .list-box {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      margin-left: 8px;
      padding-left: 9px;
      border-left: 1px solid #f0f1f3;

      .tag-box {
        margin-right: 8px;
        margin-bottom: 8px;
        padding: 0 8px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 12px;
        line-height: 22px;
        background: #fff;
        border: 1px solid rgba(6, 21, 51, 0.15);
        border-radius: 4px;
      }
    }
  }
}

.tips-box {
  display: flex;
  flex-direction: column;
  padding-top: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
</style>
