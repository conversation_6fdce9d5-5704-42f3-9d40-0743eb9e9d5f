<template>
  <a-modal
    v-model:visible="bindVisible"
    title="修改租期"
    :width="640"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <div
        class="reminder-card"
        style="position: relative"
      >
        <div class="reminder-card-header">
          <img
            alt="修改须知"
            src="https://img-t1.rrzuji.cn/uploads/scheme/2304/07/m/c9pmSiAd7L9fS0Oz6d2p.png"
            style="margin-right: 5px"
          >
          <span style="color: rgb(6, 21, 51); font-weight: 500">修改须知</span>
        </div>
        <div class="reminder-card-content">
          <div style="padding-bottom: 10px">
            如与实际签收不符可对订单租期进行修改，修改规则：
          </div>
          <div style="padding-bottom: 2px">
            1.新起租日必须大于旧起租日；修改后的账单账期将按照新起租日与旧起租日的差值进行顺延，已支付的账单账期不受影响。
          </div>
          <div
            v-if="!isSuper"
            style="padding-bottom: 2px"
          >
            2.如商家认为当前起租日需要提前，请点击「起租日申诉」提交工单，平台将根据提供的资料酌情处理。
          </div>
        </div>
      </div>
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="起租日修改为"
          name="time_start"
        >
          <div class="value">
            <a-date-picker
              v-model:value="formData.time_start"
              :disabled-date="disabledDate"
              style="width: 220px"
              value-format="YYYY-MM-DD"
              @change="handleChangeTenancyTerm"
            />
            <!--  起租日申诉（仅商家可用）  -->
            <a-button
              v-if="!isSuper"
              type="link"
              @click="showComplaintModal"
            >
              起租日申诉
            </a-button>
          </div>
          <div class="hint-text">
            当前订单起租日【{{ extraData.time_start }}】
          </div>
        </a-form-item>
        <a-form-item
          label="完租日更新为"
          name="time_end"
        >
          <div class="time-end">
            {{ formData.time_end }}
          </div>
          <div
            class="hint-text"
            style="padding-bottom: 0"
          >
            当前订单完租日【{{ extraData.time_end }}】
          </div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>

  <ComplaintDrawer
    v-model:visible="complaintDrawerVisible"
    :order-id="orderId"
  />
</template>

<script setup lang="ts">
import { PropType, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { FormInstance, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { fetchGateway } from '@/components/order-business/basic-modals/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

import ComplaintDrawer from './components/complaint-drawer.vue';

interface IExtraData {
  item_id?: string;
  time_start?: string;
  time_end?: string;
}

interface IFormData {
  item_id?: string;
  time_start?: string;
  time_end?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({
      item_id: '',
      time_start: '',
      time_end: '',
    }),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const isSuper = useRoute().query.role === 'super';
const formData = reactive<IFormData>({
  item_id: '',
  time_start: '',
  time_end: '',
});
const bindVisible = useVModel(props, 'visible', emit);

const formRef = ref<FormInstance>();
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      // 初始化表单
      Object.assign(formData, props.extraData);
    }
  },
  { immediate: true },
);

const rules = {
  time_start: [
    {
      required: true,
      validator: (rule: any, value: string, callback: any) => {
        if (!/\d{4}-\d{2}-\d{2}/.test(value)) {
          return callback(new Error('开始租期不能为空'));
        }
        if (formData.time_start === props.extraData.time_start && formData.time_end === props.extraData.time_end) {
          return callback(new Error('租期没有做出改动'));
        }
        if (!/^20[12]\d{1}-(0\d{1}|1[012])-([012]\d{1}|3[01])$/.test(value)) {
          return callback(new Error('起始租期格式有误或超出范围允许修改范围'));
        }
        const start = dayjs(value, 'YYYY-MM-DD');
        const end = dayjs(formData.time_end, 'YYYY-MM-DD');
        if (end.isBefore(start)) {
          return callback(new Error('起始日期不能大于结束日期'));
        }
        if (end.diff(start, 'year') >= 5) {
          return callback(new Error('租期暂不支持超过五年'));
        }
        const orderStart = dayjs(props.extraData.time_start, 'YYYY-MM-DD');
        if (Math.abs(orderStart.diff(start, 'day')) > 31) {
          return callback(new Error('只能推移31天内'));
        }
        callback();
      },
      trigger: ['blur'],
    },
  ],
  time_end: [
    {
      required: true,
      validator: (rule: any, value: string, callback: any) => {
        if (!/\d{4}-\d{2}-\d{2}/.test(value)) {
          return callback(new Error('结束租期不能为空'));
        }
        callback();
      },
      trigger: ['blur'],
    },
  ],
};

//限制当天之前的日期不可选
const disabledDate = (current: any) => {
  return current && current < dayjs(props.extraData.time_start).subtract(1, 'days').endOf('day');
};

function handleChangeTenancyTerm(value: string) {
  if (value) {
    const addDay = dayjs(props.extraData.time_end, 'YYYY-MM-DD').diff(
      dayjs(props.extraData.time_start, 'YYYY-MM-DD'),
      'day',
    );
    formData.time_end = dayjs(value, 'YYYY-MM-DD').add(addDay, 'day').format('YYYY-MM-DD');
  } else {
    formData.time_end = dayjs().format('YYYY-MM-DD');
  }
}

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      item_id: formData.item_id,
      time_start: formData.time_start,
      time_end: formData.time_end,
      orderId: props.orderId,
      method: 'v2.order.edit.tenancy',
    };
    fetchGateway(params)
      .then(() => {
        message.success('操作成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data', 'all_remark']);
      })
      .finally(() => (loading.value = false));
  });
}

// ============================  起租日申诉  ============================
const complaintDrawerVisible = ref(false);

function showComplaintModal() {
  bindVisible.value = false;
  complaintDrawerVisible.value = true;
}
</script>

<style scoped lang="less">
.reminder-card {
  margin-bottom: 20px;
  padding: 12px 16px;
  color: rgba(6, 21, 51, 1);
  font-weight: 400;
  background-color: #f0f7ff;
  border: 1px solid #b3d2ff;
  border-radius: 2px;
}

.reminder-card-header {
  padding-bottom: 6px;
}

.reminder-card-content {
  padding-left: 20px;
  line-height: 22px;
}

.hint-text {
  margin-top: 8px;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 12px;
}

.time-end {
  margin-top: 5px;
  color: rgb(6, 21, 51);
  font-weight: 400;
}
</style>
