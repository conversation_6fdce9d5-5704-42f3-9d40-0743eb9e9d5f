<template>
  <a-modal
    v-model:visible="bindVisible"
    class="ant-link-modal"
    :ok-button-props="{ loading: loading, disabled: !day }"
    :title="modalType === 'one' ? '修改当期扣款日' : '批量修改每期扣款日'"
    :width="640"
    @cancel="cancel"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <a-alert
        :message="`当前租期为：${formatTimestampToDate(extraData.time_start)}至${formatTimestampToDate(
          extraData.time_end,
        )}，请在当前时间区间内修改`"
        show-icon
        type="info"
      />
      <div class="content">
        <span v-if="modalType === 'one'">将当期扣款日从 {{ expectAt }} 修改为当期</span>
        <span v-else>将每期扣款日从每期 {{ expectAt }} 修改为每期</span>
        <a-input-number
          id="inputNumber"
          v-model:value="day"
          :max="28"
          :min="1"
        />
        号
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, PropType, ref } from 'vue';
import { message } from 'ant-design-vue';

import type { TRefreshDataKey } from '@/components/order-business/typing';

import { fetchGateway } from './services';

interface IExtraData {
  server_id?: number | string;
  /** 设置的地址id */
  ret_addr_id?: number | string;
  expect_at: number;
  time_start: number;
  time_end: number;
  bill_id: number | string;
  expect_date: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  modalType: {
    type: String as PropType<'one' | 'all'>,
    default: 'all',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

const expectAt = computed(() => {
  if (props.extraData.expect_at) {
    return getDayFromTimestamp(Number(props.extraData.expect_at));
  }

  return '';
});

const loading = ref(false);
const day = ref<number | null>();

async function submit() {
  if (!day.value) return;
  try {
    loading.value = true;
    await fetchGateway({
      order_id: props.orderId,
      method: 'v2.bill.antchain.bill.expect',
      type: props.modalType,
      day: day.value,
      bill_id: props.extraData.bill_id,
    });
    emit('refresh', props.orderId, ['data']);
    message.success('操作成功');
    cancel();
  } finally {
    loading.value = false;
  }
}

function cancel() {
  bindVisible.value = false;
  day.value = null;
}

function getDayFromTimestamp(timestamp: number) {
  // 将时间戳转换为毫秒，并创建 Date 对象
  const date = new Date(timestamp * 1000);

  // 获取日期的 "日" 部分
  const day = date.getDate();

  return `${day}号`;
}

function formatTimestampToDate(timestamp: number): string {
  // 如果是毫秒级时间戳，转换为秒级
  if (timestamp > 1e10) {
    timestamp = Math.floor(timestamp / 1000);
  }

  const date = new Date(timestamp * 1000); // 转换为 Date 对象
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}年${month}月${day}日`;
}
</script>

<style scoped lang="less">
.content {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 16px;
}
</style>
