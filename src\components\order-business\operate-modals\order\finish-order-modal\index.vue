<template>
  <a-modal
    v-model:visible="modelVisible"
    class="revoke-modal"
    title="交易完成"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <div class="mark">
        <div
          class="prompt"
          style="text-align: center"
        >
          {{ extraData?.confirm_text || '若商家尚未确认设备归还，提交后将视为已收回所有设备，完结不可恢复！' }}
        </div>
        <div
          v-if="getCompletionState"
          class="prompt"
          style="width: 80%; color: red"
        >
          注：当前订单存在未支付的账单。若点击交易完成，未支付的账单将变为已关闭状态
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { PropType, watch } from 'vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { MessageType } from 'ant-design-vue/lib/message';

import useCancelInsurance from '@/components/order-business/composables/use-cancel-insurance';
import { operateModal } from '@/components/order-business/operate-modals';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useUnityModal } from '@/components/unity-modal';
import { useVModel } from '@/hook';
import { POST } from '@/services/api';

interface IExtraData {
  rental_has_pay?: number | string;
  deposit_has_pay?: number | string;
  deposit_has_refund?: number | string;
  pay_status?: number;
  deposit_money?: number | string;
  rental_need_pay?: number | string;
  created_by?: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);
const cancelInsurance = useCancelInsurance();

const loading = ref(false);
const isSuper = useRoute().query.role === 'super';
const modelVisible = ref(false);

// 支付分订单显示文案
const getCompletionState = computed(() => {
  return props.extraData.is_show_wx_zff_tip;
});

const { onToggleComponent } = useUnityModal();
const closeLoading = ref<MessageType>();

function submit() {
  closeLoading.value = message.loading('加载中...');
  loading.value = true;
  const superMethod = props.extraData.pay_status === 23 ? 'order.opera.wxcomplete' : 'order.opera.complete';
  POST('/api/gateway', {
    method: isSuper ? superMethod : 'order.opera.business.complete',
    order_id: props.orderId,
    ordinary_delivery: 1,
  })
    .then(res => {
      const { type, crm } = res.data;
      modelVisible.value = false;
      emit('refresh', props.orderId, ['data', 'all_remark']);
      if (!isSuper && type) {
        // 商家端 && 补订单完结
        Modal.confirm({
          title: '温馨提示',
          content: crm,
          onOk() {
            onToggleComponent(operateModal.posLeaseAssistanceApplySuppleOrderComplete, {
              orderId: props.orderId,
              modalType: 12,
              extraData: { created_by: Number(type) },
            });
          },
        });
      } else message.success(crm);
      if (res.data.cancellationInsuranceBtn) {
        cancelInsurance.confirmCancel({
          orderId: props.orderId,
          content: res.data.cancellationInsuranceMsg,
          successCallback: () => emit('refresh', props.orderId, ['data', 'all_remark']),
        });
      }
    })
    .finally(() => {
      loading.value = false;
      closeLoading.value?.();
    });
}

/** 补订单类型 */
const suppleCreatedBy = [21, 22, 23, 24, 25, 26, 27, 28, 41, 42, 43, 44, 45, 46, 47, 48];

watch(
  bindVisible,
  value => {
    if (value) {
      bindVisible.value = false;
      if (!suppleCreatedBy.includes(props.extraData.created_by)) {
        modelVisible.value = true;
        return;
      }
      if ([26, 46].includes(props.extraData.created_by)) {
        Modal.confirm({
          title: '温馨提示',
          content: '是否确认完结该补订单（完结补订单不影响主订单的订单状态）？',
          onOk: submit,
        });
      } else submit();
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="less">
.prompt {
  margin-top: 8px;
}

.mark {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
