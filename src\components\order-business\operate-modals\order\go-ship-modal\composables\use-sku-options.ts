import { reactive, Ref } from 'vue';

import { getModal, getSkuInfo } from '../services';

export default function (category_id: Readonly<Ref<string>>, brand_id: Readonly<Ref<string>>) {
  const InitClassifyOptions = {
    modal: null,
    modalOptions: [],
    attrList: [],
  };

  const classifyOptions = reactive({ ...InitClassifyOptions });

  const getModelOptions = () => {
    getModal({
      category_id: category_id.value,
      brand_id: brand_id.value,
    }).then(({ data }: any) => {
      const { spuRelation } = data;
      classifyOptions.modalOptions =
        spuRelation.children
          .find((item: any) => item.id == category_id.value)
          ?.children.find((item: any) => item.id == brand_id.value)
          ?.children.map((item: any) => ({
            label: item.title,
            value: item.id,
          })) || [];
    });
  };

  const getSkuOptions = () => {
    getSkuInfo({
      category_id: category_id.value,
      brand_id: brand_id.value,
      model_id: classifyOptions.modal,
    }).then(({ data }: any) => {
      const { attr_list } = data;
      classifyOptions.attrList = attr_list?.map((item: any) => {
        return {
          id: item.id,
          name: item.name,
          value: null,
          options: item.attr_items.map((sku: any) => ({ label: sku.name, value: sku.id })),
        };
      });
    });
  };

  const clearClassifyOptions = () => Object.assign(classifyOptions, ...InitClassifyOptions);
  return {
    classifyOptions,
    getModelOptions,
    getSkuOptions,
    clearClassifyOptions,
  };
}
