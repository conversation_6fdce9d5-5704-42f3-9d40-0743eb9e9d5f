import { FormGroupItem } from '@/components/form-create/src/typing';
import { ColumnType } from 'ant-design-vue/lib/table';
import dayjs from 'dayjs';
export const columns: ColumnType[] = [
  {
    title: '商品id',
    key: 'goods_id',
    dataIndex: 'goods_id',
    width: 74,
  },
  {
    title: '商品名称',
    key: 'goods_name',
    dataIndex: 'goods_name',
    width: 251,
  },
  {
    title: '降价趋势',
    key: 'ratio',
    dataIndex: 'ratio',
    width: 158,
  },
  {
    title: '周期',
    key: 'days',
    dataIndex: 'days',
    width: 103,
  },
  {
    title: '创建时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 173,
    customRender: ({ value }: { value: number }) => {
      return dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '使用状态',
    key: 'status',
    dataIndex: 'status',
    width: 98,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 266,
    fixed: 'right',
  },
];
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'goods_name',
    originProps: { label: '商品搜索', name: 'goods_name' },
    elProps: { allowClear: true, placeholder: '请输入' },
    fragmentKey: 'renderInput',
  },
];
export const reduceOptions = [
  {
    label: '3天',
    value: '3',
  },
  {
    label: '7天',
    value: '7',
  },
  {
    label: '15天',
    value: '15',
  },
  {
    label: '30天',
    value: '30',
  },
];

export const recordColumns: ColumnType[] = [
  {
    title: '操作内容',
    key: 'remark',
    dataIndex: 'remark',
    width: 432,
  },
  {
    title: '操作人',
    key: 'remark_by',
    dataIndex: 'remark_by',
    width: 100,
  },
  {
    title: '备注时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 220,
  },
];
