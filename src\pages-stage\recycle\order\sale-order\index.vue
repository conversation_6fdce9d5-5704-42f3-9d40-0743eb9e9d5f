<template>
  <layout-admin-page
    :navs="['趣回收', '订单管理', '销售订单']"
    title="销售订单"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="exportAction"
      >
        <template #icon>
          <export-outlined />
        </template>
        导出查询结果
      </a-button>
    </template>
    <div class="container recycle-sale-order-container">
      <div class="search-bar pb-0">
        <!-- 搜索 -->
        <search-box
          v-model:value="searchParams"
          :search-form-group="searchFormGroup"
          @fetch-table="fetchTable"
          @reset-table="resetTable"
        />
      </div>
      <a-divider :style="{ margin: '15px 0 0 0' }" />
      <div class="tabs">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane
            v-for="item in searchOptions.orderStatusOptions"
            :key="item.value"
            :tab="item.label"
          />
        </a-tabs>
      </div>
      <tab-list
        ref="tabListRef"
        :search-params="searchParams"
      />
    </div>
  </layout-admin-page>
</template>

<script setup lang="ts">
import { ComputedRef, onMounted, reactive, ref, watch } from 'vue';
import { searchOptions, searchFormGroup } from './components/tab-list/config';
import { TabList } from './components/tab-list/component-imports';
import { ISearchParams } from './components/tab-list/data';
import { asyncEduce } from '@/utils/educe';
import { useRoute } from 'vue-router';
import { isEmpty } from 'lodash-es';
import { message } from 'ant-design-vue';
import { getTopWindowUrl } from '@/utils/url';
import { ExportOutlined } from '@ant-design/icons-vue';
import SearchBox from './components/search-box/index.vue';

const route = useRoute();

const tabListRef = ref<InstanceType<typeof TabList> | null>(null);

const searchParams = reactive<Partial<ISearchParams>>({
  category: [],
  source_type: [],
  create_time: [],
});

const fetchTable = async (bindValue: ComputedRef<Partial<ISearchParams>>) => {
  try {
    hasLoad.value = true;
    // 联动更改订单状态
    activeKey.value = bindValue.value.order_status as string;
    await tabListRef.value?.getTableList();
  } finally {
    hasLoad.value = false;
  }
};

// 导出
const exportAction = async () => {
  if (Object.values(searchParams).every(item => isEmpty(item)) && isEmpty(searchParams.create_time)) {
    return message.warn('请选择筛选条件或时间');
  }
  await asyncEduce(
    '/super/recycle/sale-order/list-export',
    tabListRef.value?.formatPrams(searchParams),
    route.query.origin as string,
  );
};

// 重置
const resetTable = async () => {
  try {
    hasLoad.value = true;
    // 联动更改订单状态
    activeKey.value = '';
    await tabListRef.value?.getTableList();
  } finally {
    hasLoad.value = false;
  }
};

// tab key
const activeKey = ref('');

const hasLoad = ref(false);

watch(
  () => activeKey.value,
  (val: string) => {
    if (!hasLoad.value) {
      searchParams.order_status = val;
      tabListRef.value?.getTableList();
    }
  },
  { immediate: true },
);

onMounted(async () => {
  const { params } = await getTopWindowUrl({ getParams: true });
  if (params?.rsp_id) {
    searchParams.rs_order_id = params?.rsp_id;
    tabListRef.value?.getTableList();
  }
});
</script>

<style lang="less" scoped>
@import '../../common/base.less';

.pb-0.search-bar {
  padding-bottom: 0;
}

.tabs {
  padding-left: 25px;
  font-weight: 500;
  font-size: 14px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 24px;
  }
}
</style>
