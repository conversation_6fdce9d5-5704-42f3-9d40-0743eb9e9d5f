<template>
  <a-tooltip
    v-model:visible="setVisible"
    color="#FFF"
    overlay-class-name="r-table-column-set-tooltip"
    placement="bottomLeft"
    trigger="click"
  >
    <template #title>
      <div class="r-table-column-set">
        <div class="r-table-column-set-title">
          列展示
        </div>
        <div class="r-table-column-set-tip">
          拖拽即可排列表格列显示的先后顺序
        </div>
        <VueDraggableNext
          :animation="300"
          chosen-class="r-table-column-set-chosen"
          class="r-table-column-set-list"
          draggable=".mover"
          filter=".unmover"
          :force-fallback="true"
          ghost-class="r-table-column-set-ghost"
          handle=".mover"
          :list="allCols"
        >
          <div
            v-for="element in allCols"
            :key="element.dataIndex"
            :class="[
              'r-table-column-set-item',
              ['action'].includes(element.dataIndex) || !element.show ? '.unmover' : 'mover',
            ]"
          >
            <div style="flex: 1; user-select: none">
              <a-checkbox
                v-model:checked="element.show"
                :disabled="['action'].includes(element.dataIndex)"
              >
                {{ element?.title || '--' }}
              </a-checkbox>
            </div>
            <HolderOutlined class="draggable-icon" />
          </div>
        </VueDraggableNext>
        <div class="r-table-column-set-bottom">
          <a-button
            color="#3777ff"
            size="small"
            type="link"
            @click="resetColumn"
          >
            重置
          </a-button>
        </div>
      </div>
    </template>
  </a-tooltip>
  <a-button
    class="r-btn-link"
    type="link"
    @click="showSet"
  >
    <SettingOutlined />
    <span style="margin-inline-start: 4px">列设置</span>
  </a-button>
</template>
<script setup lang="ts">
import { nextTick, PropType, ref, watch } from 'vue';
import { VueDraggableNext } from 'vue-draggable-next';
import { message } from 'ant-design-vue';
import { HolderOutlined, SettingOutlined } from '@ant-design/icons-vue';

import { deepClone } from '@/utils/base';

import { TDragItem, ToriColItem } from './index';

const props = defineProps({
  oriCols: {
    type: Array as PropType<ToriColItem[]>,
    default: [],
    required: true,
  },
  /** 建议使用路由 */
  localKey: {
    type: String,
    default: [],
    required: true,
  },
});
const emit = defineEmits(['update:oriCols', 'change']);
const allCols = ref<TDragItem[]>([]);

let oriColsList: ToriColItem[] = [];
const oriColsMap: Record<string, ToriColItem> = {};
// 获取首次的初始值
const stopWatch = watch(
  () => props.oriCols,
  (val: { dataIndex: string; label: string; [props: string]: any }[]) => {
    // 有些cols异步操作length初始值为0
    if (val.length > 0) {
      const localChoice = localStorage.getItem(props.localKey) && JSON.parse(localStorage.getItem(props.localKey));
      oriColsList = deepClone(val).map(item => {
        oriColsMap[item.dataIndex] = item;
        return {
          dataIndex: item.dataIndex,
          title: item.title,
          show: true,
        };
      });
      nextTick(() => {
        allCols.value = deepClone(localChoice || oriColsList);
        stopWatch();
      });
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => allCols.value,
  val => {
    localStorage.setItem(props.localKey, JSON.stringify(val));
    emit(
      'update:oriCols',
      val.filter(item => item.show).map(item => oriColsMap[item.dataIndex]),
    );
  },
  {
    deep: true,
  },
);

const setVisible = ref(false);

function showSet() {
  setVisible.value = true;
}

const resetColumn = () => {
  resetColumnValue();
  message.success('重置成功');
  setVisible.value = false;
};

/**
 * 重置
 * */
function resetColumnValue() {
  allCols.value = deepClone(oriColsList);
}
</script>
