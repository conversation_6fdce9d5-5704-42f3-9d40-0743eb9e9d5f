import { reactive, ref } from 'vue';
import dayjs from 'dayjs';

import { GET } from '@/services/api';
import { apiGetPerformanceTargetList } from './services';
import type { RTableColumnType } from 'rrz-web-design';
import type { IPageLayoutConfig, TR } from '../data-board-common-components/data';

export const getPageBaseConfig = () => {
  const title = ref('销售业绩目标');
  const pageLayoutConfig = reactive<IPageLayoutConfig>({
    navs: ['订单', '主管销售业绩', title.value],
    title: title.value,
  });

  const columns: RTableColumnType[] = [
    {
      title: '销售部门',
      dataIndex: 'dept_id',
      valueType: 'select',
      hideInTable: true,
      formFieldProps: {
        setOption: async () => {
          const { data } = await GET('/super/salesData/data/dept-list');
          return data.map((item: any) => ({
            label: item.name,
            value: item.id,
          }));
        },
        elProps: {
          allowClear: true,
          placeholder: '请选择销售部门',
        },
      },
    },
    {
      title: '时间',
      dataIndex: 'month',
      hideInTable: true,
      valueType: 'monthPicker',
    },
    {
      title: '销售部门',
      dataIndex: 'dept_name',
      hideInSearch: true,
      width: 146,
    },
    {
      title: '时间',
      dataIndex: 'month',
      hideInSearch: true,
      width: 100,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return dayjs(a.month).valueOf() - dayjs(b.month).valueOf();
      },
    },
    {
      title: '目标业绩',
      dataIndex: 'target',
      hideInSearch: true,
      width: 221,
    },
    {
      title: '实现业绩',
      dataIndex: 'finish_target',
      hideInSearch: true,
      width: 221,
    },
    {
      title: '目标达成率',
      dataIndex: 'finish_rate',
      hideInSearch: true,
      width: 104,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      hideInSearch: true,
      width: 180,
      valueType: 'date',
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return dayjs(a.updated_at).valueOf() - dayjs(b.updated_at).valueOf();
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 140,
    },
  ];

  const performanceTargetTableConfig: TR = {
    api: apiGetPerformanceTargetList,
    columns,
  };

  return {
    pageLayoutConfig,
    title,
    performanceTargetTableConfig,
  };
};
