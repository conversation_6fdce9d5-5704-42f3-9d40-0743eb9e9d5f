import type { FormGroupItem } from 'rrz-web-design';

import uploadToOss from '@/utils/oss-upload';
export const formGroup: FormGroupItem[] = [
  {
    key: 'dict_id',
    fragmentKey: 'renderSelect',
    label: '资质类型',
  },
  {
    key: 'customize_name',
    label: '资质名称',
    fragmentKey: 'renderInput',
    elProps: {
      showCount: true,
      maxlength: 50,
    },
    originProps: {
      rules: { required: true, message: '请输入资质名称', trigger: 'change' },
    },
  },
  {
    key: 'image_list',
    fragmentKey: 'renderUpload',
    label: '资质图片',
    elProps: {
      template: 'pictureCard',
      removeConfirm: true,
      uploadApi: uploadToOss,
      accept: '.jpg,.jpeg,.png',
      hideEntry: 10,
      maxCount: 10,
      showText: '上传',
      valueType: 'file',
    },
    originProps: {
      extra: '支持添加jpg、jpeg、png 格式，上限10张',
      rules: { required: true, message: '请上传资质图片', trigger: 'change' },
    },
  },
  {
    key: 'time',
    label: '许可证有效期',
    fragmentKey: 'renderInput',
  },
];
