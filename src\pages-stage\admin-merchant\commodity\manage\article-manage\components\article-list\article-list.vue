<template>
  <div class="article-list">
    <RTable
      ref="refRTable"
      :api="apiSearchTable"
      :columns="columns"
      :group-handler="groupHandler"
    >
      <template #tableBodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'title'">
          <a-space :size="16">
            <image-item
              :height="88"
              :img="imgOssServer + record.cover"
              :img-style="{
                borderRadius: '8px',
              }"
              :padding="0"
              :width="88"
              :wrap-style="{
                paddingRight: '8px',
                borderRadius: '8px',
              }"
            />
            <a-space direction="vertical">
              <span style="color: var(--ant-primary-color)">{{ record.title }}</span>
              <span>作者:{{ !!record.author ? record.author : '暂无' }}</span>
            </a-space>
          </a-space>
        </template>
      </template>
    </RTable>
    <a-drawer
      v-model:visible="articleModal.visible"
      :body-style="{ padding: '0' }"
      :closable="false"
      width="640"
    >
      <template #title>
        <div style="display: flex; align-items: center">
          <span>{{ modalTitle[articleModal.type] }}</span>
        </div>
      </template>
      <template #footer>
        <div
          v-if="articleModal.type !== 'view'"
          class="flex-wrap flex-x-end"
        >
          <a-button
            style="margin-right: 8px"
            @click="closeAddModal"
          >
            取消
          </a-button>
          <a-button
            type="primary"
            @click="submitArticleForm"
          >
            保存
          </a-button>
        </div>
      </template>
      <div class="drawer-form-content">
        <a-space
          v-if="articleModal.type === 'view'"
          :size="28"
          style="margin-bottom: 24px"
        >
          <span>创建时间：{{ timeModel.create }}</span>
          <span>更新时间：{{ timeModel.update }}</span>
        </a-space>
        <form-create
          ref="articleFormRef"
          v-model:value="articleModal.articleForm"
          :form-group="articleModal.articleFormGroup"
          :origin-props="{ layout: 'vertical', hideRequiredMark: true }"
        >
          <template #cover>
            <a-form-item
              label="标题封面"
              name="cover"
              :rules="[{ required: true, message: '封面不能为空' }]"
            >
              <RUpload
                v-model:value="articleModal.articleForm.cover"
                show-text="上传封面"
                template="pictureCard"
                :upload-api="uploadToOssCompatibility"
                :upload-props="{
                  maxCount: 1,
                }"
              />
            </a-form-item>
          </template>
          <template #content>
            <a-form-item
              label="内容"
              name="content"
              :rules="[
                { required: true, message: '内容不能为空' },
                { max: 3000, message: '内容长度不可超过3000', trigger: 'change' },
              ]"
            >
              <EditorWrap
                ref="editorRef"
                v-model:value="articleModal.articleForm.content"
                :config="{
                  height: '200',
                }"
                :edit-disable="articleModal.type === 'view'"
                :menus="editorMenus"
                :used-image-library="true"
                @open-image-library="openImageLibrary"
              />
              <!-- :edit-disable="isView" -->
            </a-form-item>
          </template>
        </form-create>
      </div>
    </a-drawer>
    <ImageLibrary
      v-model:visible="imageLibraryModal.visible"
      role="user"
      @confirm="afterImageLibrarySelect"
    />
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import type { IGroupHandlerParams, RTableInstance } from 'rrz-web-design';

import EditorWrap from '@/components/editor/editor.vue';
import { FormCreate } from '@/components/form-create';
import { FormGroupItem } from '@/components/form-create/src/typing';
import ImageLibrary from '@/components/image-library/image-library.vue';
import { uploadToOssCompatibility } from '@/utils/oss-helper';

import { articleFormGroup, columns } from './config';
import { createArticle, deleteArticleById, getNewsIndexInf, updateArticle } from './service';

const refRTable = ref<RTableInstance | null>(null);
// oss
const imgOssServer = ref<string>('');
// front
const frontDomain = ref<string>('');
function apiSearchTable(searchParams: any) {
  return new Promise(async resolve => {
    const params = {
      page: searchParams.page,
      pageSize: searchParams.page_size,
      ModNewsSearch: {
        cate: searchParams.cate_name,
        digest: searchParams.digest,
        title: searchParams.title,
        author: searchParams.author,
      },
    };
    const { data } = await getNewsIndexInf(params);
    imgOssServer.value = data.imgOssServer;
    frontDomain.value = data.frontDomain;
    resolve({
      data: data.list,
      meta: {
        pagination: {
          page: data.page.page,
          page_size: data.page.pageSize,
          total: data.page.count,
        },
      },
    });
  });
}

const articleFormRef = ref();
const articleModal = reactive<{
  visible: boolean;
  type: 'edit' | 'add' | 'view' | '';
  articleFormGroup: FormGroupItem[];
  articleForm: Record<string, any>;
}>({
  visible: false,
  type: '',
  articleFormGroup,
  articleForm: {
    cate: undefined,
    digest: '',
    title: '',
    content: '',
    author: '',
    cover: [],
    id: '',
    cate_id: undefined,
  },
});
// 富文本编辑器
const editorRef = ref();
const editorMenus = ref<string[]>([
  'head',
  'bold',
  'fontSize',
  'fontName',
  'italic',
  'underline',
  'strikeThrough',
  'indent',
  'lineHeight',
  'foreColor',
  'backColor',
  'link',
  'list',
  'justify',
  'quote',
  'image',
  'splitLine',
  'undo',
  'redo',
]);
const imageLibraryModal = reactive({ visible: false });
function openImageLibrary() {
  imageLibraryModal.visible = true;
}
function afterImageLibrarySelect(images: { url: string }[]) {
  let html = '';
  images.forEach(img => {
    html += `<img src="${img.url.replace(/\/[s,o]\//, '/m/')}" style="max-width: 100%;" />`;
  });
  editorRef.value.append(html);
}

async function showAddModal() {
  articleModal.visible = true;
}

async function closeAddModal() {
  articleModal.visible = false;
}

function getArticleForm(record: any) {
  Object.keys(articleModal.articleForm).forEach(key => {
    if (key === 'cover') (articleModal.articleForm[key] as any) = [imgOssServer.value + record[key]];
    else if (key === 'cate_id') (articleModal.articleForm[key] as any) = record['cate'];
    else if (key === 'cate') (articleModal.articleForm[key] as any) = record['cate_name'];
    else articleModal.articleForm[key] = record[key];
  });
}

const modalTitle = {
  edit: '编辑文章',
  add: '添加文章',
  view: '查看文章',
};

async function setArticleFormStatus(view: 'edit' | 'add' | 'view' | '') {
  articleModal.type = view;
  let bool = view === 'view';
  articleModal.articleFormGroup.forEach(item => {
    if (item.elProps) {
      item.elProps.disabled = bool ? true : false;
    }
    // console.log((item.elProps.disabled = bool));
  });
}
const cate_id = ref<any>('');
async function handleAdd() {
  await setArticleFormStatus('add');
  articleModal.articleForm = {
    cate: undefined,
    digest: '',
    title: '',
    content: '',
    author: '',
    cover: [],
    id: undefined,
    cate_id: undefined,
  };
  cate_id.value = undefined;
  showAddModal();
}

const timeModel = reactive({
  create: '',
  update: '',
});

async function handleView(params: IGroupHandlerParams) {
  const { record } = params;
  await setArticleFormStatus('view');
  timeModel.create = record.created_at;
  timeModel.update = record.updated_at;
  showAddModal();
  getArticleForm(record);
}

async function handleUpdate(params: IGroupHandlerParams) {
  const { record } = params;
  await setArticleFormStatus('edit');
  cate_id.value = record.cate_name;
  getArticleForm(record);
  showAddModal();
}

async function handleDelete(params: IGroupHandlerParams) {
  const { record } = params;
  Modal.confirm({
    title: '删除该数据将无法恢复，是否继续？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      const result = await deleteArticleById({
        id: record.id,
      });
      message.success(result.message);
      refRTable.value?.getTableList();
    },
  });
}

async function handleLink(params: IGroupHandlerParams) {
  const { record } = params;
  window.parent.postMessage(
    {
      action: 'blank',
      href: `${frontDomain.value}/shop-news-view/${record.id}`,
    },
    '*',
  );
}

const groupHandler = {
  handleView,
  handleUpdate,
  handleLink,
  handleDelete,
};

watch(
  () => articleModal.articleForm.cate,
  val => {
    if (['edit', 'view'].includes(articleModal.type) && val && val !== cate_id.value) {
      articleModal.articleForm.cate_id = val;
    }
  },
);

async function submitArticleForm() {
  (articleFormRef.value?.getFormRef() as any).validate().then(async () => {
    /**
     * create 不需传id
     * update 传id
     */
    let cover = '';
    if (Array.isArray(articleModal.articleForm.cover) && articleModal.articleForm.cover.length) {
      const url = articleModal.articleForm.cover[0];
      const comIndex = url.indexOf('.com/');
      const comUrl = comIndex !== -1 ? url.slice(comIndex + 4) : '';
      const cnIndex = url.indexOf('.cn/');
      const cnUrl = cnIndex !== -1 ? url.slice(cnIndex + 3) : '';
      cover = comUrl || cnUrl || '';
    }
    // let cid = cate_id.value;
    let cate = articleModal.articleForm.cate;
    let cate_b = articleModal.articleForm.cate_id;

    let baseParams = {
      id: articleModal.articleForm.id,
      ModNews: {
        ...articleModal.articleForm,
        id: undefined,
        cover,
        cate: articleModal.type === 'edit' ? cate_b : cate,
      },
    };
    const res = articleModal.type === 'edit' ? await updateArticle(baseParams) : await createArticle(baseParams);
    message.success(res.message);
    closeAddModal();
    refRTable.value?.getTableList();
  });
}

defineExpose({
  handleAdd,
});
</script>
<style lang="less" scoped>
.article-list {
  margin-top: 8px;
}

.article-list :deep(.r-search-content) {
  margin: 0 0 24px;
}

.article-list :deep(.r-table-content) {
  padding: 0;
}

.drawer-form-content {
  padding: 24px 32px;
  overflow-y: auto;
  :deep(.ant-form-item) {
    &:nth-child(4) {
      order: 2;
    }
    &:nth-child(5) {
      order: 3;
    }
  }
}
.drawer-footer {
  padding: 10px 16px;
  border-top: 1px solid #e9e9e9;
}
</style>
