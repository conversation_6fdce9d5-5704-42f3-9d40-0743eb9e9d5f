<template>
  <a-cascader
    v-model:value="channelList"
    allow-clear
    :field-names="fieldNames"
    max-tag-count="responsive"
    multiple
    :options="channelOptions"
    placeholder="请选择渠道"
    show-search
    @change="$emit('change', $event)"
    @focus="getChannelList"
  >
    <template #tagRender="text">
      <a-tag>{{ handleTagText(text.value) }}</a-tag>
    </template>
    <template #maxTagPlaceholder="data">
      <a-tooltip :overlay-style="{ width: 'auto' }">
        <template #title>
          <div
            v-for="item in data"
            :key="item.value"
          >
            {{ handleTagText(item.value) }}
          </div>
        </template>
        + {{ data.length }} ...
      </a-tooltip>
    </template>
  </a-cascader>
</template>

<script setup lang="ts">
import { PropType, ref } from 'vue';
import { apiGetChannelList } from './services';
import { flattenTreeToObject } from '@/utils/base';
import { useVModel } from '@/hook';

const emits = defineEmits(['change', 'update:value']);
const props = defineProps({
  value: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});
const channelList = useVModel(props, 'value', emits);
const channelOptions = ref([]);
let channelOptionsMap = {};
const fieldNames = { label: 'name', value: 'id', children: 'sub' };

function getChannelList() {
  if (channelOptions.value.length) {
    return;
  }
  apiGetChannelList().then(res => {
    channelOptions.value = res.data;
    channelOptionsMap = flattenTreeToObject(res.data, { key: 'id', childKey: 'sub' });
  });
}

function handleTagText(e) {
  return e
    .split('__RC_CASCADER_SPLIT__')
    .map(key => channelOptionsMap[key].name)
    .join('/');
}
</script>
