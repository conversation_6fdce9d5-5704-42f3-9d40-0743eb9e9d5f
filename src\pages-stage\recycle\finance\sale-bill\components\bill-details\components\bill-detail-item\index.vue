<template>
  <div :class="['bill-list-box', ...classNames]">
    <div class="title">
      {{ title }}
    </div>
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: '',
  },
  classNames: {
    type: Array,
    default: () => [],
  },
});
</script>

<style lang="less" scoped>
.bill-list-box {
  .title {
    position: relative;
    padding-left: 12px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: bold;
    font-size: 14px;
    line-height: 22px;
    &::before {
      position: absolute;
      top: 4px;
      left: 0;
      width: 4px;
      height: 14px;
      background: #00c8be;
      border-radius: 2px;
      content: '';
    }
  }
  .content {
    margin-top: 12px;
    margin-bottom: 24px;
  }
}
</style>
