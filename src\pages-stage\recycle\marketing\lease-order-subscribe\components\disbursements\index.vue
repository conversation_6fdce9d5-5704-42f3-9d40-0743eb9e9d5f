<template>
  <div class="disbursements">
    <div
      v-if="actualPay.payDetailBtn"
      class="row8 keynote"
    >
      <span class="actual-payment">实付款：{{ actualPay.payDetailBtn.btn_text }}</span>
    </div>

    <div
      v-if="actualPay.pay_date"
      class="row payment-time"
    >
      付款时间：{{ actualPay.pay_date }}
    </div>

    <template v-if="actualPay.payDiscount">
      <div
        v-for="(item, index) in actualPay.payDiscount"
        :key="index"
        class="row"
      >
        {{ item }}
      </div>
    </template>

    <!-- 已交租金 无交互 -->
    <div
      v-if="actualPay.already_pay_rent"
      class="row8"
    >
      <a-tag class="not_already_pay_rent">
        {{ actualPay.already_pay_rent }}
      </a-tag>
    </div>

    <extend
      ref="PayExtendComponent"
      class="pay-extend"
      @btn-visible="payExtend.btnVisible"
      @change="payExtend.change"
    >
      <template #label>
        <span
          v-show="payExtend.visible"
          class="operate-btn"
        >
          {{ payExtend.btnText }}
        </span>
      </template>
      <template #content>
        <template v-if="actualPay.viewBillBtn && actualPay.viewBillBtn.toggleList">
          <div
            v-for="item in actualPay.viewBillBtn.toggleList"
            :key="item"
            class="row"
          >
            {{ item }}
          </div>
        </template>

        <div
          v-if="actualPay.buy_money"
          class="row"
        >
          购买金额：￥{{ actualPay.buy_money.toFixed(2) }}
        </div>
      </template>
    </extend>
    <div class="divider" />
    <!-- 购买价 -->
    <div
      v-if="actualPay.pay_break_price"
      class="row keynote"
    >
      <a-tag
        v-if="actualPay.isBuyOutOrder === 1"
        color="#EB7766"
      >
        已购买
      </a-tag>
      购买价：{{
        typeof actualPay.pay_break_price === 'number'
          ? '￥' + actualPay.pay_break_price.toFixed(2)
          : actualPay.pay_break_price
      }}
    </div>

    <div class="divider" />

    <!-- 已退款 -->
    <div
      v-if="actualPay.already_refund_money"
      class="row refunded"
    >
      {{ actualPay.already_refund_money }}
    </div>
    <div class="divider" />

    <!-- 标签组 -->
    <div class="tags row8">
      <div v-if="actualPay.user_buy_out">
        <!-- 客户已购买 -->
        <a-tag
          class="tag"
          style="color: #cf1322; background-color: #fff1f0; border-color: #ffa39e"
        >
          {{ actualPay.user_buy_out }}
        </a-tag>
      </div>

      <!-- 支付宝免密代扣 -->
      <a-tooltip
        v-if="actualPay.payType && actualPay.payType.text"
        placement="top"
      >
        <template
          v-if="actualPay.payType.title"
          #title
        >
          {{ actualPay.payType.title }}
        </template>
        <a-tag
          class="tag red-bg-tag"
          style="color: #cf1322; border-color: #ffa39e"
        >
          {{ actualPay.payType.text }}
        </a-tag>
      </a-tooltip>

      <!-- 租期类型 -->
      <a-tag
        v-if="tenancy.lease_term_type_txt"
        class="tag"
      >
        {{ tenancy.lease_term_type_txt }}
      </a-tag>

      <!-- 尚未扣款 -->
      <a-tag
        v-if="actualPay.deduction_status"
        class="tag"
      >
        {{ actualPay.deduction_status }}
      </a-tag>

      <!-- 已授权代扣 -->
      <a-tag
        v-if="actualPay.withHoldBtn"
        class="tag"
      >
        {{ actualPay.withHoldBtn.btn_text }}
      </a-tag>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';

import type { ActualPayType, TenancyType } from '../../data';
import Extend from '../extend/index.vue';
import usePayExtend from './composables/use-pay-extend';

defineProps({
  actualPay: {
    type: Object as PropType<ActualPayType>,
    required: true,
  },
  // 租期
  tenancy: {
    type: Object as PropType<TenancyType>,
    default() {
      return {};
    },
  },
});

const PayExtendComponent = ref<InstanceType<typeof Extend> | null>(null);
const payExtend = usePayExtend({ PayExtendComponent });
</script>

<style lang="less" scoped>
.disbursements {
  font-size: 12px;
  line-height: 20px;

  .keynote {
    color: rgb(6, 21, 51);
    font-size: 14px;
  }

  .actual-payment {
    color: rgba(6, 21, 51, 0.85);
  }

  .payment-time {
    color: rgba(6, 21, 51, 0.45);
  }
}

.pay-extend {
  .operate-btn {
    display: inline-block;
    padding: 1px 28px 1px 8px;
    color: rgba(6, 21, 51, 1);
    font-size: 12px;
    background: #f5f7fa;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
  }

  :deep(.extend-btn) {
    margin-left: -20px;
  }
}
</style>
