export interface IOptionItem<T> {
  label: string;
  value: T;
  level?: number;
  isLeaf?: boolean;
  child?: IOptionItem<T>[];
}

export interface IReceiveAddressProps {
  province: string | null;
  city: string | null;
  area: string | null;
  address: string | null;
}

export interface ISFFormState {
  times: string[];
  sf_address: string | null;
  receive_address: Partial<IReceiveAddressProps>;
  sfServiceType: string | number | null;
  serve: string[];
  goods_cost?: number;
}

export interface IServeOption {
  label: string;
  value: string;
  href?: string;
  hrefText?: string;
}

export interface ISFExpressTypeOption {
  label: string;
  value: number;
  tips: string;
}

export interface IUseOptionsProps {
  useServiceType: () => Promise<ISFExpressTypeOption[]>;
  useServe: (options: IServeOption[]) => IServeOption[];
}
