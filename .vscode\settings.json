{
  "editor.tabSize": 2, // tab两个空格
  "files.trimTrailingWhitespace": true, // 自动去除多余空格
  "eslint.alwaysShowStatus": true, // 显示异常信息
  "editor.formatOnSave": true, // 保存时自动格式化
  "editor.formatOnPaste": false, // 粘贴时自动格式化
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "editor.defaultFormatter": "rvest.vs-code-prettier-eslint",
  "[markdown]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[json]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[yml]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[javascript]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[html]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[vue]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[css]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[less]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[scss]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "vetur.validation.template": false, // 关闭vetur的模版校验
  "eslint.validate": ["javascript", "javascriptreact", "vue", "html"], // eslint校验的范围
  "html.validate.styles": false, // vscode配置，关闭style校验防止在查看小程序**ml文件时出错
  "files.associations": {
    "*.axml": "html",
    "*.wxml": "html",
    "*.ttml": "html",
    "*.acss": "css",
    "*.wxss": "css",
    "*.ttss": "css"
  },
  "stylelint.validate": ["vue"],
  "stylelint.enable": true,
  "cSpell.words": [
    "alipay",
    "anticon",
    "btns",
    "cascader",
    "Changehanlder",
    "composables",
    "hoverable",
    "Imei",
    "maxlength",
    "navs",
    "popconfirm",
    "qualitation",
    "vuex"
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "vs-code-prettier-eslint.prettierLast": false // set as "true" to run 'prettier' last not first
}
