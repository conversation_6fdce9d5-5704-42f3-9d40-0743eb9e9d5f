// shop/template/select
import { GET, POST } from '@/services/api';

export default interface SaveNavInfoInstance {
  name: string;
  id: number | string;
  type?: string;
}

interface SaveNavInfoType {
  TblShopNav: {
    shop_name: string;
    data_config: string;
  };
}

export function fetchNavInfo(): Promise<any> {
  return GET('/shop/wrap/nav-inf');
}

export function saveNavInfo(data: SaveNavInfoType): Promise<any> {
  return POST('/shop/wrap/nav-inf', data);
}
