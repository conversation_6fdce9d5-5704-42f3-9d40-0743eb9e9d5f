<template>
  <a-drawer
    v-model:visible="visible"
    title="操作日志"
    width="80vw"
  >
    <a-table
      :columns="columns"
      :data-source="list"
      :loading="loading"
      :pagination="page"
      :scroll="{ y: 720, scrollToFirstRowOnChange: true }"
      @change="onTableChange"
    >
      <template #bodyCell="{ record, column, text }">
        <template v-if="column.dataIndex === 'bind_type'">
          <a-space wrap>
            <span>{{ text.join('、') }}</span>
            <span
              class="text-link"
              @click="openBindTypeDetail(record)"
            >详情</span>
          </a-space>
        </template>
        <template v-else-if="column.dataIndex === 'update_num'">
          <template v-if="text > 0">
            <span
              class="text-link"
              @click="goUpdateDetail(record.rule_id)"
            >{{ text }}</span>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </template>
    </a-table>
  </a-drawer>
  <a-modal
    v-model:visible="bindTypeDetailVisible"
    :footer="null"
    title="详情"
  >
    <a-table
      :columns="bindTypeDetailCol"
      :data-source="bindTypeDetailList"
      :pagination="false"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useTable } from '@/rrzu-hooks';
import { useVModel } from '@/hook';
import { apiGetOperationLog } from '../services';

const router = useRouter();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);

const columns = [
  {
    title: '项目ID',
    dataIndex: 'config_id',
  },
  {
    title: '项目名称',
    dataIndex: 'title',
    width: 224,
  },
  {
    title: '绑定类型',
    dataIndex: 'bind_type',
    width: 192,
  },
  {
    title: '修改人',
    dataIndex: 'created_by',
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return text === 1 ? '开启' : '关闭';
    },
  },
  {
    title: '修改时间',
    dataIndex: 'created_at',
    width: 170,
  },
  {
    title: '操作',
    dataIndex: 'operate',
  },
  {
    title: '修改规则量',
    dataIndex: 'update_num',
  },
];
const { loading, list, page, getList, onTableChange } = useTable({
  api: apiGetOperationLog,
});

const bindTypeDetailVisible = ref(false);
const bindTypeDetailCol = [
  {
    title: '分类',
    dataIndex: 'title',
  },
  {
    title: '详情',
    dataIndex: 'value',
  },
];
const bindTypeDetailList = ref([]);
const openBindTypeDetail = (record: any) => {
  bindTypeDetailList.value = record.bind_type_list;
  bindTypeDetailVisible.value = true;
};

function goUpdateDetail(id: number) {
  router.push({
    path: `/risk/access-rule-system/fixed-deposit-freeze/detail-log/${id}`,
  });
}

watch(
  () => visible.value,
  val => {
    val && getList();
  },
);
</script>
