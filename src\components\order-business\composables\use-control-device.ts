import { ref } from 'vue';
import { useRoute } from 'vue-router';

import { getWorkFormFilter, superGetWorkFormFilter } from './services';

export interface IControlDevice {
  apply_reason?: {
    value: number;
    label: string;
  }[];
  unique_code?: {
    value: number;
    label: string;
  }[];
  apply_time?: {
    deny_date: { type: string; start: string; end: string }[];
    allow_time: { type: string; start: string; end: string }[];
  };
  function?: {
    id: string;
    name: string;
  }[];
  unlocked_function?: Record<
    number,
    {
      unique_code: string;
      function_id: string;
      name: string;
    }[]
  >;
}

export function useControlDevice() {
  const controlDevice = ref<IControlDevice>();
  const infoLoading = ref(false);
  const isSuper = useRoute().query.role === 'super';

  function getControlDevice(orderId, type) {
    infoLoading.value = true;
    controlDevice.value = undefined;
    const api = isSuper ? superGetWorkFormFilter : getWorkFormFilter;
    api({ order_id: orderId, type })
      .then(({ data }) => (controlDevice.value = data))
      .finally(() => (infoLoading.value = false));
  }

  return {
    isSuper,
    controlDevice,
    infoLoading,
    getControlDevice,
  };
}
