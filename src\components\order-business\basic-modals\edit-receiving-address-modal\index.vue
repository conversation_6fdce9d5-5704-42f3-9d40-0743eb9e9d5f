<template>
  <a-modal
    v-model:visible="bindVisible"
    title="修改收货地址"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ style: { width: '80px' } }"
        :model="formData"
        :rules="rules"
      >
        <a-form-item>
          <div class="address-parse-box">
            <a-textarea
              v-model:value="addressParseText"
              class="address-parse-textarea"
              placeholder="粘贴文本到此处，以识别用户地址"
            />
            <div class="address-parse-button-wrapper">
              <a-button
                :loading="addressParseLoading"
                type="primary"
                @click="parseAddress"
              >
                识别
              </a-button>
            </div>
          </div>
        </a-form-item>

        <a-form-item
          label="姓名"
          name="name"
        >
          <a-input
            v-model:value="formData.name"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          label="手机"
          name="phone"
        >
          <span v-if="isLimitPhoneShow">{{ desePhone(formData.phone) }}</span>
          <a-input
            v-else
            v-model:value="formData.phone"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          label="地区"
          name="area"
        >
          <!-- <AddressSelect
            v-model:labels="formData.address_names"
            v-model:value="formData.address_ids"
          /> -->
          <RAddressCascader
            v-model:value="formData.address_code"
            style="width: 100%"
            @change="onAddressChange"
          />
        </a-form-item>
        <a-form-item
          label="详细地址"
          name="address"
        >
          <a-input
            v-model:value="formData.address"
            placeholder="请输入详细地址"
          />
        </a-form-item>
        <transition name="fade">
          <a-form-item
            v-if="previewVisible"
            label="预览"
          >
            <div class="address-line bold">
              {{ formData.name || '-' }} {{ isLimitPhoneShow ? desePhone(formData.phone) : formData.phone || '-' }}
            </div>
            <div class="address-line flex-wrap flex-y-center flex-gap-8">
              {{ addressLines.join(' ') }}
            </div>
          </a-form-item>
        </transition>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import type { CascaderProps } from 'ant-design-vue';
import { FormInstance, message } from 'ant-design-vue';
import { isUndefined } from 'lodash-es';

import { type TAddress, useAddress } from '@/components/address-select'; // AddressSelect,
import { fetchGateway } from '@/components/order-business/basic-modals/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import { addressRecognition, superAddressRecognition } from '@/services/api';
import { desePhone } from '@/utils/sensitive-info';

interface IFormData {
  name?: string;
  phone?: string;
  address?: string;
  address_ids: TAddress;
  address_names: TAddress;
  address_code: string[];
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  addressInfo: {
    type: Object as PropType<Omit<IFormData, 'address_ids' | 'address_names'>>,
    default: () => ({}),
  },
  isLimitPhoneShow: {
    type: Boolean,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const formData = ref<IFormData>({
  address_ids: [],
  address_names: [],
  address_code: [],
});

const bindVisible = useVModel(props, 'visible', emit);
const { getValuesByNames } = useAddress();

const rules = {
  name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
  phone: [{ required: true, message: '手机不能为空', trigger: 'blur' }],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  area: [
    {
      required: true,
      validator() {
        const { address_code } = formData.value;
        if (!address_code?.length || address_code?.some(isUndefined)) {
          return Promise.reject('请先完善地区选择');
        }
        return Promise.resolve();
      },
    },
  ],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

const addressLines = computed(() => {
  const { address } = formData.value;
  const [province, city, area] = formData.value.address_names;
  const areaText = [province, city, area].filter(val => !!val).join('/');
  return [areaText, address];
});

const previewVisible = computed(() => {
  const { name, phone, address, address_code } = formData.value;
  // const [province, city, area] = formData.value.address_names;
  return name && phone && address_code?.length && address;
});

watch(
  () => bindVisible.value,
  async value => {
    if (value) {
      formRef.value?.resetFields();
      const { name, phone, address: addressLine = '', address_code = [] } = props.addressInfo;
      const [, province, city, area, address = addressLine] =
        addressLine.match(/^([^ /]+)\/([^ /]+)\/(\S+)\s(.*)/) || [];
      const address_ids = (await getValuesByNames([province, city, area])).filter(key => !!key);
      const address_names = [province, city, area].filter(val => !!val);
      formData.value = { name, phone, address, address_ids, address_names, address_code };
      addressParseText.value = '';
    }
  },
  { immediate: true },
);

const isSuper = useRoute().query.role === 'super';

const onAddressChange: CascaderProps['onChange'] = (_value, selectedOptions) => {
  formData.value.address_names = selectedOptions.map(item => item.name);
};

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const [province, city] = formData.value.address_names;
    const { address_code = [] } = formData.value;
    const params = {
      name: formData.value.name,
      phone: formData.value.phone,
      address: addressLines.value.join(' '),
      city: city === '市辖区' ? province : city,
      ordinary_delivery: 1,
      orderId: props.orderId,
      is_server: isSuper ? 0 : 1, // 用于区分是商家后台还是运营后台
      method: 'v2.order.edit.customer.address',
      province_code: address_code[0],
      city_code: address_code[1],
      area_code: address_code[2],
    };
    fetchGateway(params)
      .then(() => {
        message.success('修改成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data', 'all_remark', 'super2_remark']);
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

const addressParseText = ref('');
const addressParseLoading = ref(false);
async function parseAddress() {
  try {
    const text = addressParseText.value;
    if (!text) {
      message.warning('请输入地址');
      return;
    }

    addressParseLoading.value = true;
    const api = isSuper ? superAddressRecognition : addressRecognition;
    const { data } = await api({ text });

    formData.value.name = data.person;
    if (!props.isLimitPhoneShow) {
      formData.value.phone = data.phonenum;
    }
    formData.value.address_code = [data.province_code, data.city_code, data.county_code];
    formData.value.address = data.detail;
  } finally {
    addressParseLoading.value = false;
  }
}
</script>

<style lang="less" scoped>
.address-parse-box {
  padding: 8px;
  border: 1px solid #dadce0;
  border-radius: 4px;

  .address-parse-textarea {
    border: none;
    resize: none;
    &:focus {
      box-shadow: none;
    }
  }

  .address-parse-button-wrapper {
    margin-top: 8px;
    text-align: right;
  }
}

.address-line {
  line-height: 32px;

  &.bold {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
  }
}
</style>
