import { GET } from '@/services/api';
import { getCookie } from '@/utils/cookies';
const headers = {
  Authorization: getCookie('Go-Token'),
};

/**
 * @description: 表单筛选项数据(获取)
 * @param {any} params
 * @return {*}
 */
export function getOrderCheckReportApi(params: { order_id: string }) {
  return GET('/new-work-order/order-check-report', params, {
    headers,
  });
}

/**
 * @description: 表单筛选项数据(获取)(super)
 * @param {any} params
 * @return {*}
 */
export function getSuperOrderCheckReportApi(params: { order_id: string }) {
  return GET('/super/new-work-order/order-check-report', params, {
    headers,
  });
}

/**
 * @description: 对比订单检测报告
 * @param {any} params
 * @return {*}
 */
export function getDiffReportApi(params: any) {
  return GET('/new-work-order/diff-report', params, {
    headers,
  });
}
