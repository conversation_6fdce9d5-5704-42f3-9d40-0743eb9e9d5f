<template>
  <a-modal
    v-model:visible="bindVisiable"
    destroy-on-close
    :title="`${type === 1 ? '添加' : '编辑'}文章`"
    width="480px"
    @cancel="emits('close')"
    @ok="emits('confirm', formRef, emits)"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="bindValue"
      >
        <a-form-item
          label="文章标题"
          name="title"
          required
        >
          <a-input
            v-model:value="bindValue.title"
            placeholder="25个字符以内"
          />
        </a-form-item>
        <a-form-item
          label="所属常规类目"
          name="question_type"
        >
          <a-select
            v-model:value="bindValue.question_type"
            :options="articleNormalOptions"
            placeholder="请选择"
          />
        </a-form-item>
        <a-form-item
          label="所属流程类目"
          name="process_question_type"
        >
          <a-select
            v-model:value="bindValue.process_question_type"
            :options="articleProcessOptions"
            placeholder="请选择"
          />
        </a-form-item>
        <a-form-item
          label="文章内容"
          name="content"
          required
        >
          <a-textarea
            v-model:value.trim="bindValue.content"
            :auto-size="{ minRows: 4, maxRows: 7 }"
            :maxlength="350"
            placeholder="请输入"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { useVModel } from '@/hook/use-vmodel/index';
import { articleProcessOptions, articleNormalOptions } from '../config';
import { PropType, computed, ref } from 'vue';
import { IFormModal } from '../data';
import { FormInstance } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  type: {
    type: Number,
    default: 1,
  },
  formState: {
    type: Object as PropType<Partial<IFormModal>>,
    default: () => ({}),
  },
});
const emits = defineEmits(['update:visible', 'update:formState', 'reset-table', 'confirm', 'close']);
const bindValue = computed({
  get: () => props.formState,
  set: val => {
    emits('update:formState', val);
  },
});
const bindVisiable = useVModel(props, 'visible', emits);
const formRef = ref<FormInstance | null>(null);
</script>

<style lang="less" scoped>
@import '../../../common/base.less';
:deep(.ant-form-item-label) {
  align-self: flex-start;
}
.ant-form-item {
  flex-direction: column;
  width: 100%;
}
:deep(.ant-col) {
  flex: 1;
  min-height: min-content;
}
</style>
