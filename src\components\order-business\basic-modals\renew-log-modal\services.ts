import { GET } from '@/services/api';
import type { INewLogData, IOldLogListItem } from './data';

interface INewLogParams {
  order_id: string;
}

interface IOldLogParams {
  id: string;
}

/** 获取旧续租订单记录 */
export function getOldRenewHistory(params: IOldLogParams) {
  return GET<IOldLogParams, IOldLogListItem[]>('/super/v3-order/get-renew-history', params);
}

/** 获取新续租订单记录 */
export function getNewRenewHistory(params: INewLogParams) {
  return GET<INewLogParams, INewLogData>('/super/order-continue-log-api/order-continue-log', params);
}

/** 商家获取续租订单 */
export function getRenewHistory(params: any) {
  return GET('/order-continue-log-api/server-order-continue-log', params);
}
