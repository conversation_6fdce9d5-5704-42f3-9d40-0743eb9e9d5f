<template>
  <a-modal
    v-model:visible="bindVisible"
    title="操作窗口"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="提交中..."
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="formData"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          extra="用户旧手机不再使用时，可将订单更换到新的登录手机号，订单账单同步更改"
          label="更改绑定手机"
          name="userPhone"
        >
          <a-input
            v-model:value="formData.userPhone"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useVModel } from '@/hook';
import { FormInstance, message } from 'ant-design-vue';
import { fetchGateway } from '@/components/order-business/basic-modals/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

interface IFormData {
  userPhone?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  remark: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const formData = ref<IFormData>({});
const bindVisible = useVModel(props, 'visible', emit);

const rules = {
  userPhone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
};

const formRef = ref<FormInstance>();
const loading = ref(false);

/** 确认 */
function submit() {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      order_id: props.orderId,
      remark: props.remark,
      ordinary_delivery: 1,
      userPhone: formData.value.userPhone,
      method: 'v2.order.edit.phone',
    };
    fetchGateway(params)
      .then(() => {
        message.success('修改成功');
        bindVisible.value = false;
        emit('refresh', props.orderId, ['data']);
      })
      .finally(() => {
        loading.value = false;
      });
  });
}
</script>
