/**
 * ！！！！！！！！！！！！！！！！！！！
 * 修改该文件函数时请看是否会影响到自动下发与手动下发
*/

/**
 *生成换货建议，补贴升级additionalParams
 * @param params
 * @param currKey 租赁/非租赁
 * @param additionalParams 额外参数
 * @returns
 */
export function useAdviceParams(params: any, currKey: boolean, additionalParams: any = {}) {
  return {
    ...params,
    is_lock_machine: currKey,
    ...additionalParams,
  };
}

/**
 * 生成换货建议/补贴升级的表格数据
 * @param tableData 换货建议/补贴升级
 * @param isSupport 租赁/非租赁
 * @returns
 */
export function generatePriceList(tableData: any[], isSupport: boolean) {
  if (!tableData.length) return [];
  return tableData.map(item => {
    const curr = item.evaluatePriceItem?.[isSupport ? 'lock' : 'unlock'];
    const showPrice = !(curr instanceof Array) && Number(curr?.totalPrice) !== 0;
    const price = showPrice ? curr.totalPrice : '暂无';
    return {
      ...item,
      price,
    };
  });
}
