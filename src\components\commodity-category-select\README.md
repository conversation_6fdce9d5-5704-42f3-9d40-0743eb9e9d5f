# CommodityCategorySelect 商品分类下拉选择

基于[a-select](https://3x.antdv.com/components/select-cn#API) 组件封装，在保持原有属性的基础上，针对商品类目进行封装，使其具备对商品分类的单选、多选、分级别获取、全级别获取、远程搜索、禁用项显示控制等。当前未全局引入，需手动引入后使用。

### 属性

除已有属性外，其他属性已继承自[a-select](https://3x.antdv.com/components/select-cn#API) ，更多可查看对应文档获取

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value(v-model) | 指定选中项 | number\|number\[] | -- |
| disabledType | 分类字典页面中可设置选择项的隐藏过滤，这里配置'show'可显示但禁用选项，配置'enable'可显示且选项可选 | 'show'\|'enable' | -- |
| level | 获取第level级的分类：1 一级\| 2 二级\| 3 品牌\| 4 型号 | 1 \| 2 \| 3 \| 4 | 4 |
| scene | 不同的场景会决定分类的排序，1 平台商品 2 商家商品 3 平台订单 4 商家订单 | EScene（1 \|2 \|3 \|4） | 1 或 2(根据用户 role 判断) |
| filterPidLevel | 对第几层的父 id 进行筛选（通过 pid 属性获取分类），需要获取莫个分类下子级选择项时使用 | 1 \|2 \|3 | -- |
| pid | 父级 id，也可使用完整的父级层 id 数组（完整的父级 id 查询速度更快），配合 filterPidLevel 属性使用，获取对应 id 下的子级选择项 | number\|number[] | -- |
| asyncSearch | 是否使用异步远程搜索，无法与 filterPidLevel 同时使用，推荐在需要获取 4 级的分类数据时使用，避免一次性获取过多分类数据导致加载过慢 | boolean | false |
| defineOptions | 定义选项，传入函数，可用于获取选项，return 的选择将控制最终显示的选择项内容 | (options)=>options | -- |

### 事件

均已继承自 [a-select](https://3x.antdv.com/components/select-cn#API) ，更多可查看对应文档获取

| 事件名称 | 说明         | 回调参数        |
| -------- | ------------ | --------------- |
| change   | 手动更改选择 | (value,options) |



### 获取选择项数据方式

> useCommodityCategoryList获取的数据是独立的，修改数据不会影响到下拉框组件，而其他两种均会直接影响下拉框显示内容，可根据使用场景选择不同的获取方式。

#### useCommodityCategoryList函数获取

通过调用useCommodityCategoryList函数获取数据，但需要手动配置与组件等同的参数来获取数据，如果需要动态根据参数获取对应的数据可通过传入函数的方式，特点是数据为独立的，与组件不会有关联，可在项目任何地方调用，且不会重复请求数据。

```vue
<template>
	<CommodityCategorySelect
  	v-model:value="selectId"
  	:level="2"
	/>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CommodityCategorySelect from '@/components/commodity-category-select';
import { useCommodityCategoryList } from '@/composables/commodity-category';

const selectId = ref<number>()

const { categoryList: options } = useCommodityCategoryList({
  level: 2,
});

// brandOptions会根据selectId的变化动态更新
const { categoryList: brandOptions } = useCommodityCategoryList(() => {
  filterPidLevel: 2,
  pid: selectId.value,
  level: 3,
});
</script>
```

#### ref获取

通过定义组件ref获取暴露的选择项

```vue
<template>
	<CommodityCategorySelect
    ref="selectRef"
  	v-model:value="selectId"
  	:level="3"
    :scene="EScene.PlatformOrder"
  	placeholder="请选择品牌"
	/>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import CommodityCategorySelect from '@/components/commodity-category-select';
import type { IOption } from '@/composables/commodity-category/data';
import { EScene } from '@/utils/enums/commodity';

const selectId = ref<number>();

const selectRef = ref();
const options = computed<IOption[]>(() => {
  return selectRef.value?.options;
});
</script>
```

#### defineOptions参数内获取

defineOptions在选择项发生变化时执行，便可通过执行过程时进行赋值选择项，注意defineOptions必须要有return返回

```vue
<template>
	<CommodityCategorySelect
  	v-model:value="selectId"
    :defineOptions="defineOptions"
  	:level="3"
    :scene="EScene.PlatformOrder"
  	placeholder="请选择品牌"
	/>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CommodityCategorySelect from '@/components/commodity-category-select';
import type { IOption } from '@/composables/commodity-category/data';
import { EScene } from '@/utils/enums/commodity';

const selectId = ref<number>();
const options = ref<IOption[]>([]);

function defineOptions(options: IOption[]) {
  options.value = options;
  return options;
}
</script>
```



### 在form-create中使用

项目公共组件form-create中配置时，fragmentKey配置'renderCustom'之后，component配置使用该组件即可，组件的相关属性同样在elProps可配置生效。

```ts
import { FormGroupItem } from '@/components/form-create/src/typing';
import CommodityCategorySelect from '@/components/commodity-category-select';

export const operationRecordSearchFormGroup: FormGroupItem[] = [
  {
    key: 'spu_id',
    fragmentKey: 'renderInput',
    originProps: { label: '商品ID', name: 'spu_id' },
    elProps: { placeholder: '请输入', allowClear: true },
  },
  {
    key: 'category_id',
    fragmentKey: 'renderCustom',
    component: CommodityCategorySelect,
    originProps: { label: '商品分类', name: 'category_id' },
    elProps: { level: 3 },
  },
}
```



### 应用

#### 获取所有X级的分类选择

通过level配置，默认会取第4级

```vue
<!-- 获取所有第3级的分类数据 -->
<CommodityCategorySelect
  v-model:value="formData.categoryId"
  :level="3"
  placeholder="请选择品牌"
/>
```

#### 获取第X级的id为Y下的所有子级分类选择

配置filterpPidLevel与pid来决定只显示莫个分类的所有子级选择项

```vue
<!-- 第2级id为34下的所有型号 -->
<CommodityCategorySelect
  v-model:value="category"
  :filter-pid-level="2"
  :pid="34"
  placeholder="请选择商品型号"
  style="width: 320px"
/>
<!-- 第1级id为1下的所有3级(品牌)的分类选择 -->
<CommodityCategorySelect
  v-model:value="category"
  :filter-pid-level="1"
  :pid="1"
  :level="3"
  placeholder="请选择商品品牌"
  style="width: 320px"
/>
```

#### 异步远程搜索

配置asyncSearch启用，通常在获取到4级的数据时由于数量过大导致请求缓慢时推荐开启，选择项便会通过多次请求的方式，仅在搜索时获取的分类选择项数据，其他情况不推荐开启，且不能与filterPidLevel同时使用

> 注意：CommodityCategorySelect远程搜索时暂不支持value进行回显

```vue
<!-- 可远程搜索选择有4级商品分类的选择项 -->
<CommodityCategorySelect
  v-model:value="formData.categoryId"
  async-search
  placeholder="请选择类目"
/>
```

#### 禁用选项显示控制

在分类字典页面中可设置选择项的隐藏不可选，组件内获取到数据默认会对这些选项过滤掉，这里配置通过配置disabledType控制这些选择项的显示方式，'show'是可显示但禁用选项，'enable'是可显示并且选项可选。

```vue
<!-- 显示禁用选项 -->
<CommodityCategorySelect
  v-model:value="category"
	disabled-type="show"
  :level="2"
/>
<!-- 启用禁用选项 -->
<CommodityCategorySelect
   v-model:value="category"
   disabled-type="enable"
   :level="3"
/>
```

#### 商品分类场景

不同的场景会决定分类的排序，如平台商品时会根据平台内分类对应的商品数量进行排序，商家订单则会跟根据当前商家账号下分类对应的订单数量进行排序，默认根据当前账号role的决定为平台商品或者商家商品，使用时可根据应用的页面业务场景进行配置。

```vue
<!-- 商家订单 -->
<CommodityCategorySelect
  v-model:value="category"
	:scene="EScene.StoreOrder"
  :level="2"
/>
<!-- 平台订单 -->
<CommodityCategorySelect
   v-model:value="category"
   :scene="EScene.PlatformOrder"
   :level="3"
/>
```

