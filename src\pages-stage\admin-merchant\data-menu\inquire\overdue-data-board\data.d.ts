export interface ListQueryType {
  order_created_at: string;
  data_type: string;
  overdue_node: 0 | 3 | 7;
  cascade_category?: number[];
  cascade_spu_model?: number[];
  cascade_brand?: number[];
  goods_price?: (undefined | number)[];
  is_lock?: number;
  advance_amount?: number;
  method?: string;
  risk_qualitative?: number;
  risk_comprehensive?: number;
  risk_type?: number;
  buy_age?: (undefined | number)[];
}

export interface Option {
  label: string;
  value: string | number;
}

export interface OptionMapType {
  overdue_type: Option[];
  is_lock: Option[];
  goods_value: Option[];
  advance_amount: Option[];
  risk_type: Option[];
  risk_comprehensive: Option[];
  goods_types: Option[];
  model: Option[];
  brand: Option[];
  method: Option[];
  risk_qualitative: Option[];
  overdue_node: Option[];
}

export interface MultipleCascaderItemType {
  value: number;
  level: number;
  renderIds: string[];
}
