import { GET, POST } from '@/services/api';

/** 删除接口 */
export const updateStatus = (params: any) => {
  return GET('/super/recycle/purchased-goods-price/update-status', params);
};

// 获取当前商品Id下内存及对应颜色信息
export const getSkuInfo = (params: any) => {
  return GET('/super/recycle/purchased-goods-price/chart-list', params);
};

// 获取sku图标趋势图
export const getSkuChartDetail = (params: any) => {
  return GET('/super/recycle/purchased-goods-price/chart-detail', params);
};

// 获取估价数据版本
export const getDataVersionList = (params: any) => {
  return GET('/super/recycle/purchased-goods-price/data-version-list', params);
};

// 获取详情数据
export const getDetailData = (params: any) => {
  return GET('/super/recycle/purchased-goods-price/detail', params);
};

// 获取预估回收量
export const getSkuRecycleList = (params: any) => {
  return GET('/super/recycle/purchased-goods-price/recycle-list', params);
};

// 提交规则
export const submitSetting = (params: any) => {
  return POST('/super/recycle/purchased-goods-price/setting', params, {
    headers: { 'content-type': 'application/json' },
  });
};

// 检查当前sku计算规则表格数据情况
export const checkSkuSetting = (params: any) => {
  return POST('/super/recycle/purchased-goods-price/check-setting', params, {
    closeErrorMessage: true,
    headers: { 'content-type': 'application/json' },
  });
};

// 检查一键调价是否正在执行
export const checkBatchSetting = () => {
  return GET('/super/recycle/purchased-goods-price/check-batch-setting');
};

// 获取一键调价-所有商品下所有sku组合
export const getSkuConditionList = () => {
  return GET('/super/recycle/purchased-goods-price/sku-condition-list');
};

// 获取一键调价进度
export const getBatchSettingProgress = () => {
  return GET('/super/recycle/purchased-goods-price/batch-setting-schedule');
};

// 执行一键调价
export const onBatchSetting = () => {
  return GET('/super/recycle/purchased-goods-price/batch-setting');
};

//获取调价方案下拉列表
export const getPriceSchemeList = () => {
  return GET('/super/recycle/purchased-goods-price/get-evaluate-type');
};
