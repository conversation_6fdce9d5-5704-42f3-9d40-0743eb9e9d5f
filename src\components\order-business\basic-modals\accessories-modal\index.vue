<template>
  <a-modal
    v-model:visible="bindVisible"
    :footer="null"
    title="配件清单"
    width="648px"
  >
    <div
      v-for="item in accessoryDataList"
      :key="item.type"
      :class="{ 'accessories-box': item.list?.length > 0 }"
    >
      <template v-if="item.list.length">
        <div
          class="accessories-title flex-wrap flex-y-center"
          :class="{ 'gift-box': item.type === 1 }"
        >
          {{ accessoryTypeMap[item.type] }}
          <span
            v-if="item.type === 2"
            class="accessories-little-title"
          >(注：订单购买后，以下配件默认无需归还)</span>
          <a-button
            v-if="item.type === 3"
            style="margin-left: auto"
            type="link"
            @click="checkAccessoryModalRef?.open()"
          >
            配件说明
          </a-button>
        </div>
        <div class="accessories-content flex-wrap flex-y-center">
          <div
            v-for="(listItem, index) in item.list"
            :key="index"
            class="content-item flex-wrap flex-y-center"
          >
            <div>{{ listItem.accessory_name }}</div>
            <div class="count">
              x{{ listItem.quantity }}
            </div>
            <div
              v-if="item.type === 3 && listItem?.rent_full_days"
              class="days"
            >
              ({{ listItem?.rent_full_days }}天)
            </div>
            <div class="line" />
          </div>
        </div>
      </template>
    </div>
  </a-modal>

  <CheckAccessoryModal ref="checkAccessoryModalRef" />
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed } from 'vue';
import { useCompRef } from 'rrz-web-design';

import type { IAccessoryInfo } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import CheckAccessoryModal from '@/pages-stage/commodity/manage/edit/detailed-content/sell-detail/accessories-list/check-accessory-modal/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  accessoryData: {
    type: Array as PropType<IAccessoryInfo[]>,
    default: () => [],
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const bindVisible = useVModel(props, 'visible', emit);

const accessoryTypeMap = {
  1: '赠品',
  2: '需归还',
  3: '租满x天无需归还',
};

const checkAccessoryModalRef = useCompRef(CheckAccessoryModal)

const accessoryDataList = computed(() => {
  const giftList = props.accessoryData.filter(item => Number(item.accessory_type) === 1);
  const returnList = props.accessoryData.filter(item => Number(item.accessory_type) === 2);
  // 满x天送
  const fullDaysGiftList = props.accessoryData.filter(item => Number(item.accessory_type) === 3);
  return [
    { type: 1, list: giftList },
    { type: 3, list: fullDaysGiftList },
    { type: 2, list: returnList },
  ];
});
</script>

<style scoped lang="less">
.accessories-box {
  width: 600px;
  margin: 0 auto;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 6px;

  &:last-of-type {
    margin-bottom: 0;
  }

  .accessories-title {
    height: 54px;
    padding-left: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    background: rgba(250, 173, 20, 0.15);
    border-radius: 6px 6px 0 0;

    .accessories-little-title {
      height: 22px;
      padding-left: 8px;
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }

  .gift-box {
    background-color: rgba(0, 200, 190, 0.15);
  }

  .accessories-content {
    flex-wrap: wrap;
    padding: 16px 16px 12px;

    .content-item {
      height: 22px;
      margin-bottom: 4px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;

      .count {
        margin-left: 4px;
        color: rgba(6, 21, 51, 0.4 5);
      }
    }

    .line {
      width: 1px;
      height: 14px;
      margin: 0 8px;
      background: rgba(6, 21, 51, 0.15);
    }
  }
}

.accessories-box:not(:last-of-type) {
  margin-bottom: 16px;
}
</style>
