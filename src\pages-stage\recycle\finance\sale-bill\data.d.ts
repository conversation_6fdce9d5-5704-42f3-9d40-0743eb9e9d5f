export interface ISearchParams {
  rs_order_id: string;
  rsp_order_id: string;
  order_pay_status: string;
  payment_flow_sn: string;
  category_brand_model: string;
  category: number[];
  pay_type: string | number[];
  custom_type: number[];
  start_at: string;
  end_at: string;
  source_type: number[];
  create_time: string[];
}

export interface ISaleBillList {
  rsp_order_id: string;
  source_type: string;
  pay_price: string;
  pay_type: string;
  flow_sn_type: string;
  payment_flow_sn: string;
  created_at: string;
  back_price: string;
  order_pay_status: string;
  order_price: string;
  rs_order_id: string;
  server_id: string;
  custom_type: string;
  model_id: string;
  brand_id: string;
  model_name: string;
  brand_name: string;
}

// 备注
export type RemarkType = {
  created_at: string;
  last_remark: string;
  remark_by: string;
  remark_num: number;
  remark_tag: string;
  union_id: string;
};
