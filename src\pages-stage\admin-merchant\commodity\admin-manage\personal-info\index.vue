<template>
  <div
    ref="createCreative"
    class="page"
  >
    <div class="page-header p-wrap">
      <span class="text">子账号管理</span>
      <a-button
        type="primary"
        @click="showAddModal"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        添加成员
      </a-button>
    </div>
    <div class="search-wrap">
      <form-create
        ref="searchFormRef"
        v-model:value="UserAdminDao"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="getTableList"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleSet"
          >
            重置
          </a-button>
        </template>
      </form-create>
    </div>
    <div class="table-wrap">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :pagination="page"
        :scroll="{ x: 'max-content' }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <a-space
              :size="16"
              wrap
            >
              <a-button
                class="r-btn-link"
                type="link"
                @click="fetchTree(record.id)"
              >
                分配权限
              </a-button>
              <a-button
                class="r-btn-link"
                type="link"
                @click="handleUpdate(record)"
              >
                更改名称
              </a-button>
              <a-button
                class="r-btn-danger"
                type="link"
                @click="handleDelete(record.id)"
              >
                删除
              </a-button>
              <a-button
                v-if="permission"
                class="r-btn-link"
                type="link"
                @click="JumpValidity(record)"
              >
                有效期设置
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 分配权限弹框 -->

    <a-modal
      v-model:visible="treeModal.treeModalVisible"
      style="width: 600px"
      title="分配权限"
      @ok="sendTreeForm"
    >
      <div class="experies">
        <span> 有效期 : </span>
        <a-range-picker
          v-model:value="validityDate"
          :allow-clear="false"
          :get-popup-container="createMod"
          :placeholder="['开始日期', '结束日期']"
          :ranges="preset"
          :show-time="{ format: 'HH:mm:ss' }"
          style="width: 90%"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </div>
      <div style="position: relative; max-height: 600px; overflow-y: scroll">
        <template v-if="treeData && treeData.length > 0">
          <a-tree
            v-model:checkedKeys="checkedKeys"
            checkable
            :field-names="fieldNames"
            :tree-data="treeData"
            @check="check"
          >
            <template #title="{ value, key }">
              <span
                v-if="key === '0-0-1-0'"
                style="color: #3777ff"
              >{{ value }}</span>
              <template v-else>
                {{ value }}
              </template>
            </template>
          </a-tree>
        </template>
        <div
          v-else
          class="no-tree-data"
        >
          <a-spin />
        </div>
      </div>
    </a-modal>
    <!-- 添加成员 / 更改名称弹框 -->

    <form-popup
      v-model:visible="addOrEditModalVisible"
      :form="{
        value: formState,
        formGroup: addOrEditFormGroup,
        originProps: {
          layout: 'vertical',
        },
      }"
      :origin-props="{
        title: isEdit ? '更改名称' : '添加成员',
      }"
      @submit="sendForm"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, TreeProps } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { FormCreate } from '@/components/form-create';
import { useTable } from '@/hook/component/use-table';

import { addOrEditFormGroup, columns, searchFormGroup } from './config';
import { addUserInfo, deleteUserInfo, fetchTreeData, getUserInfo, updateTreeData, updateUserInfo } from './service';

const router = useRouter();

/**
 * 检索表单
 */
const UserAdminDao = reactive({
  username: '',
  phone: '',
  created_at: '',
});

// 检索表单实例
const searchFormRef = ref<InstanceType<typeof FormCreate>>();

/**
 * 渲染表格
 */
const imgOssServer = ref<string>('');
const { list, listLoading, page, getTableList, tableChange } = useTable({
  url: '/admin/index-inf',
  formatHandle: res => {
    imgOssServer.value = res.data.imgOssServer;
    return res.data.list;
  },
  searchForm: {
    UserAdminDao,
  },
});
const permission = ref<boolean>(false);
getUserInfo().then(res => {
  permission.value = res.data.showValidityBtn;
  getTableList();
});
/**
 * 模态框表单
 */
const formState = reactive({
  username: '',
  phone: '',
  id: undefined,
});

/**
 * 重置搜索表单
 */
const handleSet = async () => {
  (searchFormRef.value?.getFormRef() as any).resetFields();
  await getTableList();
};
// 控制模态跨显示与否
const addOrEditModalVisible = ref<boolean>(false);
// 展示模态框
const showAddModal = async () => {
  isEdit.value = false;
  formState.username = '';
  formState.phone = '';
  formState.id = undefined;
  addOrEditModalVisible.value = true;
};
// 控制是否是编辑状态
const isEdit = ref<boolean>(false);

// 模态框确认
const sendForm = async () => {
  const res = isEdit.value
    ? await updateUserInfo({
        User: formState,
        id: formState.id,
      })
    : await addUserInfo({
        User: formState,
      });
  message.success(res.message);
  addOrEditModalVisible.value = false;
  await getTableList();
};
// 更改名称
const handleUpdate = (record: any) => {
  isEdit.value = true;
  const { username, phone } = record;
  formState.username = username;
  formState.phone = phone;
  formState.id = record.id;
  addOrEditModalVisible.value = true;
};

// 删除
const handleDelete = (id: number | string) => {
  Modal.confirm({
    title: '删除该数据将无法恢复，是否继续？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteUserInfo({
        id,
      });
      message.success('删除成功');
      await getTableList();
    },
  });
};

//有效期设置
const JumpValidity = (record: any) => {
  router.push({ path: '/merchant/commodity/manage/menber-validity', query: { ids: record.id } });
};

const treeData = ref<TreeProps['treeData']>([]);
// 获取树形数据
const fetchTree = async (id: string | number) => {
  treeModal.id = id;
  treeModal.treeModalVisible = true;
  treeData.value = [];
  const res = await fetchTreeData({
    id,
  });
  treeData.value = res.data.auth;
  checkedKeys.value = res.data.subUserAuth;
  validityDate.value = [res.data.asicsData.validity_start, res.data.asicsData.validity_end];
};

const fieldNames: TreeProps['fieldNames'] = {
  title: 'value',
};

const treeModal = reactive<{
  treeModalVisible: boolean;
  id: undefined | string | number;
  auth_rule: string[];
}>({
  treeModalVisible: false,
  id: undefined,
  auth_rule: [],
});

const sendTreeForm = async () => {
  const start =
    typeof validityDate.value[0] === 'string'
      ? validityDate.value[0]
      : validityDate.value[0].format('YYYY-MM-DD HH:mm:ss');
  const end =
    typeof validityDate.value[1] === 'string'
      ? validityDate.value[1]
      : validityDate.value[1].format('YYYY-MM-DD HH:mm:ss');
  const res = await updateTreeData({
    id: treeModal.id,
    auth_rule: treeModal.auth_rule,
    validity_start: start,
    validity_end: end,
  });
  treeModal.treeModalVisible = false;
  message.success(res.message);
};

const checkedKeys = ref<string[]>([]);
const validityDate = ref<any[]>([dayjs(), '2099-01-01 23:59:59']);

const fetchKeys = (target: TreeProps['treeData']) => {
  const result: any[] = [];
  target?.forEach(i => {
    if (i.children) fetchKeys(i.children);
    else result.push(i.key);
  });
  return result;
};

const check = (checkedKeys: any, e: any) => {
  treeModal.auth_rule = fetchKeys(e.checkedNodes);
};

const preset = {
  永久: [dayjs(), dayjs('2099-01-01 23:59:59')],
  一个月: [dayjs(), dayjs().add(1, 'month')],
  三个月: [dayjs(), dayjs().add(3, 'months')],
  六个月: [dayjs(), dayjs().add(6, 'months')],
  一年: [dayjs(), dayjs().add(1, 'year')],
  两年: [dayjs(), dayjs().add(2, 'years')],
  三年: [dayjs(), dayjs().add(3, 'years')],
};
const createCreative = ref(null);
const createMod = () => {
  return createCreative.value;
};
</script>

<style scoped lang="less">
.create-creative {
  :deep(.ant-picker-range-wrapper) {
    text-align: center;
  }
}
.page {
  min-height: 100vh;
  background-color: #fff;
}
.p-wrap {
  padding: 24px;
  background: #fff;
}
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}

.search-wrap {
  display: flex;
  flex-wrap: wrap;
  padding: 0 24px;
  background-color: #fff;
  :deep(.ant-form-item) {
    margin-bottom: 24px;
  }
}
.table-wrap {
  padding: 0 24px 24px;
  background-color: #fff;
}

.no-tree-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.experies {
  width: 100%;
  padding: 0 0 16px 0;
}

:deep(.ant-picker-range-wrapper) {
  display: block;
}
</style>
