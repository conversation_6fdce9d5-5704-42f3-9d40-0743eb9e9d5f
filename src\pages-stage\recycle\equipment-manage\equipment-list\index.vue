<template>
  <layout-admin-page
    class="mini-order-list"
    :navs="['趣回收', '设备管理', '设备查询表']"
    title="设备查询表"
  >
    <div class="form-wrap">
      <form-create
        ref="searchFormRef"
        v-model:value="searchParams"
        class="search-form-col"
        :form-group="searchFormGroup"
        :origin-props="{
          layout: 'inline',
        }"
      >
        <template #imei>
          <a-form-item
            label="IMEI"
            name="imei"
          >
            <a-input-search
              v-model:value="searchParams.imei"
              style="width: 216px"
              @search="handleBatch('imei')"
            >
              <template #enterButton>
                <a-button> 批量</a-button>
              </template>
            </a-input-search>
          </a-form-item>
        </template>
        <template #sn>
          <a-form-item
            label="SN"
            name="sn"
          >
            <a-input-search
              v-model:value="searchParams.sn"
              style="width: 225px"
              @search="handleBatch('sn')"
            >
              <template #enterButton>
                <a-button> 批量</a-button>
              </template>
            </a-input-search>
          </a-form-item>
        </template>
        <template #data_type>
          <a-form-item
            label="选择时间"
            name="data_type"
            :span="24"
          >
            <div class="data_type">
              <a-select
                v-model:value="searchParams.data_type"
                :options="dateTypeSelect"
                placeholder="请选择"
                style="width: 98px"
                @change="onDateTypeChange"
              />
              <a-form-item-rest>
                <a-range-picker
                  v-if="searchParams.data_type === '1'"
                  v-model:value="searchParams.timeType"
                  class="time-picker"
                  style="width: 366px"
                  value-format="YYYY-MM-DD"
                />
                <a-date-picker
                  v-else
                  v-model:value="searchParams.timeType"
                  class="time-picker"
                  picker="month"
                />
              </a-form-item-rest>
            </div>
          </a-form-item>
        </template>
        <template #buttons>
          <a-button
            style="margin-left: 6px"
            type="primary"
            @click="handleSearch"
          >
            搜索
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="handleReset"
          >
            重置
          </a-button>
          <a-button
            style="margin-left: 6px"
            @click="exportAction"
          >
            <template #icon>
              <vertical-align-bottom-outlined />
            </template>
            导出
          </a-button>
        </template>
      </form-create>
    </div>
    <a-divider :style="{ margin: '15px 0 0 0' }" />
    <div class="tabs">
      <a-tabs
        v-model:activeKey="searchParams.order_type"
        @change="getTableList('search')"
      >
        <a-tab-pane
          key="0"
          :tab="`个人订单(${orderCount.normal_order})`"
        />
        <a-tab-pane
          key="1"
          :tab="`企业订单(${orderCount.batch_order})`"
        />
      </a-tabs>
    </div>
    <customer-table
      :columns="listColumns"
      :data-source="dataSource"
      :loading="loading"
    >
      <template #rowHeader="{ record }">
        <div class="order-head">
          <div class="order-flex">
            <div class="weight-color">
              订单号：{{ record.order_id }}
            </div>
            <copy-outlined
              class="copy-btn"
              @click="copyToClipboard(record.order_id)"
            />
          </div>
          <div class="weight-color">
            回收入库时间：{{ record.enter_at || '-' }}
          </div>
          <div class="weight-color">
            订单发货时间：{{ record.out_at || '-' }}
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'imei'">
          <div class="order-body">
            <div class="order-flex">
              <div>IMEI码：{{ record.imei }}</div>
              <copy-outlined
                class="copy-btn"
                @click="copyToClipboard(record.imei)"
              />
            </div>
            <div>批次：{{ record.batch_no }}</div>
            <div>检测结果：{{ record.report_result_trans }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'user_id'">
          <div class="order-body">
            <div>用户ID：{{ record.user_id }}</div>
            <div>
              收款人姓名：{{ record.payment_name }}
              <EyeOutlined
                v-if="isEye"
                class="eye-icon"
                @click="
                  getSensitiveInfo({
                    order_id: record.order_id,
                    order_type: record.order_type,
                    field: 'payment_name',
                  })
                "
              />
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'order_user_id'">
          <div class="order-body">
            <div>用户ID：{{ record.order_user_id || '-' }}</div>
            <div>
              付款人姓名：{{ record.order_username }}
              <EyeOutlined
                v-if="isEye && record.order_user_id"
                class="eye-icon"
                @click="
                  getSensitiveInfo({
                    order_id: record.order_id,
                    order_type: record.order_type,
                    field: 'order_username',
                  })
                "
              />
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'return_type'">
          <div class="order-body">
            {{ record.return_type || '-' }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'sku_detail'">
          <div class="order-body">
            <div>{{ record.sku_detail || '-' }}</div>
            <div>SKUID：{{ record.sku_id || '-' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'receive_logistics_no'">
          <div class="order-body">
            <div>物流单号：{{ record.receive_logistics_no || '-' }}</div>
            <div>入库仓库：{{ record.warehouse_name || '-' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'pay_at'">
          <div class="order-body">
            <div>
              回收金额：<span class="weight-color">{{
                record.purchased_price ? `￥${record.purchased_price}` : '-'
              }}</span>
            </div>
            <div>付款时间：{{ record.pay_at || '-' }}</div>
            <div>
              销售金额：<span style="color: #00c8be">{{ record.sale_amount ? `￥${record.sale_amount}` : '-' }}</span>
            </div>
            <div>收款时间：{{ record.sale_pay_at || '-' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'return_amount'">
          <div class="order-body">
            <div>售后处理结果：{{ record.after_sale_result || '-' }}</div>
            <div>退货时间：{{ record.return_at || '-' }}</div>
            <div>
              退货金额：<span style="color: #ff4d4f">{{
                record.return_amount ? `￥${record.return_amount}` : '-'
              }}</span>
            </div>
            <div>退款时间：{{ record.return_pay_at || '-' }}</div>
          </div>
        </template>
      </template>
    </customer-table>
    <div
      v-if="page.total"
      class="pagination-box"
    >
      <div class="total-box">
        <span class="total-num">共<span class="value">{{ page.total }}</span>条记录</span>
        <span class="page-num">第<span class="value">{{ page.current }} / {{ Math.ceil((page.total as number) / (page.pageSize as number)) }}</span>页</span>
      </div>
      <div class="pagination">
        <a-pagination
          v-model:current="page.current"
          v-model:page-size="page.pageSize"
          :page-size-options="page.pageSizeOptions"
          show-quick-jumper
          show-size-changer
          :total="page.total"
          @change="tableChange({ current: page.current, pageSize: page.pageSize })"
        />
      </div>
    </div>
  </layout-admin-page>
  <BatchModal
    v-if="batchModalVisible"
    v-model:visible="batchModalVisible"
    :label-type="imei === 'imei' ? 'IMEI' : 'SN'"
    :type="imei"
    @change="handleBatchChange"
  />
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { CopyOutlined, EyeOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import dayJS from 'dayjs';

import useSensitiveLookModel from '@/composables/use-sensitive-look-model';
import { useTable } from '@/hook/component/use-table';
import CustomerTable from '@/pages-stage/recycle/common/components/customer-table.vue';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import useCeiling from '@/pages-stage/recycle/common/use-ceiling';
import useExportModalConfirm from '@/pages-stage/recycle/finance/self-order-pay/use-export-modal-confirm';
import { copyToClipboard } from '@/utils/base';

import BatchModal from './components/batch-modal.vue';
import { listColumns, searchFormGroup } from './config';
import { IDateSelectTypeParams, IHideParams, IOrderCountParams, ISearchParams } from './data';
import { equipmentExport, getCount, getEyeInfo, getHideInfo, getList, getSelectList } from './service';

const route = useRoute();

const searchParams = ref<ISearchParams>({
  order_id: '',
  order_type: '0',
  sku_id: null,
  sku_detail: '',
  is_lock: null,
  server_id: null,
  recycle_from: '',
  sale_to: '',
  receive_logistics_no: '',
  send_logistics_no: '',
  imei: '',
  sn: '',
  batch_no: '',
  data_type: '1',
  timeType: [],
  return_type: null,
  order_time: [],
  afterSale_time: [],
  recycle_time: [],
});

function getRequestParams(params: ISearchParams) {
  const requestParams = { ...params };
  if (searchParams.value.timeType || searchParams.value.timeType?.length) {
    // 按日统计，默认数据格式为数组； 按月统计，则数据是字符串，选择单个年份；
    if (requestParams.data_type === '1') {
      requestParams.created_at_gt = searchParams.value.timeType[0];
      requestParams.created_at_lt = searchParams.value.timeType[1];
    } else {
      requestParams.created_at_gt = dayJS(searchParams.value.timeType).startOf('month').format('YYYY-MM-DD');
      requestParams.created_at_lt = dayJS(searchParams.value.timeType).endOf('month').format('YYYY-MM-DD');
    }
  }

  if (searchParams.value.order_time?.length) {
    requestParams.order_at_gt = searchParams.value.order_time[0];
    requestParams.order_at_lt = searchParams.value.order_time[1];
  }

  if (searchParams.value.afterSale_time?.length) {
    requestParams.after_at_gt = searchParams.value.afterSale_time[0];
    requestParams.after_at_lt = searchParams.value.afterSale_time[1];
  }

  if (searchParams.value.recycle_time?.length) {
    requestParams.recycle_at_gt = searchParams.value.recycle_time[0];
    requestParams.recycle_at_lt = searchParams.value.recycle_time[1];
  }
  delete requestParams.timeType;
  delete requestParams.data_type;
  delete requestParams.order_time;
  delete requestParams.afterSale_time;
  delete requestParams.recycle_time;
  return requestParams;
}

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable({
  api: getList,
  searchForm: searchParams.value,
  totalKey: 'data.pageInfo.count',
  formatSearchValue: (params: any) => {
    return getRequestParams(params);
  },
});

const orderCount = ref<IOrderCountParams>({
  normal_order: '0',
  batch_order: '0',
});
const getOrderCount = () => {
  const requestParams = getRequestParams(searchParams.value);
  getCount(requestParams).then(res => {
    orderCount.value.normal_order = res.data?.normal_order;
    orderCount.value.batch_order = res.data?.batch_order;
  });
};

// 获取url参数进行默认查询
const getUrlParams = () => {
  const query = route.query || {};
  const keysArr = Object.keys(query).filter(key => !['role', 'origin'].includes(key));
  if (keysArr.length) {
    keysArr.forEach(key => {
      const value = query[key];
      switch (key) {
        case 'order_start_time':
          searchParams.value.order_time[0] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'order_end_time':
          searchParams.value.order_time[1] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'after_start_time':
          searchParams.value.afterSale_time[0] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'after_end_time':
          searchParams.value.afterSale_time[1] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'return_type':
          searchParams.value.return_type = Number(value);
          break;
        case 'startTime':
          searchParams.value.recycle_time[0] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'endTime':
          searchParams.value.recycle_time[1] = dayJS(value).format('YYYY-MM-DD');
          break;
        default:
          if (searchParams.value.hasOwnProperty(key)) {
            searchParams.value[key] = value;
          }
          break;
      }
    });
  }
};

const [batchModalVisible, { setTrue: showBatchModal }] = useBoolean();
const imei = ref<string>('imei');
const handleBatch = (val: string) => {
  imei.value = val;
  showBatchModal();
};
const handleBatchChange = (value: any) => {
  if (imei.value === 'imei') {
    searchParams.value.imei = value.join(',');
  } else {
    searchParams.value.sn = value.join(',');
  }
};

const dateTypeSelect = ref<IDateSelectTypeParams[]>([]);
//获取下拉列表数据
const getTypeList = async () => {
  const res = await getSelectList();
  dateTypeSelect.value = res.data.data_type || [];
};
getTypeList();

const onDateTypeChange = () => {
  searchParams.value.timeType = null;
};

//脱敏-是否显示眼睛
const isEye = ref<number>(1);
const getExitEye = () => {
  getEyeInfo().then(res => {
    if (res.code === 200) isEye.value = res.data.recycleShowViewHideBtn;
  });
};

//获取脱敏数据
const getSensitiveInfo = (params: IHideParams) => {
  getHideInfo(params).then(res => {
    if (res.code === 200) useSensitiveLookModel(res.data.value);
  });
};

//搜索
const handleSearch = () => {
  getTableList('search');
  getOrderCount();
};

const searchFormRef = ref<any>();
//重置
const handleReset = () => {
  searchFormRef.value.getFormRef().resetFields();
  searchParams.value.order_type = '0';
  searchParams.value.timeType = [];
  handleSearch();
};

//导出
const exportAction = () => {
  const requestParams = getRequestParams(searchParams.value);
  //个人订单和企业订单一起导出
  delete requestParams.order_type;
  const { down: downOrder } = useExportModalConfirm(
    async () => {
      const res = await equipmentExport(requestParams);
      return res;
    },
    `${route.query.origin}/super/async-export/index`,
    {
      title: '设备查询导出',
      content: '请确认是否导出该设备查询的数据',
    },
  );
  downOrder();
};

onMounted(() => {
  getUrlParams();
  getExitEye();
  handleSearch(); //搜索
  useCeiling(); //表格吸顶
});
</script>
<style lang="less" scoped>
.mini-order-list {
  min-width: 1160px;

  .form-wrap {
    width: 100%;
    padding: 0 24px 24px;
    background-color: #ffff;
  }

  .search-form-col {
    &.ant-form {
      display: flex;
      row-gap: 24px;
      flex-wrap: wrap;
      margin: 0 -8px;

      .ant-form-item {
        flex-basis: 16.67%;
        margin-right: 0;
        margin-bottom: 0;
        padding: 0 8px;
      }
    }
  }

  @media screen and (max-width: 1160px) {
    .search-form-col .ant-form-item {
      flex-basis: 25%;
    }
  }

  .data_type {
    display: flex;
    align-items: center;
  }

  .time-picker {
    width: 170px;
    border-left: none;
    border-radius: 0 4px 4px 0;
  }

  .tabs {
    padding-left: 25px;
    font-weight: 500;
    font-size: 14px;

    :deep(.ant-tabs-nav) {
      margin-bottom: 24px;
    }
  }

  //订单头部
  .order-head {
    display: flex;
    gap: 32px;
    align-items: center;
    height: 54px;
    padding-left: 16px;
    color: #061533 85%;
  }

  .order-flex {
    display: flex;
    align-items: center;
  }

  .copy-btn {
    margin-left: 8px;
    vertical-align: middle;
    cursor: pointer;

    &:hover {
      color: #3777ff;
    }
  }

  .weight-color {
    color: #061533d8;
    font-weight: 500;
  }

  .eye-icon {
    cursor: pointer;

    &:hover {
      color: #3777ff;
    }
  }

  //body列表
  .order-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    padding: 16px;
  }

  .pagination-box {
    position: fixed;
    right: 16px;
    bottom: 0;
    left: 16px;
    z-index: 9;
    height: 64px;
    margin: 0;
    padding: 21px 24px;
    background-color: #fff;
    border-top: 1px solid rgba(6, 21, 51, 0.06);

    .ant-pagination-total-text {
      flex: 1;
    }

    .total-box {
      float: left;
      color: rgba(6, 21, 51, 0.45);

      .page-num {
        margin-left: 16px;
      }

      .value {
        margin: 0 4px;
        color: rgba(6, 21, 51, 0.65);
      }
    }

    .pagination {
      float: right;
    }
  }
}
</style>
