<template>
  <layout-admin-page
    :content-style="{ padding: '0', backgroundColor: '#f0f2f5' }"
    :navs="['趣回收', '订单管理', '个人回收订单']"
    title="个人回收订单"
    :top-style="{ marginBottom: 0 }"
  >
    <template #extra>
      <a-dropdown>
        <a-button
          class="green-btn"
          @click.prevent
        >
          <template #icon>
            <vertical-align-bottom-outlined />
          </template>
          导出
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <a
                href="javascript:;"
                @click="onExport('1')"
              >导出订单</a>
            </a-menu-item>
            <a-menu-item>
              <a
                href="javascript:;"
                @click="onExport('2')"
              >导出检测报告</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <div class="search-form">
      <a-form
        ref="formRef"
        layout="inline"
        :model="formState"
        style="width: 100%"
      >
        <a-row
          :gutter="[0, 12]"
          style="width: 100%"
        >
          <a-col :span="5">
            <a-form-item
              label="订单类型"
              name="orderStatus"
            >
              <a-select
                v-model:value="formState.orderStatus"
                :options="orderStatusOptions"
                placeholder="请选择订单类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item
              label="订单号"
              name="orderId"
            >
              <a-input
                v-model:value="formState.orderId"
                placeholder="请输入订单号"
              />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item
              label="订单日期"
              name="date"
            >
              <a-range-picker
                v-model:value="formState.date"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item
              label="运单号"
              name="tracking_num"
            >
              <a-input
                v-model:value="formState.tracking_num"
                :maxlength="40"
                placeholder="请输入运单号"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="批量回收IMEI"
              name="batch_imei"
            >
              <a-input-search
                v-model:value="formState.batch_imei"
                style="min-width: 136px"
                @search="handleBatch"
              >
                <template #enterButton>
                  <a-button>批量</a-button>
                </template>
              </a-input-search>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="分类"
              name="category_id"
            >
              <ProdutionCascader v-model:modelValue="formState.category_id" />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item
              label="回收模式"
              name="recycle_mode"
            >
              <a-select
                v-model:value="formState.recycle_mode"
                :options="[{ label: '批量回收', value: 1 }]"
                placeholder="请选择回收模式"
              />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item
              label="批次号"
              name="batch_no"
            >
              <a-input
                v-model:value="formState.batch_no"
                :allow-clear="true"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="订单完成时间"
              name="complete_time"
            >
              <a-range-picker
                v-model:value="formState.complete_time"
                format="YYYY-MM-DD"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item>
              <a-button
                style="border-radius: 4px"
                type="primary"
                @click="onHandleControl().search"
              >
                查 询
              </a-button>
              <a-button
                style="margin-left: 10px; border-radius: 4px"
                @click="onHandleControl().reset"
              >
                重 置
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="list-mian">
      <customer-table
        :columns="listColumns"
        :data-source="tableList"
        :loading="listLoading"
        :table-thead-class="['recycle-sale-table-thead']"
      >
        <template #rowHeader="{ record }">
          <div class="order-head">
            <span class="order-text">订单号：{{ record.orderInfo?.orderId }}</span>
            <span
              v-if="record.batchNo"
              class="order-text"
            >批次号：{{ record.batchNo }}</span>
            <span
              v-if="record.imei"
              class="order-text"
            >IMEI：{{ record.imei }}
              <form-outlined
                v-if="record.order_status === '1' || record.order_status === '2'"
                class="imei-icon"
                @click="updateImei(record.orderId, record.imei)"
              />
            </span>
            <span class="order-text">创建日期：{{ record.orderInfo?.createdAt }}</span>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'goods_info'">
            <!-- 商品信息 -->
            <div class="goods-info">
              <a-image
                :src="record.goodsInfo?.goodsImage"
                :width="60"
              />
              <span style="padding-left: 8px">{{ record.goodsInfo?.goodsName }}</span>
            </div>
          </template>
          <template v-if="column.key === 'logisticInfo'">
            <!-- 物流信息 -->
            <div class="logistic-info">
              <div>
                <span class="label">寄出：</span>
                <span v-if="record.logisticInfo.tracking_num">
                  <a
                    class="a-link-hover"
                    style="padding: 0"
                    @click="() => onSeeLogisticDetail(false, record.logisticInfo, record)"
                  >
                    {{ record.logisticInfo.tracking_num }}
                  </a>

                  <DecryptField
                    v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                    :field-item="{
                      box: 'text',
                      id: record.orderId,
                      field: 'tracking_num',
                      field_type: 'SF',
                      type: 158,
                    }"
                  />
                </span>
                <span v-else>暂无数据</span>
              </div>
              <div>
                <span class="label">退回：</span>
                <span v-if="record.logisticInfo.return_tracking_num">
                  <a
                    class="a-link-hover"
                    style="padding: 0"
                    type="link"
                    @click="() => onSeeLogisticDetail(true, record.logisticInfo, record)"
                  >
                    {{ record.logisticInfo.return_tracking_num }}
                  </a>
                  <DecryptField
                    v-if="viewPlaintextAuth.recycleShowViewHideBtn"
                    :field-item="{
                      box: 'text',
                      id: record.orderId,
                      field: 'return_tracking_num',
                      field_type: 'SF',
                      type: 158,
                    }"
                  />
                </span>
                <span v-else>暂无数据</span>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'evaluate_price'">
            <!-- 估计结果 -->
            <div class="evaluate-price">
              <div class="evaluate-price-block">
                <span class="evaluate-price-text">估价金额：¥{{ makeDecimalPoint(String(record.evaluate_price)) }}</span>
                <template v-if="record.couponInfo">
                  <div>
                    <div>{{ `(含￥${makeDecimalPoint(String(record.couponInfo.discount))}营销券` }}</div>
                    <div>{{ `营销券ID： ${record.couponInfo.couponMarketId})` }}</div>
                  </div>
                </template>
                <a
                  class="a-link-hover"
                  @click="() => onSeeEvaluate(record.orderId)"
                > 查看估价报告 </a>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'fixprice_info'">
            <!-- 定价结果 -->
            <div class="evaluate-price">
              <div
                v-if="Number(record.showBtnViewQuaReport) === 1"
                class="evaluate-price-block"
              >
                <span class="evaluate-price-text">定价金额：
                  <span class="evaluate-price-text2">¥{{ makeDecimalPoint(record.purchased_price) }}</span></span>
                <a
                  class="a-link-hover"
                  @click="() => onSeeQuality(record.orderId)"
                > 查看检测结果 </a>
              </div>
              <span
                v-else
                class="evaluate-price-text"
              >暂无定价</span>
            </div>
          </template>
          <template v-if="column.key === 'order_status'">
            <!-- 订单状态 -->
            <div class="order-status">
              <div>{{ record.order_status_text }}</div>
              <template v-if="record.cancel_reason_role">
                <div>取消类型：{{ record.cancel_reason_role }}</div>
                <div>取消原因：{{ record.cancel_reason_text }}</div>
              </template>
            </div>
          </template>
          <template v-if="column.key === 'operation'">
            <!-- 操作 -->
            <!-- <operations /> -->
            <div class="operation">
              <div
                class="flex-wrap flex-vertical"
                style="margin-left: -16px"
              >
                <a-button
                  v-if="record.canSetPurchasedPrice"
                  type="link"
                  @click="() => onPricing(record.orderId)"
                >
                  定价
                </a-button>
                <a-button
                  v-if="!!record.showOrderPayBtn"
                  type="link"
                  @click="() => onSeebill(record.orderId)"
                >
                  查看账单
                </a-button>
                <!--
                顺丰取件操作项 只出现在以下订单状态下：用户已取消（取消原因：放弃回收）、平台已关闭（取消原因：，业务取消（不包含待取件状态下取消））
                a. 订单已取消或已关闭
                and
                b. 取消或关闭前，订单状态非待取件状态。
                -->
                <a-button
                  v-if="record.showBtnOrderSf"
                  type="link"
                  @click="() => onDelivery(record.orderId)"
                >
                  顺丰取件
                </a-button>
                <a-button
                  v-if="record.showBtnCancelOrderSf"
                  danger
                  type="link"
                  @click="() => onCancelDelivery(record.orderId)"
                >
                  取消预约
                </a-button>
                <!-- 待收件、待入库、待确认、待打款、待退货、打款失败 -->
                <a-button
                  v-if="Number(record.canCloseOrder) === 1 && [1, 2, 5, 6, 7, 11].includes(Number(record.order_status))"
                  danger
                  type="link"
                  @click="() => onCloseOrder(record.orderId)"
                >
                  关闭订单
                </a-button>
                <a-button
                  v-if="record.order_protocol_url"
                  type="link"
                  @click="openEvidence({ url: record.order_protocol_url, time: record.order_protocol_created_at })"
                >
                  查看证据
                </a-button>
              </div>
            </div>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <tr class="expanded-row">
            <td :colspan="listColumns.length">
              <div class="remark">
                <span class="merchant-info-text"> 用户ID:{{ record.userInfo?.userId }}</span>
                <span class="merchant-info-text">
                  <span>用户手机:</span>
                  <span>{{ record.userInfo?.phone }}</span>
                  <span
                    v-if="/\*/.test(record.userInfo?.phone) && record.showViewHideBtn"
                    @click="queryHideInfo('phone', record.orderId)"
                  ><EyeOutlined /></span>
                </span>
                <span class="merchant-info-text">
                  <span>收款账号:</span>
                  <span>{{ record.userInfo?.paymentAccount }}</span>
                  <span
                    v-if="/\*/.test(record.userInfo?.paymentAccount) && record.showViewHideBtn"
                    @click="queryHideInfo('paymentAccount', record.orderId)"
                  >
                    <EyeOutlined /></span>
                </span>
                <span class="merchant-info-text">
                  <span>收款人姓名:</span>
                  <span>{{ record.userInfo?.paymentName }}</span>
                  <span
                    v-if="/\*/.test(record.userInfo?.paymentName) && record.showViewHideBtn"
                    @click="queryHideInfo('paymentName', record.orderId)"
                  >
                    <EyeOutlined /></span>
                </span>
                <span class="merchant-info-text">
                  <span>用户收件地址:</span>
                  <span>{{ record.logisticInfo?.addr }}</span>
                  <span
                    v-if="/\*/.test(record.logisticInfo?.addr) && record.showViewHideBtn"
                    @click="queryHideInfo('addr', record.orderId)"
                  >
                    <EyeOutlined /></span>
                </span>
              </div>
            </td>
          </tr>
          <!-- 备注栏 -->
          <tr class="expanded-row">
            <td :colspan="listColumns.length">
              <div class="remark">
                <crm-remark
                  :id="`${record.orderId}_recyOrdList`"
                  :block-flex="false"
                  :item="record.remarks"
                  layout="horizontal"
                  log-link="/crm/log"
                  push-link="/crm/push"
                  size="block"
                  @add-success="onLoadList"
                >
                  <template #emptyText>
                    暂无备注
                  </template>
                </crm-remark>
              </div>
            </td>
          </tr>
        </template>
      </customer-table>
    </div>
  </layout-admin-page>
  <!-- 分页 -->
  <div class="fixed-box">
    <list-pagination
      :page="page"
      @table-change="tableChange"
    />
  </div>
  <pricing-modal
    v-if="pricingModalVisible"
    v-model:visible="pricingModalVisible"
    :order-id="currentOrderId"
    @on-load-list="onLoadList"
  />
  <delivery-modal
    v-if="deliveryModalVisible"
    v-model:visible="deliveryModalVisible"
    :order-id="currentOrderId"
    @on-load-list="onLoadList"
  />

  <evaluate-modal
    v-if="evaluateModalVisible"
    v-model:visible="evaluateModalVisible"
    :order-id="currentOrderId"
  />
  <QualityReport
    :is-new="true"
    :item="{}"
    :report-data="currentReportData"
    :visible="qualityReportVisible"
    @close="hideQualityReport"
  />
  <LogisticDraw
    v-model:visible="logisticDrawVisible"
    :logistic-info="logisticInfo"
  />

  <EvidenceModal
    v-model:visible="evidenceModalVisible"
    :evidence-obj="evidenceObj"
  />

  <BatchModal
    v-if="batchModalVisible"
    v-model:visible="batchModalVisible"
    @change="handleBatchChange"
  />

  <UpdateImeiModal
    v-if="imeiModalVisible"
    v-model:visible="imeiModalVisible"
    :imei="currentImei"
    :order-id="currentOrderId"
    @on-load-list="onLoadList"
  />
</template>
<script lang="ts" setup>
import { createVNode, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { FormInstance, message, Modal } from 'ant-design-vue';
import { EyeOutlined, FormOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { AxiosResponse } from 'axios';
import dayJS from 'dayjs';

import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
import QualityReport from '@/components/quality-report/quality-report.vue';
import { useViewPlaintextAuth } from '@/composables/use-decrypt-data';
import { useTable } from '@/hook/component/use-table';
import CustomerTable from '@/pages-stage/recycle/common/components/customer-table.vue';
import useCeiling from '@/pages-stage/recycle/common/use-ceiling';
import ListPagination from '@/pages-stage/recycle/order/enterprise-order-list/components/list-pagination.vue';
import { POST } from '@/services/api';

import useBoolean from '../../common/use-boolean';
import DeliveryModal from './components/delivery-modal.vue';
import EvaluateModal from './components/evaluate-modal.vue';
import LogisticDraw from './components/logistic-drawer.vue';
import PricingModal from './components/pricing-modal.vue';
import { listColumns } from './config';
import { IOrderList, ISearchParams } from './data';
import {
  cancelDelivery,
  closeOrder,
  exportList,
  getHideInfo,
  getQuality,
  getStatusOptions,
  getTableList,
} from './service';

const { viewPlaintextAuth } = useViewPlaintextAuth();
import useSensitiveLookModel from '@/composables/use-sensitive-look-model';
import ProdutionCascader from '@/pages-stage/recycle/base-data/commodity-list/components/prodution-cascader.vue';
import useExportModalConfirm from '@/pages-stage/recycle/finance/self-order-pay/use-export-modal-confirm';

import BatchModal from './components/batch-modal.vue';
import EvidenceModal from './components/evidence-modal.vue';
import UpdateImeiModal from './components/update-imei-modal.vue';

const route = useRoute();

const [pricingModalVisible, { setTrue: showPricingModal }] = useBoolean();

const [deliveryModalVisible, { setTrue: showDeliveryModal }] = useBoolean();

const [evaluateModalVisible, { setTrue: showEvaluateModal }] = useBoolean();

const [logisticDrawVisible, { setTrue: showLogisticDraw }] = useBoolean();

const [qualityReportVisible, { setTrue: showQualityReport, setFalse: hideQualityReport }] = useBoolean();

const [evidenceModalVisible, { setTrue: showEvidenceModal }] = useBoolean();

const [batchModalVisible, { setTrue: showBatchModal }] = useBoolean();

const [imeiModalVisible, { setTrue: showImeiModal }] = useBoolean();

const currentOrderId = ref('');
const currentReportData = ref<any[]>([]);

const orderStatusOptions = ref([]);

const currentImei = ref<string>('');

// 表单实例
const formRef = ref<FormInstance>();
const formState = reactive<ISearchParams>({
  orderStatus: 'all',
  orderId: route.query.order_id ? (route.query.order_id as string) : '',
  date: [],
  tracking_num: '',
  batch_imei: '',
  batch_no: undefined,
  recycle_mode: undefined,
  category_id: [],
  complete_time: [],
});

const makeRequestParams = (params: any) => {
  let startTime = '',
    endTime = '',
    complete_startTime = '',
    complete_endTime = '',
    requestParams = null;
  if (params.date.length) {
    startTime = params.date[0];
    endTime = params.date[1];
  }
  if (params.complete_time.length) {
    complete_startTime = params.complete_time[0];
    complete_endTime = params.complete_time[1];
  }
  requestParams = {
    orderStatus: params.orderStatus,
    orderId: params.orderId,
    end_at: endTime,
    start_at: startTime,
    tracking_num: params.tracking_num,
    category_id: params.category_id,
    recycle_mode: params.recycle_mode,
    batch_imei: params.batch_imei,
    batch_no: params.batch_no,
    complete_start_at: complete_startTime,
    complete_end_at: complete_endTime,
  };
  return requestParams;
};
const { list: tableList, listLoading, page, getTableList: onLoadList, tableChange } = useTable<IOrderList, any>({
  api: getTableList,
  totalKey: 'data.pageInfo.count',
  searchForm: formState,
  formatSearchValue: (params: any) => makeRequestParams(params),
  formatHandle: async (res: AxiosResponse<{ list: IOrderList[] }>) => {
    const ids = res.data.list.map((item: IOrderList) => item.orderId);
    let result = await POST('/crm/data', { unionIds: ids, unionSuffix: '_recyOrdList' });
    let marks = {};
    result.data.forEach((item: any) => {
      marks[item.union_id] = item;
    });
    res.data.list.forEach((item: IOrderList) => {
      item.remarks = marks[`${item.orderId}_recyOrdList`];
      item.couponInfo = item.purchasedDetail || item.evaluateDetail;
    });
    return res.data.list;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const onExport = (type: string) => {
  //导出订单
  const { down: downOrder } = useExportModalConfirm(
    async () => {
      const res = await exportList({
        ...makeRequestParams(formState),
        export_type: type,
      });
      return res;
    },
    `${route.query.origin}/super/async-export/index`,
    {
      title: `个人回收${type === '1' ? '订单数据' : '检测报告'}导出`,
      content: `请确认是否导出该订单${type === '1' ? '数据' : '检测报告'}？`,
    },
  );
  downOrder();
};

// 表单控制
const onHandleControl = () => ({
  search() {
    onLoadList('search');
  },
  reset() {
    // 1.重置所有字段
    formRef.value?.resetFields();
    // 2. 初始化页码，重新获取table数据
    onLoadList('search');
  },
});

const onPricing = (orderId: string) => {
  currentOrderId.value = orderId;
  showPricingModal();
};

// 查看账单
const onSeebill = (orderId: string) => {
  window.parent.postMessage(
    {
      action: 'blank',
      href: `/super/recycle/order-pay/index?orderId=${orderId}`,
    },
    '*',
  );
};

// 查看评估
const onSeeEvaluate = (orderId: string) => {
  currentOrderId.value = orderId;
  showEvaluateModal();
};

// 查看检测
const onSeeQuality = (orderId: string) => {
  getQuality(orderId).then(({ data }) => {
    currentReportData.value = data;
    showQualityReport();
  });
};

// 顺丰取件
const onDelivery = (orderId: string) => {
  currentOrderId.value = orderId;
  showDeliveryModal();
};

// 取消预约
const onCancelDelivery = (order_id: string) => {
  Modal.confirm({
    title: '确定要取消预约吗？',
    onOk() {
      cancelDelivery({ order_id, order_type: 0 }).then((res: any) => {
        message.success(res.message || '操作成功');
        onLoadList();
      });
    },
  });
};

// 关闭订单
const onCloseOrder = (orderId: string) => {
  Modal.confirm({
    title: '是否关闭此订单?',
    content: createVNode(
      'div',
      {
        style: 'color: rgba(0,0,0,0.65);',
      },
      '关闭订单后将不能恢复，确认关闭订单吗？',
    ),
    onOk() {
      return closeOrder(orderId).then(() => {
        message.success('订单已关闭');
        onLoadList();
      });
    },
  });
};

type TEvideceObj = {
  url: string;
  time: string;
};
const evidenceObj = ref<TEvideceObj>();
//查看证据
const openEvidence = (params: TEvideceObj) => {
  evidenceObj.value = params;
  showEvidenceModal();
};

// 查看物流信息
const logisticInfo = ref();
const onSeeLogisticDetail = (isReturn: boolean, _logisticInfo: any, record: any) => {
  logisticInfo.value = {
    isReturn,
    ..._logisticInfo,
    order_id: record.orderId,
    showViewHideBtn: viewPlaintextAuth.value.recycleShowViewHideBtn,
    type: '个人',
  };
  showLogisticDraw();
};
//当小数点后面为00时，去掉
const makeDecimalPoint = (num: string) => {
  if (num.indexOf('.') !== -1) {
    const numArr = num.split('.');
    if (numArr[1] === '00') {
      return numArr[0];
    } else {
      return num;
    }
  } else {
    return num;
  }
};

// 查询脱敏的数据
const queryHideInfo = (field: string, orderId: string) => {
  getHideInfo({ orderId, field }).then(({ data }) => {
    useSensitiveLookModel(data.value);
  });
};

const handleBatch = () => {
  showBatchModal();
};

const handleBatchChange = (value: any) => {
  formState.batch_imei = value.join(',');
};

//修改Imei
const updateImei = (orderId: string, imei: string) => {
  currentOrderId.value = orderId;
  currentImei.value = imei;
  showImeiModal();
};

// 获取url参数进行默认查询
const getUrlParams = () => {
  const query = route.query || {};
  const keysArr = Object.keys(query).filter(key => !['role', 'origin'].includes(key));
  if (keysArr.length) {
    keysArr.forEach(key => {
      const value = query[key];
      switch (key) {
        case 'startTime':
          formState.complete_time[0] = dayJS(value).format('YYYY-MM-DD');
          break;
        case 'endTime':
          formState.complete_time[1] = dayJS(value).format('YYYY-MM-DD');
          break;
        default:
          if (formState.hasOwnProperty(key)) {
            formState[key] = value;
          }
          break;
      }
    });
  }
};

onMounted(() => {
  getStatusOptions().then(({ data }) => {
    orderStatusOptions.value = data.orderStatus.map(item => {
      item.value = String(item.value);
      return item;
    });
    useCeiling();
  });
  getUrlParams();
  onLoadList();
});
</script>
<style lang="less" scoped>
@import '../../common/base.less';

.good-image {
  display: inline-block;
  width: 52px;
  height: 52px;
  border: 1px solid #e1e1e1;
  border-radius: 4px;

  img {
    width: 100%;
    height: 100%;
    object-fit: scale-down;
  }
}

:deep(.remark-component .block-remark .left-content) {
  padding-right: 0;
}

:deep(.remark-component .block-remark .left-content .block-time-by + .block-time-by) {
  margin-left: 5px;
}

:deep(.ant-btn-link) {
  color: #1677ff;
}

:deep(.ant-btn-dangerous.ant-btn-link) {
  color: var(--ant-error-color);
}

.card-mian {
  width: 100%;
  height: 100%;
  padding: 16px;

  .page-title {
    padding-top: 24px;

    .page-title-text {
      margin: 0;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 20px;
      /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
      font-family: PingFangSC-Medium, PingFang SC;
      line-height: 32px;
    }
  }
}

.list-mian {
  width: 100%;
  margin-top: 16px;
  padding-top: 16px;
  padding-bottom: 40px;
  background: #fff;

  .order-head {
    display: flex;
    align-items: center;
    width: 100%;
    height: 54px;
    padding-left: 20px;
    background: rgba(6, 21, 51, 0.02);

    .order-text {
      margin-right: 32px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 14px;

      .imei-icon {
        cursor: pointer;

        &:hover {
          color: var(--ant-primary-color);
        }
      }
    }
  }

  .goods-info {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
    background: #fff;
  }

  .logistic-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
  }

  .evaluate-price {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;

    .evaluate-price-block {
      display: flex;
      flex-direction: column;
    }
  }

  .order-status {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
    background: #fff;
  }

  .quality-result-text {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 98px;
    padding: 16px;
    background: #fff;
  }

  .operation {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.merchant-info-text {
  margin-right: 8px;

  span[role='img'] {
    margin-left: 5px;
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: var(--ant-primary-color);
    }
  }

  &::after {
    content: '，';
  }
}

:deep(.recycle-sale-table-thead tr th) {
  background: #fafafb !important;
}

.expanded-row {
  border-top: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;

  > td {
    padding: 16px;
  }

  &-td {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.a-link-hover {
  color: #061533d9;
  font-weight: 400;
  font-size: 14px;
  text-decoration: underline;
}

.a-link-hover:hover {
  color: #3777ff;
  font-weight: 400;
  font-size: 14px;
  text-decoration: underline;
}

.evaluate-price-text2 {
  color: #00c8be;
}

.fixed-box {
  position: fixed;
  bottom: 0;
  width: 100%;
}

:deep(.spacing) {
  height: 16px;
}

.search-form {
  margin-bottom: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
}
</style>
