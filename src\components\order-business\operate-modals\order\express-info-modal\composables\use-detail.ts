import { reactive } from 'vue';
import { useRoute } from 'vue-router';

import type { IExpressData, ITransportData } from '../data';
import {
  getJDTransportDetail,
  getSFTransportDetail,
  superGetJDTransportDetail,
  superGetSFTransportDetail,
} from '../services';

type DetailType = {
  change(value: string[]): void;
};

export default function (params: { orderId: string; type: 'sf' | 'jd'; data: IExpressData }): DetailType {
  const detail = reactive<DetailType>({
    change,
  });
  const isSuper = useRoute().query.role === 'super';

  /**
   * @description: 加载详情数据
   * @param {ITransportData} row
   * @return {*}
   */
  async function loadData(row: ITransportData) {
    try {
      row.loading = true;
      const api = {
        sf: isSuper ? superGetSFTransportDetail : getSFTransportDetail,
        jd: isSuper ? superGetJDTransportDetail : getJDTransportDetail,
      }[params.type] as any;
      const { data } = await api({
        order_id: params.orderId,
        id: row.id,
      });
      let logisticsInfo: any = {};
      if (params.type === 'jd') {
        logisticsInfo.waybill_no = data.waybill_no;
        logisticsInfo.receive_info = data.receive_info;
        logisticsInfo.sender_info = data.sender_info;
        logisticsInfo.goods_info = data.goods_info;
        logisticsInfo.send_start_time = data.send_start_time;
        logisticsInfo.pick_up_time = data.pick_up_time;
        logisticsInfo.serviceInfoList = [
          {
            project: data.project,
            express_way: data.express_way,
            name: data.delivery_staff_info.name || '-',
            phone: data.delivery_staff_info.phone || '-',
            sku_name: data.goods_info.name,
            num: data.goods_info.num,
            weight: data.weight,
            order_state_desc: data.order_state_desc,
          },
        ];
        logisticsInfo.billInfoList = [
          {
            freight: data.freight,
            insure_fee: data.insure_fee,
            packed_fee: data.packed_fee,
            sign_paper_fee: data.sign_paper_fee,
            platform_substract: data.platform_substract,
            other_fee: data.other_fee,
            total: data.total,
          },
        ];
        logisticsInfo.paymentInfoList = [
          {
            amount: data.total,
            state_desc: data.state_desc,
            pay_way: data.pay_way,
            pay_time: data.pay_time,
          },
        ];
        logisticsInfo.logistics = data.logistics;
      } else {
        logisticsInfo = data;
      }
      row.data = logisticsInfo;
    } finally {
      row.loading = false;
    }
  }

  const valueKeyList = ['server_send', 'user_return', 'after_sale_return'];

  /**
   * @description: 展开与收起
   * @param {object} param1
   * @return {*}
   */
  async function change(value: string[]) {
    for (let i = 0; i < value.length; i++) {
      const key = value[i];

      const valueKey = valueKeyList.find(i => key.indexOf(i) !== -1);
      if (!valueKey) return;
      const newKey = key.replace(valueKey, '');
      const row = params.data[valueKey][newKey];
      !row.data && (await loadData(row));
    }
  }

  return detail;
}
