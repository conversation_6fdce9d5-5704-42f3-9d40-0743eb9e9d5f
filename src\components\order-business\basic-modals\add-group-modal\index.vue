<template>
  <a-modal
    v-model:visible="bindVisible"
    title="选择分组"
    :width="700"
    @ok="submit"
  >
    <a-spin :spinning="loading">
      <a-checkbox-group
        v-model:value="checkGroupValue"
        style="width: 100%"
        @change="handleChange"
      >
        <a-row
          :gutter="[16, 16]"
          :wrap="true"
        >
          <a-col
            v-for="item in groupList"
            :key="item?.id"
          >
            <a-checkbox :value="item?.id">
              {{ item?.name }}
            </a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';

import { useVModel } from '@/hook';

import { bindOrderGroup, getOrderGroupBindList } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'changeOrderGroup'): void;
}>();
const bindVisible = useVModel(props, 'visible', emit);

const groupList = ref([]);
const checkGroupValue = ref([]);
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  {
    immediate: true,
  },
);

/** 加载数据 */
async function loadData() {
  loading.value = true;
  try {
    const { data } = await getOrderGroupBindList({ order_id: props.orderId });
    groupList.value = data.map(item => ({ id: item.id, name: item.name }));
    checkGroupValue.value = data.filter(item => item.is_bind).map(item => item.id);
  } finally {
    loading.value = false;
  }
}

const handleChange = value => {
  checkGroupValue.value = value;
};

// 提交
function submit() {
  loading.value = true;
  bindOrderGroup({
    order_id: props.orderId,
    ids: checkGroupValue.value,
  })
    .then(() => {
      message.success('保存成功');
      bindVisible.value = false;
      emit('changeOrderGroup');
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
