<template>
  <div
    class="flex-container"
    :style="{ 'grid-template-columns': `repeat(${columnsNumber}, minmax(auto, 1fr))`, gap: `${gap}px` }"
  >
    <slot />
  </div>
  <slot name="body" />
</template>

<script lang="ts" setup>
defineProps({
  gap: {
    type: Number,
    default: 0,
  },
  columnsNumber: {
    type: Number,
    default: 1,
  },
});
</script>

<style lang="less" scoped>
.flex-container {
  display: grid;
  grid-auto-flow: row;
}
</style>
