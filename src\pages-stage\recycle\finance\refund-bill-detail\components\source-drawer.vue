<template>
  <a-drawer
    :body-style="{ paddingTop: '12px' }"
    :closable="false"
    title="选择源单"
    :visible="visible"
    :width="1000"
    @close="onClose"
  >
    <template #extra>
      <CloseOutlined @click="onClose" />
    </template>
    <a-tabs active-key="1">
      <a-tab-pane
        key="1"
        tab="销售退款单"
      />
    </a-tabs>
    <a-form
      ref="searchFormRef"
      :model="searchParams"
    >
      <a-form-item
        label="单据编号"
        name="after_sale_no"
      >
        <a-input
          v-model:value="searchParams.after_sale_no"
          style="width: 200px"
        />
        <a-space style="margin-left: 12px">
          <a-button
            type="primary"
            @click="handleSearch"
          >
            搜索
          </a-button>
          <a-button @click="handleSet">
            重置
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
    <div class="main">
      <a-table
        class="table-list"
        :columns="sourceSelectColumns"
        :data-source="list"
        :loading="listLoading"
        :pagination="false"
        row-key="after_sale_no"
        :row-selection="rowSelection"
        :scroll="{ x: '100%' }"
        :sticky="true"
        @change="tableChange(page)"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'after_sale_no'">
            <span class="link">
              {{ record.after_sale_no }}
            </span>
          </template>
          <template v-if="column.key === 'refund_status_label'">
            <div class="status-block">
              <div
                class="point"
                :style="{ background: record.status === '1' ? '#00C8BE' : '#FAAD14' }"
              />
              {{ record.refund_status_label }}
            </div>
          </template>
        </template>
      </a-table>
      <div class="table-pagination">
        <div class="all-checked">
          <a-checkbox
            v-model:checked="checkAllState.checkAll"
            :indeterminate="checkAllState.indeterminate"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
          <div class="check-num">
            已选 <span class="num">{{ checkAllState.checkedList.length }}</span> 条
          </div>
        </div>
        <a-pagination
          v-model:current="page.current"
          :default-page-size="10"
          :page-size="page.pageSize"
          :page-size-options="[10, 30]"
          show-quick-jumper
          :total="page.total"
          @change="tableChange(page)"
        />
      </div>
    </div>
    <template #footer>
      <div class="footer-block">
        <a-space>
          <a-button @click="onClose">
            取消
          </a-button>
          <a-button
            type="primary"
            @click="handleSelect"
          >
            确认
          </a-button>
        </a-space>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { useVModel } from '@/hook';
import { useTable } from '@/hook/component/use-table';
import useCheckAll from '../composables/use-check-all';
import { sourceSelectColumns } from '../config';
import { getRefundServerOrder } from '../service';

const props = defineProps<{
  visible: boolean;
  serverId: number | string | undefined;
}>();
const emit = defineEmits(['update:visible', 'handleSelect']);

const visible = useVModel(props, 'visible', emit);
const searchParams = reactive({
  server_id: props.serverId,
  after_sale_no: undefined,
});
const searchFormRef = ref();

/*---------------- table hooks  --------------------*/
const { list, listLoading, page, getTableList, tableChange } = useTable({
  api: getRefundServerOrder,
  searchForm: searchParams,
  totalKey: 'meta.pagination.total',
  formatHandle: res => {
    return res.data;
  },
  pagination: {
    pageSize: 10,
  },
});
const { checkAllState, onCheckAllChange, rowSelection } = useCheckAll(list);
const handleSearch = () => {
  getTableList('search');
};
const handleSet = () => {
  searchFormRef.value?.resetFields();
  getTableList('search');
};

const onClose = () => {
  visible.value = false;
};
const handleSelect = () => {
  if (!checkAllState.checkedList.length) {
    return message.error('请至少选择一条数据');
  }
  emit('handleSelect', checkAllState.checkedList);
  message.success('选择成功');
  visible.value = false;
};
onMounted(() => {
  getTableList();
});
watch(
  () => props.serverId,
  (value: number | string | undefined) => {
    searchParams.server_id = value;
    getTableList();
  },
);

watch(
  () => props.visible,
  (value: boolean) => {
    if (!value) {
      checkAllState.checkedList = [];
    }
  },
);
</script>

<style lang="less" scoped>
.footer-block {
  display: flex;
  justify-content: flex-end;
}
:deep(.ant-drawer-body) {
  padding: 0;
}
.table-pagination {
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 24px;
  background: #fff;
}
.all-checked {
  display: flex;
  align-items: center;
}
.check-num {
  margin-left: 28px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 14px;
}
.num {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
}
.status-block {
  display: flex;
  align-items: center;
  .point {
    width: 8px;
    height: 8px;
    margin-right: 10px;
    border-radius: 8px;
  }
}
.link {
  padding: 4px 0;
  color: #3777ff;
}
</style>
