<template>
  <a-table
    class="bottom-fix-table"
    :columns="requierListColumns"
    :data-source="list"
    :loading="listLoading"
    :pagination="page"
    :scroll="{ x: '100%' }"
    :sticky="true"
    style="margin-top: 12px"
    @change="tableChange"
  />
</template>

<script setup lang="ts">
import { useTable } from '@/hook/component/use-table';
import { AxiosResponse } from 'axios';
import { requierListColumns } from '../config';
import { IRequireList } from '../data';

/*---------------- table hooks  --------------------*/
const { list, listLoading, page, getTableList, tableChange } = useTable<IRequireList, void>({
  url: '/demand-regis/list',
  totalKey: 'data.pageInfo.count',
  formatHandle: (res: AxiosResponse<{ list: IRequireList[] }>) => {
    return res.data.list;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});
getTableList();
defineExpose({
  getTableList,
});
</script>
