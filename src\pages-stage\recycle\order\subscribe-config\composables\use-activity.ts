import { ref, computed, Ref } from 'vue';

export default function useActivity(selectManage: Ref<any>) {
  const activityOptions = computed(() => {
    const ids = activitys.value.map((item: any) => item.value);
    const options = JSON.parse(JSON.stringify(selectManage.value.activity_select || []));
    const list = [...options];
    list.forEach(item => {
      item.label = `${item.value} | ${item.label}`;
      if (ids.includes(item.value)) {
        item.disabled = true;
      }
    });
    return list;
  });

  const activityValue = ref<any>([]);

  const activitys = ref<any>([]);

  // 应用活动id
  const useActivityId = () => {
    const ids = [...activityValue.value];
    const activityData = ids.map((id: number) => {
      const data = activityOptions.value.find(item => item.value === id);
      return data;
    });
    const activitysIds = activitys.value.map((item: any) => item.value);
    const filterActivityData = activityData.filter((item: any) => {
      return !activitysIds.includes(item.value);
    });
    activitys.value = [...activitys.value, ...filterActivityData];
    activityValue.value = [];
  };

  // 移除活动id
  const delActivityId = (index: number) => {
    activitys.value.splice(index, 1);
  };

  return {
    activityOptions,
    activityValue,
    activitys,
    useActivityId,
    delActivityId,
  };
}
