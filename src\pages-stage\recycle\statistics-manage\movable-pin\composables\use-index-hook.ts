import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { getIndexData } from '../service';
import { IIndexRes, IIndexDataCountValue } from '../data';

export default function () {
  const loading = ref<boolean>(false);
  const list = ref<IIndexDataCountValue[]>([]);

  const load = async (params?: any, key?: string, callBack?: () => void) => {
    if (!key) loading.value = true;
    const requestParams = { ...params };
    if (key) requestParams.action_key = key;
    try {
      const res = await getIndexData(requestParams);
      const data: IIndexRes = res.data;
      const { data_count } = data;

      if (key) {
        const curIndex = list.value.findIndex((item: IIndexDataCountValue) => item.key === key);
        if (curIndex >= 0) {
          const curInfo = data_count[key];
          curInfo.key = key;
          list.value[curIndex] = curInfo;
          message.success(`【${curInfo.name}】交易指标数据已更新`);
        }
      } else {
        const keyArr = Object.keys(data_count);
        const dataCountList = keyArr.map((key: string) => {
          return { ...data_count[key], key };
        });
        list.value = dataCountList;
      }
    } finally {
      loading.value = false;
      callBack && callBack();
    }
  };

  load();

  return {
    loading,
    list,
    load,
  };
}
