import { useTable } from '@/hook/component/use-table';
import { AxiosResponse } from 'axios';
import { reactive } from 'vue';
import { IOperationList, IsearchParams } from '../data';
import { deleteReduction, updataStatus } from '../service';
import { Modal, message } from 'ant-design-vue';

export default () => {
  const searchParams = reactive<Partial<IsearchParams>>({});
  /*---------------- table hooks  --------------------*/
  const { list, listLoading, page, getTableList, tableChange } = useTable<IOperationList, Partial<IsearchParams>>({
    url: '/super/recycle/purchased-goods-price-decline/list',
    totalKey: 'data.pageInfo.count',
    searchForm: searchParams,
    formatHandle: (res: AxiosResponse<{ list: IOperationList[] }>) => {
      return res.data.list;
    },
    pagination: {
      showTotal: (): string => {
        const { total, pageSize } = page;
        const totalPages = Math.ceil((total as number) / (pageSize as number));
        return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
      },
    },
  });

  const statusChange = async (status: string, record: IOperationList) => {
    try {
      await updataStatus({ id: record.id, status: status });
      message.success(`${status === '1' ? '启用' : '禁用'}成功`);
    } catch (error) {
      record.status = status === '1' ? '2' : '1';
    }
  };

  const deleteHandler = (id: string) => {
    Modal.confirm({
      title: '确认删除该降价趋势吗？',
      content: '是否删除所选分类，降价趋势删除后将不能恢复',
      onOk: async () => {
        await deleteReduction(id);
        getTableList();
        message.success('操作成功');
      },
    });
  };
  return {
    list,
    listLoading,
    page,
    getTableList,
    tableChange,
    statusChange,
    searchParams,
    deleteHandler,
  };
};
