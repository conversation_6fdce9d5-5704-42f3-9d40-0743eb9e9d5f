<template>
  <a-modal
    v-model:visible="bindVisible"
    class="pay-detail-modal"
    title="实付款详细信息"
    :width="900"
  >
    <!--  查看流水结算情况（仅运营可见）  -->
    <div
      v-if="isSuper && settlementUrl"
      class="btn-box"
    >
      <a-button
        type="link"
        @click="linkSettlement"
      >
        查看流水结算情况
      </a-button>
    </div>
    <a-space
      v-if="isSuper"
      direction="vertical"
      style="margin-bottom: 20px"
    >
      <span>预授权支付宝ID：{{ extraData.preAuthAliUserId || '--' }}</span>
      <span>免密签约支付宝ID：{{ extraData.withholdAliUserId || '--' }}</span>
    </a-space>
    <a-table
      class="list"
      :columns="columns"
      :data-source="dataList"
      :loading="loading"
      :pagination="false"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="column.key === 'already_settlement'">
          <div
            v-for="item in text"
            :key="item"
          >
            {{ item }}
          </div>
        </template>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.key === 'receipt_amount'">
          <div>
            {{ column.title }}
            <a-tooltip title="支付金额与订单金额不一致时，表示当前流水受支付宝优惠影响，支付金额即【实付金额】；">
              <QuestionCircleOutlined />
            </a-tooltip>
          </div>
        </template>
        <template v-if="column.key === 'pay_money'">
          <div>
            {{ column.title }}
            <a-tooltip title="订单金额即流水优惠前的金额">
              <QuestionCircleOutlined />
            </a-tooltip>
          </div>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import type { TableColumnType } from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

import { fetchGateway } from '@/components/order-business/basic-modals/services';
import { useVModel } from '@/hook';

interface IPayItem {
  index: number;
  pay_money: string;
  pay_from: string;
  body: string;
  ext_fund_no: string;
  payer: string;
  already_settlement: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const settlementUrl = ref('');
const dataList = ref<IPayItem[]>([]);
const extraData = ref({});

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const route = useRoute();
const isSuper = route.query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  fetchGateway({ order_id: props.orderId, method: 'v3.log.pay' })
    .then(res => {
      const { result = [], viewSettlementHref = '', ...rest } = res.data || {};
      dataList.value = result;
      settlementUrl.value = viewSettlementHref;
      extraData.value = rest;
    })
    .finally(() => {
      loading.value = false;
    });
}

const columns: TableColumnType[] = [
  {
    title: '编号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
  },
  {
    title: '支付金额',
    dataIndex: 'receipt_amount',
    key: 'receipt_amount',
  },
  {
    title: '订单金额',
    dataIndex: 'pay_money',
    key: 'pay_money',
  },
  {
    title: '付款方式',
    dataIndex: 'pay_from',
    key: 'pay_from',
  },
  {
    title: '付款时间',
    dataIndex: 'pay_time',
    key: 'pay_time',
  },
  {
    title: '付款内容',
    dataIndex: 'body',
    key: 'body',
  },
  {
    title: '流水号',
    dataIndex: 'ext_fund_no',
    key: 'ext_fund_no',
  },
  {
    title: '付款人',
    dataIndex: 'payer',
    key: 'payer',
  },
  {
    title: '已结算',
    dataIndex: 'already_settlement',
    key: 'already_settlement',
    width: 100,
  },
];

const domain = route.query.origin || window.location.origin;

function linkSettlement() {
  window.open(domain + settlementUrl.value);
}
</script>

<style scoped lang="less">
.btn-box {
  text-align: right;
}

.list {
  :deep(.ant-table-cell) {
    word-break: break-all;
  }
}
</style>
