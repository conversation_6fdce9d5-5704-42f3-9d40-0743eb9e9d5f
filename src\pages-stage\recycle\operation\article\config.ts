import { ColumnType } from 'ant-design-vue/lib/table';

import { FormGroupItem } from '@/components/form-create/src/typing';
import { getUnixFormatTime } from '@/utils/base';

import { findLabel } from './composiable/use-handler';

export const columns: ColumnType[] = [
  {
    title: '文章ID',
    key: 'id',
    dataIndex: 'id',
    width: 74,
  },
  {
    title: '文章标题',
    key: 'title',
    dataIndex: 'title',
    width: 479,
  },
  {
    title: '流程类目',
    key: 'process_question_type',
    dataIndex: 'process_question_type',
    width: 88,
    customRender: ({ value }: { value: string }) => {
      return findLabel(articleProcessOptions, value);
    },
  },
  {
    title: '更多类目',
    key: 'question_type',
    dataIndex: 'question_type',
    width: 88,
    customRender: ({ value }: { value: string }) => {
      return findLabel(articleNormalOptions, value);
    },
  },
  {
    title: '创建时间',
    key: 'created_at',
    dataIndex: 'created_at',
    width: 173,
    customRender: ({ value }: { value: number }) => {
      return getUnixFormatTime(value);
    },
  },
  {
    title: '使用状态',
    key: 'status',
    dataIndex: 'status',
    width: 98,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 124,
    fixed: 'right',
  },
];

// 常规类目
export const articleNormalOptions = [
  {
    label: '常见问题',
    value: '1',
  },
  {
    label: '邮寄问题',
    value: '2',
  },
  {
    label: '收款问题',
    value: '3',
  },
  {
    label: '退货问题',
    value: '4',
  },
  {
    label: '暂不选择',
    value: '0',
  },
];
// 流程类目
export const articleProcessOptions = [
  {
    label: '顺丰取件',
    value: '1',
  },
  {
    label: '专业检测',
    value: '2',
  },
  {
    label: '确认价格',
    value: '3',
  },
  {
    label: '极速打款',
    value: '4',
  },
  {
    label: '隐私清除',
    value: '5',
  },
  {
    label: '暂不选择',
    value: '0',
  },
];
export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'title',
    originProps: { label: '文章标题', name: 'title' },
    elProps: { allowClear: true, placeholder: '请输入', style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'question_type',
    originProps: { label: '类目', name: 'question_type' },
    elProps: { allowClear: true, placeholder: '请输入', style: { width: '100%' } },
    fragmentKey: 'renderSelect',
    options: articleNormalOptions,
  },
];
