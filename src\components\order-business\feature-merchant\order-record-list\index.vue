<template>
  <div class="links">
    <a-divider style="margin: 6px 0" />
    <!-- 成功下单 -->
    <div
      class="row keynote"
      @click="linkToList({ user_id: orderData.base_info?.user_id, num_type: 2 })"
    >
      成功下单{{ orderData.async.statistics_info?.success_order ?? '?' }}次
    </div>
    <!-- 累计下单 -->
    <div
      class="row"
      @click="linkToList({ user_id: orderData.base_info?.user_id, num_type: 1 })"
    >
      累计下单{{ orderData.async.statistics_info?.total_order ?? '?' }}次
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';

import type { IOrderItem } from '@/components/order-business/typing';

defineProps<{
  orderData: IOrderItem;
}>();

const route = useRoute();
const domain = route.query.origin || window.location.origin;

/** 链接到新的列表 */
function linkToList(params: Record<string, any>) {
  // 用a标签跳转才不会把原页面的sessionStorage携带过去
  const a = document.createElement('a');
  const requestLink = Object.keys(params)
    .map(key => `${key}=${params[key]}`)
    .join('&');
  a.target = '_blank';
  a.href = `${domain}/order/v4-order-index?${requestLink}`;
  a.click();
}
</script>

<style scoped lang="less">
.links {
  color: rgba(6, 21, 51, 0.85);
  font-size: 14px;

  .row {
    text-decoration: underline;
    cursor: pointer;
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

    &:hover {
      color: rgba(6, 21, 51, 0.65);
    }
  }

  .keynote {
    color: #e00a15;
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

    &:hover {
      color: #f9767d;
    }
  }
}
</style>
