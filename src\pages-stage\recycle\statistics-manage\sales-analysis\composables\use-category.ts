import { onMounted, ref } from 'vue';
import { getSpuDataList } from '../service';
export const useCategory = () => {
  const breedList = ref([]);

  const spuDataListloadData = (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    const { hierarchy, value } = targetOption;
    const MAP = ['category_id', 'brand_id', 'model_id'];
    const params = {
      [MAP[hierarchy - 1]]: value,
    };
    if (selectedOptions?.[0] && hierarchy === 2) {
      params.category_id = selectedOptions[0].value;
    }
    getSpuDataList(params)
      .then(({ data }) => {
        const key = ['brand', 'model'][hierarchy - 1];
        targetOption.children = handleSpuDataList(data[key], hierarchy + 1);
        breedList.value = [...breedList.value];
      })
      .finally(() => {
        targetOption.loading = false;
      });
  };

  onMounted(async () => {
    getSpuDataList({}).then(({ data }) => (breedList.value = handleSpuDataList(data.category, 1)));
  });

  return {
    spuDataListloadData,
    breedList,
  };
};

const handleSpuDataList = (data: any[], hierarchy: number) => {
  return data.reduce((acc, curr) => {
    const { id, name } = curr;
    acc.push({
      label: name,
      value: id,
      isLeaf: hierarchy === 3,
      hierarchy,
    });
    return acc;
  }, []);
};
