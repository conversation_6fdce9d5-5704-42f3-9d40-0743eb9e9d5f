<template>
  <a-modal
    v-model:visible="bindVisible"
    class="delivery-store-modal"
    title="下发仓库"
    width="660px"
    @cancel="onCancel"
  >
    <template v-if="!isSuccess">
      <!-- 为特殊类目时，不显示该模块 -->
      <div
        v-if="!isSpecialCategory"
        class="delivery-card"
      >
        <div class="delivery-card-header">
          <span style="padding-right: 4px">应发货品:</span>
          <span class="delivery-message">{{ deliveryStoreData.sku_info }}</span>
          <!-- 未识别到货品信息时,显示下面的文案 -->
          <span
            v-if="!deliveryStoreData.sku_info?.length"
            class="delivery-message"
            style="color: rgba(6, 21, 51, 0.85)"
          >系统暂未识别到货品信息</span>
        </div>
        <!-- 未识别到货品信息时,隐藏 -->
        <div class="delivery-hint">
          <div style="display: flex; align-items: center; padding-right: 16px">
            <img
              alt=""
              class="delivery-hint-icon"
              src="https://img1.rrzuji.cn/uploads/scheme/2305/27/m/twhg8oEuYUFwkPokvzRm.png"
            >
            <span>若与订单信息不一致，可申请换货说明原因</span>
          </div>
          <div style="display: flex; align-items: center">
            <img
              alt=""
              class="delivery-hint-icon"
              src="https://img1.rrzuji.cn/uploads/scheme/2305/27/m/HNkug7nKudQeVmH9iXKK.png"
            >
            <span>若无当前库存，可与用户协商换货</span>
          </div>
        </div>
      </div>
      <!-- 选择发货仓库 -->
      <a-form
        ref="formRef"
        :model="formState"
      >
        <a-form-item
          label="选择发货仓库"
          name="storeType"
          :rules="[{ required: true, message: '请选择下发仓库!' }]"
        >
          <a-radio-group v-model:value="formState.storeType">
            <a-radio-button
              :disabled="disabledSelfStore"
              :value="1"
            >
              自有仓库
            </a-radio-button>
            <a-radio-button :value="2">
              平台仓库
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <!-- 自有仓库 库存情况 固定展示该内容  -->
      <div
        v-if="formState.storeType === 1"
        class="inventory"
      >
        <div class="inventory-label">
          库存情况:
        </div>
        <div class="inventory-tag">
          暂无数据
        </div>
        <div>
          <img
            alt=""
            class="delivery-hint-icon"
            src="https://img1.rrzuji.cn/uploads/scheme/2305/27/m/L0WtlGhBA989VertIB0M.png"
          >
          <span style="color: rgba(6, 21, 51, 0.45); font-size: 12px">如有需要，可向商家客服申请订阅仓配中心，管理进销存，同步库存信息</span>
        </div>
      </div>

      <!-- 为平台仓库时显示的内容 -->
      <template v-if="formState.storeType === 2">
        <!-- 不为特殊类目 并且 无维护skuid  时显示该模块 -->
        <template v-if="!isSpecialCategory && !deliveryStoreData.sku_id">
          <div style="padding-left: 10px">
            <div class="select-label">
              选择更换货品为:
            </div>
            <div style="display: flex; flex-wrap: wrap; padding-top: 8px">
              <!-- 型号 -->
              <a-select
                v-model:value="classifyOptions.modal"
                :options="classifyOptions.modalOptions"
                placeholder="请选择型号"
                style="width: 190px"
                @change="onChangeModal"
              />
              <!-- sku -->
              <template
                v-for="(item, index) in classifyOptions.attrList"
                :key="item.id"
              >
                <a-select
                  v-model:value="item.value"
                  :options="item.options"
                  :placeholder="`请选择${item.name}`"
                  :style="{
                    width: '190px',
                    marginTop: `${index > 1 ? '8px' : 0}`,
                    marginLeft: `${index === 2 || index === 5 ? 0 : '8px'}`,
                  }"
                />
              </template>
            </div>
          </div>

          <a-button
            :loading="searchStockStatus"
            style="display: block; margin: 8px 0 0 8px"
            type="primary"
            @click="onSearchStock"
          >
            确认并查询库存
          </a-button>
        </template>
        <!-- 游戏机、苹果笔记本固定展示 -->
        <div
          v-if="isGameCategory"
          class="inventory"
          style="padding-bottom: 22px; padding-left: 38px"
        >
          <div class="inventory-label">
            发货仓库：&nbsp;
          </div>
          <a-select
            v-if="isGameCategory"
            disabled
            style="width: 210px"
            :value="9"
          >
            <a-select-option :value="9">
              深圳仓（游戏品类）
            </a-select-option>
          </a-select>
        </div>
        <!-- 设备情况 -->
        <div
          v-if="deliveryStoreData.device_auth"
          class="inventory"
          style="margin-bottom: 10px"
          :style="inventoryPadding"
        >
          <div class="inventory-label">
            设备情况:
          </div>
          <a-radio-group
            v-model:value="isNew"
            class="radio-group"
            :options="[
              { label: '全新', value: false },
              { label: '全新（仅激活）', value: true },
            ]"
          />
        </div>
        <!-- 送检情况 -->
        <div
          v-if="deliveryStoreData.is_checked && !isGameCategory"
          class="inventory"
          style="margin-bottom: 10px"
          :style="inventoryPadding"
        >
          <div class="inventory-label">
            是否送检:
          </div>
          <a-checkbox-group
            v-model:value="is_checked"
            class="checkbox-group"
            :disabled="!deliveryStoreData.replace_auth"
            :options="[{ label: '送检', value: 1 }]"
            @change="onCheckedChange"
          />
        </div>
        <a-alert
          v-if="hasInspectTips"
          style="margin-bottom: 10px"
          type="info"
        >
          <template #message>
            <span>当前平台暂无可销售的商家库存，如已有设备送往平台质检，请催促仓库及时质检，也可以</span>
            <span style="color: #3777ff;text-decoration: underline;;">发起送检单</span>
            <span>，登记设备送往平台质检。</span>
          </template>
        </a-alert>
        <!-- 库存情况 -->
        <div
          class="inventory"
          :style="inventoryPadding"
        >
          <div class="inventory-label">
            库存情况:
          </div>
          <!-- 游戏机固定展示 -->
          <div
            v-if="isGameCategory"
            class="green-status"
          >
            <sync-outlined :spin="searchStockStatus" />
            充足
          </div>
          <!-- 非游戏机、苹果笔记本 -->
          <template v-else>
            <a-radio-group
              v-model:value="regulation"
              class="radio-group"
              :options="[
                { label: '租赁服务设备', value: true, disabled: regulatoryDeviceStatusDisabled },
                { label: '非租赁服务设备', value: false, disabled: nonRegulatoryDeviceStatusDisabled },
              ]"
              @change="onChangeInventory"
            />

            <div class="status-wrap">
              <div
                class="status-tag"
                style="margin-top: 6px"
                :style="regulatoryDeviceStatusTag.style"
              >
                <sync-outlined :spin="searchStockStatus" />
                {{ regulatoryDeviceStatusTag.txt }}
              </div>
              <div
                class="status-tag"
                style="margin-top: 32px"
                :style="nonRegulatoryDeviceStatusTag.style"
              >
                <sync-outlined :spin="searchStockStatus" />
                {{ nonRegulatoryDeviceStatusTag.txt }}
              </div>
            </div>
          </template>
        </div>
      </template>
    </template>
    <template v-else>
      <div class="success-content">
        <img
          alt=""
          src="https://img1.rrzuji.cn/uploads/scheme/2306/01/m/o3L5iuOgOHkdvOL6A1Zt.png"
        >
        <div class="success-content-title">
          下发成功
        </div>
        <div>当前订单已通知仓库发货</div>
      </div>
    </template>

    <template #footer>
      <a-space v-if="!isSuccess">
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button
          v-if="deliveryStoreData.sku_id"
          @click="handleExchangeApply"
        >
          申请换货
        </a-button>
        <a-button
          v-if="formState.storeType === 1"
          :disabled="isSelfSpecialDis"
          type="primary"
          @click="handleGoShipSelf"
        >
          去发货
        </a-button>
        <template v-else>
          <!-- 该订单存在【缺货记录表】中 && 库存状态为“缺货” -->
          <a-button
            v-if="hasInAutoWarehouse && stockStatus === 1"
            danger
            type="text"
          >
            到货自动下发仓库
          </a-button>
          <!-- 商家端二级弹窗确认 -->
          <a-popover
            v-else
            v-model:visible="popoverOpen"
            placement="topLeft"
            title="下发仓库确认"
            trigger="click"
          >
            <template #content>
              <a-space direction="vertical">
                <div>商品信息：{{ extraData.spu_name + ' ' + extraData.sku_name }}</div>
                <div>货品信息：{{ deliveryStoreData.sku_info }}</div>
                <!-- 【舆情】去发货弹窗送检逻辑调整 -->
                <div
                  v-if="hasInspectTips && false"
                  class="flex-block"
                >
                  <div>请选择下发检测仓：</div>
                  <a-select
                    v-model:value="designated_warehouse_id"
                    :options="warehouseOptions"
                    placeholder="请选择"
                    style="width: 200px"
                  />
                </div>
                <a-space class="popover-button-area">
                  <a-button @click="popoverVisible = false">
                    取消
                  </a-button>
                  <a-button
                    :loading="submitLoading"
                    type="primary"
                    @click="onIssueStock"
                  >
                    确定下发
                  </a-button>
                </a-space>
              </a-space>
            </template>
            <!-- <a-button
              :disabled="hasInspectTips ? inspectDisabled : issueDisabled"
              type="primary"
            >
              下发仓库
            </a-button> -->
            <!-- 【舆情】去发货弹窗送检逻辑调整 -->
            <a-button
              :disabled="hasInspectTips || issueDisabled"
              type="primary"
            >
              下发仓库
            </a-button>
          </a-popover>
        </template>
      </a-space>
      <a-button
        v-else
        type="primary"
        @click="onCancel"
      >
        知道了
      </a-button>
    </template>
  </a-modal>

  <OldShipModal
    v-model:visible="oldShipModalVisible"
    :delivery-store-data="deliveryStoreData"
    :extra-data="extraData"
    :order-id="orderId"
    @refresh="refreshData"
    @show-exchange-apply-modal="handleExchangeApply"
  />
</template>

<script lang="ts" setup>
import { PropType, toRef } from 'vue';
import { computed, ref, watch, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { SyncOutlined } from '@ant-design/icons-vue';

import useSkuOptions from '@/components/order-business/operate-modals/order/go-ship-modal/composables/use-sku-options';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';
import isSufficientWarehouse from '@/utils/is-sufficient-warehouse';

import reporter from '../../../../composables/reporter';
import type { IDeliverStore, IExtraData, IWarehouseInformation } from '../data';
import {
  autoToWarehouse,
  cancelSfPickUp,
  getOperateMark,
  getOrderLimitIssued,
  getStoreSendWarehouseInfo,
  getWarehouseList,
  issueStock,
  searchSkuId,
  subwarehouseService,
} from '../services';
import OldShipModal from './components/old-ship-modal.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
  (e: 'showExchangeApplyModal'): void;
}>();
const bindVisible = useVModel(props, 'visible', emit);

const route = useRoute();
const isSuper = route.query.role === 'super';
const onCancel = () => (bindVisible.value = false);

const deliveryStoreData = ref<IDeliverStore>({});

const disabledSelfStore = ref(false);
const isForbid = ref(false);

/** 获取商品信息 */
function deliverGoods() {
  getStoreSendWarehouseInfo(props.orderId).then(({ data }) => {
    const {
      pdm_category_id,
      is_new,
      is_enterprise_sales,
      scm_auth,
      is_mobile_phone,
    } = data;
    deliveryStoreData.value = {
      order_id: props.orderId,
      special_category: props.extraData.special_category,
      ...data,
    };
    // 企销订单二手手机又有scm权限
    const isSpecial = is_enterprise_sales && !is_new && is_mobile_phone && scm_auth;
    isForbid.value = isSpecial ? false : (!is_new && Number(pdm_category_id) === 34);
    if (isForbid.value) {
      disabledSelfStore.value = true;
    }
    if (formState.value.storeType === 1 && isSelfSpecialDis.value) {
      message.warning('当前订单为二手手机，请先填写发货的设备串码后再进行发货。');
    }
    is_checked.value = data.is_checked ? [1] : [];
    reporter.setReportData('is_new', data.is_new ? 1 : 0);
    reporter.setReportData('replace_auth', data.replace_auth ? 1 : 0);
    reporter.setReportData('is_checked', data.is_checked ? 1 : 0);
    reporter.setReportData('is_can_distribute', data.is_can_distribute ? 1 : 0);
    reporter.setReportData('store_type', formState.value.storeType);
    reporter.reportDataAction();
  });
}

//有scm是企销且二手手机类目未绑码自行发货置灰
const isSelfSpecialDis = computed(() => {
  const {
    scm_auth,
    is_enterprise_sales,
    is_mobile_phone,
    is_new,
    system_make_up_order,
  } = deliveryStoreData.value;
  const { imei_code_data } = props.extraData;
  if (isSuper || formState.value.storeType !== 1 || system_make_up_order) return false;
  if (scm_auth && is_enterprise_sales && is_mobile_phone && !is_new && !imei_code_data.length) {
    return true;
  }

  return false;
});

const { classifyOptions, getModelOptions, getSkuOptions } = useSkuOptions(
  toRef(() => deliveryStoreData.value.pdm_category_id),
  toRef(() => deliveryStoreData.value.pdm_brand_id),
);

const formRef = ref<any>(null);

// 监管机和非监管机的分仓相关信息
const warehouseInformation = ref<{
  regulatoryDeviceInformation: IWarehouseInformation;
  nonRegulatoryDeviceInformation: IWarehouseInformation;
}>({
  regulatoryDeviceInformation: null,
  nonRegulatoryDeviceInformation: null,
});

// 特殊品类(目前指游戏机)
const isSpecialCategory = computed(() => deliveryStoreData.value.special_category);

const inventoryPadding = computed(() => {
  if (deliveryStoreData.value.sku_id || isSpecialCategory.value) {
    return { padding: '0 0 0 40px' };
  } else {
    return { padding: '24px 0 0 8px' };
  }
});

const regulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.regulatoryDeviceInformation?.inventoryStatus;
  const isNumber = typeof inventoryStatus === 'boolean';
  if (isNumber) {
    return {
      txt: inventoryStatus ? '库存充足' : '缺货',
      style: inventoryStatus
        ? { color: '#52c41a', backgroundColor: '#f0ffe1' }
        : { color: '#ff4d4f', backgroundColor: '#fff1f0', width: '54px' },
      disabled: !inventoryStatus,
    };
  }
  return {
    txt: '暂无数据',
    style: {
      color: 'rgba(6, 21, 51, 0.65)',
      backgroundColor: '#0615330a',
    },
    disabled: !inventoryStatus,
  };
});

const nonRegulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.nonRegulatoryDeviceInformation?.inventoryStatus;
  const isNumber = typeof inventoryStatus === 'boolean';

  if (isNumber) {
    return {
      txt: inventoryStatus ? '库存充足' : '缺货',
      style: inventoryStatus
        ? {
            color: '#52c41a',
            backgroundColor: '#f0ffe1',
          }
        : {
            color: '#ff4d4f',
            backgroundColor: '#fff1f0',
            width: '54px',
          },
      disabled: !inventoryStatus,
    };
  }

  return {
    txt: '暂无数据',
    style: {
      color: 'rgba(6, 21, 51, 0.65)',
      backgroundColor: '#0615330a',
    },
    disabled: !inventoryStatus,
  };
});

// 是否是游戏品类
const isGameCategory = computed(
  () => deliveryStoreData.value.special_category && warehouseInfo.value.warehouse_id === 9,
);
//送检提示
const hasInspectTips = computed(() => {
  return (
    regulatoryDeviceStatusTag.value.disabled &&
    nonRegulatoryDeviceStatusTag.value.disabled &&
    is_checked.value.includes(1)
  );
});
//租赁服务设备
const regulatoryDeviceStatusDisabled = computed(() => {
  const regulatoryDeviceStatusTagText = regulatoryDeviceStatusTag.value.txt === '暂无数据';
  return is_checked.value.includes(1) ? regulatoryDeviceStatusTagText : regulatoryDeviceStatusTag.value.disabled;
});
//非租赁服务设备
const nonRegulatoryDeviceStatusDisabled = computed(() => {
  const nonRegulatoryDeviceStatusTagText = nonRegulatoryDeviceStatusTag.value.txt === '暂无数据';
  return is_checked.value.includes(1)
    ? nonRegulatoryDeviceStatusTagText
    : nonRegulatoryDeviceStatusTag.value.disabled || isNew.value;
});
 /**【舆情】去发货弹窗送检逻辑调整 */
//送检下发按钮置灰状态
// const inspectDisabled = computed(() => {
//   return regulatoryDeviceStatusTag.value.txt === '暂无数据' || nonRegulatoryDeviceStatusTag.value.txt === '暂无数据';
// });
const warehouseOptions = ref<any[]>([]);
const handleGetWarehouseList = async () => {
  const { data } = await getWarehouseList();
  warehouseOptions.value = data.map((item: any) => {
    return {
      label: item.warehouse_name,
      value: item.id,
    };
  });
};
const formState = ref({
  storeType: 1,
  sku_id: null,
});

const isSuccess = ref(false);

// 下发按钮置灰状态
const issueDisabled = ref(true);
// 下发按钮二级弹窗显示状态
const popoverVisible = ref(false);
const popoverOpen = computed({
  get() {
    const issueDisabledValue = hasInspectTips.value ? false : !issueDisabled.value;
    return popoverVisible.value && issueDisabledValue;
  },
  set(bool: boolean) {
    popoverVisible.value = bool;
  },
});

// 查询库存情况加载状态
const searchStockStatus = ref(false);

// 下发仓库提交状态
const submitLoading = ref(false);

// 库存情况
const stockStatus = ref<0 | 1 | 2>(0);

// 是否在【缺货记录表】中
const hasInAutoWarehouse = ref<boolean>(false);

// 仓库信息
const warehouseInfo = ref({
  warehouse_type: 0, // 仓库类型
  warehouse_id: 0, // 仓库id
});
// 是否是全新（仅激活）设备
const isNew = ref(false);
// 是否送检
const is_checked = ref<number[]>([]);

watchEffect(() => {
  formState.value.storeType = deliveryStoreData.value?.replace_auth ? 2 : 1;
  // 如果有代发权限
  if (deliveryStoreData.value.is_checked) {
    // 1.如果是特殊类目,直接改库存为充足状态
    if (isSpecialCategory.value) {
      stockStatus.value = 2;
      issueDisabled.value = false;
      // 游戏机
      if (deliveryStoreData.value.special_category) {
        warehouseInfo.value.warehouse_id = 9;
      }
      return;
    }

    // 2.无维护skuid
    if (!deliveryStoreData.value.sku_id) {
      getModelOptions();
    } else {
      // 有维护skuid,那么自动查询库存
      selectOptimalWarehouseService(deliveryStoreData.value.sku_id);
    }
    validateWarehouse();
  }

  if (isForbid.value) {
    formState.value.storeType = 2;
  }
});

// 开启后3s后关闭弹窗
watch(
  () => isSuccess.value,
  newVal => {
    if (newVal) {
      const timer = setTimeout(() => {
        if (isSuccess.value) {
          onCancel();
          return;
        }
        clearTimeout(timer);
      }, 3000);
    }
  },
);

// 是否支持监管
const regulation = ref(true);

// 型号发生变化时触发
const onChangeModal = () => getSkuOptions();

// 切换租赁服务时触发
const onChangeInventory = (e: any) => {
  const value = e.target?.value as boolean;
  const { regulatoryDeviceInformation, nonRegulatoryDeviceInformation } = warehouseInformation.value;
  if (value) {
    warehouseInfo.value = {
      warehouse_id: regulatoryDeviceInformation?.warehouse_id as number,
      warehouse_type: regulatoryDeviceInformation?.warehouse_type as number,
    };
  } else {
    warehouseInfo.value = {
      warehouse_id: nonRegulatoryDeviceInformation?.warehouse_id as number,
      warehouse_type: nonRegulatoryDeviceInformation?.warehouse_type as number,
    };
  }
};

const onCheckedChange = () => {
  if (formState.value.sku_id) {
    onSearchStock();
  } else if (deliveryStoreData.value.sku_id) {
    selectOptimalWarehouseService(deliveryStoreData.value.sku_id);
  }
};

const onSearchStock = () => {
  if (!classifyOptions.modal) {
    message.warning('请先选择型号!');
    return;
  }

  const params: any = {
    model_id: classifyOptions.modal,
    brand_id: Number(deliveryStoreData.value.pdm_brand_id),
    attribute: [],
  };

  const attribute_list = classifyOptions.attrList.reduce(
    (acc: any, curr: any) => {
      if (curr.value) {
        acc.attribute_list.push(curr.value);
      }
      return acc;
    },
    { attribute_list: [] },
  );
  params.attribute.push(attribute_list);

  searchStockStatus.value = true;
  // 1. 根据所选的sku信息查找出对应的skuid
  searchSkuId({ ...params })
    .then(({ data }: any) => {
      const { sku_id } = data.sku_list[0];
      formState.value.sku_id = sku_id;
      if (!sku_id) {
        message.warning('未找到当前商品的sku_id');
        // 下发按钮置灰并且将库存状态修改为暂无数据
        searchStockStatus.value = false;
        stockStatus.value = 0;
        issueDisabled.value = true;
        return;
      }
      issueDisabled.value = false;
      // 2.查询分仓服务
      selectOptimalWarehouseService(sku_id);
    })
    .finally(() => (searchStockStatus.value = false));
};

/*
  查询分仓服务,择优选择
  1. 「租赁服务设备」、「非租赁服务设备」(本质就是监管机和非监管机)，哪个有货就有些命中对应的那个
  2. 如果双方都有货，那么优先选择「租赁服务设备」
  3. 没货的需要置灰不允许切换
*/
async function selectOptimalWarehouseService(sku_id: string) {
  searchStockStatus.value = true;

  const params = {
    sku_id,
    item_num: Number(deliveryStoreData.value.item_num),
    order_id: deliveryStoreData.value.order_id,
    up_stock: true,
    is_activate_only: isNew.value,
    is_checked: is_checked.value.includes(1),
    server_id: Number(props.extraData.server_id),
    created_port: isSuper ? 1 : 2,
  };

  try {
    // 监管机
    const { data: regulatoryDeviceInformation } = await subwarehouseService({
      ...params,
      is_lock_machine: true,
    });
    // 非监管机
    const { data: nonRegulatoryDeviceInformation } = await subwarehouseService({
      ...params,
      is_lock_machine: false,
    });

    const {
      warehouse_id: regulatoryWarehouseId,
      warehouse_type: regulatoryWarehouseType,
    } = regulatoryDeviceInformation;
    const {
      warehouse_id: nonRegulatoryWarehouseId,
      warehouse_type: nonRegulatoryWarehouseType,
    } = nonRegulatoryDeviceInformation;

    // 监管机库存状态
    const regulatoryInventoryStatus = isSufficientWarehouse({
      warehouse_type: regulatoryWarehouseType,
      warehouse_id: regulatoryWarehouseId,
      regulation: true,
    });

    // 非监管机库存状态
    const nonRegulatoryInventoryStatus = isSufficientWarehouse({
      warehouse_type: nonRegulatoryWarehouseType,
      warehouse_id: nonRegulatoryWarehouseId,
      regulation: false,
    });

    warehouseInformation.value = {
      regulatoryDeviceInformation: {
        ...regulatoryDeviceInformation,
        inventoryStatus: regulatoryInventoryStatus,
      },
      nonRegulatoryDeviceInformation: {
        ...nonRegulatoryDeviceInformation,
        inventoryStatus: nonRegulatoryInventoryStatus,
      },
    };

    // 如果监管机有货
    if (regulatoryInventoryStatus) {
      stockStatus.value = 2; // 充足状态
      issueDisabled.value = false;
      warehouseInfo.value = {
        warehouse_id: regulatoryWarehouseId,
        warehouse_type: regulatoryWarehouseType,
      };
      regulation.value = true;
      return;
    }

    // 如果监管机无货并且非监管机有货
    if (!regulatoryInventoryStatus && nonRegulatoryInventoryStatus) {
      stockStatus.value = 2; // 充足状态
      issueDisabled.value = false;
      warehouseInfo.value = {
        warehouse_id: nonRegulatoryWarehouseId,
        warehouse_type: nonRegulatoryWarehouseType,
      };
      regulation.value = false;
      return;
    }

    // 如果都没货
    if (!regulatoryInventoryStatus && !nonRegulatoryInventoryStatus) {
      stockStatus.value = 1; // 缺货状态
      issueDisabled.value = true;
      regulation.value = true;
      return;
    }
  } finally {
    searchStockStatus.value = false;
  }
}
const designated_warehouse_id = ref();

const onIssueStock = async () => {
  try {
    const params = {
      orderId: props.orderId,
      type: 'mark',
      markType: 299,
      value: 1,
      is_lock: 2,
      sku_id: deliveryStoreData.value.sku_id || formState.value.sku_id,
      is_support: regulation.value ? 1 : 0,
      // 固定参数，后端编写是商家后台传参
      isServer: 1,
      activate_only: isNew.value ? 1 : 0,
      is_checked: is_checked.value.includes(1) ? 1 : 0,
      ...warehouseInfo.value,
      server_delivery: route.name.includes('merchantShipmentsWorkbench') ? 1 : '',
    };

    /**【舆情】去发货弹窗送检逻辑调整 */
    if (hasInspectTips.value && false) {
      params.designated_warehouse_id = designated_warehouse_id.value;
    }

    submitLoading.value = true;
    const {
      data: { express_result },
    } = await getOrderLimitIssued({ order_id: props.orderId });
    await issueStock(params);
    if (express_result === 1) {
      await cancelSfPickUp({ order_id: props.orderId });
    }
    refreshData();
    isSuccess.value = true;
  } finally {
    submitLoading.value = false;
    popoverVisible.value = false;
  }
};

function refreshData() {
  emit('refresh', props.orderId, ['data', 'all_remark']);
}

// 识别该订单是否在【缺货记录表】
async function validateWarehouse() {
  const { data } = await autoToWarehouse({ order_id: props.orderId });
  hasInAutoWarehouse.value = !!data.is_auto;
}

/** 申请换货 */
function handleExchangeApply() {
  onCancel();
  emit('showExchangeApplyModal');
}

const oldShipModalVisible = ref(false);
/** 商家自发货 */
const handleGoShipSelf = () => {
  onCancel();
  oldShipModalVisible.value = true;
};

watch(
  () => props.visible,
  value => {
    // 埋点
    if (value) {
      deliverGoods();
      if (route.name.includes('merchantShipmentsWorkbench')) {
        getOperateMark({ type: 5 });
      }
      handleGetWarehouseList();
    }
  },
  { immediate: true },
);
watch(
  () => formState.value.storeType,
  value => {
    if (value === 2) {
      // 2.无维护skuid
      if (!deliveryStoreData.value.sku_id) {
        getModelOptions();
      }
    }
    if (value === 1 && isSelfSpecialDis.value) {
      message.warning('当前订单为二手手机，请先填写发货的设备串码后再进行发货。');
    }
  },
);
</script>

<style lang="less" scoped>
.delivery-card {
  position: relative;
  margin-bottom: 24px;
  padding: 24px;
  color: rgba(6, 21, 51, 0.65);
  background-color: #f0f7ff;
  border-radius: 4px;
}

.delivery-card-header {
  font-size: 16px;
}

.delivery-message {
  color: #3777ff;
  font-weight: 500;
}

.delivery-hint {
  display: flex;
  padding-top: 16px;
}

.delivery-hint-icon {
  width: 16px;
  height: 16px;
}

.inventory {
  display: flex;
  align-items: center;
  padding-left: 33px;
  color: rgb(6, 21, 51);

  .green-status {
    margin-left: 12px;
    padding: 2px 6px;
    color: #52c41a;
    font-size: 12px;
    text-align: center;
    background-color: #f0ffe1;
    border-radius: 4px;
  }
}

.inventory-tag {
  margin: 0 8px;
  padding: 2px 6px;
  color: rgba(6, 21, 51, 0.65);
  font-size: 12px;
  background-color: rgba(6, 21, 51, 0.04);
  border-radius: 4px;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px 0;
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 16px;
}

.success-content-title {
  padding: 8px 0;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 20px;
}

.select-label {
  position: relative;
  padding: 0;
  color: rgba(6, 21, 51, 0.85);
}

.select-label::before {
  position: absolute;
  top: 0;
  left: -8px;
  color: #ff4d4f;
  content: '*';
}

.checkbox-group,
.radio-group {
  position: relative;
  display: flex;
  flex-flow: column wrap;
  gap: 24px;
  padding-left: 8px;

  :deep(.ant-radio-wrapper),
  :deep(.ant-checkbox-wrapper) {
    position: relative;
    gap: 8px;

    & > span.ant-radio + *,
    & > span.ant-checkbox + * {
      box-sizing: border-box;
      min-width: 130px;
      padding: 5px 0;
      color: rgba(6, 21, 51, 0.85);
      text-align: center;
      background-color: #f5f7fa;
      border: 1px solid #f5f7fa;
      border-radius: 4px;
    }

    &:not(:has(.ant-radio-disabled)) > span.ant-radio + *:hover,
    &:not(:has(.ant-checkbox-disabled)) > span.ant-checkbox + *:hover {
      border-color: var(--ant-primary-color);
      transition: border-color 0.3s linear;
    }

    &:not(:first-child)::before {
      position: absolute;
      top: -29px;
      left: 8px;
      width: 1px;
      height: 34px;
      background-color: rgba(6, 21, 51, 0.06);
      content: '';
    }

    &.ant-radio-wrapper-checked,
    &.ant-checkbox-wrapper-checked {
      & > span.ant-radio + *,
      & > span.ant-checkbox + * {
        color: var(--ant-primary-color);
        background-color: var(--ant-primary-1);
        border-color: var(--ant-primary-color);
        transition: border-color 0.3s linear;
      }

      &::before {
        background-color: var(--ant-primary-color);
      }
    }

    &.ant-radio-wrapper-disabled > .ant-radio-disabled + span,
    &.ant-checkbox-wrapper-disabled > .ant-checkbox-disabled + span {
      color: rgba(0, 0, 0, 0.25);
      background-color: #f5f7fa;
      border-color: #f5f7fa;
      cursor: not-allowed;
    }
  }
}

.status-wrap {
  margin-left: 8px;

  .status-tag {
    padding: 2px 6px;
    font-size: 12px;
    border-radius: 4px;
  }
}

.popover-button-area {
  justify-content: end;
  width: 100%;
}
</style>
