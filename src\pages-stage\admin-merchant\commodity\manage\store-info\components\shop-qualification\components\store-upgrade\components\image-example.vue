<template>
  <a-space
    :size="4"
    @click="setVisible(true)"
  >
    <EyeOutlined style="color: #1677ff" />
    <a class="text-link">图片示例</a>
  </a-space>

  <a-image
    :preview="{
      visible,
      onVisibleChange: setVisible,
    }"
    :src="src"
    :style="{
      display: 'none',
    }"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { EyeOutlined } from '@ant-design/icons-vue';

defineProps<{
  src: string;
}>();

const visible = ref(false);

function setVisible(value: boolean) {
  visible.value = value;
}
</script>
