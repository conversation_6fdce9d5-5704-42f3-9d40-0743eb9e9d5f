import { GET } from '@/services/api';

// 设备管理-列表
export function getList(params: any) {
  return GET('/super/recycle/device-management/list', params);
}

//设备管理-列表统计
export function getCount(params: any) {
  return GET('/super/recycle/device-management/count', params);
}

// 设备管理-查看隐私信息
export function getHideInfo(params: any) {
  return GET('/super/recycle/device-management/get-hide-info', params);
}

// 是否显示眼睛
export function getEyeInfo() {
  return GET('/super/user-v2/user-view-plaintext-auth');
}

// 获取搜索项相关下拉数据
export function getSelectList() {
  return GET('/super/recycle/data-count/filter-params');
}

//设备管理-导出
export function equipmentExport(params: any) {
  return GET('/super/recycle/device-management/export', params);
}
