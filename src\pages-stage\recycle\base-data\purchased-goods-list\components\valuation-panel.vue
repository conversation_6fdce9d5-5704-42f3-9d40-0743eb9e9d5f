<template>
  <transition name="slide">
    <div
      v-show="visible"
      class="charts-container"
    >
      <form-card title="价格趋势图表">
        <a-select
          class="block"
          :filter-option="filterOption"
          label-in-value
          :max-tag-count="2"
          :max-tag-text-length="14"
          mode="multiple"
          :options="chartsOptions"
          placeholder="请选择"
          show-search
          :value="selectedCharts"
          @deselect="onChartsDeselect"
          @select="onChartsSelect"
        />
        <div class="charts-list">
          <template
            v-for="item in selectedCharts"
            :key="item.key"
          >
            <transition name="fade">
              <a-card
                :body-style="{ padding: '12px' }"
                class="chart-item"
              >
                <div class="title-block">
                  <div class="title">
                    {{ item.label }}
                  </div>
                  <DeleteOutlined @click="handleCloseChart(item.id)" />
                </div>
                <week-picker
                  v-model:value="item.range"
                  class="block"
                  @pick="(timeArr: dayjs.Dayjs[]) => onRangeChange(item, timeArr)"
                />
                <div
                  :id="item.id"
                  style="margin-top: 10px"
                />
              </a-card>
            </transition>
          </template>
        </div>
      </form-card>
    </div>
  </transition>
</template>

<script setup lang="ts">
import dayjs, { Dayjs } from 'dayjs';
import FormCard from '../../../common/components/form-card.vue';
import { ref, watch } from 'vue';
import { queryChartDetail, queryChartDropdownList } from '../service';
import { message } from 'ant-design-vue';
import { useLineChart } from '../../../common/use-line-chart';
import { DeleteOutlined } from '@ant-design/icons-vue';
import WeekPicker from '../../../common/components/week-picker.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: () => false,
  },
  spuId: {
    type: Number,
    default: '',
  },
});

// 价格趋势选项
const selectedCharts = ref<Record<string, any>>([]);
const chartsOptions = ref([]);
queryChartDropdownList().then(({ data }) => {
  chartsOptions.value = data;
});

// 图表选中渲染
const { render: renderLineChart } = useLineChart();
const chartMap = new Map();
const dateFormat = 'YYYY-MM-DD';
const onChartsSelect = (cur: Record<string, any>) => {
  cur.id = `_chart_${cur.key}`;
  queryChartDetail({ id: cur.key }).then(async ({ data }) => {
    cur.range = ref<[Dayjs, Dayjs]>([dayjs(data.base.start_date, dateFormat), dayjs(data.base.end_date, dateFormat)]);
    selectedCharts.value.unshift(cur);
    const source = data?.data.flat() ?? null;
    const chart = await renderLineChart({
      container: cur.id,
      source,
    });
    chartMap.set(cur.id, {
      chart,
      source,
    });
  });
};

// 图表按日期筛选
const onRangeChange = (row: any, timeArr: dayjs.Dayjs[]) => {
  const [start_date, end_date] = timeArr.map(time => time.format('YYYY-MM-DD'));
  queryChartDetail({ id: row.value, start_date, end_date }).then(({ data }) => {
    if (!data.data?.length) {
      message.error('该区间暂无数据');
      return;
    }
    const source = data?.data.flat();
    chartMap.get(row.id) && chartMap.get(row.id).chart.changeData(source);
  });
};
// 取消选中图表
const onChartsDeselect = (cur: Record<string, any>) => {
  const idx = selectedCharts.value.findIndex((rec: { key: any }) => rec.key === cur.key);
  selectedCharts.value.splice(idx, 1);
};
//关闭图表
const handleCloseChart = (id: string) => {
  selectedCharts.value = selectedCharts.value.filter((item: any) => item.id !== id);
};
const filterOption = (input: string, option: any) => {
  return option.name.indexOf(input) >= 0;
};
watch(
  () => props.visible,
  value => {
    if (value) {
      queryChartDropdownList(props.spuId).then(({ data }) => {
        selectedCharts.value = data;
        selectedCharts.value.forEach((item: any) => {
          item.id = `_chart_${item.value}`;
          queryChartDetail({ id: item.value }).then(async ({ data }) => {
            const source = data?.data.flat() ?? null;
            item.range = ref<[Dayjs, Dayjs]>([
              dayjs(data.base.start_date, dateFormat),
              dayjs(data.base.end_date, dateFormat),
            ]);
            const chart = await renderLineChart({
              container: item.id,
              source,
            });
            chartMap.set(item.id, {
              chart,
              source,
            });
          });
        });
      });
    }
  },
);
</script>

<style scoped lang="less">
.block {
  width: 100%;
}
// 图表渐显动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
// 估价面板伸缩动画
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-enter-from,
.slide-leave-to {
  width: 0 !important;
  opacity: 0;
}
.charts-container {
  width: 420px;
  height: 100%;
  overflow-y: scroll;
  border-right: 1px solid #e9e9e9;
  .chart-item {
    margin-top: 24px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

    .title-block {
      display: flex;
      justify-content: space-between;
      .title {
        margin-bottom: 12px;
      }
    }
    div[id^='_chart_'] {
      min-height: 178px;
    }
  }
}
</style>
