import type { FormGroupItem, fragmentKey as IFragmentKey } from 'rrz-web-design';
import { FormItemOriginProps } from 'rrz-web-design/es/components/r-form/src/types';

export type TFormGroupItem = FormGroupItem & {
  originProps?: FormItemOriginProps;
  /** 子表单 */
  children?: TFormGroupItem[];
  /** 子表单展示 */
  childrenDisplay?: (form: any) => boolean;
  /** 展示额外的输入表单（radio、checkbox） */
  extraInputKey?: string;
  extraInputLabel?: string;
  /** 额外的输入表单跟随的选项value */
  extraInputMatch?: string;
};

export const notRequiredKeys = [
  'investment_amount',
  'year_business_turnover',
  'business_information',
  'business_information_money',
  'business_information_address',
];

function getGroupItem(key: string, label: string, fragmentKey: IFragmentKey = 'renderRadio'): TFormGroupItem {
  const returnData = { key, label, fragmentKey } as TFormGroupItem;
  if (fragmentKey === 'renderRadio') returnData.elProps = { optionType: 'default' };
  return returnData;
}

export const formGroup: TFormGroupItem[] = [
  getGroupItem('investment_type', '招商类型'),
  { key: 'work_province', label: '办公地点', fragmentKey: 'renderCascader' },
  {
    ...getGroupItem('office', '办公室'),
    childrenDisplay: form => form['office'] === '11',
    children: [
      getGroupItem('office_position', '办公室位置'),
      getGroupItem('office_env', '办公环境', 'renderCheckbox'),
      getGroupItem('office_area', '办公面积'),
      {
        ...getGroupItem('office_business', '办公室业务', 'renderCheckbox'),
        extraInputKey: 'office_business_other',
        extraInputMatch: '17',
      },
      getGroupItem('office_inventory_num', '设备库存数量'),
      {
        ...getGroupItem('office_inventory_cate', '设备库存种类', 'renderCheckbox'),
        extraInputKey: 'office_inventory_cate_other',
        extraInputMatch: '14',
      },
      getGroupItem('office_stash', '仓库情况'),
    ],
  },
  {
    ...getGroupItem('office_team', '办公室团队'),
    childrenDisplay: form => form['office_team'] === '11',
    children: [
      getGroupItem('office_person_number', '办公室人员数量'),
      {
        ...getGroupItem('office_person_make', '办公室人员构成情况', 'renderCheckbox'),
        extraInputKey: 'office_person_make_other',
        extraInputMatch: '15',
      },
    ],
  },
  {
    ...getGroupItem('offline_stores', '线下门店'),
    childrenDisplay: form => form['offline_stores'] === '11',
    children: [
      getGroupItem('stores_position', '门店位置'),
      getGroupItem('stores_city_position', '门店在城市位置'),
      getGroupItem('stores_signboard', '门店招牌'),
      getGroupItem('stores_env', '门店环境', 'renderCheckbox'),
      getGroupItem('stores_area', '门店面积'),
      getGroupItem('stores_business', '门店业务', 'renderCheckbox'),
      getGroupItem('stores_inventory_num', '门店库存数量'),
      {
        ...getGroupItem('stores_inventory_cate', '门店库存种类', 'renderCheckbox'),
        extraInputKey: 'stores_inventory_cate_other',
        extraInputMatch: '14',
      },
      getGroupItem('stores_stash', '门店仓库情况'),
      getGroupItem('stores_person_number', '门店人员数量'),
      {
        ...getGroupItem('stores_person_make', '门店人员构成情况', 'renderCheckbox'),
        extraInputKey: 'stores_person_make_other',
        extraInputMatch: '15',
      },
      {
        ...getGroupItem('stores_main_customer', '门店主要客群'),
        extraInputKey: 'stores_main_customer_other',
        extraInputMatch: '15',
      },
      getGroupItem('stores_customer_flow', '门店客流量/日'),
      {
        ...getGroupItem('stores_nature', '门店性质'),
        extraInputKey: 'stores_nature_other',
        extraInputMatch: '15',
      },
    ],
  },
  {
    ...getGroupItem('source_funds', '资金来源', 'renderCheckbox'),
    extraInputKey: 'source_funds_other',
    extraInputMatch: '13',
  },
  getGroupItem('bad_debt', '坏账可承受范围'),
  getGroupItem('operation_mode', '运营模式'),
  getGroupItem('industry_experience', '租赁行业经验'),
  {
    ...getGroupItem('year_invest_amount', '年投资金', 'renderInputNumber'),
    elProps: { addonAfter: '万' },
  },
  {
    ...getGroupItem('investment_amount', '预估加投资金', 'renderInputNumber'),
    elProps: { addonAfter: '万' },
  },
  {
    ...getGroupItem('year_business_turnover', '年营业额', 'renderInputNumber'),
    elProps: { addonAfter: '万' },
  },
  {
    key: 'cash_conv',
    label: '资金周转周期',
    fragmentKey: 'renderRadio',
    elProps: { optionType: 'default', options: [] },
  },
  {
    key: 'third_party_risk',
    label: '第三方风控能力',
    fragmentKey: 'renderRadio',
    elProps: { optionType: 'default', options: [] },
  },
  {
    key: 'bad_debt_method',
    label: '现有坏账处理方式',
    fragmentKey: 'renderCheckbox',
    extraInputKey: 'bad_debt_method_other',
    extraInputMatch: '14',
  },
  {
    ...getGroupItem('open_store_other', '其他平台开店', 'renderRadio'),
    extraInputKey: 'open_store_other_info',
    extraInputMatch: '10',
  },
  {
    ...getGroupItem('drainage_method', '引流方式', 'renderCheckbox'),
    extraInputKey: 'drainage_method_other',
    extraInputMatch: '16',
  },
  {
    key: 'lease_bkg',
    label: '是否有租赁背景',
    fragmentKey: 'renderRadio',
    elProps: { optionType: 'default', options: [] },
  },
  {
    key: 'daily_order_limit',
    label: '日承接订单上限',
    fragmentKey: 'renderInputNumber',
  },
  {
    ...getGroupItem('store_project', '是否已有店铺入驻项目', 'renderRadio'),
    childrenDisplay: form => form['store_project'] === '10',
    extraInputKey: 'store_project_info',
    extraInputMatch: '10',
    children: [
      {
        key: 'same_legal',
        label: '是否同一个法人',
        fragmentKey: 'renderRadio',
        elProps: { optionType: 'default', options: [] },
      },
      {
        key: 'same_license',
        label: '是否用一个营业执照',
        fragmentKey: 'renderRadio',
        elProps: { optionType: 'default', options: [] },
      },
      {
        key: 'same_team',
        label: '是否同一个运营团队',
        fragmentKey: 'renderRadio',
        elProps: { optionType: 'default', options: [] },
      },
    ],
  },
  {
    key: 'own_supply_chain',
    label: '自有供应链',
    fragmentKey: 'renderCheckbox',
    elProps: { options: [] },
  },
  getGroupItem('accept_untaxed_equipment', '是否能接受未税设备'),
  getGroupItem('be_on_duty', '是否周一到周日都有人值班'),
  getGroupItem('model_accept_intention', '机型接单倾向', 'renderCheckbox'),
  {
    ...getGroupItem('rest_day_duty', '周六日值班情况'),
    childrenDisplay: form => form['rest_day_duty'],
    children: [getGroupItem('rest_day_duty_other', '周六日值班班次')],
  },
  {
    ...getGroupItem('rest_day_work', '节假日上班情况'),
    childrenDisplay: form => form['rest_day_work'],
    children: [getGroupItem('rest_day_work_other', '节假日上班班次')],
  },
  {
    ...getGroupItem('accept_no_service_equipment', '是否接受不带租赁服务设备'),
    childrenDisplay: form => form['accept_no_service_equipment'] === '11',
    children: [getGroupItem('risk_pass_order_service_equipment', '风控通过订单是否接受不带租赁服务的设备')],
  },

  getGroupItem('customer_service_system', '有无客户服务制度'),
  {
    ...getGroupItem('standard_risk_rule', '有无标准版的风控规则'),
    childrenDisplay: form => form['standard_risk_rule'] === '10',
    children: [getGroupItem('standard_risk_rule_other', '具体的风控规则和描述', 'renderInput')],
  },
  getGroupItem('third_risk_tool', '有无第三方风控工具'),
  getGroupItem('supply_chain_channel', '供应链渠道', 'renderCheckbox'),
  getGroupItem('business_information_money', '注册资金'),
  {
    key: 'business_information_address',
    label: '注册地址',
    fragmentKey: 'renderInput',
  },
];
