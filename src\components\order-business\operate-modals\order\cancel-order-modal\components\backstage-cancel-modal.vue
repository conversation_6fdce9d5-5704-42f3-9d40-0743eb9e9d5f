<template>
  <a-modal
    v-model:visible="bindVisible"
    :body-style="{ paddingBottom: 0 }"
    class="cancel-modal"
    :ok-text="isSuperCancelType ? '确认关闭' : '确认'"
    title="取消订单"
    :width="1000"
    @ok="onSubmit"
  >
    <a-spin
      :spinning="cancel.loading || reflowLoading"
      tip="提交中..."
    >
      <a-form layout="vertical">
        <a-form-item v-if="isSuperCancelType">
          <a-alert
            message="该订单已流入【公域池】作为线索让商家转化，若未转化成功24小时内自动关闭。"
            style="color: #2b374d"
            type="warning"
          />
        </a-form-item>

        <a-form-item>
          <!-- 选择退款流水 -->
          <SelectRefundStatement
            ref="selectRefundStatementRef"
            :order-id="orderId"
            :type="ERefundType.CancelOrder"
            :visible-dependency="bindVisible"
          />
        </a-form-item>

        <a-form-item
          v-if="extraData.deposit_has_pay > extraData.deposit_has_refund"
          label="退款押金"
          name="refund_deposit"
        >
          <a-input-number
            v-model:value="cancel.data.refund_deposit"
            :precision="2"
            style="width: 100%"
          />
        </a-form-item>

        <template v-if="extraData.pay_status === 8 || extraData.pay_status === 11">
          <a-form-item
            label="扣取赔偿金"
            name="credit_money"
          >
            <a-input-number
              v-model:value="cancel.data.credit_money"
              :max="extraData.deposit_money"
              placeholder="请输入"
              :precision="2"
              style="width: 100%"
            />
            <div>最多能扣取{{ extraData.deposit_money }}元</div>
          </a-form-item>

          <a-form-item
            label="赔偿金类型"
            name="money_type"
          >
            <a-radio-group v-model:value="cancel.data.money_type">
              <a-radio
                v-for="item in moneyTypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </template>

        <a-form-item
          v-if="isSuperCancelType"
          label="拒单时间（流入公域池时间）"
        >
          {{ cancelReason?.cancel_date }}
        </a-form-item>

        <a-form-item
          v-if="!isSuperCancelType"
          label="取消类型"
          name="reason_type"
          v-bind="cancel.validateInfos['reason_type']"
        >
          <group-radio
            v-model="cancel.data.reason_type"
            :options="cancel.reasonTypeOptions"
            @change="cancel.data.reason = undefined"
          />
        </a-form-item>

        <a-form-item
          label="取消原因"
          name="reason"
          style="margin-bottom: 0"
          v-bind="cancel.validateInfos['reason']"
        >
          <a-select
            v-if="!isSuperCancelType"
            v-model:value="cancel.data.reason"
            :allow-clear="true"
            :disabled="!cancel.data.reason_type"
            placeholder="请选择"
          >
            <a-select-option
              v-for="item in cancel.reasonOptions[cancel.data.reason_type!]"
              :key="item.id"
              :value="item.id"
            >
              {{ item.title }}
            </a-select-option>
          </a-select>
          <span v-else>{{ cancelReason?.cancel_reason }}</span>
          <template #extra>
            <div
              v-if="!isSuper"
              style="color: #ff4d4f"
            >
              为准确客观评估商家等级，请选择如实上报取消类型
            </div>
          </template>
        </a-form-item>

        <a-form-item v-if="isSuper">
          <a-checkbox v-model:checked="cancel.data.is_sms_send">
            取消后默认发送短信
          </a-checkbox>
        </a-form-item>

        <a-form-item
          v-if="isSuperCancelType"
          style="margin: 24px 0 0"
        >
          <span style="color: var(--ant-error-color)">若取消订单会存在商家无法转化的情况，请确保该订单不需再转化的情况下操作关闭。</span>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, h, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Form, message, Modal } from 'ant-design-vue';

import GroupRadio from '@/components/group-radio/group-radio.vue';
import useCancelInsurance from '@/components/order-business/composables/use-cancel-insurance';
import { IReflowParams, useOrderReflow } from '@/components/order-business/composables/use-order-reflow';
import { ERefundType } from '@/components/order-business/feature-application/select-refund-statement/data.d';
import SelectRefundStatement from '@/components/order-business/feature-application/select-refund-statement/index.vue';
import { useVModel } from '@/hook';
import { queryOrderCancelReasonOption } from '@/services/api';

import type { CancelType, ICancelReasonInfo, Option } from '../data';
import {
  fetchOrderCancelReason,
  getRefundMoney,
  hasSfOrder,
  largeOrderRefund,
  orderRefund,
  superGetRefundMoney,
} from '../services';

interface IExtraData {
  rental_has_pay?: number | string;
  rental_has_refund?: number | string;
  deposit_has_pay?: number | string;
  deposit_has_refund?: number | string;
  pay_status?: number;
  deposit_money?: number | string;
  is_while_user?: boolean;
}

const props = withDefaults(
  defineProps<{
    visible: boolean;
    orderId: string;
    extraData: IExtraData;
  }>(),
  {
    visible: false,
    orderId: '',
    extraData: () => ({
      rental_has_pay: '',
      rental_has_refund: '',
      deposit_has_pay: '',
      deposit_has_refund: '',
      pay_status: 0,
      deposit_money: '',
    }),
  },
);

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success', partialRefresh?: boolean): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);
const selectRefundStatementRef = ref();

const route = useRoute();
const isSuper = route.query.role === 'super';
const isSuperCancelType = computed(() => isSuper && cancelReason.value?.type === 2);

const cancel = reactive<CancelType>({
  loading: false,
  data: {
    reason_type: undefined,
    reason: undefined,
    is_sms_send: true,
  },
  rules: {
    reason_type: [{ required: true, message: '请选择取消类型' }],
    reason: [{ required: true, message: '请选择取消原因' }],
  },
  reasonTypeOptions: [],
  reasonOptions: {},
  validateInfos: undefined,
});
const { validate, resetFields, validateInfos } = Form.useForm(cancel.data, cancel.rules);
const cancelReason = ref<ICancelReasonInfo>();

const cancelInsurance = useCancelInsurance();

const { loading: reflowLoading, checkOrderReflow, handleOrderReflow } = useOrderReflow();

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      initData();
    } else {
      resetFields();
    }
  },
  { immediate: true },
);

function initData() {
  cancel.validateInfos = validateInfos;
  // 每次打开弹框重置一下
  const order_id = props.orderId;
  cancel.loading = true;
  fetchOrderCancelReason({ order_id: [props.orderId] }).then(({ data }) => {
    cancelReason.value = data?.list?.[props.orderId];
  });
  const api = isSuper ? superGetRefundMoney : getRefundMoney;
  api({
    order_id: props.orderId,
  }).then(res => {
    cancel.data.refund_deposit = Number(res.data.refund_deposit);
    if (props.extraData.pay_status === 11 || props.extraData.pay_status === 8) {
      cancel.data.credit_money = 0;
      cancel.data.money_type = 3;
    }
    cancel.loading = false;
  });
  queryOrderCancelReasonOption({ order_id }, { isMerchant: !isSuper }).then(({ data }) => {
    cancel.reasonTypeOptions = data;
    Object.values(data)
      .flat()
      .forEach(({ id, child }: any) => {
        cancel.reasonOptions[id] = child;
      });
    cancel.loading = false;
  });
}

async function executeOrderReflow() {
  try {
    // 判断订单是否符合触发回流
    const { reason_type, reason } = cancel.data;
    const params = { order_id: props.orderId, reason_type, reason } as IReflowParams;
    const { data } = await checkOrderReflow(params);
    // 不符合回流，跳出去继续执行订单取消
    if (!data.is_ok) return Promise.resolve();
    // 执行订单回流
    await handleOrderReflow(params);
    bindVisible.value = false;
    message.success('操作成功');
    setTimeout(() => {
      emit('success', false);
    }, 1000);
    return Promise.reject('订单触发回流');
  } catch (error) {
    return Promise.reject(error);
  }
}

async function cancelSubmit() {
  const { cancel_reason, cancel_sub_reason } = cancelReason.value || {};
  try {
    if (!isSuperCancelType.value) {
      await validate();
      await executeOrderReflow();
    } else {
      cancel.data = { ...cancel.data, reason_type: cancel_reason, reason: cancel_sub_reason };
    }
    cancel.loading = true;
    // 获取选择退款流水数据，没有退款流水也可以取消订单
    const params = (await selectRefundStatementRef.value?.requestParams(false)) || { refund_rental: 0 };
    if (props.extraData?.is_while_user) {
      largeOrderRefund({
        order_id: props.orderId,
        ...cancel.data,
        ...params,
      })
        .then(() => {
          bindVisible.value = false;
          message.success('操作成功');
          emit('success');
        })
        .finally(() => {
          cancel.loading = false;
        });
      return;
    }
    orderRefund({
      order_id: props.orderId,
      ...cancel.data,
      ...params,
      is_refuse: isSuperCancelType.value ? 1 : 0,
      is_sms_send: !isSuper || cancel.data.is_sms_send ? 1 : 0, // 商家端统一发送短信，运营端根据选择判断
      server_delivery: route.name.includes('merchantShipmentsWorkbench') ? 1 : '',
    })
      .then(res => {
        bindVisible.value = false;
        message.success('操作成功');
        emit('success');
        if (res.data?.is_supporting_policy_refund) {
          cancelInsurance.confirmCancel({
            orderId: props.orderId,
            successCallback: () => emit('success'),
          });
        }
      })
      .finally(() => {
        cancel.loading = false;
      });
  } catch (error) {
    console.error(error);
  }
}

function onSubmit() {
  if (isSuper) {
    // 当前仅运营端需要做判断
    hasSfOrder(props.orderId).then(res => {
      if (res.data.has_sf_order) {
        riskShow();
      } else {
        cancelSubmit();
      }
    });
  } else {
    cancelSubmit();
  }
}

function riskShow() {
  Modal.confirm({
    title: '风险提示',
    content: h('div', [
      h('div', { style: { color: 'red' } }, '当前订单关联的顺丰运单【未取消】'),
      h('div', '订单关闭后，顺丰小哥依然会上门取件确定取消订单吗？'),
    ]),
    onOk() {
      cancelSubmit();
    },
  });
}

const moneyTypeOptions: Option<number>[] = [
  {
    label: '租金',
    value: 1,
  },
  {
    label: '购买',
    value: 2,
  },
  {
    label: '赔偿',
    value: 3,
  },
];
</script>
