<template>
  <div class="collection-info-mian">
    <div class="title-block">
      <div class="line" />
      <div class="title">
        源单信息
      </div>
      <div
        class="add-btn"
        @click="handleAdd"
      >
        <PlusOutlined style="color: #5f6778" />
        <span class="add-text">选择源单</span>
      </div>
    </div>

    <a-table
      id="sourceTable"
      :columns="sourceColumns"
      :data-source="sourecList"
      :pagination="false"
      :scroll="{ x: '100%' }"
      :sticky="true"
      style="margin-top: 16px"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'created_at'">
          <div v-if="index !== sourecList.length - 1">
            {{ record.created_at }}
          </div>
          <div
            v-else
            class="table-total-title"
          >
            合计
          </div>
        </template>
        <template v-if="column.key === 'total_price'">
          <div v-if="index !== sourecList.length - 1">
            {{ record.total_price }}
          </div>
          <div
            v-else
            class="table-total-number"
          >
            {{ totalAmountObj.total_price || '0.00' }}
          </div>
        </template>
        <template v-if="column.key === 'finish_pay_price'">
          <div v-if="index !== sourecList.length - 1">
            {{ record.finish_pay_price }}
          </div>
          <div
            v-else
            class="table-total-number"
          >
            {{ totalAmountObj.finish_pay_price || '0.00' }}
          </div>
        </template>
        <template v-if="column.key === 'wait_pay_price'">
          <div v-if="index !== sourecList.length - 1">
            {{ record.wait_pay_price }}
          </div>
          <div
            v-else
            class="table-total-number"
          >
            {{ totalAmountObj.wait_pay_price || '0.00' }}
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <div
            v-if="index !== sourecList.length - 1"
            style="color: red"
            @click="handleRemove(index)"
          >
            移除
          </div>
        </template>
        <template v-if="column.key === 'pay_price'">
          <div
            v-if="index !== sourecList.length - 1"
            :class="[!record.pay_price && record.pay_price !== 0 && isError && 'error']"
          >
            <a-input-number
              v-model:value="record.pay_price"
              allow-clear
              :parser="value => value.match(/^[0-9]*\.([0-9]{0,2})|^[0-9]*/)[0]"
              placeholder="请输入"
              :step="0.01"
              style="width: 100%"
              @blur="handleBlur"
            />
          </div>
          <div
            v-else
            class="table-total-number"
          >
            {{ totalAmountObj.pay_price }}
          </div>
        </template>
      </template>
    </a-table>
    <a-divider />
  </div>
  <SourceDrawer
    v-model:visible="sourceVisible"
    :already-ids="alreadyIds"
    :server-id="serverId"
    @handle-select="handleSelect"
  />
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed, onMounted } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useVModel } from '@/hook';
import { sourceColumns } from '../config';
import SourceDrawer from './source-drawer.vue';

const props = defineProps<{
  value: any[];
  serverId: number | string;
  isFirst: boolean;
}>();
const emit = defineEmits(['update:value', 'handleTotal']);
const sourecList = useVModel(props, 'value', emit);
const sourceVisible = ref(false);
const isError = ref(false);
const totalAmountObj = ref({
  total_price: 0,
  finish_pay_price: 0,
  wait_pay_price: 0,
  pay_price: 0,
});
let tabledom: any = null;
const hasTotal = ref(false);
const alreadyIds = computed(() => {
  return sourecList.value.map((item: any) => item.source_order_id) || [];
});
const handleAdd = () => {
  sourceVisible.value = true;
};
const handleRemove = (index: number) => {
  sourecList.value.splice(index, 1);
  handleBlur();
  nextTick(() => {
    handleTableDom();
  });
};
const handleBlur = () => {
  totalAmountObj.value = {
    total_price: 0,
    finish_pay_price: 0,
    wait_pay_price: 0,
    pay_price: 0,
  };
  sourecList.value.forEach((item: any, index: number) => {
    if (sourecList.value.length - 1 !== index) {
      totalAmountObj.value.total_price = Number(item.total_price) + totalAmountObj.value.total_price;
      totalAmountObj.value.finish_pay_price = Number(item.finish_pay_price) + totalAmountObj.value.finish_pay_price;
      totalAmountObj.value.wait_pay_price = Number(item.wait_pay_price) + totalAmountObj.value.wait_pay_price;
      totalAmountObj.value.pay_price = Number(item.pay_price) + totalAmountObj.value.pay_price;
    }
  });
  hasTotal.value = !!sourecList.value.length;
  emit('handleTotal', totalAmountObj.value.pay_price);
};
const handleSelect = (list: any[]) => {
  const ids = sourecList.value.map(item => item.source_order_id);
  list.forEach((item: any) => {
    if (!ids.includes(item.source_order_id)) {
      sourecList.value.splice(sourecList.value.length - 2, 0, { ...item, pay_price: item.wait_pay_price });
      nextTick(() => {
        handleTableDom();
      });
    }
  });
  nextTick(() => {
    handleBlur();
  });
};
const handleError = () => {
  isError.value = true;
};
const handleTableDom = () => {
  tabledom = document.querySelector('#sourceTable')?.querySelectorAll('.ant-table-row');
  let len = tabledom.length;
  tabledom.forEach((item: any, index: number) => {
    if (len - 1 === index) {
      item.childNodes.forEach((td: any) => {
        if (td.style) {
          td.style.background = '#ebfef9';
        }
      });
    } else {
      item.childNodes.forEach((td: any) => {
        if (td.style) {
          td.style.background = '#fff';
        }
      });
    }
  });
};
onMounted(() => {
  handleTableDom();
});
watch(
  () => props.serverId,
  () => {
    if (!props.isFirst) {
      sourecList.value = [{}];
      nextTick(() => {
        handleTableDom();
      });
    }
  },
);
defineExpose({
  handleBlur,
  totalAmountObj,
  handleError,
  handleTableDom,
});
</script>

<style scoped lang="less">
.collection-info-mian {
  padding: 24px 24px 0 24px;
  .title-block {
    display: flex;
    align-items: center;
    .line {
      width: 4px;
      height: 16px;
      background: #00c8be;
      border-radius: 2px;
    }
    .title {
      margin-left: 12px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 16px;
    }
    .add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 94px;
      height: 24px;
      margin-left: 24px;
      background: #fff;
      border: 1px solid rgba(6, 21, 51, 0.15);
      .add-text {
        margin-left: 8px;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }
  }
  .tabel-total {
    display: flex;
    align-items: center;
    height: 54px;
    background: #f9f9fb;
    .table-total-title {
      color: rgba(6, 21, 51, 0.85);
      font-weight: 500;
      font-size: 14px;
    }
    .table-total-number {
      display: flex;
      color: #00c8be;
      font-weight: 500;
      font-size: 14px;
    }
    .table-total-number-item {
      width: 130px;
    }
  }
}
.error {
  :deep(.ant-input-number) {
    border: 1px solid red;
  }
}
.table-total-title {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 14px;
}
.table-total-number {
  display: flex;
  color: #00c8be;
  font-weight: 500;
  font-size: 14px;
}
</style>
