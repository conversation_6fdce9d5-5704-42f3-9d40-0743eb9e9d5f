import { RTableColumnType } from 'rrz-web-design';
import { apiGetSalesMonthDetail } from './service';

export const getTableListConfig = () => {
  const columns: RTableColumnType[] = [
    {
      title: '序号',
      dataIndex: '_index',
      hideInSearch: true,
      width: 60,
    },
    {
      title: '销售部门',
      dataIndex: 'dept_name',
      hideInSearch: true,
      width: 146,
    },
    {
      title: '销售人员',
      dataIndex: 'sales_name',
      hideInSearch: true,
      width: 133,
    },
    {
      title: '状态',
      dataIndex: 'sales_is_valid',
      hideInSearch: true,
      width: 88,
      valueType: 'status',
      valueEnum: [
        { color: '#00C8BE', value: 0, label: '离职' },
        { color: '#ccc', value: 1, label: '在职' },
      ],
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.sales_is_valid - b.sales_is_valid;
      },
    },
    {
      title: '新增业绩总额',
      dataIndex: 'order_money',
      hideInSearch: true,
      width: 133,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.order_money - b.order_money;
      },
    },
    {
      title: '回款总额',
      dataIndex: 'charge_amount',
      hideInSearch: true,
      width: 133,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.charge_amount - b.charge_amount;
      },
    },
    {
      title: '次月退组订单数',
      dataIndex: 'terminate_one_month',
      hideInSearch: true,
      width: 133,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.terminate_one_month - b.terminate_one_month;
      },
    },
    {
      title: '3个月退租订单数',
      dataIndex: 'terminate_three_month',
      hideInSearch: true,
      width: 140,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.terminate_three_month - b.terminate_three_month;
      },
    },
    {
      title: '逾期订单数',
      dataIndex: 'overdue_order_count',
      hideInSearch: true,
      width: 133,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.overdue_order_count - b.overdue_order_count;
      },
    },
    {
      title: '逾期金额',
      dataIndex: 'overdue_order_amount',
      hideInSearch: true,
      width: 133,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.overdue_order_amount - b.overdue_order_amount;
      },
    },
    {
      title: '业绩达成占比',
      dataIndex: 'target_rate',
      hideInSearch: true,
      width: 133,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return a.target_rate.replace('%', '') - b.target_rate.replace('%', '');
      },
    },
  ];

  const tableListConfig: Record<string, any> = {
    api: apiGetSalesMonthDetail,
    columns,
    search: false,
    inModal: true,
    tableProps: {
      pagination: false,
      scroll: { y: 330 },
    },
    autoFetch: false,
  };
  return {
    tableListConfig,
    columns,
  };
};
