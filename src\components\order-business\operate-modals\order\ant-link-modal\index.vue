<template>
  <a-modal
    v-model:visible="bindVisible"
    class="ant-link-modal"
    :ok-button-props="{
      loading: submitLoading,
      style: {
        display: isCompleteSign ? 'none' : 'inline-block',
      },
    }"
    ok-text="同意签署"
    :title="!isCompleteSign ? '合同签署确认' : '合同签署查看'"
    :width="640"
    @ok="submit"
  >
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <div class="preview-pdf-container">
        <div
          v-for="(item, index) in docList"
          :key="index"
          class="pdf-viewer"
        >
          <div class="file-extra" />
          <div class="pdf-container">
            <a-tag
              :bordered="false"
              color="success"
              style="align-self: flex-start"
            >
              {{ contractStatus === 2 && isCompleteSign ? '落签处理中' : statusText[contractStatus] }}
            </a-tag>
            <div class="pdf-name">
              <span>{{ contractName || '-' }}</span>
              <a @click="handleView(item.downloadUrl)">查看</a>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>

  <!--  预览合同PDF  -->
  <PreviewPdf
    v-model:visible="previewPdfVisible"
    :link="previewLink"
  />
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

import PreviewPdf from './components/preview-pdf/index.vue';
import { getSignInfo, postSignFlowAuth } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:visible', 'refresh']);

const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  },
});

// 合同签署状态
const statusText = ['未知', '草稿', '签署中', '签署完成', '撤销', '终止', '过期', '拒签'];

const loading = ref(false);
const isCompleteSign = ref(false);
const docList = ref([]);
const contractStatus = ref(0);
const contractName = ref('');

watch(
  bindVisible,
  value => {
    if (value) {
      loading.value = true;
      getSignInfo({ order_id: props.orderId })
        .then(res => {
          if (res?.data) {
            isCompleteSign.value = res.data.is_complete_sign;
            docList.value = res.data.doc_list;
            contractStatus.value = Number(res.data.contract_status);
            contractName.value = res.data.contract_name;
          }
        })
        .catch(() => (bindVisible.value = false))
        .finally(() => (loading.value = false));
    }
  },
  { immediate: true },
);

const submitLoading = ref(false);

const previewPdfVisible = ref(false);

/** 下载链接 */
const previewLink = ref('');

function handleView(url: string) {
  if (contractStatus.value === 3) {
    previewLink.value = url;
    previewPdfVisible.value = true;
  } else {
    message.error('合同签署完成才能查看');
  }
}

async function submit() {
  submitLoading.value = true;
  await postSignFlowAuth({ order_id: props.orderId }).finally(() => (submitLoading.value = false));
  message.success('签署成功');
  bindVisible.value = false;
  emit('refresh', props.orderId);
}
</script>

<style scoped lang="less">
.preview-pdf-container {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .pdf-viewer {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}

.file-extra {
  position: relative;
  display: inline-block;
  width: 100px;
  height: 100px;
  background: url(https://img1.rrzuji.cn/assest/201904/SUPER5CB6D3794DA60.png) no-repeat;
  background-position: 20px 20px;
  background-size: 60px 60px;
  border: 1px solid #eee;

  .type {
    position: absolute;
    bottom: 33px;
    left: 49px;
    width: 100%;
  }
}

.pdf-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 10px;

  .pdf-name {
    display: flex;
    align-items: center;
    justify-content: space-between;

    span {
      width: 400px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
