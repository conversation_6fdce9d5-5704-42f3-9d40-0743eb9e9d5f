import { EQualificationFileType } from '@/pages-stage/commodity/dict/qualification/data.d';

import GoodsInfo from './components/goods-info/index.vue';
import IndustryInfo from './components/industry-info/index.vue';
import InterviewInfo from './components/interview-info.vue';
import StoreUpgrade from './components/store-upgrade/index.vue';
import { EOptions } from './data.d';

// 选项
export const options = [
  {
    label: '行业资质',
    value: EOptions.INDUSTRY_QUALIFICATIONS,
  },
  {
    label: '商品资质',
    value: EOptions.GOODS_QUALIFICATIONS,
  },
  {
    label: '面审信息',
    value: EOptions.INTERVIEW_INFO,
  },
  {
    label: '店铺升级',
    value: EOptions.SHOP_UPGRADE,
  },
];

export const currentComMap = {
  [EOptions.INTERVIEW_INFO]: InterviewInfo,
  [EOptions.INDUSTRY_QUALIFICATIONS]: IndustryInfo,
  [EOptions.GOODS_QUALIFICATIONS]: GoodsInfo,
  [EOptions.SHOP_UPGRADE]: StoreUpgrade,
};

export const backTypeMap = {
  [EQualificationFileType.INDUSTRY]: EOptions.INDUSTRY_QUALIFICATIONS,
  [EQualificationFileType.GOODS]: EOptions.GOODS_QUALIFICATIONS,
};
