<script setup lang="ts">
import { nextTick, ref } from 'vue';
import dayjs from 'dayjs';
import { RTable, RTableColumnType, RTableInstance, useBoolean } from 'rrz-web-design';

import { filterOption, mapToOption } from '@/pages-stage/operations/log-manage/decision-path-config/utils';

import { getEditFields, getSupplementInfoLogs } from '../services';

const tableColumns: RTableColumnType[] = [
  {
    title: '修改时间 ',
    dataIndex: 'created_at_text',
    width: 120,
    valueType: 'rangePicker',
    search: {
      transform: ([start, end]) => {
        return {
          edit_time: [dayjs(start).unix(), dayjs(end).endOf('day').unix()],
        };
      },
    },
  },
  { title: '修改人 ', dataIndex: 'created_by_name', width: 140, hideInSearch: true },
  {
    title: '修改类目',
    dataIndex: 'edit_field_name',
    width: 200,
    valueType: 'select',
    formFieldProps: {
      elProps: { mode: 'multiple', maxTagCount: 'responsive', showSearch: true, filterOption },
      setOption: async () => {
        const { data } = await getEditFields();
        return mapToOption(data);
      },
    },
    search: {
      transform: edit_field => ({ edit_field }),
    },
  },
  { title: '变更前内容', dataIndex: 'before_data', hideInSearch: true },
  { title: '变更后内容', dataIndex: 'after_data', hideInSearch: true },
  { title: '操作结果', dataIndex: 'port_label', width: 100, hideInSearch: true },
];

const [visible, setVisible] = useBoolean();

const tableRef = ref<RTableInstance>();

defineExpose({
  open() {
    setVisible(true);
    nextTick(() => tableRef.value?.getTableList('search'));
  },
});
</script>

<template>
  <a-drawer
    v-model:visible="visible"
    :body-style="{
      padding: 0,
      margin: '24px',
    }"
    title="修改记录"
    width="min(calc(100vw - 24px), 1080px)"
  >
    <RTable
      ref="tableRef"
      :api="getSupplementInfoLogs"
      :auto-fetch="false"
      :columns="tableColumns"
      in-modal
    />
  </a-drawer>
</template>
