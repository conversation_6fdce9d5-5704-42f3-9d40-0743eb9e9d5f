<template>
  <!-- 为特殊类目时，不显示该模块 -->
  <div
    v-if="!isSpecialCategory"
    class="sku-info flex-wrap flex-vertical flex-gap-8"
  >
    <div class="bold">
      根据货品信息，您可选择以下方式对订单进行发货：
    </div>
    <div class="delivery-card">
      <div>
        <span class="delivery-card-label">应发货品：</span>
        <span
          v-if="sku?.sku_info"
          class="text-link"
        >{{ sku.sku_info }}
          <a-tag
            v-for="(item, index) in getRandomName(sku.attr_tag)"
            :key="index"
            color="blue"
          >{{
            '随机' + item
          }}</a-tag>
        </span>
        <!-- 未识别到货品信息时,显示下面的文案 -->
        <span
          v-else
          class="bold"
        >系统暂未识别到货品信息</span>
      </div>
      <div v-if="sku?.one_device_code">
        <span class="delivery-card-label">应发设备码：</span>
        <span>
          {{ sku?.one_device_code }}
        </span>
      </div>
      <div v-show="!isPlatformShipment && sku?.order_id">
        <span class="delivery-card-label">订单号：</span>
        <span class="text-link">{{ sku.order_id }}</span>
      </div>
    </div>
  </div>
  <!-- 代发指南 -->
  <div
    v-if="isPlatformShipment"
    class="delivery-guide"
  >
    <Collapse
      :default-expand="true"
      :trigger-style="{ width: 'fit-content' }"
    >
      <template #trigger="{ expand }">
        <div class="flex-wrap flex-y-center flex-gap-6 delivery-guide-trigger">
          <span><QuestionCircleFilled style="color: #8f95a3" /></span>
          <span class="bold">代发指南</span>
          <span
            class="text-link action-icon"
            :style="{
              transform: `rotate(${expand ? 180 : 0}deg)`,
            }"
          ><DownOutlined /></span>
        </div>
      </template>
      <div class="delivery-guide-content">
        <span>商家可向平台进行充值采购设备，由平台仓库进行发货，发货后结算设备采购费用，详细可查阅</span>
        <span
          class="text-link"
          @click="handleCheckRules"
        >《平台代发规则说明》</span>
      </div>
    </Collapse>
  </div>
</template>

<script lang="ts" setup>
import { DownOutlined, QuestionCircleFilled } from '@ant-design/icons-vue';

import Collapse from '@/components/collapse/collapse.vue';

import type { IDeliverStore } from '../../data';

defineProps<{
  sku: IDeliverStore;
  isSpecialCategory: boolean;
  isPlatformShipment: boolean;
}>();

function handleCheckRules() {
  window.open('https://img1.rrzuji.cn/template_doc/PlatformDistributionRules.pdf', '_blank');
}

function getRandomName(attr_tag: string) {
  if (attr_tag) {
    return attr_tag.split(',').map(item => item.trim());
  }
  return [];
}
</script>

<style scoped lang="less">
.bold {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
}

.delivery-card {
  position: relative;
  padding: 12px 24px;
  color: rgba(6, 21, 51, 0.65);
  background: #f0f7ff;
  border-radius: 4px;
}

.delivery-guide {
  &-trigger {
    cursor: pointer;
  }

  &-content {
    margin-top: 4px;
    padding: 12px 16px;
    background: #f0f7ff;
    border-radius: 4px;
  }
}

.action-icon {
  cursor: pointer;
  transition: all 0.2s;
}
</style>
