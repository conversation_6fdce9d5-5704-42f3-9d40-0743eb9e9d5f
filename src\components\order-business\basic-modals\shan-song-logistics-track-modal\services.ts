import { GET } from '@/services/api';

/**
 * @description: 获取闪送物流详情（运营端）
 * @param {any} params
 * @return {*}
 */
export function superGetSSTransportDetail(params: any) {
  return GET('/logistics/admin/logistics/orderDetail', params, {
    hostType: 'Golang',
  });
}

/**
 * @description: 获取闪送物流详情（商家端）
 * @param {any} params
 * @return {*}
 */
export function getSSTransportDetail(params: any) {
  return GET('/logistics/server/logistics/orderDetail', params, {
    hostType: 'Golang',
  });
}
