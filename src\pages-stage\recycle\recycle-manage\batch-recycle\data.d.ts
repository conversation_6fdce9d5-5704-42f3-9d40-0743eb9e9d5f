export interface IDataSourceItem {
  // 订单号
  order_id: string;
  // 商家ID
  server_id: string;
  // 批次号
  batch_no: string;
  // 订单状态
  order_status: string;
  // 估价
  evaluate_price: string;
  // 定价
  purchased_price: string;
  // 创建时间
  created_at: string;
  // 商品名称
  goods_name: string;
  // 商品图
  goods_image: string;
  // 订单状态文案
  order_status_text: string;
  // 是否可取消订单 1是0否
  canCloseOrder: number;
  // 是否展示查看检测报告按钮 1是0否
  showBtnViewQuaReport: number;
  // 是否可设置定价 1是0否
  canSetPurchasedPrice: number;
  // 物流信息
  logisticInfo: ILogisticInfo;
  //退回地址详情
  returnLogisticInfo: ILogisticInfo;
  //是否可以修改退回地址：1是2否
  canEditReturnInfo: number;
  // 协议url
  protocol_url?: string;
}

export interface ILogisticInfo {
  addr: string;
  tracking_num: string;
  phone: string;
  logistic_code: string;
  logistic_phone: string;
  shipper_name: string;
  name: string;
}
