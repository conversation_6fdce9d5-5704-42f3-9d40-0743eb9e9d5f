<script setup lang="ts">
import { ref } from 'vue';
import { message, ModalProps } from 'ant-design-vue';
import type { RModalFormInstance } from 'rrz-web-design';
import { RModalForm } from 'rrz-web-design';

import { formGroup } from './config';
import { APIBatchAddTag } from './services';
const emit = defineEmits(['refresh']);

const layout = {
  layout: 'vertical',
};

const refRModalForm = ref<RModalFormInstance | null>(null);
const isShowModal = ref(false);

const initFormData = async () => {
  isShowModal.value = true;
  refRModalForm.value?.open();
};

const closeModal = () => {
  if (refRModalForm.value) {
    refRModalForm.value.resetFields();
    refRModalForm.value.close();
  }
  isShowModal.value = false;
};

const sendData = async (data: Record<string, any>) => {
  if (refRModalForm.value) {
    await refRModalForm.value.validate();
    await APIBatchAddTag({
      ...data,
      tagIds: data.tagIds ? data.tagIds : [data.pid],
    });
    message.success('操作成功');
    closeModal();
    emit('refresh');
  }
};

const modalProps: ModalProps = {
  destroyOnClose: true,
  bodyStyle: { padding: '24px 0 24px 24px' },
  width: 500,
  zIndex: 999,
};

defineExpose({
  open: initFormData,
});
</script>

<template>
  <RModalForm
    ref="refRModalForm"
    v-model:open="isShowModal"
    :form-group="formGroup"
    :form-props="{ originProps: layout }"
    :modal-props="modalProps"
    title="批量添加标签"
    @submit="sendData"
  />
</template>
