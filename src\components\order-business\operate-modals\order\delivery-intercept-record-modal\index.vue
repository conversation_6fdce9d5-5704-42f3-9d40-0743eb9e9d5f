<template>
  <a-modal
    :confirm-loading="submitLoading"
    destroy-on-close
    :footer="null"
    style="width: 480px"
    :title="title"
    :visible="visible"
    @cancel="onCancel"
  >
    <div class="content">
      <template
        v-for="item in workList"
        :key="item.id"
      >
        <div class="work-item">
          <div class="top">
            <div class="left-info">
              <div class="info-title">
                {{ item.current_node }}
              </div>
              <div class="info-desc">
                {{ item.work_type_text }}
              </div>
            </div>

            <div class="right-status">
              <span
                v-if="[1, 5].includes(item.work_status)"
                class="repeal-text"
                @click="onRepeal(item.id)"
              >撤销</span>
              <!-- <span
                v-if="[6].includes(item.work_type)"
                class="repeal-text"
                @click="goToWorkDetail(item.id)"
              >工单详情</span> -->
              <a-badge
                :status="statusMap[item.work_status]"
                :text="item.work_status_text"
              />
            </div>
          </div>
          <div class="preview-verify">
            <div>相关凭证:</div>
            <div class="preview-info">
              <template
                v-for="(preItem, preIndex) in getPreviewInfo(item)"
                :key="preIndex"
              >
                <a-button @click="openPreview(preItem)">
                  {{ preItem.label }}
                </a-button>
              </template>
            </div>
          </div>
        </div>
      </template>
    </div>
  </a-modal>
  <RPreview
    v-model:preview="previewInfo.open"
    :type="previewInfo.type"
    :value="previewInfo.url"
  />
  <a-modal
    v-model:visible="showAssociation"
    :footer="null"
    title="关联的工单"
  >
    <div
      v-for="item in associationList"
      :key="item.number"
      class="association-item"
    >
      <span>{{ item.sort_name }}：</span>
      <a-button
        size="small"
        type="link"
        @click="handleJumpWorkOrder(item.number)"
      >
        {{ item.number }}
      </a-button>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { RPreview } from 'rrz-web-design';

import type { TRefreshDataKey } from '@/components/order-business/typing';

import { getRole } from '../delivery-intercept-modal/config';
import { IWorkListItem, TAssociationWorkNumber } from './data';
import { dealOrderWorkStatus, getWorkOrder } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
  (e: 'showInterceptModal'): void;
}>();

const statusMap = {
  1: 'warning',
  2: 'processing',
  3: 'default',
  4: 'error',
};

const submitLoading = ref(false);

const workList = ref<IWorkListItem[]>([]);

const previewInfo = ref({
  open: false,
  type: 'img',
  url: '',
});

const getPreviewInfo = (item: any) => {
  const { images, video, audio } = item;
  const parseList = (str: string | undefined) => (str ? str.split(',') : []);

  const imagesList = parseList(images);
  const videoList = parseList(video);
  const audioList = parseList(audio);
  return [
    ...imagesList.map((url, index) => ({ label: `图片${index + 1}`, type: 'img', url })),
    ...videoList.map((url, index) => ({ label: `视频${index + 1}`, type: 'video', url })),
    ...audioList.map((url, index) => ({ label: `音频${index + 1}`, type: 'audio', url })),
  ];
};

const openPreview = (previewItem: any) => {
  const { type, url } = previewItem;
  previewInfo.value = {
    open: true,
    type,
    url,
  };
};

const associationList = ref<TAssociationWorkNumber[]>([]);
const showAssociation = ref(false);
// async function goToWorkDetail(id: number) {
//   const { data } = await getAssociationWorkNumber({ association_id: id + '', type: 1 });
//   if (Array.isArray(data) && data.length) {
//     associationList.value = data;
//     showAssociation.value = true;
//   } else {
//     message.success('无工单详情');
//   }
// }
const route = useRoute();
function handleJumpWorkOrder(number: string) {
  const domain = route.query.origin || window.location.origin;
  const isSuper = getRole() === 'super';
  const queryText = new URLSearchParams({
    number: number,
    activeKey: '2',
  }).toString();
  const url = isSuper
    ? `/super/new-work-order/index?number=${number}&url=/super/work-order-inter/index`
    : `/work-order/index?${queryText}`;
  window.open(`${domain}${url}`);
}

// 撤销工单
const onRepeal = (id: number) => {
  dealOrderWorkStatus({
    id,
    work_status: 4, // 撤销
  }).then(() => {
    onCancel();
    message.success('操作成功');
    emit('refresh', props.orderId, ['data', 'all_remark']);
  });
};

watchEffect(async () => {
  if (!props.visible) return;
  const current_node = props.title === '发货拦截申请' ? 2 : 1;
  const { data } = await getWorkOrder({
    v3_order_id: props.orderId,
    current_node,
  });
  workList.value = data;
});

const onCancel = () => {
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.content {
  .work-item {
    padding: 16px 24px;
    background-color: rgba(6, 21, 51, 0.04);
    border-radius: 4px;

    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .left-info {
      color: rgba(6, 21, 51, 0.45);
      font-size: 12px;
      .info-desc {
        padding-top: 8px;
        color: rgba(6, 21, 51, 0.85);
        font-size: 14px;
      }
    }

    .right-status {
      display: flex;
      gap: 8px;
      align-items: center;
      .repeal-text {
        color: #3777ff;
        cursor: pointer;
        text-decoration-line: underline;
      }
    }

    &:not(:last-child) {
      margin-bottom: 16px;
    }
  }
}

.preview-verify {
  display: flex;
  gap: 16px;
  margin-top: 8px;

  .preview-info {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 8px;
  }
}
.association-item {
  display: flex;
  gap: 16px;
  align-items: center;
}
.association-item:not(:last-child) {
  margin-bottom: 16px;
}
</style>
