<template>
  <div class="select-row">
    <a-select
      v-model:value="form.attr"
      allow-clear
      :disabled="disabled"
      :field-names="{
        label: 'label',
        value: 'value',
      }"
      :filter-option="filterOption"
      :options="options"
      placeholder="请选择属性"
      show-search
      :style="`width: ${leftWidth}; margin-right: 8px`"
      @change="onAttrChange"
    />
    <a-select
      v-model:value="form.content"
      allow-clear
      :disabled="disabled"
      :filter-option="filterOption"
      :mode="isSingle ? '' : 'multiple'"
      option-label-prop="label"
      :options="childOptions"
      placeholder="请选择内容"
      show-search
      :style="`width: ${rightWidth}`"
      @change="onContentChange"
    />
    <slot />
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Array,
      default: () => [],
    },
    contentListMap: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    leftWidth: {
      type: String,
      default: '160px',
    },
    rightWidth: {
      type: String,
      default: '480px',
    },
    filterKey: {
      type: String,
      default: 'label',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['getContentOptions'],
  data() {
    return {
      contentOptions: [],
      contentLoading: false,
      form: {},
    };
  },
  computed: {
    contentOptionsEmptyText({ form, contentOptions, contentLoading }) {
      const { attr } = form;
      if (!attr) return '请先选择属性';
      if (contentLoading) return '正在获取中...';
      if (!contentLoading && !contentOptions.length) return '暂无数据';
      return '暂无数据';
    },
    // 是否为单选
    isSingle({ options, form }) {
      const info = options.find(item => item.value === form.attr) || {};
      return info.is_single === 1;
    },
    // 二级下拉选择
    childOptions({ list, contentListMap, form }) {
      const { attr } = form;
      const options = JSON.parse(JSON.stringify(contentListMap[attr] || []));
      // 筛选出与当前一级选项一致的数据
      const filterList = list.filter(item => item.attr === attr);
      const contentKeys = filterList.reduce((pre, cur) => {
        return [...pre, ...cur.content];
      }, []);
      options.forEach(item => (item.disabled = contentKeys.includes(item.value)));
      return options;
    },
  },
  watch: {
    value: {
      handler(value) {
        this.form = value || {};
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    onAttrChange(value, options) {
      this.form.attr_label = (options && options.label) || '';
      this.form.content = this.isSingle ? null : [];
      this.form.content_label = this.isSingle ? null : [];
      this.form.child = [];
    },
    onContentChange(value, options) {
      this.form.content_label = this.isSingle
        ? this.getSingleData(value, options)
        : this.getMultipleData(value, options);
    },
    // 获取单选时，content_label的数据储存格式
    getSingleData(value, options) {
      return !value ? [] : [options.label];
    },
    // 获取多选值时，content_label的数据储存格式
    getMultipleData(value, options) {
      return !value ? [] : options.length ? options.map(item => item.label) : [];
    },
    filterOption(value, option) {
      return option[this.filterKey].toLowerCase().indexOf(value.toLowerCase()) >= 0;
    },
  },
};
</script>

<style lang="less" scoped>
.select-row {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 8px;
  border-radius: 2px;
  &:hover {
    background: rgba(6, 21, 51, 0.04);
  }
}
:deep(.anticon-close) {
  font-size: 12px !important;
}
</style>
