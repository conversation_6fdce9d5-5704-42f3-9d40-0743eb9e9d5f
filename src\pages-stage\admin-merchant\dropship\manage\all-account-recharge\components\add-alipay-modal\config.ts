import { LocationQueryRaw } from 'vue-router';

import { FormGroupItem } from '@/components/form-create/src/typing';

const addBankModalFormGroup: (FormGroupItem & { exist?: (params: LocationQueryRaw) => boolean })[] = [
  {
    key: 'alipay_payuser',
    originProps: { label: '支付宝户名', name: 'alipay_payuser' },
    elProps: {
      allowClear: true,
      style: { width: '100%' },
      placeholder: '请输入支付宝户名',
      controls: false,
    },
    fragmentKey: 'renderInput',
  },
  {
    key: 'alipay_account',
    originProps: { label: '支付宝账号', name: 'alipay_account' },
    elProps: { allowClear: true, style: { width: '100%' }, placeholder: '请输入支付宝账号', controls: false },
    fragmentKey: 'renderInput',
  },
];

export { addBankModalFormGroup };
