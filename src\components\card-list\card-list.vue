<template>
  <div>
    <div class="header flex-wrap flex-y-center">
      <div
        v-if="cardTitle"
        class="title flex-wrap flex-y-center"
      >
        <span>{{ cardTitle }}</span>
        <a-switch
          v-if="proxyChildSwitchAssign.show && cardSwitchName"
          :checked="proxySwitchStatus"
          class="proxy-child-switch"
          :size="proxyChildSwitchAssign.size"
          style="margin-left: 8px"
          @change="proxyChildSwitchStatus"
        />
        <div
          v-if="isOrderCancelPop"
          class="flex-wrap flex-y-center"
          style="cursor: pointer"
          @click="onEdit()"
        >
          <plus-outlined class="header-add-icon" />
          <span class="header-add-btn">添加{{ text }}</span>
        </div>
      </div>
      <div
        v-if="cardText"
        class="text"
      >
        {{ cardText }}
      </div>
    </div>
    <span
      v-if="addBtnTitle && btnTitleBefore"
      class="btn-title"
    > {{ addBtnTitle }}</span>
    <template v-if="pageName === 'lifePageConfig'">
      <div :class="[text !== '模块（产品展示）' ? 'header-box' : 'modal-box']">
        <i
          v-show="text !== '模块（产品展示）'"
          class="header-icon"
          style="margin-left: 16px"
        />
        <span
          v-if="addBtnTitle && !btnTitleBefore"
          class="btn-title"
          style="margin-left: 8px"
        > {{ addBtnTitle }}</span>
        <a-button
          v-if="isEdit && allowAdd && items.length < max"
          :class="['add-btn', pageName === 'lifePageConfig' && text !== '商品' ? 'add-border' : '']"
          size="large"
          style="margin-bottom: 0"
          type="link"
          @click="onEdit()"
        >
          <PlusOutlined />
          {{ pageName === 'lifePageConfig' && text !== '商品' ? '新增' : '添加' }}{{ text }}
        </a-button>
      </div>
    </template>
    <template v-else>
      <slot
        v-if="isEdit && allowAdd"
        name="addBtn"
      >
        <a-button
          class="add-btn"
          :class="{ 'disabled-add-btn': items.length >= max }"
          size="large"
          type="link"
          @click="onEdit()"
        >
          <PlusOutlined />
          添加{{ text }}
          <span v-if="isShowMax">（{{ items.length }}/{{ max }}）</span>
          <span v-if="showItemsLength && items.length">（{{ items.length }}）</span>
        </a-button>
      </slot>

      <span
        v-if="addBtnTitle && !btnTitleBefore"
        class="btn-title"
      > {{ addBtnTitle }}</span>
      <img
        v-if="addBtnTitleTag && addBtnTitleTag != ''"
        alt="addBtnTitleTag"
        class="title-tag"
        contextmenu=""
        :src="addBtnTitleTag"
      >
      <span
        v-if="addBtnSubtitle"
        class="btn-sub-title"
      > {{ addBtnSubtitle }}</span>
    </template>

    <span v-if="scrollbg && scrollbg.method == 'text'">
      <br>
      <CardImage
        v-if="scrollbg"
        :allow-delete="false"
        :allow-edit="false"
        :card-switch-false-value="cardSwitchFalseValue"
        :card-switch-name="cardSwitchName"
        :card-switch-true-value="cardSwitchTrueValue"
        :card-text-type="cardTextType"
        :card-width="cardWidth"
        :image-height="imageHeight"
        :image-key="imageKey"
        :image-width="imageWidth"
        :is-edit="isEdit"
        :item="scrollbg"
        :show-image-path="showImagePath"
        style="margin-left: 7px"
        @on-delete="onDelete"
        @on-edit="onEdit(scrollbg)"
        @on-switch="onSwitch"
      />
    </span>
    <drag
      :direction="direction"
      :item-width="itemWidth"
      :items="items"
      @on-drag-start="onDragStart"
      @on-sort="onSortChange"
    >
      <template #item="{ item }">
        <template v-if="cardType === 'scheme'">
          <CardScheme
            :active="item.active"
            :allow-delete="allowDelete"
            :card-switch-false-value="cardSwitchFalseValue"
            :card-switch-name="cardSwitchName"
            :card-switch-true-value="cardSwitchTrueValue"
            :card-text-type="cardTextType"
            :card-width="cardWidth"
            :image-height="imageHeight"
            :image-width="imageWidth"
            :is-edit="isEdit"
            :item="item"
            :new-terminal="newTerminal"
            :preview="false"
            @click="cardClick(item.dragId)"
            @on-delete="onDelete"
            @on-edit="onEdit(item)"
            @on-switch="onSwitch"
          />
        </template>
        <template v-if="cardType === 'image'">
          <CardImage
            :allow-delete="allowDelete"
            :allow-edit="allowEdit"
            :card-switch-false-value="cardSwitchFalseValue"
            :card-switch-name="cardSwitchName"
            :card-switch-show="cardSwitchShow"
            :card-switch-true-value="cardSwitchTrueValue"
            :card-text-type="cardTextType"
            :card-width="cardWidth"
            :image-height="imageHeight"
            :image-key="imageKey"
            :image-width="imageWidth"
            :is-edit="isEdit"
            :is-order-cancel-pop="isOrderCancelPop"
            :item="item"
            :show-image-path="showImagePath"
            @on-delete="onDelete"
            @on-edit="onEdit(item)"
            @on-switch="onSwitch"
          />
        </template>
        <template v-if="cardType === 'text'">
          <CardText
            :allow-delete="allowDelete"
            :card-item-class="cardItemClass"
            :card-switch-false-value="cardSwitchFalseValue"
            :card-switch-name="cardSwitchName"
            :card-switch-true-value="cardSwitchTrueValue"
            :card-text-type="cardTextType"
            :card-width="cardWidth"
            :is-edit="isEdit"
            :item="item"
            :switch-wrap-style="switchWrapStyle"
            @on-delete="onDelete"
            @on-edit="onEdit(item)"
            @on-switch="onSwitch"
          />
        </template>
        <template v-if="cardType === 'menu'">
          <CardMenu
            :active="item.active"
            :allow-delete="allowDelete"
            :allow-edit="item.allowEdit === false ? false : true"
            :card-item-class="cardItemClass"
            :card-switch-false-value="cardSwitchFalseValue"
            :card-switch-name="cardSwitchName"
            :card-switch-true-value="cardSwitchTrueValue"
            :card-text-type="cardTextType"
            :card-width="cardWidth"
            :is-edit="isEdit && !item.noEdit"
            :item="item"
            :show-drag-icon="showDragIcon"
            @click="cardClick(item.dragId)"
            @on-delete="onDelete"
            @on-edit="onEdit(item)"
            @on-switch="onSwitch"
          />
        </template>
        <template v-if="cardType === 'coupon'">
          <CardCoupon
            :allow-delete="allowDelete"
            :card-switch-false-value="cardSwitchFalseValue"
            :card-switch-name="cardSwitchName"
            :card-switch-true-value="cardSwitchTrueValue"
            :card-text-type="cardTextType"
            :card-width="cardWidth"
            :image-height="imageHeight"
            :image-key="imageKey"
            :image-width="imageWidth"
            :is-edit="isEdit"
            :item="item"
            :show-image-path="showImagePath"
            @on-delete="onDelete"
            @on-edit="onEdit(item)"
            @on-switch="onSwitch"
          />
        </template>
        <template v-if="cardType === 'cardListGroup'">
          <card-list-group
            :group-type="groupType"
            :items="item.items"
            :page-name="pageName"
            :parent="item"
            @on-delete="onDelete(item)"
            @on-edit="onEdit(item)"
          />
        </template>
      </template>
    </drag>
    <form-modal
      v-model:visible="formModal.visible"
      :async-data="formModal.asyncData"
      :auto-close="modalShow"
      :extra-post="formModal.extraPost"
      :form="formModal.form"
      :label-col="labelCol"
      :title="formModal.title"
      :url="formModal.url"
      :width="formWidth"
      @on-success="onSuccess"
      @on-validate="onValidate"
      @radio-change="onRadioChange"
    />
  </div>
</template>
<script>
/**
 * 这是一个业务组件，显示一个可拖拽可编辑的卡片列表
 * tips: 这个组件的数据，一定要整个替换才会被监听。
 *
 * 1.支持租赁商品显示以及编辑删除
 * 2.支持轮播图片显示以及编辑删除
 * 3.
 */
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { noop } from 'lodash-es';

import { formConfig, modalFormLabelCol } from '../../config/form-config';
import { card } from '../../mixin/card';
import CardCoupon from '../card/card-coupon.vue';
import CardImage from '../card/card-image.vue';
import CardMenu from '../card/card-menu.vue';
import CardScheme from '../card/card-scheme.vue';
import CardText from '../card/card-text.vue';
import CardListGroup from '../card-list-group/card-list-group.vue';
export default {
  components: {
    PlusOutlined,
    CardScheme,
    CardImage,
    CardText,
    CardMenu,
    CardCoupon,
    CardListGroup,
  },
  inject: {
    formGroup: {
      from: 'formGroup',
      default: undefined,
    },
    effects: {
      from: 'CardListEffects',
      default: {
        trigger: noop,
      },
    },
  },
  props: {
    value: {
      type: Array,
      default() {
        return [];
      },
    },
    cardType: {
      type: String,
      default: 'image', // image | scheme | text | menu
    },
    cardText: {
      type: String,
      default: '', // image | scheme | text | menu
    },
    cardTitle: {
      type: String,
      default: '', // image | scheme | text | menu
    },
    scrollbg: {
      type: null,
      default: '', // image | scheme | text | menu
    },
    cardForm: {
      type: String,
      default: '',
    },
    direction: {
      type: String,
      default: 'horizontal', // vertical | horizontal
    },
    addBtnText: {
      type: String,
      default: '',
    },
    itemWidth: {
      type: [String, Number],
      default: 'auto',
    },
    formWidth: {
      type: [Number],
      default: 800,
    },
    isAddSign: {
      type: Boolean,
      default: false,
    },
    sameSign: {
      type: Boolean,
      default: false,
    },
    groupType: {
      type: String,
      default: '',
    },
    addBtnTitle: {
      type: String,
      default: '',
    },
    addBtnSubtitle: {
      type: String,
      default: '',
    },
    addBtnTitleTag: {
      type: String,
      default: '',
    },
    modalShow: {
      type: Boolean,
      default: true,
    },
    valiData: {
      type: Function,
      default: null,
    },
    //隐藏子模块的switch，一般在被父模块或者其他代理switch之后开启，自身不再需要控制switch
    cardSwitchShow: {
      type: Boolean,
      default: true,
    },
    //用于代理开启了cardSwitchName字段的子模块，接管所有子模块中的switch
    proxyChildSwitch: {
      type: Object,
      default() {
        return {};
      },
    },
    //用于代理其他模块的switch,数组内的模块switch状态会与当前一致
    proxyModuleSwitch: {
      type: Array,
      default() {
        return [];
      },
    },
    //指定switch默认值
    defaultCardSwitchValue: {
      type: Boolean || Number || String || undefined,
      default: undefined,
    },
    //指定btnTitle样式放前面
    btnTitleBefore: {
      type: Boolean,
      default: false,
    },
    pageName: {
      type: String,
      default: '',
    },
    // 当点击保存或者修改时，是否调用message进行提示
    dialogConfirmShoweMessage: {
      type: Boolean,
      default: true,
    },
    // 鉴别值
    identity: {
      type: String,
      default: '',
    },
    //是否启用自定义删除
    customDelete: {
      type: Boolean,
      default: false,
    },
    /**配置的小程序端口 */
    newTerminal: {
      type: String,
      default: '',
    },
    /** 是否显示列表长度 */
    showItemsLength: {
      type: Boolean,
      default: false,
    },
    ...card.props,
  },
  emits: [
    'update:value',
    'on-card-click',
    'on-success',
    'on-edit',
    'on-add',
    'on-delete',
    'on-switch',
    'on-before-edit',
    'on-sort-change',
    'on-drag-start',
  ],
  data() {
    return {
      retailPriceObject: {
        key: 'retail_price',
        title: '价格',
        placeholder: '请输入价格',
        value: '',
        type: 'string',
        rules: [
          {
            required: true,
            message: '请输入价格',
            trigger: ['blur', 'change'],
          },
        ],
      },
      items: [],
      formModal: {
        visible: false,
        form: [],
        title: '',
        asyncData: false,
        url: '',
        extraData: {},
      },
      id: '',
      labelCol: { span: 4 },
    };
  },
  computed: {
    text() {
      if (this.addBtnText) {
        return this.addBtnText;
      }
      const { cardType } = this;
      let text = '';
      switch (cardType) {
        case 'scheme':
          text = '商品';
          break;
        case 'image':
          text = '图片';
          break;
        case 'coupon':
          text = '优惠券';
          break;
        case 'text':
          text = '';
          break;
      }
      return text;
    },
    isShowMax() {
      return !(this.max === Infinity);
    },
    proxySwitchStatus() {
      let status = false;
      if (this.items.length) {
        status = this.items.every(item => {
          return item[this.cardSwitchName];
        });
      }
      //让被代理模块的状态跟当前模块switch保持一样
      this.handleOtherModuleSwitch(status);
      return status;
    },
    // 合并初始值
    proxyChildSwitchAssign() {
      return Object.assign(
        {
          show: false,
          size: 'default',
          conflictModule: [], //与自身switch状态冲突的其他card-list模块
        },
        this.proxyChildSwitch,
      );
    },
  },
  watch: {
    value: {
      handler(value) {
        console.log(value);
        // 避免重复更新，例如排序时触发了双向绑定更新，会有问题
        if (this.items === value && this.items) {
          return;
        }
        this.id = this.getUid();
        // 替换数组，触发drag内容变化 (相当于更改了prop数据，更改了父组件数据。要不然父组件，拿不到dragId)
        this.items = value;
        this.items.forEach((item, index) => {
          item.dragId = item.id || item.dragId || `${this.id}${index.toString()}`;

          // 特殊处理图片类型的extraData
          if (this.cardType === 'image' && item.extraData) {
            const target = item.extraData;
            if (typeof target === 'string') {
              item.extraData = target;
              return;
            }
            if (typeof target === 'object') {
              let res = '';
              Object.keys(target).forEach(key => {
                res = `${res}${res ? '&' : ''}${key}=${target[key]}`;
              });
              item.extraData = res;
              return;
            }
            item.extraData = '';
          }
          // 特殊处理商品类型的rent_tag 返回的数组要转成字符串
          if (this.cardType === 'scheme' && item.rent_tag) {
            item.rent_tag = item.rent_tag.toString();
          }
        });
      },
      immediate: true,
    },
  },
  methods: {
    cardClick(id) {
      this.$emit('on-card-click', id);
    },
    onDelete(item) {
      if (this.customDelete) {
        this.$emit('on-delete', item);
        return;
      }
      for (let i = 0; i < this.items.length; i++) {
        if (this.items[i].dragId === item.dragId) {
          this.items.splice(i, 1);
          this.$message.success('操作成功，保存修改后生效');
          this.$emit('on-delete');
          this.update();
          this.effects.trigger('delete', {
            target: item,
            identity: this.identity,
          });
          break;
        }
      }
    },
    // 存储被删除的ID，后续保存时进行同步
    recordPositionSign(item) {
      if (this.enableEffect) {
        const record = {
          position_id: item.position_sign,
          port: this.port,
          entrance: this.entrance,
        };
        const deletedPositionSign = window.sessionStorage.getItem('pit_deleted_position_sign') ?? '[]';
        const newVal = JSON.parse(deletedPositionSign);
        newVal.push(record);
        window.sessionStorage.setItem('pit_deleted_position_sign', JSON.stringify(newVal));
      }
    },
    openModal({ title, form, url, extraPost }) {
      this.formModal.title = title;
      this.formModal.form = form;
      this.formModal.asyncData = form[0].asyncData;
      this.formModal.url = url;
      this.formModal.extraPost = extraPost;

      this.formModal.visible = true;
    },
    onSuccess(form) {
      this.$emit('on-success', form);
    },
    onValidate(newItem) {
      const newItems = JSON.parse(JSON.stringify(this.items)); //即将更新的数据
      // 渠道重复自定义验证start
      if (this.activeItemId !== null) {
        for (let i = 0; i < this.items.length; i++) {
          if (this.items[i].dragId === this.activeItemId) {
            const tamp = this.handleValidateTamp(newItem, this.items[i]);
            newItems.splice(i, 1, tamp);
          }
        }
      } else {
        newItems.splice(newItems.length, 0, newItem);
      }
      if (this.valiData) {
        // 这里的返回值有boolean | string | array类型（之前遗留的问题，不好兼容）
        const result = this.valiData(newItems);
        // 因为之前写死了'已存在默认渠道,请选择其他渠道!'的提示，所以只能特殊处理
        const msg = result === true ? '已存在默认渠道,请选择其他渠道!' : result;
        if (result === true || Array.isArray(result)) {
          this.formModal.visible = false;
        } else {
          message.error(msg);
          return;
        }
      }

      // 渠道重复自定义验证end
      if (this.activeItemId !== null) {
        for (let i = 0; i < this.items.length; i++) {
          if (this.items[i].dragId === this.activeItemId) {
            const tamp = this.handleValidateTamp(newItem, this.items[i]);
            // 处理多个元素，共用一个坑位的情况，标识：sameSign
            if (this.sameSign && !tamp.position_sign) {
              tamp.position_if = 0;
              const firstHasSign = this.items.filter(e => e.position_sign)[0];
              tamp.position_sign = firstHasSign ? firstHasSign.position_sign : `pos${new Date().valueOf()}`;
            }
            if (this.isAddSign && !tamp.position_sign) {
              tamp.position_if = 0;
              tamp.position_sign = `pos${new Date().valueOf()}`;
            }
            this.items.splice(i, 1, tamp);
            this.dialogConfirmShoweMessage && this.$message.success('操作成功，保存修改后生效');
            this.update();
            this.$emit('on-edit', tamp);
            break;
          }
        }
      } else {
        const tamp = {
          ...newItem,
          dragId: `${this.items.length}${this.getUid()}`,
        };
        if (this.cardSwitchName) {
          tamp[this.cardSwitchName] =
            this.defaultCardSwitchValue == undefined ? this.cardSwitchTrueValue : this.defaultCardSwitchValue; // 防止新增时缺少控制字段
        }
        if (this.isAddSign) {
          tamp.position_if = 0;
          tamp.position_sign = `pos${new Date().valueOf()}`;
        }
        // 处理多个元素，共用一个坑位的情况，标识：sameSign
        if (this.sameSign) {
          tamp.position_if = 0;
          const firstHasSign = this.items.filter(e => e.position_sign)[0];
          tamp.position_sign = firstHasSign ? firstHasSign.position_sign : `pos${new Date().valueOf()}`;
        }
        this.items.splice(this.items.length, 0, tamp);
        this.$message.success('操作成功，保存修改后生效');
        this.update();
        this.$emit('on-add', tamp);
      }
    },
    onDragStart(data) {
      this.$emit('on-drag-start', data);
    },
    onSortChange(data) {
      this.update();
      this.$emit('on-sort-change', data);
    },
    update() {
      this.$emit('update:value', this.items);
    },
    onEdit(item) {
      this.$emit('on-before-edit', item);
      this.$nextTick(async () => {
        if (!formConfig[this.cardForm]) {
          return;
        }
        if (!item && this.items.length >= this.max) {
          return;
        }
        this.labelCol = modalFormLabelCol[this.cardForm + 'LabelCol'];
        let form = formConfig[this.cardForm].map(formItem => {
          // oppo商圈小程序-热租爆品\推荐产品-租金单位新增'零售价'单选按钮
          if (this.isOppoBD(formItem)) {
            const hasRetailPrice = formItem?.options?.find(item => item.label === '零售价');
            if (!hasRetailPrice) {
              formItem.options?.push({
                label: '零售价',
                value: '零售价',
              });
            }
            return { ...formItem };
          }
          formItem.options = formItem.options?.filter(item => item.label !== '零售价');

          return { ...formItem };
        });
        if (item && this.handleEditForm(form, item)) {
          form = this.handleEditForm(form, item);
        }
        await this.effects.trigger('before-edit', {
          target: item,
          form,
          identity: this.identity,
        });
        // 保留编辑标识
        this.activeItemId = null;
        if (item) {
          form.forEach(formItem => {
            if (typeof item[formItem.key] !== 'undefined') {
              formItem.value = item[formItem.key];
            }
          });
          this.activeItemId = item.dragId;
        }
        this.openModal({
          title: item ? `编辑${this.text}` : `添加${this.text}`,
          form,
          url: item?.formActionsUrl || '',
          extraPost: item?.extraPost || {},
        });
      });
    },
    // 卡片switch开关变更
    onSwitch(item, changeItemValue) {
      for (let i = 0; i < this.items.length; i++) {
        if (this.items[i].dragId === item.dragId) {
          this.items[i] = Object.assign(item, changeItemValue);
          this.update();
          break;
        }
      }
      this.$emit('on-switch', item, changeItemValue);
    },
    getUid() {
      return `list_${Math.random().toString(16).slice(-6)}${new Date().getTime()}`;
    },
    proxyChildSwitchStatus(status) {
      if (this.items.length && this.cardSwitchName) {
        this.items.forEach(item => {
          item[this.cardSwitchName] = status;
        });
        this.handleOtherModuleSwitch(status);
        this.handleConflictModule(status);
      }
    },
    // 代理其他模块的switch
    handleOtherModuleSwitch(status) {
      const modules = this.proxyModuleSwitch;
      modules.length &&
        modules.forEach(key => {
          const curModule = this.formGroup.filter(item => item.key == key)[0];
          if (curModule.value.length) {
            curModule.cardSwitchName != undefined &&
              curModule.value.map(item => {
                item[curModule.cardSwitchName] = status;
              });
          }
        });
    },
    //更改冲突模块switch
    handleConflictModule(status) {
      const modules = this.proxyChildSwitchAssign.conflictModule;
      if (!modules.length && !status) return;
      modules.forEach(key => {
        const curModule = this.formGroup.filter(item => item.key == key)[0];
        if (curModule.value.length) {
          curModule.cardSwitchName != undefined &&
            curModule.value.map(item => {
              item[curModule.cardSwitchName] = false;
            });
        }
      });
    },

    onRadioChange(data) {
      const { form, formItem } = data;
      // 命中oppo商圈-热阻爆品 || 推荐产品
      if (
        this.newTerminal === 'alipay.oppoBD' &&
        ['tabSchemeFormConfig', 'recommendProductFormConfig'].includes(this.cardForm)
      ) {
        this.handleRentUnit(form, formItem, this.cardForm);
      }
    },
    /**处理热租爆品\推荐产品的租金单位radio切换表单显示不同字段 */
    handleRentUnit(form, formItem, cardForm) {
      const keyArray = [];
      const titleArray = [];
      if (cardForm === 'tabSchemeFormConfig') {
        keyArray.push(...['origin_rental', 'rental']);
        titleArray.push(...['原租金', '活动租金']);
      } else if (cardForm === 'recommendProductFormConfig') {
        keyArray.push('rental');
        titleArray.push('最低租金');
      }

      // 租金单位为零售价的，移除对应keyArray\titleArray对应的表单key\title
      if (form?.suffix === '零售价' && formItem?.key === 'suffix' && formItem?.type === 'radio') {
        const newForm = this.formModal.form?.filter(item => {
          item.value = form[item.key];
          return !keyArray.includes(item.key) || !titleArray.includes(item.title);
        });
        newForm.splice(3, 0, this.retailPriceObject);
        this.formModal.form = newForm;
      } else {
        // 租金单位为非零售价,重置为默认的表单数据
        const fromConfigList = formConfig[this.cardForm].map(formItem => ({ ...formItem }));
        fromConfigList.forEach(formItemConfig => {
          if (typeof form[formItemConfig.key] !== 'undefined') {
            formItemConfig.value = form[formItemConfig.key];
          }
        });
        this.formModal.form = fromConfigList;
      }
    },

    /**
     * 租金单位为零售价且有价格字段时，增加价格表单项配置，去掉原有的租金相关表单项配置
     * form: 当前的表单配置项
     * curFormValue: 当前表单值对象
     */
    handleEditForm(form, curFormValue) {
      // 存在租金单位为零售价 && 价格字段
      if (curFormValue.suffix === '零售价' && curFormValue.retail_price) {
        const keyArray = [];
        const titleArray = [];
        if (this.cardForm === 'tabSchemeFormConfig') {
          keyArray.push(...['origin_rental', 'rental']);
          titleArray.push(...['原租金', '活动租金']);
        } else if (this.cardForm === 'recommendProductFormConfig') {
          keyArray.push('rental');
          titleArray.push('最低租金');
        }
        const newForm = form?.filter(item => !keyArray.includes(item.key) || !titleArray.includes(item.title));
        newForm.splice(3, 0, this.retailPriceObject);
        return newForm;
      }
      return null;
    },

    /**
     * 当是opoo商圈&&推荐产品|| 热租爆品&&存在租金字段为'零售价'时，移除'租金'相关字段
     * newItem： 当前表单字段值对象
     * configItem: 配置的表单字段值对象
     */
    handleValidateTamp(newItem, configItem) {
      let tamp = {
        ...configItem,
        ...newItem,
      };
      if (
        this.newTerminal === 'alipay.oppoBD' &&
        ['tabSchemeFormConfig', 'recommendProductFormConfig'].includes(this.cardForm) &&
        newItem.suffix === '零售价'
      ) {
        if (this.cardForm === 'tabSchemeFormConfig') {
          delete tamp['origin_rental'];
        }
        delete tamp['rental'];
      }
      return tamp;
    },

    /**判断: oppo商圈小程序&& (热租爆品|| 推荐产品模块) && 字段为租金单位 && 类型是单选 */
    isOppoBD(formItem) {
      return (
        this.newTerminal === 'alipay.oppoBD' &&
        ['recommendProductFormConfig', 'tabSchemeFormConfig'].includes(this.cardForm) &&
        formItem.key === 'suffix' &&
        formItem.type === 'radio'
      );
    },
  },
};
</script>
<style scoped lang="less">
.add-btn {
  margin-bottom: 8px;
  color: var(--ant-primary-color);
}
.disabled-add-btn {
  color: rgba(6, 21, 51, 0.25);
}
.add-btn:hover {
  color: #6198ff;
}
.header-add-btn {
  color: rgba(6, 21, 51, 0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
.header-add-icon {
  width: 15px;
  height: 16px;
  margin: 0 4px 0 16px;
  color: rgba(6, 21, 51, 0.65);
}
.header {
  .title {
    position: relative;
    margin-bottom: 8px;
    padding-left: 16px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
  }
  .title::before {
    position: absolute;
    top: 50%;
    left: 6px;
    width: 4px;
    height: 16px;
    background: rgb(0, 200, 190);
    transform: translateY(-50%);
    content: '';
  }
}
.btn-title,
.btn-sub-title {
  display: inline-block;
  margin-left: 18px;
  font-weight: bold;
  font-size: 16px;
  line-height: 26px;
}
.btn-sub-title {
  margin-left: 14px;
  color: #999;
  font-size: 14px;
}

.title-tag {
  display: inline-block;
  max-width: 32px;
  margin-left: 8px;
}

.add-border {
  margin-left: 16px;
  color: rgba(6, 21, 51, 0.65);
  font-size: 14px;
  line-height: 23px;
  border: 1px solid rgba(6, 21, 51, 0.15);
}

.header-box {
  display: flex;
  align-items: center;
  min-width: calc(100vw - 400px);
  margin-bottom: 10px;
  line-height: 40px;
  background: #f5f7fa;
  border-radius: 6px;
}

.header-icon {
  width: 2px;
  height: 14px;
  background: #00c8be;
}

.modal-box {
  margin-right: 6px;
  margin-bottom: 16px;
  margin-left: 6px;
  line-height: 72px;
  // background: #fff;
}
:deep(.ant-card-actions span:hover) {
  color: rgba(0, 0, 0, 0.45);
}
:deep(.anticon-edit:hover) {
  color: rgba(6, 21, 51, 0.65) !important;
}
:deep(.anticon-delete:hover) {
  color: rgba(6, 21, 51, 0.65) !important;
}
</style>
