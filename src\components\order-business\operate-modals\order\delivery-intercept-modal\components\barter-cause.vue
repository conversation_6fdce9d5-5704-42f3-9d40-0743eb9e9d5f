<template>
  <a-form
    ref="formRef"
    layout="vertical"
    :model="formState"
  >
    <a-form-item
      label="选择更换货品"
      required
    >
      <div
        class="flex-wrap"
        style="flex-wrap: wrap; padding-top: 8px"
      >
        <!-- 型号  -->
        <a-select
          v-model:value="formState.modalId"
          :disabled="!isSuper"
          :options="modalOptions"
          placeholder="请选择型号"
          style="width: 190px"
          @change="onChangeModal"
        />
        <!-- sku -->
        <a-select
          v-for="(item, index) in attrList"
          :key="item.id"
          v-model:value="item.value"
          :options="item.options"
          :placeholder="`请选择${item.name}`"
          :style="{
            width: '190px',
            marginTop: `${index > 1 ? '8px' : 0}`,
            marginLeft: `${[2, 5].includes(index) ? 0 : '8px'}`,
          }"
        />
      </div>
    </a-form-item>

    <a-form-item>
      <a-button
        :disabled="searchStockState.disabled"
        :loading="searchStockState.lodaing"
        style="margin-right: 12px"
        type="primary"
        @click="handleSearchStock"
      >
        确认并查询库存
      </a-button>
      <a-tag :color="searchStockState.isQueryStock ? 'green' : 'red'">
        {{ searchStockState.isQueryStock ? '已查询' : '未查询' }}
      </a-tag>
    </a-form-item>

    <!-- 设备情况  -->
    <a-tabs
      v-if="initData?.device_auth"
      v-model:activeKey="formState.activate_only"
      class="clear-tab"
      @change="handleSearchStock"
    >
      <a-tab-pane
        :key="false"
        tab="全新"
      />
      <a-tab-pane :key="true">
        <template #tab>
          <div class="flex-wrap flex-con flex-y-center">
            全新（仅激活）
            <img
              src="https://img1.rrzuji.cn/uploads/scheme/2412/07/m/VfiQCzPbFIAZ3gvqHGMo.png"
              style="width: 16px"
            >
          </div>
        </template>
      </a-tab-pane>
    </a-tabs>

    <!-- 新网商家才有的票据选择 -->
    <template v-if="initData?.is_new_online_server && !formState.isChecked">
      <a-radio-group
        v-model:value="bindInvoice"
        style="margin-bottom: 16px"
        @change="handleSearchStock"
      >
        <a-radio-button :value="2">
          有票
        </a-radio-button>
        <a-radio-button
          :disabled="initData.is_new"
          :value="3"
        >
          无票
        </a-radio-button>
      </a-radio-group>
    </template>

    <div class="flex-con flex-wrap">
      <a-radio-group
        v-model:value="formState.regulation"
        class="radio-group"
        :options="[
          { label: '租赁服务设备', value: true, disabled: isForbid },
          {
            label: '非租赁服务设备',
            value: false,
            disabled: formState.activate_only,
          },
        ]"
        @change="handleSupportChange"
      />

      <!--   预估价格   -->
      <div
        class="status-wrap"
        style="margin-right: 16px"
      >
        <div
          v-for="(item, key) in warehouseInformation"
          :key="key"
          class="status-item alert-text flex-wrap flex-y-center flex-gap-4"
        >
          <template v-if="item.showPrice && !formState?.isChecked && item.inventoryStatus">
            <span>预估价：￥{{ item?.totalPrice }}</span>
            <a-popover>
              <template #content>
                <div class="flex-wrap flex-vertical flex-gap-8">
                  <div class="flex-wrap flex-y-center flex-gap-8">
                    <InfoCircleOutlined />
                    <span>实际价格按设备下发日期的价格为准</span>
                  </div>
                  <div class="flex-wrap flex-y-center flex-gap-8 alert-text">
                    <div>预估价<br>({{ item?.totalPrice }})</div>
                    <div>=</div>
                    <a-space>
                      <template #split>
                        <span>+</span>
                      </template>
                      <template v-if="item.deviceMoney !== false">
                        <div>设备价<br>({{ item.deviceMoney }})</div>
                      </template>
                      <template v-if="item.inspectMoney !== false && !item.isNew">
                        <div>检测价<br>({{ item.inspectMoney }})</div>
                      </template>
                      <template v-if="item.sendMoney !== false">
                        <div>发货费<br>({{ item.sendMoney }})</div>
                      </template>
                      <template v-if="item.accessoryMoney !== false">
                        <div>配件费<br>({{ item.accessoryMoney }}{{ item.isNew ? `，线下结算` : '' }})</div>
                      </template>
                      <template v-if="item.lockMoney !== false">
                        <div>租赁服务费<br>({{ item.lockMoney }})</div>
                      </template>
                    </a-space>
                  </div>
                </div>
              </template>
              <InfoCircleOutlined
                class="cursor"
                style="font-size: 12px"
              />
            </a-popover>
          </template>
          <template v-else-if="formState.isChecked">
            <span>无需采购</span>
          </template>
          <template v-else>
            <span>预估价：暂无</span>
          </template>
        </div>
      </div>

      <!--   库存情况   -->
      <div class="status-wrap">
        <div class="status-tag">
          <a-tag :color="regulatoryDeviceStatusTag.color">
            <SyncOutlined :spin="searchStockState.lodaing" />
            {{ regulatoryDeviceStatusTag.txt }}
          </a-tag>
        </div>

        <div class="status-tag">
          <a-tag :color="nonRegulatoryDeviceStatusTag.color">
            <SyncOutlined :spin="searchStockState.lodaing" />
            {{ nonRegulatoryDeviceStatusTag.txt }}
          </a-tag>
        </div>
      </div>
    </div>
    <!-- 是否送检 -->
    <div
      v-if="initData?.is_checked"
      class="inspect-checkbox"
    >
      <a-checkbox
        v-model:checked="formState.isChecked"
        @change="handleCheckedChange"
      >
        送检设备（选择后无需采购）
      </a-checkbox>
    </div>

    <!--   换货建议  -->
    <ReplacementProposalTable
      v-if="formState.stockStatus === EStockStatus.NotEnough && (exchangeTableData.length || upgradeTableData.length)"
      :checked="formState.isChecked"
      :exchange-table-data="exchangeTableData"
      :is-support="formState.regulation"
      :is-upgrade="initData?.storage_memory_up_model"
      style="margin-top: 10px"
      :upgrade-table-data="upgradeTableData"
      @affirm-barter="(sku_id: string, warehouse_id: string) => affirmBarter(sku_id, warehouse_id, 'exchange')"
      @upgrade-barter="(sku_id: string, warehouse_id: string) => affirmBarter(sku_id, warehouse_id, 'upgrade')"
    />

    <!-- 采购余额展示 -->
    <div
      class="flex-wrap flex-y-center flex-gap-8"
      style="margin-top: 40px"
    >
      <template v-if="!showSelectControls">
        <span class="bold">项目采购余额：<span class="fw-500">选择设备信息后可见</span></span>
      </template>
      <template v-else-if="!accountAmount">
        <span class="bold">项目采购余额：
          <a-spin
            size="small"
            :spinning="true"
          />
        </span>
      </template>
      <template v-else-if="!accountAmount.canShow">
        <span class="bold">项目采购余额：<span class="fw-500">线下结算</span></span>
      </template>
      <template v-else>
        <span class="bold">项目采购余额：<span class="fw-500">{{ accountAmount.amount }}</span></span>
        <span
          class="action-icon bold"
          @click="getPriceInfo"
        >
          <SyncOutlined :spin="accountState.loading" />
        </span>
        <a-button
          v-if="!isSuper"
          class="line-btn"
          style="margin-left: 8px"
          @click="goRechargeRoute"
        >
          充值
        </a-button>
      </template>
    </div>
  </a-form>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Form, message } from 'ant-design-vue';
import { InfoCircleOutlined, SyncOutlined } from '@ant-design/icons-vue';
import { useCompRef } from 'rrz-web-design';

import ReplacementProposalTable from '@/components/order-business/operate-modals/order/go-ship-modal/grayscale-deliver-modal/components/platform-shipment/components/replacement-proposal-table.vue';
import useRepTable from '@/components/order-business/operate-modals/order/go-ship-modal/grayscale-deliver-modal/components/platform-shipment/composables/use-rep-table';
import { useVModel } from '@/hook/use-vmodel/index';

import { EStockStatus } from '../../exchange-apply-modal/composables/use-check-stock';
import { IAttrItem } from '../../exchange-apply-modal/data';
import { getSkuInfo, skuSearchBySkuId } from '../../exchange-apply-modal/services';
import { IAcountAmount } from '../../go-ship-modal/data';
import { useAdviceParams } from '../../go-ship-modal/grayscale-deliver-modal/components/platform-shipment/composables/use-exchange-advice';
import useBarter from '../composables/use-barter';
import useSearchStock from '../composables/use-search-stock';
import { getRole } from '../config';

const isSuper = getRole() === 'super';
const props = defineProps({
  orderId: {
    type: String,
    default: '',
  },
  serverId: {
    type: Number,
    default: 0,
  },
  invoice: {
    type: Number,
    default: 0,
  },
});
const emits = defineEmits(['submit', 'update:invoice']);

const route = useRoute();

const attrValueMap = new Map();

const formRef = useCompRef(Form);

const formState = ref({
  modalId: null,
  stockStatus: EStockStatus.Undefined,
  skuId: '',
  activate_only: false,
  isChecked: false,
  regulation: false,
  server_id: props.serverId,
  c_type: 1, // 埋点相关，如果是走的换货建议，那么就是2
});

/** sku列表选择信息 */
const attrList = ref<IAttrItem[]>([]);

//换货/补贴升级下发仓库id
const exchange_warehouse_id = ref(0);

const bindInvoice = useVModel(props, 'invoice', emits);

const { exchangeTableData, upgradeTableData, getTableData } = useRepTable();
const { modalOptions, initData } = useBarter(props.orderId, isSuper);
const {
  isForbid,
  searchStockState,
  warehouseInformation,
  accountState,
  handleSearchStock,
  handleWarehouseBaseParams,
  getPriceInfo,
} = useSearchStock({
  formState,
  attrList,
  initData,
  getTableData,
  invoice: bindInvoice,
});

watch(
  () => initData.value?.sku_id,
  async val => {
    if (val) {
      await handleGetAttrValue(val);
      // 自动查一次库存
      handleSearchStock();
    }
  },
  {
    deep: true,
  },
);

const regulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.regulatoryDeviceInformation?.inventoryStatus;
  return getRenderData(inventoryStatus);
});

const nonRegulatoryDeviceStatusTag = computed(() => {
  const inventoryStatus = warehouseInformation.value.nonRegulatoryDeviceInformation?.inventoryStatus;
  return getRenderData(inventoryStatus);
});

const showSelectControls = computed(() => {
  return !!(initData.value?.sku_id && formState.value.skuId);
});

// 当前项目余额
const accountAmount = computed<IAcountAmount>(() => {
  const { regulation } = formState.value;
  return accountState.value.amount?.[regulation ? 'lock' : 'unlock'];
});

function handleSupportChange(ev: any) {
  const { value: currVal } = ev.target;
  const currKey = currVal ? 'regulatoryDeviceInformation' : 'nonRegulatoryDeviceInformation';
  const currInventoryStatus = warehouseInformation.value[currKey]?.inventoryStatus;
  if (currInventoryStatus) {
    formState.value.stockStatus = EStockStatus.Enough; // 充足状态
  } else {
    formState.value.stockStatus = EStockStatus.NotEnough; // 缺货状态
    // 请求换货推荐
    const skuId = formState.value.skuId;
    if (!skuId) return;
    const params = handleWarehouseBaseParams(skuId);
    const exchangeParams = useAdviceParams(params, currKey === 'regulatoryDeviceInformation');
    const upgradeParams = useAdviceParams(params, currKey === 'regulatoryDeviceInformation', {
      get_other_sku_type: 2,
      skip_server_config: initData.value?.storage_memory_up_auth ? undefined : 1,
    });
    getTableData(exchangeParams, upgradeParams, initData.value?.storage_memory_up_model);
  }
}

async function handleGetAttrValue(sku_id: string) {
  const {
    data: { model_id, sku_val_arr },
  } = await skuSearchBySkuId({ sku_id });
  formState.value.modalId = model_id;
  sku_val_arr.forEach((it: { attr_val_id: number; attr_id: number }) => {
    attrValueMap.set(it.attr_id, it.attr_val_id);
  });
  await getSkuOptions();
}

/** 获取sku列表选择信息 */
async function getSkuOptions() {
  const { pdm_category_id, pdm_brand_id } = initData.value;
  const response = await getSkuInfo({
    category_id: Number(pdm_category_id),
    brand_id: Number(pdm_brand_id),
    model_id: formState.value.modalId,
  });
  attrList.value =
    response.data?.attr_list?.map((item: any) => {
      const { id, name, attr_items } = item;
      return {
        id,
        name,
        value: attrValueMap.get(id) || null,
        options: attr_items.map((sku: any) => ({ label: sku.name, value: sku.id })),
      };
    }) || [];
}

function getRenderData(inventoryStatus?: boolean | undefined) {
  if (typeof inventoryStatus === 'boolean') {
    return {
      txt: inventoryStatus ? '库存充足' : '缺货',
      color: inventoryStatus ? 'success' : 'error',
      disabled: !inventoryStatus,
    };
  }

  return {
    txt: '暂无数据',
    color: 'default',
    disabled: !inventoryStatus,
  };
}

function goRechargeRoute() {
  const { account_id, account_type } = accountAmount.value;
  window.open(
    `${route.query.origin}/supply-chain-account/index?action=goRecharge&type=${account_type}&id=${account_id}`,
    '_blank',
  );
}

function onChangeModal() {
  attrValueMap.clear();
  getSkuOptions();
}

async function affirmBarter(sku_id: string, warehouse_id: number, type: 'exchange' | 'upgrade') {
  formState.value.skuId = sku_id;
  formState.value.stockStatus = 1;
  formState.value.c_type = type === 'exchange' ? 2 : 21;
  exchange_warehouse_id.value = warehouse_id;
  emits('submit');
}

const commit = () => {
  return new Promise(resolve => {
    formRef.value.validate().then(() => {
      const currKey = formState.value.regulation ? 'regulatoryDeviceInformation' : 'nonRegulatoryDeviceInformation';
      const { skuId, stockStatus, regulation, activate_only, isChecked, c_type } = formState.value;
      // 先判断是否缺货，缺货不能提交
      if (stockStatus !== EStockStatus.Enough) {
        message.warning('库存不足，无法提交申请');
        return;
      }

      const params = {
        c_type,
        new_sku_id: skuId,
        old_sku_id: initData.value?.sku_id,
        is_support: regulation ? 1 : 0,
        is_activate_only: activate_only ? 1 : 0,
        is_checked: isChecked ? 1 : 0,
        warehouse_id: warehouseInformation.value[currKey].warehouse_id || exchange_warehouse_id.value,
      };

      if (c_type === 21) {
        params.storage_memory_up = 1;
      }

      resolve(params);
    });
  });
};

function getIsNewOnlineServer() {
  return initData.value?.is_new_online_server;
}

function handleCheckedChange(e) {
  const val = e.target.checked;
  bindInvoice.value = val ? 0 : 2;
  handleSearchStock();
}

defineExpose({
  commit,
  getIsNewOnlineServer,
});
</script>

<style lang="less" scoped>
.clear-tab {
  :deep(.ant-tabs-nav) {
    margin-bottom: 24px;
  }
  :deep(.ant-tabs-tab) {
    padding-top: 0;
  }
}

.radio-group {
  position: relative;
  display: flex;
  flex-flow: column wrap;
  gap: 24px;
  padding-left: 8px;

  :deep(.ant-radio-wrapper),
  :deep(.ant-checkbox-wrapper) {
    position: relative;
    gap: 8px;

    & > span.ant-radio + *,
    & > span.ant-checkbox + * {
      box-sizing: border-box;
      min-width: 130px;
      padding: 5px 0;
      color: rgba(6, 21, 51, 0.85);
      text-align: center;
      background-color: #f5f7fa;
      border: 1px solid #f5f7fa;
      border-radius: 4px;
    }

    &:not(:has(.ant-radio-disabled)) > span.ant-radio + *:hover,
    &:not(:has(.ant-checkbox-disabled)) > span.ant-checkbox + *:hover {
      border-color: var(--ant-primary-color);
      transition: border-color 0.3s linear;
    }

    &:not(:first-child)::before {
      position: absolute;
      top: -29px;
      left: 8px;
      width: 1px;
      height: 34px;
      background-color: rgba(6, 21, 51, 0.06);
      content: '';
    }

    &.ant-radio-wrapper-checked,
    &.ant-checkbox-wrapper-checked {
      & > span.ant-radio + *,
      & > span.ant-checkbox + * {
        color: var(--ant-primary-color);
        background-color: var(--ant-primary-1);
        border-color: var(--ant-primary-color);
        transition: border-color 0.3s linear;
      }

      &::before {
        background-color: var(--ant-primary-color);
      }
    }

    &.ant-radio-wrapper-disabled > .ant-radio-disabled + span,
    &.ant-checkbox-wrapper-disabled > .ant-checkbox-disabled + span {
      color: rgba(0, 0, 0, 0.25);
      background-color: #f5f7fa;
      border-color: #f5f7fa;
      cursor: not-allowed;
    }
  }
}

.status-wrap {
  display: flex;
  flex-flow: column wrap;
  gap: 24px;

  .status-item,
  .status-tag {
    height: 34px;
    line-height: 34px;
  }
}

.inspect-checkbox {
  padding: 16px 0 0 8px;
}

.alert-text {
  color: var(--ant-error-color);
}

.action-icon {
  color: var(--ant-primary-color);
  cursor: pointer;
}

.line-btn {
  color: var(--ant-primary-color);
  border-color: var(--ant-primary-color);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:hover,
  &:active,
  &:focus,
  &:visited {
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    background: var(--ant-primary-color);
    border-color: var(--ant-primary-color);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }
}

.cursor {
  cursor: pointer;
}

.fw-500 {
  font-weight: 500;
}

.bold {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
}
</style>
