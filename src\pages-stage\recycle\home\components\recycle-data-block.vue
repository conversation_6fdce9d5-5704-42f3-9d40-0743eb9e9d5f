<template>
  <a-spin
    :spinning="loading"
    tip="Loading..."
  >
    <card-box
      page-url="/super/recycle/data-count/daily-page"
      title="回收情况"
    >
      <flex-container
        :columns-number="3"
        :gap="8"
      >
        <card-data-item
          title="回收成交订单"
          :title-style="itemTitleStyle"
          unit="元"
          :value="chartInfo.finish_order_price"
          :value-style="itemValueStyle"
          :value-suffix="`/${chartInfo.finish_order_num}单`"
        />
        <card-data-item
          title="回收入库订单"
          :title-style="itemTitleStyle"
          unit="元"
          :value="chartInfo.in_warehouse_price"
          :value-style="itemValueStyle"
          :value-suffix="`/${chartInfo.in_warehouse_num}单`"
        />
        <card-data-item
          title="回收退货订单"
          :title-style="itemTitleStyle"
          unit="元"
          :value="chartInfo.return_price"
          :value-style="itemValueStyle"
          :value-suffix="`/${chartInfo.return_num}单`"
        />
        <template #body>
          <a-select
            v-model:value="typeValue"
            :options="typeOptions"
            style="width: 180px; margin-top: 8px"
            @change="createCharts"
          />
          <div
            v-show="!isEmpty"
            id="recycle_dataConatiner"
          />
          <EmptyBlock
            v-show="isEmpty"
            class="empty-box"
          />
        </template>
      </flex-container>
    </card-box>
  </a-spin>
</template>

<script lang="ts" setup>
import { IRecycleData } from '../data.d';
import useThetaData from '../composables/use-theta-data';
import FlexContainer from './flex-container.vue';
import CardBox from './card-box.vue';
import CardDataItem from './card-data-item.vue';
import EmptyBlock from './empty-block.vue';

const {
  itemTitleStyle,
  itemValueStyle,
  typeOptions,
  typeValue,
  chartInfo,
  createCharts,
  loading,
  isEmpty,
} = useThetaData<IRecycleData>({
  element: 'recycle_dataConatiner',
  url: '/super/recycle/data-count/order-detail-data',
  totalText: '今日回收总额',
  totalField: 'finish_order_price',
});
</script>

<style lang="less" scoped>
.containerCss() {
  width: 100%;
  height: 320px;
  margin: 0 auto;
  margin-top: 24px;
}
#recycle_dataConatiner {
  .containerCss();
}
.empty-box {
  .containerCss();
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
