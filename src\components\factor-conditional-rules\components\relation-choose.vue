<template>
  <div class="logic-line">
    <div
      class="logic-label"
      @click="toggleLogic"
    >
      {{ logicMaps[innerValue] }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const emits = defineEmits(['update:value']);
const props = defineProps<{
  value: string;
}>();

const logicMaps = {
  or: '或',
  and: '且',
};

const innerValue = computed({
  get: () => props.value,
  set: value => emits('update:value', value),
});

function toggleLogic() {
  innerValue.value = innerValue.value === 'or' ? 'and' : 'or';
}
</script>

<style lang="less" scoped>
.logic-line {
  position: absolute;
  z-index: 999;
  height: 100%;
  margin: 0 4px;
}

.logic-line::before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 10px;
  width: 2px;
  background-color: #00c8be;
  opacity: 0.2;
  content: '';
}

.logic-label {
  position: absolute;
  top: 50%;
  width: 22px;
  color: #00c8be;
  text-align: center;
  background-color: #e5f9f8;
  border-radius: 4px;
  transform: translateY(-50%);
  cursor: pointer;
  user-select: none;
}
</style>
