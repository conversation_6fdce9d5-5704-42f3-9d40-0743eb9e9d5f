import { GET,POST } from '@/services/api';
//商家后台
// 消息渠道-列表
export function channelList() {
  return GET('/server-message-channel/list');
}

//消息渠道-更新状态
export function channelUpdateStatus(params:any) {
  return POST('/server-message-channel/update-status',params);
}

//消息渠道-联系人列表
export function channelContactList(channel_id:string) {
  return GET(`/server-message-channel/contact-list?channel_id=${channel_id}`);
}
//消息渠道-新增联系人
export function channelContactAdd(params:any) {
  return POST('/server-message-channel/contact-add',params);
}
//消息渠道-删除联系人
export function channelContactDel(params:any) {
  return POST('/server-message-channel/contact-delete',params);
}
//发送手机验证码
export function messageTepSend(params:any) {
  return POST('/server-message-template/send-code',params);
}
