import { ref } from 'vue';
import { IComputedTableDataSource } from '../data.d';

export default function () {
  const skuTableData = ref<IComputedTableDataSource[]>([]);

  const defaultItem: IComputedTableDataSource = {
    base1: null,
    base2: null,
    price_type: null,
    change_price_type: null,
    arithmetic_type: null,
    sku_base_price: 0,
  };

  function delSkuTableItem(index: number) {
    skuTableData.value.splice(index, 1);
  }
  function addSkuTableItem() {
    skuTableData.value.push({ ...defaultItem });
  }

  return {
    skuTableData,
    delSkuTableItem,
    addSkuTableItem,
  };
}
