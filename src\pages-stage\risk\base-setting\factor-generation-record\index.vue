<template>
  <layout-admin-page
    :navs="['风控', '风控基础设置', '风控能力配置', '因子生成记录']"
    title="因子生成记录"
  >
    <template #title-prefix>
      <arrow-left-outlined
        style="margin-right: 16px; font-size: 16px"
        @click="router.go(-1)"
      />
    </template>
    <div class="search-form">
      <a-form
        ref="formRef"
        layout="inline"
        :model="formState"
        style="width: 100%"
      >
        <a-row style="width: 100%">
          <a-col :span="6">
            <a-form-item
              label="订单号"
              name="order_id"
            >
              <a-input
                v-model:value="formState.order_id"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              label="风控因子"
              name="factor_ids"
            >
              <a-select
                v-model:value="formState.factor_ids"
                :field-names="{ label: 'name', value: 'id' }"
                mode="multiple"
                :options="factorList"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item>
              <a-button
                style="border-radius: 4px"
                type="primary"
                @click="search"
              >
                查询
              </a-button>
              <a-button
                style="margin-left: 10px; border-radius: 4px"
                @click="reset"
              >
                重置
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="list-mian">
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="listLoading"
        :pagination="hasCondition ? false : page"
        :scroll="{ x: 1300 }"
        @change="tableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <a-space>
              <a
                class="operation"
                @click="handleDetail(record.id)"
              > 查看详情 </a>
            </a-space>
          </template>
          <template v-if="column.key === 'created_at'">
            {{ dayjs(record.created_at * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </template>
      </a-table>
      <!-- 页码 -->
      <ListPagination
        v-if="hasCondition"
        class="pagination"
        :next-disabled="nextDisabled"
        :pagination="bindPagination"
        :show-page-count="false"
        @change-page="onChangePage"
      />
    </div>
  </layout-admin-page>
  <RuleDetailsModal
    v-model:visible="detailVisible"
    :rule-id="currentId"
    @on-close="onClose"
  />
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { FormInstance } from 'ant-design-vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import type { IPagination } from '@/components/gather-search/src/data';
import { useTable } from '@/hook/component/use-table';

import ListPagination from './components/list-pagination.vue';
import RuleDetailsModal from './components/rule-details-modal/index.vue';
import { columns } from './config';
import { RiskControlFactorALl, RiskControlOrderQuery } from './service';

const router = useRouter();
const formRef = ref<FormInstance>();
const formState = ref({
  factor_ids: [],
  order_id: '',
});

const detailVisible = ref(false);
const factorList = ref([]);
const currentId = ref('');

const { list, listLoading, getTableList, tableChange, page } = useTable({
  api: RiskControlOrderQuery,
  pageKey: 'page_no',
  totalKey: 'data.total_count',
  searchForm: formState.value,
  formatSearchValue: () => {
    hasCondition.value = !!formState.value.order_id.trim() || !!formState.value.factor_ids.join(',');
    return {
      order_id: formState.value.order_id.trim(),
      factor_ids: formState.value.factor_ids.join(','),
    };
  },
  formatHandle: res => {
    return res.data.riskControlOrder_list;
  },
});

const hasCondition = ref(false);

const bindPagination = computed<IPagination>(() => ({
  current: page.current,
  pageSize: page.pageSize,
  total: hasCondition.value ? 9999 : page.total,
  type: 'simple',
}));

const onChangePage = (current: number) => {
  page.current = current;
  getTableList();
};

const nextDisabled = computed(() => {
  return hasCondition.value ? list.value?.length < page.pageSize : false;
});

const search = () => {
  getTableList('search');
};
const reset = () => {
  formRef.value?.resetFields();
  getTableList('search');
};
const handleDetail = (id: string) => {
  currentId.value = id;
  detailVisible.value = true;
};
const onClose = () => {
  detailVisible.value = false;
};
const getriskList = async () => {
  const res = await RiskControlFactorALl();
  factorList.value = res.data.factor_list;
};
onMounted(() => {
  getTableList();
  getriskList();
});
</script>

<style lang="less" scoped>
.search-form {
  margin-bottom: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
}

.list-mian {
  padding: 0 24px 24px;
  background-color: #fff;
}

.operation {
  color: #3777ff;
}
</style>
