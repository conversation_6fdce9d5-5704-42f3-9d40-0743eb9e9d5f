export interface AuthorityCenterRow {
  phone: string;
  name: string;
  account_id: number;
  auth_export: number;
  auth_search: number;
  created_at: string;
  created_at_text: string;
  id: number;
  server_id: number;
}

export interface Account {
  id?: string;
  name: string;
  phone: string;
  password: string;
  auth_search: 0 | 1;
  auth_export: 0 | 1;
  auth: Record<string, any>;
}

export interface ConfigurationItem {
  icon: string;
  module_id: number;
  module_name: string;
  module_cn_name: number;
  is_conf: boolean;
  config_at: number;
  is_open_module: number;
  function: [
    {
      func_id: number;
      func_name: string;
      func_cn_name: string;
      is_conf: boolean;
      config_at: number;
    },
  ];
}
