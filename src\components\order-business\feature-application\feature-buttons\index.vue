<template>
  <OperateButtonGroup
    :button-data="orderData.button_list || []"
    :button-group="infoButtonGroup"
    class="button-group"
    :modal-attrs="{ orderId }"
  />
</template>
<script lang="ts" setup>
import { computed, createVNode, h, ref, toRef } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import { OperateButtonGroup } from '@/components/order-business/feature-application';
import { operateModal } from '@/components/order-business/operate-modals';
import {
  confirmNotarizeNotice,
  confirmWhitePingShan,
} from '@/components/order-business/operate-modals/order/launch-notarization-modal/config.tsx';
import type { IButtonGroupItem, IOrderItem, IStringOption } from '@/components/order-business/typing';
import { useUnityModal } from '@/components/unity-modal';
import { useTopWindow } from '@/hook/common/use-top-window';
import { ETicketStatus } from '@/typing';

import LogisticsChangeButton from './components/logistics-change-button.vue';
import { useGenBill } from './composables/use-gen-bill';
import { useWithHold } from './composables/use-with-hold';
import { expediteRiskOrder, findMDMAffiliatedShop, getScmAuthInfo, isCanApplySend } from './services';

const props = defineProps<{
  orderData: IOrderItem;
  /** 补订单-用途下拉选择项 */
  replenishmentOptions?: IStringOption[];
}>();

const orderId = toRef(() => props.orderData.base_info?.order_id || '');

const route = useRoute();
const domain = route.query.origin || window.location.origin;
const isSuper = route.query.role === 'super';
const urgentRisk = ref('加急风控');

const { goGebBill } = useGenBill(orderId.value);
const { checkWithHold, loading: withHoldLoading } = useWithHold(orderId.value);
const { onToggleComponent } = useUnityModal();

const GREEN_STYLE = { 'background-color': '#00c8be', 'color': '#fff' };
const ORIGIN_STYLE = { 'background-color': '#FEB043', 'color': '#fff' };
const RED_STYLE = { 'background-color': '#FF7D73', 'color': '#fff' };
const BLUE_STYLE = { 'background-color': '#58A9FF', 'color': '#fff' };

const logisticsStatusMap = { 0: '待处理', 1: '处理中', 2: '已完成', 3: '已取消', 4: '已撤销' };

const infoButtonGroup = computed<IButtonGroupItem[]>(() => [
  {
    buttonCom: operateModal.interWorkOrder,
    label: extra => {
      return Number(extra?.work_order_inter_status) === 4 ? '取消工单驳回详情' : '取消工单详情';
    },
    buttonAttrs: (extra: any) => ({
      //工单状态为驳回时需提醒;(work_order_inter_status：4)
      style: ORIGIN_STYLE,
      onClick: () => {
        const { inter_work_order_id, key } = extra;
        const superUrl = !!Number(key)
          ? `/super/work-order-inter/my-handle&pageType=2&currentKey=${key}`
          : '/super/work-order-inter/index';
        const url = isSuper
          ? `/super/new-work-order/index?url=${superUrl}&detailId=${inter_work_order_id}`
          : `/work-order-inter/detail-index?id=${inter_work_order_id}&key=${key}&isFromeOrder=${key}`;
        window.open(`${domain}${url}`);
      },
    }),
  },
  {
    buttonCom: operateModal.cityMonitor,
  },
  {
    buttonCom: operateModal.interceptDetails,
    label: extra => {
      const name = extra?.change_type === 1 ? '物流拦截' : '地址变更';
      const status = logisticsStatusMap[extra?.status];
      return `${name}(${status})`;
    },
    buttonAttrs: { style: ORIGIN_STYLE },
  },
  {
    buttonCom: operateModal.urgentSend,
    buttonAttrs: { style: RED_STYLE },
  },
  {
    buttonCom: operateModal.orderTicket,
    label: (extra: any) => {
      if (extra?.status === ETicketStatus.WaitUpload) {
        return '演唱会门票：待上传';
      } else {
        return `演唱会门票：【${extra?.order_sort}】查看`;
      }
    },
    buttonAttrs: { style: RED_STYLE },
  },
  {
    buttonCom: operateModal.modifyPrice,
    buttonAttrs: { style: GREEN_STYLE },
  },
  {
    buttonCom: operateModal.suppleOrder,
    modalAttrs: {
      replenishmentOptions: toRef(() => props.replenishmentOptions),
    },
  },
  {
    buttonCom: operateModal.sendMessage,
    label: extra => extra?.text || '发送短信',
    buttonAttrs: { style: BLUE_STYLE },
  },
  // 补押金
  {
    buttonCom: operateModal.makeUpDeposit,
  },
  //  补充评估
  {
    buttonCom: operateModal.assessment,
    modalAttrs: {
      userId: props.orderData.base_info?.user_id,
    },
  },
  {
    buttonCom: operateModal.urgentRisk,
    label: () => urgentRisk.value,
    buttonAttrs: {
      style: ORIGIN_STYLE,
      onClick: () => {
        Modal.confirm({
          title: '加急风控',
          icon: createVNode(ExclamationCircleOutlined),
          content: createVNode(
            'div',
            { style: 'color:red;' },
            '是否需要风控人员优先风控此订单？此次加急记录会被记录，请按需使用',
          ),
          onOk: () => {
            expediteRiskOrder({ orderId: orderId.value }).then(() => {
              message.success('风控加急成功');
              urgentRisk.value = '已加急风控';
            });
          },
        });
      },
    },
  },
  {
    buttonCom: operateModal.changeRental,
  },
  {
    buttonCom: operateModal.reFreezeDeposit,
  },
  {
    buttonCom: operateModal.launchNotarization,
    buttonAttrs: {
      style: BLUE_STYLE,
      onClick: async extra => {
        const is_read = await confirmNotarizeNotice();
        if (!is_read) return;
        const isAuth = await confirmWhitePingShan();
        if (!isAuth) return;
        onToggleComponent(operateModal.launchNotarization, {
          orderId: orderId.value,
          extraData: extra,
        });
      },
    },
  },
  {
    buttonCom: operateModal.uploadDeviceImei,
    buttonAttrs: extra => ({
      style: BLUE_STYLE,
      disabled: extra?.disabled,
      onClick: async () => {
        if (
          props.orderData.device_info?.warehouse_deliver_imei?.type === 'drop_shipping_delivery_order' &&
          props.orderData.device_info?.warehouse_deliver_imei.imei?.length
        ) {
          message.error('当前订单已绑定平台代发设备，请联系平台客服解除绑定再操作');
          return;
        }
        const {
          base_info: { server_id, order_status },
          device_info: { warehouse_deliver_imei },
          spu_info: { is_old_mobile_phone },
        } = props.orderData;
        const { data: scmData } = await getScmAuthInfo(orderId.value);
        const { scm_auth, is_enterprise_sales, warehouse_list, is_scm_order } = scmData;
        findMDMAffiliatedShop(server_id).then(({ data }) => {
          onToggleComponent(operateModal.uploadDeviceImei, {
            orderId: orderId.value,
            orderStatus: order_status,
            deviceNum: props.orderData.base_info?.quantity,
            isSalesOrder: props.orderData.base_info?.is_sales_order,
            isBind: data.is_bind,
            imei: warehouse_deliver_imei?.imei || [],
            isOldMobilePhone: is_old_mobile_phone,
            scmAuth: scm_auth,
            isEnterpriseSales: is_enterprise_sales,
            warehouseList: warehouse_list,
            isScmOrder: is_scm_order,
          });
        });
      },
    }),
  },
  {
    buttonCom: operateModal.deviceCode,
    buttonAttrs: {
      style: BLUE_STYLE,
      onClick: () => {
        if (
          props.orderData.device_info?.warehouse_deliver_imei?.type === 'drop_shipping_delivery_order' &&
          props.orderData.device_info?.warehouse_deliver_imei.imei?.length
        ) {
          message.error('当前订单已绑定平台代发设备，请联系平台客服解除绑定再操作');
          return;
        }
        onToggleComponent(operateModal.deviceCode, { orderId: orderId.value });
      },
    },
  },
  {
    buttonCom: operateModal.deliveryRemind,
    buttonAttrs: (extra?: { remind_send_status?: number }) => ({
      style: extra?.remind_send_status === 1 ? BLUE_STYLE : { backgroundColor: '#e4dfdf8a' },
    }),
  },
  {
    buttonCom: operateModal.changeTerm,
  },
  {
    buttonCom: operateModal.viewReturnShip,
    buttonAttrs: {
      style: BLUE_STYLE,
      onClick: (extra: any) => {
        if (isSuper) {
          window.open(`${domain}/super/return/view-v2?order_id=${orderId.value}&item_id=${extra?.item_id}`);
        } else {
          window.open(`${domain}/return/view-v2?order_id=${orderId.value}&item_id=${extra?.item_id}`);
        }
      },
    },
  },
  {
    buttonCom: operateModal.continueTerm,
  },
  {
    buttonCom: operateModal.continueTermApply,
    buttonAttrs: { style: RED_STYLE },
  },
  {
    buttonCom: operateModal.refundApply,
  },
  {
    buttonCom: operateModal.preAuthInfo,
    buttonAttrs: { style: BLUE_STYLE },
  },
  {
    buttonCom: operateModal.expressInfo,
    label: extra => {
      if (extra?.is_wait_pay) {
        return extra?.express_type === 1 ? '顺丰运输账单待支付' : '京东运输账单待支付';
      } else {
        return extra?.express_type === 1 ? '顺丰运输服务详情' : '京东运输服务详情';
      }
    },
    buttonAttrs: extra => ({
      style: extra?.is_wait_pay ? RED_STYLE : BLUE_STYLE,
      onClick: () => {
        onToggleComponent(operateModal.expressInfo, {
          extraData: extra,
          orderId: orderId.value,
          orderStatus: props.orderData.base_info?.order_status,
        });
      },
    }),
  },
  {
    buttonCom: operateModal.shanSongServiceDetails,
    label: '闪送运输服务详情',
    buttonAttrs: extra => ({
      style: BLUE_STYLE,
      onClick: () => {
        onToggleComponent(operateModal.shanSongServiceDetails, {
          extraData: extra,
          orderId: orderId.value,
          orderStatus: props.orderData.base_info?.order_status,
          serverId: props.orderData.base_info?.server_id,
        });
      },
    }),
  },
  {
    buttonCom: operateModal.payFromDeposit,
  },
  {
    buttonCom: operateModal.reFreezeUnfinish,
    buttonAttrs: { danger: true },
  },
  {
    buttonCom: operateModal.changeOverdueRent,
  },
  {
    buttonCom: operateModal.subAccountFailInfo,
    buttonAttrs: {
      style: RED_STYLE,
      href: `${domain}/super/v2-order/v2-separate-account?order_id=${orderId.value}`,
      target: '_blank',
    },
  },
  {
    buttonCom: operateModal.withHold,
    buttonAttrs: {
      style: GREEN_STYLE,
      loading: withHoldLoading,
      onClick: () => {
        // 仅运营人员可以查看代扣信息
        if (isSuper) {
          checkWithHold();
        } else {
          message.success('该订单已授权代扣');
        }
      },
    },
  },
  {
    buttonCom: operateModal.genBill,
    buttonAttrs: {
      onClick: goGebBill,
    },
  },
  {
    buttonCom: operateModal.refundNote,
    label: extra => renderSalesBtn('退货售后单', extra?.returns_num),
    buttonAttrs: {
      onClick: (extra: any) => {
        if (isSuper) {
          superAfterSalesJump('return', extra?.returns_work_no);
        } else {
          afterSalesJump('search', extra?.returns_work_no);
        }
      },
    },
  },
  {
    buttonCom: operateModal.exchangeNote,
    label: extra => renderSalesBtn('换货售后单', extra?.exchange_num),
    buttonAttrs: {
      onClick: (extra: any) => {
        if (isSuper) {
          superAfterSalesJump('exchange', extra?.exchange_work_no);
        } else {
          afterSalesJump('search', extra?.exchange_work_no);
        }
      },
    },
  },
  {
    buttonCom: operateModal.afterSaleOrder,
    label: (extra: any) => {
      return extra.text || '企销售后单';
    },
    buttonAttrs: {
      onClick: (extra: any) => {
        const isSuper = route.query.role === 'super';
        if (isSuper) {
          // 后台跳转
          window.open(`${domain}/super/salesAfter/wo/index?order_id=${extra.order_id}`, '_blank');
        } else {
          window.open(`${domain}/salesAfter/wo/index?order_id=${extra.order_id}`, '_blank');
        }
      },
    },
  },
  {
    buttonCom: operateModal.afterSaleWorkDataCanCreate,
    buttonAttrs: {
      style: BLUE_STYLE,
      onClick: () => {
        afterSalesJump('add', orderId.value);
      },
    },
  },
  {
    buttonCom: operateModal.interceptCreate,
    label: () => h(LogisticsChangeButton, { orderId: orderId.value }),
    buttonAttrs: {
      style: { padding: 0, ...ORIGIN_STYLE },
    },
  },
  {
    buttonCom: operateModal.uploadReturnDetectionBtn,
  },
  {
    buttonCom: operateModal.userCompensationBtn,
    buttonAttrs: {
      onClick: () => {
        onToggleComponent(operateModal.userCompensationBtn, {
          orderId: orderId.value,
          itemId: props.orderData?.spu_info?.code_url,
        });
      },
    },
  },
  {
    buttonCom: operateModal.changeDeductionDayBtn,
  },
  {
    buttonCom: operateModal.createNoRentBill,
  },
  {
    buttonCom: operateModal.matchRent,
  },
  {
    buttonCom: operateModal.changeSendWay,
    buttonAttrs: {
      onClick: async extra => {
        await isCanApplySend(orderId.value);
        onToggleComponent(operateModal.changeSendWay, {
          orderId: orderId.value,
          extraData: extra,
        });
      },
    },
  },
]);

/** 售后渲染按钮 */
function renderSalesBtn(label: string, count?: number) {
  // 提示数量的红点（仅运营可见）
  if (isSuper) {
    return h('div', { style: 'position: relative' }, [label, h('div', { class: 'after-sales-num' }, count)]);
  }
  return label;
}

const router = useRouter();

/** 跳转售后页面（运营端） */
function superAfterSalesJump(type: 'exchange' | 'return', id?: string | number) {
  // 因退货订单type存在多个值，且订单列表未返回type，这里暂时去除type参数，解决跳转筛选无数据问题
  const queryStr = type === 'exchange' ? `work_no=${id}&type=2` : `work_no=${id}`;
  if (process.env.NODE_ENV !== 'development') {
    const { navigate } = useTopWindow();
    navigate('blank', `/super/warehouse/after-sale-work/index?${queryStr}`);
    return;
  }
  router.push(`/order/delivery-sales/list?${queryStr}`);
}

/** 跳转售后页面（商家端） */
function afterSalesJump(type: 'add' | 'search', id: string | number) {
  const queryStr = type === 'add' ? `order_id=${id}` : `work_no=${id}`;
  if (process.env.NODE_ENV !== 'development') {
    const { navigate } = useTopWindow();
    navigate('blank', `/warehouse/after-sale-work/index?type=${type}&${queryStr}`);

    return;
  }
  router.push(`/admin-merchant/order/manage/after-sales-workbench?type=${type}&${queryStr}`);
}
</script>

<style lang="less" scoped>
.button-group {
  width: 100%;
  padding: 16px 12px;
  border-bottom: 1px solid #f0f1f3;

  :deep(.ant-btn:hover) {
    opacity: 0.7;
  }

  :deep(.ant-btn[disabled]) {
    opacity: 0.5;
  }

  :deep(.after-sales-num) {
    position: absolute;
    top: -12px;
    right: -24px;
    width: 20px;
    height: 20px;
    color: #fff;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    background: #ff4d4f;
    border-radius: 50%;
  }
}
</style>
