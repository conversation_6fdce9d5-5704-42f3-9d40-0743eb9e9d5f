import { ref } from 'vue';

import { createGlobalRequest, createGlobalState } from '@/hook';

import { ICalendar } from '../data';
import { getBusinessCalendar } from '../services';

const { getData, clearData } = createGlobalRequest(getBusinessCalendar);

export const useCalenderInfo = createGlobalState(() => {
  const calenderInfo = ref<ICalendar>();

  function getCalenderInfo() {
    getData().then(res => {
      calenderInfo.value = res?.data;
    });
  }

  function resetCalenderInfo() {
    clearData();
    getCalenderInfo();
  }

  return {
    calenderInfo,
    getCalenderInfo,
    resetCalenderInfo,
  };
});
