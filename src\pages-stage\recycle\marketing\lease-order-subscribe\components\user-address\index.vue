<template>
  <div class="user-address">
    <div
      class="row"
      style="margin-bottom: 4px"
    >
      <sensitive-field
        :id="orderId"
        ref="nameRef"
        :eye-btn="true"
        field="name"
        field-type="2"
        post-type="server"
        real-field="name"
        :row="sensitiveRow"
        :type="1001"
      />
    </div>
    <div class="row">
      <!-- 收货手机号 -->
      <sensitive-field
        :id="orderId"
        ref="phoneRef"
        :btn-show="receivingData.trip_type !== 1"
        :eye-btn="true"
        field="phone"
        field-type="1"
        post-type="server"
        real-field="phone"
        :row="sensitiveRow"
        :type="1001"
      />
    </div>
    <div class="row">
      <sensitive-field
        :id="orderId"
        ref="addressRef"
        :eye-btn="true"
        field="address"
        field-type="3"
        post-type="server"
        real-field="address"
        :row="sensitiveRow"
        :type="1001"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType,ref } from 'vue';

import { ReceivingDataType } from '../../data';

const props = defineProps({
  orderId: {
    type: String,
    required: true,
  },
  receivingData: {
    type: Object as PropType<ReceivingDataType>,
    required: true,
  },
});

const sensitiveRow = ref<ReceivingDataType>(props.receivingData);
</script>

<style lang="less" scoped>
.user-address {
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
</style>
