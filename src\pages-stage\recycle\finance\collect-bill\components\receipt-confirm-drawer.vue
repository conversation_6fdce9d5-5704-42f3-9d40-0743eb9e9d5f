<template>
  <a-drawer
    class="receipt-class"
    :closable="false"
    :footer-style="{ textAlign: 'right' }"
    title="线上收款确认"
    :visible="visible"
    width="1060px"
    @close="onCancel"
  >
    <template #extra>
      <close-outlined
        style="font-size: 16px; cursor: pointer"
        @click="onCancel"
      />
    </template>
    <a-spin :spinning="loading">
      <div class="receipt-top">
        <div class="top-block">
          <div>收款对象：</div>
          <div>收款金额合计：</div>
        </div>
        <div class="top-block">
          <div>{{ receiptObject.server_name || '-' }}</div>
          <a-statistic
            :precision="2"
            prefix="￥"
            :value="receiptObject.total_pay_price"
            :value-style="{ color: '#061533', fontSize: '16px' }"
          />
        </div>
      </div>
      <div class="receipt-container">
        <div class="title">
          收款源单明细
        </div>
        <div class="table">
          <a-table
            class="bottom-fix-table"
            :columns="receiptConfirmColumns"
            :data-source="dataSource"
            :pagination="false"
            :scroll="{ y: '100%' }"
            :sticky="true"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'order_pay_status_text'">
                <div class="status-block">
                  <div
                    class="point"
                    :style="{ background: record.order_pay_status_text === '待支付' ? '#FAAD14' : '#00C8BE' }"
                  />
                  {{ record.order_pay_status_text }}
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button @click="onCancel">
        取消
      </a-button>
      <a-button
        :loading="btnLoading"
        style="margin-left: 7px"
        type="primary"
        @click="onCollection"
      >
        生成收款码
      </a-button>
    </template>
  </a-drawer>
  <CollectionCodeModal
    v-if="codeModalVisible"
    v-model:isPushed="isPushed"
    v-model:visible="codeModalVisible"
    :pay-id="payId"
    :pay-link="payLink"
    @re-load="reLoad"
  />
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import { receiptConfirmColumns } from '../config';
import { IReceiptConfirmList, IReceiptConfirmObject } from '../data';
import { getSourceOrderList, createReceipt } from '../service';
import CollectionCodeModal from './collection-code-modal.vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderIds: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['update:visible', 'onLoadList']);

const loading = ref<boolean>(true);

const dataSource = ref<IReceiptConfirmList[]>();
const receiptObject = ref<IReceiptConfirmObject>({
  server_name: '',
  total_pay_price: '',
});

const sourceIds = ref<string[]>([]);

const [codeModalVisible, { setTrue: showCodeModal }] = useBoolean();

const getCollection = () => {
  getSourceOrderList({ order_ids: props.orderIds })
    .then(({ data }) => {
      dataSource.value = data.list ?? [];
      receiptObject.value.server_name = data.server_name ?? '';
      receiptObject.value.total_pay_price = data.total_pay_price ?? '';
    })
    .finally(() => {
      loading.value = false;
    });
};

const btnLoading = ref<boolean>(false);
const isGenerated = ref<boolean>(false); //是否点击生成收款码

const payId = ref<string>('');
const isPushed = ref<boolean>(false);
const payLink = ref<string>('');

const getQrCode = async () => {
  try {
    const { data } = await createReceipt({
      order_ids: props.orderIds,
      source_order_ids: sourceIds.value,
    });
    payId.value = data.pay_log_id;
    //1-不可收款推送 2-可推
    isPushed.value = data.has_send !== 2;
    payLink.value = data.pay_link;
  } finally {
    btnLoading.value = false;
  }
};

const onCollection = async () => {
  if (dataSource.value?.length) {
    isGenerated.value = true;
    sourceIds.value = dataSource.value.map(item => item.source_order_id);
    btnLoading.value = true;
    await getQrCode();
    showCodeModal();
  }
};

const onCancel = () => {
  emits('update:visible', false);
  if (isGenerated.value) {
    emits('onLoadList');
  }
};

const reLoad = () => {
  loading.value = true;
  getCollection();
};

onMounted(() => {
  getCollection();
});
</script>
<style scoped lang="less">
.receipt-top {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px 24px;
  background: rgba(6, 21, 51, 0.04);
  border-radius: 4px;
  .top-block {
    display: flex;
    flex-direction: column;
    gap: 16px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    &:nth-child(1) {
      color: rgba(6, 21, 51, 0.65);
      text-align: right;
    }
    &:nth-child(2) {
      color: #061533;
    }
  }
}

.receipt-container {
  .title {
    position: relative;
    display: flex;
    align-items: center;
    margin: 24px 0 16px 0;
    margin-right: 8px;
    padding-left: 12px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      background: #00c8be;
      border-radius: 2px;
      transform: translateY(-50%);
      content: '';
    }
  }
  .status-block {
    display: flex;
    align-items: center;
    .point {
      width: 8px;
      height: 8px;
      margin-right: 10px;
      border-radius: 8px;
    }
  }
}
</style>
