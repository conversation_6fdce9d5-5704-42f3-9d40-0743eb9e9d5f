<template>
  <layout-admin-page
    :navs="['数据', '数据查询', '考核结果查询']"
    title="考核结果查询"
  >
    <a-spin :spinning="table.loading">
      <div class="examination-result">
        <section class="examination-result-search">
          <a-form class="assess-board-form">
            <a-row :gutter="[24, 24]">
              <a-col>
                <a-form-item :colon="false">
                  <template #label>
                    <CalendarOutlined style="color: #656f81" />
                  </template>

                  <a-select
                    v-model:value="search.condition.date"
                    :options="search.optionMap.dateOptions"
                    placeholder="请选择"
                    style="width: 240px"
                    @change="handleSelectChange"
                  />

                  <span style="margin-left: 8px">常规考核结果</span>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </section>

        <a-divider style="margin-top: 0" />

        <div class="sub-title">
          考核周期：近9前7，即接单时间在{{ filterCycleTime }}的订单
        </div>

        <REmpty
          v-if="!isResult"
          style="margin-top: 16px"
        />

        <tabs
          v-else
          v-model:activeKey="tabsData.activeKey"
          :tabs-list="tabsData.tabsList"
          @load-list="handleSelectChange"
        />

        <a-divider />

        <div
          v-if="tabsData.activeKey !== 1 && tabsData.activeKey !== 3"
          class="tips"
        >
          <InfoCircleFilled class="tips-icon" />

          <span
            v-if="tabsData.activeKey === 0"
            class="tips-text"
          >计算公式：（满足常规考核和风控通过的订单）中，已发货订单/可发货订单=（风控通过）常规考核发货率</span>
          <span
            v-if="tabsData.activeKey === 2"
            class="tips-text"
          >发货前只收取首期租金即收取期数为1</span>
        </div>

        <div class="table-search">
          <div class="table-search-left">
            订单明细（结果有效统计时间截止{{ filterTime }} 00:00:00）
            <span
              v-if="tabsData.activeKey === 0"
              class="table-desc"
            >总计：计入常规考核的风控通过订单{{ orderNumData.normal_assess_num }}笔，其中{{
              filterTime
            }}日前已发货订单{{ orderNumData.delivery_num }}笔</span>
          </div>
          <a-input
            v-model:value="search.condition.order_id"
            placeholder="搜索订单号"
            style="width: 203px"
            @press-enter="handlePressEnter"
          >
            <template #suffix>
              <SearchOutlined style="color: rgba(6, 21, 51, 0.25)" />
            </template>
          </a-input>
        </div>

        <a-table
          class="examination-result-table"
          :columns="tableColumns"
          :data-source="table.list"
          :pagination="table.pagination"
          row-key="id"
          :scroll="{ x: 'max-content' }"
          @change="tableChange"
        >
          <template #bodyCell="{ text, column }">
            <template v-if="column.key === 'risk_qualitative'">
              {{ table.optionMap.risk_qualitative[text] || '--' }}
            </template>
            <template v-if="boolKeys.includes(column.key)">
              {{ text ? '是' : '否' }}
            </template>
            <template v-if="column.key === 'inspection_dimension'">
              {{ text === 1 ? '是' : '否' }}
            </template>
            <template v-if="timeKeys.includes(column.key)">
              {{ text ? dayjs.unix(text).format('YYYY-MM-DD HH:mm:ss') : '--' }}
            </template>
            <template v-if="column.key === 'reason'">
              {{ table.reasonObj[text] || '--' }}
            </template>
          </template>
        </a-table>
      </div>
    </a-spin>

    <template #extra>
      <a-button
        :disabled="search.optionMap.dateOptions.length === 0 || !isResult"
        :loading="exportLoading"
        style="margin-right: 8px"
        @click="handleExport"
      >
        <template #icon>
          <export-outlined class="export-icon" />
        </template>
        导出订单明细
      </a-button>
      <a-button
        :disabled="resultBtnDisabled"
        type="primary"
        @click="
          router.push(
            `/data-menu/inquire/appeal?pageType=${pageType}&distribute_id=${distribute_id}&filterTime=${filterTime}&filterCycleTime=${filterCycleTime}&appeal_id=${appeal_id}`,
          )
        "
      >
        {{ appeal_id === 0 ? '结果申诉' : '申诉记录' }}
      </a-button>
    </template>
  </layout-admin-page>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { PaginationProps } from 'ant-design-vue';
import { CalendarOutlined, ExportOutlined, InfoCircleFilled, SearchOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import Tabs from './components/tabs/index.vue';
import useSearch from './composables/use-search';
import useTable from './composables/use-table';
import useTabs from './composables/use-tabs';
import { ApiExport, ApiGetResult, ApiGetTabel } from './service';

const boolKeys = ['server_can_delivery_sign', 'is_delivery', 'no_mark_deliver_pass_48h'];
const timeKeys = ['sent_sign_created_at', 'server_receive_at', 'sent_at', 'min_respone_time'];

const router = useRouter();

const search = useSearch();
const table = useTable();
const tabsData = useTabs();

const exportLoading = ref<boolean>(false);
const orderNumData = reactive({
  normal_assess_num: 0,
  delivery_num: 0,
});
const filterTime = computed(() => {
  if (search.condition.date) {
    return dayjs(search.condition.date + '').format('YYYY-MM-DD');
  }
  return '--';
});

const filterCycleTime = computed(() => {
  if (search.condition.date) {
    const a = dayjs(search.condition.date + '')
      .subtract(9, 'days')
      .format('YYYY-MM-DD');
    const b = dayjs(search.condition.date + '')
      .subtract(3, 'days')
      .format('YYYY-MM-DD');
    return a + '~' + b;
  }
  return '--';
});

const tableColumns = computed(() => {
  const columns = table.columnsObj[tabsData.activeKey];
  columns.forEach(item => {
    if (item.key === 'is_delivery') {
      item.title = `是否${filterTime.value}前已发货订单`;
    }
  });
  return columns;
});

const resultBtnDisabled = computed(
  () => search.optionMap.dateOptions.length === 0 || !distribute_id.value || !isResult.value || overtime_appeal.value,
);

const loadList = () => {
  if (!search.condition.date) {
    return;
  }
  table.loading = true;

  ApiGetTabel({
    ...search.condition,
    order_id: search.condition.order_id ? Number(search.condition.order_id) : undefined,
    indicator_type: tabsData.activeKey === 3 ? tabsData.activeKey + 2 : tabsData.activeKey + 1,
    page_comp: {
      page: table.pagination.current,
      page_size: table.pagination.pageSize,
    },
  })
    .then(res => {
      orderNumData.delivery_num = res.data.other.delivery_num;
      orderNumData.normal_assess_num = res.data.other.normal_assess_num;
      table.list = res.data.list || [];
      table.pagination.total = res.data.page_info.count;
    })
    .finally(() => {
      table.loading = false;
    });
};

const tableChange = (pagination: PaginationProps) => {
  table.pagination.current = pagination.current;
  table.pagination.pageSize = pagination.pageSize;
  loadList();
};

const isResult = ref<boolean>(false);
const distribute_id = ref<number | undefined>();
const appeal_id = ref<number>(0);
const overtime_appeal = ref<boolean>(false);
const pageType = computed(() => (appeal_id.value === 0 ? 1 : 2));
const getResult = () => {
  if (!search.condition.date) {
    isResult.value = false;
    return;
  }
  ApiGetResult({ date: search.condition.date })
    .then(res => {
      distribute_id.value = res.data.id;
      overtime_appeal.value = res.data.overtime_appeal;
      appeal_id.value = res.data.appeal_id;
      tabsData.tabsList.forEach(item => {
        item.value = res.data[item.key] || 0;
        item.key === 'risk_pass_delivery_rate' && (item.value = ((item.value as number) * 100).toFixed(2) + '%');
        item.key === 'avg_valid_response_second' && (item.value = filterValue(item.value));
        item.status = res.data[item.statusKey];
      });
      isResult.value = true;
    })
    .catch(() => {
      isResult.value = false;
    });
};

const handleSelectChange = () => {
  search.condition.order_id = undefined;
  getResult();
  table.resetPagination();
  loadList();
};

const handleExport = () => {
  exportLoading.value = true;
  ApiExport({ date: search.condition.date })
    .then(res => {
      window.open(res.data);
    })
    .finally(() => {
      exportLoading.value = false;
    });
};

const handlePressEnter = () => {
  table.resetPagination();
  loadList();
};

const initData = async () => {
  await search.getOptions();
  getResult();
  loadList();
};

function filterValue(value: number) {
  value = +value;
  const hours = Math.floor(value / 3600);
  const minutes = Math.floor((value % 3600) / 60);
  const seconds = Math.floor(value % 60);
  return `${hours}时 ${minutes}分 ${seconds}秒`;
}

onMounted(() => {
  initData();
});
</script>

<style lang="less" scoped>
:deep(.page-top) {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
}

.examination-result {
  padding: 0 24px;
  background-color: #fff;

  .sub-title {
    color: rgba(6, 21, 51, 0.85);
    font-weight: bold;
    font-size: 16px;
  }

  &-form {
    margin: 24px;

    :deep(.ant-form-item) {
      margin-bottom: 0;
    }
  }

  .tips {
    display: flex;
    align-items: center;
    padding: 9px 16px;
    background: #f0f7ff;
    border-radius: 4px;

    &-icon {
      margin-right: 10px;
      color: #3777ff;
    }

    &-text {
      color: rgba(6, 21, 51, 0.65);
    }
  }

  .table-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 0 8px;

    &-left {
      color: rgba(6, 21, 51, 0.85);

      .table-desc {
        color: rgba(6, 21, 51, 0.45);
      }
    }
  }
}
</style>
