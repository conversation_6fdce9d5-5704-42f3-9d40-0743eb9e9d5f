import { GET, POST } from '@/services/api';

/**
 * 修改备注
 */
export function changeRemark(data: { order_id: string; remark: string; is_sync_crm: number }) {
  return POST('/order-remark/store', data);
}

/**
 * 订单备注-标签-根据分组id获取列表
 */
export function queryListByGroup(data: { tag_group_id: string }) {
  return GET('/orderRemark/tag/query-list-by-group', data);
}

// 订单备注-标签组-查找所有数据
export function queryAllData() {
  return GET('/orderRemark/tag-group/query-all-data');
}

/** 添加订单履约状态 */
export function getCreateOrderLog(params: any) {
  return GET('/v3-order/create-order-log', params);
}

//订单添加备注时需请求该接口关联
export function relateWorkOrder(data: any) {
  return POST('/new-work-order/update-relate', data);
}
