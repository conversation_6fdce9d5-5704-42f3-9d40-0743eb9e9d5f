<template>
  <div class="tabs">
    <div
      v-for="(item, index) in tabsList"
      :key="item.label"
      class="tabs-item"
      :class="bindActiveKey === index ? 'actived' : ''"
      @click="handleClick(index)"
    >
      <img
        class="tabs-item-img"
        :src="item.icon"
        :width="50"
      >
      <div class="tabs-item-left">
        <div class="tabs-item-label">
          {{ item.label }}
        </div>
        <div class="tabs-item-value">
          {{ item.value }}
        </div>
      </div>

      <div
        class="tabs-item-status"
        :class="item.status ? '' : 'not-stand'"
      >
        {{ item.status ? '达标' : '不达标' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { TabsItemType } from '../../data';

const props = defineProps({
  activeKey: {
    type: Number,
    required: true,
  },
  tabsList: {
    type: Array as PropType<TabsItemType[]>,
    required: true,
  },
});

const emit = defineEmits(['update:activeKey', 'load-list']);

const bindActiveKey = computed({
  get() {
    return props.activeKey;
  },
  set(value) {
    emit('update:activeKey', value);
  },
});

const handleClick = (index: number) => {
  bindActiveKey.value = index;
  emit('load-list');
};
</script>

<style lang="less" scoped>
.tabs {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-block: 24px;

  &-item {
    position: relative;
    display: flex;
    flex-basis: 268px;
    align-items: center;
    padding: 24px 16px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 8px;
    box-shadow: 0 2px 15px 0 rgba(6, 21, 51, 0.08);
    cursor: pointer;

    &-left {
      margin-left: 16px;
    }

    &-label {
      margin-bottom: 2px;
      color: rgba(6, 21, 51, 0.65);
      font-size: 12px;
    }

    &-value {
      color: rgba(6, 21, 51, 0.85);
      font-weight: bold;
      font-size: 24px;
    }

    &-status {
      position: absolute;
      top: 0;
      right: 0;
      width: 52px;
      color: #52c41a;
      font-size: 12px;
      text-align: center;
      background: #d1f6bf;
      border-top-right-radius: 8px;
      border-bottom-left-radius: 8px;
    }
  }

  .actived {
    border: 1px solid #3777ff;
  }

  .not-stand {
    color: #ff4d4f;
    background: #ffc3c4;
  }
}
</style>
