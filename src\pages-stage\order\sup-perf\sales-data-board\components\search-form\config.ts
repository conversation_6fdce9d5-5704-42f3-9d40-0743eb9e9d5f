import { ref } from 'vue';

export const getSearchConfig = () => {
  const searchFormGroup = ref<Record<string, any>>([
    {
      key: 'dept_id',
      label: '销售部门',
      type: 'select',
      options: [],
      elProps: {
        placeholder: '请选择销售部门',
        style: {
          width: '100%',
        },
      },
    },
    {
      key: 'dept_type',
      label: '销售人员',
      type: 'radioGroup',
      options: [
        {
          label: '全部销售',
          value: 1,
        },
        {
          label: '单个销售',
          value: 2,
        },
      ],
    },
    {
      key: 'dept_ids',
      type: 'select',
      options: [],
      isHidden: true,
    },
    {
      key: 'date',
      label: '时间',
      type: 'monthPicket',
      elProps: {
        placeholder: '请选择时间',
        style: {
          width: '100%',
        },
      },
    },
    {
      key: 'date_range',
      label: '统计时段',
      type: 'radioGroup',
      options: [
        {
          label: '一年',
          value: 1,
        },
        {
          label: '6个月',
          value: 2,
        },
        {
          label: '三个月',
          value: 3,
        },
      ],
    },
  ]);
  return {
    searchFormGroup,
  };
};
