import { GET, IAxiosResponse } from '@/services/api';
import { ITemplateItem } from './data';

/**
 * 获取估价模板列表
 * @param {any} params
 * @returns {any}
 */
export const queryTemplateList = (params: Record<string, any>): Promise<IAxiosResponse<ITemplateItem[]>> => {
  return GET('/super/recycle/evaluate-template/list', params);
};

/**
 * 删除估价模板
 * @param {any} id:string|number
 * @returns {any}
 */
export const deleteTemplate = (id: string | number): Promise<IAxiosResponse<void>> => {
  return GET('/super/recycle/evaluate-template/del', { id });
};
