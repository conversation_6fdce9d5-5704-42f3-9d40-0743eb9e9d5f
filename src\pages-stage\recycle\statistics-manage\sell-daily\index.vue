<template>
  <layout-admin-page
    :navs="['趣回收', '经营管理', '销售日报']"
    title="销售日报"
  >
    <div class="container">
      <SearchGroup
        v-model:value="searchInfo"
        @confirm="getTableList"
      />
      <a-table
        class="table-box bottom-fix-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="page"
        :scroll="{ x: 1612 }"
        @change="tableChange"
      >
        <template #headerCell="{ column }">
          <template v-if="column.tipsText">
            {{ column.title }}
            <a-tooltip placement="top">
              <template #title>
                <span>{{ column.tipsText }}</span>
              </template>
              <ExclamationCircleOutlined style="color: grey; font-size: 14px" />
            </a-tooltip>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="['sale_num'].includes(column.dataIndex)">
            <span
              :class="{
                active: record[column.dataIndex] && Number(record[column.dataIndex]) != 0,
              }"
              @click="onOpenPage(column.dataIndex, record)"
            >{{ record[column.dataIndex] }}</span>
          </template>
          <template v-if="['out_warehouse_device_num', 'return_goods_device_num'].includes(column.dataIndex)">
            <div class="flex-box">
              <div>
                {{
                  column.dataIndex === 'out_warehouse_device_num' ? record.out_warehouse_num : record.return_goods_num
                }}
              </div>
              <a-button
                class="column-button"
                @click="onOpenPage(column.dataIndex, record)"
              >
                <div>
                  {{ `关联${record[column.dataIndex]}台设备` }}
                  <a-tooltip placement="top">
                    <template #title>
                      <span>{{ column.equipmentTipsText }}</span>
                    </template>
                    <ExclamationCircleOutlined style="font-size: 14px" />
                  </a-tooltip>
                </div>
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </layout-admin-page>
</template>

<script lang="ts" setup>
import { onMounted,reactive } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { useTable } from '@/hook/component/use-table';

import SearchGroup from './components/search-group.vue';
import { columns } from './config';
import { IDataSource,ISearchInfo } from './data';

const defaultTimeType = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];

const searchInfo = reactive<ISearchInfo>({
  source_type: '0',
  data_type: '1',
  timeType: defaultTimeType,
});

const { list: dataSource, page, listLoading: loading, getTableList, tableChange } = useTable<IDataSource, any>({
  url: '/super/recycle/data-count/order-sale-data',
  searchForm: searchInfo,
  method: 'GET',
  totalKey: 'data.pageInfo.count',
  formatSearchValue: (params: ISearchInfo) => {
    const requestParams = { ...params };
    delete requestParams.timeType;
    if (params.timeType || params.timeType?.length) {
      // 按日统计，默认数据格式为数组； 按月统计，则数据是字符串，选择单个年份；
      if (requestParams.data_type === '1') {
        requestParams.start_at = params.timeType[0];
        requestParams.end_at = params.timeType[1];
      } else {
        requestParams.start_at = dayjs(params.timeType).startOf('year').format('YYYY-MM-DD');
        requestParams.end_at = dayjs(params.timeType).endOf('year').format('YYYY-MM-DD');
      }
    }
    return requestParams;
  },
  pagination: {
    showTotal: (): string => {
      let { total, pageSize } = page;
      let totalPages = Math.ceil((total as number) / (pageSize as number));
      return `共${page.total}条记录  第 ${page.current} / ${totalPages} 页`;
    },
  },
});

const onOpenPage = (key: string, record: IDataSource) => {
  const value = record[key];
  // 没有数据来源、值为0或空，点击不做跳转；
  if (!value || Number(value) === 0) return;

  const pageUrl = {
    sale_num: '/super/quality-spu/quality-orders-v3',
    out_warehouse_device_num: '/super/recycle/device-management/index',
    return_goods_device_num: '/super/recycle/device-management/index',
  };
  const url = pageUrl[key];
  const recordDateList = [record['date'], record['date']];
  const dateList = recordDateList.map((val, index) => {
    const array = val.split('-');
    if (array.length < 3) {
      val = dayjs(val)[index === 0 ? 'startOf' : 'endOf']('month').format('YYYY-MM-DD');
    } else {
      val = dayjs(val).format('YYYY-MM-DD');
    }
    return val;
  });
  const params = {
    startTime: dateList[0],
    endTime: dateList[1],
    warehouse_id: '8',
  };

  if (['out_warehouse_device_num', 'return_goods_device_num'].includes(key)) {
    if (key === 'out_warehouse_device_num') {
      params.order_start_time = dateList[0];
      params.order_end_time = dateList[1];
    } else if (key === 'return_goods_device_num') {
      params.after_start_time = dateList[0];
      params.after_end_time = dateList[1];
      params.return_type = 1;
    }
    delete params.startTime;
    delete params.endTime;
    delete params.warehouse_id;
  }

  const urlParamsStr = Object.keys(params).reduce((pre, cur) => {
    return pre ? `${pre}&${cur}=${params[cur]}` : `?${cur}=${params[cur]}`;
  }, '');
  window.parent.postMessage(
    {
      action: 'blank',
      href: url + urlParamsStr,
    },
    '*',
  );
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
.container {
  padding: 2px 24px 26px 24px;
}

.table-box {
  margin-top: 24px;

  .active {
    color: #3777ff;
    cursor: pointer;
  }
}

.column-button {
  display: flex;
  align-items: center;
  width: 133px;
  height: 22px;
}

.flex-box {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
