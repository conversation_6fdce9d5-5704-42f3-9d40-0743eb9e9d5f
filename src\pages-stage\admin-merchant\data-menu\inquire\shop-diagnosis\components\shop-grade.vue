<template>
  <a-spin :spinning="loading">
    <div
      v-if="resultList.length"
      class="shop-grade"
    >
      <div
        class="collapse"
        :class="isOpen ? 'collapse-open' : ''"
      >
        <div class="shop-grade-header">
          <div
            class="result-title"
            style="width: 40%; margin-right: 8px"
          >
            数据结果
          </div>

          <div
            v-if="Object.keys(penaltyResultList).length && moduleItem.notifyType !== 3"
            class="result-title"
            style="flex: 1"
          >
            奖惩结果
          </div>
        </div>

        <div class="shop-grade-content">
          <div class="data-result">
            <div
              v-for="item in resultList"
              :key="item.name"
              class="data-item"
            >
              <div class="data-item-title">
                {{ item.name }}
              </div>
              <div class="data-item-table-header">
                <div
                  v-for="headerItem in tableHeaderList"
                  :key="headerItem.title"
                  class="data-item-table-header-item"
                >
                  <img
                    alt=""
                    class="header-icon"
                    :src="headerItem.icon"
                    :width="10"
                  >
                  <span>{{ headerItem.title }}</span>
                </div>
              </div>

              <div
                v-for="tableItem in item.tableList"
                :key="tableItem.label"
                class="data-item-table-content"
                :style="{ background: dataObj.notify_type[moduleItem.notifyType].tableColor }"
              >
                <div class="content-item">
                  {{ tableItem.label }}
                </div>
                <div class="content-item">
                  {{ tableItem.reachValue }}
                </div>
                <div
                  class="content-item"
                  :class="tableItem['is_pass'] ? 'green' : 'red'"
                >
                  {{ tableItem.realityValue }}
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="Object.keys(penaltyResultList).length && moduleItem.notifyType !== 3"
            class="award-result"
          >
            <div
              v-for="item in Object.values(penaltyResultList)"
              :key="item.resultChild!.name"
              class="award-item"
            >
              <div class="award-title">
                考核【{{ item.resultChild!!.name }}】不达标，命中处罚
              </div>
              <div class="award-content">
                处罚内容【{{ item.resultChild!.penalty_content }}】 预计生效时间段【{{ item.resultChild!!.date_range_str
                }}】
              </div>
              <div
                v-if="item.is_invalid"
                class="award-status"
              >
                该处罚已解除，解除时间为：{{ item.resultChild!.invalid_time_str }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <footer
        class="footer"
        @click="isOpen = !isOpen"
      >
        <template v-if="!isOpen">
          <DoubleRightOutlined class="open-icon" />
          展开全部
        </template>

        <template v-else>
          <DoubleRightOutlined class="close-icon" />
          收起全部
        </template>
      </footer>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
import { onMounted, PropType, ref } from 'vue';
import { DoubleRightOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { hKeys, indicatorLabelObj, rateKeys } from '../config';
import { IDataObj, IModuleLinesItem, IPenaltyResult, IResultItem, ITableItemType } from '../data';
import { apiGetReportDetail } from '../service';

defineProps({
  moduleItem: {
    type: Object as PropType<IModuleLinesItem>,
    required: true,
  },
  dataObj: {
    type: Object as PropType<IDataObj>,
    required: true,
  },
});

const tableHeaderList = [
  {
    title: '考核指标',
    icon: 'https://img1.rrzuji.cn/uploads/scheme/2502/07/m/ykEKFPKK4JkTCWBZcyRg.png',
  },
  {
    title: '达标值',
    icon: 'https://img1.rrzuji.cn/uploads/scheme/2502/07/m/Q2ORSUqaRRMOo7H24pFL.png',
  },
  {
    title: '实际值',
    icon: 'https://img1.rrzuji.cn/uploads/scheme/2502/07/m/3ZYCq1M5q5CMmVeEBmRs.png',
  },
];

const isOpen = ref(false);

const loading = ref(false);
const resultList = ref<IResultItem[]>([]);
const penaltyResultList = ref<Record<string, IPenaltyResult>>({});
const loadData = () => {
  loading.value = true;

  const today = dayjs();
  const currentDayOfWeek = today.day();
  const date = (currentDayOfWeek < 4 ? today.subtract(1, 'week').day(4) : today.day(4)).format('YYYYMMDD');

  apiGetReportDetail({
    date: Number(date),
  })
    .then(res => {
      const list = res.data.lines || [];
      const penaltyResultObj: Record<string, IPenaltyResult> = {};
      list.forEach(item => {
        const tableList: ITableItemType[] = [];
        Object.keys(indicatorLabelObj).forEach(key => {
          if (item[key].is_set) {
            tableList.push({
              label: indicatorLabelObj[key],
              reachValue: filterIndicator(key, item[key].pass_line.val, item[key].pass_line.symbol),
              realityValue: filterIndicator(key, item[key].val),
              is_pass: item[key].is_pass,
            });
          }
        });

        // 根据 penalty_content 分类
        item.penalty_result.forEach(i => {
          i.name = item.name;
          const resultItem = penaltyResultObj[i.penalty_content];
          if (!resultItem) {
            penaltyResultObj[i.penalty_content] = { is_invalid: i.is_invalid, children: [i] };
          } else {
            const newChild = [...resultItem.children!, i];
            const newValid = newChild.every(c => c.is_invalid); // 是否全部为true
            penaltyResultObj[i.penalty_content] = { is_invalid: newValid, children: newChild };
          }
        });

        item.tableList = tableList;
      });

      // 特殊处理奖惩结果
      Object.keys(penaltyResultObj).forEach(key => {
        let min;
        let max;
        let maxInvalid;

        // 计算时间范围
        penaltyResultObj[key].children!.forEach(item => {
          const date_range = item.date_range_str.split('~');
          const [minDate, maxDate] = date_range;
          !min && (min = minDate);
          !max && (max = maxDate);
          !maxInvalid && (maxInvalid = item.invalid_time_str);
          maxInvalid &&
            (maxInvalid = dayjs(maxInvalid).isAfter(item.invalid_time_str) ? maxInvalid : item.invalid_time_str);
          if (min && max) {
            min = dayjs(min).isBefore(minDate) ? min : minDate;
            max = dayjs(max).isAfter(minDate) ? max : maxDate;
          }
        });

        penaltyResultObj[key].resultChild = {
          ...penaltyResultObj[key].children![0],
          invalid_time_str: maxInvalid,
          date_range_str: min + '~' + max,
        };
        delete penaltyResultObj[key].children;
      });
      penaltyResultList.value = penaltyResultObj;
      resultList.value = list;
    })
    .finally(() => {
      loading.value = false;
    });
};

const filterIndicator = (key, text, symbol?): string => {
  let resNum: string | number = (text = Number(text));
  if (rateKeys.includes(key)) resNum = (text * 100).toFixed(2) + '%';
  if (hKeys.includes(key)) resNum = (text / 3600).toFixed(2) + 'h';
  return (symbol ? symbol : '') + resNum;
};

defineExpose({
  loadData,
});

onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.shop-grade {
  padding: 8px;
  background: linear-gradient(180deg, #f6f7f9 0%, rgba(246, 247, 249, 0.45) 100%);
  border-radius: 4px;
  transition: max-height 0.3s;

  .collapse {
    display: grid;
    grid-template-rows: 0fr;
    max-height: 400px;
    overflow: hidden;

    &-open {
      max-height: unset;
    }
  }

  &-header {
    display: flex;
  }

  &-content {
    display: flex;
  }

  .data-result {
    width: 40%;
    margin-right: 8px;
  }

  .award-result {
    flex: 1;
  }

  .data-item {
    margin-top: 8px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;

    &-title {
      padding-left: 8px;
      font-weight: bold;
    }

    &-table {
      &-header {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        background: #fbfbfb;
        border-radius: 4px;

        &-item {
          display: flex;
          align-items: center;
          width: 33.33%;
          color: rgba(6, 21, 51, 0.65);
          font-size: 12px;

          .header-icon {
            margin-right: 4px;
            padding-bottom: 1px;
          }
        }
      }

      &-content {
        display: flex;
        align-items: center;
        margin-top: 8px;
        padding: 8px 16px;

        .content-item {
          width: 33.33%;
          color: rgba(6, 21, 51, 0.65);

          &:nth-child(2),
          &:nth-child(3) {
            color: rgba(6, 21, 51, 0.85);
            font-weight: bold;
            font-size: 20px;
          }
        }

        .green {
          color: #52c41a !important;
        }

        .red {
          color: #f5222d !important;
        }
      }
    }
  }

  .award-item {
    margin-top: 8px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;

    .award-title {
      padding: 4px 8px;
      color: #ff4d4f;
      background: #fff1f0;
      border-radius: 4px;
    }

    .award-content {
      margin: 12px 0 8px 0;
      color: rgba(6, 21, 51, 0.65);
      font-size: 12px;
    }

    .award-status {
      color: #faad14;
      font-size: 12px;
    }
  }

  .result-title {
    position: relative;
    margin-bottom: 12px;
    padding-left: 12px;
    font-weight: bold;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 18px;
      background: #3777ff;
      border-radius: 4px;
      transform: translateY(-50%);
      content: '';
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    padding: 6px 0;
    color: rgba(6, 21, 51, 0.45);
    background: rgba(255, 255, 255, 0.75);
    border-radius: 4px;
    cursor: pointer;

    .open-icon {
      margin-right: 8px;
      transform: rotate(90deg);
    }

    .close-icon {
      margin-right: 8px;
      transform: rotate(-90deg);
    }
  }
}
</style>
