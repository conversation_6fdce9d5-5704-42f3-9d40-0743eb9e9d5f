<template>
  <div>
    <template
      v-for="(item, index) in list"
      :key="item.tag_id"
    >
      <a-space direction="vertical">
        <a-tag
          v-if="item.name"
          :color="colorList[index]"
          :style="{ color: colorList[index] }"
        >
          {{ item.name }}
        </a-tag>
      </a-space>
    </template>
  </div>
</template>

<script setup lang="ts">
import { IListItem } from './data';
interface IProps {
  list: IListItem[];
  colorList: string[];
}

withDefaults(defineProps<IProps>(), {
  colorList: () => ['purple', 'green', 'orange', 'cyan', 'pink'],
});
</script>
