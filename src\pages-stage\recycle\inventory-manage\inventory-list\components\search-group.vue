<template>
  <a-form
    ref="formRef"
    class="search-flex"
    layout="inline"
    :model="formInfo"
  >
    <a-form-item
      label="数据来源"
      name="source_type"
    >
      <a-select
        v-model:value="formInfo.source_type"
        :options="optionsManage.source_type"
        placeholder="请选择"
        style="width: 184px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="品类"
      name="category_brand_model"
    >
      <a-cascader
        v-model:value="formInfo.category_brand_model"
        allow-clear
        :field-names="{ label: 'title', value: 'id' }"
        :max-tag-count="2"
        multiple
        :options="breedList"
        placeholder="请选择"
        style="width: 212px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="SKU ID"
      name="sku_id"
    >
      <a-input
        v-model:value="formInfo.sku_id"
        allow-clear
        :maxleng="60"
        placeholder="请输入"
        style="width: 193px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="成新度"
      name="condition"
    >
      <a-select
        v-model:value="formInfo.condition"
        allow-clear
        mode="multiple"
        :options="optionsManage.condition"
        placeholder="请选择"
        style="width: 200px"
      />
    </a-form-item>
    <a-form-item>
      <div style="display: flex; align-items: center">
        <a-button
          style="margin-right: 8px"
          type="primary"
          @click="emits('confirm', 'search')"
        >
          查询
        </a-button>
        <a-button
          style="margin-right: 15px"
          @click="onReset"
        >
          重置
        </a-button>
      </div>
    </a-form-item>
    <a-form-item
      label=""
      name="no_sale_stock"
    >
      <a-checkbox
        v-model:checked="isSale"
        @change="saleChange"
      >
        过滤无销售库存
      </a-checkbox>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, PropType } from 'vue';
import { ISearchInfo } from '../data.d';
import { getTableSearchList, getBreedList } from '../service';

const props = defineProps({
  value: {
    type: Object as PropType<ISearchInfo>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:value', 'confirm']);

const formRef = ref<HTMLElement | any>(null);

const formInfo = ref<Partial<ISearchInfo>>({});

const optionsManage = reactive({
  source_type: [],
  condition: [],
});

const getOptionsData = async () => {
  const res = await getTableSearchList();
  const data = res.data || {};
  const keysArr = Object.keys(data);
  keysArr.forEach(key => {
    if (optionsManage.hasOwnProperty(key)) {
      optionsManage[key] = data[key] || [];
    }
  });
};

const onEmit = () => {
  emits('update:value', formInfo.value);
};

const isSale = ref<boolean>(false);

const onReset = () => {
  formRef.value?.resetFields();
  isSale.value = false;
  onEmit();
  emits('confirm', 'search');
};

watch(
  () => props.value,
  newValue => {
    formInfo.value = newValue || {};
  },
  {
    immediate: true,
  },
);

getOptionsData();

const saleChange = (e: any) => {
  formInfo.value.no_sale_stock = e.target.checked ? 1 : 2;
  emits('confirm', 'search');
};

const breedList = ref();

const getBreed = () => {
  getBreedList({}).then(res => {
    breedList.value = res.data.spuRelation?.children || [];
  });
};
getBreed();
</script>
<style lang="less" scoped>
.search-flex {
  display: flex;
  gap: 12px;
}
</style>
