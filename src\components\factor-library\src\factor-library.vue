<template>
  <div
    class="factor-library"
    :class="{ 'factor-readonly': !!readonly }"
  >
    <div
      v-if="componentDisabled"
      class="factor-header"
    >
      <slot name="title">
        <strong>{{ title }}</strong>
      </slot>
    </div>
    <RFactorSelector
      v-if="contentOptions.length"
      ref="factorRef"
      v-bind="$attrs"
      :disabled="componentDisabled"
      :factory-rules="contentData"
      :max-level="1"
      :zero-level-max-length="max"
      @change="handleChange"
    >
      <!-- 头部 -->
      <template #createCondition="{ createRules, rulesData }">
        <div class="factor-header">
          <slot name="title">
            <strong>{{ title }}</strong>
          </slot>
          <div
            v-if="!componentDisabled"
            style="cursor: pointer"
            @click="createRules(rulesData)"
          >
            <PlusCircleFilled :style="{ color: '#00c8be', marginRight: '4px' }" />
            添加条件
          </div>
        </div>
      </template>
      <!--  规则  -->
      <template #conditionRules="bundleScope">
        <FactorFeature
          v-model:content="bundleScope.conditionRulesData"
          :condition-map="defaultConditionMap"
          :condition-render="{
            target: FactorConditionSelect,
            props: {
              factorData: factorData,
              options: contentOptions,
              fieldNames: { label: 'name', value: 'code' },
              showSearch: true,
              allowClear: true,
              optionFilterProp: 'name',
              placeholder: readonly ? '未选择' : '请选择',
              style: { width: contentWidth ? `${contentWidth}px` : '180px' },
            },
            updated,
          }"
          :disabled="disabled"
          :judge-map="judgeMap"
          :result-props="{
            readonly,
            ...(readonly ? { placeholder: '无' } : {}),
          }"
          style="flex: 1"
        />
      </template>
    </RFactorSelector>
    <!--  加载中  -->
    <a-spin
      v-if="loading"
      class="factor-loading"
      :spinning="loading"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { PlusCircleFilled } from '@ant-design/icons-vue';
import type { RFactorSelectorInstance } from 'rrz-web-design';
import { RFactorSelector } from 'rrz-web-design';

import FactorConditionSelect from './components/factor-condition-select.vue';
import FactorFeature from './components/factor-feature.vue';
import { defaultConditionMap, factorComponentList, judgeMap } from './config';
import { IContentOptions, IFactorContentData, TUpdatedFn } from './data';
import { IFactorData, IFactorLibraryProps } from './factor-library';
import { getLibraryData, toFactorContentData, toFactorLibraryData } from './helper';

defineOptions({ inheritAttrs: false });

const props = defineProps<IFactorLibraryProps>();

const emit = defineEmits<{
  (e: 'update:value', data: IFactorData): void;
  (e: 'change', data: IFactorData): void;
}>();

/** 公共组件的数据 */
const contentData = ref<IFactorContentData>({});
/** 因子中台的数据 */
const factorData = ref<IFactorData>(props.value || {});

// 监听数据变化, 将因子中台的数据转化为组件可回显的数据
const isChange = ref(false);
watch(
  () => props.value,
  () => {
    if (!isChange.value) {
      // 需要判断是否由change触发的，避免循环触发change事件导致死循环
      contentData.value = toFactorContentData(props.value);
    }
    isChange.value = false;
  },
  { immediate: true, deep: true },
);

/** 修改数据 */
function handleChange(value: IFactorContentData) {
  factorData.value = toFactorLibraryData(value);
  emit('update:value', factorData.value);
  emit('change', factorData.value);
  isChange.value = true;
}

const loading = ref(false);
/** 因子库条件数据 */
const contentOptions = ref<IContentOptions[]>([]);
/** 组件禁用规则 */
const componentDisabled = computed(() => props.disabled || props.readonly || !contentOptions.value.length);

/** 获取对应模板的因子库条件数据 */
watch(
  () => props.code,
  () => {
    if (!props.code) {
      contentOptions.value = [];
      return;
    }
    loading.value = true;
    getLibraryData(props.code)
      .then(res => (contentOptions.value = res.data?.factor_list || []))
      .finally(() => (loading.value = false));
  },
  { immediate: true },
);

/** content条件更新后返回对应的key、特殊组件等信息，显示选择后所可用的判断条件与结果组件内容 */
const updated: Promise<TUpdatedFn> = value => {
  const condition = contentOptions.value.find(item => item.code === value);
  if (!condition) {
    return undefined;
  }
  // 选择项值统一使用string类型，确保与回显数据类型一致
  const options = condition.option_value?.map(item => ({ label: item.name, value: String(item.value) }));
  const specialCondition = factorComponentList.find(item => item.value === condition.option_code)?.condition;
  return {
    key: condition.factor_type,
    props: { options: options, code: condition.option_code },
    ...specialCondition?.[condition.factor_type], // 特殊渲染组件
  };
};

const factorRef = ref<RFactorSelectorInstance>();

/** 获取数据 */
async function getData(validate = true) {
  const data: IFactorContentData = await factorRef.value?.getRulesData(validate);
  // 转化为标准的后端数据
  return toFactorLibraryData(data);
}

defineExpose({ getData });
</script>

<style lang="less" scoped>
.factor-library {
  position: relative;

  .factor-header {
    display: flex;
    gap: 8px;
    align-items: center;
    padding-bottom: 8px;
  }

  .factor-loading {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
  }

  // 禁用“且或”的切换，后续迭代如需切换，删除掉此样式
  :deep(.r-logic-label) {
    pointer-events: none;
  }
}

// 显示只读的组件样式
.factor-readonly {
  * {
    pointer-events: none;
  }

  :deep(.ant-select-arrow) {
    display: none;
  }

  :deep(.ant-select-selection-item-remove) {
    display: none;
  }

  :deep(.ant-input-clear-icon) {
    display: none;
  }
}
</style>
