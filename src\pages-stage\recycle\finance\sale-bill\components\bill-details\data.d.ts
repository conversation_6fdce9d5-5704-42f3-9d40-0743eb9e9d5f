export interface ISaleBillDetail {
  rsp_order_id: string;
  v3_order_id: string;
  source_type: string;
  model_name: string;
  brand_name: string;
  custom_type: string;
  server_id: string;
  server_name: string;
  created_at: string;
  order_price: string;
  pay_price: string;
  goods_price: string;
  logistics_price: string;
  quality_check_price: string;
  lock_price: string;
  accessory_price: string;
  delivery_service_price: string;
  rs_order_id: string;
  other_money: string;
  finish_pay_price: string;
  wait_pay_price: string;
}

export interface IPayInfoList {
  pay_type: string;
  status: string;
  amount: string;
  payment_flow_sn: string;
  created_at: string;
  created_by: string;
  username: string;
}
export type IRefundInfoList = IPayInfoList;
