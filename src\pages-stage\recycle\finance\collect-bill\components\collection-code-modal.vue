<template>
  <a-modal
    title="收款码"
    :visible="visible"
    width="480px"
    @cancel="onCancel"
  >
    <div class="collection-infer">
      您可以提供以下收款二维码给商家进行确认，也可以选择点击【收款推送】至商家后台让商家自行对账确认支付。
    </div>
    <div class="collection-code">
      <img
        class="code-img"
        :src="imageBase64"
      >
    </div>
    <template #footer>
      <a-button @click="onCancel">
        取消
      </a-button>
      <a-button
        :disabled="isPushed"
        :loading="submitLoading"
        type="primary"
        @click="onOk"
      >
        {{ isPushed ? '已推送' : '收款推送' }}
      </a-button>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import QRCode from 'qrcode';
import { adviseMerchant } from '../service';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isPushed: {
    type: Boolean,
    default: true,
  },
  payId: {
    type: String,
    default: '',
  },
  payLink: {
    type: String,
    default: '',
  },
});

const submitLoading = ref<boolean>(false);

const imageBase64 = ref<string>('');

const emits = defineEmits(['update:visible', 'update:isPushed', 'reLoad']);

const getQrCode = async () => {
  imageBase64.value = await QRCode.toDataURL(props.payLink, {});
};

const onCancel = () => {
  emits('update:visible', false);
  emits('reLoad');
};

//收款推送
const onOk = async () => {
  submitLoading.value = true;
  try {
    await adviseMerchant({ pay_log_id: props.payId });
    emits('update:isPushed', true);
    message.success('推送成功');
  } finally {
    submitLoading.value = false;
  }
};
onMounted(() => {
  getQrCode();
});
</script>
<style scoped lang="less">
.collection-infer {
  padding: 16px 24px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background: #e6fff9;
  border-radius: 4px;
}
.collection-code {
  width: 192px;
  height: 192px;
  margin: 20px auto 0 auto;
  .code-img {
    width: 100%;
    height: 100%;
  }
}
</style>
