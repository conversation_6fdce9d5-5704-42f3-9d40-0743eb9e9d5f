<template>
  <a-form
    ref="formRef"
    class="search-flex"
    layout="inline"
    :model="formInfo"
  >
    <a-form-item
      label="品类"
      name="category_brand_model"
    >
      <a-cascader
        v-model:value="formInfo.category_brand_model"
        allow-clear
        change-on-select
        :load-data="spuDataListloadData"
        :max-tag-count="2"
        multiple
        :options="breedList"
        placeholder="请选择"
        style="width: 212px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="选择时间"
      name="data_type"
    >
      <div class="data_type">
        <a-select
          v-model:value="formInfo.data_type"
          :options="dateTypeSelect"
          placeholder="请选择"
          style="width: 98px"
          @change="onDateTypeChange"
        />
        <a-form-item-rest>
          <a-date-picker
            v-if="formInfo.data_type === '1'"
            v-model:value="formInfo.date"
            class="time-picker"
            value-format="YYYY-MM-DD"
          />
          <a-date-picker
            v-else
            v-model:value="formInfo.date"
            class="time-picker"
            picker="month"
            value-format="YYYY-MM"
          />
        </a-form-item-rest>
      </div>
    </a-form-item>
    <a-form-item>
      <div style="display: flex; align-items: center">
        <a-button
          style="margin-right: 8px"
          type="primary"
          @click="emits('confirm', 'search')"
        >
          查询
        </a-button>
        <a-button
          style="margin-right: 15px"
          @click="onReset"
        >
          重置
        </a-button>
      </div>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { ref, watch, PropType } from 'vue';
import dayjs from 'dayjs';
import { useCategory } from '../composables/use-category';
import { ISearchInfo, IDateSelectTypeParams } from '../data.d';
import { getSelectList } from '../service';

const props = defineProps({
  value: {
    type: Object as PropType<ISearchInfo>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:value', 'confirm']);

const formRef = ref<HTMLElement | any>(null);

const formInfo = ref<Partial<ISearchInfo>>({});

const dateTypeSelect = ref<IDateSelectTypeParams[]>([]);
//获取下拉列表数据
const getTypeList = async () => {
  const res = await getSelectList();
  dateTypeSelect.value = res.data.data_type || [];
};
getTypeList();

const onDateTypeChange = () => {
  formInfo.value.date = '';
};

const onEmit = () => {
  emits('update:value', formInfo.value);
};

const onReset = () => {
  formRef.value?.resetFields();
  formInfo.value.date = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
  onEmit();
  emits('confirm', 'search');
};

const { spuDataListloadData, breedList } = useCategory();

watch(
  () => props.value,
  newValue => {
    formInfo.value = newValue || {};
  },
  {
    immediate: true,
  },
);
</script>
<style lang="less" scoped>
.search-flex {
  display: flex;
  gap: 12px;
}

.time-picker {
  width: 170px;
  border-left: none;
  border-radius: 0 4px 4px 0;
}
</style>
