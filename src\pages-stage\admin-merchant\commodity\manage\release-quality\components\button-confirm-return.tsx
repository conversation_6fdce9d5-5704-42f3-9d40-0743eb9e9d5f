import { message } from 'ant-design-vue';
import { defineComponent, ref } from 'vue';
import { getReturnAddress, postConfirmReturn } from '../services';
import { useButtonModal } from '@/pages-stage/stock/manage/device/composables/use-button-modal';

import type { Ref } from 'vue';
import type { IAddressDataSource } from '../services';
import type { IUseButtonModal } from '@/pages-stage/stock/manage/device/composables/use-button-modal';

interface ISetupData extends Omit<IUseButtonModal, 'request' | 'open'> {
  dataSource: Ref<IAddressDataSource[]>;
  openModal: () => void;
  confirmReturn: () => Promise<void>;
}

export default defineComponent({
  emits: ['refresh'],
  setup(props, { emit }): ISetupData {
    const { visible, loading, open, close, request } = useButtonModal();
    const dataSource = ref<IAddressDataSource[]>([]);

    const openModal = () => {
      open();
      request(getReturnAddress).then(res => {
        dataSource.value = res.data;
      });
    };

    const confirmReturn = () =>
      request(postConfirmReturn).then(res => {
        message.success(res.message);
        emit('refresh');
        close();
      });

    return {
      visible,
      loading,
      close,

      openModal,
      dataSource,
      confirmReturn,
    };
  },
  render() {
    return (
      <>
        <a-button type="link" onClick={this.openModal}>
          {this.$slots.default && this.$slots.default()}
        </a-button>
        <a-modal
          vModel={[this.visible, 'visible']}
          ok-text="确定"
          cancel-text="取消"
          destroyOnClose={true}
          title="确认寄回"
          onCancel={this.close}
          onOk={this.confirmReturn}
          confirmLoading={this.loading}
        >
          <a-spin spinning={this.loading}>
            <a-typography-paragraph>
              <a-typography-text type="secondary">
                寄回操作暂不支持撤销，点击确认后，设备将进入待寄回状态，若存在上架的严选商品，将自动下架
              </a-typography-text>
            </a-typography-paragraph>
            <a-select style="width:100%">
              {this.dataSource.map(item => (
                <a-select-option value={item.id}>
                  {item.name}-{item.phone}-{item.address}
                </a-select-option>
              ))}
            </a-select>
          </a-spin>
        </a-modal>
      </>
    );
  },
});
