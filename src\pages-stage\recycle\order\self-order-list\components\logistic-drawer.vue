<template>
  <a-drawer
    :closable="false"
    :destroy-on-close="true"
    title="物流信息"
    :visible="visible"
    width="800px"
  >
    <template #extra>
      <div
        class="close-btn"
        @click="closeDrawer"
      >
        <CloseOutlined />
      </div>
    </template>
    <div class="drawer-inner">
      <div class="drawer-inner-info">
        <div class="title">
          <span>物流公司：</span>
          <span>{{ logisticInfo.shipper_name || '顺丰快递' }}</span>
        </div>
        <div class="title">
          <span>物流单号：</span>
          <span>{{ logisticInfo.isReturn ? logisticInfo.return_tracking_num : logisticInfo.tracking_num }}
            <DecryptField
              v-if="logisticInfo.showViewHideBtn"
              :field-item="{
                box: 'text',
                id: logisticInfo.order_id,
                field: logisticInfo.isReturn ? 'return_tracking_num' : 'tracking_num',
                field_type: 'SF',
                type: logisticInfo.type === '个人' ? 158 : 135,
              }"
            />
          </span>
        </div>
      </div>
      <a-spin :spinning="loading">
        <div class="tracking-list clearfix">
          <a-timeline v-if="hasList">
            <template
              v-for="(item, index) in logisticList"
              :key="index"
            >
              <a-timeline-item>
                <div class="timeline-item flex-wrap flex-vertical">
                  <div>{{ item[1] }}</div>
                  <div>{{ item[0] }}</div>
                </div>
              </a-timeline-item>
            </template>
          </a-timeline>
          <a-empty
            v-else
            description="暂未查询到物流信息"
            :image="simpleImage"
          />
        </div>
      </a-spin>

      <div class="drawer-action flex-wrap flex-x-end">
        <a-button
          type="primary"
          @click="() => closeDrawer()"
        >
          确认
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { Empty } from 'ant-design-vue';
import { getLogisticsTrajectory } from '../service';
import useBoolean from '../../../common/use-boolean';
import DecryptField from '@/components/decrypt-field/decrypt-field.vue';
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  logisticInfo: {
    type: Object,
    default: {},
  },
});

const emit = defineEmits(['update:visible']);

const logisticList = ref<any[]>([]);
const [hasList, { setTrue: setHasList, setFalse: setNoList }] = useBoolean(false);
const [loading, { setFalse: setLoadingDone, setTrue: setLoading }] = useBoolean(true);

const getAgainRequest = () => {
  getLogisticsTrajectory({
    biz_content: JSON.stringify({
      type: props.logisticInfo.logistic_code,
      number: props.logisticInfo.isReturn
        ? props.logisticInfo.return_tracking_num_text
        : props.logisticInfo.tracking_num_text,
      phone: '',
    }),
    ordinary_delivery: 1,
  }).then(res => {
    let { html } = res;
    html && setHasList();
    logisticList.value = html.split('<br/>').map((item: string) => item.split('&nbsp;'));
  });
};

watch(
  () => props.visible,
  visible => {
    if (visible) {
      setNoList();
      setLoading();
      getLogisticsTrajectory({
        biz_content: JSON.stringify({
          type: props.logisticInfo.logistic_code,
          number: props.logisticInfo.isReturn
            ? props.logisticInfo.return_tracking_num_text
            : props.logisticInfo.tracking_num_text,
          phone: props.logisticInfo.phone,
        }),
        ordinary_delivery: 1,
      }).then(res => {
        let { html } = res;
        html && setHasList();
        logisticList.value = html.split('<br/>').map((item: string) => item.split('&nbsp;'));
        if (html === '') {
          //获取html为空，则手机号参数传空值，再请求一次
          getAgainRequest();
        }
        setLoadingDone();
      });
    }
  },
);

const closeDrawer = () => {
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.close-btn {
  color: rgba(0, 0, 0, 0.45);
  font-weight: 600;
  font-size: 16px;
  font-style: normal;
  line-height: 1;
  cursor: pointer;
  transition: color 0.2s;
  &:hover {
    color: rgba(0, 0, 0, 0.88);
  }
}
.drawer-inner {
  padding-bottom: 53px;
  .drawer-inner-info {
    margin-bottom: 24px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: PingFangSC-Medium, PingFang SC;
    line-height: 22px;
    .title {
      span:last-child {
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
      }
    }
    .title + .title {
      margin-top: 12px;
    }
  }
  .tracking-list {
    padding: 16px 16px 0;
    background: #f6f7f8;
    border-radius: 2px;
    :deep(.ant-timeline-item-last > .ant-timeline-item-content) {
      min-height: 0;
    }
    .timeline-item div:last-child {
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }
  .drawer-action {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 10px 24px;
    background: #fff;
    border-top: 1px solid #e9e9e9;
  }
}
</style>
