<!--修改寄出方式-->
<template>
  <div>
    <div
      v-for="item in alertTextList"
      :key="item"
      style="color: red"
    >
      {{ item }}
    </div>
    <h3 class="title-text">
      当前寄出方式和运费
    </h3>
    <div>
      <span class="self-label">寄出方式：</span><span>{{ send_type_label }}</span>
    </div>
    <div>
      <span class="self-label">运费：</span><span>{{ freight }}</span>
    </div>

    <h3 style="margin-top: 16px">
      修改后寄出方式和运费
    </h3>

    <a-form-item
      label="寄出方式"
      :name="['data', 'send_type']"
      :rules="[{ required: true }]"
    >
      <a-radio-group
        v-model:value="modelForm.send_type"
        :options="sendOptions"
      />
    </a-form-item>
    <div><span class="self-label">运费：</span><span>0.00</span></div>
    <div style="color: red">
      <span class="self-label">退还用户运费：</span><span>{{ freight }}</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';

import { getSendTypeInfo } from '@/components/order-business/operate-modals/order/after-assist-modal/services';
import { useVModel } from '@/hook/use-vmodel';

type TchangeSendForm = {
  send_type: string | undefined;
  edit_send_json_data: string;
};

const props = withDefaults(
  defineProps<{
    form: TchangeSendForm;
    orderId: string;
    visible: boolean;
  }>(),
  {
    form: {},
    orderId: '',
    visible: false,
  },
);
const emit = defineEmits<{
  (event: 'update:form', value: { item1: string; item2: string }): void;
}>();

const alertTextList = [
  '1、发货后不支持修改寄出方式，若需修改寄出方式，请工单通过后，再发货',
  '2、若用户已支付运费，修改成功后会将运费会退还给用户',
  '3、请上传与用户的协商凭证，更有利于工单通过审核',
];
const modelForm = useVModel(props, 'form', emit);
const sendOptions = ref([]);
const freight = ref('');
const send_type_label = ref('');

async function init() {
  const res = await getSendTypeInfo({ order_id: props.orderId });

  sendOptions.value = res.data.support_send_type_list.map(item => ({
    label: item.value,
    value: item.key,
  }));
  freight.value = res.data.freight.toFixed(2);
  send_type_label.value = res.data.send_type_label;
}

watch(
  () => props.visible,
  val => {
    val && init();
  },
  {
    immediate: true,
  },
);
watch(
  () => modelForm.value.send_type,
  val => {
    const send_type_item = sendOptions.value?.find(item => item.value === val);
    if (!send_type_item) {
      return;
    }
    const obj = {
      origin_data: {
        freight: freight.value,
        send_type_label: send_type_label.value,
      },
      change_data: {
        freight: '0.00',
        send_type_label: send_type_item.label,
      },
    };
    modelForm.value.edit_send_json_data = JSON.stringify(obj);
  },
);
</script>

<style scoped lang="less">
:deep(.ant-radio-wrapper-in-form-item) {
  display: block;
}

.self-label {
  font-weight: 600;
}
</style>
