import { GET, POST } from '@/services/api';

export function getSearchMap() {
  return GET(
    '/merchant/rDI/deliveryBoard/searchMap',
    {},
    {
      hostType: '<PERSON><PERSON><PERSON>',
    },
  );
}

export function getStatisticsData(data: any) {
  return POST('/merchant/rDI/overdueBoard/serQuery', data, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
    hostType: '<PERSON><PERSON><PERSON>',
  });
}
