<template>
  <div class="top-util-box flex-wrap flex-x-justify">
    <div class="top-util-left flex-wrap flex-y-center">
      <div class="top-util-text">
        修改日志
      </div>
      <a-radio-group
        v-model:value="listType"
        @change="setListData"
      >
        <a-radio-button value="before">
          修改前
        </a-radio-button>
        <a-radio-button value="after">
          修改后
        </a-radio-button>
      </a-radio-group>
    </div>
  </div>
  <div class="change-type-tip flex-wrap flex-y-center">
    <div class="tip-item tip-change flex-wrap flex-y-center">
      修改
    </div>
    <div class="tip-item tip-add flex-wrap flex-y-center">
      新增
    </div>
    <div class="tip-item tip-del flex-wrap flex-y-center">
      删除
    </div>
  </div>
  <a-spin :spinning="pageLoading">
    <div class="config-content-box">
      <div class="config-item">
        <div class="config-item-title">
          型号规则
        </div>
        <config-table
          :columns="modelCols"
          :data-source="modelRuleList"
          :is-edit="false"
        />
      </div>
      <div class="config-item">
        <div class="config-item-title">
          类目规则
        </div>
        <config-table
          :columns="categoryCols"
          :data-source="categoryRuleList"
          :is-edit="false"
        />
      </div>
    </div>
  </a-spin>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, Ref, onMounted } from 'vue';
import { getVersionCompare } from '../service';
import { formatModelSource2List, formatCategorySource2List } from '../util';
import { modelCols, categoryCols } from '../config';
import configTable from '@/pages-stage/commodity/setting/deposit-config-v2/components/config-table.vue';

interface pageSetupData {
  navs: string[];
  listType: Ref<Tversion>;
  pageLoading: Ref<boolean>;
  modelRuleList: Ref<any[]>;
  modelCols: Ref<any[]>;
  categoryRuleList: Ref<any[]>;
  categoryCols: Ref<any[]>;
  setListData: () => void;
}
type Tversion = 'before' | 'after';

export default defineComponent({
  components: {
    configTable,
  },
  props: {
    curId: {
      type: String,
      required: true,
    },
  },
  setup(props): pageSetupData {
    console.log(props);
    const navs = ['风控', '风控基础设置', '交互式风控配置', '修改日志'];

    const allList: { [key: string]: any[] } = {
      beforeModelRuleList: [],
      afterModelRuleList: [],
      beforeCategoryRuleList: [],
      afterCategoryRuleList: [],
    };
    const state = reactive<{
      listType: Tversion;
      pageLoading: boolean;
      modelRuleList: any[];
      modelCols: any[];
      categoryRuleList: any[];
      categoryCols: any[];
    }>({
      listType: 'before',
      pageLoading: false,
      modelRuleList: [],
      modelCols: [],
      categoryRuleList: [],
      categoryCols: [],
    });

    async function createPageData() {
      const res = await getVersionCompare({ id: props.curId });
      const { risk_grade, is_new_product, interactive_type, deposit_grade, versionData } = res.data;
      const editCols: any[] = [];
      risk_grade.forEach((item: { id: string; name: string }) => {
        editCols.push({
          title: item.name,
          key: `ratio${item.id}`,
          displayKey: `wholeRatio${item.id}`, // TODO 这个 display 可以写成一个回调函数的形式，更灵活，待优化
          isEdit: true,
        });
      });
      state.modelCols = [...modelCols, ...editCols];
      state.categoryCols = [...categoryCols, ...editCols];
      const beforeOptions = {
        condition: {},
        depositLevel: {},
      };
      const afterOptions = {
        condition: {},
        depositLevel: {},
      };
      // before修改前数据整理
      is_new_product.forEach((item: { id: string; name: string }) => {
        beforeOptions.condition[`con${item.id}`] = item;
        afterOptions.condition[`con${item.id}`] = item;
      });
      deposit_grade.forEach((item: { id: string; name: string }) => {
        beforeOptions.depositLevel[`dep${item.id}`] = item;
        afterOptions.depositLevel[`dep${item.id}`] = item;
      });

      // 处理model_rule
      allList.beforeModelRuleList = formatModelSource2List(
        versionData.old_config.model_rule_info,
        beforeOptions,
        interactive_type,
      );
      // 处理category_rule
      allList.beforeCategoryRuleList = formatCategorySource2List(
        versionData.old_config.category_rule_info,
        beforeOptions,
        interactive_type,
      );

      // after修改后数据整理
      // 处理model_rule
      allList.afterModelRuleList = formatModelSource2List(
        versionData.new_config.model_rule_info,
        beforeOptions,
        interactive_type,
      );
      // 处理category_rule
      allList.afterCategoryRuleList = formatCategorySource2List(
        versionData.new_config.category_rule_info,
        beforeOptions,
        interactive_type,
      );
      // 数据对比找不同，标识新旧两个版本的新增、删除、修改
      compareHandler(allList.beforeModelRuleList, allList.afterModelRuleList);
      compareHandler(allList.beforeCategoryRuleList, allList.afterCategoryRuleList);
      setListData();
    }

    function compareHandler(before: any[], after: any[]) {
      const tempBefore: any = {};
      before.forEach((item: any, index: number) => {
        tempBefore[`${item.id}-${index}`] = item;
      });
      const tempAfter: any = {};
      after.forEach((item: any, index: number) => {
        const key = `${item.id}-${index}`;
        tempAfter[key] = item;
        if (!tempBefore[key]) {
          // 之前没有，现在有，标识当前为新增
          item.isAdd = true;
        } else {
          // 前后都有，比较tags 和基本类型数据
          compareTags(tempBefore[key].tags, item.tags);
          compareInfo(tempBefore[key], item);
        }
      });

      before.forEach((item: any, index: number) => {
        if (!tempAfter[`${item.id}-${index}`]) {
          item.isDel = true;
        }
      });
    }
    function compareTags(
      before: { id: string | number; name: string }[],
      after: { id: string | number; name: string }[],
    ) {
      const beforeIds = before.map((item: any) => item.id);
      const afterIds = after.map((item: any) => item.id);
      before.forEach((item: any) => {
        if (!afterIds.includes(item.id)) {
          item.classType = 'delete';
        }
      });
      after.forEach((item: any) => {
        if (!beforeIds.includes(item.id)) {
          item.classType = 'add';
        }
      });
    }
    function compareInfo(before: any, after: any) {
      Object.entries(before).forEach(([key, value]: [string, any]) => {
        if (typeof value !== 'object') {
          if (value !== after[key]) {
            before[`${key}_change`] = true;
            after[`${key}_change`] = true;
          }
        }
      });
    }

    function setListData() {
      state.modelRuleList = allList[`${state.listType}ModelRuleList`];
      state.categoryRuleList = allList[`${state.listType}CategoryRuleList`];
    }

    onMounted(() => {
      createPageData();
    });

    return {
      navs,
      ...toRefs(state),
      setListData,
    };
  },
});
</script>

<style lang="less" scoped>
.page-deposit-config-compare {
  padding: 16px;
}
.top-util-box {
  padding: 20px 32px;
  background-color: #fff;
  border-bottom: 1px solid #e9e9e9;
  .top-util-text {
    margin-right: 24px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
  }
}
.change-type-tip {
  padding: 24px 32px 0;
  background-color: #fff;
  .tip-item {
    margin-right: 24px;
    font-size: 14px;
    &::before {
      display: inline-block;
      width: 24px;
      height: 4px;
      margin-right: 8px;
      border-radius: 4px;
      content: ' ';
    }
  }
  .tip-change {
    color: #00c8be;
    &::before {
      background: #00c8be;
    }
  }
  .tip-add {
    color: #3777ff;
    &::before {
      background: #3777ff;
    }
  }
  .tip-del {
    color: #ff4d4f;
    &::before {
      background: #ff4d4f;
    }
  }
}
.config-content-box {
  padding: 24px 32px;
  background-color: #fff;
  .config-item {
    margin-bottom: 24px;
  }
  .config-item-title {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
