<template>
  <div class="form-wrap">
    <span class="form-wrap-label">基础信息</span>
    <div class="form-wrap-content">
      <div
        v-for="(item, index) in baseFormList"
        :key="index"
        class="basis-form"
      >
        <div class="form-label">
          {{ item.label }}：
        </div>
        <div class="form-value">
          {{ (item.value && item.value()) || params[item.field] || '--' }}
        </div>
      </div>
    </div>
  </div>
  <div
    v-if="params.order_form && params.order_form.length"
    class="form-wrap"
  >
    <span class="form-wrap-label">订单维度</span>
    <div class="form-wrap-content">
      <div
        v-for="(item, index) in params.order_form"
        :key="index"
        class="order-item"
      >
        <template v-if="item.attr_label">
          <div class="item-parent">
            <div class="form-label">
              {{ item.attr_label }}：
            </div>
            <div class="form-value">
              {{ getCn(item.content_label) }}
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
  <div
    v-if="params.goods_form && params.goods_form.length"
    class="form-wrap"
  >
    <span class="form-wrap-label">商品维度</span>
    <div class="form-wrap-content">
      <div
        v-for="(item, index) in params.goods_form"
        :key="index"
        class="goods-item"
      >
        <div class="index-header">
          <div class="index-tags">
            {{ String.fromCharCode(index + 65) }}
          </div>
          场景{{ numberToString(String(index + 1)) }}
        </div>
        <div class="item-wrap">
          <div class="items-form">
            <div class="items-form-label">
              商品类目：
            </div>
            <div class="items-form-value">
              {{ item.categoryCn }}
            </div>
          </div>
          <div
            v-if="item.brandCn"
            class="items-form"
          >
            <div class="items-form-label">
              商品品牌：
            </div>
            <div class="items-form-value">
              {{ item.brandCn }}
            </div>
          </div>
        </div>
        <div
          v-if="getShopText(item) && getShopText(item).length"
          class="item-wrap"
        >
          <div class="items-form">
            <div class="items-form-label">
              商品内容：
            </div>
            <div class="items-form-value">
              <span
                v-for="(shopItem, shopIndex) in getShopText(item)"
                :key="shopIndex"
                class="value-tags"
              >
                {{ shopItem }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    v-if="params.activity"
    class="form-wrap"
  >
    <span class="form-wrap-label">应用活动</span>
    <div class="form-wrap-content flex-row">
      <span
        v-for="(item, index) in params.activity"
        :key="index"
        class="value-tags"
      >
        {{ item.label || '--' }}
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { IRuleSubmitData, IRuleGoodsFormItem } from '../data.d';
const props = defineProps({
  params: {
    type: Object as PropType<IRuleSubmitData>,
    default: () => ({}),
  },
});

const baseFormList: any[] = [
  {
    label: '规则ID',
    field: 'id',
  },
  {
    label: '规则名称',
    value: () => `${props.params.id} - ${props.params.name}`,
  },
  {
    label: '规则描述',
    field: 'detail',
  },
  {
    label: '创建时间',
    field: 'created_at',
  },
  {
    label: '更新时间',
    field: 'updated_at',
  },
  {
    label: '更新人',
    field: 'updated_name',
  },
];

const getCn = (list: string[]) => {
  const str = list.reduce((pre: string, cur: string) => {
    return pre ? pre + '、' + cur : cur;
  }, '');
  return str;
};

const getShopText = (item: IRuleGoodsFormItem) => {
  const strArr: string[] = [];
  const child = item.child || [];
  child.forEach((childItem: any) => {
    const modelCn = childItem.modelCn || [];
    const newCn = childItem.newCn || [];
    const newStr = newCn.join('、');
    modelCn.forEach((modelItem: string) => {
      strArr.push(`${modelItem} ${newStr ? ' | ' + newStr : ''}`);
    });
  });
  return strArr;
};

const numberToString = (number: string) => {
  if (number.match(/\D/) || number.length >= 14) return;
  let zhArray = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']; // 数字对应中文
  let baseArray = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万']; //进位填充字符，第一位是 个位，可省略
  let string = String(number)
    .split('')
    .reverse()
    .map((item, index) => {
      // 把数字切割成数组并倒序排列，然后进行遍历转成中文
      // 如果当前位为0，直接输出数字， 否则输出 数字 + 进位填充字符
      item = Number(item) == 0 ? zhArray[Number(item)] : zhArray[Number(item)] + baseArray[index];
      return item;
    })
    .reverse()
    .join(''); // 倒叙回来数组，拼接成字符串
  string = string.replace(/^一十/, '十'); // 如果以 一十 开头，可省略一
  string = string.replace(/零+/, '零'); // 如果有多位相邻的零，只写一个即可
  return string;
};
</script>

<style lang="less" scoped>
.form-wrap {
  display: flex;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid rgba(6, 21, 51, 0.06);
  .form-wrap-label {
    flex-shrink: 0;
    margin-right: 32px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
  }
  .form-wrap-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    .basis-form {
      display: flex;
      margin-bottom: 8px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .form-label {
        flex-shrink: 0;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
      .form-value {
        color: rgba(6, 21, 51, 0.85);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }
    .order-item {
      margin-bottom: 8px;
      .item-parent {
        display: flex;
      }
      .item-child {
        position: relative;
        display: flex;
        margin-top: 8px;
        padding-left: 32px;
        &::after {
          position: absolute;
          top: 0;
          left: 16px;
          width: 1px;
          height: 100%;
          // margin-top: 4px;
          background: rgba(6, 21, 51, 0.15);
          content: '';
        }
        &:nth-child(n + 2) {
          &::before {
            position: absolute;
            top: -8px;
            left: 16px;
            width: 1px;
            height: 8px;
            background: rgba(6, 21, 51, 0.15);
            content: '';
          }
        }
        &:nth-last-child(1) {
          &::after {
            margin-top: 0;
            margin-bottom: 4px;
          }
          &::before {
            height: 8px;
          }
        }
      }
      .form-label {
        flex-shrink: 0;
        color: rgba(6, 21, 51, 0.65);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
      .form-value {
        color: rgba(6, 21, 51, 0.85);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }
    .goods-item {
      margin-bottom: 24px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .index-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        color: rgba(6, 21, 51, 0.85);
        font-weight: 500;
        .index-tags {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 22px;
          height: 22px;
          margin-right: 8px;
          color: #fff;
          background: #00c8be;
          border-radius: 50%;
        }
      }
      .item-wrap {
        display: flex;
        margin-bottom: 8px;
        &:nth-last-child(1) {
          margin-bottom: 0;
        }
        .items-form {
          display: inline-flex;
          margin-right: 24px;
          &:nth-last-child(1) {
            margin-right: 0;
          }
          .items-form-label {
            display: inline-flex;
            flex-shrink: 0;
            align-items: flex-start;
            color: rgba(6, 21, 51, 0.65);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
          }
          .items-form-value {
            display: flex;
            flex-wrap: wrap;
            color: rgba(6, 21, 51, 0.85);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
          }
        }
      }
    }

    .special-data-text {
      margin-bottom: 8px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }

    .value-tags {
      display: inline-block;
      max-width: 203px;
      margin: 0 8px 8px 0;
      padding: 0 8px;
      overflow: hidden;
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      white-space: nowrap;
      text-overflow: ellipsis;
      background: rgba(6, 21, 51, 0.04);
      border: 1px solid rgba(6, 21, 51, 0.15);
      border-radius: 2px;
    }
  }
}
.flex-row {
  flex-direction: row !important;
  flex-wrap: wrap;
}

.order_num--text {
  display: inline-block;
  margin: 0 8px;
  color: red;
  font-weight: bold;
}
</style>
