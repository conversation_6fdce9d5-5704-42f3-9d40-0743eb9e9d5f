import dayjs from 'dayjs';
import { RTableColumnType } from 'rrz-web-design';
import { apiGetPerformanceTargetList } from './service';

export const getSettingTargetConfig = () => {
  const columns: RTableColumnType[] = [
    {
      title: '月份',
      dataIndex: 'month',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '销售部门',
      dataIndex: 'dept_name',
      hideInSearch: true,
      width: 128,
    },
    {
      title: '业绩目标',
      dataIndex: 'target',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      hideInSearch: true,
      width: 180,
      sorter: (a: Record<string, any>, b: Record<string, any>) => {
        return dayjs(a.updated_at).valueOf() - dayjs(b.updated_at).valueOf();
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 88,
    },
  ];

  const settingTargetTableConfig: Record<string, any> = {
    api: apiGetPerformanceTargetList,
    columns: columns,
    inModal: true,
    search: false,
    autoFetch: false,
    tableProps: { pagination: false },
  };
  return {
    settingTargetTableConfig,
  };
};
