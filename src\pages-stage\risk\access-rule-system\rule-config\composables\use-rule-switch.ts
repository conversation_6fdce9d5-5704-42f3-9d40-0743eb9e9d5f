import { reactive } from 'vue';
import { message } from 'ant-design-vue';
import { apiChangeRuleStatus } from '../services';

export function useRuleSwitch() {
  const switchLoading = reactive({});
  function changeStatus(status: boolean, record: any) {
    switchLoading[record.id] = true;
    apiChangeRuleStatus({
      id: record.id,
      status: status ? 1 : 0,
    })
      .then(() => {
        record.status = status;
        message.success('操作成功');
      })
      .finally(() => {
        switchLoading[record.id] = false;
      });
  }
  return {
    switchLoading,
    changeStatus,
  };
}
