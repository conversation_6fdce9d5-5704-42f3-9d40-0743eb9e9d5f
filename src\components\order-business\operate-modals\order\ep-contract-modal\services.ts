import { GET, POST } from '@/services/api';

/** 获取合同列表 */
export function getThreeContractList(params: any): Promise<any> {
  return GET('/three-contract/list', params);
}

/**
 * 获取合同详情
 * @param params 审批id
 */
export const getContractDetail = (params: { id: string }): Promise<any> => {
  return GET('/three-contract/detail', params);
};

/**
 * 获取备注
 * @param params 上上签合同id
 */
export const getRemarkList = (params: { contract_id: string }): Promise<any> => {
  return GET('/three-contract/sign-log-list', params);
};

/**
 * 授权提醒
 * @param 上上签合同id
 */
export const getCheckAuthRemind = (params: { contract_id: string }): Promise<any> => {
  return GET('/three-contract/check-auth-remind', params);
};

/**
 * 自动签署
 * @param 上上签合同id
 */
export const postAutoSign = (data: { contract_id: string }): Promise<any> => {
  return POST('/three-contract/auto-sign', data);
};
