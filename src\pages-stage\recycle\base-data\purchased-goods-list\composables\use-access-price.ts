import { ref, reactive, computed, nextTick, onMounted } from 'vue';
import { Form, Modal, message } from 'ant-design-vue';
import {
  queryTempalteNameList,
  queryTemplateLogDetail,
  createPurchasedGood,
  updatePurchasedGood,
  searchPurchasedGood,
} from '../service';
import useBoolean from '@/pages-stage/recycle/common/use-boolean';
import useValuationOption from './use-valuation-option';
import { IOption } from '../data';

export default function (props: any, emits: any) {
  const loading = ref(false);
  const submitLoading = ref(false);
  const isCheck = computed(() => props.actionType === 'check');
  const hasBeenModified = ref(false);
  const isEdit = computed(() => props.actionType === 'update');

  // 表单内容
  const state = reactive<{
    id?: string;
    name?: string;
    template_log_id?: string;
    condition_type: string[];
    base_option: IOption[];
    base: number[];
    price_set: any[];
    function_option_set: any[];
    sort?: number;
    min_price: number;
    columns: any[];
    [key: string]: any;
  }>({
    id: undefined,
    name: undefined,
    template_log_id: undefined,
    condition_type: [],
    base_option: [],
    base: [],
    price_set: [],
    function_option_set: [],
    sort: undefined,
    min_price: 0.0,
    columns: [],
  });
  const rules = {
    name: [{ required: true, message: '请输入商品名称' }],
    template_log_id: [{ required: true, message: '请选择评估模板' }],
    base_option: [{ required: true, message: '请选择基础评估项' }],
    sort: [{ required: true, message: '请输入排序' }],
    min_price: [{ required: true, message: '请输入最低回收价' }],
  };
  const { validate, validateInfos, clearValidate } = Form.useForm(state, rules);

  const rowCount = computed(() =>
    state.base_option
      .map((_: Record<string, any>) => _.option)
      .reduce((count: number, option: Record<string, any>[]) => count * option.length, 1),
  );

  // 初始化操作
  onMounted(() => {
    state.name = props.currentCommodity.name;
    state.id = props.currentCommodity.id;
    if (props.actionType !== 'create') {
      loading.value = true;
      searchPurchasedGood(props.currentCommodity.id)
        .then(({ data }: any) => {
          hasBeenModified.value = true;
          state.name = data.name;
          state.sort = data.sort;
          state.min_price = data.min_price || 0;
          state.condition_type = data.condition_type || [];
          state.base_option = data.base_option || [];
          state.function_option_set = data.function_option_set || [];
          state.price_set = data.price_set || [];
          state.template_log_id = data.template_name;
          state._template_log_id = data.template_log_id;
          initTable(true);
        })
        .finally(() => (loading.value = false));
    }
  });

  // 价格趋势面板
  const [chartsPanelVisible, { toggle: toggleChartsPanelVisible }] = useBoolean(false);

  // 估价模板选项
  const templateOptions = ref([]);
  queryTempalteNameList().then(({ data }) => {
    templateOptions.value = data;
  });
  const onTemplateSelect = async (id: string) => {
    try {
      await confirmBeforeEdit();
      handleTemplateChange(id);
    } catch (err) {}
  };
  const handleTemplateChange = (id: string) => {
    state.template_log_id = id;
    queryTemplateLogDetail(id).then(({ data }) => {
      state.condition_type = data.condition_type;
      state.base = [];
      state.base_option = [];
      state.columns = [];
      state.price_set = [];
      state.function_option_set = data.function_option.map(({ is_multi, name, origin_log_id, option }: IOption) => {
        return {
          is_multi,
          name,
          origin_log_id,
          set: option!.map((opt: string) => ({
            option: opt,
            price: undefined,
          })),
        };
      });
      setConditionTypeColumns();
      nextTick(() => {
        clearValidate('base');
      });
    });
  };

  const confirmBeforeEdit = (): Promise<void> =>
    new Promise((resolve, reject) => {
      if (hasBeenModified.value) {
        Modal.confirm({
          title: '将丢失已设置内容，确定操作吗？',
          onOk: () => {
            hasBeenModified.value = false;
            resolve();
          },
          onCancel: () => reject(),
        });
      } else resolve();
    });

  // 基础评估选项
  const { options: baseOptions } = useValuationOption(1);
  // 选中 & 取消基础评估内容
  const onBaseOptionSelect = async (value: number) => {
    try {
      await confirmBeforeEdit();
      const realValue = baseOptions.value.find(_ => _.value == value);
      realValue && state.base_option.push(realValue.real);
      state.base.push(value);
      initTable();
    } catch (err) {}
  };
  const onBaseOptionDeselect = async (value: number) => {
    try {
      await confirmBeforeEdit();
      const index = state.base_option.findIndex(opt => opt.origin_log_id == value);
      state.base_option.splice(index, 1);
      const idx = state.base.indexOf(value);
      state.base.splice(idx, 1);
      initTable();
    } catch (err) {}
  };
  // 删除基础评估项
  const onDeleteOption = async (origin_log_id: number, index: number) => {
    const raw_index = state.base.indexOf(origin_log_id);
    try {
      await confirmBeforeEdit();
      index != -1 && state.base_option.splice(index, 1);
      raw_index != -1 && state.base.splice(raw_index, 1);
      initTable();
    } catch (err) {}
  };

  // 生成表格列
  const setConditionTypeColumns = (flag?: boolean) => {
    state.condition_type.forEach((title: string, i: number) => {
      const dataIndex = 'type' + ++i;
      state.columns.push({
        title,
        dataIndex,
        width: 110,
      });
      !flag &&
        state.price_set.forEach((row: { [x: string]: undefined }) => {
          row[dataIndex] = undefined;
        });
    });
  };

  /**
   * Description 初始化可编辑表格
   * @param {any} flag 是否初始化数据
   * @returns {any}
   */
  const initTable = (flag?: boolean) => {
    state.columns = [];
    if (!flag) {
      state.price_set = state.base_option.length ? new Array(rowCount.value).fill(0).map(() => ({})) : [];
    }
    // 基础评估项设置
    const base_option = JSON.parse(JSON.stringify(state.base_option)).reverse();
    base_option.reduce((pre: number, cur: any, i: number, origin: any[]) => {
      const dataIndex = 'base' + (Math.abs(i + 1 - origin.length) + 1);
      state.columns.unshift({
        title: cur.title,
        dataIndex,
        width: 110,
        rowSpan: pre,
        customCell: (_: any, index: number) => {
          let rowSpan = pre;
          rowSpan = index % pre === 0 ? pre : 0;
          return { rowSpan };
        },
      });
      !flag &&
        state.price_set.forEach((row: Record<string, any>, line: number) => {
          row[dataIndex] = cur.option[Math.floor(line / pre) % cur.option.length];
        });
      return cur.option.length * pre;
    }, 1);
    setConditionTypeColumns(flag);
  };

  // 复用上一行数据填充
  const autoFillWithPreRow = (index: number, column: any) => {
    for (let current = index; current < index + column.rowSpan; current++) {
      state.columns.forEach(({ dataIndex }) => {
        if (dataIndex.includes('type')) {
          state.price_set[current][dataIndex] = state.price_set[current - column.rowSpan][dataIndex];
        }
      });
    }
  };

  // 操作按钮
  const onClose = () => emits('update:visible', false);
  const onConfirm = () => {
    validate().then(() => {
      submitLoading.value = true;
      let { template_log_id, base_option, price_set, function_option_set } = state;
      const { id, sort, min_price } = state;
      base_option = base_option.map(({ origin_log_id }: any, idx: number) => ({ origin_log_id, sort: idx + 1 }));
      // 格式化价格设置和填充默认值0
      function_option_set = function_option_set.map(({ origin_log_id, set }: any) => {
        set.forEach((row: any) => {
          if (row.price === undefined) row.price = 0;
        });
        return { origin_log_id, set };
      });
      price_set = price_set.map((row: any) => {
        Object.keys(row).forEach((key: string) => {
          if (row[key] === undefined) row[key] = 0;
        });
        return row;
      });
      template_log_id = isNaN(+template_log_id!) ? state._template_log_id : template_log_id;
      const api = isEdit.value ? updatePurchasedGood : createPurchasedGood;
      const payload = { template_log_id, sort, min_price, base_option, price_set, function_option_set };
      api({
        [isEdit.value ? 'id' : 'base_goods_id']: +id!,
        ...payload,
      })
        .then(() => {
          message.success(isEdit.value ? '保存成功' : '创建成功');
          onClose();
          emits('onLoadList');
        })
        .finally(() => {
          submitLoading.value = false;
        });
    });
  };

  return {
    loading,
    submitLoading,
    isCheck,
    isEdit,
    hasBeenModified,

    state,
    rules,
    validateInfos,

    chartsPanelVisible,
    toggleChartsPanelVisible,

    templateOptions,
    onTemplateSelect,

    baseOptions,
    onBaseOptionSelect,
    onBaseOptionDeselect,
    onDeleteOption,

    autoFillWithPreRow,

    onClose,
    onConfirm,
  };
}
