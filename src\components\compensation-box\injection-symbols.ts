/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2025-05-14 16:41:58
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2025-05-14 22:14:22
 * @FilePath: src/components/compensation-box
 * @Description: 注入符号
 */
import type { InjectionKey, Ref } from 'vue';

export const isCanEditPlatformSymbol = Symbol('是否可以修改平台赔付金额') as InjectionKey<Ref<boolean>>;

export const isCanEditUserPaySymbol = Symbol('是否可以修改赔付金额') as InjectionKey<Ref<boolean>>;

export const isCanEditAllSymbol = Symbol('是否可以修改所有') as InjectionKey<Ref<boolean>>;

export const isFromOrderListSymbol = Symbol('是否从订单列表中打开') as InjectionKey<Ref<boolean>>;

export const isEditStatusSymbol = Symbol('是否处于编辑状态') as InjectionKey<Ref<boolean>>;

export const orderIdSymbol = Symbol('订单id') as InjectionKey<Ref<string>>;

export const detailPropsSymbol  = Symbol('详情数据') as InjectionKey<Ref<any>>;

export const damageExpansionInfoSymbol = Symbol('赔付单扩展数据') as InjectionKey<Ref<any>>;
