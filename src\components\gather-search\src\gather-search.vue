<template>
  <div class="gather-search">
    <!-- 搜索栏筛选 -->
    <a-skeleton
      active
      :loading="loading"
      :paragraph="{ rows: 3 }"
    >
      <SearchBox
        ref="searchBoxRef"
        v-model:form-data="bindFormData"
        class="search-box"
        :form-dependencies="formDependencies"
        :form-list-group="bindFormGroup"
        :form-origin-props="formOriginProps"
        :form-target-height="formTargetHeight"
        @key-enter="onSearchForm"
      >
        <!-- 继承插槽 -->
        <template
          v-for="(_index, name) in slots"
          #[name]="slotData"
        >
          <slot
            :name="name"
            :search-form="bindFormData"
            v-bind="slotData ? slotData : {}"
          />
        </template>

        <template #left>
          <!-- 根据路由参数显示搜索标签 -->
          <QueryTags
            :query-group="queryGroup"
            :query-value="bindRouteData"
          />

          <a-space>
            <!-- 搜索按钮 -->
            <a-button
              :disabled="searchDisabled"
              type="primary"
              @click="onSearchForm(ESearchAction.Search)"
            >
              查询
            </a-button>
            <!-- 重置按钮 -->
            <a-button
              :disabled="searchDisabled"
              @click="onResetForm"
            >
              重置
            </a-button>
            <slot name="operate" />
            <!-- 搜索历史控件 -->
            <HistoryControl
              v-if="history"
              @change="onSearchHistory"
            />
          </a-space>
        </template>

        <template #right>
          <slot name="application" />
        </template>
      </SearchBox>

      <!-- 标签栏筛选 -->
      <a-tabs
        :active-key="bindTabValue"
        class="search-tabs"
        @change="onChangeTab"
      >
        <a-tab-pane
          v-for="item in tabsGroup"
          :key="item.value"
          :disabled="tabDisabled"
        >
          <template #tab>
            <slot
              :item="item"
              name="tab"
            >
              {{ item.label }}
            </slot>
          </template>
        </a-tab-pane>
        <template #rightExtra>
          <slot name="tabsRightExtra" />
        </template>
      </a-tabs>
    </a-skeleton>

    <!-- 内容 -->
    <slot name="content" />

    <!-- 页码 -->
    <ListPagination
      v-if="pagination !== false"
      class="pagination"
      :pagination="bindPagination"
      :show-page-count="bindShowPageCount"
      @change-page="onChangePage"
      @change-show-page-count="onChangeShowPageCount"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, PropType, ref, toRef, useSlots } from 'vue';

import HistoryControl from './components/history-control.vue';
import ListPagination from './components/list-pagination.vue';
import QueryTags from './components/query-tags.vue';
import SearchBox from './components/search-box.vue';
import { useFormGroup } from './composables/use-form-group';
import { useSearchAction } from './composables/use-search-action';
import type { IPagination, IQueryGroupItem, ITabGroupItem, TFormGroup, TObject } from './data';
import { ESearchAction } from './typing';

const props = defineProps({
  /** 表单组 */
  formGroup: {
    type: Array as PropType<TFormGroup[]>,
    default: () => [],
  },
  /** 动态表单依赖项 */
  formDependencies: {
    type: Object as PropType<Record<string, any[]>>,
    default: () => ({}),
  },
  /** 路由传参搜索配置 */
  queryGroup: {
    type: Array as PropType<IQueryGroupItem[]>,
    default: () => [],
  },
  /** 标签栏配置 */
  tabsGroup: {
    type: Array as PropType<ITabGroupItem[]>,
    default: () => [],
  },
  /** 搜索表单数据 */
  formData: {
    type: Object as PropType<TObject>,
    default: () => ({}),
  },
  /** pagination.type为'control'时生效，是否显示页码信息 */
  showPageCount: {
    type: Boolean,
    default: undefined,
  },
  /** 搜索历史控制 */
  history: {
    type: Boolean,
    default: true,
  },
  /** 搜索禁用 */
  searchDisabled: {
    type: Boolean,
    default: false,
  },
  /** tab选择值 */
  tabValue: {
    type: [String, Number],
    default: undefined,
  },
  /** tab禁用 */
  tabDisabled: {
    type: Boolean,
    default: false,
  },
  /** 分页配置 */
  pagination: {
    type: [Object, Boolean] as PropType<IPagination | false>,
    default: () => ({
      current: 1,
      pageSize: 10,
    }),
  },
  /** 加载搜索表单 */
  loading: {
    type: Boolean,
    default: false,
  },
  formOriginProps: {
    type: Object,
    default: () => ({}),
  },
  formTargetHeight: {
    type: Number,
    default: undefined,
  },
});

const emit = defineEmits<{
  (e: 'update:formData', value: TObject): void;
  (e: 'update:tabValue', value: string | number);
  (e: 'update:showPageCount', value: boolean);
  (e: 'search', value: TObject, action: ESearchAction): void;
}>();

const slots = useSlots();

// 表单控件
const { bindFormGroup } = useFormGroup(toRef(() => props.formGroup));

const searchBoxRef = ref<InstanceType<typeof SearchBox>>();

const {
  bindFormData,
  bindPageSize,
  bindCurrent,
  bindShowPageCount,
  bindTabValue,
  bindRouteData,
  onResetCurrent,
  onSearchForm,
  onResetForm,
  onChangeTab,
  onChangePage,
  onChangeShowPageCount,
  onSearchHistory,
} = useSearchAction({ props, emit, searchBoxRef });

const bindPagination = computed<IPagination>(() => ({
  current: bindCurrent.value,
  pageSize: bindPageSize.value,
  total: props.pagination?.total,
  type: props.pagination?.type,
}));

defineExpose({
  onResetCurrent,
  onSearchForm,
  onResetForm,
  bindFormData,
  bindPagination,
});
</script>

<style lang="less" scoped>
.gather-search {
  display: flex;
  flex-direction: column;

  :deep(.ant-skeleton-title) {
    display: none;
  }

  :deep(.ant-skeleton-paragraph) {
    margin-top: 0;
  }

  .search-box {
    margin-bottom: 12px;
    padding: 0 24px;
  }

  .search-tabs {
    padding: 0 24px;

    :deep(.ant-tabs-nav) {
      margin: 0;
    }
  }

  .pagination {
    position: sticky;
    bottom: 0;
    box-shadow: 0 -1px #f0f0f0;
  }

  // 隐藏输入框的上下箭头
  :deep(.search-box) {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }

    input[type='number'] {
      -moz-appearance: textfield;
    }
  }
}
</style>
