<template>
  <a-modal
    v-model:visible="visible"
    :body-style="{ paddingBottom: 0 }"
    destroy-on-close
    title="押金冻结方式设置"
    width="480px"
    @cancel="close"
    @ok="confirm"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="formState"
      >
        <a-form-item
          :extra="payWayOption.find(item => item.value === formState.secret_value)?.extra"
          label="押金冻结方式"
          name="secret_value"
        >
          <a-select
            v-model:value="formState.secret_value"
            :options="payWayOption"
            placeholder="请选择"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { useModal } from '@/hook/component/use-modal';
import { message } from 'ant-design-vue';
import { reactive } from 'vue';
import { Form } from 'ant-design-vue';
import { apiChangePayWay, apiGetPayWay } from '../services';

const { open, visible, close, loading, confirm } = useModal(
  async () => {
    if (formState.secret_value === void 0) {
      message.error('请选择押金冻结方式');
      return;
    }
    await apiChangePayWay(formState);
    message.success('操作成功');
    close();
  },
  {
    afterClose: () => {
      resetFields();
    },
    afterOpen: async () => {
      try {
        loading.value = true;
        const res = await apiGetPayWay();
        formState.secret_value = res.data.secret_value;
      } finally {
        loading.value = false;
      }
    },
  },
);

const payWayOption = [
  {
    label: '支付宝账号自主冻结',
    value: '2',
    extra: '支付宝账号自主冻结，是使用自有支付宝账号冻结，用户下单直接支付押金。',
  },
  {
    label: '支付宝资金预授权冻结',
    value: '1',
    extra: '支付宝资金预授权冻结，是借用支付宝冻结工具对用户押金进行冻结。',
  },
];

const formState = reactive({
  secret_value: void 0,
});

const { resetFields } = Form.useForm(formState);

defineExpose({
  open,
});
</script>
