import { ref } from 'vue';
import { getOrderSaleDetail } from '../service';

type IInfo = {
  // 销售数量
  sale_num: number;
  // 销售金额
  sale_price: number;
  // 出库数量
  out_warehouse_num: number;
  // 出库金额
  out_warehouse_price: number;
  // 退货数量
  return_goods_num: number;
  // 退货金额
  return_goods_price: number;
  // 毛利
  gross_profit: number;
};

export default function () {
  const info = ref<Partial<IInfo>>({});

  const init = async () => {
    const { data } = await getOrderSaleDetail();
    info.value = data;
  };

  init();

  return {
    info,
  };
}
