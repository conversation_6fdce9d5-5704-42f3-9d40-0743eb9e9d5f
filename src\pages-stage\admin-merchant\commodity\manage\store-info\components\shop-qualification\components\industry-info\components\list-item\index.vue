<template>
  <div class="back-img">
    <img :src="releaseStatusColorMap[item.release_status].img">
  </div>
  <div class="info-item-content">
    <span class="info-item-name">{{ currentIndex }} - 行业</span>
    <a-space>
      <span :class="[statusColorMap[item.status]]">{{ item.status_label }}</span>
      <span>({{ item.release_status_label }})</span>
    </a-space>
  </div>
  <div class="info-item-line">
    <a-space
      :size="[0, 8]"
      wrap
    >
      <span class="info-item-cate">行业类目：</span>
      <a-tag>
        {{ item.secondary_category_name }}
      </a-tag>
    </a-space>
    <a-space>
      <span
        v-if="item.release_status !== ECredentialAuditStatus.Pass"
        class="info-item-detail"
        @click="goDetail(item, EHandleType.DETAIL, ECurrentDataType.RELEASE)"
      >审核记录
      </span>
      <span
        v-if="item.release_status !== ECredentialAuditStatus.WaitAudit"
        class="info-item-detail"
        @click="
          goDetail(
            item,
            EHandleType.EDIT,
            item.release_status === ECredentialAuditStatus.Reject
              ? ECurrentDataType.RELEASE
              : ECurrentDataType.CURRENTUSE,
          )
        "
      >修改
      </span>
      <a-popconfirm
        title="确认删除该资质？"
        @confirm="del(item)"
      >
        <span
          v-if="item.release_status === ECredentialAuditStatus.Pass && item.status === ECredentialStatus.Usable"
          class="info-item-detail"
        >删除
        </span>
      </a-popconfirm>
      <span
        class="info-item-detail"
        @click="goDetail(item, EHandleType.DETAIL, ECurrentDataType.CURRENTUSE)"
      >查看<RightOutlined />
      </span>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { message } from 'ant-design-vue/es';
import { RightOutlined } from '@ant-design/icons-vue';

import {
  ECurrentDataType,
  EHandleType,
} from '@/pages-stage/admin-merchant/commodity/manage/store-info/components/shop-qualification/data.d';
import { ECredentialAuditStatus, ECredentialStatus } from '@/pages-stage/server/audit/qualification/data.d';

import { releaseStatusColorMap } from '../../config';
import { delIndustryInfo } from '../../service';
import { statusColorMap } from './config';
const props = defineProps<{
  item: Record<string, any>;
  index: number;
  page: { current: number; pageSize: number };
}>();
const emits = defineEmits(['goDetail', 'refush']);

const currentIndex = computed(() => {
  return props.index + 1 + (props.page.current - 1) * props.page.pageSize;
});

async function del(info: Record<string, any>) {
  await delIndustryInfo({ id: info.id });
  message.success('删除成功');
  emits('refush');
}
function goDetail(info: Record<string, any>, type: EHandleType, dataType: ECurrentDataType) {
  emits('goDetail', info, type, dataType);
}
</script>

<style scoped lang="less">
.back-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  img {
    width: 70px;
    height: 70px;
  }
}
.info-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  .success {
    color: #3777ff;
  }
  .processing {
    color: #faad14;
  }
  .reject {
    color: #ff4d4f;
  }
  .info-item-name {
    z-index: 3;
    color: rgba(6, 21, 51, 0.85);
  }
}
.info-item-line {
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 400;
  font-size: 14px;
  .info-item-cate {
    color: rgba(6, 21, 51, 0.45);
  }
  .info-item-detail {
    color: rgba(6, 21, 51, 0.25);
    cursor: pointer;
  }
}
</style>
