export interface EditData {
  data: string;
  reason: string;
  status: number;
}

export interface SelectCategory {
  water: string;
}
export interface BaseInfoInterface {
  address: string;
  address_type: string;
  behalf_user: string;
  can_apply_choice: string;
  category_list: string[];
  city: string;
  company: string;
  company_images: string[];
  company_name: string;
  contact_user: string;
  edit_data: EditData;
  fax: string;
  follow_up_staff: string;
  follow_up_staff_id: string;
  head_image: string;
  imgOssServer: string;
  invoice: string;
  legal_idcard: string;
  legal_person_idcard: string;
  legal_person_other_idcard: string;
  legal_person_phone: string;
  license_image: string;
  license_no: string;
  provinces: string;
  real_address: string;
  select_category: SelectCategory;
  server_id: string;
  status: string;
  tellphone: string;
  type: string;
  updated_at: string;
  video: string;
}

export type DataModal = Partial<Record<any, string>>;
// export interface DataModal {
// }

export interface IGoodsNumInfo {
  data: DataModal;
  goods_num: number;
}
