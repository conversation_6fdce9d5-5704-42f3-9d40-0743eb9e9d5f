import { ref } from 'vue';
import { useRoute } from 'vue-router';

import type { IStrOrNumOption } from '@/components/order-business/typing';
import { createGlobalRequest } from '@/hook';

import { fetchMerShipperOptions, fetchShipperOptions } from './services';

const { getData: getSuperData } = createGlobalRequest(fetchShipperOptions);
const { getData: getMerData } = createGlobalRequest(fetchMerShipperOptions);

/** 订单列表选项数据 */
export function useLogisticOption() {
  const isSuper = useRoute().query.role === 'super';
  const getOptionData = isSuper ? getSuperData : getMerData;

  /** 特殊寄送方式 */
  const specialOptions = [
    { label: '上门自取', value: -1 },
    { label: '上门安装', value: -2 },
    { label: '随机快递', value: 'random' },
  ];
  /** 寄送方式 */
  const logisticOptions = ref<IStrOrNumOption[]>([]);
  /** 快递公司 */
  const shipperOptions = ref<IStrOrNumOption[]>([]);
  /** 加载中 */
  const loading = ref(true);

  getOptionData()
    .then(res => {
      shipperOptions.value = res?.data || [];
      logisticOptions.value = specialOptions.concat(res?.data || []);
    })
    .finally(() => {
      loading.value = false;
    });

  return {
    loading,
    logisticOptions,
    shipperOptions,
  };
}
