import { GET, POST } from '@/services/api';

export function getFormFilter() {
  return GET('/common/form-filter?page_from=batchOrderBill_index');
}

// 获取table数据
export function getTableList(params: any) {
  return GET('/super/recycle/server-bill/list', params);
}

// 获取列表备注数据
export function getCrmListApi(params: any) {
  return POST('/crm/data', params);
}

// 获取收款类型下拉
export function getBillPayFilter() {
  return GET('/super/recycle/server-bill/bill-pay-filter');
}

// 结算
export function settlement(params: any) {
  return POST('/super/recycle/server-bill/batch-save-bill-pay', params);
}
//导出
export function exportList(params: any) {
  return GET('/super/recycle/server-bill/export', params);
}
//结算
export function serverBillPay(params: any) {
  return POST('/super/recycle/server-bill/pay', params);
}

// 企业回收结算 - 趣回收
export function getZfbDataCount(params: any) {
  return GET('/super/recycle/server-bill/zfb-data-count', params);
}

// 企业回收结算 - 供应链
export function getWarehouseDataCount(params: any) {
  return GET('/super/recycle/server-bill/warehouse-data-count', params);
}
