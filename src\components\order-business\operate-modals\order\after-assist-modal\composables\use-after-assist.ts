import { createVNode, h, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { CloseCircleFilled, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
import { safeJsonStringify } from '@rrzu/utils';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { operateModal } from '@/components/order-business/operate-modals';
import { useUnityModal } from '@/components/unity-modal';
import { deepClone } from '@/utils/base';

import { AfterAssistForm, AfterAssistInfoType, IFreezeData, IReletTipsData } from '../data';
import { EAssistType } from '../data.d';
import {
  ApiCheckRenwalData,
  ApiGetPeriods,
  appealClosing,
  applyRefund,
  applyRenewal,
  changeBillingCycle,
  changeSendApi,
  checkCompleteReplenishOrderApply,
  checkOrderContinue,
  checkRemindApi,
  checkRemindApiMerch,
  checkWorkOrderCreateApi,
  completeReplenishOrderApply,
  depositTransferPayment,
  extendOrderEndApply,
  freeDepositForPayment,
  getOrderDetail,
  getPeriodsV2Api,
  getRenewBuyoutPrice,
  isCanSendApply,
  modifyBillAmount,
  updateBillPaymentStatus,
  updateOrderReturnStatus,
  updateOrderTotalRent,
} from '../services';

type AfterAssistType = {
  visible: boolean;
  freezeTransPayDetailVisible: boolean;
  /** 冻结转支付说明弹框类型，目前为 条件/规则 */
  freezeTransPayType: 'condition' | 'rule';
  loading: boolean;
  showModal(type: number): void;
  info: AfterAssistInfoType;
  data: AfterAssistForm;
  submit(form: AfterAssistForm): void;
  changeRenwalData(data: any): void;
  checkRenwalData(): void;
  checkPreview(): void;
  beforeSubmit(form: AfterAssistForm): void;
  freezeTransPayDetailShow(data: 'condition' | 'rule'): void;
  reletTipsVisible: boolean;
  reletTipsData: IReletTipsData;
  freezeData: IFreezeData;
  buttonLoading: boolean;
};
export default function (props: any, emit: any) {
  const titleMap = {
    1: '申请续租',
    2: '申请退款',
    3: '修改账单金额申请',
    5: '申请更换账单期数',
    7: '修改账单支付状态',
    4: '修改订单总租金',
    6: '修改订单归还状态',
    11: '冻结押金转支付',
    12: '补订单完结申请',
    13: '申请延长完结',
    15: '结算比例申诉',
    16: '修改寄出方式',
  };

  const apiMap = {
    1: applyRenewal,
    2: applyRefund,
    3: modifyBillAmount,
    5: changeBillingCycle,
    7: updateBillPaymentStatus,
    4: updateOrderTotalRent,
    6: updateOrderReturnStatus,
    11: freeDepositForPayment,
    12: completeReplenishOrderApply,
    13: extendOrderEndApply,
    15: appealClosing,
    16: changeSendApi,
  };

  const sensorsTrackKeyMap = {
    [EAssistType.changeBillPeriod]: '$OrderChangeBillPeriodNumClick',
    [EAssistType.modifyOrderTotalRental]: '$OrderModiftTotalRentClick',
    [EAssistType.extendOrder]: '$OrderExtendOrderClick',
  };

  const honourMap = {
    '0': '距离到期大于7天',
    '-1': '距离到期0-7天',
    '1': '逾期0-7天',
    '7': '逾期7-35天',
    '35': '逾期35-65天',
    '65': '逾期65天以上',
  };

  const afterAssist = reactive<AfterAssistType>({
    visible: false,
    freezeTransPayDetailVisible: false,
    freezeTransPayType: 'condition',
    freezeTransPayDetailShow,
    loading: false,
    buttonLoading: false,
    showModal,
    info: {},
    data: {
      channel: 1,
      proof_img: [],
      proof_audio: [],
      data: {},
      feedback: '',
    },
    submit,
    changeRenwalData,
    checkRenwalData,
    checkPreview,
    beforeSubmit,
    reletTipsVisible: false,
    reletTipsData: {},
    freezeData: {},
  });

  const route = useRoute();

  const currentType = ref();

  const { onToggleComponent } = useUnityModal();

  //申请更换账单期数
  watch(
    () => afterAssist.data.updated_period,
    val => {
      if (!val) return;
      const length = val.length;
      if (!length || afterAssist.info.type !== EAssistType.changeBillPeriod) return;
      const { before_period, after_period } = val[length - 1];
      if (before_period && after_period) {
        val.push({ before_period: undefined, after_period: undefined });
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );

  // 申请续租的data
  function changeRenwalData(data: any) {
    afterAssist.data.data.renewal_time_start = data.renewal_time_start;
    afterAssist.data.data.renewal_time_end = data.renewal_time_end || data.renewal_time_start;

    const diffDay =
      dayjs(afterAssist.data.data.renewal_time_end).diff(afterAssist.data.data.renewal_time_start, 'day') + 1;

    afterAssist.data.data.renewal_rental_money = data.rental_money;
    afterAssist.data.data.renewal_total_rental_money = Math.floor(data.rental_money * diffDay * 100) / 100;
    afterAssist.info.diffDay = diffDay;
  }

  // 结算比例申诉
  function closingRatio(data: any) {
    const ori_order_ratio = Number(data.base_info.ori_order_ratio || 0) * 100;
    afterAssist.data.data.order_ratio = ori_order_ratio;
    afterAssist.data.data.ori_order_ratio = ori_order_ratio;
  }

  // 修改寄出方式和运费
  function changeSend() {
    // 寄出方式
    afterAssist.data.data.send_type = undefined;
    afterAssist.data.data.edit_send_json_data = '';
  }

  function checkRenwalData() {
    ApiCheckRenwalData({
      order_id: props.orderId,
      end_date: afterAssist.data.data.renewal_time_end,
      renew_total_rental: afterAssist.data.data.renewal_total_rental_money,
    }).then(res => {
      afterAssist.info.is_pass = res.data.is_pass;
      afterAssist.info.err_msg = res.data.err_msg;
    });
  }

  function checkPreview() {
    getRenewBuyoutPrice({
      order_id: props.orderId,
      end_date: afterAssist.data.data.renewal_time_end,
    }).then(res => {
      afterAssist.info.buyout_need_pay_data = res.data.buyout_need_pay_data.map((item: any) => ({
        ...item,
        start_at: dayjs.unix(item.start_at).format('YYYY-MM-DD'),
        end_at: dayjs.unix(item.end_at).format('YYYY-MM-DD'),
      }));
      afterAssist.info.preview_is_pass = res.data.rental_gt_buyout_check.is_pass;
      afterAssist.info.preview_err_msg = res.data.rental_gt_buyout_check.msg;
    });
  }

  function changeRefundData(data: any) {
    afterAssist.data.data.refund_money = data.most_refund_money;
    afterAssist.data.data.most_refund_money = data.most_refund_money;
  }

  function changeRentalTotal(data: any) {
    afterAssist.data.data.total_rental_money = data.total_rental_money;
    afterAssist.data.data.deposit_need_pay = data.deposit.need_pay;
  }

  // 设置修改金额数组
  function setBillArray() {
    afterAssist.data.data.bill_array_list = [];
  }

  function freezeTransPay() {
    afterAssist.data.data.transfer_money = afterAssist.freezeData.max_can_deposit_pay;
    afterAssist.data.data.transfer_type = '10';
    afterAssist.data.data.bill_ids = undefined;
  }

  async function getPeriodsV2() {
    const { data } = await getPeriodsV2Api({ order_id: props.orderId });
    const { period_num_list = [], un_period_num_list = [] } = data;
    if (period_num_list?.length > 0 && un_period_num_list?.length > 0) {
      afterAssist.info.period_num_list = period_num_list;
      afterAssist.info.un_period_num_list = un_period_num_list;
    }
  }

  function getPeriods(status?: number) {
    afterAssist.loading = true;
    const data: Record<string, number> = { order_id: props.orderId };
    if (status !== undefined) {
      data.status = status;
    }
    ApiGetPeriods(data)
      .then(res => {
        afterAssist.info.periodOptions = res.data.period_num_list || [];
      })
      .finally(() => {
        afterAssist.loading = false;
      });
  }

  const getBuyOut = (data?: any) => {
    getRenewBuyoutPrice({
      order_id: props.orderId,
      end_date: data.renewal_time_end || data.renewal_time_start,
    }).then(res => {
      afterAssist.info.buyout_need_pay_data = res.data.buyout_need_pay_data.map((item: any) => ({
        ...item,
        start_at: dayjs.unix(item.start_at).format('YYYY-MM-DD'),
        end_at: dayjs.unix(item.end_at).format('YYYY-MM-DD'),
      }));
      afterAssist.info.preview_is_pass = res.data.rental_gt_buyout_check.is_pass;
      afterAssist.info.preview_err_msg = res.data.rental_gt_buyout_check.msg;
    });
  };

  const getDetail = async (type: number) => {
    // 当订单为【资产入池】订单时，租后协助里类型为【修改账单金额】【更换账单租期】禁止使用
    if (props.extraData.is_asset_order && (type === 3 || type === 5)) {
      return message.error('当前订单为【资产入池】订单，不支持使用该功能');
    }
    const hasError = await checkBeforeGetDetail(type);
    if (hasError) return;
    afterAssist.visible = true;
    // 重置一下
    afterAssist.data = {
      channel: 1,
      proof_img: [],
      proof_audio: [],
      data: {},
      feedback: '',
    };
    afterAssist.loading = true;
    getOrderDetail({ order_id: props.orderId })
      .then(res => {
        afterAssist.info = {
          ...res.data,
          time_date: res.data.time.start + '~' + res.data.time.end,
          rental_type_text: res.data.rental_type == '0' ? '短租' : '长租',
          honour_type_text: honourMap[res.data.overdue],
          work_type_text: titleMap[type],
          type,
          deposit_amount: res.data.deposit.amount,
        };
        afterAssist.data.type = type;
        afterAssist.data.order_id = props.orderId;
        afterAssist.info.type === 1 && changeRenwalData(res.data);
        afterAssist.info.type === 1 && checkRenwalData();
        afterAssist.info.type === 1 && getBuyOut(res.data);
        afterAssist.info.type === 2 && changeRefundData(res.data);
        afterAssist.info.type === 3 && setBillArray();
        afterAssist.info.type === 4 && changeRentalTotal(res.data);
        afterAssist.info.type === 11 && freezeTransPay(res.data);
        afterAssist.info.type === 11 && getBuyOut(res.data);
        afterAssist.info.type === 15 && closingRatio(res.data);
        afterAssist.info.type === EAssistType.changeSend && changeSend();

        getPeriods(afterAssist.info.type === 3 ? 1 : undefined);
        if (afterAssist.info.type === EAssistType.changeBillPeriod) {
          getPeriodsV2();
          afterAssist.data.updated_period = [{ before_period: undefined, after_period: undefined }];
        }
      })
      .finally(() => {
        afterAssist.loading = false;
      });
  };
  const checkType = {
    // 冻结押金转支付验证
    11: async function (resolve: (value: string | boolean) => void) {
      try {
        afterAssist.buttonLoading = true;
        const res = await depositTransferPayment(props.orderId);
        if (Array.isArray(res.data?.bad_bill)) {
          res.data.bad_bill.forEach(item => {
            item.period_num = `第${item.period_num}期`;
          });
        }

        afterAssist.freezeData = res.data;
        // 判断是否有错
        const isError = Array.isArray(res.data) && res.data.length === 0;
        if (isError) {
          afterAssist.buttonLoading = false;
          const errorContent = h('div', [
            h('div', '符合条件规则'),
            h('div', '① 订单逾期：租赁到期超15天'),
            h('div', '② 账单逾期：存在逾期账单即符合，但存在预收账单租金时，逾期租金必须大于预收租金'),
            h('div', { style: { color: 'red' } }, 'ps：同时存在按账单逾期逻辑走'),
          ]);
          resolve('该订单不符合押金转支付规则，无法申请！');
          return Modal.error({
            width: 461,
            title: '该订单不符合押金转支付规则，无法申请！',
            okText: '我知道了',
            content: errorContent,
            icon: createVNode(CloseCircleFilled),
            maskClosable: true,
          });
        }
        afterAssist.buttonLoading = false;
        resolve(false);
      } catch (e) {
        afterAssist.buttonLoading = false;
      }
    },
    [EAssistType.changeSend]: async function (resolve: (value: string | boolean) => void) {
      isCanSendApply({ order_id: props.orderId, type: 16 })
        .then(() => {
          resolve(false);
        })
        .catch(err => {
          resolve(err);
        });
    },
  };

  function checkBeforeGetDetail(type: number) {
    return new Promise(resolve => {
      if (checkType[type]) {
        checkType[type](resolve);
      } else {
        resolve(false);
      }
    });
  }

  function freezeTransPayDetailShow(type: 'condition' | 'rule') {
    afterAssist.freezeTransPayDetailVisible = true;
    afterAssist.freezeTransPayType = type;
  }

  async function showModal(type: number) {
    const sensorsKey = sensorsTrackKeyMap[type as keyof typeof sensorsTrackKeyMap];
    if (sensorsKey) {
      window.sensors.track({ key: sensorsKey });
    }
    await checkCompleteReplenishOrderApply({
      order_id: props.orderId,
      type,
    });
    // 26、46补租金类型订单完结
    if (props.modalType === 12 && [26, 46].includes(props.extraData.created_by)) {
      onToggleComponent(operateModal.finishOrder, {
        orderId: props.orderId,
        extraData: { orderId: props.orderId, created_by: props.extraData.created_by },
      });
      return;
    }
    currentType.value = type;
    if (type === 1) {
      checkOrderContinue({
        order_id: props.orderId,
      }).then(res => {
        if (res.status === 0) {
          if (!res.data.hasCanContinue) return message.error('该订单不支持续租!');
          if (res.data.hasRenewNotClosed === 0) {
            getDetail(type);
          } else {
            Modal.warning({
              title: '续租提示',
              content: ' 当前订单存在未处理「续租工单」，请先处理「续租工单」再执行续租操作；',
            });
          }
        }
      });
    } else {
      getDetail(type);
    }
  }

  //自动审核-提交前数据格式化及检查
  const beforeSubmitCheck: Record<number, (params: AfterAssistForm, form: AfterAssistForm) => any> = {
    [EAssistType.changeBillPeriod]: (params: AfterAssistForm, form: AfterAssistForm) => {
      const tempPeriod = params.updated_period?.filter(item => item.after_period && item.before_period) || [];
      params.updated_period = safeJsonStringify(tempPeriod);
      params.data.must_zero = 1;
      params.data.account = 1;
      delete params.data.before_period;
      delete params.data.after_period;
      return {
        order_id: props.orderId,
        port: 1,
        type: form.type,
        extra_data: safeJsonStringify({ updated_period: tempPeriod }),
      };
    },
    [EAssistType.extendOrder]: (_: AfterAssistForm, form: AfterAssistForm) => {
      return {
        order_id: props.orderId,
        port: 1,
        type: form.type,
        extra_data: safeJsonStringify({ reason_type: form.data.reason_type }),
      };
    },
  };

  // 提交
  async function submit(form: AfterAssistForm) {
    if (afterAssist.loading) {
      return;
    }
    try {
      afterAssist.loading = true;
      const postParams: AfterAssistForm = {
        ...cloneDeep(form),
        proof_img: form.proof_img.join(','),
        proof_audio: form.proof_audio.join(','),
      };
      if (form.type && typeof beforeSubmitCheck[form?.type] === 'function') {
        const queryData = beforeSubmitCheck[form.type](postParams, form);
        await checkWorkOrderCreateApi(queryData);
      }
      if (form.type === 7) {
        postParams.data.cbs_version = 'v2';
        postParams.data.bill_list = JSON.stringify(postParams.data.bill_list);
      }
      // 补订单完结申请
      if (form.type === 12) {
        postParams.data = {
          refund_money: form.data.refund_money?.toFixed?.(2),
          supple_order_type: props.extraData.created_by,
          money: afterAssist.info.total_rental_money,
        };
      }
      const apiMatch = apiMap[currentType.value];
      const res = await apiMatch(postParams);
      if (res.data?.is_apply_renew_action) {
        afterAssist.reletTipsData = res.data;
        afterAssist.reletTipsVisible = true;
        return;
      }
      if (res.data.number) alertSuccess(res.data.number);
      else message.success(res.message);
      emit('refresh', props.orderId, ['data', 'all_remark', 'super2_remark']);
      afterAssist.visible = false;
      afterSubmit(form);
    } finally {
      afterAssist.loading = false;
    }
  }

  /** 确认退款之前，判断该订单在一天内是否申请过相同的退款金额申请 */
  async function beforeSubmit(form: AfterAssistForm) {
    // 申请退款的
    if (form.type === 2) {
      // 区分商家、运营后台
      const isSuper = route.query?.role === 'super';
      const api = isSuper ? checkRemindApi : checkRemindApiMerch;
      const {
        data: { status, un_finish_refund_work },
      } = await api({ order_id: props.orderId, money: form.refund_rental, type: 1 });
      // 如果是售后退款工单
      if (un_finish_refund_work) {
        Modal.info({
          title: '温馨提示',
          icon: createVNode(ExclamationCircleOutlined),
          content: createVNode('div', { style: 'color:red;' }, un_finish_refund_work.text),
          onOk: () => {
            submit(form);
          },
        });
        return;
      }
      // 有相同退款申请，提示
      if (Number(status) === 1) {
        Modal.confirm({
          title: '温馨提示',
          icon: createVNode(ExclamationCircleOutlined),
          content: createVNode('div', { style: 'color:red;' }, '该订单已申请过一笔相同的退款金额，是否仍继续申请?'),
          onOk: () => submit(form),
          onCancel: () => {
            afterAssist.visible = false;
          },
        });
      } else if (Number(status) === 0) {
        submit(form);
      }
    } else if (form.type === 11) {
      if (form.data.transMoney === '') {
        return message.error('从冻结押金转支付金额为必填');
      }
      // 租金类逾期账单必填
      if (form.data.transfer_type === '10' && (form.data.bill_ids === undefined || form.data.bill_ids.length === 0)) {
        return message.error('逾期账单不可为空');
      }

      const confirmTitle =
        form.data.transfer_type === '10' ? '你确定申请冻结押金转租金吗?' : '你确定申请冻结押金转购买吗？';
      const warnContent =
        form.data.transfer_type === '11'
          ? '为避免用户投诉，若冻结押金转支付后，用户已交金额已达购买价，商家需在提交工单当天将该订单处理完结，否则系统将自动完结订单。'
          : '';
      return Modal.confirm({
        title: confirmTitle,
        okText: '确定',
        icon: createVNode(InfoCircleOutlined),
        content: h('div', [
          h('div', '确定后，一旦处理完成，用户冻结押金会减少，无法撤销！'),
          h('div', { style: { color: 'red' } }, warnContent),
        ]),
        onOk() {
          const submitData = deepClone(form);
          if (submitData.data.bill_ids) {
            submitData.data.bill_ids = submitData.data.bill_ids.join(',');
          }
          if (submitData.data.transfer_type !== '10') {
            // 购买不需要抵扣逾期账单
            delete submitData.data.bill_ids;
          }
          submit(submitData);
        },
      });
    } else if (form.type === 3) {
      const exitNull = form.data.bill_array_list?.some(item => item.bill_money == null);
      if (exitNull) {
        return message.error('存在未输入金额，请输入所有金额');
      }
      const formData = {
        ...form.data,
        bill_list: [],
        period_num: 0,
        bill_money: 0,
        bill_array: JSON.stringify(
          form.data.bill_array_list?.map(item => {
            return {
              ori_bill_money: item.origin,
              bill_money: item.bill_money,
              period_num: item.value,
              bill_id: item.id,
            };
          }),
        ),
      };
      delete formData.bill_array_list;
      submit({
        ...form,
        data: formData,
      });
    } else if (form.type === 15) {
      submit({
        ...form,
        data: {
          order_ratio: form.data.order_ratio! / 100,
        },
      });
    } else {
      submit(form);
    }
  }

  function afterSubmit(form) {
    const afterSubmitEven = {
      11() {
        // 1、转购买情况下才需要判断
        // 2、填入金额等于购买款
        const { transfer_type, transfer_money } = form.data;
        const { is_merchant, buyout_need_pay } = afterAssist.freezeData;
        if (transfer_type === '11' && transfer_money == buyout_need_pay && !is_merchant) {
          Modal.warning({
            content:
              '冻结押金转购买成功，用户已交金额已达购买价；为避免用户投诉，商家需在提交工单当天将该订单处理完结，否则系统将自动完结订单',
          });
        }
      },
    };
    afterSubmitEven[afterAssist.info.type] && afterSubmitEven[afterAssist.info.type]();
  }

  function alertSuccess(id: number) {
    const onClick = () => {
      window.parent.postMessage(
        {
          action: 'jump',
          jump_url: `/work-order/index?action=goDetail&id=${id}`,
        },
        '*',
      );
    };
    Modal.success({
      title: '申请成功',
      content: h('div', [
        '你可前往',
        h('span', { class: 'text-link underlined', onClick }, '商家工单总览'),
        '查看工单审核进度。',
      ]),
    });
  }

  return afterAssist;
}
