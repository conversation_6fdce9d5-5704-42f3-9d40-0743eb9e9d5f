<template>
  <div class="generic-table">
    <RTable
      ref="rTableRef"
      v-bind="{ ...rTableProps }"
      :api="apiSearchTable"
      :columns="columns"
      :group-handler="groupHandler"
    >
      <template #toolBar="scoped">
        <a-button
          v-if="displayBtn?.includes('add')"
          type="primary"
          @click="addForm"
        >
          新增
        </a-button>
        <a-button
          v-if="displayBtn?.includes('export')"
          :loading="exportLoading"
          type="primary"
          @click="exportTable"
        >
          {{ exportLoading ? '正在导出中' : '导出' }}
        </a-button>
        <slot
          name="toolBar"
          v-bind="scoped"
        />
      </template>
      <template #tableBodyCell="scoped">
        <slot
          name="tableBodyCell"
          v-bind="scoped"
        >
          <!-- 这里是对提交的类型数据的处理 -->
          <!-- 统一对文件类型的做处理 -->
          <template v-if="scoped.column.type === EType.File">
            <template v-if="scoped.column.attr.file_type === 'image'">
              <a-space :size="8">
                <a-image
                  v-for="(imgUrl, index) in scoped.record[scoped.column.dataIndex]?.split(',')"
                  :key="index"
                  :src="imgUrl"
                  :width="40"
                />
              </a-space>
            </template>
            <template v-else-if="scoped.column.attr.file_type === 'video'">
              <a-space :size="8">
                <a-button
                  v-for="(videoUrl, index) in scoped.record[scoped.column.dataIndex]?.split(',')"
                  :key="index"
                  @click="preview('video', videoUrl)"
                >
                  <VideoCameraOutlined />
                </a-button>
              </a-space>
            </template>
            <template v-else>
              <a-space :size="8">
                <a-button
                  v-for="(fileUrl, index) in scoped.record[scoped.column.dataIndex]?.split(',')"
                  :key="index"
                  @click="preview('file', fileUrl)"
                >
                  {{ extendedNameUpper(fileUrl) }}
                </a-button>
              </a-space>
            </template>
          </template>
        </slot>
      </template>
    </RTable>
    <GenericForm
      :id="modal.id"
      v-model:open="modal.open"
      :form-id="formId"
      :modal-type="modal.modalType"
      :title-prefix="modal.titlePrefix"
      @success="submitSuccess"
    />
    <RPreview
      v-model:preview="previewModal.open"
      :type="previewModal.type"
      :value="previewModal.value"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { VideoCameraOutlined } from '@ant-design/icons-vue';
import type { RTableInstance } from 'rrz-web-design';
import { RTable } from 'rrz-web-design';

import GenericForm from '../../components/generic-form/generic-form.vue';
import { useGenericTable } from '../../composables/generic-form';
import { EType } from '../../composables/generic-form/config';
import type { IGenericTableProps } from '../../composables/generic-form/data';
import { extendedNameUpper, isImageUrl, isVideoUrl } from '../../composables/generic-form/utils';

const props = withDefaults(defineProps<IGenericTableProps>(), {
  displayBtnColumns: true,
  displayBtn: ['edit', 'add', 'view', 'del', 'export'],
  extraBtnGroup: [],
  extraGroupHandler: {},
  rTableProps: {},
});

const rTableRef = ref<RTableInstance | null>(null);
const route = useRoute();
// 表单id优先使用路由参数，如果没有则使用组件属性
const formId = route.query?.form_id || props?.formId || '';
const { columns, apiSearchTable, groupHandler, modal, exportTable, exportLoading } = useGenericTable(
  formId,
  props,
  rTableRef,
);

function submitSuccess() {
  rTableRef?.value?.getTableList('search');
}
function addForm() {
  modal.open = true;
  modal.modalType = 'add';
  modal.titlePrefix = '新增';
}

const previewModal = reactive({
  open: false,
  type: 'video',
  value: '',
});

function preview(type: string, url: string) {
  switch (type) {
    case 'video':
      openPreviewModal('video', url);
      break;
    case 'file':
      if (isImageUrl(url)) {
        openPreviewModal('img', url);
      } else if (isVideoUrl(url)) {
        openPreviewModal('video', url);
      } else {
        window.open(url, '_blank');
      }
      break;
    default:
      console.warn(`Unknown type: ${type}`);
      break;
  }
}

function openPreviewModal(type: string, value: string) {
  previewModal.open = true;
  previewModal.type = type;
  previewModal.value = value;
}

const rTableMethod = {
  getSearchForm: () => rTableRef.value?.searchForm,
  getTableList: (type?: 'search') => rTableRef.value?.getTableList(type),
  resetFields: () => rTableRef.value?.resetFields(),
  getTablePage: () => rTableRef.value?.getTablePage(),
  getTransformData: () => rTableRef.value?.getTransformData(),
};

defineExpose({
  addForm,
  exportTable,
  rTableMethod,
});
</script>
