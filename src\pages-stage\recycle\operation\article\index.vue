<template>
  <layout-admin-page
    :navs="['趣回收', '运营管理', '文章管理']"
    title="文章管理"
  >
    <template #extra>
      <a-button
        type="primary"
        @click="addActionHanlder"
      >
        <template #icon>
          <plus-outlined />
        </template>
        添加文章
      </a-button>
    </template>
    <div class="container">
      <div class="search">
        <form-create
          ref="searchFormRef"
          v-model:value="searchParams"
          class="search-grid"
          :form-group="searchFormGroup"
          :origin-props="{
            layout: 'inline',
          }"
        >
          <template #buttons>
            <a-button
              style="margin-right: 10px"
              type="primary"
              @click="getTableList"
            >
              查询
            </a-button>
            <a-button @click="resetSearch">
              重置
            </a-button>
          </template>
        </form-create>
      </div>
      <div class="main">
        <a-table
          class="bottom-fix-table"
          :columns="columns"
          :data-source="list"
          :loading="listLoading"
          :pagination="page"
          :scroll="{ x: '100%' }"
          :sticky="true"
          style="margin-top: 12px"
          @change="tableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button
                class="link-btn"
                type="link"
                @click="editActionHandler(record.id)"
              >
                编辑
              </a-button>
              <a-button
                danger
                type="link"
                @click="deleteHandler(record.id)"
              >
                删除
              </a-button>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-switch
                v-model:checked="record.status"
                checked-children="启用"
                checked-value="1"
                un-checked-children="不启用"
                un-checked-value="2"
                @change="statusChange($event, record)"
              />
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 添加趋势 -->
    <article-modal
      v-model:formState="formState"
      v-model:visible="visible"
      :loading="loading"
      :type="type"
      @close="close"
      @confirm="confirm"
      @reset-table="resetSearch"
    />
  </layout-admin-page>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { columns, searchFormGroup } from './config';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useArticle } from './composiable/use-article';
import { useHandler } from './composiable/use-handler';
import ArticleModal from './components/article-modal.vue';

const { list, listLoading, page, getTableList, tableChange, searchParams, deleteHandler, statusChange } = useHandler();
const { visible, editActionHandler, loading, close, confirm, addActionHanlder, type, formState } = useArticle();

const searchFormRef = ref(null);

// 重置搜索
const resetSearch = () => {
  (searchFormRef?.value as any)?.getFormRef().resetFields();
  getTableList();
};

onMounted(() => {
  getTableList();
});
</script>

<style lang="less" scoped>
@import '../../common/base.less';
.search {
  margin: 0 24px;
}
</style>
