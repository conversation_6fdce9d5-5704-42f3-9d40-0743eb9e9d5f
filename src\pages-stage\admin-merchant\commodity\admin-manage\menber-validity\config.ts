import { Rule } from 'ant-design-vue/lib/form';
import { ColumnType } from 'ant-design-vue/lib/table';

import { FormGroupItem } from '@/components/form-create/src/typing';
/**
 * 自定义模态框手机号码校验规则
 */
const validatePhone = async (_rule: Rule, value: string) => {
  if (value.length !== 11) {
    return Promise.reject('请输入11位的手机号码！');
  } else return Promise.resolve();
};

export const searchFormGroup: FormGroupItem[] = [
  {
    key: 'auth_name',
    originProps: { label: '权限名', name: 'auth_name' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
  {
    key: 'user_id_str',
    fragmentKey: 'renderInput',
    originProps: { label: '用户名', name: 'user_id_str' },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
];
/**
 * 表格columns
 */
export const columns: ColumnType[] = [
  {
    title: '序号',
    width: 100,
    dataIndex: 'name',
    key: 'index',
    customRender: ({ index }: { index: number }) => `${index + 1}`,
  },
  {
    title: '用户名',
    dataIndex: 'user_name_list',
    key: 'user_name_list',
    width: 150,
  },
  {
    title: '权限名',
    dataIndex: 'rule_name_list2',
    key: 'rule_name_list2',
  },
  {
    title: '权限有效期',
    dataIndex: 'validity_start',
    customRender: ({ record }) => record.validity_start + ' ~ ' + record.validity_end,
    key: 'validity_start',
    width: 350,
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ record }) => (record.status === '0' ? '停用' : '开启'),
    key: 'status',
    width: 80,
  },
  {
    title: '操作时间',
    dataIndex: 'created_date',
    key: 'created_date',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    align: 'left',
    width: 200,

    customCell: () => {
      return {
        style: {
          //可以定义样式
          'text-align': 'left',
        },
      };
    },
  },
];

/**
 * 模态框表单渲染配置
 */
export const addOrEditFormGroup: FormGroupItem[] = [
  {
    key: 'username',
    fragmentKey: 'renderInput',
    originProps: {
      label: '用户名（账号名称）',
      name: 'username',
      rules: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { max: 30, trigger: 'change', message: '用户名长度不可大于30' },
      ],
    },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
  },
  {
    key: 'phone',
    originProps: {
      label: '手机号（11位手机号）',
      name: 'phone',
      rules: [{ required: true, validator: validatePhone, trigger: 'blur' }],
    },
    elProps: { placeholder: '请输入', allowClear: true, style: { width: '100%' } },
    fragmentKey: 'renderInput',
  },
];
