import { ref, reactive, onMounted } from 'vue';
import { ISearchConfigItem, ITableColumnsItem, IOptions } from '@/components/search-table/src/data.d';
import { apiGetRuleOptions } from './services';

export const ruleOptions = reactive<{ method: IOptions[] }>({
  method: [],
});
const searchFormConfig = ref<ISearchConfigItem[]>([
  {
    label: '渠道名称',
    key: 'title',
    renderType: 'input',
  },
  {
    //渠道绑定
    label: '渠道绑定',
    key: 'method',
    renderType: 'select',
    options: ruleOptions.method,
    renderProps: {
      mode: 'multiple',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
    },
  },
]);
// [规则ID,渠道名称,渠道绑定,修改人,修改时间,状态,操作]
const tbCol: ITableColumnsItem[] = [
  {
    title: '规则ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '渠道名称',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '渠道绑定',
    dataIndex: 'channel_text',
    key: 'channel_text',
  },
  {
    title: '修改人',
    dataIndex: 'updated_by',
    key: 'updated_by',
  },
  {
    title: '修改时间',
    dataIndex: 'updated_at',
    key: 'updated_at',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 210,
  },
];

function getRuleOptions() {
  apiGetRuleOptions().then(res => {
    const { method } = res.data;
    // 删除原有数据加入新数据
    ruleOptions.method.splice(0, ruleOptions.method.length, ...method);
  });
}

export function useConfig() {
  onMounted(() => {
    getRuleOptions();
  });
  return {
    searchFormConfig,
    tbCol,
    ruleOptions,
  };
}
