<template>
  <a-drawer
    v-model:visible="visible"
    class="opening-record"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
    :get-container="false"
    placement="right"
    title="操作记录"
    width="800"
  >
    <a-table
      :columns="recordColumns"
      :data-source="list"
      :loading="loading"
      :pagination="false"
      :scroll="{ x: '100%' }"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import { recordColumns } from '../config';
import { useModal } from '@/hook/component/use-modal';
import { ApiGetRemarks } from '../service';
import { ref } from 'vue';
import { RemarkType } from '../data';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const list = ref<RemarkType[]>([]);
const { open, visible, loading } = useModal(undefined, {
  afterOpen: async () => {
    try {
      loading.value = true;
      const { data } = await ApiGetRemarks({ unionId: `${props.id}_price.decline.recycle` });
      list.value = data;
    } finally {
      loading.value = false;
    }
  },
});

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
@import '../../../common/base.less';
</style>
