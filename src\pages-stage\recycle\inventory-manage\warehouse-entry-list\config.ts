import { ColumnProps } from 'ant-design-vue/lib/table';

export const columns: ColumnProps = [
  {
    dataIndex: 'created_at',
    title: '单据日期',
    width: 188,
  },
  {
    dataIndex: 'od_order_no',
    title: '单据编号',
    width: 146,
  },
  {
    dataIndex: 'category_name',
    title: '品类',
    width: 74,
  },
  {
    dataIndex: 'brand_name',
    title: '品牌',
    width: 74,
  },
  {
    dataIndex: 'model_name',
    title: '型号',
    width: 145,
  },
  {
    dataIndex: 'sku_info',
    title: 'sku信息',
    width: 146,
  },
  {
    dataIndex: 'sku_id',
    title: 'SKU ID',
    width: 126,
  },
  {
    dataIndex: 'device_code',
    title: '串码',
    width: 126,
  },
  {
    dataIndex: 'warehouse_name',
    title: '仓位',
    width: 116,
  },
  {
    dataIndex: 'evaluate_price',
    title: '单位成本(元)',
    width: 114,
  },
  {
    dataIndex: 'order_id',
    title: '源单单号',
    width: 145,
  },
];
