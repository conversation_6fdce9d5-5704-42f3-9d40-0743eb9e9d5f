<template>
  <a-drawer
    v-model:visible="visible"
    destroy-on-close
    placement="right"
    :title="type === 'add' ? '创建定向规则' : '查看定向规则'"
    width="1048px"
    @close="close"
  >
    <VersionSelect
      v-if="type != 'add'"
      ref="version_select"
      v-model:is-edit="isEdit"
      v-model:loading="loading"
      :submit-loading="btnLoading"
      @confirm="onSubmit"
      @on-load="versionOnLoad"
    />

    <DetailForm
      v-show="!loading"
      ref="detailForm"
      :is-edit="isEdit"
    />

    <a-spin
      v-show="loading"
      style="width: 100%"
      tip="正在获取中..."
    />
    <template
      v-if="type === 'add'"
      #footer
    >
      <div class="bottom-block">
        <a-button
          :disabled="btnLoading"
          style="margin-right: 8px"
        >
          取消
        </a-button>
        <a-button
          :loading="btnLoading"
          type="primary"
          @click="onSubmit"
        >
          提交
        </a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue';
import { useModal } from '@/hook/component/use-modal';
import { IDataSourceItem, IRuleSubmitData } from '../data';
import { getRuleVersionDetail, onAddRuleFormData, onEditRuleFormData } from '../service';
import VersionSelect from './fixed-version-select.vue';
import DetailForm from './fixed-detail-form.vue';

const emits = defineEmits(['tableLoad']);

const { visible, open, close } = useModal(
  () => {
    return;
  },
  {
    afterClose: () => {
      activeKey.value = '1';
      dataStatistics.value = [];
    },
  },
);

// 查看规则详情时，储存所查看的规则数据；
let currentRule: any = null;

const type = ref<string>('');
const isEdit = ref<boolean>(false);

const loading = ref<boolean>(true);
const btnLoading = ref<boolean>(false);

// 选项卡标识
const activeKey = ref<string>('1');

const version_select = ref<any>(null);
const detailForm = ref<any>(null);

// 数据统计
const dataStatistics = ref<any>([]);

const onShow = (enterType: string, item?: IDataSourceItem) => {
  type.value = enterType;
  isEdit.value = enterType === 'add';
  if (enterType === 'add') loading.value = false;
  currentRule = item;
  open();
  if (enterType != 'add') {
    reloadVersion();
  }
};

const reloadVersion = () => {
  nextTick(() => {
    version_select.value.init('init', { rule_id: currentRule.id });
  });
};

const versionOnLoad = async (params: any) => {
  const { id, cb } = params;
  try {
    const res = await getRuleVersionDetail({ version_id: id });
    const data = res.data || {};
    const { rule_json } = data;
    detailForm.value?.setFormValue(rule_json);
  } finally {
    cb && cb();
  }
};

const onSubmit = async () => {
  const params: IRuleSubmitData = await detailForm.value?.validate();
  if (!params) return;
  if (currentRule) {
    params.id = currentRule.id;
  }
  console.log('params--', params);
  btnLoading.value = true;
  const fn = type.value === 'add' ? onAddRuleFormData : onEditRuleFormData;
  try {
    await fn(params);
    if (type.value === 'add') {
      emits('tableLoad');
      close();
    } else {
      reloadVersion();
    }
  } finally {
    btnLoading.value = false;
  }
};

defineExpose({
  onShow,
});
</script>

<style lang="less" scoped>
.tab-block {
  margin-bottom: 8px;
}
.bottom-block {
  text-align: right;
}
</style>
