<template>
  <a-modal
    v-model:visible="visible"
    title="导出"
    @ok="onOrderExport"
  >
    <a-form
      ref="exportFormRef"
      :model="orderExportForm"
    >
      <a-form-item
        label="订单类型"
        name="type"
        :rules="[{ required: true, message: '请选择订单类型' }]"
      >
        <a-select
          v-model:value="orderExportForm.type"
          :field-names="{ value: 'id', label: 'title' }"
          :options="configData.orderType"
          placeholder="请选择订单类型"
        />
      </a-form-item>
      <a-form-item
        label="项目名称"
        name="config_ids"
        :rules="[{ required: true, message: '请选择项目名称' }]"
      >
        <a-select
          v-model:value="orderExportForm.config_ids"
          allow-clear
          :field-names="{ value: 'id', label: 'title' }"
          mode="multiple"
          option-filter-prop="title"
          :options="configData.configData"
          placeholder="请选择项目名称"
          show-search
        />
      </a-form-item>
      <a-form-item
        label="订单创建时间"
        name="date"
        :rules="[{ required: true, message: '请选择订单创建时间' }]"
      >
        <a-range-picker
          v-model:value="orderExportForm.date"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useVModel } from '@/hook';
import { asyncEduce } from '@/utils/educe';

import { apiGetExportPreData } from '../services';

const route = useRoute();
const props = defineProps({
  visible: Boolean,
});
const emits = defineEmits(['update:visible']);
const visible = useVModel(props, 'visible', emits);
const exportFormRef = ref();
const orderExportForm = reactive({
  type: undefined,
  config_ids: [],
  date: [],
});
const configData = reactive({
  orderType: [],
  configData: [],
});

function getExportPreData() {
  apiGetExportPreData().then(res => {
    Object.assign(configData, res.data);
  });
}

function onOrderExport() {
  exportFormRef.value.validate().then(() => {
    const { date, ...rest } = orderExportForm;
    visible.value = false;
    asyncEduce(
      '/super/fixed-deposit-config/export-order-data',
      {
        ...rest,
        start_date: date[0],
        end_date: date[1],
      },
      route.query.origin,
    );
  });
}

watch(
  () => visible.value,
  val => {
    val && getExportPreData();
  },
  { immediate: true },
);
</script>
