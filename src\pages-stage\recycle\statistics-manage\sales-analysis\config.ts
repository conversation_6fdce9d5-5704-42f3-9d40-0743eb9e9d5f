export const columns: any = [
  {
    dataIndex: 'category_name',
    title: '品类',
    width: 88,
  },
  {
    dataIndex: 'brand_name',
    title: '品牌',
    width: 88,
  },
  {
    dataIndex: 'model_name',
    title: '型号',
    width: 160,
  },
  {
    dataIndex: 'sku_info',
    title: 'SKU信息',
    width: 312,
  },
  {
    dataIndex: 'sku_id',
    title: 'SKUID',
    width: 168,
  },
  {
    dataIndex: 'sale_num',
    title: '基本数据(销售)',
    width: 126,
  },
  {
    dataIndex: 'sale_price',
    title: '销售金额',
    width: 132,
  },
  {
    dataIndex: 'average_sale_price',
    title: '销售均价',
    width: 100,
  },
  {
    dataIndex: 'differences_price',
    title: '进销差价',
    width: 100,
  },
  {
    dataIndex: 'sale_purchased_price',
    title: '销售成本',
    width: 132,
  },
  {
    dataIndex: 'sale_win_price',
    title: '销售收入',
    width: 132,
  },
  {
    dataIndex: 'gross_profit',
    title: '销售毛利',
    width: 132,
  },
  {
    dataIndex: 'stock',
    title: '基本数量(期末)',
    width: 126,
  },
  {
    dataIndex: 'stock_purchased_price',
    title: '期末库存成本',
    width: 132,
  },
  {
    dataIndex: 'average_stock_purchased_price',
    title: '库存均价',
    width: 100,
  },
];
