import { GET, POST } from '@/services/api';

import { getRole } from '../delivery-intercept-modal/config';

const config = {
  hostType: 'Golang',
};

/**
 * @description: 获取下发仓库基础数据（运营）
 * @param {any} order_id
 * @return {*}
 */
export function superGetSendWarehouseInfo(order_id: string) {
  return GET('/super/v3-order/send-warehouse-alert', { order_id });
}

/**
 * @description: 获取下发仓库基础数据（商家）
 * @param {any} order_id
 * @return {*}
 */
export function getStoreSendWarehouseInfo(order_id: string) {
  return GET('/order/send-warehouse-alert', { order_id });
}

/**
 * @description: 下发仓库
 * @param {any} data
 * @return {*}
 */
export function issueStock(data: any) {
  let url = '/order/mark';
  if (getRole() === 'super') {
    url = '/super/v2-order/mark';
  }
  return POST(url, { ...data });
}

/**
 * @description: 根据sku属性查找skuid
 * @param {any} data
 * @return {*}
 */
export function searchSkuId(data: any) {
  return POST('/warehouse/FindSingleSku', { ...data }, config);
}

/**
 * @description: 分仓服务
 * @param {any} data
 * @return {*}
 */
export function subwarehouseService(data: any) {
  return POST(
    '/warehouse/WarehouseSplit',
    {
      trigger_way: 3,
      order_scene: 1, // 订单业务场景值
      created_port: 1, // 运营后台
      ...data,
    },
    config,
  );
}

/**
 * @description: 识别该订单是否在【缺货记录表】
 * @param {order_id: string} data
 * @return {Promise<any>}
 */
export function autoToWarehouse(data: { order_id: string }) {
  return GET('/super/direct-pre-lease/auto-to-warehouse', data);
}

/**
 * @description: 去发货
 * @param {any} data
 * @return {*}
 */
export function goToShip(data: any) {
  data.method = 'v2.order.delivery';
  return POST('/api/gateway', data);
}

/**
 * @Description 获取下发货品信息（商家端）
 * @param {any} params:any
 * @returns {any}
 */
export function getPurchaseInfo(params: any) {
  return GET('/shop-purchased-device/purchased-delivery-info', params);
}

/**
 * @Description 获取下发货品信息（运营端）
 * @param {any} params:any
 * @returns {any}
 */
export function getPurchaseInfoSuper(params: any) {
  return GET('/super/warehouse/shop-purchased-device/purchased-delivery-info', params);
}

/**
 * @description: 获取型号
 * @param {any} params
 * @return {*}
 */
export function getModal(params: any) {
  return GET('/warehouse/SpuRelation', { source: 'skuCreate', ...params }, config);
}

/**
 * @description: 获取当前型号的sku信息
 * @param {any}
 * @return {*}
 */
export function getSkuInfo({ category_id, brand_id, model_id }: any) {
  return GET('/warehouse/AttrList', { category_id, brand_id, model_id }, config);
}

/**
 * @description: 去发货（旧）图片上传
 * @param {any} params
 * @return {*}
 */
export function uploadImg(params: any) {
  return POST('/super/v2-order/image', params, {
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * @description: 取消顺丰寄件
 * @param {any} params
 * @return {*}
 */
export function cancelSfPickUp(params: any) {
  return POST('/v2-order-sf-express/cancel', params);
}

/**
 * @description: 获取订单下发状态及顺丰预约
 * @param {any} params
 * @return {*}
 */
export function getOrderLimitIssued(params: any) {
  return GET('/order/server-oms-limit-issued', params);
}

export function getOperateMark(params: { type: number }) {
  return GET('/order/delivery-operate-mark', params);
}

/** 是否是内测商家 */
export function isInnerMerchant() {
  return GET('/shop-purchased-device/is-white');
}

/**
 * @description:获取字典树
 * @param order_id
 * @returns
 */
export function getDictTree(order_id: string) {
  return GET('/order/get-scm-dict', { order_id });
}
/**
 * @description:查询库存
 * @param data sku_id order_id:订单号 warehouse_id:仓库id
 * @returns
 */
export function queryInventoryApi(data: any) {
  return GET('/order/get-device-codes', data);
}
/**
 * @description:获取sku
 * @param data order_id:订单号 category_id brand_id model_id
 * @returns
 */
export function getSkuApi(data: any) {
  return GET('/order/get-sku-list', data);
}

//仓库信息
export function getWarehouseList() {
  return GET('/iem-device/warehouse-info');
}

// 获取页面优化标志（码商商家扫码订单的手机类目）
export function getPageOptiFlag(params: { order_id: string }) {
  return GET('/code-merch/self-order-info', params);
}

/**
 * @description:获取换货建议
 * @param data 与分仓请求的参数一致
 * @returns
 */
export function getReplacementProposal(data: any) {
  let url = '/order/exchange-suggest';
  const base_info = {
    trigger_way: 3,
    order_scene: 1, // 订单业务场景值
    created_port: 2, // 商家后台
  };
  if (getRole() === 'super') {
    url = '/super/v3-order/exchange-suggest';
    base_info.created_port = 1;
  }
  return POST(
    url,
    { ...base_info, ...data },
    {
      headers: {
        'content-type': 'application/json;charset=UTF-8',
      },
    },
  );
}

/**
 * @description:预定下发
 * @param data
 * @returns
 */
export function reserveSend(data: any) {
  let url = '/order/reserve-send';
  if (getRole() === 'super') {
    url = '/super/v3-order/reserve-send';
  }
  return POST(url, { ...data });
}

/**
 * @description:获取是否有全新仅激活的库存
 * @param data
 * @returns
 */
export function getActivateInventory(data: any) {
  return GET('/warehouse/QueryInventory', data, config);
}

/**
 * @description:预创建充值奖金额
 * @param data
 * @returns
 */
export function sendServerRecharge(data: any) {
  return POST('/shop-purchased-device/one-replace-send-server-recharge', data);
}

export function fetchReceivingAddressLog(params: any) {
  return GET('/order/TblOrderAddressChangeLog/query', params, { hostType: 'Golang' });
}

/**
 * @description: 闪送预下单 （运营端）
 * @param {any} params
 * @return {*}
 */
export function apiAdminIssOrder(params: any) {
  return POST('/logistics/admin/iss/order', params, {
    hostType: 'Golang',
  });
}

/**
 * @description: 闪送预下单 （商家端）
 * @param {any} params
 * @return {*}
 */
export function apiServerIssOrder(params: any) {
  return POST('/logistics/server/iss/order', params, {
    hostType: 'Golang',
  });
}

/**
 * @description: 闪送提交订单 （运营端）
 * @param {any} params
 * @return {*}
 */
export function apiAdminIssOrderPlace(params: any) {
  return POST('/logistics/admin/iss/orderPlace', params, {
    hostType: 'Golang',
  });
}

/**
 * @description: 闪送提交订单 （商家端）
 * @param {any} params
 * @return {*}
 */
export function apiServerIssOrderPlace(params: any) {
  return POST('/logistics/server/iss/orderPlace', params, {
    hostType: 'Golang',
  });
}

/** 闪送&到店服务 - 运营后台 */
export function apiAdminGetShanSongList<T, K>(params: K) {
  return GET<T, K>('/logistics/admin/serverStore/list', params, {
    hostType: 'Golang',
  });
}

/** 闪送&到店服务 - 商家后台 */
export function apiMerchantGetShanSongList<T, K>(params: K) {
  return GET<T, K>('/logistics/serverStore/list', params, {
    hostType: 'Golang',
  });
}
