<!--
  针对传递给接口的特殊数据结构进行封装修改 CommodityCategoryCascader 组件的配置，
  常用于页面内列表头部的商品分类多级多选，选择的值转化为"层级\_分类 id"结构数组存储。
-->
<template>
  <CommodityCategoryCascader
    ref="cascaderRef"
    v-bind="targetAttrs"
    v-model:value="selectValue"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { computed, PropType, ref, useAttrs, watch } from 'vue';

import CommodityCategoryCascader from '@/components/commodity-category-cascader';
import { ICommodityCategoryCascaderProps } from '@/components/commodity-category-cascader/src/data';
import type { IOption } from '@/composables/commodity-category/data';
import { EScene } from '@/utils/enums/commodity';

/** 废弃声明，此组件存在不可逆bug，请使用CommodityCategoryCascader替代 */
if (process.env.NODE_ENV === 'development') {
  console.warn(
    '【已废弃】CategoryMultipleCascader组件存在bug，请使用CommodityCategoryCascader组件替代，需要与后端一同修复；原因：当仅选择品牌时，如选中了‘手机/数码-手机-苹果’、‘电脑/平板-平板-苹果’，此时数据绑定为[“3_26”,“3_26”]，品牌id并不唯一，需要改用[[],[]]。',
  );
}
defineOptions({ inheritAttrs: false });

const props = defineProps({
  value: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const emits = defineEmits(['update:value']);

const attrs = useAttrs();
/** 组件属性，继承属性，同时组件固定远程搜索，且为多选 */
const targetAttrs = computed<ICommodityCategoryCascaderProps>(() => {
  return {
    maxTagCount: 'responsive',
    scene: EScene.PlatformOrder,
    changeOnSelect: true,
    asyncSearch: true,
    ...attrs,
    multiple: true,
  };
});

/** 选择的值 */
const selectValue = ref<number[][]>();

watch(
  () => props.value,
  () => {
    // 选择的值只能根据绑定的数据置空，但不支持绑定值进行回显（回显数据需要包含所有父级id，但是绑定值只包含最后一级id）
    if (props.value.length === 0) {
      selectValue.value = [];
    }
    // 兼容清空时空数组情况
    if (props.value.length === 1 && !props.value[0]) {
      emits('update:value', []);
    }
  },
);

/** 修改选择 */
function handleChange() {
  const value = selectValue.value?.map(item => `${item.length}_${item[item.length - 1]}`) || [];
  emits('update:value', value);
}

const cascaderRef = ref();

const options = computed<IOption[]>(() => {
  return cascaderRef.value?.options || [];
});

// 向外暴露
defineExpose({
  options,
});
</script>
