const cols: any[] = [
  {
    dataIndex: 'sign',
    title: '检测批次',
    width: 140,
  },
  {
    dataIndex: 'title',
    title: '批次名称',
    ellipsis: true,
    width: 160,
  },
  {
    dataIndex: 'num',
    title: '商品总数',
    width: 100,
  },
  {
    dataIndex: 'pass',
    title: '检测通过商品数',
    width: 160,
  },
  {
    dataIndex: 'pass_lock',
    title: '待锁机商品数',
    width: 160,
  },
  {
    dataIndex: 'wait_pay',
    title: '批次所有未付款锁机商品数',
    width: 220,
  },
  {
    dataIndex: 'price',
    title: '锁机费用单价',
    width: 140,
  },
  {
    dataIndex: 'price_sum',
    title: '总计',
    width: 100,
  },
  {
    dataIndex: 'status_text',
    title: '批次状态',
    width: 100,
  },
  {
    dataIndex: 'created_at',
    title: '创建时间',
    width: 200,
  },
  {
    dataIndex: 'payed_at',
    title: '付款时间',
    width: 200,
  },
  {
    title: '操作',
    width: 220,
    slots: { customRender: 'operations' },
    fixed: 'right',
  },
];
import { computed, nextTick, Ref, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

import { getBatchSelect, getPayQrcode, getPayStatus } from '../services';
enum MERCHANT_STATUS {
  WAIT_PAY = 0,
  HAS_PAY = 1,
}
export default function useTable(type: string, activeTab: Ref<any>, createPageData: (isDeleted?: boolean) => void) {
  const route = useRoute();
  let goQualityListPath = '';
  const payModalData: Ref<any> = ref({ visible: false, id: '', money: '' });
  const setPriceModalData: Ref<any> = ref({
    visible: false,
    batchSelect: [],
    currentBatch: [],
  });
  const btnGroup = [
    { title: '查看', cb: goQualityList },
    {
      title: '去支付锁机费用',
      cb: showPayModal,
      fn: ({ type, currentTab }: { type: string; currentTab: number }) =>
        type === 'merchant' && currentTab === MERCHANT_STATUS.WAIT_PAY,
    },
    {
      title: '价格设置',
      cb: showPriceModal,
      fn: ({ type, currentTab }: { type: string; currentTab: number }) =>
        type === 'admin' && currentTab === MERCHANT_STATUS.WAIT_PAY,
    },
  ];
  const actions: Ref<any[]> = computed(() => {
    return btnGroup.filter(item => !item.fn || item.fn({ type, currentTab: activeTab.value }));
  });

  const dataSource: Ref<Array<any>> = ref([]);
  const listLoading = ref(false);

  //methods
  /**
   * 商家点击查看 跳到 检测商品；运营点击查看 跳到 严选商品
   * */
  function goQualityList(row: any) {
    const origin = route.query.origin;
    localStorage.setItem('waitPayListSign', row.sign);
    if (row.qua_version == 1) {
      if (type === 'admin') {
        goQualityListPath = '/super/quality-spu/warehouse-quality-index';
      } else {
        goQualityListPath = '/quality-spu/warehouse-quality-index';
      }
    }
    window.open(`${origin + goQualityListPath}`);
  }

  /**
   * 价格设置
   */
  function showPriceModal(row: any) {
    let batch_id;
    if (row && (batch_id = row.id)) {
      setPriceModalData.value.currentBatch = [batch_id];
    } else {
      setPriceModalData.value.currentBatch = ['all'];
    }
    setPriceModalData.value.visible = true;
  }

  function actionHandler(cb: any, row: any) {
    if (cb instanceof Function) {
      cb(row);
    }
  }

  function handleType() {
    if (type === 'admin') {
      goQualityListPath = '/super/quality-spu/quality-list';
      cols.splice(1, 0, {
        dataIndex: 'server_name',
        title: '商家名称',
        ellipsis: true,
      });
    } else if (type === 'merchant') {
      goQualityListPath = '/quality-spu/index';
    }
  }

  async function showPayModal(row: any) {
    //可能已支付了，就刷新。没有的话再显示支付弹窗。
    const {
      data: { is_pay },
    } = await getPayStatus({ id: row.id });
    if (is_pay) {
      message.success('该批次已支付！', 1);
      createPageData(true);
    } else {
      const {
        data: { qrcodeImg },
      } = await getPayQrcode({ id: row.id });
      payModalData.value.qrCode = qrcodeImg;
      payModalData.value.id = row.id;
      payModalData.value.money = row.price_sum.toFixed(2);
      await nextTick();
      payModalData.value.visible = true;
    }
  }

  async function getBatches() {
    const { data: list } = await getBatchSelect();
    const options = list.map((item: any) => ({ value: item.batch_id, label: item.sign }));
    setPriceModalData.value.batchSelect = [{ value: 'all', label: '全部' }, ...options];
  }

  getBatches();
  handleType();
  return {
    cols,
    actions,
    dataSource,
    listLoading,
    payModalData,
    showPriceModal,
    actionHandler,
    showPayModal,
    setPriceModalData,
  };
}
