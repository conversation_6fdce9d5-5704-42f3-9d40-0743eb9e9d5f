# CommodityCategorySelect 商品分类选择

针对商品分类的中各个场景下封装的分类下拉选择，分类选择项可通过 useCommodityCategory 方法获取

```vue
<template>
  <CommodityCategorySelect
    cascader
    change-on-select
    disabled-type="hide"
    placeholder="请选择发布类目"
    size="large"
    :value="categoryValue"
    @change="onSelectCategory"
  />
</template>

<script setup lang="ts">
import CommodityCategorySelect from '@/components/commodity-category-select';
import { IOption, useCommodityCategory } from '@/composables/use-commodity-category';

defineProps<{
  categoryValue: number[];
}>();

const emit = defineEmits<{
  (e: 'change', value: number[]): void;
}>();

/** 选择分类 */
function onSelectCategory(values?: number[], selectedData?: IOption[]) {
  const titles = selectedData?.map(item => item.label);
  console.log(titles);
  emit('change', values ?? []);
}

const { loading, categoryOptions } = useCommodityCategory({
  disabledType: 'hide',
  cascader: true,
});
</script>
```

:::

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value(v-model) | 指定选中项 | string\|string[]\|number\|number[] | -- |
| disabledType | 禁用项的特殊处理，'hide':隐藏；'enable':启用 | 'hide'\|'enable' | -- |
| level | 获取到的分类级别：1 一级\| 2 二级\| 3 品牌\| 4 模型 | 1 \| 2 \| 3 \| 4 | 4 |
| maxLevel | 接口返回的最高层级,默认 4 级,同场景下使用同一个最高层级时可避免重复请求资源 | 1 \|2 \|3 \|4 | 4 |
| scene | 接口获取的场景，默认 1 或 2(根据用户类型 role 判断)，1 平台商品 2 商家商品 3 平台订单 4 商家订单 | 1 \|2 \|3 \|4 | 1 或 2(根据用户类型 role 判断) |
| filterPidLevel | 根据第几层的父 id 进行筛选（需要传入父级 pid 获取分类） | 1 \|2 \|3 | undefined |
| pid | 父级 id，也可使用完整的父级层级 id 数组（推荐，完整各个父级 id 数组查询性能更好） | number\|number[] | undefined |
| disabled | 禁用状态 | boolean | false |
| placeholder | 选择框默认文字 | string | undefined |
| cascader | 是否为联级选择 | boolean | false |
| multiple | 是否多选 | boolean | false |
| changeOnSelect | 联级选择下，是否点选每级菜单选项值都发生变化 | boolean | false |
| size | 大小 | 'large' \| 'default' \| 'small' | default |
| maxTagCount | 最多显示多少个 tag | number\|'responsive' | undefined |
| custom | 自定义选项 | (options,value)=>options | undefined |
| getPopupContainer | 菜单渲染父节点 | () => HTMLElement | () => document.body |

### 事件

| 事件名称 | 说明         | 回调参数        |
| -------- | ------------ | --------------- |
| change   | 手动更改选择 | (value,options) |
