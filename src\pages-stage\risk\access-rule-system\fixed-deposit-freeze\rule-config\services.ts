import { GET, POST } from '@/services/api';

/** 获取固定押金配置列表 */
export function apiGetFixedDepositAllocation(params: any): Promise<any> {
  return GET('/super/fixed-deposit-config/get-list-data', params);
}

/** 修改配置状态 */
export function apiChangeConfigStatus(params: any): Promise<any> {
  return POST('/super/fixed-deposit-config/set-status', params);
}

/** 删除配置 */
export function apiDeleteFixedDepositConfig(params: any): Promise<any> {
  return POST('/super/fixed-deposit-config/delete-data', params);
}

//-------------------------------
/** 保存规则配置的前置配置数据 */
export function apiSaveFixedDepositConfig(params: any): Promise<any> {
  return POST('/super/fixed-deposit-config/save-data', params);
}

/** 获取编辑规则配置的前置配置数据 */
export function apiGetEditRuleConfig(params: { id?: string }) {
  return GET('/super/fixed-deposit-config/get-pre-data', params);
}

/** 校验店铺数据 */
export function apiCheckShop(params: { server_id: string }): Promise<any> {
  return GET('/super/fixed-deposit-config/check-shop', params);
}

/** 校验商品数据 */
export function apiCheckProduct(params: { item_ids: string; type_id: string }): Promise<any> {
  return GET('/super/fixed-deposit-config/check-item-id', params);
}

export function apiGetExportPreData() {
  return GET('/super/fixed-deposit-config/get-export-pre-data');
}

export function apiGetOperationLog(params: any): Promise<any> {
  return GET('/super/fixed-deposit-config/operate-log', params);
}
