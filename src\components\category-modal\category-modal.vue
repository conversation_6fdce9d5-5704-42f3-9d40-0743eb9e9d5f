<script setup lang="ts">
import { computed, nextTick, onMounted, PropType, ref, watch } from 'vue';
import { Empty } from 'ant-design-vue';
import { CloseOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { isFunction } from 'lodash-es';
import { useBoolean } from 'rrz-web-design';

import { getCategoryData } from './services';

type TCategory = {
  key: string;
  name: string;
  child: TCategory[];
};

type TOption = { label: string; value: string };

type TConfirmFn = (value: string[], options: TOption[]) => Promise<any>;

const props = defineProps({
  /** category接口用作value值的key，业务使用code，数据维护使用id */
  valueKey: {
    type: String,
    default: 'code',
  },
  confirm: {
    type: Object as PropType<TConfirmFn>,
    default: undefined,
  },
});

const emits = defineEmits<{
  (e: 'confirm', value: string[], options: TOption[]): void;
}>();

const [visible, setVisible] = useBoolean();
const [loading, setLoading] = useBoolean();
const [submitLoading, setSubmitLoading] = useBoolean();

const categoryData = ref<TCategory[]>([]);
const categoryMap = ref({});
const subCategoryData = ref<TCategory[]>([]);
const activeCategoryIndex = ref(0);
const searchKeyword = ref<string>();
const selectData = ref<string[]>([]);
const selectedWrapperRef = ref<HTMLElement>();

const computedCategory = computed(() => {
  return subCategoryData.value.filter(
    t => !searchKeyword.value || String(t.name).toLowerCase().indexOf(String(searchKeyword.value).toLowerCase()) > -1,
  );
});

const checkAll = computed(() => !subCategoryData.value.some(t => !selectData.value.includes(t.key)));

const indeterminate = computed(
  () => subCategoryData.value.some(t => selectData.value.includes(t.key)) && !checkAll.value,
);

function getBadgeCount(index: number) {
  return categoryData.value[index].child.filter(
    t => String(t.name).toLowerCase().indexOf(String(searchKeyword.value).toLowerCase()) > -1,
  ).length;
}

function queryCategoryData(silence = false) {
  !silence && setLoading(true);
  getCategoryData()
    .then(({ data }) => {
      const { category_list = [] } = data;
      categoryData.value = category_list.map(parent => {
        return {
          name: parent.name,
          key: String(parent[props.valueKey]),
          child:
            parent.child?.map(child => {
              categoryMap.value[child[props.valueKey]] = child.name;
              return { name: child.name, key: String(child[props.valueKey]) };
            }) || [],
        };
      });
    })
    .finally(() => setLoading(false));
}

function handleCategoryClick(index: number) {
  activeCategoryIndex.value = index;
  subCategoryData.value = categoryData.value[index].child || [];
}

function handleCheckAllClick() {
  const keys = subCategoryData.value.map(child => child.key);
  if (checkAll.value) {
    selectData.value = selectData.value.filter(key => !keys.includes(key));
  } else if (indeterminate.value) {
    keys.forEach(key => {
      !selectData.value.includes(key) && selectData.value.push(key);
    });
  } else selectData.value.push(...keys);
}

function handleCheckChange(key: string) {
  if (selectData.value.includes(key)) {
    selectData.value = selectData.value.filter(t => t !== key);
  } else selectData.value.push(key);
}

function handleRemove(key: number | 'all') {
  if (key === 'all') selectData.value = [];
  else selectData.value.splice(key, 1);
}

function handleClose() {
  setVisible(false);
  selectData.value = [];
  searchKeyword.value = undefined;
  handleCategoryClick(0);
}

function handleConfirm() {
  const value = Array.from(selectData.value);
  const options = selectData.value.map(key => ({ label: categoryMap.value[key], value: key }));
  if (props.confirm && isFunction(props.confirm)) {
    setSubmitLoading(true);
    props
      .confirm(value, options)
      .then(() => {
        handleClose();
      })
      .finally(() => setSubmitLoading(false));
  } else {
    emits('confirm', value, options);
    handleClose();
  }
}

function scrollSelectedBottom(duration = 300) {
  if (!selectedWrapperRef.value) return;
  const target = selectedWrapperRef.value;
  const distance = target.scrollHeight - target.clientHeight - target.scrollTop;

  let lastTime;
  const stepFn = timestamp => {
    if (lastTime === undefined) lastTime = timestamp;
    const elapsed = timestamp - lastTime;
    target.scrollTop += (elapsed * distance) / duration;
    if (target.scrollTop < target.scrollHeight - target.clientHeight) {
      requestAnimationFrame(stepFn);
    }
  };
  requestAnimationFrame(stepFn);
}

watch(
  () => selectData.value.length,
  (newLen, oldLen = 0) => {
    if (newLen > oldLen) nextTick(() => scrollSelectedBottom());
  },
);

onMounted(() => {
  queryCategoryData();
});

defineExpose({
  open(selected?: string[] | number[]) {
    if (selected?.length) selectData.value = selected.map(String);
    queryCategoryData(true);
    setVisible(true);
  },
});
</script>

<template>
  <a-modal
    v-model:visible="visible"
    :ok-button-props="{ loading: submitLoading }"
    title="编辑类目"
    width="872px"
    @cancel="handleClose"
    @ok="handleConfirm"
  >
    <a-spin :spinning="loading">
      <div class="flex-wrap gap-16">
        <div class="selector flex-wrap flex-vertical gap-16">
          <a-input
            v-model:value="searchKeyword"
            allow-clear
            class="flex-static"
            placeholder="搜索品类"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
          <div class="selector-wrapper flex-con flex-wrap gap-16">
            <div class="category-list flex-con">
              <div
                v-for="(categoryItem, cidx) in categoryData"
                :key="categoryItem.key"
                :class="[
                  'category-item flex-wrap flex-y-center flex-x-justify',
                  { active: cidx === activeCategoryIndex },
                ]"
                @click="handleCategoryClick(cidx)"
              >
                <span
                  v-ellipsis="1"
                  class="no-wrap-box"
                  :title="categoryItem.name"
                >{{ categoryItem.name }}</span>
                <a-badge
                  v-if="searchKeyword"
                  :count="getBadgeCount(cidx)"
                  :number-style="{
                    backgroundColor: '#fff',
                    color: '#999',
                    boxShadow: '0 0 0 1px #d9d9d9 inset',
                  }"
                  show-zero
                />
              </div>
              <a-empty
                v-if="!categoryData.length"
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
              />
            </div>
            <div class="sub-category-list flex-con">
              <div
                v-if="computedCategory.length && !searchKeyword"
                class="category-item"
              >
                <a-checkbox
                  :checked="checkAll"
                  :indeterminate="indeterminate"
                  @change="handleCheckAllClick"
                >
                  全选
                </a-checkbox>
              </div>
              <div
                v-for="subCategoryItem in computedCategory"
                :key="subCategoryItem.key"
                class="category-item"
              >
                <a-checkbox
                  :checked="selectData.includes(subCategoryItem.key)"
                  @change="handleCheckChange(subCategoryItem.key)"
                >
                  {{ subCategoryItem.name }}
                </a-checkbox>
              </div>
              <a-empty
                v-if="!computedCategory.length"
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
              />
            </div>
          </div>
        </div>
        <div class="selected flex-wrap flex-vertical gap-16">
          <div class="sticky-head flex-static flex-wrap flex-x-justify">
            <span>已选：{{ selectData.length }}项</span>
            <div
              class="text-link"
              @click="handleRemove('all')"
            >
              清空
            </div>
          </div>
          <div
            ref="selectedWrapperRef"
            class="selected-wrapper flex-con"
          >
            <transition-group
              v-if="selectData.length"
              name="fade"
              tag="div"
            >
              <div
                v-for="(selectedItem, sidx) in selectData"
                :key="selectedItem"
                class="selected-item flex-wrap flex-y-center flex-x-justify"
              >
                <span>{{ categoryMap[selectedItem] }}</span>
                <CloseOutlined
                  class="close-icon"
                  @click="handleRemove(sidx)"
                />
              </div>
            </transition-group>
            <a-empty
              v-else
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            />
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<style scoped lang="less">
.selector,
.selected {
  height: 468px;

  overflow: auto;
  border: 1px solid #dadce0;
  border-radius: 4px;
}

.selector {
  flex: 4;
  padding: 16px;

  .selector-wrapper {
    overflow: auto;

    .category-list,
    .sub-category-list {
      overflow: auto;
      color: rgba(6, 21, 51, 0.85);
    }

    .category-list > .category-item {
      position: relative;
      padding: 5px 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover,
      &.active {
        background: #f5f7fa;
      }

      &.active::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 2px;
        height: 100%;
        background: var(--ant-primary-color);
        content: '';
      }
    }

    .sub-category-list > .category-item {
      padding: 5px 16px;
    }
  }
}

.selected {
  flex: 3;
  padding: 16px 0;

  .sticky-head {
    padding: 0 16px;
  }

  .selected-wrapper {
    overflow: auto;

    .selected-item {
      padding: 5px 16px;
      transition: all 0.3s;

      &:hover {
        background: #f5f7fa;
      }

      .close-icon {
        color: rgba(6, 21, 51, 0.45);
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          color: var(--ant-error-color);
        }
      }
    }
  }
}
</style>
