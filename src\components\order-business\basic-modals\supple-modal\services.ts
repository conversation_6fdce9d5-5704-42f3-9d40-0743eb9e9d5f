import { GET } from '@/services/api';

/** 获取关联列表（运营）*/
export function getSuppleOrder(params: any) {
  return GET('/super/v3-order/get-supple', params);
}

/** 获取关联列表（商家）*/
export function getSuppleOrder2(params: any) {
  return GET('/order/get-supple', params);
}
/**
 * @description: 查询商家订单v3是否关闭
 * @param {any} params
 * @return {*}
 */
export function checkCloseOrder(params: any) {
  return GET('/v3-order/check-close-order', params);
}
