import { GET, POST } from '@/services/api';

/**
 * @Description 获取营业时间配置
 * @param {any} payload:any
 * @returns {any}
 */
export function getRelieveInfo(payload: any) {
  return GET('/server/get-relieve-info', payload);
}

/**
 * @Description 获取营业时间配置
 * @param {any} payload:any
 * @returns {any}
 */
export function postApplyRelieveInfo(payload: any) {
  return POST('/server/apply-relieve-business-time', payload);
}
