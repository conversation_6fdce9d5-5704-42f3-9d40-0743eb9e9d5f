<template>
  <div :class="[canOpenFlex && chartsPanelVisible ? 'flex-box' : 'common-box']">
    <div
      v-if="canOpenFlex"
      class="trigger"
      :class="{ expand: !chartsPanelVisible }"
      @click="toggleChartsPanelVisible"
    >
      <template v-if="chartsPanelVisible">
        <span>收起检测报告</span>
      </template>
      <template v-else>
        <span>展开检测报告</span>
      </template>
      <DoubleLeftOutlined :rotate="chartsPanelVisible ? 180 : 0" />
    </div>
    <div
      v-if="haveReport"
      class="left-box"
    >
      <div class="diff-box">
        <h4 class="title">
          检测报告
        </h4>
        <div class="diff-line">
          <span
            class="diff-label"
            @click="detailReport('before')"
          >归还前的检测报告</span>
          <span class="diff-icon">VS</span>
          <span
            class="diff-label"
            @click="detailReport('after')"
          > 归还后的检测报告</span>
        </div>
        <div
          v-if="!isDetail"
          class="diff-line"
        >
          <a-button
            :loading="diffReportLoading"
            type="primary"
            @click="getDiffReport"
          >
            <template #icon>
              <SearchOutlined />
            </template>
            点击对比
          </a-button>
        </div>
      </div>
      <div
        v-if="chartsPanelVisible"
        class="report-box"
      >
        <div
          v-if="formData.report_data.report_before_check_report"
          class="report-item"
        >
          <QualityReportContent
            :is-new="true"
            :report-data="formData.report_data.report_before_check_report"
          />
        </div>
        <div
          v-if="formData.report_data.report_after_check_report"
          class="report-item"
        >
          <QualityReportContent
            :is-new="true"
            :report-data="formData.report_data.report_after_check_report"
          />
        </div>
      </div>
    </div>
    <div class="right-box">
      <div
        v-for="(item, key, index) in list"
        :key="key"
        class="compensation-item"
      >
        <CompensationItem
          v-if="!isDetail || detailData.template_data[key]?.list?.length || isCanEditAll"
          ref="compensationItemRef"
          v-model:detailItemValue="detailData.template_data[key]"
          v-model:itemValue="templateData[key]"
          :can-row-select="canRowSelect"
          :current-index="index"
          :have-objection="haveObjection"
          :is-detail="isDetail"
          :key-name="key"
          :no-show-img="noShowImg"
          :no-show-platform="noShowPlatform"
          :options="item"
          :report-imags="afterImages"
        />
      </div>
      <div
        v-if="!canRowSelect && !noShowImg"
        :class="{ 'bottom-price': true, 'border-left': !isDetail || isCanEditAll }"
      >
        <a-descriptions :column="2">
          <template
            v-for="item in bottomShow"
            :key="item.key"
          >
            <a-descriptions-item v-if="!item.isDetail || (item.isDetail && detailData[item.key])">
              <template #label>
                <div class="common-title">
                  {{ item.name }}
                </div>
              </template>
              <div class="common-value">
                <span :class="[statusKeys.includes(item.key) ? getStatusColor(item.key) : 'red-text']">{{
                  (props.isDetail && !isCanEditAll ? detailData[item.key] : formData[item.key]) || 0
                }}</span>
                <span
                  v-if="item.extra"
                  class="common-text"
                >{{ item.extra }}</span>
              </div>
            </a-descriptions-item>
          </template>
        </a-descriptions>
      </div>
    </div>
  </div>
  <QualityReport
    :is-new="true"
    :report-data="reportParams"
    :visible="reportVisible"
    @close="() => (reportVisible = false)"
  />
</template>

<script setup lang="ts">
import { computed, provide, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { DoubleLeftOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { commonAssign, isImage } from '@rrzu/utils';
import { nanoid } from 'nanoid';

import QualityReportContent from '@/components/quality-report/quality-content.vue';
import QualityReport from '@/components/quality-report/quality-report.vue';
import { deepClone } from '@/utils/base';
import floatCalculator from '@/utils/float-calculator';
import { accAdd, accMin } from '@/utils/number';

import CompensationItem from './components/compensation-item.vue';
import {
  bottomCommonShow,
  bottomDetailShow,
  initData,
  initDataV2,
  maxPlatformPayMoney,
  statusColorMap,
  statusKeys,
  statusTextMap,
} from './config';
import {
  ECanEdit,
  TCompensationFormData,
  TComTemplateData,
  TDiffReport,
  TItemValueList,
  TList,
  TProps,
  TUrl,
} from './data.d';
import {
  damageExpansionInfoSymbol,
  detailPropsSymbol,
  isCanEditAllSymbol,
  isCanEditPlatformSymbol,
  isCanEditUserPaySymbol,
  isEditStatusSymbol,
  isFromOrderListSymbol,
  orderIdSymbol,
} from './injection-symbols';
import { getDiffReportApi, getOrderCheckReportApi, getSuperOrderCheckReportApi } from './services';
const props = defineProps<TProps>();
const route = useRoute();
const isSuper = route.query.role === 'super';
const emits = defineEmits(['update:data']);
const chartsPanelVisible = ref(false);
function toggleChartsPanelVisible() {
  chartsPanelVisible.value = !chartsPanelVisible.value;
}
const compensationItemRef = ref<any>();
//底部显示
const bottomShow = computed(() => {
  return props?.showDetailBottom ? [...bottomCommonShow, ...bottomDetailShow] : bottomCommonShow;
});

const haveReport = computed(() => {
  return (
    props.showTestReport &&
    (Number(formData.value.report_before) ||
      Number(formData.value.report_after) ||
      Number(detailData.value.report_after) ||
      Number(detailData.value.report_before))
  );
});

const canOpenFlex = computed(() => {
  return props.isFlex && props.isFlex && haveReport.value;
});

const isCanEditPlatform = computed(() => props.canEdit === ECanEdit.platformPay);
provide(isCanEditPlatformSymbol, isCanEditPlatform);

const isCanEditUserPay = computed(() => props.canEdit === ECanEdit.userPay);
provide(isCanEditUserPaySymbol, isCanEditUserPay);

const isCanEditAll = computed(() => props.canEdit === ECanEdit.all);
provide(isCanEditAllSymbol, isCanEditAll);

const isFromOrderList = computed(() => props.isFromOrderList || false);
provide(isFromOrderListSymbol, isFromOrderList);

const isEditStatus = computed(() => props.canEdit !== ECanEdit.none);
provide(isEditStatusSymbol, isEditStatus);

const orderId = computed(() => props.orderId || '');
provide(orderIdSymbol, orderId);

const detailProps = computed(() => props.detail || null);
provide(detailPropsSymbol, detailProps);

const damageExpansionInfo = computed(() => props.damageExpansionInfo || null);
provide(damageExpansionInfoSymbol, damageExpansionInfo);

//详情信息
const detailData = ref<TCompensationFormData>({
  template_data: {},
  money: 0,
  platform_pay_money: 0,
  user_pay_money: 0,
});

//提交侧数据
const templateData = computed({
  get() {
    return props?.data?.template_data || {};
  },
  set(val) {
    emits('update:data', val);
  },
});

//提交侧数据
const formData = computed<Partial<TCompensationFormData>>({
  get() {
    return props?.data || {};
  },
  set(val) {
    emits('update:data', val);
  },
});
const list = ref<TList>({});

const reportParams = ref({});
const reportVisible = ref(false);
function detailReport(type: 'before' | 'after') {
  if (props.isFlex) return;
  const temp = props.isDetail ? detailData.value?.report_data : formData.value?.report_data;
  if (temp && temp[`report_${type}_check_report`]) {
    reportParams.value = temp[`report_${type}_check_report`];
    reportVisible.value = true;
  } else {
    message.warn('检测报告不存在');
  }
}

//判断是否都填
function haveValue(val: TItemValueList) {
  const temp = Object.values(val);
  return temp.every((item, idx) => {
    if (isFromOrderList.value) {
      const keys = Object.keys(val);
      if (val.money_calculation_type && val.money_calculation_type === 1 && keys[idx] === 'ratio') {
        return true;
      }
    }
    return Array.isArray(item) ? item.length > 0 : !!(item?.toString() || 0);
  });
}

//判断是否都没填
function allNoHaveValue(val: TItemValueList) {
  const temp = Object.values(val);
  return temp.every(item => {
    if (isFromOrderList.value) {
      if (Array.isArray(item)) {
        return item.length === 0;
      }
      // null、undefined、空字符串、0 都算没填
      return item === null || item === undefined || item === '' || item === 0;
    } else {
      return Array.isArray(item) ? item.length === 0 : !(item?.toString() || 0);
    }
  });
}

//提交时校验数据与格式化
function checkAndFormatData() {
  const data: any = {};
  return new Promise((resolve, reject) => {
    if (isCanEditAll.value && Number(formData.value.platform_pay_money) > maxPlatformPayMoney) {
      message.error(`平台免赔保障补贴不能大于${maxPlatformPayMoney}`);
      reject();
    }
    const temp: TComTemplateData = JSON.parse(JSON.stringify(templateData.value));
    Object.keys(temp).forEach(key => {
      const title = key === 'custom_item' ? '自定义项' : key;
      let needCheckUrl = false;
      temp[key].list.forEach(item => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { money_calculation_type, ratio, ...ret } = item;
        if (!haveValue(ret) && !allNoHaveValue(ret)) {
          message.error(`${title}有未填写完整的内容`);
          reject();
        } else if (!allNoHaveValue(ret)) {
          needCheckUrl = true;
        }
      });
      if (needCheckUrl && temp[key].url.length === 0) {
        message.error(`${title}未上传凭证照片/视频`);
        reject();
      }
      const tempList = temp[key].list.reduce((acc, cur) => {
        if (haveValue(cur))
          acc.push({
            ...cur,
            id: nanoid(8),
          });
        return acc;
      }, [] as any[]);
      const tempUrl = temp[key].url.map(item => item.url || item);
      if (tempList.length > 0 && tempUrl.length > 0) {
        const flag = !tempUrl.every(item => typeof item === 'string' && item.includes('https'));
        if (flag) {
          message.error('请等待凭证照片、视频上传完毕');
          reject();
        }
        data[key] = {
          list: tempList,
          url: tempUrl,
        };
      }
    });
    const result = Object.keys(data).length > 0 ? data : null;
    resolve(result);
  });
}

//运营获取改动的数据
function getDetailHandleRes() {
  return new Promise((resolve, reject) => {
    if (Number(detailData.value.platform_pay_money) > maxPlatformPayMoney) {
      message.error(`平台免赔保障补贴不能大于${maxPlatformPayMoney}`);
      reject();
    }
    resolve(isCanEditAll.value ? formData.value : detailData.value);
  });
}

//商家修改赔付单详情时获取改动的数据
function getServerDetailHandleRes() {
  return new Promise((resolve, reject) => {
    if (Number(detailData.value.platform_pay_money) > maxPlatformPayMoney) {
      message.error(`平台免赔保障补贴不能大于${maxPlatformPayMoney}`);
      reject();
    }
    if (Number(detailData.value.money) > Number(props.detail?.money)) {
      message.error(`修改的总赔付金额不能大于原总赔付金额${props.detail?.money}`);
      reject();
    }
    resolve(detailData.value);
  });
}
//商家发起小判官时获取选择的数据
function getSelectData() {
  return new Promise((resolve, reject) => {
    const selectTemplateRows: TComTemplateData = {};
    const tempVal = detailData.value.template_data;
    Object.keys(detailData.value.template_data).forEach(key => {
      const tempRows = tempVal[key].selectRows;
      if (tempRows && tempRows?.length) {
        tempVal[key].list.forEach(item => {
          const tempRow = tempRows.find(row => row.id === item.id);
          if (tempRow) {
            item.haveObjection = true;
          }
        });
        selectTemplateRows[key] = {
          list: tempVal[key].list,
          url: tempVal[key].url,
        };
      }
      delete tempVal[key].selectRows;
    });
    const numAndMoeny = getNumAnyMoney(tempVal);
    if (numAndMoeny.tempNum === 0) {
      message.error('请选择异议项');
      reject();
    }
    const userAndPlatform = getPayMoney(tempVal);
    const { damage_pic, report_after, report_before, compensate, is_platform_pay } = detailData.value;
    resolve({
      report_after,
      report_before,
      compensate,
      is_platform_pay,
      needPlatform: is_platform_pay === '1' ? '1' : '0',
      damage_pic,
      template_data: tempVal,
      template_num: numAndMoeny.tempNum,
      money: numAndMoeny.tempMoney,
      platform_pay_money: userAndPlatform.platformPayMoney,
      user_pay_money: userAndPlatform.userPayMoney,
    });
  });
}

const dontInitData = ref(false);
//内部工单清空平台支付金额
function clearPlatform() {
  Object.keys(detailData.value.template_data).forEach(key => {
    detailData.value.template_data[key].list.forEach(item => {
      item.platform_pay_money = 0;
    });
  });
  dontInitData.value = true;
}

const afterImages = ref<TUrl[]>([]);
//获取检测报告
async function getOrderCheckReport() {
  try {
    if (!props.orderId) return;
    const api = isSuper ? getSuperOrderCheckReportApi : getOrderCheckReportApi;
    const { data: res } = await api({
      order_id: props.orderId,
    });
    if (!res.template?.template_data || Object.keys(res.template?.template_data).length === 0) return;
    formData.value.report_data = res;
    if (res.report_after_check_report) formatAfterReportData(res.report_after_check_report);
    if (!res.report_after_check_report && !res.report_before_check_report) {
      chartsPanelVisible.value = false;
    }
    formData.value.report_before = res.report_before?.id || 0;
    formData.value.report_after = res.report_after?.id || 0;
    Object.keys(res.template.template_data).forEach(key => {
      templateData.value[key] = deepClone(isFromOrderList.value ? initDataV2 : initData);
    });
    list.value = res.template.template_data;
    formData.value.report_template_id = res.template?.id;
  } finally {
    list.value = {
      ...list.value,
      custom_item: [
        {
          name: '',
          questions: [],
        },
      ],
    };
  }
}

function isOssImage(url: string) {
  return url && isImage(url) && url.includes('https');
}

/**
 * 获取新版检测报告内的所有图片
 * images
 * imageValue
 * multipleImageValue
 */
function formatAfterReportData(reportData: any) {
  const report = reportData?.report;
  const tempImags: string[] = [];
  report?.images?.forEach((item: any) => {
    setImage(item.value);
  });
  //兼容旧版质检报告
  if (Array.isArray(report)) {
    report.forEach(item => {
      item.list.forEach((e: any) => {
        if (e.type === 'checkbox') {
          e.result.forEach((imgItem: any) => setImage(imgItem.images));
        } else if (e.result.images) {
          setImage(e.result.images);
        }
      });
    });
  } else {
    Object.keys(report?.projectMap || {}).forEach(key => {
      const temp = report?.projectMap[key];
      temp.forEach((item: any) => {
        if (item.type === 'radio') {
          setImage(item.imageValue);
        }
        if (item.type === 'multiple' && item.multipleParentChecked.length > 0) {
          item.multipleParentChecked.forEach((multipleItem: string) => {
            setImage(item.multipleImageValue[multipleItem]);
          });
        }
      });
    });
  }

  function setImage(url: string) {
    if (isOssImage(url)) tempImags.push(url);
  }

  afterImages.value = tempImags.map(item => ({
    url: item,
  }));
}

//对比检测报告
const diffReportLoading = ref(false);
async function getDiffReport() {
  try {
    diffReportLoading.value = true;
    const { report_before, report_after } = formData.value;
    if (!Number(report_before) && Number(report_after)) {
      return message.warn('当前质检报告信息不足，请手动发起赔付单');
    }
    const { data = [] } = await getDiffReportApi({ report_before, report_after, order_id: props.orderId });
    if (data.length === 0) return message.warn('未检测到异常项，请确认是否需要发起赔付');
    compensationItemRef.value.map((item: any) => item?.openCollapse());
    for (const key in templateData.value) {
      const result = data.filter((item: TDiffReport) => item.title === key);
      if (result.length === 0) continue;
      templateData.value[key].list = result.map((item: TDiffReport) => {
        const { qualityTitle, parentChecked, childrenChecked } = item;
        let params: any = {
          item: qualityTitle,
          desc: parentChecked,
          damage_type: '',
          money: '',
        };
        if (childrenChecked) {
          params['degree'] = childrenChecked;
        }
        return params;
      });
      templateData.value[key].url = [];
    }
  } finally {
    diffReportLoading.value = false;
  }
}

function getPayMoney(val: TComTemplateData) {
  let platformPayMoney = 0;
  let userPayMoney = 0;
  let tempMoney = 0;
  if (isCanEditUserPay.value || isCanEditAll.value) {
    const obj = getNumAnyMoney(val);
    tempMoney = Number(obj.tempMoney);
  }
  Object.keys(val).forEach(key => {
    for (let index = 0; index < val[key].list.length; index++) {
      let platform_pay_money = Number(val[key].list[index].platform_pay_money);
      if (!platform_pay_money) continue;
      platformPayMoney = accAdd(platform_pay_money, platformPayMoney);
    }
  });
  const moeny = isCanEditUserPay.value || isCanEditAll.value ? tempMoney : Number(detailData.value.money);
  const temp = accMin(moeny, platformPayMoney);
  if (temp > 0) {
    userPayMoney = temp;
  }
  return {
    platformPayMoney: platformPayMoney.toFixed(2),
    userPayMoney: userPayMoney.toFixed(2),
    tempMoney: tempMoney.toFixed(2),
  };
}

//计算平台与用户支付金额
watch(
  () => [isCanEditPlatform.value, detailData.value],
  () => {
    if ((!isCanEditPlatform.value && !isCanEditUserPay.value && !dontInitData.value) || !detailData.value.template_data)
      return;
    const val = detailData.value.template_data;
    const obj = getPayMoney(val);
    detailData.value.user_pay_money = obj.userPayMoney;
    detailData.value.platform_pay_money = obj.platformPayMoney;
    if (isCanEditUserPay.value) {
      detailData.value.money = obj.tempMoney;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

function getNumAnyMoney(val: TComTemplateData) {
  let tempNum = 0;
  let tempMoney = 0;
  Object.keys(val).forEach(key => {
    val[key].list.forEach(item => {
      const moenyItem = Number(item.money);
      if (haveValue(item) && Number.isFinite(moenyItem)) {
        tempNum++;
        tempMoney = accAdd(moenyItem, tempMoney);
      }
    });
  });
  return {
    tempNum,
    tempMoney: tempMoney.toFixed(2),
  };
}

//新建表单时计算总额与赔付项数量
watch(
  () => templateData.value,
  val => {
    if (!val) return;
    const obj = getNumAnyMoney(val);
    const userAndPlatform = getPayMoney(val);
    formData.value.money = obj.tempMoney;
    formData.value.template_num = obj.tempNum;
    formData.value.user_pay_money = userAndPlatform.userPayMoney;
    formData.value.platform_pay_money = userAndPlatform.platformPayMoney;
  },
  {
    deep: true,
  },
);

function getStatusText(value?: any, linkData?: any) {
  const flag = Number(value) === 1 && Number(linkData) === 0;
  return flag ? '无需支付' : statusTextMap[Number(value) - 1] || '未发起';
}

function getStatusColor(status: string) {
  const text = props.isDetail && !isCanEditAll.value ? detailData.value[status] : formData.value[status];
  const index = statusTextMap.findIndex(item => {
    return item.includes(text);
  });
  return index > -1 ? statusColorMap[index] : '';
}

//运营编辑赔付详情时初始化
watch(
  () => isCanEditAll.value,
  async val => {
    if (!val || !props.detail) return;
    await getOrderCheckReport();
    const { template_data, ...rest } = deepClone(props.detail);
    commonAssign(formData.value, rest, {
      targetKeys: [
        'platform_pay_money',
        'user_pay_money',
        'user_pay_status',
        'platform_pay_status',
        'money',
        'template_num',
        'damage_pic',
      ],
      formatValue: {
        user_pay_status: value => getStatusText(value, rest.user_pay_money),
        platform_pay_status: value => getStatusText(value, rest.platform_pay_money),
      },
    });
    Object.keys(templateData.value).forEach(key => {
      const temp = deepClone(template_data[key]) || deepClone(isFromOrderList.value ? initDataV2 : initData);
      if (temp.list.length > 0) {
        temp.list.forEach(item => {
          if (!('platform_pay_money' in item)) {
            item['platform_pay_money'] = '';
          }
          if (isFromOrderList.value) {
            const money = Number(item.money);
            const userMoney = floatCalculator.subtract(money, Number(item.platform_pay_money));
            item['user_pay_money'] = money === 0 ? '' : userMoney;

            if (!item?.money_calculation_type) {
              item['money_calculation_type'] = 1;
            }
          }
        });
      }
      templateData.value[key] = temp;
    });
    if (!Object.keys(templateData.value).some(item => item === 'custom_item')) {
      templateData.value['custom_item'] = deepClone(isFromOrderList.value ? initDataV2 : initData);
    }
  },
  {
    immediate: true,
  },
);

//详情初始化
function initDetailData() {
  if (dontInitData.value) return;
  detailData.value = deepClone(props.detail);
  const keyList: TList = {};
  if (!detailData.value?.template_data) return;
  const val = detailData.value?.template_data;
  Object.keys(val).forEach(key => {
    val[key].list.forEach(item => {
      if (!item?.platform_pay_money) {
        item['platform_pay_money'] = 0;
      }
      if (isFromOrderList.value) {
        const money = Number(item.money);
        const platformPay = Number(item.platform_pay_money);
        const diff = isNaN(money) || isNaN(platformPay) ? 0 : floatCalculator.subtract(money, platformPay);
        item['user_pay_money'] = money === 0 ? '' : diff;

        if (!item?.money_calculation_type) {
          item['money_calculation_type'] = 1;
        }
      }
    });
    keyList[key] = [{ name: '', questions: [] }];
  });
  if (!isCanEditAll.value) {
    list.value = keyList;
  }
  detailData.value.platform_pay_status = getStatusText(
    detailData.value.platform_pay_status,
    detailData.value.platform_pay_money,
  );
  detailData.value.user_pay_status = getStatusText(detailData.value.user_pay_status, detailData.value.user_pay_money);
  if (!Number(detailData.value.user_pay_money) && !Number(detailData.value.platform_pay_money)) {
    detailData.value.user_pay_money = detailData.value.money;
  }
}

//新建or详情初始化
watch(
  () => [props.orderId, props.isDetail, props.detail],
  () => {
    if (props.isDetail) {
      //处理回显数据
      initDetailData();
    } else if (props.orderId) {
      getOrderCheckReport().finally(() => {
        templateData.value['custom_item'] = deepClone(isFromOrderList.value ? initDataV2 : initData);
      });
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

defineExpose({
  checkAndFormatData,
  getDetailHandleRes,
  clearPlatform,
  getServerDetailHandleRes,
  getSelectData,
  initDetailData,
  chartsPanelVisible,
});
</script>
<style lang="less" scoped>
.common-box {
  position: relative;
}
.flex-box {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;
  .left-box {
    flex: 1;
    width: 60%;
    margin-right: 16px;
    padding: 0 16px;
    overflow: auto;
    background: #fff;
    border-right: 1px solid #e9e9e9;

    .report-box {
      display: flex;
      gap: 12px;
      width: 100%;
      margin-top: 21px;
      padding: 16px;
      background: #f0f2f5;
      .report-item {
        width: calc(50% - 6px);
      }
    }
  }
  .right-box {
    width: 40%;
  }
}
.trigger {
  position: absolute;
  top: 0;
  left: -64px;
  z-index: 9999999999999;
  display: flex;
  width: 40px;
  padding: 16px 12px;
  line-height: 1;
  letter-spacing: 5px;
  white-space: pre-line;
  background: #fff;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  writing-mode: vertical-rl;
  &.expand {
    color: #fff;
    background: var(--ant-primary-color);
  }
  & > span[role='img'] {
    transition: all 0.3s;
  }
}
.test-report {
  height: 22px;
  margin-bottom: 16px;
  .label {
    color: rgba(6, 21, 51, 0.45);
    font-size: 14px;
  }
  .link-btn {
    padding: 0;
  }
}

.diff-box {
  margin-bottom: 24px;
  padding: 16px;
  font-size: 14px;
  background: #f9f9fb;
  border-radius: 4px 4px 4px 4px;
  .title {
    margin-bottom: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    text-align: left;
  }
  .diff-line {
    display: flex;
    gap: 24px;
    align-items: center;
    justify-content: center;
    .diff-label {
      color: #3777ff;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      cursor: pointer;
      text-decoration-line: underline;
    }
    .diff-icon {
      color: rgba(6, 21, 51, 0.85);
      font-weight: bold;
      font-size: 22px;
    }
  }
}

.compensation-item {
  margin-top: 24px;
}

.border-left {
  border-left: 6px solid var(--ant-primary-color);
}
.bottom-price {
  margin-top: 24px;
  padding: 16px;
  background: #f7f9fc;
  border-radius: 4px 0 0 4px;
  :deep(.ant-descriptions-item) {
    padding-bottom: 0 !important;
  }
  :deep(.ant-descriptions-item-container) {
    align-items: center;
  }
  :deep(.ant-descriptions-item-content) {
    align-items: center;
  }
  .red-text {
    color: #ff4d4f;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }
  .common-title {
    width: 120px;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  :deep(.ant-descriptions-item-label::after) {
    content: '';
  }
  .common-text {
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }

  .common-value {
    display: flex;
    align-items: center;
    text-align: right;
  }

  .red-status {
    color: #ff4d4f;
  }

  .green-status {
    color: #52c41a;
  }
}
</style>
