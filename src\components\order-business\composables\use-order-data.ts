import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { intersection } from 'lodash-es';

import type { IAsyncOrderData, IOrderItem, IOrderTableData, TRefreshDataKey } from '@/components/order-business/typing';
import { createGlobalCancel } from '@/hook';
import type { IAxiosResponse } from '@/services/api';
import { objectFormatKey } from '@/utils/base';

import {
  fetchAppleOrderCancelReason,
  fetchOrderCancelReason,
  fetchOrderRemarks,
  fetchOrderStatistics,
  fetchSuperAppleOrderCancelReason,
  getCommodityItem,
  getIssueRemark,
} from './services';

/**
 * 订单数据
 * @param api
 * @param asyncDataKey 需要异步获取数据的key
 * @param extra
 * */
export function useOrderData<T extends IOrderTableData = IOrderTableData, K extends Record<string, any>>(
  api: (params: Record<string, any>) => Promise<IAxiosResponse<T>>,
  asyncDataKey?: (keyof IAsyncOrderData)[],
  extra: K = {},
) {
  if (!api || typeof api !== 'function') {
    message.error('Api 传入错误，请检查配置');
    return;
  }
  const tableLoading = ref(false);
  const tableData = ref<T>();
  const tableTotal = ref(0);
  const tableAlert = ref('');
  const { isManualCancel, send: fetchSearch } = createGlobalCancel(api);
  let bufferSearch: Record<string, any> | undefined = undefined; // 保存搜索条件，可用于刷新
  const isSuper = useRoute().query.role === 'super';

  /**
   * 搜索订单数据
   * @param searchParams 搜索条件, 默认为上次的搜索条件(刷新数据，searchParams为空，则使用上次的搜索条件查询，如果未搜索过时则不执行查询)
   * */
  function searchOrder(searchParams?: Record<string, any>) {
    // 当searchParams为空时，使用上次的搜索条件查询
    const searchData = searchParams || bufferSearch;
    // 无搜索条件不查询
    if (!searchData) {
      return;
    }
    bufferSearch = searchData;

    window.scrollTo({ top: 0 }); // 滚动到顶部

    const params = objectFormatKey(searchData, [
      ['tabValue', 'type'],
      ['pageSize', 'per_page'],
      ['current', 'page'],
      ['showPageCount', 'show_order_count'],
    ]);

    // 去除前后空格，并且对url自动转义进行解码
    for (const key in params) {
      if (typeof params[key] === 'string') {
        params[key] = decodeURIComponent(params[key].trim());
      }
    }
    tableLoading.value = true;

    return fetchSearch(params)
      .then(res => {
        if (res.status === 0) {
          (res.data as T)?.order_list?.forEach(item => (item.async = {}));
          tableAlert.value = '';
          tableData.value = res.data;
          tableTotal.value = tableData.value?.pagination?.total || tableData.value?.order_list?.length || 0;
          getAsyncData(tableData.value?.order_list, asyncDataKey, extra, isSuper);
        } else {
          // 请求失败处理
          tableAlert.value = res.message || '';
        }
        return { isRequestOver: true };
      })
      .catch(err => {
        if (err?.response) {
          // 直接返回字符串的情况处理
          const alertDanger = /(?<=alert-danger[^>]+?>)[^<]+/.exec(err.response.data);
          tableAlert.value = alertDanger ? alertDanger[0].replace(/[\n\r ]/g, '') : '';
          return;
        }
        if (err?.message) {
          tableAlert.value = err.message;
        }
        return { isRequestOver: !isManualCancel() };
      })
      .finally(() => {
        tableAlert.value = tableAlert.value.replace(/:*$/, '');
        // 重复请求时导致提前结束的情况，不结束加载
        if (!isManualCancel()) {
          tableLoading.value = false;
        }
      });
  }

  /**
   * 刷新订单列表中的订单数据
   * @param orderId 需要刷新的订单id, 不传则刷新整个列表
   * @param dataKeys 需要异步获取数据的key，不传入则全部获取
   * @param otherParams 其他参数，会传递给接口
   * @param orderStatus 当前查询条件的订单状态
   * */
  async function refreshOrder(
    orderId?: string,
    dataKeys: TRefreshDataKey[] = [...asyncDataKey, 'data'],
    otherParams: Record<string, any> = {},
    orderStatus?: number,
  ) {
    if (!orderId) {
      searchOrder();
      return;
    }
    if (!tableData.value?.order_list?.length) {
      return;
    }
    const orderIndex = tableData.value.order_list.findIndex(item => item.base_info?.order_id === orderId);
    if (orderIndex === -1) {
      console.warn('已触发刷新，但传入的 orderId 未在订单列表中未找到');
      return;
    }
    // 更新数据的key必须要在默认数据key列表中才会触发刷新
    const defaultDataKeys = [...asyncDataKey, 'data'];
    dataKeys = intersection(dataKeys, defaultDataKeys);
    if (dataKeys.includes('data')) {
      // 重新刷新整条订单数据并异步获取其他数据
      tableLoading.value = true;
      return api({ order_id: orderId, refresh_order: true, ...otherParams })
        .then(res => {
          let orderData = (res.data as T).order_list?.[0];
          // 当未返回订单或订单状态不等于传入的orderStatus时，订单将被移除
          if (!orderData || (orderStatus && orderData.base_info?.order_status !== orderStatus)) {
            orderData = undefined;
            tableData.value.order_list.splice(orderIndex, 1);
            if (tableTotal.value) tableTotal.value--;
          } else {
            orderData.async = tableData.value.order_list[orderIndex].async; // 保留异步接口请求的数据
            tableData.value.order_list.splice(orderIndex, 1, orderData);
            getAsyncData([tableData.value.order_list[orderIndex]], dataKeys, extra, isSuper); // 根据key重新获取对应异步接口的数据
          }
          return { orderData };
        })
        .finally(() => {
          tableLoading.value = false;
        });
    } else {
      // 仅异步获取其他数据
      getAsyncData([tableData.value.order_list[orderIndex]], dataKeys, extra, isSuper);
    }
  }

  return {
    tableLoading,
    tableData,
    tableTotal,
    tableAlert,
    searchOrder,
    refreshOrder,
  };
}

/**
 * 异步获取并拼接订单数据
 * @param orderList 需要处理的订单列表
 * @param dataKeys 需要异步获取数据的key
 * @param extra
 * */
function getAsyncData(orderList?: IOrderItem[], dataKeys?: TRefreshDataKey[], extra = {}, isSuper?: boolean) {
  const orderIds = orderList?.map(item => item.base_info?.order_id);
  const woIds = orderList?.map(item => item.base_info?.id);
  if (orderIds?.length) {
    const dataRequestList: { [key in TRefreshDataKey]: () => void } = {
      statistics_info: () => setOrderStatistics(orderIds, orderList, extra),
      order_cancel_data: () => setCancelReason(orderIds, orderList, isSuper),
      all_remark: () => setCrmList('all', orderIds, orderList),
      super2_remark: () => setCrmList('super2', orderIds, orderList),
      afterSale_remark: () => setAfterSaleRemark('_salesWo.all', woIds, orderList),
      issue_remark: () => setIssueRemark(orderIds, orderList),
      commodity_info: () => setCommodityItem(orderIds, orderList),
    };
    dataKeys?.forEach(key => {
      dataRequestList[key]?.();
    });
  }
}

/** 设置取消原因信息 */
async function setCancelReason(orderIds: string[], orderList?: IOrderItem[], isSuper?: boolean) {
  await fetchOrderCancelReason({ order_id: orderIds }).then(res => {
    const cancelReasonData = res.data?.list || {};
    orderList?.forEach(item => {
      const orderId = item.base_info?.order_id;
      item.async.order_cancel_data = cancelReasonData[orderId] || undefined;
    });
  });

  const fetchApi = isSuper ? fetchSuperAppleOrderCancelReason : fetchAppleOrderCancelReason;
  fetchApi({ order_ids: orderIds }).then(res => {
    const appleCancelReasonData = res.data || {};
    orderList?.forEach(item => {
      const orderId = item.base_info?.order_id;
      if (orderId && appleCancelReasonData[orderId] && !Array.isArray(appleCancelReasonData[orderId])) {
        item.async.order_cancel_data = {
          ...item.async.order_cancel_data,
          cancel_sub_reason: appleCancelReasonData[orderId].name,
          cancel_reason: undefined,
        };
      }
    });
  });
}

/** 获取备注最新一条数据 */
function setCrmList(type: 'all' | 'super2', orderIds: string[], orderList?: IOrderItem[]) {
  fetchOrderRemarks({
    unionIds: orderIds,
    unionSuffix: '_order.' + type,
  }).then(res => {
    const remarkData = Object.fromEntries(res.data.map(item => [item.union_id, item]));
    orderList?.forEach(item => {
      const key = `${item.base_info?.order_id}_order.${type}`;
      if (remarkData[key]) {
        if (type === 'all') {
          item.async.all_remark = remarkData[key];
        } else if (type === 'super2') {
          item.async.super2_remark = remarkData[key];
        }
      }
    });
  });
}

/** 获取售后单最新一条备注数据 */
function setAfterSaleRemark(type: '_salesWo.all', saleIds: string[], orderList?: IOrderItem[]) {
  fetchOrderRemarks({
    unionIds: saleIds,
    unionSuffix: '_salesWo.all',
  }).then(res => {
    const remarkData = Object.fromEntries(res.data.map(item => [item.union_id, item]));
    orderList?.forEach(item => {
      const key = `${item.base_info?.id}${type}`;
      if (remarkData[key]) {
        item.async.afterSale_remark = remarkData[key];
      }
    });
  });
}

/** 查询订单相关统计数据 */
function setOrderStatistics(orderIds: string[], orderList?: IOrderItem[], extra = {}) {
  fetchOrderStatistics({
    order_id: orderIds,
    is_need_area_num: extra?.is_need_area_num,
  }).then(res => {
    const statisticsData = res.data?.list || {};
    orderList?.forEach(item => {
      const orderId = item.base_info?.order_id;
      item.async.statistics_info = statisticsData[orderId] || undefined;
    });
  });
}

/** 代发货订单备注信息 */
function setIssueRemark(orderIds: string[], orderList?: IOrderItem[]) {
  getIssueRemark(orderIds).then(res => {
    const remarkData = res.data || {};
    orderList?.forEach(item => {
      const orderId = item.base_info?.order_id;
      item.async.issue_remark = remarkData[orderId] || undefined;
    });
  });
}

function setCommodityItem(orderIds: string[], orderList?: IOrderItem[]) {
  getCommodityItem({
    order_ids: orderIds,
  }).then(res => {
    orderList?.forEach(item => {
      const orderId = item.base_info?.order_id;
      if (orderId) {
        item.async.commodity_info = res.data.data[orderId] || undefined;
      }
    });
  });
}
