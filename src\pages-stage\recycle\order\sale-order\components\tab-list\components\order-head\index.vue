<template>
  <div class="order-head">
    <div class="order-id flex-center">
      <div class="head-label">
        订单编号
      </div>
      <div>{{ commonHead.rs_order_id }}</div>
      <img
        class="copy-btn"
        src="https://img1.rrzuji.cn/uploads/scheme/2307/08/m/QdZuN8n0tbasjGtuzgc3.png"
        @click="copyToClipboard(commonHead.rs_order_id)"
      >
    </div>
    <div class="create-date flex-center">
      <div class="head-label">
        创建时间
      </div>
      {{ dayjs.unix(Number(commonHead.created_at)).format('YYYY-MM-DD HH:mm:ss') }}
    </div>
    <div
      v-if="getOrderLogisticInfo"
      class="logistic-info flex-center"
    >
      <div class="head-label">
        物流单号
      </div>
      <div
        class="logistic-code"
        @click="logisticRef?.open"
      >
        {{ getOrderLogisticInfo }}
      </div>
      <img
        class="copy-btn"
        src="https://img1.rrzuji.cn/uploads/scheme/2307/08/m/QdZuN8n0tbasjGtuzgc3.png"
        @click="copyToClipboard(commonHead.logistic_code)"
      >
    </div>
  </div>
  <!-- 物流信息弹窗 -->
  <logistic
    ref="logisticRef"
    :logistic-info="commonHead"
  />
</template>

<script setup lang="ts">
import { PropType, computed, ref } from 'vue';
import { copyToClipboard } from '@/utils/base';
import { ICommonHead, IOrderLogisticData } from '../../data';
import dayjs from 'dayjs';
import Logistic from './components/logistic-list/index.vue';

const props = defineProps({
  commonHead: {
    type: Object as PropType<ICommonHead & IOrderLogisticData>,
    default: () => ({}),
  },
});

const logisticRef = ref<InstanceType<typeof Logistic> | null>(null);

// 物流信息
const getOrderLogisticInfo = computed(() => {
  const {
    commonHead: { logistic_name, logistic_code },
  } = props;
  return logistic_name && logistic_code ? `${logistic_name}-${logistic_code}` : '';
});
</script>

<style lang="less" scoped>
.order-head {
  display: flex;
  align-items: center;
  height: 54px;
  color: rgba(6, 21, 51, 0.85);
  font-weight: 500;
  font-size: 14px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: PingFangSC-Medium, PingFang SC;
  .head-label {
    padding-left: 16px;
    &::after {
      content: '：';
    }
  }
  .copy-btn {
    margin-left: 8px;
    vertical-align: middle;
    cursor: pointer;
  }
  .flex-center {
    display: flex;
    align-items: center;
  }
  .order-id {
    min-width: 400px;
  }
  .create-date {
    min-width: 304px;
  }
  .logistic-info {
    min-width: 390px;
    .logistic-code {
      color: #3777ff;
      font-weight: 400;
      cursor: pointer;
    }
  }
}
</style>
