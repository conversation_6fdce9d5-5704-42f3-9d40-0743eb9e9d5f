<template>
  <a-form
    ref="formRef"
    layout="inline"
    :model="formInfo"
  >
    <a-form-item
      label="业务分类"
      name="source_type"
    >
      <a-select
        v-model:value="formInfo.source_type"
        :options="optionsManage.source_type"
        placeholder="请选择"
        style="width: 184px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="品类"
      name="category_brand_model"
    >
      <a-cascader
        v-model:value="formInfo.category_brand_model"
        allow-clear
        change-on-select
        :load-data="spuDataListloadData"
        :max-tag-count="2"
        multiple
        :options="breedList"
        placeholder="请选择"
        style="width: 212px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item
      label="SKU ID"
      name="sku_id"
    >
      <a-input
        v-model:value="formInfo.sku_id"
        allow-clear
        :maxleng="60"
        placeholder="请输入"
        style="width: 193px"
        @change="onEmit"
      />
    </a-form-item>
    <a-form-item>
      <a-button
        style="margin-right: 8px"
        type="primary"
        @click="emits('confirm', 'search')"
      >
        查询
      </a-button>
      <a-button @click="onReset">
        重置
      </a-button>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, PropType } from 'vue';
import { useCategory } from '../composables/use-category';
import { ISearchInfo } from '../data.d';
import { getTableSearchList } from '../service';

const props = defineProps({
  value: {
    type: Object as PropType<ISearchInfo>,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:value', 'confirm']);

const formRef = ref<HTMLElement | any>(null);

const formInfo = ref<ISearchInfo>({
  source_type: '0',
  category_brand_model: [],
  sku_id: '',
});

const optionsManage = reactive({
  source_type: [],
});

const getOptionsData = async () => {
  const res = await getTableSearchList();
  const data = res.data || {};
  const keysArr = Object.keys(data);
  keysArr.forEach(key => {
    if (optionsManage.hasOwnProperty(key)) {
      optionsManage[key] = data[key] || [];
    }
  });
};

const onEmit = () => {
  emits('update:value', formInfo.value);
};

const onReset = () => {
  formRef.value?.resetFields();
  onEmit();
  emits('confirm', 'search');
};

const { spuDataListloadData, breedList } = useCategory();

watch(
  () => props.value,
  newValue => {
    formInfo.value = newValue || {};
  },
  {
    immediate: true,
  },
);

getOptionsData();
</script>
