<template>
  <div class="form-wrap">
    <a-form
      ref="searchFormRef"
      :model="formData"
    >
      <a-row :gutter="24">
        <a-col :span="4">
          <a-form-item
            label="销售部门"
            name="dept_id"
          >
            <a-select
              v-model:value="formData.dept_id"
              :disabled="salesDeptList.length === 1"
              :options="salesDeptList"
              placeholder="请选择销售部门"
              style="width: 100%"
              @change="handleGetSalesList"
            />
          </a-form-item>
        </a-col>
        <a-col :span="3.5">
          <a-form-item
            label="销售人员"
            name="dept_type"
          >
            <a-radio-group v-model:value="formData.dept_type">
              <a-radio-button
                v-for="child in options.typeList"
                :key="child.value"
                :value="child.value"
              >
                {{ child.label }}
              </a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col
          v-if="formData.dept_type === 2"
          :span="5"
        >
          <a-form-item
            label=""
            name="sales_ids"
          >
            <a-select
              v-model:value="formData.sales_ids"
              allow-clear
              mode="multiple"
              :options="salesList"
              placeholder="请选择销售部门人员"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="4">
          <a-form-item
            label="时间"
            name="month"
          >
            <a-date-picker
              v-model:value="formData.month"
              picker="month"
              placeholder="请选择时间"
              style="width: 100%"
              value-format="YYYY-MM"
            />
          </a-form-item>
        </a-col>
        <a-col>
          <a-form-item
            label="统计时段"
            name="time_type"
          >
            <a-radio-group v-model:value="formData.time_type">
              <a-radio-button
                v-for="child in options.rangeList"
                :key="child.value"
                :value="child.value"
              >
                {{ child.label }}
              </a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, defineProps } from 'vue';
import { FormInstance } from 'ant-design-vue';
import { apiGetSalesList } from '../../services';
import { ISelectItem, IFormData } from '../../data';
import { useVModel } from '@/hook';

interface IProps {
  salesDeptList: ISelectItem[];
  modelValue: IFormData;
}

interface ISelectInfo {
  typeList: ISelectItem[];
  rangeList: ISelectItem[];
}

const props = defineProps<IProps>();
const emit = defineEmits(['update:modelValue']);
const formData = useVModel(props, 'modelValue', emit);
const salesList = ref<ISelectItem[]>([]);
const searchFormRef = ref<FormInstance>();
const options = reactive<ISelectInfo>({
  typeList: [
    {
      label: '全部销售',
      value: 1,
    },
    {
      label: '单个销售',
      value: 2,
    },
  ],
  rangeList: [
    {
      label: '一年',
      value: 1,
    },
    {
      label: '6个月',
      value: 2,
    },
    {
      label: '三个月',
      value: 3,
    },
  ],
});

const handleGetSalesList = async (dept_id: string | number) => {
  const { data } = await apiGetSalesList({ dept_id });
  salesList.value = data.map((item: any) => ({
    label: item.username,
    value: item.id,
  }));
};

// 处理销售选项
watch(
  () => formData.value.dept_type,
  type => {
    if (type === 2 && !formData.value.dept_id) {
      salesList.value = [];
    }
    formData.value.sales_ids = [];
  },
);

// 处理是否选择销售列表
watch(
  () => props.salesDeptList,
  async val => {
    if (val.length === 1) {
      handleGetSalesList(val[0].value);
    }
  },
);

// 处理销售人员最多可选三项
watch(
  () => formData.value.sales_ids,
  sales_ids => {
    salesList.value = salesList.value.map(item => ({
      ...item,
      disabled: sales_ids.length && sales_ids.length === 3 ? !sales_ids.includes(item.value + '') : false,
    }));
  },
);
</script>

<style scoped lang="less">
.form-wrap {
  .form-item-flex {
    display: flex;
  }
}
</style>
