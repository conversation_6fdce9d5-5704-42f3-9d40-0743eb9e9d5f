<template>
  <a-modal
    :title="title"
    :visible="bindVisible"
    @cancel="handleCancel"
  >
    <div class="container">
      <div class="block">
        应发货品：{{ data?.before_up_sku_info }}
      </div>
      <div class="block">
        <div class="text">
          下发货品：{{ data?.after_up_sku_info }}
        </div>
        <div class="tag">
          <a-tag color="cyan">
            {{ Number(data?.is_support) === 1 ? '租赁服务设备' : '非租赁服务设备' }}
          </a-tag>
          <a-tag color="#108ee9">
            {{ Number(data?.is_checked) === 1 ? '送检' : '非送检' }}
          </a-tag>
          <a-tag
            v-if="data?.storage_memory_up"
            color="orange"
          >
            {{ Number(data?.storage_memory_up) ? '补贴发货' : '' }}
          </a-tag>
          <a-tag
            v-if="data?.attr_tag"
            color="#9eb7fa"
          >
            随机{{ data.attr_tag }}
          </a-tag>
          <a-tag
            v-if="data?.invoice"
            color="#de858e"
          >
            {{ Number(data.invoice) === 2 ? '有票' : '无票' }}
          </a-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button @click="handleCancel">
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    default: '自动下发成功',
  },
});

const emits = defineEmits(['update:visible']);
const bindVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emits('update:visible', value);
  },
});

const handleCancel = () => {
  bindVisible.value = false;
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.block {
  padding: 16px;
  color: #333;
  background-color: #ecf4ff;
  .tag {
    margin-top: 16px;
  }
}
</style>
