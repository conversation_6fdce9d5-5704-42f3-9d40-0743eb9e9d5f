<template>
  <a-alert type="warning">
    <template #message>
      <a-spin :spinning="pageOptiLoading">
        <div
          class="flex-wrap flex-vertical flex-gap-4"
          style="color: #ff4d4f"
        >
          <template v-if="pageOpti">
            <div>
              1、【上门自取】与【上门安装】无需填写快递单号。若用户为【上门自取/安装】，需让租户在回单上写明"上门自取/安装，本人确认已收到设备"。请商家注意收集并录入凭证，如租户清晰身份证正反图片、纸质签收回单上须有租户签名、沟通记录等。如有原件，注意保存原件
            </div>
            <div>
              2、为了规避经营风险，需上传设备串码、手机包装盒存证、发货图片，请务必认真填写，平台将进行不定期巡查，处罚标准可参考商家后台规则中心《码商订单设备串码处罚规则》。出现处罚商家需及时进行处理
            </div>
            <div>3、一个订单如有多台机器，仅需要录入其中一台设备串码即可；</div>
            <div>
              4、一个订单如有多台机器，手机包装盒存证处，全新手机请上传所有的手机包装盒正背面，二手手机上传所有的手机正背面
            </div>
          </template>
          <template v-else>
            <div>提示：【上门自取】与【上门安装】无需填写快递单号。</div>
            <div>
              请商家注意收集并录入凭证，<span style="font-weight: bold">如租户清晰身份证正反图片、纸质签收回单上须有租户签名、沟通记录等。</span>如有原件，注意保存原件。
            </div>
            <div>若用户为【上门自取/安装】，需让租户在回单上写明"上门自取/安装，本人确认已收到设备"。</div>
            <template v-if="extraData.offline_contract_show">
              <div>
                线下租赁服务协议：用户与商家在线下签署的租赁协议，上传时需要完整且清晰地展现协议的所有内容<b>(若不上传，后续产生客诉，平台将以线上协议作为判断纠纷的依据)；</b>
              </div>
              <div>
                其他补充资料：可以证明用户已签协议(例如手持合同拍照)、已提车(签字的提车证明)、已发货的凭证(寄给用户的物流单)等;
              </div>
              <div>
                如发生客诉，上传的补充资料可快速厘清责任，避免因凭证不足，产生的客诉拉扯，增加商家自身处理时间；
              </div>
            </template>
            <template v-if="extraData.ms_loan_limit">
              <div>
                为了规避经营风险，需上传设备串码及手机包装盒正背面（人人租管家app扫一扫功能支持扫描设备条码自动识别）；
              </div>
              <div>注意：</div>
              <div>1、一个订单仅需要录入一台设备串码即可；</div>
              <div>2、全新手机上传手机包装盒正背面，二手手机上传手机正背面；</div>
            </template>
          </template>
        </div>
      </a-spin>
    </template>
  </a-alert>

  <a-tabs
    v-model:active-key="state.shipmentType"
    class="clear-tab"
    @change="handleTabChange"
  >
    <a-tab-pane
      v-if="!isSuper"
      key="pickUpAndShip"
      tab="取件发货服务"
    />
    <a-tab-pane
      key="delivery"
      tab="上传快递单号"
    />
    <a-tab-pane
      key="other"
      tab="用户自提/上门安装"
    />
  </a-tabs>

  <a-alert
    v-if="isDelivery && deliveryTip"
    show-icon
    type="warning"
  >
    <template #message>
      <div style="color: #ff4d4f">
        用户已修改收货信息，发货前请再次确认快递单收货信息是否正确
      </div>
    </template>>
  </a-alert>

  <a-form
    ref="formRef"
    :label-col="{ style: { width: '180px' } }"
    :model="state"
    :rules="rules"
    :wrapper-col="{ style: { flex: '0 0 365px' } }"
  >
    <!-- 取件发货服务 -->
    <template v-if="state.shipmentType === 'pickUpAndShip'">
      <a-spin :spinning="pickUpLoading">
        <a-alert
          v-if="isWithhold"
          message="当前订单未授权免密代扣或预授权时，暂时无法使用平台物流服务。"
          show-icon
          style="margin-bottom: 24px"
          type="error"
        >
          <template #icon>
            <exclamation-circle-filled style="margin-right: 18px; color: #ff4d4f; font-size: 22px" />
          </template>
        </a-alert>

        <a-form-item label="订单号">
          {{ orderId }}
        </a-form-item>
        <a-form-item>
          <template #label>
            配送方式(寄出)
            <InfoCircleOutlined
              :style="{
                color: '#aeb1bb',
                fontSize: '14px',
                marginLeft: '5px',
              }"
              @click="openSendTypeTip"
            />
          </template>
          {{ sendTypeText }}
        </a-form-item>

        <a-form-item
          label="设备串码"
          name="imei"
        >
          <p
            v-for="item in deviceList"
            :key="item.value"
          >
            {{ item.label }}：{{ item.value }}
          </p>
          <template #extra>
            <div style="color: #ff4d4f">
              可手动输入或在商家APP订单列表对应订单处使用扫一扫功能录入设备串码。（串码类型未做限制）
            </div>
          </template>
        </a-form-item>

        <a-form-item
          v-if="state.pickUpFormState.express_id != 'SS' || state.pickUpFormState.ss_express_type != 'shanSong'"
          label="联系方式"
          :name="['pickUpFormState', 'phone']"
          required
        >
          <a-input v-model:value="state.pickUpFormState.phone" />
        </a-form-item>

        <template v-if="isPengquanOrder">
          <ScmDeviceForm
            ref="deviceFormRef"
            :order-id="props.orderId"
            :sku-options="skuOptions"
            :warehouse-list="data.warehouse_list"
            @handle-change-code="handleChangeCode"
          />
        </template>

        <a-form-item
          label="寄出快递"
          required
        >
          <a-select
            v-model:value="state.pickUpFormState.express_id"
            :options="sendExpressOptions"
            placeholder="请选择寄出快递"
            @select="refreshTimeOptions"
          />
        </a-form-item>

        <!-- 顺丰服务类型 -->
        <a-form-item
          v-if="state.pickUpFormState.express_id === 'SF'"
          :name="['pickUpFormState', 'sf_express_type']"
          required
        >
          <template #label>
            服务类型
            <InfoCircleOutlined
              :style="{
                color: '#aeb1bb',
                fontSize: '14px',
                marginLeft: '5px',
              }"
              @click="handleOpen"
            />
          </template>
          <a-radio-group
            v-model:value="state.pickUpFormState.sf_express_type"
            style="display: flex; flex-direction: row; width: 400px; margin-top: 6px"
          >
            <a-radio
              v-for="(item, index) in sfExpressTypeOptions"
              :key="index"
              :value="item.value"
            >
              <div class="radio-text">
                <a-tag
                  v-if="item.tips"
                  class="tips-text"
                >
                  {{ item?.tips }}
                </a-tag>

                <div class="radio-label">
                  {{ item.label }}
                </div>
                <div class="radio-label-text">
                  {{ item.desc }}
                </div>
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 同城闪送服务类型 -->
        <a-form-item
          v-if="state.pickUpFormState.express_id === 'SS'"
          :name="['pickUpFormState', 'ss_express_type']"
          required
        >
          <template #label>
            服务类型
            <InfoCircleOutlined
              :style="{
                color: '#aeb1bb',
                fontSize: '14px',
                marginLeft: '5px',
              }"
              @click="handleOpen"
            />
          </template>
          <a-radio-group
            v-model:value="state.pickUpFormState.ss_express_type"
            style="display: flex; flex-direction: row; width: 400px; margin-top: 6px"
            @change="refreshTimeOptions"
          >
            <a-radio
              v-for="(item, index) in ssExpressTypeOptions"
              :key="index"
              :value="item.value"
            >
              <div class="radio-text">
                <a-tag
                  v-if="item.tips"
                  class="tips-text"
                >
                  {{ item?.tips }}
                </a-tag>

                <div class="radio-label">
                  {{ item.label }}
                </div>
                <div class="radio-label-text">
                  {{ item.desc }}
                </div>
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <template
          v-if="state.pickUpFormState.express_id === 'SS' && state.pickUpFormState.ss_express_type === 'shanSong'"
        >
          <a-form-item
            label="取货门店"
            :name="['pickUpFormState', 'server_store_id']"
            required
          >
            <a-select
              v-model:value="state.pickUpFormState.server_store_id"
              :options="shanSongOption"
              placeholder="请选择取货门店"
              style="width: 100%"
              @change="handelSeverStore"
            />
          </a-form-item>
          <a-form-item
            label="联系方式"
            :name="['pickUpFormState', 'store_contact_id']"
            required
          >
            <a-select
              v-model:value="state.pickUpFormState.store_contact_id"
              :options="shanSongContactOption"
              placeholder="请选择联系方式"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="取件地址">
            {{ collectionAddress }}
          </a-form-item>
          <!-- <a-form-item
            label="上门取件时间"
            :name="['pickUpFormState', 'ss_send_start_time']"
          >
            <a-date-picker
              v-model:value="state.pickUpFormState.ss_send_start_time"
              format="YYYY-MM-DD HH:mm"
              placeholder="请选择上门取件时间"
              show-time
              value-format="YYYY-MM-DD HH:mm"
              @change="handleTimeChange"
            />
            <div class="ss-time-tip">
              （只支持一个小时以后两天以内）
            </div>
          </a-form-item> -->
        </template>
        <template
          v-if="state.pickUpFormState.express_id != 'SS' || state.pickUpFormState.ss_express_type != 'shanSong'"
        >
          <a-form-item
            label="取件地址"
            :name="['pickUpFormState', 'address_id']"
            required
          >
            <a-select
              v-model:value="state.pickUpFormState.address_id"
              :options="addressOptions"
              placeholder="请选择取件地址"
              @select="refreshTimeOptions"
            />
          </a-form-item>
          <a-form-item
            label="上门取件时间"
            :name="['pickUpFormState', 'send_start_time']"
            required
          >
            <a-cascader
              v-model:value="state.pickUpFormState.send_start_time"
              :disabled="!state.pickUpFormState.address_id || !isSuccessGet"
              :options="timeOptions"
              placeholder="请选择"
            />
          </a-form-item>

          <a-form-item required>
            <template #label>
              已选服务
              <exclamation-circle-outlined
                style="margin: 0 3px; color: rgba(6, 21, 51, 0.45)"
                @click="toServicePng"
              />
            </template>
            <div>
              <Checkbox
                v-if="state.pickUpFormState.express_id !== 'JD'"
                v-model:checked="state.pickUpFormState.is_sign"
                :checked-value="1"
                class="checkbox-item"
                :un-checked-value="0"
              >
                微签
              </Checkbox>
              <Checkbox
                v-if="state.pickUpFormState.express_id === 'JD'"
                v-model:checked="state.pickUpFormState.is_chain_sign"
                :checked-value="1"
                class="checkbox-item"
                :un-checked-value="0"
              >
                微签
              </Checkbox>
              <Checkbox
                v-model:checked="state.pickUpFormState.is_insure"
                :checked-value="1"
                class="checkbox-item"
                :un-checked-value="0"
              >
                保价
              </Checkbox>
              <Checkbox
                v-if="state.pickUpFormState.express_id !== 'JD'"
                v-model:checked="state.pickUpFormState.is_packed"
                :checked-value="1"
                class="checkbox-item"
                :un-checked-value="0"
              >
                包装
              </Checkbox>
              <a-tooltip placement="topLeft">
                <template
                  v-if="state.pickUpFormState.express_id !== 'JD' && isSuper"
                  #title
                >
                  抱歉，目前顺丰纸质签回单功能未开放，建议您使用微签服务。如仍坚持需要可以选择使用京东快递
                </template>
                <Checkbox
                  v-model:checked="state.pickUpFormState.is_sign_paper"
                  :checked-value="1"
                  class="checkbox-item"
                  :disabled="state.pickUpFormState.express_id !== 'JD' && isSuper"
                  :un-checked-value="0"
                >
                  纸质签回单
                </Checkbox>
              </a-tooltip>
            </div>
            <div
              v-if="showRemind && state.pickUpFormState.is_sign"
              style="margin-top: 5px; color: #faad14"
            >
              {{ remindText }}
            </div>
          </a-form-item>
        </template>
        <a-alert
          show-icon
          type="warning"
        >
          <template #message>
            提示：选择【闪送】服务类型时，点击【确定】后，平台会弹出弹窗为你预测闪送费用以及闪送接单时间，再次点击【发起闪送】才会为你呼叫骑手。<span
              style="color: #faad14"
            >【预测有效期为30分钟，超时需要重新发货】</span>
          </template>
        </a-alert>
      </a-spin>
    </template>

    <!-- 上传快递单号/用户自提/上门安装 -->
    <template v-else>
      <a-form-item
        :label="isDelivery ? '快递公司' : '配送方式'"
        :name="['form', 'shipper_code']"
        required
      >
        <a-select
          v-model:value="state.form.shipper_code"
          :allow-clear="true"
          :options="isDelivery ? companyOptions : otherOptions"
          placeholder="请选择"
        />
      </a-form-item>

      <a-form-item
        v-show="isDelivery"
        label="快递单号"
        :name="['form', 'logistic_code']"
        :required="isDelivery"
      >
        <a-input
          v-model:value="state.form.logistic_code"
          placeholder="运单号一般为10~14位数字"
        />
      </a-form-item>
      <a-form-item label="设备串码">
        <p
          v-for="item in deviceList"
          :key="item.value"
        >
          {{ item.label }}：{{ item.value }}
        </p>
        <template #extra>
          <div style="color: #ff4d4f">
            可手动输入或在商家APP订单列表对应订单处使用扫一扫功能录入设备串码。（串码类型未做限制）
          </div>
        </template>
      </a-form-item>

      <template v-if="isPengquanOrder">
        <ScmDeviceForm
          ref="deviceFormRef"
          :order-id="props.orderId"
          :sku-options="skuOptions"
          :warehouse-list="data.warehouse_list"
          @handle-change-code="handleChangeCode"
        />
      </template>

      <template v-if="extraData.ms_loan_limit">
        <a-form-item
          :label="pageOpti ? '设备识别码（即IMEI码）' : '手机序列号（IMEI码）'"
          :name="['form', 'ms_loan_limit_imei']"
          required
          :rules="pageOpti && { validator: validateMsLoanLimitImei }"
        >
          <a-input
            v-model:value="state.form.ms_loan_limit_imei"
            :placeholder="pageOpti ? '请输入设备识别码（15位数，安卓86开头，苹果35开头）' : '请输入手机序列号'"
            style="width: 100%"
          />
          <template
            v-if="pageOpti"
            #extra
          >
            <div style="color: #ff4d4f">
              串码一旦保存作为存证不可修改，请务必认真填写
            </div>
          </template>
        </a-form-item>
        <a-form-item
          label="手机包装盒存证"
          :name="['form', 'ms_loan_limit_image']"
        >
          <div>
            <a-upload
              v-model:file-list="state.form.ms_loan_limit_image"
              accept=".jpg, .jpeg, .png"
              :custom-request="(option: any) => handleCustomRequest(option)"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <div>
                <plus-outlined />
                <div style="margin-top: 8px">
                  添加图片
                </div>
              </div>
            </a-upload>
            <div class="img-tips">
              {{ pageOpti ? '全新手机上传手机包装盒正背面，二手手机上传手机正背面' : '请上传手机包装盒正背面' }}
            </div>
          </div>
        </a-form-item>
      </template>

      <template v-if="extraData.offline_contract_show">
        <a-form-item
          label="线下租赁协议"
          :name="['form', 'is_support_offline_contract']"
        >
          <a-select
            v-model:value="state.form.is_support_offline_contract"
            :allow-clear="true"
            :options="[
              { label: '是', value: '1' },
              { label: '否', value: '0' },
            ]"
            placeholder="请选择"
          />
        </a-form-item>
        <a-form-item
          :colon="false"
          label=" "
          :name="['form', 'contract_file_1']"
        >
          <div class="flex-wrap flex-vertical flex-gap-8">
            <div class="img-tips">
              请上传线下租赁协议（线下协议选 "是" 时必填）
            </div>
            <a-upload
              v-model:file-list="state.form.contract_file_1"
              accept=".jpg, .jpeg, .png"
              :custom-request="(option: any) => handleCustomRequest(option,0)"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <div>
                <plus-outlined />
                <div style="margin-top: 8px">
                  添加图片
                </div>
              </div>
            </a-upload>
          </div>
        </a-form-item>

        <a-form-item
          label="存证标题"
          :name="['form', 'contract_title_2']"
        >
          <a-input
            v-model:file-list="state.form.contract_title_2"
            placeholder="请输入存证标题"
          />
        </a-form-item>
        <a-form-item
          :colon="false"
          label=" "
          :name="['form', 'contract_file_2']"
        >
          <div class="flex-wrap flex-vertical flex-gap-8">
            <div class="img-tips">
              请上传用户手持证明等其他资料（选填）
            </div>

            <a-upload
              v-model:file-list="state.form.contract_file_2"
              accept=".jpg, .jpeg, .png"
              :custom-request="(option: any) => handleCustomRequest(option,1)"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <div>
                <plus-outlined />
                <div style="margin-top: 8px">
                  添加图片
                </div>
              </div>
            </a-upload>
          </div>
        </a-form-item>
      </template>

      <a-form-item
        label="上传发货照片/视频"
        :name="['form', 'image']"
      >
        <file-upload
          v-model:value="stateFormImage"
          action="site"
          :max="8"
          quality="o"
        />
        <div class="img-tips">
          请上传此订单的高清实拍发货图片，最多支持上传8张图片
        </div>
        <div class="img-tips">
          为避免后续产生纠纷，请务必在发货前拍摄【设备细节图】和【发货图片】
        </div>
      </a-form-item>
    </template>
  </a-form>

  <!-- 配件按信息展示 -->
  <Accessory :accessory-data="accessoryData" />
  <!-- 自定义图片预览弹窗 -->
  <a-modal
    :footer="null"
    :title="previewTitle"
    :visible="previewVisible"
    @cancel="handleCancel"
  >
    <img
      alt="example"
      :src="previewImage"
      style="width: 100%"
    >
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, toRaw, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ExclamationCircleFilled,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';
import { useCompRef } from 'rrz-web-design';

import { RequestTypeEnum, useAddressRealRemind } from '@/components/order-business/composables/use-address-real-remind';
import { useLogisticOption } from '@/components/order-business/composables/use-logistic-option';
import { useSendType } from '@/components/order-business/composables/use-send-type';
import type { IStringOption } from '@/components/order-business/typing';

import Checkbox from '../../../../pickup-appoint-service-modal/pick-up-service-modal/components/checkbox.vue';
import { useTimeOptions } from '../../../../pickup-appoint-service-modal/pick-up-service-modal/componsables/use-time-options';
import {
  checkOrderClosing,
  getExpressDiscount,
  getExpressType,
  getOperateMark,
  getSuLogistics,
} from '../../../../pickup-appoint-service-modal/pick-up-service-modal/services';
import type { IAccessoryData, IDeliverStore, IExtraData } from '../../../data';
import {
  apiAdminGetShanSongList,
  apiMerchantGetShanSongList,
  getDictTree,
  getPageOptiFlag,
  uploadImg,
} from '../../../services';
import { fetchReceivingAddressLog } from '../../../services.ts';
import Accessory from './components/accessory-card.vue';
import ScmDeviceForm from './components/scm-device-form.vue';
import FileUpload from './file-upload/index.vue';

defineOptions({ inheritAttrs: false });

const route = useRoute();
const isSuper = route.query.role === 'super';

const props = defineProps<{
  accessoryData: IAccessoryData;
  extraData: IExtraData;
  orderId: string;
  serverId: number;
  data: IDeliverStore;
  setIsPickMode: (bool: boolean) => void;
}>();

const deviceFormRef = useCompRef(ScmDeviceForm);
const deliveryTip = ref(null);

const { logisticOptions } = useLogisticOption();

const companyOptions = computed(() => {
  return logisticOptions.value?.filter(({ value }) => ![-1, -2].includes(value)) || [];
});
const otherOptions = computed(() => {
  return logisticOptions.value?.filter(({ value }) => [-1, -2].includes(value)) || [];
});

/** 设备串码列表 */
const deviceList = ref<{ label: string; value: string }[]>([]);

const formRef = ref();
const state = reactive<{
  shipmentType: 'pickUpAndShip' | 'delivery' | 'other';
  form: any;
  pickUpFormState: any;
}>({
  shipmentType: isSuper ? 'delivery' : 'pickUpAndShip',
  form: {
    shipper_code: undefined,
    logistic_code: undefined,
    ms_loan_limit_imei: '',
    ms_loan_limit_image: [],
    is_support_offline_contract: '1',
    contract_file_1: [],
    contract_title_2: '',
    contract_file_2: [],
    image: [],
  },
  pickUpFormState: {},
});

const stateFormImage = ref({});
watch(
  () => stateFormImage.value,
  (newVal) => {
    const arr = newVal?.value;
    if (!Array.isArray(arr)) return;
    state.form.image = [];
    state.form.image = arr && [...arr];
  }, {
    immediate: true,
  }
)

const tabMaps = {
  pickUpAndShip: '取件发货服务',
  delivery: '上传快递单号',
  other: '用户自提/上门安装',
};

const isPengquanOrder = computed(() => {
  const { is_pengquan_order } = props.data;
  return is_pengquan_order && !isSuper;
});

function handleTabChange(value: string) {
  (window as any).sensors.track({
    key: '$OrderListClick',
    customProperties: {
      $elementType: 'OrderListShipment',
      $elementContent: tabMaps[value],
    },
  });
  if (isPengquanOrder.value) {
    deviceFormRef.value?.handleResetFields();
  }
}

// 快递配送方式
const isDelivery = computed(() => state.shipmentType === 'delivery');
const supportContract = computed(() => state.form.is_support_offline_contract === '1');

const rules = computed(() => {
  return {
    shipper_code: [{ required: true, message: isDelivery.value ? '请选择快递公司' : '请选择配送方式' }],
    contract_file_1: [
      {
        validator: (_, value) => {
          if (supportContract.value && !value?.length) return Promise.reject('请上传线下租赁协议');
          return Promise.resolve();
        },
      },
    ],
  };
});

// 自定义a-upload api
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

async function handlePreview(file: any) {
  if (!file.url && !file.preview) {
    file.preview = (await getBase64(file.originFileObj)) as string;
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
}

function handleCancel() {
  previewVisible.value = false;
  previewTitle.value = '';
}

function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

async function handleCustomRequest(option: any, type?: number) {
  const data = new FormData();
  data.append('uploadfile', option.file);
  data.append('upload_from', 'server_v3_order');
  await uploadImg(data)
    .then(res => {
      if (res.data.status === 1) {
        option.file.response = res.data;
        option.onSuccess(res.data.oss_url);
      } else {
        message.success(res.data.msg);
      }
    })
    .catch(() => {
      message.error('文件上传失败');
      if ([0, 1].includes(type as number)) {
        state.form[`contract_file_${(type as number) + 1}`].pop();
      } else {
        state.form.ms_loan_limit_image.pop();
      }
    });
}

const { sendTypeText, isWithhold, getSuLogisticsData } = useSendType({
  api: getSuLogistics,
});

const pickUpLoading = ref(false);

function getDeviceList() {
  const deviceLists: IStringOption[] = [];
  props.extraData.item_array?.forEach(item => {
    const fItem = props.extraData.imei_code_data?.find(f => +f.sort === +item) || {};
    deviceLists.push({
      label: item,
      value: fItem.code || '',
    });
  });
  deviceList.value = deviceLists;
}

async function fetchAndUpdateDeliveryTip() {
  try {
    const res = await fetchReceivingAddressLog({ order_id: props.orderId });
    const logData = res.data || [];
    const logItem = logData.find(i => i.type === 2 && i.action_type === 1);
    deliveryTip.value = !!logItem;
  } catch (error) {
    deliveryTip.value = false;
  }
}

watch(
  () => state.shipmentType,
  async value => {
    // 重置表单
    formRef.value?.resetFields();

    // 统一处理非 'pickUpAndShip' 情况
    if (value !== 'pickUpAndShip') {
      props.setIsPickMode(false);
      // 获取设备串码
      getDeviceList();
    }

    if (value === 'pickUpAndShip') {
      setupPickUp();
      props.setIsPickMode(true);
    } else if (value === 'delivery' && deliveryTip.value === null) {
      await fetchAndUpdateDeliveryTip();
    }

    // 获取顺丰物流数据
    getSuLogisticsData(props.orderId);
  },
  {
    immediate: true,
  },
);

const descObj = {
  2: '首重(1.0kg)12.0元起',
  255: '首重(20kg)40元起',
  263: '首重(1.0kg)13元起',
  1: '首重(1.0kg)15元起',
};

// 闪送门店列表
const shanSongDataList = ref([]);
// 闪送门店列表下拉选项
const shanSongOption = ref([]);
// 联系人下拉选项
const shanSongContactOption = ref([]);
// 取件地址
const collectionAddress = ref('');

function handelSeverStore(value: number) {
  const shanSongItem = shanSongDataList.value.find(item => item.id === value);
  shanSongContactOption.value = shanSongItem.store_contact.map(item => ({ label: item.contact_phone, value: item.id }));
  collectionAddress.value = shanSongItem.store_address + shanSongItem.store_address_desc;
  // 联系方式默认选择默认联系人
  state.pickUpFormState.store_contact_id = shanSongItem.store_contact.find(item => item.is_default === 1).id;
}

function setupPickUp() {
  if (route.name.includes('merchantShipmentsWorkbench')) {
    getOperateMark({ type: 6 });
  }

  pickUpLoading.value = true;
  // 生成账单判断是否订单处于取消中
  checkOrderClosing({ orderId: props.orderId })
    .then(async () => {
      // ============================= 获取设备串码列表 =================================
      const deviceLists: IStringOption[] = [];
      props.extraData.item_array?.forEach(item => {
        const fItem = props.extraData.imei_code_data?.find(f => +f.sort === +item) || {};
        deviceLists.push({
          label: item,
          value: fItem.code || '',
        });
      });
      deviceList.value = deviceLists;
      // ============================= 获取顺丰上门取件类型 =================================
      const { data: countRes } = await getExpressDiscount({ order_id: props.orderId });
      const { data: orderRes } = await getExpressType({ order_id: props.orderId });
      sfExpressTypeOptions.value = orderRes.option_list;
      if (countRes) {
        countRes.forEach(item => {
          const matchingOptionIndex = sfExpressTypeOptions.value.findIndex(
            option => option.label === item.express_type_name,
          );
          if (matchingOptionIndex !== -1) {
            sfExpressTypeOptions.value[matchingOptionIndex].tips = item.discount_value_label;
          }
        });
      }
      sfExpressTypeOptions.value.forEach(item => {
        item.desc = descObj[item.value];
      });

      // ============================= 获取闪送上门取件类型 =================================
      // 获取顺丰同城
      const sfSameCity = sfExpressTypeOptions.value.find(item => item.value === 263);
      const apiGetShanSongList = isSuper ? apiAdminGetShanSongList : apiMerchantGetShanSongList;
      const apiGetShanSongListParams = isSuper
        ? { server_id: props.serverId, open: 1, page_size: 9999 }
        : { page_size: 9999 };
      const { data: shanSongData } = await apiGetShanSongList(apiGetShanSongListParams);
      shanSongDataList.value = shanSongData;

      // 默认选项
      const defaultOption = { label: '闪送', value: 'shanSong' };

      shanSongOption.value = [];
      shanSongData.forEach(shanSongDataItem => {
        const isShanSong =
          shanSongDataItem.service_certification_list.includes(1) &&
          shanSongDataItem.service_certification_open_list.includes(1);
        if (isShanSong) {
          shanSongOption.value.push({
            label: shanSongDataItem.store_name,
            value: shanSongDataItem.id,
          });
        }
      });

      // 判断是否有已认证已开通的闪送门店
      if (shanSongOption.value.length) {
        ssExpressTypeOptions.value = [defaultOption, sfSameCity];
      } else {
        ssExpressTypeOptions.value = [sfSameCity];
      }
      // ============================= 初始化表单信息 =================================
      state.pickUpFormState = {
        phone: props.extraData.go_ship_phone,
        express_id: 'SF',
        sf_express_type: 2,
        send_start_time: '',
        is_sign: 0,
        is_insure: 0,
        is_packed: 0,
        is_sign_paper: false,
        ss_express_type: ssExpressTypeOptions.value[0]?.value,
      };
      setDefaultAddressId();
      state.pickUpFormState.imei = deviceLists.map(item => item.value).join(' ');
      state.pickUpFormState.sf_express_type = Number(orderRes.select);
    })
    .catch(e => {
      console.error(e);
    })
    .finally(() => {
      pickUpLoading.value = false;
    });
}

function setDefaultAddressId() {
  if (!addressOptions.value.length) {
    return;
  }
  let addressId = undefined;
  if (addressOptions.value.length === 1) {
    addressId = addressOptions.value[0].id;
  } else {
    addressId = addressOptions.value.find(item => item.is_default)?.value ?? '';
  }
  state.pickUpFormState.address_id = addressId;
  refreshTimeOptions();
}

const sendExpressOptions = [
  { value: 'SF', label: '顺丰快递' },
  { value: 'JD', label: '京东快递' },
  { value: 'SS', label: '同城闪送' },
];

const addressOptions = computed(() => {
  return props.extraData.server_address_list?.map((item: any) => ({
    value: item.id,
    label: item.province + ' ' + item.city + ' ' + item.address,
    is_default: item.isDefault,
  }));
});

const sfExpressTypeOptions = ref<any[]>([]);
const ssExpressTypeOptions = ref<any[]>([]);

const { isSuccessGet, timeOptions, getTimeOptions } = useTimeOptions();

// 获取地址实名制提醒
const { remindText, showRemind } = useAddressRealRemind(
  () => props.orderId,
  isSuper ? RequestTypeEnum.OPERATION : RequestTypeEnum.SHOP,
);

/** 刷新可选时间 */
function refreshTimeOptions() {
  state.pickUpFormState.send_start_time = [];
  getTimeOptions(
    state.pickUpFormState.address_id,
    state.pickUpFormState.express_id,
    props.extraData.server_address_list || [],
    props.orderId,
    state.pickUpFormState.ss_express_type,
  );
}

function toServicePng() {
  window.open('https://img1.rrzuji.cn/uploads/scheme/2311/10/m/PdIrw4mrvEMf7cDNKMPf.png');
}

function handleOpen() {
  window.open('https://img1.rrzuji.cn/uploads/scheme/2311/10/m/0xbBw2Bav72NiGMy5RDK.png');
}

const openSendTypeTip = () => {
  window.open('https://img1.rrzuji.cn/uploads/scheme/2503/21/m/H7TarFsvPNefZJj928Js.png');
};

const skuOptions = ref([]);
const getSkuOption = async () => {
  const { data } = await getDictTree(props.orderId);
  skuOptions.value = data;
};

const device_codes = ref('');
const handleChangeCode = (code: any) => {
  device_codes.value = code;
};

const pageOpti = ref(false);
const pageOptiLoading = ref(false);

const initPageOpti = async () => {
  try {
    pageOptiLoading.value = true;
    if (!isSuper) {
      const { data } = await getPageOptiFlag({ order_id: props.orderId });
      pageOpti.value = data.is_code_merch_self_order;
    } else {
      pageOpti.value = false;
    }
  } finally {
    pageOptiLoading.value = false;
  }
};

const validateMsLoanLimitImei = (_, value) => {
  if (!/^(86|35)\d{13}$/.test(value)) return Promise.reject('请输入设备识别码（15位数，安卓86开头，苹果35开头）');
  return Promise.resolve();
};

defineExpose({
  validate: async () => {
    try {
      const validationPromises = [];

      if (deviceFormRef.value && props.data.is_pengquan_order) {
        validationPromises.push(deviceFormRef.value.validate());
      }

      if (formRef.value) {
        validationPromises.push(formRef.value.validate());
      }

      // 并发执行校验
      await Promise.all(validationPromises);
    } catch (error) {
      // 捕获校验错误
      console.error('校验失败:', error);
      throw error;
    }

    (window as any).sensors.track({
      key: '$OrderListClick',
      customProperties: {
        $elementType: 'OrderListShipment',
        $elementContent: `${tabMaps[state.shipmentType]}提交`,
      },
    });

    // 取件发货服务
    if (state.shipmentType === 'pickUpAndShip') {
      return { ...cloneDeep(state.pickUpFormState), device_codes: device_codes.value };
    }

    const { offline_contract_show } = props.extraData;
    const {
      shipper_code,
      logistic_code = '',
      image,
      ms_loan_limit_imei,
      ms_loan_limit_image,
      is_support_offline_contract,
      contract_file_1,
      contract_title_2,
      contract_file_2,
    } = cloneDeep(state.form);

    const params: any = {
      order_id: props.orderId,
      orderId: props.orderId,
      ordinary_delivery: 1,
      logistic_code,
      shipper_code,
      image: toRaw(image) || [],
      ms_loan_limit_imei,
      ms_loan_limit_image: ms_loan_limit_image.map((i: any) => i.response).join(','),
      device_codes: device_codes.value,
    };

    if (offline_contract_show) {
      params.is_support_offline_contract = is_support_offline_contract;
      const offline_contract_file = [
        {
          title: '线下租赁协议',
          file_url: contract_file_1.map((i: any) => i.response),
        },
      ];
      if (supportContract.value) {
        contract_file_2.length &&
          offline_contract_file.push({
            title: contract_title_2,
            file_url: contract_file_2.map((i: any) => i.response),
          });
        params.offline_contract_file = offline_contract_file;
      }
    }

    return params;
  },
  isWithdraw: isWithhold,
});

// 时间变化时的校验
// function handleTimeChange(value: any) {
//   if (!value) return;

//   const selectedTime = dayjs(value);
//   const currentTime = dayjs();

//   // 校验时间是否在有效范围内
//   if (selectedTime.isBefore(currentTime.add(1, 'hour'))) {
//     message.error('请选择一个小时以后的时间');
//     state.pickUpFormState.ss_send_start_time = null; // 清空无效选择
//   } else if (selectedTime.isAfter(currentTime.add(2, 'day'))) {
//     message.error('请选择两天以内的时间');
//     state.pickUpFormState.ss_send_start_time = null; // 清空无效选择
//   }
// }

onMounted(() => {
  if (isPengquanOrder.value) {
    //发请求获取sku
    getSkuOption();
  }
  initPageOpti();
});
</script>

<style lang="less" scoped>
.clear-tab {
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
}

.img-tips {
  width: 420px;
  color: rgba(6, 21, 51, 0.65);
}

.radio-text {
  position: relative;
}

.radio-label-text {
  margin-top: 4px;
  color: rgba(6, 21, 51, 0.45);
  font-weight: 400;
  font-size: 12px;
}

.tips-text {
  position: absolute;
  top: -13px;
  right: -10px;
  margin: 0;
  padding: 0 4px;
  color: #fff;
  font-size: 10px;
  line-height: normal;
  background: #ff4d4f;
  border-color: transparent;
  border-radius: 2px;
}

.ss-time-tip {
  color: #faad14;
  font-weight: 400;
  font-size: 13px;
}
</style>
