<template>
  <a-spin :spinning="spinning">
    <div class="information-bank">
      <div class="top">
        <TopCard
          :rate-info="rateInfo"
          :sales-info="salesInfo"
          @on-change-time="onChangeTime"
        />
      </div>
      <div class="section">
        <SectionSellCard
          :base-info="baseInfo"
          :goods-info="goodsInfo"
          :member-info="memberInfo"
          :num-info="numInfo"
          :server-city="serverCity"
        />
      </div>
      <div class="bottom">
        <BottomCard
          :category-list="baseInfo.category_list"
          :goods-info="goodsInfo"
          :select-category="baseInfo.select_category"
        />
      </div>
    </div>
  </a-spin>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { BottomCard, SectionSellCard, TopCard } from './components';
import { getSiteInfo } from './service';
import { BaseInfoInterface, DataModal, IGoodsNumInfo } from './types';

const spinning = ref<boolean>(true);
// const isRole = window.location.href.indexOf('/super/') > -1 ? 'super' : 'user';
// https://g2.antv.vision/zh/examples/pie/donut#clock
const baseInfo = ref<Partial<BaseInfoInterface>>({});
const rateInfo = ref<DataModal>({});
const salesInfo = ref<DataModal>({});
const numInfo = ref<DataModal>({});
const memberInfo = ref<DataModal>({});
const serverCity = ref<DataModal>({});
const goodsInfo = ref<Partial<IGoodsNumInfo>>({});

const queryParams = reactive<{
  edit: boolean;
  api: number;
  day: string | undefined;
}>({
  edit: true,
  api: 0,
  day: undefined,
});

const onChangeTime = async (time: string) => {
  queryParams.day = time[0] + '~' + time[1];
  await fetchSideInfo();
};

const fetchSideInfo = async () => {
  spinning.value = true;
  const res = await getSiteInfo(queryParams);
  const data = res.message.data;
  baseInfo.value = data.baseInfo;
  rateInfo.value = data.RateInfo;
  salesInfo.value = data.salesInfo;
  numInfo.value = data.numInfo;
  memberInfo.value = data.memberInfo;
  serverCity.value = data.serverCity;
  goodsInfo.value = data.goodsInfo;
  spinning.value = false;
};

onMounted(() => {
  fetchSideInfo();
});
</script>
<style scoped lang="less">
.information-bank {
  width: 100%;
  min-height: calc(100vh - 64px);
  margin-top: 8px;
  background-color: #fff;
  .bottom,
  .bottom-chart,
  .top {
    width: 100%;
  }
  .bottom-chart {
    margin-top: 24px;
  }
  .section {
    display: flex;
    width: 100%;
    margin: 24px 0;
  }
}
</style>
