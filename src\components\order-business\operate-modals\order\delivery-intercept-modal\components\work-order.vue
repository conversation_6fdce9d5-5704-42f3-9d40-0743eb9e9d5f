<template>
  <LargeTicketTemplate
    ref="largeTicketTemplateRef"
    :order-id="orderId"
    :template-data="templateData"
    @settemplate-data="settemplateData"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

import { TFieldItem, TTemplate } from '@/pages-stage/user-ticket-v2/components/large-ticket-template/data.d';
import LargeTicketTemplate from '@/pages-stage/user-ticket-v2/components/large-ticket-template/index.vue';
import { deepClone } from '@/utils/base';

import { getInterceptTemplateApi } from '../services';
const props = defineProps({
  orderId: {
    type: String,
    default: '',
  },
  currentKey: {
    type: Number,
    default: 183,
  },
});

const largeTicketTemplateRef = ref();
const tempCate = ref({
  new_type_id: '',
  template_id: '',
});
const templateData = ref<TTemplate>({
  common_data: [],
  operate_data: [],
  link_data: [],
});
//公共区赋值
function settemplateData(info: TFieldItem[]) {
  templateData.value.common_data = info;
}
function commit() {
  return new Promise(async (resolve, reject) => {
    await largeTicketTemplateRef.value?.setData().catch(() => {
      reject();
    });
    resolve({
      ...tempCate.value,
      order_id: props.orderId,
      json_data: JSON.stringify(templateData.value),
    });
  });
}

async function getTempalte() {
  const { data } = await getInterceptTemplateApi({ order_id: props.orderId, key: props.currentKey });
  tempCate.value = {
    new_type_id: data.new_type_id,
    template_id: data.id,
  };
  templateData.value = deepClone(data.json_data);
}

watch(
  () => props.orderId,
  val => {
    if (!val) return;
    getTempalte();
  },
  {
    immediate: true,
  },
);

defineExpose({
  commit,
});
</script>
