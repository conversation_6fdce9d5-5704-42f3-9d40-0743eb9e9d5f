<template>
  <section>
    <div
      class="form-box"
      :style="{
        height: sumHeight + 'px',
      }"
    >
      <div ref="formRef">
        <form-create
          ref="searchFormRef"
          v-model:value="bindValue"
          class="search-form"
          :form-group="searchFormGroup"
          :origin-props="{
            layout: 'inline',
          }"
        />
      </div>
    </div>

    <div class="btns">
      <a-space :size="8">
        <a-button
          style="margin-right: 10px"
          type="primary"
          @click="queryTableList"
        >
          查询
        </a-button>
        <a-button @click="resetSearch">
          重置
        </a-button>
        <span
          class="more-filtering"
          :class="isMore ? 'active' : 'default'"
          @click="moreChange"
        >
          <span>{{ isMore ? '收起筛选' : '更多筛选' }}</span>
          <img
            class="icon"
            src="https://img1.rrzuji.cn/uploads/scheme/2301/09/o/AhlqYzpmw9nw41GSTW2X.png"
          >
        </span>
      </a-space>
    </div>
  </section>
</template>

<script setup lang="ts">
import { PropType, computed, onMounted, ref } from 'vue';
import { FormGroupItem } from '@/components/form-create/src/typing';
import { FormInstance } from 'ant-design-vue';

const props = defineProps({
  searchFormGroup: {
    type: Array as PropType<FormGroupItem[]>,
    default: () => [],
  },
  value: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['update:value', 'fetch-table', 'reset-table']);

//搜索组件实例
const searchFormRef = ref();

// 搜索参数
const bindValue = computed({
  get: () => {
    return props.value;
  },
  set: val => {
    emits('update:value', val);
  },
});

// 搜索外部容器
const formRef = ref<HTMLElement>();

// 收起/展开
const sumHeight = ref(88);
const isMore = ref(false);

// 更多筛选
const moreChange = () => {
  if (!formRef.value) return;
  isMore.value = !isMore.value;
  sumHeight.value = isMore.value ? formRef.value.clientHeight : 88;
};

// 查询
const queryTableList = () => {
  emits('fetch-table', bindValue);
};

// 重置
const resetSearch = () => {
  (searchFormRef.value?.getFormRef() as FormInstance)?.resetFields();
  emits('reset-table');
};

onMounted(() => {
  window.addEventListener('resize', () => {
    isMore.value = false;
    sumHeight.value = 88;
  });
});
</script>

<style lang="less" scoped>
@import url('../../../../common/base.less');
.form-box {
  margin-bottom: 24px;
  overflow: hidden;
  transition: height 0.3s ease-in-out;

  .search-form {
    display: grid;
    flex: 1;
    grid-gap: 24px;
    grid-template-columns: repeat(1, 1fr); // 修改这里
    margin-bottom: 16px;
  }
}

.btns {
  .more-filtering {
    margin-left: 16px;
    color: #3777ff;
    cursor: pointer;
    user-select: none;
    .icon {
      width: 16px;
      height: 16px;
      margin-left: 4px;
      transition-duration: 0.3s;
    }
  }
  .more-filtering.default {
    .icon {
      transform: translateY(2px);
    }
  }
  .more-filtering.active {
    .icon {
      transform: rotateZ(180deg) translateY(0);
    }
  }
}
</style>
