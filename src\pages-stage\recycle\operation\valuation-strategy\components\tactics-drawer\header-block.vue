<template>
  <div class="header-block">
    <span
      v-if="title"
      class="header-block__title"
    >{{ title }}</span>
    <div class="flex-con">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.header-block {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 12px;
  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 4px;
    height: 16px;
    background: #00c8be;
    border-radius: 2px;
    transform: translateY(-50%);
    content: '';
  }
  &__title {
    margin-right: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
