<template>
  <!-- 支持发起公证 -->
  <a-tag
    v-if="showStatus"
    class="aea_txt_tag"
    size="small"
    :style="
      clickStatus.includes(aeaStatus) && {
        cursor: 'pointer',
      }
    "
  >
    <span @click="goNotarialRecord">
      {{ EStatusText[aeaStatus] }}
    </span>
  </a-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { confirmNotarizeNotice } from '@/components/order-business/operate-modals/order/launch-notarization-modal/config.tsx';

import { EStatus } from './data.d';

const props = defineProps({
  orderId: {
    type: String,
    default: '',
  },
  aeaStatus: {
    type: Number,
    default: 0,
  },
  notarizationType: {
    type: Number,
    default: 0,
  },
});

const EStatusText: { [key in EStatus]: string } = {
  [EStatus.WAIT_SIGH]: '可存证，待用户签署',
  [EStatus.WAIT_SUBMIT]: '用户完成签署，待提交存证',
  [EStatus.WAIT_NOTARIZATION]: '已存证，待发起公证',
  [EStatus.WAIT_VIDEO_AUDIT]: '用户完成签署，视频审核中',
  [EStatus.WAIT_SUBMIT_VIDEO]: '视频审核通过，待提交存证',
  [EStatus.WAIT_RESIGN]: '视频审核不通过，待用户重新签署',
  [EStatus.WAIT_CONFIRM]: '视频审核通过，待确认',
  [EStatus.Rejected]: '视频审核不通过，待重新发起公证',
  [EStatus.WAIT_START_NOTARIZE]: '视频审核不通过，待重新发起公证',
};

const clickStatus = [EStatus.WAIT_SUBMIT, EStatus.WAIT_SUBMIT_VIDEO, EStatus.WAIT_RESIGN, EStatus.WAIT_CONFIRM];

const route = useRoute();
const router = useRouter();

const showStatus = computed(() => {
  // 长租的（可存证，待用户签署）状态不显示
  const hideStatus = props.notarizationType === 1 && props.aeaStatus === EStatus.WAIT_SIGH;
  return hideStatus ? false : Object.values(EStatus).includes(props.aeaStatus);
});

/** 跳转商家后台/营销/我的应用/公证记录 */
async function goNotarialRecord() {
  if (!clickStatus.includes(props.aeaStatus)) return;
  const is_read = await confirmNotarizeNotice();
  if (!is_read) return;
  router.push(`/application-detail/super?id=6&activeKey=2&order_id=${props.orderId}&origin=${route.query.origin}`);
}
</script>

<style scoped lang="less">
.aea_txt_tag {
  margin-top: 4px;
  color: #0550e3;
  background-color: #fff;
  border: 1px solid #0550e3;
}
</style>
