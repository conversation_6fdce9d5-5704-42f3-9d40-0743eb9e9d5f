export interface IOptionItem {
  type: string | number;
  id: string | number;
  name: string;
  title_data: {
    title: string;
    desc: string;
    images: string[];
    [key: string]: any;
  };
  option_data: Record<string, any>[];
  is_open: number | string;
}
export interface ITreeItem {
  name: string;
  id?: string | number;
  key: string;
  type: string | number;
  children?: ITreeItem[];
  isLeaf?: boolean;
}
