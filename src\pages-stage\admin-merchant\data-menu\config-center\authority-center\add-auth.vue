<template>
  <div class="add-auth">
    <a-button
      v-if="value"
      class="save-btn"
      size="middle"
      type="primary"
      @click="addAccount"
    >
      <template #icon>
        <plus-outlined />
      </template>
      添加账号
    </a-button>
    <!-- 添加账号 -->
    <add-account-modal
      v-model:data="addAccountInfo"
      v-model:value="isShowAddAccount"
      v-bind="$attrs"
      @reset-list="resetList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { AuthorityCenterRow } from './data';
import AddAccountModal from './add-account-modal.vue';
import { PlusOutlined } from '@ant-design/icons-vue';

defineProps({
  value: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['resetList', 'load-config']);
const isShowAddAccount = ref(false);
const addAccountInfo = ref<AuthorityCenterRow>({
  phone: '',
  name: '',
  account_id: 0,
  auth_export: 0,
  auth_search: 1,
  created_at: '',
  created_at_text: '',
  id: 0,
  server_id: 0,
});

// 添加账号
function addAccount() {
  emit('load-config', 0);
  isShowAddAccount.value = true;
}

function resetList() {
  emit('resetList');
}
</script>
