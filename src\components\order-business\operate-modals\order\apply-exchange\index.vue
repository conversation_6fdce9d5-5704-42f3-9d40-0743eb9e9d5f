<template>
  <a-modal
    :ok-button-props="{
      disabled: confirmLoading,
    }"
    title="售后申请单"
    :visible="visible"
    width="540px"
    @cancel="handleCancel"
    @ok="onSubmit"
  >
    <a-form
      ref="refApplyFrom"
      autocomplete="off"
      :label-col="{ span: 10 }"
      layout="vertical"
      :model="formObj"
    >
      <a-form-item
        label="售后类型"
        name="type"
        :rules="[{ required: true, message: '请选择售后类型' }]"
      >
        <a-radio-group v-model:value="formObj.type">
          <a-radio value="1">
            退货
          </a-radio>
          <a-radio value="2">
            换货
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="formObj.type === '1'"
        label="是否拦截拒收"
        name="is_intercept_logistic"
        :rules="[{ required: true, message: '请选择是否拦截拒收' }]"
      >
        <a-radio-group v-model:value="formObj.is_intercept_logistic">
          <a-radio :value="1">
            是
          </a-radio>
          <a-radio :value="2">
            否
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="售后原因"
        name="reason_option"
        :rules="[{ required: true, message: '请选择售后原因' }]"
      >
        <a-select
          v-model:value="formObj.reason_option"
          :options="salesReasonOption"
          placeholder="请选择售后原因"
        />
      </a-form-item>
      <a-form-item
        label="售后原因说明"
        name="reason"
        :rules="[{ required: true, message: '请输入售后原因说明' }]"
      >
        <a-textarea
          v-model:value="formObj.reason"
          allow-clear
          :maxlength="200"
          placeholder="请输入售后原因"
          :rows="4"
          show-count
        />
      </a-form-item>
      <a-form-item name="video">
        <template #label>
          上传视频 <span class="extra-text">不能超过20M</span>
        </template>
        <a-upload
          v-model:file-list="formObj.video"
          accept=".mpeg, .avi, .mp4, .wmv, .flv, .mov, .mkv, .rmvb, .3gp"
          :custom-request="customRequest"
          :max-count="1"
        >
          <a-button :loading="confirmLoading">
            <UploadOutlined />
            上传视频文件
          </a-button>
          <div
            class="extra-text"
            style="margin-top: 10px"
          >
            支持扩展名：avi、mpeg、mp4、wmv、flv、mov、mkv、rmvb、3gp
          </div>
          <template #itemRender="{ file }">
            <div
              v-if="file.response"
              class="custom-file-link"
            >
              <PaperClipOutlined />
              <a
                :href="file.response"
                style="margin-right: 10px; font-size: 14px"
                target="_blank"
              >视频文件 </a>
              <DeleteOutlined @click="handleDelete" />
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <a-form-item name="img_url">
        <template #label>
          上传照片 <span class="extra-text">不能超过5M</span>
        </template>
        <image-upload
          v-model:value="formObj.img_url"
          :max="6"
          quality="o"
          style="margin-top: 24px"
          upload-text="上传图片"
        />
      </a-form-item>
      <a-form-item
        label="售后运费"
        name="is_free_shipping"
        required
      >
        <a-radio-group v-model:value="formObj.is_free_shipping">
          <a-radio :value="1">
            平台包邮
          </a-radio>
          <a-radio :value="2">
            用户自付
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="寄回仓库"
        name="warehouse_id"
        :rules="[{ required: true, message: '请选择寄回仓库' }]"
      >
        <a-select
          v-model:value="formObj.warehouse_id"
          :options="warehouseOption"
          placeholder="请选择寄回仓库"
          @change="handleReturnWarehouse"
        />
      </a-form-item>
      <a-form-item
        label="寄回地址"
        name="warehouse_addr_id"
        :rules="[{ required: true, message: '请选择寄回地址' }]"
      >
        <a-select
          v-model:value="formObj.warehouse_addr_id"
          :field-names="{ label: 'addr_name', value: 'id' }"
          :options="addressOption"
          placeholder="请选择寄回地址"
          @change="handleReturnAddress"
        />
        <div v-if="setAddressVisible">
          <span>尚未设置地址信息</span>
          <span
            class="set-infer"
            @click="handleSetAddress"
          >去设置</span>
        </div>
        <a-descriptions
          v-if="formObj.warehouse_addr_id"
          class="address_info"
          :column="1"
        >
          <a-descriptions-item
            label="收货人"
            :label-style="{ paddingLeft: '1em' }"
          >
            {{ addressInfo.name }}
          </a-descriptions-item>
          <a-descriptions-item label="手机号码">
            {{ addressInfo.phone }}
          </a-descriptions-item>
          <a-descriptions-item label="收货地址">
            {{ addressInfo.address }}
          </a-descriptions-item>
        </a-descriptions>
      </a-form-item>
      <a-form-item
        label="备注"
        name="remark"
      >
        <a-textarea
          v-model:value="formObj.remark"
          :maxlength="200"
          placeholder="请输入备注"
          :rows="4"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { computed, type PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { DeleteOutlined, PaperClipOutlined, UploadOutlined } from '@ant-design/icons-vue';

import ImageUpload from '@/components/image-upload/image-upload.vue';
import useCustomRequest from '@/components/order-business/composables/use-custom-request';
import { POST } from '@/services/api';
import { getOssImageParams } from '@/utils/oss-helper';

import { getAddressApi, getAfterSaleReasonOptions, getWarehouseList } from './services';

interface IExtraData {
  delivery_mark_id?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
});

const route = useRoute();

const emits = defineEmits(['update:visible', 'refresh']);
const refApplyFrom = ref();

const formObj = ref<{
  type: string;
  reason: string;
  logistic_num: string;
  img_url: string[];
  remark: string;
  logistic_code: string | undefined;
  video: any;
  is_intercept_logistic: number;
  warehouse_id: string | undefined;
  warehouse_addr_id: string | undefined;
  is_free_shipping: 1 | 2;
  reason_option?: string;
}>({
  type: '',
  reason: '',
  logistic_num: '',
  img_url: [],
  remark: '',
  logistic_code: undefined,
  video: [],
  is_intercept_logistic: 1,
  warehouse_id: undefined,
  warehouse_addr_id: undefined,
  is_free_shipping: 1,
  reason_option: undefined,
});
const salesReasonOption = ref([
  {
    value: 1,
    label: '不想要',
  },
  {
    value: 2,
    label: '设备外观存在缺陷',
  },
  {
    value: 3,
    label: '设备功能缺失/异常',
  },

  {
    value: 4,
    label: '不想要监管机',
  },
  {
    value: 5,
    label: '电池健康太低',
  },
  {
    value: 6,
    label: '平台服务问题',
  },
  {
    value: 7,
    label: '少发/漏发配件',
  },
  {
    value: 8,
    label: '货不对板',
  },
  {
    value: 9,
    label: '其他',
  },
]);

const deliveryMarkId = computed(() => {
  return props.extraData.delivery_mark_id;
});

const onSubmit = async () => {
  refApplyFrom.value?.validateFields().then(async () => {
    let imgArr, videoArr;
    if (formObj.value.img_url.length) {
      let str = new RegExp('https://img-t1.rrzuji.cn');
      try {
        const { imgOssServer } = await getOssImageParams();
        str = new RegExp(imgOssServer);
      } catch {
        console.log('获取oss域名失败');
      }
      imgArr = formObj.value.img_url.map(v => {
        return v.replace(str, '');
      });
    }
    if (formObj.value.video.length) {
      const reg = /https:\/\/img-t1\.rrzuji\.cn|https:\/\/img1\.rrzuji\.cn/;
      videoArr = formObj.value.video.map((i: any) => i.response.replace(reg, ''));
    }
    if (formObj.value.type === '2') {
      delete formObj.value.is_intercept_logistic;
    }

    POST('/super/warehouse/after-sale-work/create', {
      ...formObj.value,
      img: imgArr,
      video: videoArr,
      delivery_mark_id: deliveryMarkId.value,
    }).then(() => {
      message.success('售后申请成功');
      emits('refresh', props.orderId);
      handleCancel();
    });
  });
};

const { customRequest, confirmLoading } = useCustomRequest(formObj.value.video, 20);
const handleDelete = () => {
  formObj.value.video = [];
};

const warehouseOption = ref([]);
const addressOption = ref([]);
const setAddressVisible = ref(false); //是否显示去仓库列表设置地址
const addressInfo = ref({
  name: '',
  phone: '',
  address: '',
});

const domain = route.query.origin || window.location.origin;
const handleSetAddress = () => {
  //找仓库value对应的label
  const warehouseObj = warehouseOption.value.find((item: any) => item.value === formObj.value.warehouse_id);
  window.open(domain + `/super/warehouse/warehouse/warehouse-index?warehouse_name=${warehouseObj?.label}`);
};

//改变寄回仓库
const handleReturnWarehouse = async () => {
  formObj.value.warehouse_addr_id = undefined;
  const res = await getAddressApi(formObj.value.warehouse_id);
  addressOption.value = res.data || [];
  setAddressVisible.value = res.data.length === 0;
};

const handleReturnAddress = (_: any, option: any) => {
  addressInfo.value = {
    name: option.name,
    phone: option.phone,
    address: option.address,
  };
};

const getWarehouse = async () => {
  const { data } = await getWarehouseList();
  warehouseOption.value = data.warehouse_id;
};

const handleCancel = () => {
  refApplyFrom.value?.resetFields();
  setAddressVisible.value = false;
  addressOption.value = [];
  emits('update:visible', false);
};
const handleAfterSaleReasonOptions = async () => {
  const res = await getAfterSaleReasonOptions();
  salesReasonOption.value = res.data;
};
watch(
  () => props.visible,
  val => {
    if (val) {
      //发起寄回地址的请求
      getWarehouse();
      handleAfterSaleReasonOptions();
    }
  },
  {
    immediate: true,
  },
);
</script>
<style lang="less" scoped>
.extra-text {
  margin-left: 5px;
  color: rgba(6, 21, 51, 0.45);
  font-size: 12px;
}

:deep(.img_upload_wrap) {
  margin-top: 0 !important;
}

.address_info {
  width: 400px;
  height: 122px;
  margin-top: 20px;
  padding: 12px;
  background: #fafafa;
}

.set-infer {
  padding-left: 5px;
  color: #4876f6;
  cursor: pointer;
}
</style>
