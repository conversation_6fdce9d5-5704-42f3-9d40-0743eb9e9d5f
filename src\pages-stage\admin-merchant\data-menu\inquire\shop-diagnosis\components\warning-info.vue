<!-- 复制迁移首页商家预警 -->
<template>
  <div class="warning-info">
    <div class="warning-info-header">
      <div class="warning-info-header-left">
        <div class="warning-info-title">
          {{ item.title }}
        </div>
        <div class="warning-info-sub">
          <span class="font-black-65">{{ convertExplicitValueAsText(item.last_value, item) }}</span>
          <span class="red-color"> ({{ compareAsText(item.value, item.conf_value) }})</span>
          <span
            v-if="!item.special_tag"
            class="font-black-65"
            style="margin-left: 8px"
          >平台考核值: <span class="red-color">{{ convertExplicitValueAsText(item.conf_value, item) }}</span></span>
          <a-tag
            v-if="item.special_tag"
            class="red-tag"
          >
            {{ specialTagMap[item.special_tag] }}
          </a-tag>
        </div>
      </div>
      <div class="warning-info-header-right">
        <a-button
          class="warn-btn"
          :data-sensors_indicator_val="indicatorVal"
          type="link"
          @click="goDetial(item.hit_id)"
        >
          详情
        </a-button>
        <a-divider type="vertical" />
        <a-button
          class="warn-btn"
          :data-sensors_indicator_val="indicatorVal"
          type="link"
          @click="goHandle(item)"
        >
          去处理
        </a-button>
      </div>
    </div>

    <div class="warning-info-tags">
      <a-tag class="tag">
        预警日期: {{ smartDateRangeAsText(item, 'MM.DD', '-') }}
      </a-tag>
      <a-tag class="tag">
        更新时间: {{ item.warning_time_str }}
      </a-tag>
    </div>

    <div class="warning-info-footer">
      <div class="left">
        <template v-if="!item.special_tag">
          <a-button
            v-for="orderItem in item.order_ids.slice(0, 5)"
            :key="orderItem"
            class="left-link"
            type="link"
            @click="handleOrderIDClick(orderItem)"
          >
            {{ orderItem }}
          </a-button>

          <a-button
            v-if="item.order_ids?.length > 5"
            class="left-link"
            type="link"
            @click="moreOpen(item.order_ids)"
          >
            更多
          </a-button>
        </template>
        <div
          v-else
          class="suggest"
        >
          <a-tooltip placement="topRight">
            <template #title>
              预警建议：{{ item.warning_suggest }}
            </template>
            预警建议：{{ item.warning_suggest }}
          </a-tooltip>
        </div>
      </div>
      <div
        v-if="item.course_id"
        class="right font-black-45"
      >
        不知道怎么处理？
        <a-button
          :data-sensors_indicator_val="indicatorVal"
          @click="goLearn(item.course_id, item.rule_id)"
        >
          立即学习
        </a-button>
      </div>
    </div>

    <a-modal
      v-model:visible="moreVisible"
      :footer="null"
      title="查看所有订单"
    >
      <template
        v-for="orderItem in currentOrderIDs"
        :key="orderItem"
      >
        <a-button
          class="left-link"
          :data-sensors_indicator_val="indicatorVal"
          type="link"
          @click="handleOrderIDClick(orderItem)"
        >
          {{ orderItem }}
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { h, PropType, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useModal } from 'rrz-web-design';

import { useWarningUtils } from '@/pages-stage/admin-merchant/home/<USER>/components/merchant-warning-v2/composables/use-warning-utils';

import { EBasicIndicator, specialTagMap } from '../config';
import { IWarningItem, TItem } from '../data';

const props = defineProps({
  item: {
    type: Object as PropType<IWarningItem>,
    required: true,
  },
  tabActiveKey: {
    type: String,
    required: true,
  },
  rulePath: {
    type: String,
    required: true,
  },
  indicatorVal: {
    type: Number,
    required: true,
  },
});

const router = useRouter();
const route = useRoute();

const { openPage, smartDateRangeAsText, compareAsText, convertExplicitValueAsText, track } = useWarningUtils(
  router,
  route,
);

const currentOrderIDs = ref<number[]>([]);
/* 预警项订单号显示更多弹窗 */
const { visible: moreVisible, open: moreOpen } = useModal(undefined, {
  beforeOpen: (ids: number[]) => {
    currentOrderIDs.value = ids;
  },
});

function goHandle(record: TItem) {
  track('去处理');
  const { basic_indicator, special_tag } = record;
  // 平均预收多期弹起平均预收多期处理弹窗
  if (basic_indicator === EBasicIndicator.AvgPrepaymentPeriodNum) {
    Modal.confirm({
      maskClosable: true,
      title: '预警提醒',
      icon: h(ExclamationCircleOutlined),
      content: `您的平均预收期数为：${record.last_value || record.value}，平台标准值：${
        record.conf_value
      }， 请及时去订单列表相关预警订单上传证据；（上传规则详见下方规则；若已上传，请忽略该项预警。）`,
      okText: '去处理',
      cancelText: '查看规则',
      onOk() {
        openPage('OrderListV3');
        return Promise.resolve();
      },
      onCancel(e) {
        if (e && e.triggerCancel) return;
        toRule();
        return Promise.resolve();
      },
    });
    return;
  }
  // 预存款根据不同类型跳转
  if (special_tag) {
    const open_recharge = special_tag === 1 ? 'platform' : special_tag === 2 ? 'thirdParty' : undefined;
    openPage('PurchaseManagement', { open_recharge });
    return;
  }
  // 其他情况一律打开发货工作台
  openPage('DeliveryWorkbench');
}

/**
 * 详情埋点
 */
function goDetial(id: number) {
  track('详情');
  openPage('WarningDetail', {
    id,
    period_num_rule_path: props.rulePath,
  });
}

/**
 * 去学习埋点
 */
function goLearn(courseID: number, ruleID: number) {
  track('去学习', ruleID);
  openPage('BusinessUniversityCourseDetail', { courseID, ruleID });
}

/**
 * 订单号点击埋点
 */
function handleOrderIDClick(id: number) {
  track('订单号点击', id);
  openPage('OrderListV3', { id });
}

function toRule() {
  window.open(props.rulePath, '_blank');
}
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

.font-black-45 {
  color: @font-black-45;
}

.font-black-65 {
  color: @font-black-65;
}

.red-color {
  color: #ff4d4f;
}

.warning-info {
  padding: 16px;
  border: 1px solid rgba(6, 21, 51, 0.15);
  border-radius: 8px;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-left {
      display: flex;
      align-items: flex-end;

      .warning-info-title {
        position: relative;
        padding-left: 8px;
        font-weight: bold;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 4px;
          height: 14px;
          background: #ff4d4f;
          border-radius: 2px;
          transform: translate(0, -50%);
          content: '';
        }
      }

      .warning-info-sub {
        margin-left: 8px;
        font-size: 12px;

        .red-tag {
          margin-left: 8px;
          color: #f5222d;
          background-color: #fff1f0;
          border: 1px solid #ffa39e;
        }
      }
    }

    &-right {
      .warn-btn {
        padding-inline: 0;
      }
    }
  }

  &-tags {
    margin: 8px 0 22px;

    .tag {
      border-color: transparent;
    }
  }

  &-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;

    .left {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
    }

    .suggest {
      overflow: hidden;
      color: #061533a6;
      font-size: 14px;
      text-overflow: ellipsis;
    }

    .left-link {
      margin-right: 8px;
      font-size: 12px;
      padding-inline: 0;

      :deep(span) {
        text-decoration: underline;
      }
    }
  }
}
</style>
