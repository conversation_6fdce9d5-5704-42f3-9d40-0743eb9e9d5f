<template>
  <div class="order-remark">
    <crm-remark
      :id="`${orderId}_${type}`"
      :block-flex="false"
      :item="orderRemarks[`${orderId}_${type}`]"
      layout="horizontal"
      log-link="/crm/log"
      :prefix-text="prefixText"
      push-link="/crm/push"
      size="block"
      @add-success="success"
    >
      <template
        v-if="slot.emptyText"
        #emptyText
      >
        <slot name="emptyText" />
      </template>
    </crm-remark>
  </div>
</template>

<script setup lang="ts">
import { PropType, useSlots } from 'vue';
import dayjs from 'dayjs';
import { RemarkType } from '../../data';

const slot = useSlots();

const props = defineProps({
  orderId: {
    type: String,
    required: true,
  },
  type: {
    type: String as PropType<'pay.recycle'>,
    required: true,
  },
  prefixText: {
    type: String,
    default: '',
  },
  orderRemarks: {
    type: Object as PropType<Record<string, RemarkType>>,
    default: () => ({}),
  },
});

const success = ({ text }: { text: string }) => {
  const crmItem = props.orderRemarks[`${props.orderId}_${props.type}`];
  crmItem.created_at = dayjs().format(' MM月DD hh:mm');
  crmItem.remark_by = '当前账户';
  crmItem.last_remark = text;
  crmItem.remark_num = Number(crmItem.remark_num) + 1;
};
</script>
