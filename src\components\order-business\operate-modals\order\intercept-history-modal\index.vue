<template>
  <a-drawer
    class="custom-class"
    placement="right"
    title="拦截历史"
    :visible="bindVisible"
    :width="480"
    @close="bindVisible = false"
  >
    <a-spin
      :spinning="loading"
      tip="Loading..."
    >
      <a-timeline>
        <a-timeline-item
          v-for="(item, index) in list"
          :key="index"
        >
          <div class="item-header">
            <span class="item-reason">{{ item.reason_type_text }}</span>
            <span class="item-time">{{ item.created_at_text }}</span>
            <span class="item-creator">{{ item.created_by_name }}</span>
          </div>
          <div class="item-value">
            <div class="item-detail">
              {{ item.remark || item.interception_reason_text }}
            </div>
            <!-- 若选择的是文字原因，则无需展示 -->
            <div
              v-if="item.transfer_time != '0.00' || item.image_urls?.length"
              class="item-form-control"
            >
              <div
                v-if="item.image_urls?.length"
                class="image-box"
              >
                <div
                  v-for="urlItem in item.image_urls"
                  :key="urlItem"
                  class="image-item"
                >
                  <a-image
                    :height="88"
                    :src="urlItem"
                    style="max-width: 88px; max-height: 88px; object-fit: contain"
                    :width="88"
                  />
                </div>
              </div>
              <span
                v-else-if="item.transfer_time != '0.00'"
                class="form-text"
              >
                拦截流转时间{{ item.transfer_time }} 小时
              </span>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { useVModel } from '@/hook';

import type { IInterceptRecordItem } from './data';
import { getInterceptRecordsList, superGetInterceptRecordsList } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const list = ref<IInterceptRecordItem[]>([]);
const isSuper = useRoute().query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  const api = isSuper ? superGetInterceptRecordsList : getInterceptRecordsList;
  api({ order_id: props.orderId })
    .then(res => {
      const { data } = res;
      list.value = data || [];
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<style lang="less" scoped>
.item-header {
  clear: both;

  .item-reason {
    color: #061533;
    font-weight: 500;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
  }

  .item-time {
    margin-left: 8px;
    color: rgba(6, 21, 51, 0.45);
    font-weight: 400;
    font-size: 12px;
    font-style: normal;
    line-height: 20px;
  }

  .item-creator {
    float: right;
    color: rgba(6, 21, 51, 0.65);
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
  }
}

.item-value {
  width: 100%;
  margin: 8px 0 6px 0;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;

  .item-detail {
    color: #061533;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    text-align: left;
  }

  .item-form-control {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(6, 21, 51, 0.06);

    .form-text {
      color: rgba(6, 21, 51, 0.65);
      font-weight: 400;
      font-size: 14px;
      font-style: normal;
      line-height: 22px;
      text-align: left;
    }

    .image-box {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .image-item {
        box-sizing: border-box;
        width: 104;
        height: 104;
        padding: 8px;
        background: #fff;
        border: 1px solid rgba(6, 21, 51, 0.15);
        border-radius: 4px;
      }
    }
  }
}
</style>
