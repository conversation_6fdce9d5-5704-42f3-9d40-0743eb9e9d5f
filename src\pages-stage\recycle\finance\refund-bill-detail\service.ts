import { GET, POST } from '@/services/api';
export interface IVerification {
  server_id?: number | string;
  coin_type: number;
  remark: string;
  date: string;
  images: string[];
  pay_order_list: any[];
  source_order_list: any[];
  refund_order_id?: string;
}

//创建退款单
export function createRefundOrder(params: IVerification) {
  return POST('/super/recycle/sale-refund-order/save', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

//退款单-新建审核
export function createRefundCheck(params: IVerification) {
  return POST('/super/recycle/sale-refund-order/save-check', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

//编辑退款单
export function editRefundOrder(params: IVerification) {
  return POST('/super/recycle/sale-refund-order/edit', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

//退款单-编辑审核
export function editRefundCheck(params: IVerification) {
  return POST('/super/recycle/sale-refund-order/edit-check', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

//退款单-详情
export function getRefundDetail(params: any) {
  return GET('/super/recycle/sale-refund-order/detail', params);
}

// 退款单-商家源单列表
export function getRefundServerOrder(params: any) {
  return POST('/super/recycle/sale-refund-order/server-order-list', params);
}

//审核退款单
export function checkRefundOrder(params: any) {
  return POST('/super/recycle/sale-refund-order/check', params);
}

// 核销商家列表
export function getServerList() {
  return GET('/super/recycle/sale-verification/server-list');
}

// 退款单-获取需要退给商家的所有源单金额
export function getRefundWaitPay(params: any) {
  return GET('/super/recycle/sale-refund-order/get-wait-pay', params);
}

// 商家源单
export function getServerOrderList(params: any) {
  return GET('/super/recycle/sale-verification/server-order-list', params, {
    headers: { 'content-type': 'application/json;charset=utf-8' },
  });
}

// 核销列表选择参数
export function getSelectList() {
  return GET('/super/recycle/sale-verification/select-list');
}
