import type { TableColumnType } from 'ant-design-vue';

import { reactive } from 'vue';

type ServiceType = {
  columns?: TableColumnType[];
};

export default function (): ServiceType {
  const columns: TableColumnType[] = [
    {
      title: '服务项目',
      dataIndex: 'project',
      key: 'project',
      width: 100,
    },
    {
      title: '寄付方式',
      dataIndex: 'express_way',
      key: 'express_way',
      width: 100,
    },
    {
      title: '快递员姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '快递手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '托寄物品',
      dataIndex: 'sku_name',
      key: 'sku_name',
    },
    {
      title: '数量',
      dataIndex: 'num',
      key: 'num',
      width: 80,
    },
    {
      title: '实际重量（Kg）',
      dataIndex: 'weight',
      key: 'weight',
      width: 140,
    },
    {
      title: '取件状态',
      dataIndex: 'order_state_desc',
      key: 'order_state_desc',
      width: 100,
    },
  ];

  const service = reactive<ServiceType>({
    columns,
  });

  return service;
}
