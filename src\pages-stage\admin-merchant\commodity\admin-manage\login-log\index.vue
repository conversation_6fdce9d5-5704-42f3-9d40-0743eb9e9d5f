<template>
  <div class="page-wrap">
    <div class="page-header p-wrap">
      <span class="text">登录记录</span>
    </div>
    <a-table
      :columns="columns"
      :data-source="list"
      :loading="listLoading"
      :pagination="page"
      @change="tableChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

import { useTable } from '@/hook/component/use-table';

import { columns } from './config';

const imgOssServer = ref<string>('');
const { list, listLoading, page, getTableList, tableChange } = useTable({
  url: '/admin/log-inf',
  formatHandle: res => {
    imgOssServer.value = res.data.imgOssServer;
    return res.data.list;
  },
  // searchForm: searchParams, // 双向绑定检索表单，已经一些tab状态栏
});

getTableList();
</script>

<style scoped lang="less">
.page-wrap {
  min-height: 100vh;
  padding: 24px;
  background: #fff;
}

.p-wrap {
  padding-bottom: 24px;
  background: #fff;
  .text {
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    font-size: 20px;
  }
}
</style>
