<template>
  <a-modal
    v-model:visible="bindVisible"
    :title="orderId"
    @ok="confirm"
  >
    <a-spin
      class="flex-wrap flex-y-center"
      :spinning="loading"
    >
      <div>是否支持续租：</div>
      <a-radio-group v-model:value="canContinue">
        <a-radio :value="1">
          是
        </a-radio>
        <a-radio :value="0">
          否
        </a-radio>
      </a-radio-group>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { useVModel } from '@/hook';
import { setSuperOrderCanContinue, setStoreOrderCanContinue } from './services';
import type { TRefreshDataKey } from '@/components/order-business/typing';

const props = defineProps<{
  visible?: boolean;
  orderId: string;
  renewContinue: number;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);
const canContinue = ref(1);
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      canContinue.value = props.renewContinue;
    }
  },
  { immediate: true },
);

const isSuper = useRoute().query.role === 'super';

function confirm() {
  loading.value = true;
  const api = isSuper ? setSuperOrderCanContinue : setStoreOrderCanContinue;
  api({ order_id: props.orderId, can_continue: canContinue.value })
    .then(() => {
      message.success('操作成功!');
      bindVisible.value = false;
      emit('refresh', props.orderId, ['data']);
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
