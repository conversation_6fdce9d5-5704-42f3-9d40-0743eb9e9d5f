<template>
  <div class="table-wrap">
    <a-tabs
      v-model:activeKey="activeKey"
    >
      <a-tab-pane
        key="exchange"
        tab="换货建议"
      >
        <div class="alert-block">
          当前平台仓其他属性设备有货，可考虑与用户协商换货～
        </div>
      </a-tab-pane>
      <a-tab-pane
        v-if="isUpgrade"
        key="upgrade"
      >
        <template #tab>
          <div class="upgrade-title">
            <div class="rel-text">
              补贴升级
              <div class="pos-text">
                NEW
              </div>
            </div>
            <a-tooltip placement="rightBottom">
              <template #title>
                补贴发货：该补贴发货模式由平台补贴部分金额进行设备内存升级发货（补贴费用区间200～500）。
              </template>
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <div class="alert-block">
          <span>当前平台仓其他属性设备有货，选择升级发货平台会按照规则</span>
          <span class="red-text">补贴相应金额</span>
          <span>，可考虑与用户协商换货～</span>
        </div>
      </a-tab-pane>
    </a-tabs>
    <div
      v-if="activeKey === 'exchange'"
      class="header"
    >
      换货建议
      <span class="hint">{{
        props.checked
          ? '该型号下您在平台中其他颜色有送检库存，可考虑与用户协商换货～'
          : '当前平台仓其他属性设备有货，可考虑与用户协商换货～'
      }}</span>
    </div>
    <a-table
      :columns="columns"
      :data-source="activeKey === 'exchange' ? exchangeList : upgradeList"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'handle'">
          <span
            v-if="activeKey === 'exchange'"
            class="link-btn"
            @click="emit('affirmBarter', record.sku_id, record.warehouse_id)"
          >换货下发</span>
          <span
            v-else
            class="link-btn"
            @click="emit('upgradeBarter', record.sku_id, record.warehouse_id)"
          >升级下发</span>
        </template>
      </template>
    </a-table>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted,ref } from 'vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

import { generatePriceList } from '../composables/use-exchange-advice';

const props = defineProps({
  exchangeTableData: {
    type: Array,
    default: () => [],
  },
  upgradeTableData: {
    type: Array,
    default: () => [],
  },
  isSupport: {
    type: Boolean,
    default: false,
  },
  isUpgrade: {
    type: Boolean,
    default: false,
  },
  checked: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['affirmBarter','upgradeBarter']);

const activeKey = ref('exchange');

const columns = [
  {
    title: '有库存货品信息',
    dataIndex: 'sku_info',
  },
  {
    title: '预估价格',
    dataIndex: 'price',
    width: 168,
  },
  {
    title: '操作',
    dataIndex: 'handle',
    width: 168,
  },
];

const exchangeList = computed(() => {
  return generatePriceList(props.exchangeTableData, props.isSupport);
});

const upgradeList = computed(() => {
  return generatePriceList(props.upgradeTableData, props.isSupport);
});

onMounted(() => {
  if (exchangeList.value.length) {
    activeKey.value = 'exchange';
  } else if(props.isUpgrade){
    activeKey.value = 'upgrade';
  }
});
</script>
<style lang="less" scoped>
.table-wrap {
  margin-top: 0;
}

.link-btn {
  color: #3777ff;
  cursor: pointer;
}

.alert-block {
  display: inline-block;
  margin-bottom: 16px;
  padding: 4px 8px;
  color: var(--ant-primary-color);
  font-size: 14px;
  background-color: var(--ant-primary-1);
  border: 1px solid var(--ant-primary-color);
  border-radius: 4px;

  .red-text {
    color:red;
  }
}

.upgrade-title {
  display: flex;
  gap: 16px;
  align-items: center;
}

.rel-text {
  position: relative;

  .pos-text {
    position: absolute;
    top: -12px;
    right: -18px;
    width: 32px;
    color: #fff;
    font-size: 10px;
    text-align: center;
    background-color: #ee502f;
    border-radius: 8px 8px 8px 0;
  }
}

.header {
  padding-bottom: 12px;
  color: rgba(6, 21, 51, 0.85);
  .hint {
    padding-left: 12px;
    color: #faad14;
    font-weight: 400;
  }
}
</style>
