<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { apiGetInteractRiskDetail, apiSaveInteractRiskDetail } from './service';
import { Data } from './data';

const PHONE_ITEM: Data.IZfbPhoneItem = {
  min_ali_id: '',
  max_ali_id: '',
};

const settingForm = reactive<Data.ISettingForm>({
  id: '',
  alipay_user_ids: [],
  aliUserIdConfig: [],
});
console.log(settingForm);
const addPhoneItem = () => {
  settingForm.aliUserIdConfig.push({ ...PHONE_ITEM });
};
const delPhoneItem = (index: number) => {
  settingForm.aliUserIdConfig.splice(index, 1);
  console.log(settingForm);
};
const getInteractRiskDetail = () => {
  apiGetInteractRiskDetail().then(res => {
    settingForm.id = res.data.id;
    settingForm.aliUserIdConfig = res.data.aliUserIdConfig;
    settingForm.alipay_user_ids = res.data.alipay_user_ids ? res.data.alipay_user_ids.split(',') : [];
  });
};
const saveInteractRiskDetail = () => {
  const params = {
    id: settingForm.id,
    aliUserIdConfig: [...settingForm.aliUserIdConfig],
    alipay_user_ids: settingForm.alipay_user_ids.join(),
  };
  console.log(params);
  apiSaveInteractRiskDetail(params);
};
onMounted(() => {
  getInteractRiskDetail();
});
</script>

<template>
  <div class="source-zfb">
    <a-form
      layout="vertical"
      :model="settingForm"
    >
      <a-form-item label="指定支付宝">
        <a-select
          v-model:value="settingForm.alipay_user_ids"
          mode="tags"
          :open="false"
        />
      </a-form-item>
      <a-form-item label="指定支付宝尾号">
        <transition-group name="fade">
          <div
            v-for="(item, index) in settingForm.aliUserIdConfig"
            :key="item.id"
            class="phone-tail-item"
          >
            <a-input
              v-model:value="item.min_ali_id"
              allow-clear
              placeholder="请输入开始ID"
            />
            <div class="item-divider" />
            <a-input
              v-model:value="item.max_ali_id"
              allow-clear
              placeholder="请输入结束ID"
            />
            <DeleteOutlined @click="delPhoneItem(index)" />
          </div>
        </transition-group>
        <div
          class="add-phone-btn"
          @click="addPhoneItem"
        >
          <PlusOutlined />继续添加
        </div>
      </a-form-item>
      <a-form-item>
        <a-button
          type="primary"
          @click="saveInteractRiskDetail"
        >
          提交
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="less" src="./index.less"></style>
