import { GET, POST } from '@/services/api';

export function getAdvanceReport(params: { orderId: string; isSuper: boolean }) {
  const { orderId, isSuper } = params;
  if (isSuper) {
    return GET('/super/risk-report-order/render-fee-report', { order_id: orderId }, { closeErrorMessage: true });
  } else {
    return GET('/risk-report-order/generate-fee-report', { order_id: orderId }, { closeErrorMessage: true });
  }
}

export function evaluateReport(data: any, isSuper: boolean) {
  return GET(isSuper ? '/super/risk-report-order/evaluate-report' : '/risk-report-order/evaluate-report', data);
}

export const APIPreviewReport = (params: any) => {
  return POST('/super/risk-report-config/preview-report', params);
};
