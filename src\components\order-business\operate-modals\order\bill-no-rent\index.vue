<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { useVModel } from '@vueuse/core';
import { FormGroupItem, RForm } from 'rrz-web-design';

import type { TRefreshDataKey } from '@/components/order-business/typing';

import { createBill, formatPre, getFilterData } from './services';

const route = useRoute();
formatPre(route);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});
const emits = defineEmits<{
  (event: 'update:visible', value: [] | [string, string]): void;
  (e: 'refresh', orderId: string, dataKey?: TRefreshDataKey[]): void;
}>();

const copyVisible = useVModel(props, 'visible', emits);
const loading = ref(false);
watch(
  () => props.visible,
  val => {
    if (val) {
      getFilterData().then(res => {
        billTypeOptions.value = res.data.bill_type;
      });
    }
  },
  {
    immediate: true,
  },
);

function confirm() {
  const data = {
    order_id: props.orderId,
    order_type: 10,
    ...formState,
  };
  createBill(data).then(() => {
    message.success('成功');
    copyVisible.value = false;
    emits('refresh', props.orderId, ['data', 'all_remark', 'super2_remark']);
  });
}

const refNoRent = ref();

function close() {
  formState.bill_type = undefined;
  refNoRent.value.resetFields();
}

const formState = reactive({});
const searchFormGroup: FormGroupItem[] = [
  {
    key: 'bill_amount',
    label: '账单金额',
    fragmentKey: 'renderInputNumber',
    elProps: {
      min: 0,
      precision: 2,
    },
  },
  {
    key: 'bill_type',
    fragmentKey: 'renderSelect',
  },
  {
    key: 'bill_describe',
    label: '账单说明',
    fragmentKey: 'renderTextarea',
  },
];
const billTypeOptions = ref([]);
</script>

<template>
  <!--  该页面再发货工作台也做复用-->
  <a-modal
    v-model:visible="copyVisible"
    :confirm-loading="loading"
    title="创建非租金账单"
    @cancel="close"
    @ok="confirm"
  >
    <a-alert
      class="warning-alert"
      type="warning"
    >
      <template #description>
        <div>规则说明</div>
        <div>1、订单有同类型【待付款】的非租金账单时不允许创建</div>
        <div>2、订单状态非【待发货】【待收货】【待归还】【归还中】时不允许创建</div>
        <div>3、订单未生成租金账单时不允许创建</div>
      </template>
    </a-alert>
    <RForm
      ref="refNoRent"
      v-model:value="formState"
      :form-group="searchFormGroup"
    >
      <template #bill_type>
        <a-form-item label="账单类型">
          <a-select
            v-model:value="formState.bill_type"
            allow-clear
            :options="billTypeOptions"
            placeholder="请选择"
          />
          <div class="bill-type-text">
            非租金账单不支持自动代扣，优先用户主动支付
          </div>
        </a-form-item>
      </template>
    </RForm>
  </a-modal>
</template>

<style scoped lang="less">
.warning-alert {
  margin-bottom: 24px;
}

.bill-type-text {
  padding-top: 8px;
  color: #ccc;
  font-size: 14px;
}
</style>
