<!--
  @description 上传三方协议模态框
-->
<template>
  <a-modal
    class="upload-tripartite"
    :title="'上传三方合同'"
    :visible="visible"
    :width="480"
    @cancel="emit('update:visible', false)"
    @ok="handleOk"
  >
    <a-upload-dragger
      v-model:fileList="uploadList"
      class="upload"
      :custom-request="uploadToOss"
      :multiple="true"
      name="file"
      @change="handleChange"
    >
      <img src="https://img1.rrzuji.cn/uploads/scheme/2311/15/m/kfzQATBx1sSJZzfFqUQN.png">
      <p class="main-title">
        点击或将文件拖拽到这里上传
      </p>
      <p class="sub-title">
        支持文件类型：.doc .docx .pdf等文档文件 或.jpg .png .jpeg等图片文件
      </p>
    </a-upload-dragger>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { generateAndUpload2Oss } from '@/utils/oss-helper';
import { message, type UploadChangeParam } from 'ant-design-vue';
import { saveEvidence } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  operateId: {
    type: String,
    default: '0',
  },
  orderId: {
    type: String,
    default: '0',
  },
});

const emit = defineEmits(['update:visible', 'upload']);
const uploadList = ref<any>([]);

const handleChange = async (info: UploadChangeParam) => {
  uploadList.value = info.fileList;
};

const handleOk = () => {
  const fileList = uploadList.value.map((item: any) => {
    return {
      url: item.response.url,
      origin_name: item.name,
      size: item.size,
    };
  });
  saveEvidence({
    order_id: props.orderId,
    type: 'pushEvidence',
    evidencePhase: 127,
    evidenceType: 'file',
    operateId: props.operateId,
    saveData: fileList,
  }).then(() => {
    message.success('上传成功');
    emit('update:visible', false);
    // 清空上传列表
    uploadList.value.splice(0, uploadList.value.length);
    emit('upload');
  });
};

// 上传至oss
const uploadToOss = async (options: any) => {
  const res = await generateAndUpload2Oss(
    options.file as File,
    {
      progress: percent => options.onProgress && options.onProgress({ percent: percent }),
    },
    true,
  );
  options.onSuccess &&
  options.onSuccess({
    url: res.url,
    ossServer: res.oss,
    status: 'done',
  });
};
</script>

<style scoped lang="scss">
.upload-tripartite {
  min-width: 432px;

  .upload {
    img {
      width: 49px;
      height: 48px;
    }

    .main-title {
      margin-top: 16px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
    }

    .sub-title {
      color: rgba(6, 21, 51, 0.45);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
    }
  }
}

:deep(.upload) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 426px;
  height: 214px;
  padding: 40px 82px 38px 84px;
  border-radius: 4px;
}
</style>
