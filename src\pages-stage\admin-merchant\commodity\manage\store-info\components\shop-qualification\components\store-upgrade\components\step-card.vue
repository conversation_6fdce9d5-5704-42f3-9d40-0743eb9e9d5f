<template>
  <div class="flex-wrap flex-y-center">
    <div class="step-card">
      <div class="step-card-header">
        <span class="step-card-title">{{ title }}</span>
        <Tag
          v-if="tag"
          :tag="tag.name"
          :type="tag.type"
        />
        <a-button
          v-if="btn"
          class="step-card-button"
          :type="btn.type"
          @click="btn.onClick"
        >
          {{ btn.name }}
        </a-button>
      </div>
      <div class="step-card-body">
        <div
          class="step-card-body-content"
          :style="instructionStyle"
        >
          <div
            class="step-card-body-bg"
            :style="{ backgroundImage }"
          />
          {{ instruction }}
        </div>
      </div>
    </div>
    <div
      v-if="showArrow"
      class="step-card-header-arrow"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, CSSProperties } from 'vue';

import Tag from './tag.vue';

const props = defineProps<{
  title: string;
  instruction: string;
  instructionStyle?: CSSProperties;
  backgroundImageType: string;
  showArrow: boolean;
  tag?: any;
  btn?: any;
}>();

// 背景图
const backgrounds = { submit: 'ODHwjyF0qgR4SaJMFIVA', setting: 'HPIxAnbTQW9SPaeKLY2i' };
const backgroundImage = computed(
  () => `url('https://img1.rrzuji.cn/uploads/scheme/2506/26/m/${backgrounds[props.backgroundImageType]}.png')`,
);

const tag = computed(() => props.tag ?? false);
const btn = computed(() => props.btn ?? false);
</script>

<style lang="less" scoped>
.step-card {
  display: flex;
  flex-direction: column;
  width: 453px;
  height: 229px;
  overflow: hidden;
  background-color: #f0f6ff;
  border-radius: 8px;
}

.step-card-header {
  display: flex;
  gap: 8px;
  align-items: center;
  height: 64px;
  padding: 20px 24px;
  background: linear-gradient(270deg, #e6eeff 0%, #cbdcff 100%);
  border-radius: 8px;
}

.step-card-title {
  color: #3777ff;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
}

.step-card-button {
  margin-left: auto;
}

.step-card-header-arrow {
  width: 36px;
  height: 42px;
  margin-left: 14px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2506/30/m/rUcYpbBYueffYHpv84Lc.png') 100% / cover;
}

.step-card-body {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 16px;
}

.step-card-body-content {
  position: relative;
  flex: 1;
  padding: 16px;
  color: #061533a6;
  line-height: 22px;
  background: linear-gradient(180deg, #fff 0%, #ffffffd9 100%);
  border-radius: 8px;
}

.step-card-body-bg {
  position: absolute;
  right: 16px;
  bottom: 0;
  width: 124px;
  height: 124px;
  background-size: 100%;
}
</style>
