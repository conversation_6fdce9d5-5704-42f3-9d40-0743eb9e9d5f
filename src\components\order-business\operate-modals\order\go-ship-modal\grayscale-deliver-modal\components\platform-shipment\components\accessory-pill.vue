<template>
  <span>
    <a-tooltip :overlay-style="{ maxWidth: '310px' }">
      <template #title>
        <div class="flex-wrap flex-vertical flex-gap-24">
          <div
            v-for="typeItem in data"
            :key="typeItem.type"
          >
            <div>
              <span>{{ typeItem.type === 1 ? '赠品' : '需归还' }}</span>
              <span
                v-if="typeItem.type === 2"
                class="light"
              >（租完即送套餐，配件默认无需归还）</span>
              <span>：</span>
            </div>
            <div
              v-for="item in typeItem.list"
              :key="item.accessory_type"
            >
              <span>{{ item.accessory_name }} x{{ item.num }}</span>
            </div>
          </div>
        </div>
      </template>
      <a-button
        class="flex-wrap flex-y-center flex-gap-4"
        size="small"
        type="primary"
      >
        <img src="https://img1.rrzuji.cn/uploads/scheme/2401/15/m/65KEAzKxC1pa2ai0L72w.svg">
        <span>固定配件</span>
      </a-button>
    </a-tooltip>
  </span>
</template>

<script lang="ts" setup>
import type { IAccessoryData } from '../../../../data';

defineProps<{
  data?: IAccessoryData[];
}>();
</script>

<style lang="less" scoped>
.light {
  color: rgba(255, 255, 255, 0.6);
}
</style>
