<template>
  <section>
    <div class="store-alert">
      <a-space>
        <div>您当前店铺类型为</div>
        <Tag
          :tag="upgraded ? '码商型商家' : '平台型商家'"
          type="primary"
        />
      </a-space>
    </div>

    <div class="store-panel">
      <div class="store-image" />

      <div class="store-upgrade-title">
        <i class="icon-vip" /><span>升级为</span><span class="important">码商型商家</span>
      </div>

      <div class="instruction-box">
        <div>1.已完成线下面审，检查经营行为无异常，有线下门店</div>
        <div>2.入驻商家的经营主体与实际经营地址一致</div>
      </div>

      <div
        :class="['upgrade-btn', { disabled: upgraded }]"
        @click="handleUpgrade"
      >
        {{ upgraded ? '已升级完成' : '去升级' }}
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { useSharedData } from '../composables/use-shared-data';
import { EEnterStatus, EShowComponent } from '../data.d';
import { recordUpgrade } from '../services';
import Tag from './tag.vue';

const { sharedData, updateSharedData } = useSharedData();

const upgraded = computed(() => sharedData.audit_status === EEnterStatus.Passed);

function handleUpgrade() {
  if (!upgraded.value) {
    recordUpgrade();
    updateSharedData({
      activeComponent: EShowComponent.Condition,
    });
  }
}
</script>

<style lang="less" scoped>
.store-alert {
  padding: 14px 16px;
  background: linear-gradient(90deg, #6b9bff1f 0%, #3471f30d 100%);
  border-radius: 4px;
}

.store-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 482px;
  height: 477px;
  margin-top: 16px;
  padding: 65px 40px 40px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/11/m/othZLu1aheUhZ0511UDU.png') 100% / cover;
}

.store-image {
  width: 88px;
  height: 88px;
  margin-bottom: 24px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/11/m/DRJuPcvrIKEvkZdsrbpr.png') 100% / cover;
}

.store-upgrade-title {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 24px;
  color: #061533a6;
  font-size: 20px;
  line-height: 24px;
}

.icon-vip {
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url('https://img1.rrzuji.cn/uploads/scheme/2503/12/m/j1zBEJ83wtIAyTlrJvYB.png') 100% / cover;
}

.important {
  color: #3777ff;
}

.instruction-box {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 88px;
  padding: 16px;
  color: #061533a6;
  line-height: 18px;
  background: linear-gradient(290deg, #f7fbff0f 0%, #e8f1ff 100%);
  border: 1px solid #fff;
  border-radius: 8px;
}

.upgrade-btn {
  width: 100%;
  padding: 12px 0;
  color: #fff;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  background-color: #3777ff;
  border-radius: 4px;
  cursor: pointer;
}

.upgrade-btn.disabled {
  background-color: #3777ff40;
  cursor: not-allowed;
}
</style>
