.source-zfb {
  width: 640px;
  padding: 8px 32px 24px;
  .phone-tail-item {
    position: relative;
    display: flex;
    align-items: center;
    width: 370px;
    margin-bottom: 16px;
    .item-divider::after {
      margin: 0 8px;
      color: rgba(0, 0, 0, 0.65);
      content: "-";
    }
    .anticon.anticon-delete{
      position: absolute;
      right: -26px;
      color: rgba(0, 0, 0, 0.65);
      cursor: pointer;
    }
  }
  .add-phone-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 370px;
    height: 32px;
    border: 1px dashed rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    cursor: pointer;
    .anticon {
      margin-right: 8px;
    }
    &:hover {
      color: #00c8be;
      background: #fff;
      border-color: #00c8be;
    }
  }
}
