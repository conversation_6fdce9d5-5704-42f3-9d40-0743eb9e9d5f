<template>
  <div class="base-block">
    <FormInfo
      class="father_use_send"
      title="取件信息"
    >
      <div class="form-item required">
        <span class="form-item__label">寄件人姓名:</span>
        <div class="form-item__content">
          <a-input
            v-model:value="formState.userName"
            placeholder="请输入"
            style="width: 194px"
          />
        </div>
      </div>
      <div class="form-item required">
        <span class="form-item__label">寄件人手机号:</span>
        <div class="form-item__content">
          <a-input-number
            v-model:value="formState.phone"
            :controls="false"
            :min="0"
            placeholder="请输入"
            :precision="0"
            style="width: 194px"
          />
        </div>
      </div>
      <div class="form-item required">
        <span class="form-item__label">取件时间:</span>
        <div class="form-item__content">
          <a-cascader
            v-model:value="formState.bookTime"
            :options="createTimeOptions()"
            placeholder="请选择"
            style="width: 197px"
            @change="onChange"
          />
        </div>
      </div>
      <br>
      <div class="use-send">
        <a-checkbox
          v-model:checked="isUseSend"
          @change="useSendChange"
        />
        同步为退货信息
      </div>
      <div
        class="form-item required"
        style="margin-top: 12px"
      >
        <span class="form-item__label">取件地址:</span>
        <div class="form-item__content">
          <div class="address-box">
            <a-select
              v-model:value="provinceValue"
              :filter-option="filterOption"
              :options="provinceOptions"
              placeholder="请选择省份"
              show-search
              style="width: 112px; margin-right: 4px"
              @change="
                () => {
                  cityValue = null;
                  areaValue = null;
                }
              "
            />
            <a-select
              v-model:value="cityValue"
              :filter-option="filterOption"
              :options="cityOptions"
              placeholder="请选择城市"
              show-search
              style="width: 112px; margin-right: 4px"
              @change="
                () => {
                  areaValue = null;
                }
              "
            />
            <a-select
              v-model:value="areaValue"
              :filter-option="filterOption"
              :options="areaOptions"
              placeholder="请选择市区"
              show-search
              style="width: 112px"
            />
          </div>
          <a-input
            v-model:value="addressValue"
            placeholder="请输入详细地址"
            style="width: 535px; margin-left: 8px"
          />
        </div>
      </div>
    </FormInfo>
    <FormInfo title="收款信息">
      <div class="form-item required">
        <span class="form-item__label">真实姓名:</span>
        <div class="form-item__content">
          <a-input
            v-model:value="formState.payeeName"
            allow-clear
            placeholder="请输入"
            style="width: 194px"
            @change="onChange"
          />
        </div>
      </div>
      <div class="form-item required">
        <span class="form-item__label">收款方式:</span>
        <div class="form-item__content">
          <a-select
            v-model:value="formState.payeeType"
            disabled
            :options="optionsMap.collect"
            placeholder="请选择"
            style="width: 194px"
            @change="onChange"
          />
        </div>
      </div>
    </FormInfo>
    <FormInfo
      v-if="!isUseSend"
      title="退货信息"
    >
      <div class="form-item required">
        <span class="form-item__label">收件人姓名:</span>
        <div class="form-item__content">
          <a-input
            v-model:value="formState.return_name"
            placeholder="请输入"
            style="width: 194px"
          />
        </div>
      </div>
      <div class="form-item required">
        <span class="form-item__label">收件人手机号:</span>
        <div class="form-item__content">
          <a-input-number
            v-model:value="formState.return_phone"
            :controls="false"
            :min="0"
            placeholder="请输入"
            :precision="0"
            style="width: 194px"
          />
        </div>
      </div>
      <br>
      <div
        class="form-item required"
        style="margin-top: 12px"
      >
        <span class="form-item__label">收件地址:</span>
        <div class="form-item__content">
          <div class="address-box">
            <a-select
              v-model:value="returnProvinceValue"
              :filter-option="returnFilterOption"
              :options="returnProvinceOptions"
              placeholder="请选择省份"
              show-search
              style="width: 112px; margin-right: 4px"
              @change="
                () => {
                  returnCityValue = null;
                  returnAreaValue = null;
                }
              "
            />
            <a-select
              v-model:value="returnCityValue"
              :filter-option="returnFilterOption"
              :options="returnCityOptions"
              placeholder="请选择城市"
              show-search
              style="width: 112px; margin-right: 4px"
              @change="
                () => {
                  returnAreaValue = null;
                }
              "
            />
            <a-select
              v-model:value="returnAreaValue"
              :filter-option="returnFilterOption"
              :options="returnAreaOptions"
              placeholder="请选择市区"
              show-search
              style="width: 112px"
            />
          </div>
          <a-input
            v-model:value="returnAddressValue"
            placeholder="请输入详细地址"
            style="width: 535px; margin-left: 8px"
          />
        </div>
      </div>
      <br>
      <div
        class="return_infer"
        style="margin-top:12px"
      >
        请完善退货地址，若出现退货将退货至该地址
      </div>
    </FormInfo>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, PropType, onMounted } from 'vue';
import { getFilterItem, getLastAddr } from '../service';
import FormInfo from './form-info.vue';
import { IFormState } from '../data';
import { baseForm, createTimeOptions } from '../config';
import { useAddress } from '@/pages-stage/recycle/order/self-order-list/composables/use-address';

const props = defineProps({
  value: {
    type: Object as PropType<IFormState>,
    default: () => ({}),
  },
});
const emits = defineEmits(['update:value']);

const {
  provinceValue,
  cityValue,
  areaValue,
  addressValue,

  provinceOptions,
  cityOptions,
  areaOptions,

  filterOption,
} = useAddress();

let {
  provinceValue:returnProvinceValue,
  cityValue:returnCityValue,
  areaValue:returnAreaValue,
  addressValue:returnAddressValue,

  provinceOptions:returnProvinceOptions,
  cityOptions:returnCityOptions,
  areaOptions:returnAreaOptions,

  filterOption:returnFilterOption
} = useAddress();
//是否使用发货地址用作退货地址
const isUseSend = ref(true)
const formState = ref<IFormState>({
  ...baseForm,
});

const useSendChange = (e:any) => {
  formState.value.use_send_info = e.target.checked ? 1 : 2;
  if (!e.target.checked) {
    //取件信息赋值退货信息
    formState.value.return_name = formState.value.userName;
    formState.value.return_phone = formState.value.phone;
    returnProvinceValue.value =  provinceValue.value;
    returnCityValue.value = cityValue.value;
    returnAreaValue.value = areaValue.value;
    returnAddressValue.value = addressValue.value;
  } else {
    formState.value.return_name = null;
    formState.value.return_phone = null;
    returnProvinceValue.value = null;
    returnCityValue.value = null;
    returnAreaValue.value = null;
    returnAddressValue.value = null;
  }
  emits('update:value', formState.value);
}
watch(
  props.value,
  (val: IFormState) => {
    if (!val) return;
    const keyArr = Object.keys(baseForm);
    for (const key of keyArr) {
      val[key] && (formState.value[key] = val[key]);
    }
  },
  {
    deep: true,
  },
);

watch(
  [() => provinceValue.value, () => cityValue.value, () => areaValue.value, () => addressValue.value],
  ([province, city, area, address]) => {
    formState.value['province_name'] = province;
    formState.value['city_name'] = city;
    formState.value['area_name'] = area;
    formState.value['addr_detail'] = address;
    emits('update:value', formState.value);
  },
);
watch(
  [() => returnProvinceValue.value, () => returnCityValue.value, () => returnAreaValue.value, () => returnAddressValue.value],
  ([province, city, area, address]) => {
    formState.value['return_province_name'] = province;
    formState.value['return_city_name'] = city;
    formState.value['return_area_name'] = area;
    formState.value['return_addr_detail'] = address;
    emits('update:value', formState.value);
  },
);
const optionsMap = reactive({
  collect: [],
});

const onChange = () => {
  emits('update:value', formState.value);
};

const getOptions = async () => {
  const res = await getFilterItem();
  const { payeeType } = res.data;
  optionsMap.collect = payeeType || [];
};

// 获取默认地址回显
const getAddr = async () => {
  const { data } = await getLastAddr();
  const addKeyMap = {
    province_name: provinceValue,
    city_name: cityValue,
    area_name: areaValue,
    addr_detail: addressValue,
  };
  for (const key in addKeyMap) {
    const value = data[key];
    if (value) addKeyMap[key].value = data[key];
  }
};

onMounted(() => {
  getOptions();
  getAddr();
});
</script>

<style lang="less" scoped>
.base-block {
  display: grid;
  grid-template-columns: auto;
  gap: 16px;
  min-width: 1200px;
  margin: 4px 0 24px 0;
  .form-item {
    position: relative;
    display: inline-flex;
    align-items: center;
    margin-right: 48px;
    padding-left: 18px;
    &:nth-last-child(1) {
      margin-right: 0;
    }
    &::after {
      position: absolute;
      top: 8px;
      left: 0;
      width: 4px;
      height: 16px;
      background: #3777ff;
      border-radius: 3px;
      content: '';
    }
    &.required {
      &::before {
        position: absolute;
        top: 7px;
        left: 11px;
        color: #ff4d4f;
        content: '*';
      }
    }
    &__label {
      display: block;
      margin-right: 4px;
      color: rgba(6, 21, 51, 0.85);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
    &__content {
      display: inline-flex;
      align-items: center;
    }
  }
}
.address-box {
  display: flex;
  flex: 1;
}

//判断是否是寄件地址
.father_use_send{
  position:relative;

}
.use-send{
  position:absolute;
  top:62%;
  left:16px;
  width: 120px;
  color: rgba(6,21,51,0.65);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

.return_infer{
  position:relative;
  width:588px;
  padding-left:10px;
  color: #FF4D4F;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  &::before {
        position: absolute;
        top: 3px;
        left: 0;
        color: #ff4d4f;
        content: '*';
      }
}
</style>
