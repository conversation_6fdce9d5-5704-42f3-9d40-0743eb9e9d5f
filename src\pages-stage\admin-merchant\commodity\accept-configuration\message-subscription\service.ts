import { GET,POST } from '@/services/api';
//消息订阅-模板列表
export function messageTepList(params:any) {
  return GET('/server-message-template/list',params);
}
//消息订阅-更新模板状态
export function messageTepUp(params:any) {
  return POST('/server-message-template/update-status',params);
}
//消息订阅-模板联系人列表
export function messageTepContactList(params:any) {
  return GET('/server-message-template/contact-list',params);
}
//消息订阅-模板新增联系人
export function messageTepContactAdd(params:any) {
  return POST('/server-message-template/contact-add',params);
}
//消息订阅-模板删除联系人
export function messageTepContactDel(params:any) {
  return POST('/server-message-template/contact-delete',params);
}
//基础数据
export function messageBase() {
  return GET('/server-message-template/base');
}
//发送手机验证码
export function messageTepSend(params:any) {
  return POST('/server-message-template/send-code',params);
}

