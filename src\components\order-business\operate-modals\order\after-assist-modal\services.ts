import axios, { AxiosResponse } from 'axios';

import { GET, POST } from '@/services/api';
import { formatUrl } from '@/services/format-url';

/**
 * 判断续租租金是否通过
 */
export function ApiCheckRenwalData(data: any) {
  return POST('/order/check-renew-rental-when-change', data);
}

/**
 * 申请更换账单期数，期数获取（新）
 */
export function getPeriodsV2Api(data: { order_id: string }) {
  return GET('/v2-bill/get-bill-list', data);
}

/**
 * 校验工单是否可以创建
 */
export function checkWorkOrderCreateApi(data: any) {
  return POST('/work-order-examine/check-work-order-create', data);
}

/**
 * 获取期数选项
 */
export function ApiGetPeriods(data: any) {
  return GET('/v2-bill/get-period-num-list', data);
}

/**
 * 获取租后协助订单详情
 */
export function getOrderDetail(data: any) {
  return GET('/order/v2-detail', data);
}

/**
 * 获取买断尾款
 */
export function getRenewBuyoutPrice(data: any) {
  return POST('/order/preview-renew-buyout-price', data);
}

/**
 * 判断是否可以申请续租
 */
export function checkOrderContinue(data: any) {
  return GET('/super/v2-order/order-continue-validate', data);
}

/**
 * 校验该订单在一天内是否申请过相同的退款金额申请
 * @param params order_id:订单id money:退款金额 type: 1：租后协助-申请退款（默认）；2：工单管理-退款申请；3：订单列表V3-申请退款；4：订单退款工作台（新）
 * @returns status 0：无需提醒；1：需要提醒
 */
export function checkRemindApi(params: {
  order_id: string;
  money?: number;
  type: 1;
}): Promise<AxiosResponse<{ status: number }>> {
  return GET('/super/order-refund/check-remind', params);
}

// 上面接口的商家端
export function checkRemindApiMerch(params: {
  order_id: string;
  money?: number;
  type: 1;
}): Promise<AxiosResponse<{ status: number }>> {
  return GET('/order-refund/check-remind', params);
}

/**
 *  查询订单信息(主要针对冻结金额转押金)
 * @param params order_id:订单id money:退款金额 type: 1：租后协助-申请退款（默认）；2：工单管理-退款申请；3：订单列表V3-申请退款；4：订单退款工作台（新）
 * @returns status 0：无需提醒；1：需要提醒
 */
export function depositTransferPayment(order_id: string) {
  return GET('/order/deposit-transfer-payment', { order_id });
}

/**
 * 申请续租
 */
export function applyRenewal(data: any) {
  return POST('/work-order/apply-renewal', data);
}

// 申请退款
export function applyRefund(data: any) {
  return POST('/work-order/apply-refund', data);
}

// 修改账单金额
export function modifyBillAmount(data: any) {
  return POST('/work-order/modify-bill-amount', data);
}

// 更换账单期数
export function changeBillingCycle(data: any) {
  return POST('/work-order/change-billing-cycle', data);
}

// 修改订单总租金
export function updateOrderTotalRent(data: any) {
  return POST('/work-order/update-order-total-rent', data);
}

// 修改订单归还状态
export function updateOrderReturnStatus(data: any) {
  return POST('/work-order/update-order-return-status', data);
}

// 修改账单支付状态
export function updateBillPaymentStatus(data: any) {
  return POST('/work-order/update-bill-payment-status', data);
}

// 冻结押金转支付
export function freeDepositForPayment(data: any) {
  return POST('/work-order/freeze-deposit-for-payment', data);
}

/**
 * 租后协助上传文件
 */
export function commonFile(data: any) {
  const url = formatUrl({ url: '/common/mergeSliceFile' });
  return axios.post(url, data, {
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 租后协助上传图片
 */
export function commonImage(data: any) {
  const url = formatUrl({ url: '/common/image?pathName=activity&oss=1' });
  return axios.post(url, data, {
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 租后协助分割文件
 */
export function sliceFileUpload(data: any) {
  const url = formatUrl({ url: '/common/sliceFileUpload' });
  return axios.post(url, data, {
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
}

// 补订单完结申请
export function completeReplenishOrderApply(data: any) {
  return POST('/work-order/supple-order-complete', data);
}

// 检查是否可以申请补订单完结
export function checkCompleteReplenishOrderApply(data: any) {
  return GET('/work-order/check-create', data);
}

// 延长完结申请
export function extendOrderEndApply(data: any) {
  return POST('/work-order/apply-delay-complete', data);
}

// 结算比例申诉
export function appealClosing(data: any) {
  return POST('/work-order/change-order-ratio', data);
}

// 修改寄出方式
export function changeSendApi(data: any) {
  return POST('/work-order/create', data);
}

// 是否可以申请修改寄出方式
export function isCanSendApply(data: { order_id: string; type: string }) {
  return POST('/work-order/create-before-check', data);
}

// 获取寄出方式详情
export function getSendTypeInfo(data: { order_id: string }) {
  return GET('/orderLogisticsV2/order-logistics-v2/get-send-type-info', data);
}
