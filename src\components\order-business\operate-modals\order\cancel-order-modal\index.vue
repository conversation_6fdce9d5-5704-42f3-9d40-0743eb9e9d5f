<template>
  <BackstageCancelModal
    v-model:visible="backstageCancelVisible"
    :extra-data="extraData"
    :order-id="orderId"
    @success="onRefresh"
  />
  <UserCancelModal
    v-model:visible="userCancelVisible"
    :extra-data="extraData"
    :order-id="orderId"
    @success="onRefresh"
  />
  <AppleOrderCancelModal
    v-model:visible="appleOrderCancelVisible"
    :extra-data="extraData"
    :order-id="orderId"
    @success="onRefresh"
  />
  <AppleOrderCancelModalV1
    v-model:visible="appleOrderCancelVisibleV1"
    :extra-data="extraData"
    :order-id="orderId"
    @success="onRefresh"
  />
</template>

<script setup lang="ts">
import { type PropType, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import {
  getCancelOrderLimit,
  getRiskRepeatCancelWayApi,
  superGetCancelOrderLimit,
} from '@/components/order-business/operate-modals/order/cancel-order-modal/services';
import type { TRefreshDataKey } from '@/components/order-business/typing';
import { useVModel } from '@/hook';

import AppleOrderCancelModal from './components/apple-order-cancel-modal/index.vue';
import AppleOrderCancelModalV1 from './components/apple-order-cancel-modal-v1/index.vue';
import BackstageCancelModal from './components/backstage-cancel-modal.vue';
import UserCancelModal from './components/user-cancel-modal.vue';
import type { IExtraData } from './data.d';
import { getOrderCancelReasonOptionsApi } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
  extraData: {
    type: Object as PropType<IExtraData>,
    default: () => ({}),
  },
  onSuccess: {
    type: Function,
    default: null,
  },
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', orderId?: string, dataKey?: TRefreshDataKey[]): void;
}>();

const bindVisible = useVModel(props, 'visible', emit);
const backstageCancelVisible = ref(false);
const userCancelVisible = ref(false);
const appleOrderCancelVisible = ref(false);
const appleOrderCancelVisibleV1 = ref(false);
const isSuper = useRoute().query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      openModal();
      bindVisible.value = false;
    }
  },
  { immediate: true },
);
const route = useRoute();
const domain = route.query?.origin || window.location.origin;

function openModal() {
  const api = isSuper ? superGetCancelOrderLimit : getCancelOrderLimit; // 权限检测接口
  api({ order_id: props.orderId }).then(() => {
    const { is_cancel_work_order_id, key } = props.extraData;
    if (props.extraData.is_cancel_work_order_id) {
      handleCancelWorkOrder(Number(is_cancel_work_order_id), Number(key));
    } else {
      cancelOrder();
    }
  });
}

//非大工单取消逻辑
function cancelOrder() {
  const { apply_cancel_status } = props.extraData;
  if ([0, 11, 12, 16].includes(apply_cancel_status as number)) {
    backstageCancelVisible.value = true;
  } else {
    userCancelVisible.value = true;
  }
}

async function handleCancelWorkOrder(id: number, key: number) {
  if (id === -1) {
      const {
      data: { result },
    } = await getRiskRepeatCancelWayApi({ order_id: props.orderId });
    if (Number(result) === 1) {
      openAppleOrderModal();
    } else {
      cancelOrder();
    }
  } else {
    const { is_while_user } = props.extraData;
    if (is_while_user && isSuper) {
      cancelOrder();
    } else {
      const superUrl = !!Number(key)
        ? `/super/work-order-inter/my-handle&pageType=2&currentKey=${key}`
        : '/super/work-order-inter/index';
      const url = isSuper
        ? `/super/new-work-order/index?url=${superUrl}&detailId=${id}`
        : `/work-order-inter/detail-index?id=${id}&key=${key}&isFromeOrder=${key}`;
      window.open(`${domain}${url}`);
    }
  }
}

async function openAppleOrderModal() {
  const { data } = await getOrderCancelReasonOptionsApi();
  if (data && Object.keys(data)?.length > 0) {
    appleOrderCancelVisible.value = true;
  } else {
    appleOrderCancelVisibleV1.value = true;
  }
}

async function onRefresh(partialRefresh = true) {
  props.onSuccess && (await props?.onSuccess());
  if (!partialRefresh) emit('refresh');
  else emit('refresh', props.orderId, ['data', 'all_remark', 'order_cancel_data']);
}
</script>
