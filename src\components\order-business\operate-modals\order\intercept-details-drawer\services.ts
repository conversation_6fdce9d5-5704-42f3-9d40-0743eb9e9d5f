import { GET } from '@/services/api';

/**
 * 物流变更详情
 * @param {string} order_id
 * @returns
 */
export function getLogisticschangeDetail(order_id: string) {
  return GET('/warehouse/logisticschanges/detail', { order_id }, { hostType: 'Golang' });
}

/**
 * @description: 查询当前订单的部分原始收货数据
 * @param {string} order_id
 * @return {*}
 */
export function getOriginBaseInfo(order_id) {
  return GET('/warehouse/logisticschanges/originBaseInfo', { order_id }, { hostType: 'Golang' });
}

/**
 * @description: 撤销变更申请
 * @param {string} order_id
 * @return {*}
 */
export function applyRepeal(order_id) {
  return GET('/warehouse/logisticschanges/cancel', { order_id }, { hostType: 'Golang' });
}

/**
 * @description: 查询变更日志
 * @param {string} order_id
 * @return {*}
 */
export function searchChangeLog(order_id) {
  return GET('/warehouse/logisticschanges/log', { order_id }, { hostType: 'Golang' });
}
