<template>
  <a-modal
    v-model:visible="bindVisible"
    class="user-credentials-modal"
    :mask-closable="false"
    title="获取用户凭证"
  >
    <a-spin
      :spinning="loading"
      tip="加载中..."
    >
      <div class="prompt">
        已通知租户在订单内上传本人身份证，用户上传身份证后，可点击【租户凭证】查看身份证照片。
      </div>
      <div class="copy-box">
        <div class="label">
          快捷上传链接
        </div>
        <div class="value">
          <span class="url">{{ url }}</span>
          <span
            class="btn"
            @click="copyIdFn"
          >复制</span>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        @click="bindVisible = false"
      >
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

import { useVModel } from '@/hook';

import { getUserCredentials } from './services';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);

const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);
const url = ref('');
const isSuper = useRoute().query.role === 'super';

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      loadData();
    }
  },
  { immediate: true },
);

function setLoading(flag = false) {
  loading.value = flag;
}

function loadData() {
  setLoading(true);
  getUserCredentials({
    order_id: props.orderId,
    proof_status: 1,
    method: 'v2.order.id.card',
    link_type: isSuper ? 'super' : 'merchant',
  })
    .then(res => {
      url.value = res.data.url;
    })
    .finally(() => {
      setLoading();
    });
}

/**
 * @description: 复制
 * @return {*}
 */
async function copyIdFn() {
  const clipboardObj = navigator.clipboard;
  if (!clipboardObj) {
    message.error('浏览器不支持自动复制，请手动复制');
    return;
  }
  try {
    await clipboardObj.writeText(url.value);
    message.success('复制成功!');
  } catch (err) {
    message.error('浏览器不支持自动复制，请手动复制');
  }
}
</script>

<style scoped lang="less">
.prompt {
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.copy-box {
  margin-top: 12px;

  .label {
    color: rgb(6, 21, 51);
  }

  .value {
    display: flex;
    margin-top: 8px;

    .url {
      display: inline-block;
      width: 300px;
      height: 32px;
      padding: 5px 12px;
      color: rgba(6, 21, 51, 1);
      font-weight: bold;
      background: rgba(6, 21, 51, 0.04);
      border: 1px solid rgba(6, 21, 51, 0.15);
      border-radius: 2px 2px 0 0;
    }

    .btn {
      display: inline-block;
      height: 32px;
      padding: 5px 16px;
      color: #fff;
      background: #00c8be;
      border-radius: 0 2px 2px 0;
      cursor: pointer;
    }
  }
}
</style>
