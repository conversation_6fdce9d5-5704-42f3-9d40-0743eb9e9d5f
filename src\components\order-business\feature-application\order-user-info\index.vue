<template>
  <div class="user-info">
    <ExpandBox
      transition-time="0.09s"
      :value="expand"
      @change="handleChange"
    >
      <template
        v-if="showIdentity"
        #before
      >
        <div
          class="all-info col"
          @click="openAllIdentityInfoModal"
        >
          全部身份信息
          <a-divider
            style="margin-right: 0"
            type="vertical"
          />
        </div>
      </template>
      <template #button="{ hasExtendMore }">
        {{ hasExtendMore ? '收起信息' : '展开信息' }}
      </template>

      <template #content>
        <a-skeleton
          v-if="dataLoading"
          active
          loading
        />
        <div v-else>
          <div class="identity-search">
            <a-checkbox
              v-model:checked="checkState.checkAll"
              :indeterminate="checkState.indeterminate"
              style="margin-right: 24px"
              @change="onCheckAllChange"
            >
              全选
            </a-checkbox>
            <a-checkbox-group v-model:value="checkState.checkedList">
              <a-checkbox
                v-for="item in infoCheckList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-checkbox>
            </a-checkbox-group>
            <div class="search-list">
              <a-button
                type="primary"
                @click="showUserInfoModal"
              >
                查看
              </a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
              >
                重置
              </a-button>
            </div>
          </div>
          <div
            v-if="userData"
            class="identity-info"
          >
            <span>{{ userData.user_info?.user_id }}</span>
            <template v-if="userData.server_info?.server_id">
              <span>{{ userData.server_info.company_name }}</span>
              <span>统一社会信用代码：{{ userData.server_info.license_no }}</span>
              <span>法人：{{ userData.server_info.legal }}</span>
              <span>法人身份证：{{ userData.server_info.legal_idcard }}</span>
              <span
                v-if="isShowEnterpriseStatus"
                :style="[{ color: userData.server_info.company_status ? '#00c1b6' : 'red' }]"
              >
                企业{{ userData.server_info.company_status ? '已' : '未' }}授权
              </span>
            </template>
            <template v-else>
              <span>{{ userData.user_info?.username }}</span>
              <span v-if="showIdCard">
                {{ userData.user_info?.idcard }}
                <template v-if="userData.order_change_info?.length">
                  (<a-button
                    size="small"
                    style="padding: 0"
                    type="link"
                    @click.stop="openBindRecordModal"
                  >更多</a-button>
                  | <a-button
                    size="small"
                    style="padding: 0"
                    type="link"
                    @click.stop="hideIdCard"
                  > 隐藏</a-button>)
                </template>
              </span>
            </template>
            <span
              v-text-hide
              data-html2canvas-ignore
            >
              <template v-if="userData.pre_authorization_info?.pre_authorization_alipay_id">
                {{ '预授权支付宝id：' + userData.pre_authorization_info.pre_authorization_alipay_id }}，
                {{ '下单支付宝手机号：' + userData.pre_authorization_info.pay_order_alipay_phone }}
              </template>
              <template v-else> 非预授权订单 </template>
            </span>

            <span v-if="userData.pre_authorization_info?.alipay_user_info?.length">
              <span v-if="userData.pre_authorization_info?.alipay_user_info?.length > 1">用户存在多个支付宝id</span>
              <span
                v-for="(alipayInfoItem, index) in userData.pre_authorization_info?.alipay_user_info"
                :key="index"
              >
                (支付宝资料：{{ alipayInfoItem.province }}， {{ alipayInfoItem.city }}，
                {{ alipayInfoItem.alipay_user_id }}， {{ '芝麻分等级：' + (alipayInfoItem.zm_level || '查无') }})
              </span>
            </span>
            <span>{{ userData.pre_authorization_info?.face_recognition_result }}</span>
            <span>性别：{{ userData.user_info?.sex }}</span>
            <span>年龄：{{ userData.user_info?.age }}</span>
            <span
              v-text-hide
              data-html2canvas-ignore
            >
              (身份验证信息：{{ '姓名：' + (userData.authenication_info?.name || '暂无') }}，
              {{ '身份证：' + (userData.authenication_info?.idcard || '暂无') }}，
              {{ '人脸' + facePlatform + 'id：' + (userData.authenication_info?.face_alipay_id || '查无') }})
            </span>
            <span>
              (用户授权信息：{{ '姓名：' + (userData.user_authoriztion_identity_info?.name || '暂无') }}，
              {{ '身份证：' + (userData.user_authoriztion_identity_info?.idcard || '暂无') }}
              {{ '下单手机号：' + (userData.user_info?.register_phone || '暂无') }}
              )
            </span>

            <template v-if="!isSalesOrderList">
              <span>会员等级：{{ userData.user_info?.level || 'VIP0' }}</span>
              <span v-if="activeInfo?.is_activated"> 激活情况：{{ activeInfo?.is_activated }} </span>
              <span v-if="activeInfo?.activated_time"> 激活日期：{{ activeInfo?.activated_time }} </span>
              <span v-if="hasVerifyAddress && showVerifyAddressBtn">
                <span> 地址未认证:</span>
                <a-button
                  size="small"
                  type="link"
                  @click="openVerifyAddressModal"
                > 去验证 </a-button>
              </span>
            </template>
            <span
              v-if="orderData?.wait_risk_info?.order_ant_risk_info"
              class="zhima-level"
            >
              {{ orderData?.wait_risk_info?.order_ant_risk_info }}
            </span>
            <span v-if="orderData?.wait_risk_info?.order_ascription">
              {{ orderData?.wait_risk_info?.order_ascription }}
            </span>
            <span v-if="orderData?.wait_risk_info?.zm_grade_label">
              信用级别：{{ orderData?.wait_risk_info?.zm_grade_label }}
            </span>
            <span v-if="orderData?.wait_risk_info?.zhima_level">
              {{ orderData?.wait_risk_info?.zhima_level }}
            </span>
            <span v-if="orderData?.wait_risk_info?.memory">
              {{ orderData?.wait_risk_info?.memory }}
            </span>
          </div>
        </div>
      </template>
    </ExpandBox>
  </div>
</template>

<script setup lang="ts">
import { computed, type Directive, reactive, ref, watch } from 'vue';

import ExpandBox from '@/components/expand-box';
import { basicModal } from '@/components/order-business/basic-modals';
import type { IOrderItem, IUserData, TUserDataKey } from '@/components/order-business/typing';
import { useUnityModal } from '@/components/unity-modal';
import type { IRiskOrderItem } from '@/pages-stage/order/manage/risk-list-v3/data';
import { EOrderStatus } from '@/typing';

import { fetchOrderUserInfo, getActiveInfo } from './services';

interface IProps {
  orderData: IOrderItem & IRiskOrderItem;
  expand?: boolean
  showIdentity?: boolean
  isSalesOrderList?: boolean
}

const props = withDefaults(defineProps<IProps>(), {
  expand: false,
  showIdentity: true,
  orderData: () => ({}),
  isSalesOrderList: false
})



/** 展开时加载数据 */
function handleChange(value: boolean) {
  if (value) {
    getUserData();
    getActiveIdentity();
  }
}

// =============== 用户信息 ===============
const userData = ref<IUserData>();
const dataLoading = ref(false);


const isShowEnterpriseStatus = computed(() => {
  return props.isSalesOrderList
    && userData.value?.server_info?.license_no
    && userData.value?.server_info?.hasOwnProperty('company_status')
});


async function getUserData() {
  try {
    if (userData.value || dataLoading.value) {
      return;
    }
    dataLoading.value = true;
    const orderId = props.orderData.base_info?.order_id;
    const { data } = await fetchOrderUserInfo({ order_id: [orderId] });
    if (data.list?.[orderId]) {
      userData.value = data.list[orderId];
    }
  } finally {
    dataLoading.value = false;
  }
}

/** 人脸认证平台 */
const facePlatform = computed(() => [23, 30].includes(props.orderData.base_info?.pay_status) ? '微信' : '支付宝');

const hasVerifyAddress = computed(() => {
  const idCard = userData.value?.user_info?.idcard;
  const orderStatus = props.orderData.base_info?.order_status;
  return idCard && orderStatus > EOrderStatus.NoPay && orderStatus < EOrderStatus.CloseUser;
});

// ================ 激活信息 ================
const activeInfo = ref<{
  is_activated: string,
  activated_time: string,
}>();
const activeLoading = ref(false);

async function getActiveIdentity() {
  try {
    if (activeInfo.value || activeLoading.value) {
      return;
    }
    activeLoading.value = true;
    const { data } = await getActiveInfo({ order_id: props.orderData.base_info?.order_id });
    activeInfo.value = {
      activated_time: data.activated_time,
      is_activated: data.is_activated,
    };
  } finally {
    activeLoading.value = false;
  }
}

// ================ 选择查看用户信息 ================
const infoCheckList = computed(() => {
  const list: { label: string; value: TUserDataKey }[] = [];
  if (userData.value?.user_info?.user_id) {
    list.push({ label: '用户信息', value: 'user_info' });
  }
  if (userData.value?.server_info?.server_id) {
    list.push({ label: '企业信息', value: 'server_info' });
  }
  list.push({ label: '预授权信息', value: 'pre_authorization_info' });
  list.push({ label: '身份验证信息', value: 'authenication_info' });
  list.push({ label: '用户授权身份信息', value: 'user_authoriztion_identity_info' });
  return list;
});

const checkState = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [] as string[],
});

function onCheckAllChange(e: any) {
  checkState.checkedList = e.target.checked ? infoCheckList.value.map(item => item.value) : [];
  checkState.indeterminate = false;
}

watch(
  () => checkState.checkedList,
  val => {
    checkState.indeterminate = !!val.length && val.length < infoCheckList.value.length;
    checkState.checkAll = val.length === infoCheckList.value.length;
  },
);

function reset() {
  checkState.indeterminate = false;
  checkState.checkAll = false;
  checkState.checkedList = [];
}

// ================ 弹窗控制 ================
const { onToggleComponent, visible } = useUnityModal();

function showUserInfoModal() {
  onToggleComponent(basicModal.identityInfo, {
    orderId: props.orderData.base_info?.order_id,
    checkInfoList: checkState.checkedList,
    payStatus: props.orderData.base_info?.pay_status,
  });
}

function openAllIdentityInfoModal() {
  onToggleComponent(basicModal.allIdentityInfo, {
    orderId: props.orderData.base_info?.order_id,
    userId: props.orderData.base_info?.user_id,
  });
}

function openBindRecordModal() {
  onToggleComponent(basicModal.bindRecord, {
    list: userData.value?.order_change_info,
  });
}

const showVerifyAddressBtn = ref(true);

function openVerifyAddressModal() {
  onToggleComponent(basicModal.verifyAddress, {
    orderId: props.orderData.base_info?.order_id,
    onSuccess: () => showVerifyAddressBtn.value = false,
  });
}

// ================ 文本隐藏控制 =================
const vTextHide: Directive = {
  mounted: (el: HTMLElement) => {
    el.style.textDecoration = 'underline';
    el.style.cursor = 'pointer';
    // 点击时移除元素
    el.addEventListener('click', () => {
      const parentNode = el.parentNode;
      parentNode?.removeChild(el);
    });
  },
};

const showIdCard = ref(true);

function hideIdCard() {
  showIdCard.value = false;
}

// 截图时清空默认选中
function setDefaultCheckList() {
  checkState.checkedList = []
  const list = infoCheckList.value.filter((item) => item.value === 'user_info' || item.value === 'authenication_info')
  if(list.length) {
    checkState.checkedList = list.map((item) => item.value)
  }
}

function closeUserInfoModal() {
  visible.value = false
}




defineExpose({
  setDefaultCheckList,
  showUserInfoModal,
  closeUserInfoModal,
  handleChange
})
</script>

<style scoped lang="less">
.user-info {
  padding: 12px 16px 0 12px;

  .all-info {
    margin-right: 10px;
    color: #3777ff;
    text-decoration: underline;
    cursor: pointer;

    &:hover {
      color: #6198ff;
    }
  }

  .btn {
    text-decoration: underline;
    cursor: pointer;
  }

  .zhima-level {
    color: red;
  }
}

.identity-search {
  padding: 8px 0 16px;

  .search-list {
    margin-top: 16px;

    :deep(.ant-btn) {
      padding: 4px 15px;
    }
  }
}

.identity-info {
  > span:not(:last-child) {
    margin-right: 10px;
    padding-right: 10px;
    border-right: 1px solid #f0f1f3;
  }
}
</style>
