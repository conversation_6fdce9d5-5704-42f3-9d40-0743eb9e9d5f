export interface IOptions {
  id: number;
  name: string;
}
export interface handleOptionsParams {
  [key: string]: IOptions[];
}
export interface handleRuleListParams {
  list: 1;
}
export interface ITbDataRes {
  category_ids: {
    id: string;
    name: string;
  }[];
  id: string;

  model_ids: {
    id: string;
    name: string;
  }[];
  tmp_config: {
    category_ids: number[];
    model_ids: number[];
    user_risk_levels: {
      [key: string]: number;
    };
  };
}
export interface IOrderStatus {
  color?: string;
  id: number;
  status?: string;
}
export interface ITbDataItem {
  condition: {
    id: number;
    name: string;
  };
  deposit: {
    id: number;
    name: string;
  };
  user_risk_levels: undefined | number;
  type: {
    category: {
      id: string;
      name: string;
      status?: 'deleted' | 'added';
      color?: 'red' | 'blue';
    }[];
    model: {
      id: string;
      name: string;
      status?: 'deleted' | 'added';
      color?: 'red' | 'blue';
    }[];
  };
  id: string;
}

export interface IRuleHashList {
  [key: string]: ITbDataRes;
}
