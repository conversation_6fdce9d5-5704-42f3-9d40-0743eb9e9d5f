import { GET, POST } from '@/services/api';

export function apiGetDictionaryMenu() {
  return GET('/super/produceDict/category/get-menu');
}

export function apiGetDetailRule(params: { channel_id: string }) {
  return GET('/super/risk-access-rule/get-list-data', params);
}
export function apiGetDetailRuleLog(params: { id: string }) {
  return GET('/super/risk-access-channel/get-detail-log', params);
}

export function apiAddRule(params: { channel_id: string }) {
  return POST('/super/risk-access-rule/add', params);
}

export function apiDeleteRule(params: { id: string; channel_id: string }) {
  return POST('/super/risk-access-rule/delete', params);
}

export function apiSaveRule(params: { channel_id: string; config: string }) {
  return POST('/super/risk-access-rule/save-data', params);
}

// /super/risk-access-rule/edit
export function apiEditRuleCategory(params: { channel_id: string; rule_id: string; category_id: string[] }) {
  return POST('/super/risk-access-rule/edit', params);
}
//clear-invalid-data
export function apiClearInvalidData(params: { channel_id: string }) {
  return GET('/super/risk-access-rule/clear-invalid-data', params);
}
