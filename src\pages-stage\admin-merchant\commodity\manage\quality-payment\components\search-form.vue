<!--  -->
<template>
  <a-form
    ref="formRef"
    layout="inline"
    :model="formState"
  >
    <div
      v-for="formItem in formItemList"
      :key="formItem.name"
    >
      <a-form-item
        :label="formItem.label"
        :name="formItem.name"
      >
        <!-- select -->
        <a-select
          v-if="formItem.type === 'select'"
          v-model:value="formState[formItem.name]"
          :placeholder="formItem.placeholder"
          style="width: 180px"
          @press-enter="search('filter')"
        >
          <a-select-option
            v-for="option in formItem.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.name }}
          </a-select-option>
        </a-select>
        <!-- input -->
        <a-input
          v-if="formItem.type === 'input'"
          v-model:value="formState[formItem.name]"
          :placeholder="formItem.placeholder"
          @press-enter="search('filter')"
        />
      </a-form-item>
    </div>
    <a-form-item v-if="formItemList.length">
      <a-button
        type="primary"
        @click="search('filter')"
      >
        搜索
      </a-button>
      <a-button
        style="margin-left: 8px"
        @click="search('reset')"
      >
        重置
      </a-button>
    </a-form-item>
  </a-form>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, PropType, watch } from 'vue';
type TOption = { name: any; value: any };
export interface IFormItem {
  type: string; //form-item类型 :input/ select/
  name: string; //form-item的name 用来resetField等
  label?: any; //form-item的label
  placeholder: string;
  value: any;
  options?: Array<TOption>;
  [key: string]: any;
}
export type IFormItemList = Array<IFormItem>;

export default defineComponent({
  props: {
    formItemList: {
      type: Array as PropType<IFormItem[]>,
      default: () => {
        return [];
      },
    },
  },
  emits: ['search'],
  setup(props, { emit }) {
    const formRef = ref();
    const formState = reactive({});
    watch(
      props.formItemList,
      (list: IFormItemList) => {
        list.forEach((item: IFormItem) => (formState[item.name] = item.value));
      },
      { immediate: true },
    );

    function search(type?: string) {
      if (type == 'reset') {
        formRef.value.resetFields();
      }
      emit('search');
    }
    return { formRef, formState, search };
  },
});
</script>
<style scoped lang="less">
:deep(.ant-form-item) {
  margin-bottom: 24px;
}
</style>
