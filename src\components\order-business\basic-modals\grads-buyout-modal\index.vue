<template>
  <RThemeModal
    v-model:open="bindVisible"
    :mask-closable="false"
    ok-text="知道了"
    :show-cancel-button="false"
    title="说明"
    type="primary"
    :width="800"
    @ok="bindVisible = false"
  >
    <a-radio-group
      v-model:value="activeSelect"
      button-style="solid"
      style="margin-bottom: 24px"
    >
      <!-- <a-radio-button value="1">
        文案说明
      </a-radio-button> -->
      <a-radio-button value="2">
        梯度买断说明
      </a-radio-button>
    </a-radio-group>
    <template v-if="activeSelect === '2'">
      <a-alert type="info">
        <template #description>
          <div>买断价（梯度）说明：</div>

          <div>1、系指订单在原租期内（不含续租）对应的每期买断价如下表所示，一般而言，越早买越划算！</div>
          <div>2、当期买断尾款=当期买断价-实付租金-买断优惠，如订单有冻结押金，冻结押金可抵扣买断尾款；</div>
          <div>须知：当订单为租完即送类型套餐时，存在非到期买断价 &lt; 总租金的情况；</div>
        </template>
      </a-alert>
      <a-divider />
      <div class="title">
        {{ title }}
      </div>
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="false"
        :scroll="{ y: 300 }"
      />
    </template>
    <template v-else>
      <div class="tip-text">
        <p class="tip-text-title">
          尊敬的商家伙伴，您好！
        </p>
        <p>
          为提升用户体验，人人租平台已对用户端页面进行优化，将租赁方案中涉及的<span>「买断价」</span>统一调整为更易理解的<span>「到期购买价/当期购买价」</span>。
        </p>
        <p>
          请注意：用户端显示的<span>「当期购买价/到期购买价」</span>与商家端的<span>「买断尾款」</span>暂为同一概念但不同计价方式，均是租赁物在当期/到期的购买价格。
        </p>
        <p>请您在与用户沟通时留意术语差异，避免因表述不一致产生误解。</p>
        <p style="text-decoration-line: underline">
          例如： 用户申请「购买价结算」类似于触发您后台的「买断申请」流程。
        </p>
      </div>
      <div class="tip-light">
        <p>
          如有用户对费用逻辑存在疑问，建议直接引导其参考订单详情页的「到期购买价/当期购买价」说明，或联系客服协助解释。感谢您的理解与配合！人人租将持续优化服务体验，与您共同助力用户租赁旅程更顺畅！
        </p>
        <p>
          <InfoCircleOutlined />
          其他说明： 人人租将在未来几个月内对租赁价格体系进行持续迭代，请您留意平台通知
        </p>
      </div>
    </template>
  </RThemeModal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { TableColumnType } from 'ant-design-vue/lib/components';
import { InfoCircleOutlined } from '@ant-design/icons-vue';

import { useVModel } from '@/hook';

import { getGradsBuyout } from './services';

interface IGradsBuyoutItem {
  tenancy: string;
  time_radius: string;
  buyout_price: string;
}

const props = defineProps({
  orderId: {
    type: String,
    required: true,
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const activeSelect = ref('2');

const title = ref('');
const dataList = ref<IGradsBuyoutItem[]>([]);
const bindVisible = useVModel(props, 'visible', emit);
const loading = ref(false);

watch(
  () => bindVisible.value,
  value => {
    if (value) {
      activeSelect.value = '2';
      loadData();
    }
  },
  { immediate: true },
);

function loadData() {
  loading.value = true;
  getGradsBuyout({
    order_id: props.orderId,
  })
    .then(res => {
      dataList.value = res.data.buyout_list;
      title.value = res.data.sku_name;
    })
    .finally(() => {
      loading.value = false;
    });
}

const columns: TableColumnType[] = [
  {
    title: '期数',
    dataIndex: 'tenancy',
    key: 'tenancy',
  },
  {
    title: '生效租期时间段',
    dataIndex: 'time_radius',
    key: 'time_radius',
  },
  {
    title: '梯度买断价',
    dataIndex: 'grads_buyout',
    key: 'grads_buyout',
  },
];
</script>

<style lang="less" scoped>
.title {
  margin-bottom: 24px;
  text-align: center;
}
.tip-text {
  padding: 16px;
  background: #f4f8ff;
  border-radius: 8px;
  .tip-text-title {
    margin-bottom: 8px;
    color: rgba(6, 21, 51, 0.85);
    font-weight: 500;
    line-height: 22px;
  }
  p {
    color: rgba(6, 21, 51, 0.65);
    span {
      color: #3777ff;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.tip-light {
  margin-top: 12px;
  padding: 16px;
  color: rgba(6, 21, 51, 0.45);
  background: rgba(67, 111, 199, 0.02);
  border: 1px solid rgba(6, 21, 51, 0.04);
  border-radius: 8px;
  p:last-child {
    margin-bottom: 0;
  }
}
// 运营后台不调整颜色
:deep(.ant-radio-button-wrapper) {
  &:hover {
    color: #3777ff !important;
  }
}
:deep(.ant-radio-button-wrapper-checked) {
  color: #fff;
  background: #3777ff !important;
  border-color: #3777ff !important;
  &:hover {
    color: #fff !important;
  }
}
</style>
