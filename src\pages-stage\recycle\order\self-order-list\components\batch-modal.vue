<template>
  <a-modal
    :destroy-on-close="true"
    title="批量回收IMEI码"
    :visible="visible"
    @cancel="onCancel"
    @ok="handleOk"
  >
    <a-form>
      <a-form-item label="IMEI">
        <a-textarea
          v-model:value="content"
          placeholder="多个IMEI需要隔行"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const content = ref('');
const emits = defineEmits(['update:visible', 'change']);
const onCancel = () => emits('update:visible', false);
const handleOk = () => {
  if (content.value) {
    const data = content.value.split('\n');
    if (data.some(item => item.length >= 20)) {
      message.warn('极限值为20字符');
      return;
    }
    emits('change', data);
  }

  onCancel();
};
</script>
