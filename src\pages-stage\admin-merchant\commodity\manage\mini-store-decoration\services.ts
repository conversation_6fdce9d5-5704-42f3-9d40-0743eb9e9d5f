import { GET } from '@/services/api';

// 获取装修数据
export function APIGetDecorationInfo() {
  return GET('/shop/mini-app-brand-shop-decorate/detail');
}

// 保存装修数据
export function APISetDecorationInfo(params: any) {
  return GET('/shop/mini-app-brand-shop-decorate/save', params);
}
// 商品id校验
export function APIVerifyId(params: { spu_id: string }) {
  return GET('/shop/mini-app-brand-shop-decorate/spu-verify', params);
}
