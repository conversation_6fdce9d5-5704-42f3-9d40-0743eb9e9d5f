<template>
  <div class="aliyun-captcha">
    <div>
      <a-button
        :disabled="codeState.isSend"
        type="primary"
        @click="sendCode"
      >
        {{ props.bntText }}
      </a-button>
      <span
        v-if="codeState.isSend"
        class="text"
        style="margin-left: 6px"
      >
        {{ codeState.time + 's 后可重新发送' }}
      </span>
    </div>
    <!-- 用于唤起验证弹框 -->
    <button
      :id="buttonId"
      style="display: none"
    />
  </div>
</template>
<script setup lang="ts">
import { onBeforeUnmount, onUnmounted, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { throttle } from 'lodash-es';

import { loadScript } from '@/utils/base';

import type { IAliyunCaptchaConfig, ICaptchaVerifyParam,IConfig, IProps, WindowWithInitCaptchaType } from './data';
import { getAliConfig, postAliVerify } from './service';

// 阿里云验证码文档：
// https://help.aliyun.com/zh/captcha/captcha2-0/user-guide/add-a-web-or-html5-client-to-alibaba-cloud-captcha-2?spm=a2c4g.11186623.0.0.292d7b7fbDrct1

const props = withDefaults(defineProps<IProps>(), {
  // 初始化配置
  config: () => ({}),
  buttonId: 'button-code',
  // 按钮文字
  bntText: '获取验证码',
  // 是否显示倒计时
  showCountDown: true,
  // 倒计时，为0时，不显示倒计时
  countDown: 60,
});
const emit = defineEmits(['changeShow']);

let captcha = null;
let captchaConfig: IConfig = {};

function sendCode() {
  // 验证流程完毕后需要将验证码组件卸载（从dom中移除）
  emit('changeShow');
  //显示验证码弹框之前的钩子
  const isShow = props.config.beforeShow ? props.config.beforeShow() : true;
  if (isShow) {
    showCaptcha(props.config);
  }
}

const showCaptcha = throttle(showAliCaptcha, 1000);
async function showAliCaptcha(config: IConfig, url = '/site/get-ali-config') {
  captchaConfig = config;
  try {
    if (captcha) {
      // 显示阿里云验证弹框
      buttonClick();
      return;
    }
    await loadJs();
    await getVerifyConfig(url);
  } catch (error) {
    console.error(error);
  }
}

function loadJs() {
  return loadScript('https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js', () => {
    const { initAliyunCaptcha } = window as WindowWithInitCaptchaType;
    if (initAliyunCaptcha) {
      return true;
    }
    return false;
  });
}
// 阿里验证的默认配置
const defaultConfig: IAliyunCaptchaConfig = {
  SceneId: '',
  prefix: '',
  mode: 'popup',
  button: `#${props.buttonId}`,
};
async function getVerifyConfig(url?: string) {
  if (!url) {
    initCaptcha();
    return;
  }
  const { data } = await getAliConfig(url);
  defaultConfig.SceneId = data.scene_id;
  defaultConfig.prefix = data.prefix;
  initCaptcha();
}

let timer: any = null;
const codeState = reactive({
  time: 60,
  isSend: false,
});
// 初始化验证码验证
function initCaptcha() {
  (window as WindowWithInitCaptchaType).initAliyunCaptcha({
    ...defaultConfig,
    ...captchaConfig,
    captchaVerifyCallback: captchaVerifyCallback,
    onBizResultCallback: onBizResultCallback,
    getInstance: getInstance,
  });

  function getInstance(instance: any) {
    captcha = instance;
    // 显示阿里云验证弹框
    buttonClick();
  }

  let errMsg = '';
  // 业务请求(带验证码校验)回调函数
  async function captchaVerifyCallback(captchaVerifyParam: ICaptchaVerifyParam) {
    try {
      // 过滤参数
      let newParams = captchaConfig.formatHandle
        ? captchaConfig.formatHandle(captchaConfig.params)
        : captchaConfig.params;

      const params = {
        ...newParams, // 自定义业务参数
        ali_captcha_verify_param: captchaVerifyParam, // 验证码参数
      };
      const res = await postAliVerify(props.config.url, params);
      const verifyResult = {
        captchaResult: res.data.ali_verify_captcha, // 验证码验证是否通过，boolean类型，必选
        bizResult: false, // 业务是否通过，boolean类型，可选
      };
      const bizResult = captchaConfig.requestCallback && captchaConfig.requestCallback(res);
      verifyResult.bizResult = bizResult || false;
      message.success('验证码发送成功');

      return verifyResult;
    } catch (err: any) {
      console.error('err', err);
      errMsg = '请求出错了~';
      return { captchaResult: false, bizResult: false };
    }
  }

  // 业务请求验证结果回调函数
  function onBizResultCallback(bizResult: boolean) {
    if (bizResult) {
      captchaConfig.resultSuccessCallback && captchaConfig.resultSuccessCallback(bizResult);
      if (props.countDown <= 0 || !props.showCountDown) return;
      codeState.isSend = true;
      codeState.time = props.countDown;
      timer = setInterval(() => {
        codeState.time--;
        if (codeState.time <= 0) {
          codeState.isSend = false;
          codeState.time = props.countDown;
          clearInterval(timer);
        }
      }, 1000);
    } else {
      message.error(errMsg);
    }
  }
}

// 显示阿里云验证弹框
function buttonClick() {
  const captchaButton = document.getElementById(props.buttonId);
  captchaButton?.click();
}

onBeforeUnmount(() => {
  // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
  document.getElementById('aliyunCaptcha-mask')?.remove();
  document.getElementById('aliyunCaptcha-window-popup')?.remove();
});

onUnmounted(() => {
  timer = null;
});
</script>
