<template>
  <div class="sku-detail-panel">
    <HeaderBlock title="预估日回收量">
      <a-tooltip placement="top">
        <template #title>
          <span>当销售价未出现波动时，每日预估回收量</span>
        </template>
        <info-circle-outlined style="color: rgba(6, 21, 51, 0.25); font-size: 16px" />
      </a-tooltip>
    </HeaderBlock>
    <div class="sku-container">
      <div class="sku-container__wrapper">
        <SkuTag
          v-for="item in oddList"
          :key="item.sku"
          :item="item"
        />
      </div>
      <div class="sku-container__wrapper">
        <SkuTag
          v-for="item in evenList"
          :key="item.sku"
          :item="item"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, PropType } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { IGoodsSkuInfo } from '../../data.d';
import HeaderBlock from './header-block.vue';
import SkuTag from './sku-tag.vue';

const props = defineProps({
  skuInfoList: {
    type: Array as PropType<IGoodsSkuInfo[]>,
    default: () => [],
  },
});

// 左侧单数列
const oddList = ref<IGoodsSkuInfo[]>([]);
// 右侧双数列
const evenList = ref<IGoodsSkuInfo[]>([]);

watch(
  () => props.skuInfoList,
  val => {
    oddList.value = val.filter((item, index) => index % 2 === 0);
    evenList.value = val.filter((item, index) => index % 2 !== 0);
  },
);
</script>

<style lang="less" scoped>
.sku-detail-panel {
  padding: 24px;
  .sku-container {
    display: flex;
    align-items: flex-start;
    margin-top: 16px;
    &__wrapper {
      display: grid;
      flex: 1;
      gap: 13px 0;
      &:nth-child(1) {
        padding-right: 24px;
      }
      &:nth-last-child(1) {
        box-sizing: border-box;
        padding-left: 24px;
        border-left: 1px solid #e6e6e6;
      }
    }
  }
}
</style>
