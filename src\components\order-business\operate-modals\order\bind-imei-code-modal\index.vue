<template>
  <a-modal
    :title="bindingStatus ? '换货绑定' : '货品绑定'"
    :visible="visible"
    width="620px"
    @cancel="handleCancel"
  >
    <a-form
      :label-col="{ style: { width: '108px' } }"
      layout="horizontal"
    >
      <div class="base-info">
        <div class="form-item p-b14">
          <div class="label">
            货品信息 :
          </div>
          <div
            class="value"
            style="padding-right: 16px"
          >
            {{ skuInfo }}
          </div>
          <div style="color: rgba(6, 21, 51, 0.25)">
            发货以货品信息为准
          </div>
        </div>
        <div class="form-item p-b14">
          <div class="label">
            货品数 :
          </div>
          <div class="value">
            {{ num }}
          </div>
        </div>
        <div class="form-item">
          <div class="label">
            发货仓库 :
          </div>
          <div class="value">
            {{ warehouseName }}
          </div>
          <a-button
            v-if="isAutoBandBtn"
            ghost
            :loading="autoLoading"
            size="small"
            style="margin-left: 8px"
            type="primary"
            @click="hanldAutoBind"
          >
            自动绑码
          </a-button>
        </div>
      </div>

      <a-form-item
        label="唯一码"
        :rules="[{ required: true, message: '请输入唯一码' }]"
      >
        <a-textarea
          v-model:value="value"
          class="textarea"
          placeholder="若需要绑定多个唯一码，可换行批量绑定"
          :rows="4"
          type="number"
          @change="changeHandler"
        />
      </a-form-item>
      <a-form-item
        v-if="isEc && device_type !== '2'"
        label="序列号"
        name="formSerialNums"
      >
        <a-textarea
          v-model:value="formSerialNums"
          class="textarea"
          placeholder="若需要绑定多个序列号，可换行批量绑定"
          :rows="4"
          type="number"
        />
      </a-form-item>

      <div
        v-if="skuList.length"
        class="sku-list"
      >
        <div class="sku-list-title">
          设备SKU：
        </div>
        <div
          v-for="(skuinfo, idx) in skuList"
          :key="idx"
          class="sku-item"
          :style="{ ...skuInfoStyle, paddingLeft: idx !== 0 ? '10px' : '0' }"
        >
          {{ skuinfo }}
        </div>
      </div>

      <div
        v-if="skuList.length && !checkResult"
        class="hint"
      >
        <img
          alt="错误icon"
          src="https://img1.rrzuji.cn/uploads/scheme/2306/05/m/B7S3kC2slKIeq1B3W329.png"
        >
        {{ compulsionExchangeDis ? '当前绑定货品与货品信息不一致，请校验后重新绑定' : compulsionErrorInfer }}
      </div>
    </a-form>
    <template #footer>
      <a-space>
        <a-button
          v-if="!bindingStatus"
          :disabled="compulsionExchangeDis"
          :loading="compulsionLoading"
          type="primary"
          @click="compulsionConfirm"
        >
          强制换货
        </a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
        <a-button
          :disabled="!checkResult"
          :loading="loading"
          type="primary"
          @click="handleOk"
        >
          确定
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts">
import type { Ref } from 'vue';
import { computed, defineComponent, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { debounce } from 'lodash-es';

import { GET, POST } from '@/services/api';

import { forceBindCode, getAutoBindCode, getAutoBindCodeBtn, getAutoBindCodeBtnCheck } from './services';

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    num: {
      type: Number,
      default: 1,
    },
    deviceType: {
      type: String,
      default: '',
    },
    orderId: {
      type: String,
      default: '',
    },
    imeis: {
      type: String,
      default: '',
    },
    serialNum: {
      type: String,
      default: '',
    },
    bindingStatus: {
      type: Boolean,
      default: true,
    },
    skuInfo: {
      type: String,
      default: '',
    },
    isEc: {
      // 是否是ec订单
      type: Boolean,
      default: false,
    },
    warehouseName: {
      // 发货仓库
      type: String,
      default: '',
    },
  },
  emits: ['update:visible', 'refresh'],
  setup(props, { emit }) {
    // 导入的IMEi
    const value: Ref<string> = ref('');
    // 序列号
    const formSerialNums = ref('');
    const device_type: Ref<string> = ref('');
    const skuList = ref<string[]>([]);

    const skuListLoading = ref(false);

    const formatValue = computed(() =>
      value.value
        .trim()
        .split(/[(\r\n)\r\n]+/)
        .join(','),
    );

    const checkResult: Ref<boolean> = ref(false);

    const skuInfoStyle = computed(() => {
      return {
        color: checkResult.value ? '#00C8BE' : '#FF4D4F',
      };
    });

    const compulsionExchangeDis = computed(() => {
      return !skuList.value.length || checkResult.value || !value.value;
    });

    const handlgetIsAutoBandBtn = async () => {
      const res = await getAutoBindCodeBtn({
        order_id: props.orderId,
      });
      isAutoBandBtn.value = res.data.auto_band;
    };

    // change事件节流
    const changeHandler = debounce(getSkuByImei, 1000, { leading: false });

    async function getSkuByImei() {
      skuListLoading.value = true;
      try {
        const res = await GET('/super/tbl-warehouse/sku', {
          imei: formatValue.value,
          device_type: device_type.value,
          order_id: props.orderId,
        });

        checkResult.value = res.data.check_result;

        compulsionErrorInfer.value = res.data?.msg || '';

        const keys = Object.keys(res.data).filter(key => !['check_result', 'msg'].includes(key));

        keys.forEach((key: string) => {
          const result = [];
          for (const value in res.data[key]) {
            result.push(res.data[key][value]);
          }
          skuList.value = result;
        });
      } finally {
        skuListLoading.value = false;
      }
    }

    watch(
      [() => props.visible, () => props.bindingStatus],
      ([visible, bindingStatus]) => {
        if (!visible) return;

        value.value = props.imeis ? props.imeis.split(',').join('\r\n') : '';
        formSerialNums.value = props.serialNum ? props.serialNum.split(',').join('\r\n') : '';
        device_type.value = props.deviceType;
        skuList.value = [];

        if (bindingStatus) {
          getSkuByImei();
        } else {
          handlgetIsAutoBandBtn();
        }
      },
      {
        immediate: true,
      },
    );

    const handleCancel = () => {
      emit('update:visible', false);
    };

    // 加载
    const loading: Ref<boolean> = ref(false);
    const autoLoading: Ref<boolean> = ref(false);
    const handleOk = async () => {
      loading.value = true;
      try {
        const res: any = await POST('/super/quality-spu/order-binding-phone', {
          order_id: props.orderId,
          imeis: formatValue.value,
          device_type: device_type.value,
          serial_nums: formSerialNums.value
            .trim()
            .split(/[(\r\n)\r\n]+/)
            .join(','),
        });

        message.success(res.message);
        emit('refresh', props.orderId, ['data']);
        handleCancel();
      } catch (err) {
        console.log(err);
      }
      loading.value = false;
    };
    const isAutoBandBtn = ref(false);

    const hanldAutoBind = async () => {
      autoLoading.value = true;
      try {
        await getAutoBindCodeBtnCheck({
          order_id: props.orderId,
        });
        await getAutoBindCode({
          order_id: props.orderId,
        });
        emit('refresh', props.orderId, ['data']);
        handleCancel();
      } finally {
        autoLoading.value = false;
      }
    };

    const compulsionLoading = ref(false);
    const compulsionErrorInfer = ref('');
    const compulsionConfirm = async () => {
      compulsionLoading.value = true;
      try {
        //强制换货
        const res = await forceBindCode({
          order_id: props.orderId,
          imeis: formatValue.value,
          device_type: device_type.value,
          serial_nums: formSerialNums.value
            .trim()
            .split(/[(\r\n)\r\n]+/)
            .join(','),
        });

        message.success(res.message);
        emit('refresh', props.orderId, ['data']);
        handleCancel();
      } finally {
        compulsionLoading.value = false;
      }
    };

    return {
      value,
      loading,
      skuList,
      skuListLoading,
      changeHandler,
      handleOk,
      handleCancel,
      device_type,
      checkResult,
      skuInfoStyle,
      formSerialNums,
      isAutoBandBtn,
      hanldAutoBind,
      autoLoading,
      compulsionExchangeDis,
      compulsionErrorInfer,
      compulsionConfirm,
    };
  },
});
</script>
<style lang="less" scoped>
.base-info {
  margin-bottom: 24px;
  padding-bottom: 24px;
  color: rgba(6, 21, 51, 0.85);
  border-bottom: 1px solid rgba(6, 21, 51, 0.06);

  .form-item {
    display: flex;
  }

  .value {
    padding-left: 8px;
    font-weight: 500;
  }
}

.sku-list {
  display: flex;
}

.hint {
  display: flex;
  align-items: center;
  padding: 8px 0 0 68px;
  color: rgba(6, 21, 51, 0.65);

  img {
    padding-right: 4px;
  }
}

.label {
  width: 71px;
}

.sku-list-title {
  width: 71px;
  font-weight: 500;
}

.textarea {
  color: rgba(6, 21, 51, 0.85);
}

.textarea::placeholder {
  color: rgba(6, 21, 51, 0.25);
}

.p-b14 {
  padding-bottom: 14px;
}
</style>
