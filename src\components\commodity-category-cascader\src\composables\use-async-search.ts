import { debounce } from 'lodash-es';
import { computed, ref } from 'vue';
import type { IOption } from '@/composables/commodity-category/data';
import type { IDisplayRenderParams } from '../data';

/** 远程搜索内容 */
export function useAsyncSearch(params: {
  /** 是否远程搜索 */
  asyncSearch?: boolean;
  /** 选择层级 */
  level?: number;
  /** 是否每级菜单选项值都可选 */
  changeOnSelect?: boolean;
  /** 是否多选 */
  multiple?: boolean;
  /** 展示内容函数 */
  displayRender?: (renderData: IDisplayRenderParams) => string;
}) {
  const { asyncSearch, level, changeOnSelect, multiple, displayRender } = params;

  // 没有设置远程搜索时，不需要额外控制
  if (!asyncSearch) {
    return {
      customDisplayRender: displayRender,
    };
  }

  /** 搜索框输入内容 */
  const searchText = ref('');
  /** 远程搜索内容 */
  const searchName = ref('');
  /** 是否使用名称搜索 */
  const isNameSearch = computed(() => Boolean(searchName.value));

  /** 输入框搜索 */
  function inputSearch(value: string) {
    searchText.value = value;
    searchData();
    // 数据为空时直接赋值置空，不需要等待
    if (!value && searchName.value) {
      searchName.value = '';
    }
  }

  /** 异步防抖搜索 */
  const searchData = debounce(() => {
    // 有输入时才触发赋值
    if (searchText.value) {
      searchName.value = searchText.value;
    }
  }, 700);

  /** 清空搜索内容 */
  function clearSearch() {
    if (searchText.value) {
      inputSearch('');
    }
  }

  /**
   * 为分类数据插入空选择项
   * （搜索数据的子级只有一项时（如：[{label: 'a',children: [{label: 'b'}]}]，搜索"b"并选择"b"时仅会选中父级的"a"），是无法选择子级的，所以需要在子级加入空选择项）
   * @param options 分类数据
   * @returns 有空选项的分类数据
   *  */
  function pushEmptyOptions(options: IOption[]) {
    const newOptions: IOption[] = [];
    // 判断选择项是否包含输入搜索内容（忽略大小写）
    const inputText = searchName.value.toLowerCase();
    options.forEach(item => {
      let childOptions: IOption[] | undefined = item.children;
      const hasInclude = Boolean(item.label.toLowerCase().includes(inputText));
      // 自循环，搜索的子级只有一个选择项时插入空选项
      if (!hasInclude && item.children?.length) {
        childOptions = pushEmptyOptions(item.children);
        if (childOptions.length === 1) {
          childOptions.push({ label: '', value: 0, disabled: false });
        }
      }
      newOptions.push({
        ...item,
        children: childOptions,
      });
    });
    return newOptions;
  }

  const targetLevel = level || 4;
  /** 过滤未包含的输入选项与空选项 */
  function filterEmptyOptions(inputValue: string, options?: IOption[]) {
    // 如果没有设置了changeOnSelect，只显示有最后一级的选择项
    if (!options || (!changeOnSelect && options.length < targetLevel)) {
      return false;
    }
    // 判断选择项是否包含输入搜索内容（忽略大小写）
    const inputText = inputValue.toLowerCase();
    const hasInclude = Boolean(options.find(item => item.label.toLowerCase().includes(inputText)));
    if (!hasInclude) {
      return false;
    }
    // 判断选择项是否包含空选项
    const hasEmpty = Boolean(options.find(item => !item.value));
    if (hasEmpty) {
      return false;
    }
    return true;
  }

  /** 缓存渲染数据，在搜索时用于渲染，避免回显时由于列表无对应选项导致显示成id */
  const bufferRenderObj: { [key in string]: IOption } = {};

  /** 自定义已选项渲染内容函数 */
  function customDisplayRender(renderData: IDisplayRenderParams) {
    const { labels, selectedOptions } = renderData;
    const renderOptions: IOption[] = [];
    selectedOptions.forEach((option, index) => {
      if (option?.value) {
        renderOptions.push(option);
        // 没有缓存时，缓存数据
        const key = `${index}-${option.value}`;
        if (!bufferRenderObj[key]) {
          bufferRenderObj[key] = option;
        }
      } else {
        // 返回空选项时，使用缓存
        const key = `${index}-${labels[index]}`;
        renderOptions.push(bufferRenderObj[key]);
      }
    });
    const renderLabels = renderOptions.map((option, index) => option?.label || labels[index]);

    if (displayRender) {
      return displayRender({ labels: renderLabels, selectedOptions: renderOptions });
    }
    return multiple ? renderLabels[renderLabels.length - 1] : renderLabels.join(' / ');
  }

  return {
    /** 远程搜索内容 */
    searchName,
    /** 是否进行远程搜索 */
    isNameSearch,
    /** 输入搜索 */
    inputSearch,
    /** 清空搜索 */
    clearSearch,
    /** 为分类数据插入空项 */
    pushEmptyOptions,
    /** 过滤未包含的输入选项与空项 */
    filterEmptyOptions,
    /** 自定义已选择的项渲染内容函数 */
    customDisplayRender,
  };
}
